##################################################################################################################################################################################################################
########################################################### CHAPTER 8  FALLING INTO PLACE C #################################################################################################################################################################################
##################################################################################################################################################################################################################

label v8c:
    $ lena_active = True
    $ ian_active = False
    $ save_name = "Lena: Chapter 8"
    $ lena_look = 4
    scene blackbg with long
    show active_lena with long
    pause 1.0

    call calendar(_day="Saturday") from _call_calendar_59

    $ lena_cherry_agenda = True
    play sound "music/broken_dreams1.mp3" loop
    $ flena = "drama"
    $ lena_makeup = 0
    play sound "sfx/door_home.mp3"
    scene lenahomenight_dark with long
    show lena with short
    "When I got home I still felt my heart in my throat."
    "Of all people, of all places, Cherry had to be the one to be there tonight."
    l "I can't believe my bad luck. It's ridiculous..."
    if ian_lena_dating and ian_cherry_dating:
        "And her and Ian... They were hooking up..."
        $ flena = "mad"
        l "Is Destiny playing a joke on me?"
        $ flena = "sad"
        l "It feels like my story wants to repeat itself..."
        if lena_ian_love and ian_lena_over == False:
            l "I want to cry..."
        elif ian_lena_over:
            l "It seems I was right giving up on Ian..."
            if lena_ian_love:
                l "Still... I almost want to cry..."
    elif ian_lena_dating and v2_cherry_home:
        "And her and Ian... They had hooked up..."
        $ flena = "mad"
        l "Is Destiny playing a joke on me?"
        $ flena = "sad"
        l "How close is my story to repeating itself?"
        l "Of all people, why Ian?"
        if lena_ian_love and ian_lena_over == False:
            l "I want to cry..."
        elif ian_lena_over:
            l "I guess I was right giving up on him..."
    else:
        $ flena = "mad"
        l "Why the hell did she have to become friends with Ian and his gang? How random is that?"
        l "There are plenty of people in this city to pick from!"
        $ flena = "sad"
        l "I'm so tired..."
    play sound "sfx/door.mp3"
    scene lenaroomnight with short
    $ lena_look = 2
    "I closed the door to my room, took off my clothes, and collapsed on the bed."
    show lenabra with short
    l "My past seems to keep pursuing me... All I wanted was to shake it off..."
    if axel_pictures_watch:
        show lenabra at right with move
        show v4_polaroid4
        show v4_polaroid3
        show v4_polaroid2
        show v4_polaroid1
        with short
        "I picked up the notebook where I stored Axel's polaroids."
        "I still thought about him... I hadn't yet managed to get him off my mind, despite my best efforts."
        if lena_axel_dating:
            hide v4_polaroid1 with short
            "And I would be seeing him soon..."
            if lena_axel_desire:
                $ flena = "blush"
                hide v4_polaroid2 with short
                "He had been showing up even in my dreams..."
        else:
            "All I could do was try and stay away from him... And even that had been hard."
        $ flena = "worried"
        "I had no idea if I was trying to run from my past or make peace with it."
        "I just felt so confused..."
        hide v4_polaroid4
        hide v4_polaroid3
        hide v4_polaroid2
        hide v4_polaroid1
        with short
        show lenabra at truecenter with move
        $ flena = "sad"
        "And suddenly meeting Cherry only made my old wounds feel fresh once again."
    else:
        "I had been trying to turn the page. To make peace with it."
        $ flena = "sad"
        "But suddenly meeting Cherry made my old wounds feel fresh once again."
    l "I thought I had been making progress, sorting out my feelings..."
    l "Maybe I was just being naive and nothing's changed..."
    $ flena = "worried"
    l "Am I still that same girl?"
    stop music fadeout 2.0
    if lena_mike_dating:
        $ v8_mike_sex = True
        $ flena = "serious"
        l "No, I'm not."
        jump v8mikedlc
    elif lena_louise_sex_late:
        jump v8lenalouisesaturday
    else:
        scene black with long
        jump v8lenasaturdaymorning

## LOUISE SEX ################################################################################################################################################################################################
label v8lenalouisesaturday:
    scene lenaroomnight with long
    "It wasn't that late, but I tried to go to sleep. Everything felt so heavy and I just wanted to rest."
    "Of course, it wasn't so easy. My mind wouldn't let me."
    "I put on my headphones and tried listening to some relaxing music, hoping to trick myself into sleeping."
    "When I was finally starting to feel a bit relaxed, something suddenly sparked my attention. A strange pressure on the bed..."
    scene v6_louise1 with long
    "... and over my body."
    lo "Lena... Are you awake?"
    show v6_louise1b with long
    l "Wha--{w=0.5}{nw}"
    l "Louise...!"
    lo "I thought you'd be out tonight..."
    l "I came back early... Very early."
    lo "I'm glad, because... I didn't want to be alone on a Saturday night, and I was missing you so much..."
    "She leaned forward, her red, peachy lips dangerously close to mine."
    menu:
        "Kiss Louise":
            $ renpy.block_rollback()
            $ lena_louise_sex_late2 = True
            "I let myself get carried away at the moment. After all, why the hell not?"
            play music "music/sex_hot.mp3" loop
            scene v6_louise2 with long
            "I was feeling down that night. Isolated."
            "Louise provided me with warmth and comfort, and I in turn did the same for her. We were there for each other..."
            "I could certainly use a little bit of loving to end the night."
            if lena_lust < 7:
                call xp_up('lust') from _call_xp_up_419
            "We rolled the sheets aside so our bodies could be skin to skin. Louise's warm and sweet kisses took my mind off my problems..."
            jump v6louisenightsex

        "Send her back to her room":
            $ renpy.block_rollback()
            $ lena_reject_louise = True
            $ flena = "sad"
            $ flouise = "sad"
            l "Louise... It's late..."
            l "You should go back to your room..."
            lo "Can I at least sleep with you...?"
            l "I'm not exactly in a social mood tonight... I'd rather be alone, if you don't mind."
            scene lenaroomnight
            show lenabra2 at rig
            show louisenude at lef
            with short
            "After a moment of doubt, Louise got off the bed."
            lo "Am I bothering you?"
            l "I didn't say that you were bothering me... It's just... I have a lot on my mind tonight..."
            lo "Oh, okay. I'm sorry."
            if lena_louise > 6:
                call friend_xp('louise', -1) from _call_friend_xp_579
                $ lena_louise = 6
            lo "Good night."
            play sound "sfx/door.mp3"
            hide louisenude with short
            l "What's gotten into her...?"
            scene black with long
            jump v8lenasaturdaymorning

    label v8sexlouiselateend:
        $ gallery_unlock_scene("CH06_S07")
        "This was definitely a better way to end my Saturday."
        "Satisfied, I could shut my mind off for a while at least, and get some much-needed sleep."
        scene black with long
        jump v8lenasaturdaymorning

##SUNDAY MORNING ################################################################################################################################################################################################################
label v8lenasaturdaymorning:
    call calendar(_day="Sunday") from _call_calendar_60

    $ lena_look = 1
    $ flena = "worried"
    scene lenaroom with long
    play sound "sfx/meow.mp3"
    show lola at lef with short
    "Like most mornings, Lola woke me up."
    if lena_louise_sex_late2:
        show lenanude at rig with short
        l "Can't you let me sleep in for a change? It's Sunday..."
        show lola at lef3
        show lenanude at rig3
        with move
        $ flouise = "smile"
        show louisenude with short
        lo "Nhhh... Good morning."
        $ flena = "n"
        l "Good morning, Louise. Did you sleep well?"
        lo "Never better. Thank you for letting me stay the night with you."
        play sound "sfx/meow.mp3"
        l "I'll go feed this little monster..."
    else:
        show lenaunder at rig with short
        l "Can't you let me sleep in for a change? It's Sunday..."
        if v8_mike_sex:
            if lena_mike_love:
                "I barely noticed when Mike left. I vaguely remembered him kissing me goodbye on the cheek."
                "He must've left really early in the morning, maybe even before the sunrise."
                $ flena = "smile"
                l "At least he stayed... I slept better than I thought I would."
            else:
                l "Last night was just what I needed... I slept better than I thought I would."
        else:
            $ flena = "n"
            l "..."
            l "Oh well, not that I was sleeping in the first place."
            "It had been a rough night. Another one."
        "I thought after the concert was done I could release some tension and rest up a bit..."
        "As it always turned out to be, things weren't that easy."
        play sound "sfx/meow.mp3"
        $ flena = "n"
        l "Yeah, I'm going, I'm going... Sometimes I think you only like me because I feed you."
    play sound "sfx/door.mp3"
    scene lenahome with short
    show lenabra at rig
    show lola_b at lef
    with short
    "I poured some food into Lola's bowl and she purred happily."
    play sound "sfx/purr.mp3"
    hide lola_b
    show lolahappy_b at lef
    l "You can't complain, huh? I treat you like a princess..."
    $ flena = "sad"
    l "I wish somebody would treat me the same way."
    $ louise_look = 1
    $ flouise = "smile"
    play sound "sfx/door.mp3"
    hide lolahappy_b
    show louise at lef
    with short
    if lena_louise_sex_late2:
        l "Oh, you're already dressed?"
        lo "Yeah, I have a meeting with my mentor this morning. The last one before presenting my thesis."
        lo "Tomorrow is the big day!"
    else:
        lo "Good morning, Lena!"
        $ flena = "n"
        l "Good morning Louise... You're up early."
        lo "I have my last reunion with my mentor before presenting my thesis. Tomorrow is the big day!"
    $ flena = "smile"
    l "Congratulations! You look really happy."
    lo "It feels like it's taken ages... All the work is done already, just one last step and..."
    lo "I can't wait to be done with it!"
    l "You'll have a master's degree. I'm jealous."
    if lena_louise_sex_late2:
        lo "What about you? Have any plans for this Sunday?"
        l "I'm going shopping with Holly and Ivy later this morning, but I still have plenty of time before that."
    else:
        lo "What about you? How come you're up so early?"
        l "I just couldn't sleep... I'm going shopping with Holly and Ivy this morning, but I still have plenty of time before that."
    $ flouise = "sad"
    lo "Oh, I see... We could go shopping together once I'm done with my presentation."
    l "Sure."
    if v8_jeremy_flirt:
        jump v8jeremyscene
    $ flouise = "n"
    lo "Well, I'm off. See you later, okay? We could watch a movie together or something, when you're done with your shopping..."
    $ flena = "smile"
    l "Sure! Maybe Holly would like to join, too."
    lo "That'd be nice."
    l "I'll tell her. Have a good day, Louise."
    stop music fadeout 2.0
    scene lenahome with long
    "I killed some time until it was time to get going and meet the girls."
    if ian_lena_dating and v7_holly_kiss:
        "I had to talk to Holly..."
    jump v8lenashopday

## JEREMY ######################################################################################################################################################################################################################################################################################################
label gallery_CH08_S09:
        if _in_replay:
            call setup_CH08_S09 from _call_setup_CH08_S09

label v8jeremyscene:
    $ flouise = "n"
    lo "Oh, by the way, I left Jeremy sleeping in my room. Try not to be too noisy if you can, he must be tired."
    if v8_mike_sex:
        "That's right... Mike told me Jeremy would probably spend the night at my place, too."
        "I feigned ignorance."
    l "He stayed over?"
    lo "Yes, he came after his shift at the club."
    lo "Well, I'm off! See you later, okay? Maybe we can watch a movie together tonight..."
    l "Sure! Maybe Holly would like to join, too."
    lo "That'd be nice."
    l "I'll tell her. Have a good day, Louise."
    stop music fadeout 2.0
    play sound "sfx/door.mp3"
    hide louise with short
    $ flena = "shy"
    show lenabra at truecenter with move
    "So Jeremy was sleeping in Louise's bed..."
    "I couldn't help but think about our texting this past Thursday. It was cut short just when it was getting good..."
    "Images of that night at Ivy's place came to mind. Memories of what it felt like to have that enormous cock in my hands, stroking it..."
    "I started feeling goosebumps. Just thinking about it had me rushing with excitement and anticipation."
    menu:
        "{image=icon_lust.webp}Wake Jeremy up":
            $ renpy.block_rollback()
            $ v8_jeremy_sex = True

        "{image=icon_will.webp}Don't even think about it" if lena_will > 0:
            $ renpy.block_rollback()
            $ flena = "blush"
            call willdown from _call_willdown_8
            l "I can't believe I'm even considering this...!"
            l "Jeremy is Louise's boyfriend... Flirting over the phone is one thing, but this would be..."
            l "It's just too much."
            if ian_lena_dating and ian_lena_over == False:
                l "Things already escalated way further than they should've. And I have to think about Ian..."
            else:
                l "Things already escalated way further than they should've."
            hide lenabra with short
            "I did the right thing and pulled myself away from temptation. I even decided to take a cold shower."
            if ian_lena_dating and v7_holly_kiss:
                "After that, I left to meet with Holly. We had things to talk about..."
            else:
                "After that, I left to meet with the girls."
            $ renpy.end_replay()
            jump v8lenashopday

    $ flena = "flirt"
    "I couldn't keep denying it. I had been lusting over Jeremy's big black meatstick for a while, now."
    if lena_bbc:
        "I had gone as far as digging into Louise's phone for pictures to masturbate to..."
    if v3_spy:
        "Ever since that day when I spied on Louise and Jeremy, I had been secretly desiring to take her place, even if it was just once."
    "And now that I had held it in my hands and gotten a taste of it, the craving was even more intense."
    "And I knew Jeremy craved for me, too... He would surely give me what I wanted..."
    l "Oh God, I can't believe I'm about to do this..."
    scene louiseroom with long
    "I sneaked into Louise's room silently."
    $ flena = "shy"
    show lenabra2 at rig3 with short
    "The light of the early morning entered through the curtains, but Jeremy didn't seem bothered by it."
    "He was turned away from the window, still asleep."
    "But not for long."
    $ flena = "flirtshy"
    hide lenabra2
    show lenatopless2 at rig3
    with short
    "My heart was beating like crazy as I took off my t-shirt and undid my bra. I wanted to offer Jeremy a sight he could not refuse."
    "I was really gonna go through this...!"
    hide lenatopless2
    show lenanude2 at rig3
    with short
    "I felt the adrenaline pumping through my body, like I was about to skydive out of a plane for the first time."
    show lenanude2 at truecenter with move
    "A part of me was pleading with myself to stop, but that voice sounded distant, buried in the back of my mind."
    if lena_ian_love and ian_lena_over == False:
        "Lust was pushing back my feelings for Ian, making me ignore that I was betraying them."
    "There was no turning back. I had made up my mind."
    "I wanted something and I was gonna take it."
    play music "music/sex_hot.mp3" loop
    scene v8_jeremy1 with long
    pause 1
    "I pulled the bedsheet to the side, revealing Jeremy's athletic, naked body, and I dropped into the mattress, between his legs."
    j "Uh--?" with vpunch
    "He rose up, startled. I smiled."
    l "Good morning! Rise and shine!"
    j "What the...? Lena, why are you...?"
    "He was still a bit disoriented. I was counting on it."
    "I took a look at his dick. Even when soft it continued to be a thick, long lump of meat, wrapping around Jeremy's thigh."
    "It was so close to my face..."
    l "I thought you could use some help getting up. Or do you intend to sleep in all morning?"
    j "Uh, I..."
    l "That would be such a shame, don't you think? Especially having me here..."
    scene v8_jeremy1b with long
    "I slid my hand up Jeremy's thigh until I reached his cock."
    "My fingers wrapped around the shaft and I picked it up. It was so heavy..."
    j "Damn, Lena... This is..."
    menu:
        "Hot?":
            $ renpy.block_rollback()
            l "This is so hot, isn't it?"
            j "It is, yeah... But..."
            "His big, tasty lump of meat wobbled in my hand as I began slowly stroking it, trying to get it hard."
            l "Do you want me to stop? Because I'm dying to play with your cock..."
            if lena_lust < 8:
                call xp_up('lust') from _call_xp_up_420
            j "Oh, damn..."

        "Wrong?":
            $ renpy.block_rollback()
            l "This is, what? Wrong?"
            j "Well, yeah..."
            "His big, tasty lump of meat wobbled in my hand as I began slowly stroking it, trying to get it hard."
            l "Maybe. But I feel like being a bit naughty. Don't you?"
            if lena_charisma < 8:
                call xp_up('charisma') from _call_xp_up_421
            j "Fuck... Yeah..."

        "Our secret?":
            $ renpy.block_rollback()
            l "This will be our little secret. Right?"
            j "Uh..."
            "His big, tasty lump of meat wobbled in my hand as I began slowly stroking it, trying to get it hard."
            l "I want this moment to belong to us. Just us... Don't you agree?"
            if lena_wits < 8:
                call xp_up('wits') from _call_xp_up_422
            j "Yeah..."

        "{i}Shhhh{/i}":
            $ renpy.block_rollback()
            l "{i}Shhhh{/i} Don't worry about it right now."
            "His big, tasty lump of meat wobbled in my hand as I began slowly stroking it, trying to get it hard."
            l "Just enjoy it..."
            j "Oh, damn..."

    "If Jeremy had any intention of stopping me, he repressed it. Desire was taking over..."
    "And that turned me on even more. I had his big cock all to myself, finally..."
    stop music fadeout 2.0
    "I intended to thoroughly enjoy it."
    play music "music/sex_bingo.mp3" loop
    scene v8_jeremy2 with long
    pause 1
    if v7_bbc_cum:
        "I could finally do what I had been dreaming of: tasting his big black cock again."
    else:
        "I could finally do what I had been dreaming of: tasting his big black cock."
    l "Nhhh..."
    "I had never played with a cock like that. Running my tongue along the shaft took forever, and I could easily use both hands to stroke it at the same time..."
    "Its heft, its texture, its aroma... Everything about it felt so unique and different from other cocks."
    "Something inside of me, a primal, lusting pulsation, made me want to worship that colossal manhood that was in front of me."
    scene v8_jeremy2b with long
    play sound "sfx/bj1.mp3"
    pause 1
    l "Mhhnn..."
    "My greedy tongue reached Jeremy's glans and I painted it with my warm saliva, making him twitch."
    j "Ohhh...!"
    "My lips joined in right away, wrapping around and sliding over the thick head of his cock."
    if v7_bbc_cum:
        "I clearly remembered how big it was, how it filled my mouth completely..."
        "Today, however, I could take as much time as I wanted to enjoy it, to engrave his taste and shape into my memory..."
    else:
        "It felt so big in my mouth, filling it almost completely..."
        "Oh, how I had been wanting to taste it! And today there was nobody there to stop me."
        "I could take as much time as I wanted to enjoy it, to engrave this feeling into my memory..."
    "I paid attention to Jeremy's reaction as I licked and smooched his cock. His breathing was becoming more and more bewildered, he twitched and moaned softly..."
    "And most importantly, I felt his dick swelling between my hands, getting bigger and harder!"
    scene v8_jeremy3 with long
    pause 1
    "It was time to get serious."
    "I got on my knees and leaned forward, lining up Jeremy's cock with my mouth."
    j "Mhhh, yes..."
    "I felt like the girl in that porn video I had masturbated to... Only this time that girl was me."
    scene v8_bbc0 with long
    play sound "sfx/ah1.mp3"
    pause 1
    "I couldn't help it: I started masturbating while sucking that towering beast of a cock."
    "My pussy was already hot and melting, itching to be pleasured."
    "Touching myself while enjoying that fat cock made it even more inebriating...!"
    scene v8_jeremy3b with long
    pause 1
    "I tried to take as much of it as I could into my mouth. It wasn't much."
    "Jeremy's cock stretched my throat to its limit. I gagged a bit."
    l "Mggh...!"
    scene v8_jeremy3_comp with long
    pause 1
    j "Oooff...!"
    "Jeremy threw his head back and let out a sigh of pleasure. That turned me on like crazy."
    "I wanted to make that cock completely mine!"
    scene v8_jeremy3_animation with long
    play sound "sfx/bj2.mp3"
    "It was like trying to shove a torpedo down my throat. It felt impossible..."
    "But I wanted to try it nonetheless."
    j "Oof, Lena... That's so hot..."
    j "I can't believe how good your mouth feels!"
    "Hearing Jeremy's praise fanned my fire even more if that was even possible."
    "I was possessed by lust, drunk on that massive black cock I had been helplessly craving."
    play sound "sfx/oh1.mp3"
    scene v8_bbc0b with long
    pause 1
    "I shoved three fingers inside my soaking pussy. I could feel my warm juices dripping down my thigh..."
    "This was so much better than masturbating to porn! So much better!"
    "I was getting high on arousal. It was like a drug."
    "And I wanted more...!"
    scene v8_jeremy3_animation with long
    play sound "sfx/dp2.mp3"
    pause 1
    if ian_lena_dating:
        "I didn't care if Jeremy was supposed to be Louise's boyfriend, or Ian's close friend."
        if lena_ian_love:
            "I was probably falling in love with Ian, I had realized that. But this had nothing to do with love..."
        else:
            "I really liked Ian. But this was just a completely different ball game."
    else:
        "I didn't care Jeremy was supposed to be Louise's boyfriend."
    "I knew perfectly well what I was doing was wrong, that thought didn't even cross my mind at that time."
    "I was just obeying my impulses, surrendering to my desire for the biggest cock I had ever seen..."
    "And it felt so damn good!"
    play sound "sfx/orgasm1.mp3"
    scene v8_bbc0b with long
    l "Oh, fuck!!"
    show v8_bbc0c with vpunch
    l "I'm cumming, I'm... Aaahhh!!!"
    with vpunch
    pause 0.5
    with vpunch
    pause 0.5
    with vpunch
    pause 0.5
    "I was struck by a surprisingly powerful orgasm that made my whole body shake and tremble."
    "I felt the muscles of my pussy contracting around my fingers and my juices gushing out."
    "The room spun around me as I was taken awash by that intense pleasure... So fucking good!"
    scene v8_jeremy5 with long
    play sound "sfx/mh1.mp3"
    pause 1
    l "Ahhh... That was incredible..."
    j "Wow... Did you cum for real?"
    l "Wanna check?"
    j "Fuck, that was so hot!"
    l "It was... I wanted to do this so badly..."
    play sound "sfx/bj4.mp3"
    "I took a breather to observe my handiwork."
    "Jeremy's dick was still completely hard, pointing to the ceiling like a lewd and defiant colossus."
    "My saliva clearly marked how many inches I had managed to swallow... It was less than I expected!"
    l "Damn... It's so big..."
    "I caressed Jeremy's shaft with my tongue, reaching the base of his cock."
    "He twitched when I began licking his balls, my hand slowly stroking the shaft."
    j "Do you... want to keep going?"
    l "We can't waste this chance, can we?"
    "My sticky saliva dripped down his cock, smearing my cheek and fingers."
    menu:
        "Suck Jeremy off until he cums":
            $ renpy.block_rollback()
            l "Now's your turn to cum... I want to squeeze a big load out of that cock."
            scene v8_jeremy3_animation2 with long
            play sound "sfx/dp2.mp3"
            pause 1
            j "Mhhh...! Damn, baby...!"
            if lena_lust < 9:
                call xp_up('lust') from _call_xp_up_423
            l "Feed me your cum...!"
            scene v8_jeremy3
            show v8_jeremy4_animation
            with short
            play sound "sfx/dp1.mp3"
            "Jeremy was more than willing to give me what I wanted. He held his cock and started masturbating vigorously while I continued to suck him off."
            "Even though I had just come I was still so aroused... I couldn't get enough of this cock!"
            "Sucking on it felt almost narcotic, addicting, blissful..."
            "All I wanted to do was service that meaty icon of exuberant manhood and milk every last drop of it!"
            "I felt his cock throbbing inside my mouth, his fingers hitting my lips with every stroke of his hand."
            j "Oh yes, yes, yes...! Your mouth is incredible...!"
            "Jeremy's breathing was rough and his body tensed up, twitching. It was coming, I was about to get it...!"
            scene v8_jeremy3
            show v8_jeremy4a
            show v8_jeremy3_cum
            with flash
            play sound "sfx/gag1.mp3"
            j "Ohhh, fuck yeah!!" with vpunch
            pause 0.7
            with vpunch
            pause 0.7
            with vpunch
            pause 0.7
            "Powerful jets of cum shot into my mouth, finally filling it with thick and sour cum."
            "I tried swallowing it, but there was too much! And more kept shooting out!"
            play sound "sfx/bj4.mp3"
            scene v8_jeremy5
            show v8_jeremy5_cum
            with long
            pause 1
            l "Nhahh..."
            "I couldn't hold it in anymore. When I opened my mouth Jeremy's cum spilled all over, drenching his cock and Louise's bedsheets."
            "I watched the thick white drops stream down the shaft, contrasting strongly with its dark skin."
            "I was mesmerized by the vision of that lump of dark meat, shiny with my juices, ejecting loads of warm cum."
            "It was beautiful... And so fucking hot!"
            play sound "sfx/giggle.mp3"
            l "That was fast, hee hee..."
            "I couldn't help but wonder how would that dark lance feel penetrating my pussy..."
            "I wished I had time to find that out, but that would have to wait for another chance."
            "Another chance... Would there even be a second one?"
            "I knew it shouldn't... But I couldn't help but hope for one."

        "Ask Jeremy to fuck you":
            $ renpy.block_rollback()
            $ lena_jeremy_sex = True
            l "I've been wanting to feel this cock of yours inside of me since I first saw it..."
            j "You want me to... fuck you?"
            l "Yes. That's exactly what I want."
            scene v8_jeremy6a
            if lena_piercing1:
                show v8_jeremy6_p1
            elif lena_piercing2:
                show v8_jeremy6_p2
            with long
            pause 1
            "I laid in bed, legs spread apart and up in the air, and waited for Jeremy to get on top of me."
            if lena_lust < 9:
                call xp_up('lust') from _call_xp_up_424
            "I had been fantasizing about this... Wondering what that dark lance would feel like penetrating me... Imagining what Louise felt every time I heard her scream at the other side of my wall."
            "And now I was finally going to experience it!"
            "My pussy was burning up with anticipation as Jeremy lined up his cock with my slit. Its size looked really intimidating..."
            l "Go slowly..."
            j "Yeah, I know."
            "Jeremy began applying pressure. I felt the head of his cock trying to force its way inside..."
            scene v8_jeremy6b
            if lena_piercing1:
                show v8_jeremy6_p1
            elif lena_piercing2:
                show v8_jeremy6_p2
            with long
            play sound "sfx/oh1.mp3"
            pause 1
            l "Ahhnn...!"
            "A subtle mix of pleasure and pain jolted my body when it penetrated me. Nothing had ever stretched my pussy so much, and just the tip was in...!"
            j "Shit, you're tight..."
            l "Oh, God... Slowly..."
            "Jeremy continued to push his hips forward, trying to slide a couple more inches inside of me."
            "Despite my pussy being completely drenched he had trouble fitting any more of his tool in. It was becoming increasingly painful..."
            l "Ahhh, wait... I can't take it deeper right now..."
            j "Alright... I'll try to move."
            "Jeremy pulled his hips back and his cock slid across my vagina until only the head remained inside."
            "Then he slowly thrust his battering ram again, starting a gentle back and forth motion."
            "He was really fucking me with that gigantic cock...!"
            scene v8_jeremy6c with long
            play sound "sfx/ah5.mp3"
            pause 1
            l "Oh, fuck..."
            "I tried to balance things by rubbing my clit, injecting a new source of pleasure into the situation."
            "My heart was beating like crazy, moans constantly escaping my lips as Jeremy's monster cock grinded against the inner walls of my pussy."
            if lena_axel_desire:
                "Not even Axel had managed to stretch me out like that..."
            "But I didn't dislike that pain. It brought with it an intense, lacerating pleasure."
            "Driven by an animalistic desire, all I wanted was for Jeremy to push that big black cock even deeper."
            "To fuck me even harder...!"
            j "Oh, shit, you're squeezing me so much... It feels too good...!"
            j "Lena, I can't hold it anymore! I can't...!"
            scene v8_jeremy6d with long
            pause 1
            "Jeremy pulled out just in time."
            show v8_jeremy6_cum1 with flash
            j "Ahhhh, fuck!!{w=0.5}{nw}" with vpunch
            pause 0.7
            with vpunch
            pause 0.7
            with vpunch
            pause 0.7
            hide v8_jeremy6_cum1
            show v8_jeremy6_cum2
            with long
            "Powerful and constant jets of sperm shot from his cock, one after another."
            "They sprayed my body, from belly to chest, almost reaching my open, panting mouth."
            "I was mesmerized by the vision of that lump of dark meat, shiny with my juices, ejecting loads of warm cum."
            "It was beautiful... And so fucking hot!"
            "It was over so fast, though..."
            "But I had really done it. I had been fucked by Jeremy's big black cock, Louise be damned."

    "My mind was somewhat benumbed, as if shrouded by a dense, cozy mist. I tried snapping out of it."
    $ flena = "slutshy"
    if lena_jeremy_sex:
        $ fjeremy = "sad"
    else:
        $ fjeremy = "smile"
    stop music fadeout 2.0
    scene louiseroom
    show lenanude2 at rig
    if lena_jeremy_sex:
        show lena_cum2 at rig
    else:
        show lena_cum1 at rig
    show jeremynude at lef
    with long
    if lena_jeremy_sex:
        j "Uh, sorry... I was already on edge after the way you sucked me off..."
        j "It didn't take much to make me cum."
        l "It's alright... I'll take it as a compliment."
        $ fjeremy = "smile"
    else:
        j "God, Lena, that was... Damn, you were so hot."
        l "Yeah... I can see you really liked it..."
    l "Look, I'm covered with your cum."
    l "You're such a bad boy..."
    $ fjeremy = "sad"
    j "Uh, yeah, about that..."
    j "This was awesome, but I'm not sure if we should have... I mean, I was just sleeping and then you..."
    $ flena = "flirtshy"
    l "Can you blame me? There's no way I could resist that thing you've got between your legs... Not after getting a taste of it at Ivy's place."
    $ fjeremy = "happy"
    j "I see... I can't really blame you, in that case...!"
    l "It's not fair only Louise gets to experience it... That was so hot...!"
    $ flena = "shy"
    l "Let's keep this between us. I don't need to say it, do I?"
    $ fjeremy = "n"
    j "No, of course not... I'll be discreet about it."
    l "Yes, please. This will be our little secret. For everyone's sake..."
    $ fjeremy = "smile"
    j "Sounds good to me."
    l "I need to go... But first I need a shower!"
    l "And we should probably put the bedsheets into the washer... I'm afraid I came all over them!"
    j "I'll take care of it. Don't be late..."
    "I winked at him."
    l "Thanks, Jeremy!"
    play sound "sfx/shower.mp3"
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH08_S09")
    scene v1_lena_shower
    if lena_piercing1:
        show v1_lena_shower_p1
    if lena_piercing2:
        show v1_lena_shower_p2
    if lena_tattoo2:
        show v1_lena_shower_t2
    if lena_tattoo3:
        show v1_lena_shower_t3
    with long
    pause 1
    "I cleaned myself and left the house. Holly was waiting for me..."

# SHOPPING ######################################################################################################################################################################################################################################################################################################
label v8lenashopday:
    $ lena_look = 4
    $ flena = "n"
    $ holly_look = 1
    $ fholly = "n"
    $ ivy_look = 1
    $ fivy = "smile"
## HOLLY TALK
    if ian_lena_dating and v7_holly_kiss:
        $ fholly = "blush"
        play music "music/hollys_theme.mp3" loop
        scene street with long
        "I had agreed to meet Holly half an hour before meeting Ivy. We had a lot to discuss..."
        if v8_jeremy_sex:
            "What had just happened with Jeremy still had my mind and body bewildered, but I set those thoughts aside to focus on the task at hand."
        show lena at rig3
        show holly3 at lef3
        with short
        "I found Holly already  waiting for me. She looked nervous."
        l "Hi, Holly."
        h "Hi..."
        l "How are you doing?"
        h "I, um... I'm fine."
        "The atmosphere was tense, but I was already expecting that. I decided to try and loosen it up a bit."
        l "Let's take a seat on that terrace over there. I could use a coffee."
        h "Alright..."
        show lena at rig
        show holly3 at lef
        with move
        l "So... This has been one weird week, hasn't it?"
        h "Yeah... Um..."
        h "Despite what you told me... That you're not upset with me... I want to apologize."
        l "You already did that."
        h "But not properly. I haven't explained..."
        l "Well, that's why we're here, right? To have a proper talk about this whole situation..."
        # lena ian over
        if ian_lena_over:
            h "Is it true that... because of what happened, you and Ian are no longer...?"
            l "We're no longer involved with each other, yeah."
            l "I mean, we are, but simply as friends. I made that very clear to him."
            # holly sex
            if ian_holly_sex:
                if ian_holly_dating:
                    l "I know he wants to be more than just friends with you, and I don't want to get in the way of that."
                    $ fholly = "worried"
                    h "But you two were together, and I...!"
                    $ fholly = "blush"
                    h "I'm the one getting in the way."
                    l "You're not. Ian made his choice. He likes you."
                    if lena_ian_love:
                        $ flena = "sad"
                        l "I like him too, but..."
                        l "We were not dating or anything. Not really."
                        l "We were just friends with benefits, and that kind of thing always comes to an end."
                        h "I... I got between you two in the worst possible way...!"
                    else:
                        l "I like him too, but not in the way you do."
                        l "We were just friends with benefits. We can continue to be friends, and leave those benefits to you..."
                        l "I know it's what he wants."
                        h "Still, I got between you two in the worst possible way..."
                else:
                    h "But Ian and I... I mean, we're not together, so..."
                    h "I know I already got between you two in the worst possible way, but...! It won't happen again..."
                    l "No, Holly. Ian made his choice, and I made mine."
                    if lena_ian_love:
                        $ flena = "sad"
                        l "What we had is over... But it's better this way."
                        l "It's clear Ian is not sure about what he wants. Not in the state he is in now, at least."
                        l "I know I would only end up hurting myself if I decided to try something with him."
                    else:
                        l "It's clear Ian is not sure about what he wants. And I'm not so sure what I want myself, to be honest..."
                        l "All I know is if I want things to be simple. And Ian was making them complicated, so..."
                    $ flena = "serious"
                    l "Besides, I don't like what he did in general, and I don't like what he did to you."
                    l "He slept with you and came up with excuses to distance himself after what happened the next morning."
                    l "So many guys pull that stunt off, and it's pathetic. They're allergic to taking some responsibility for their actions..."
                    h "I see what you mean... But I'm also responsible for what happened."
                    $ flena = "sad"
                    h "I knew this outcome was a possibility... It's not the one I was hoping for, but I can't force Ian to change the way he feels."
                    h "I wanted this to happen, even if it was just once..."
                    h "Even if I suspected Ian and you had something going on, I..."
                "Holly looked down, ashamed."
                h "I'm very sorry, Lena. I'm a selfish person and I made things weird for all involved."
                $ flena = "sad"
                l "You're many things, Holly, but I'm sure selfish is not one of them... You were true to your feelings..."
                l "And I'm at fault, too. I've also been selfish..."
                l "I... I knew about your feelings for Ian, and I kept what was going on between us from you."
                l "I was confused and I didn't know what to do, so I did nothing. And this issue got to this point because of that."
                l "So, I'm sorry, Holly."
                h "So, about Ian...?"
                if lena_ian_love:
                    l "What he did hurt me... But I won't hold a grudge against him."
                    l "It's not like we were dating or anything, after all..."
                    l "If something like this had to happen, it's best it happened soon. Now I know where we stand."
                else:
                    l "It's not like we were dating or anything, but I still don't like what he did..."
                    l "Still, I don't hold a grudge against him. You seem to be okay with things and I know he wasn't trying to be malicious."
                l "We talked things over already, and I want to try staying friends with him. I feel he wants the same."
                if ian_holly_dating:
                    h "So you're not mad that I... That he and I...?"
                    $ flena = "smile"
                    l "I'm not mad. I liked Ian too, so I can see why you like him. Despite his flaws, he is a great guy."
                    l "And I like you too, so I'm happy that you got a chance at being with the guy you like..."
                    l "You deserve good things happening to you, Holly."
                    $ fholly = "blush"
                    h "You're too good with me, Lena..."
                    l "I'm not. And I don't mind stepping aside and letting you and Ian be together."
                    l "Granted, things might be a bit... awkward between us for a while, but I can live with that."
                    l "I just want you guys to be happy. And don't worry about me. There are plenty more fish in the sea!"
                    h "Thank you, Lena... I don't know what to say..."
                    l "Then don't say anything. Everything's okay and that's that, okay?"
                    $ fholly = "smile"
                    hide holly3
                    show holly2 at lef
                    with short
                    h "Yeah."
                else:
                    $ fholly = "n"
                    h "I hope we can all continue to be friends. I'd hate it if what happened ruined it..."
                    $ flena = "sad"
                    l "Sex makes everything more complicated than it has to be... Will you be comfortable around him now?"
                    $ fholly = "blush"
                    h "I'll try. I want to."
                    $ flena = "smile"
                    l "Then that's the end of it. Let's talk things over more openly from now on."
                    $ fholly = "smile"
                    hide holly3
                    show holly2 at lef
                    with short
                    h "Yes... I'd like that."
            # just kiss
            elif v7_holly_kiss:
                $ fholly = "worried"
                h "But it was just a kiss... You shouldn't judge him so harshly...!"
                l "You're too kind, Holly. It speaks to your good heart that you're defending Ian in this situation, but..."
                $ fholly = "blush"
                l "This is my choice. And I believe it's better this way."
                l "It's clear Ian is not sure about what he wants. Not in the state he is in now, at least."
                if lena_ian_love:
                    $ flena = "sad"
                    l "I..."
                    l "I know I would only end up hurting myself if I decided to try something with him."
                else:
                    l "And I'm not so sure what I want myself. All I know is I want things to be simple."
                    l "And Ian was making them complicated, so..."
                h "I see..."
                h "Don't blame him for what happened, at least..."
                $ flena = "n"
                l "I don't. We already talked and smoothed things over."
                l "I want to try staying friends with him, and I feel he wants the same."
                "Holly looked down, ashamed."
                h "I'm sorry I made things weird between the three of us."
                $ flena = "sad"
                l "It's okay... I'm at fault, too. I should've been more honest with you."
                l "I also... I knew about your feelings for Ian, and I kept what was going on between us from you."
                l "I was confused and I didn't know what to do, so I did nothing. And this issue got to this point because of that."
                l "So, I'm sorry, Holly."
                h "I guess we all should've just... talked things over. But I've never been too good at that..."
                $ flena = "smile"
                l "It's alright. Let's try to talk things over more openly from now on."
                $ fholly = "smile"
                hide holly3
                show holly2 at lef
                with short
                h "I'd like that."
        # lena ian still dating
        else:
            h "I just want to say: what happened was entirely my fault."
            h "Please, don't reject Ian because of it..."
            $ flena = "worried"
            l "I don't think you bear all the responsibility for what happened. Ian has his share, too..."
            h "But I knew you and Ian had something going on, and yet I stole a kiss from him..."
            h "I'm sorry, that was really selfish of me."
            $ flena = "sad"
            l "You're many things, Holly, but I'm sure selfish is not one of them... You were honest about your feelings..."
            l "And I'm at fault, too. I've also been selfish..."
            l "I kept what was going on between Ian and me from you. And I also knew about your feelings for him..."
            l "I was confused and I didn't know what to do, so I did nothing. I was afraid of what could happen and this issue got to this point because of that."
            l "So, for what it's worth, I'm sorry, Holly."
            h "You shouldn't apologize... And I hope what happened didn't cause a problem between you and Ian..."
            $ flena = "worried"
            l "It kinda did. I wasn't happy about it, of course..."
            h "I hope you believe him. It was just a kiss, nothing more. He backed down and told me he couldn't... go any further..."
            $ flena = "n"
            l "I know. We talked it over and we're fine now."
            l "If he hadn't been honest with me I don't think I could've been okay with this situation. But I understand why this happened..."
            $ flena = "sad"
            l "If anything, I feel guilty about getting in your way. I know you really like Ian..."
            h "Oh, please, no... I..."
            h "I was wrong to do what I did. I was so fearful of losing your friendship..."
            h "I know I don't deserve it, but..."
            $ flena = "smile"
            l "You do deserve it, Holly. I don't blame you, and my only worry is that things might turn awkward between us."
            h "I feel the same..."
            l "So let's not let that happen. Let's try to talk things over more openly from now on."
            $ fholly = "smile"
            hide holly3
            show holly2 at lef
            with short
            h "I'd like that."
            $ flena = "sad"
            l "And about me and Ian... Are you okay that we are together...?"
            h "Yes! Yes, of course."
            h "I'm happy for you. You're both people I really appreciate and I'm happy that you make each other happy."
            $ flena = "smile"
            l "Thanks, Holly..."
        show lena at rig3
        show holly2 at lef3
        with move
        show ivy2 with short
        if holly_gym:
            v "Hey, bitches! What are you doing here?"
            $ flena = "smile"
            l "We were just finishing our coffee."
            hide ivy2
            show ivy
            with short
            v "Let's get going, then! Are you coming, Holly?"
            $ fholly = "happy"
            h "Yeah!"
        else:
            v "Here you are, Lena! I was looking for you."
            l "Holly and I were just finishing our coffee."
            hide ivy2
            show ivy
            with short
            v "Oh, that's right. You were bringing her with us today..."
            $ fholly = "n"
            h "Hi..."
            v "Come on, let's go!"
        stop music fadeout 2.0
        play music "music/girls_day.mp3" loop
        v "Ready to get that credit card working?"
        $ flena = "smile"
        if lena_money > 4:
            l "I'm looking forward to doing some shopping, yeah!"
        else:
            l "I don't think I can afford that, but we'll see!"
##NO HOLLY TALK
    else:
        scene street with long
        show lena with short
        "I arrived at the meeting point on time. I was looking forward to hanging out with the girls."
        if v8_jeremy_sex:
            "What had just happened with Jeremy still had my mind and body bewildered... I was still processing it."
            "I would need to put that in the back of my mind for the time being, though. Ivy had just shown up."
        else:
            "Ivy showed up shortly after."
        play music "music/girls_day.mp3" loop
        show lena at rig with move
        show ivy2 at lef with short
        v "Hey there! Ready to get that credit card working?"
        if lena_money > 4:
            l "I'm looking forward to doing some shopping, yeah!"
        else:
            l "I don't think I can afford that, but we'll see!"
        hide ivy2
        show ivy at lef
        with short
        if holly_gym:
            $ fholly = "smile"
            v "Where's Holly?"
        show lena at rig3
        show ivy at truecenter
        with move
        show holly2 at lef3 with short
        h "Hi..."
        $ flena = "smile"
        if holly_gym:
            v "There you are!"
        else:
            l "Hi, Holly! You made it."
            v "Oh, that's right. You were bringing her with us today..."
            h "I hope I'm not a bother..."
            l "Of course not!"
        v "Come on, let's go!"
## SHOPPING STARTS #########################################################################################################################
    $ fholly = "smile"
    $ v8shopping = 0 # stages of shopping scene
    $ v8mcmnt5 = 0  # lena comments on her spending
    $ v8mcmnt4 = 0
    $ v8mcmnt3 = 0
    $ v8mcmnt2 = 0
    $ v8mcmnt1 = 0
    $ v8mcmnt0 = 0
    $ v8shop_clothes = False
    $ v8shop_sex = False
    $ v8shop_ivy = False
    $ v8shop_holly = False
    scene mall
    show lena at rig3
    show holly2 at lef3
    show ivy2
    with long
    v "Where should we go first?"
    $ config.menu_include_disabled = False
    $ greyed_out_disabled = True
    menu v8shoppingtrip:
        "Shop for clothes" if v8shop_clothes == False:
            $ renpy.block_rollback()
            $ v8shop_clothes = True
            $ v8shopping += 1
            $ flena = "smile"
            l "Let's check out that clothing store."
            $ fivy = "smile"
            v "Let's."
            $ fholly = "smile"
            jump v8shopclothes

        "Visit the sex shop" if v8shop_sex == False:
            $ renpy.block_rollback()
            $ v8shop_sex = True
            $ v8shopping += 1
            l "We can drop by the sex shop..."
            $ fivy = "flirt"
            $ fholly = "blush"
            v "I like how you think! Let's go."
            jump v8shopsex

        "Let Ivy decide" if v8shop_ivy == False:
            $ renpy.block_rollback()
            $ v8shop_ivy = True
            $ v8shopping += 1
            l "Is there any shop you wanted to check out, Ivy?"
            v "Yeah, several! Come this way."
            jump v8shopivy

        "Let Holly decide" if v8shop_holly == False:
            $ renpy.block_rollback()
            $ v8shop_holly = True
            l "Let's see... Holly, did you want to visit a particular shop?"
            hide holly
            show holly2 at lef3
            with short
            h "Yes, in fact... "
            $ fholly = "happy"
            if lena_holly < 10:
                call friend_xp('holly', 1) from _call_friend_xp_580
            h "I want to check out this bookstore. They have a really big section on classics and philosophy I always like to browse..."
            $ fivy = "sad"
            v "Boooring. Do we really need to go?"
            $ flena = "sad"
            $ fholly = "blush"
            hide holly2
            show holly3 at lef3
            with short
            h "No... I guess I can go on my own next time..."
            $ fivy = "smile"
            $ flena = "n"
            v "Cool. Let's think about something else."
            hide holly3
            show holly at lef3
            with short
            jump v8shoppingtrip

        "Enough shopping for today" if v8shopping > 2:
            $ renpy.block_rollback()
            l "I think we've done enough shopping for today."
            v "Not yet! Let me check this last store...!"
            if v7tattoobuy and lena_tattoos:
                v "Besides, we still have to wait a bit before you can go back to the tattoo parlor."
            l "Alright."
            jump v8shopend

## CLOTHES #############
    label v8shopclothes:
        hide ivy
        hide ivy2
        hide holly2
        hide holly
        with short
        show lena at left with move
        "We entered the store and I browsed some clothes."
        "I had seen some pretty nice outfits last time I was here..."
        call screen v8clothingshop
        label v8shopwits:
            hide lena with short
            l "Let me try this on..."
            show lenanude
            show lena_wits1
            with long
            $ flena = "smile"
            l "This one is definitely lovely!"
            if lena_money > 0:
                menu:
                    "{image=icon_pay.webp}Buy this outfit":
                        $ renpy.block_rollback()
                        $ lena_wardrobe_wits1 = True
                        l "I'm in love with this outfit. I'll buy it!"
                        call money(-1) from _call_money_56
                        l "What now?"
                    "Put it back":
                        $ renpy.block_rollback()
                        $ flena = "n"
                        l "I really like it, but I'm not sure I should buy it."
                jump v8clothesshopcontinue
            else:
                $ flena = "sad"
                l "I really like it, but sadly I can't afford to buy it..."
                jump v8clothesshopcontinue
            label v8clothesshopcontinue:
                $ renpy.block_rollback()
                hide money_down
                hide lenanude
                hide lena_wits1
                hide lena_athletics1
                hide lena_charisma1
                with short
                $ flena = "n"
                show lena with short
                show lena at left with move
                call screen v8clothingshop
            label v8leaveshop:
                $ renpy.block_rollback()
                hide money_down
                hide lenanude
                hide lena_wits1
                hide lena_athletics1
                hide lena_charisma1
                with short
                l "I think I've seen enough."
                show lena at rig3 with move
                $ config.menu_include_disabled = False
                $ greyed_out_disabled = True
                jump v8interstice
        label v8shopcharisma:
            hide lena with short
            l "Let's try this one..."
            show lenanude
            show lena_charisma1
            with long
            $ flena = "happy"
            l "Wow, this one's so cool!"
            if lena_money > 0:
                menu:
                    "{image=icon_pay.webp}Buy this outfit":
                        $ renpy.block_rollback()
                        $ lena_wardrobe_charisma1 = True
                        l "I need to buy this!"
                        call money(-1) from _call_money_57
                        $ flena = "smile"
                        l "What's next?"
                    "Put it back":
                        $ renpy.block_rollback()
                        $ flena = "n"
                        l "It's great, but I'm not sure I should buy it..."
                jump v8clothesshopcontinue
            else:
                $ flena = "sad"
                "But I don't have enough money to buy it..."
                jump v8clothesshopcontinue
        label v8shopathletics:
            hide lena with short
            l "This one looks interesting..."
            show lenanude
            show lena_athletics1
            with long
            $ flena = "smile"
            l "It's nice and comfy!"
            if lena_money > 0:
                menu:
                    "{image=icon_pay.webp}Buy this outfit":
                        $ renpy.block_rollback()
                        $ lena_wardrobe_athletics1 = True
                        l "I really like it, I'll get it!"
                        call money(-1) from _call_money_58
                        l "And now...?"
                    "Put it back":
                        $ renpy.block_rollback()
                        $ flena = "n"
                        l "It's really cool, but I'm not sure I should buy it..."
                jump v8clothesshopcontinue
            else:
                $ flena = "sad"
                "But I don't have enough money to buy it..."
                jump v8clothesshopcontinue
        label v8shoplust:
            hide lena with short
            l "This one is so daring..."
            show lenanude
            show lena_lust1
            with long
            $ flena = "shy"
            l "Wow! I look hot!"
            if lena_money > 0:
                menu:
                    "{image=icon_pay.webp}Buy this outfit":
                        $ renpy.block_rollback()
                        $ lena_wardrobe_lust1 = True
                        l "I'm definitely getting this!"
                        call money(-1) from _call_money_21
                        l "And now...?"
                    "Put it back":
                        $ renpy.block_rollback()
                        $ flena = "n"
                        l "I'm not sure it suits me..."
                jump v8clothesshopcontinue
            else:
                $ flena = "sad"
                "But I don't have enough money to buy it..."
                jump v8clothesshopcontinue

## SEX SHOP #############
    label v8shopsex:
        $ toytrack_badboy = False
        $ toytrack_collar = False # tracks if Lena had these toys before buying them now
        if toy_badboy == False:
            $ toytrack_badboy = True # didn't buy this toy before
        if toy_collar == False:
            $ toytrack_collar = True # didn't buy this toy before
        scene sexshop
        show lena at rig3
        show ivy2
        show holly3 at lef3
        with long
        if lena_tattoos and v7tattoobuy:
            l "I have an appointment with Jess today."
            v "Oh, you're finally gonna get a tat done?"
            l "Yeah. That's the idea, at least..."
            v "I'll go with you."
            hide holly3 with short
            show ivy2 at lef3 with move
            if jess_bad:
                show jessb
            else:
                show jessg
            with short
            l "Hi!"
            js "Hey, there you are."
            js "Can you come later? I'm working on a client right now."
            $ flena = "n"
            l "Sure."
            js "Give me a couple hours, then I'll show you the designs I prepared for you."
            v "I want to see them too!"
            js "See you in a bit."
            hide jessb
            hide jessg
            with short
            l "Seems I'll have to wait..."
            v "Let's see what's on the shelves meanwhile! Every time I come to this place I end up buying something, I can't help it!"
        else:
            v "I love coming to this place... But I always end up buying something, I can't help it!"
        if lena_money < 6:
            l "I can't afford to spend money on these kinds of things right now, but... Let's have a look anyway."
        else:
            l "I shouldn't spend too much money on frivolities, but let's have a look..."
        hide holly3 with short
        show ivy2 at left
        show lena at right
        with move

        call open_sexshop from _call_open_sexshop_1

        show lena at rig
        show ivy2 at lef
        with move
        if v6_robert_date == False and lena_wardrobe_bunny:
            $ fivy = "flirt"
            v "Someone's feeling naughty! What do you plan on using that sexy bunny costume for?"
            $ flena = "flirtshy"
            l "I don't know yet... I just felt like buying it. But I'm sure I'll find the chance to use it!"
            if toy_double or (toytrack_badboy and toy_badboy) or (toytrack_collar and toy_collar):
                v "Let me see what else you got..."
        elif toy_double or (toytrack_badboy and toy_badboy) or (toytrack_collar and toy_collar):
            $ fivy = "flirt"
            v "Someone's feeling naughty!"
            $ flena = "flirtshy"
            v "Let me see what you got..."
        elif lena_money < 3:
            $ flena = "sad"
            l "I shouldn't really be spending money on this in my situation..."
        else:
            l "I don't feel like buying anything right now..."
        if toytrack_badboy and toy_badboy:
            # $ toytrack_badboy = False
            v "You finally got yourself a decent dildo!"
            v "And a pretty big one if you ask me, but..."
            v "I guess you like them big, huh?"
        if toytrack_collar and toy_collar:
            v "I see you bought a chained collar... What are you up to, you naughty bitch?"
            l "Who knows... It might come in handy, given the right situation..."
            v "Now I wonder if you're hoping to make someone wear it or you want to wear it yourself..."
            if lena_fty_slave:
                v "Never mind. You definitely want someone to hold the leash for you!"
                l "I can't deny that."
        # if toy_mandingo:
        #     if lena_fty_bbc:
        #         v "You bought that humongous black dildo, huh? I can't say I'm surprised...!"
        #         l "What can I say? I have my fantasies..."
        #     else:
        #         v "I must admit I'm very surprised, Lena... What are you gonna do with that humongous black dildo, huh?"
        #         l "Well, I... I'm not sure!"
        #     v "Look at you... You're turning into a total size queen, ha ha!"
        if toy_double:
            v "I'm curious... What did you buy that double-ended dildo for?"
            l "To club people in the head, in case I need to defend myself..."
            v "Ha, ha, yeah, sure. I think I know what you really have in mind, you dirty slut..."
            v "And I might be up for it if you ask nicely, ha ha ha!"
            l "Ivy!"
        # if toy_tail:
        #     v "That tail plug you bought looks kinda cute. Maybe I'll get one too."
        #     l "It feels fluffy..."
        #     v "And you're into pet play, it seems! Who would've thought, hee hee..."
        if toy_collar and toytrack_collar == False:
            v "Have you seen that chain and collar? Aren't you tempted?"
            l "I already bought one."
            v "Oh, you did! You really are into that sort of thing, huh..."
            v "Now I wonder if you're hoping to make someone wear it or you want to wear it yourself..."
            if lena_fty_slave:
                v "Never mind. You definitely want someone to hold the leash for you!"
                l "I can't deny that."
        # if toytrack_collar and toy_collar:
        #     $ toytrack_collar = False

        l "I'm done here. Let's go."
        jump v8interstice

# SHOP IVY #############
    label v8shopivy:
        "Ivy took us to an underwear store."
        v "I've been wanting to buy a new swimsuit... And I can't say no to some new lingerie!"
        hide ivy with short
        pause 0.5
        $ ivy_look = "bikini"
        show ivy2 with long
        $ fholly = "blush"
        hide holly
        show holly2 at lef3
        with short
        v "How does this one look?"
        if persistent.include_disabled:
            $ config.menu_include_disabled = True
        $ greyed_out_disabled = False
        menu:
            "{image=icon_lust.webp}Smoking hot!" if lena_lust > 5:
                $ renpy.block_rollback()
                $ flena = "flirt"
                l "Wow, you look smoking hot in that one, Ivy!"
                v "Right? I chose the right one for the job, then!"
                if lena_ivy < 12:
                    call friend_xp('ivy', 1) from _call_friend_xp_581
                v "I'm buying this, no doubt!"

            "{image=icon_charisma.webp}It's killer!" if lena_charisma > 5:
                $ renpy.block_rollback()
                $ flena = "happy"
                l "Wow, that one looks killer! It looks really good on you!"
                v "Right? I love it too!"
                if lena_ivy < 12:
                    call friend_xp('ivy', 1) from _call_friend_xp_582
                v "I'm buying this, no doubt!"

            "Looks good!":
                $ renpy.block_rollback()
                $ flena = "smile"
                l "Looks good!"
                v "It does, right? I'm getting this one, then!"

            "It's alright":
                $ renpy.block_rollback()
                $ flena = "n"
                l "It's alright, I guess."
                $ fivy = "serious"
                v "What a way to kill my vibe..."
                l "You're not gonna get it unless I give you some enthusiastic input?"
                $ fivy = "n"
                v "No, I'm getting it anyway."
                l "I knew it."

        $ config.menu_include_disabled = False
        $ greyed_out_disabled = True
        $ flena = "n"
        v "What about you? Let's get you something, Lena!"
        l "I haven't bought new underwear in a long time, that's true..."
        l "Let's see what they have."
        show ivy2 at rig3
        show lena at truecenter
        with move
        hide lena with short
        $ lena_look = "underwear2"
        $ flena = "smile"
        pause 0.5
        show lenabra2 with long
        l "What about this one?"
        $ fivy = "flirt"
        v "I like it!"
        h "It looks perfect on you..."
        l "It's not expensive. I'll buy it."
        # v "And how about some lingerie? Have you seen this one over here?"
        # v "I think it will really suit you!"
        # if lena_wardrobe_lingerie == False:
        #     menu:
        #         "Try on the lingerie":
        #             $ renpy.block_rollback()
        #             if persistent.include_disabled:
        #                 $ config.menu_include_disabled = True
        #             $ greyed_out_disabled = False
        #             l "Let me try it on..."
        #             hide lenabra2 with short
        #             pause 0.5
        #             $ flena = "shy"
        #             $ lena_look = "black_lingerie"
        #             show lenabra2 with long
        #             l "Wow... This is so nice..."
        #             h "You look like a top model..."
        #             v "Damn, I must admit it looks super good! Black really is your color."
        #             menu:
        #                 "{image=icon_pay.webp}{image=icon_pay.webp} Buy the lingerie" if lena_money > 1:
        #                     $ renpy.block_rollback()
        #                     $ lena_wardrobe_lingerie = True
        #                     l "Damn, it's expensive, but I'm in love with it! I'll buy it!"
        #                     v "Wise choice!"
        #                     $ flena = "flirtshy"
        #                     l "Now I only have to find an excuse to wear it..."
        #                     call money(-2)
        #                     $ flena = "smile"
        #                     $ config.menu_include_disabled = False
        #                     $ greyed_out_disabled = True
        #
        #                 "Put it back":
        #                     $ renpy.block_rollback()
        #                     $ flena = "sad"
        #                     l "I'd love to buy it, but it's way too expensive..."
        #                     l "And I don't know when I would get the chance to use it."
        #                     v "Oh, that's easy..."
        #                     l "Don't tempt me. I'll just put it back on the shelf."
        #                     hide lenabra2 with short
        #                     pause 0.5
        #                     $ flena = "n"
        #                     $ lena_look = 4
        #                     show lenabra2 with long
        #
        #         "Forget it":
        #             $ renpy.block_rollback()
        #             $ flena = "n"
        #             l "Forget it... It's really nice, but way too expensive."
        # else:
        #     $ flena = "smile"
        #     l "Oh, this one... I already have one very similar."
        #     $ fivy = "n"
        #     hide ivy2
        #     show ivy at rig3
        #     with short
        #     v "You do?"
        #     l "Yes, Mr. Ward gifted it to me after our first photo shoot... Too bad I haven't found an excuse to wear it again!"
        #     v "Damn, that's a good client to have. This set is quite expensive!"
        if holly_gym:
            v "We should get something for you too, Holly."
            h "Oh, I have enough underwear already. My Mom bought some for me recently..."
            $ fivy = "serious"
            hide ivy2
            hide ivy
            show ivy at rig3
            with short
            v "Oh, shut up! I've seen your underwear in the locker room... and let me say, it's not good!"
            $ flena = "sad"
            hide holly2
            show holly3 at lef3
            with short
            h "Why...?"
            $ fivy = "smile"
            v "Because it looks like you're still wearing the same underwear you had when you were twelve!"
            v "Come on, you need to upgrade your style and get something sexier!"
            $ fivy = "flirt"
            v "Holly, strip down. Lena, help me pick something for her!"
            $ flena = "smile"
            l "On it!"
            show holly3 at truecenter
            show lenabra2 at lef3
            with move
            hide holly3
            show hollybra3
            with short
            "We browsed the shelves and made Holly try a few sets."
            hide hollybra3 with short
            $ holly_look = "sexy"
            l "What about this one...?"
            show hollybra2 with long
            h "I don't know..."
            $ fivy = "smile"
            v "It looks decent!"
            h "You think so?"
            l "I think so too!"
            v "Better than the ones you have, that's for sure. Not that it was a high bar, but..."
            h "I don't know, I look..."
            "Holly took a look at me, then at Ivy."
            hide hollybra2
            show hollybra3
            with short
            h "I'm so... flabby, and flat as a board."
            $ flena = "sad"
            $ fivy = "n"
            l "Stop saying that. You're beautiful too, in your own way."
            v "Well, you could lose a few pounds, that's true."
            $ flena = "worried"
            h "..."
            v "But listen, you have potential. Turn around for me..."
            "Ivy analyzed Holly with expert eyes."
            $ flena = "n"
            v "For someone who just started working out recently you don't have a bad frame. You're naturally thin, not much of a chest, but you have wide hips..."
            v "You just need to lay off the sweets!"
            h "But I love sweets..."
            v "Shush! No more cakes and no more chocolate! And no milkshakes!"
            $ fivy = "smile"
            v "Start eating clean, keep working out and I believe you can become pretty hot yourself..."
            v "And learn to dress with style! Start by buying this set."
            $ fholly = "shy"
            hide hollybra3
            show hollybra2
            with short
            h "Alright... I will."
        scene mall with long
        $ lena_look = 4
        $ ivy_look = 1
        $ holly_look = 1
        $ fholly = "n"
        $ flena = "n"
        $ fivy = "n"
        "We made our purchases and left the shop."
        show lena at rig3
        show ivy
        show holly at lef3
        with short
        jump v8interstice

## LAST SHOP #############
    label v8shopend:
        "We followed Ivy to the store she wanted to visit."
        $ flena = "surprise"
        "It was a high-end store where they sold classy and very expensive clothes and accessories."
        l "I didn't know you shopped in these kinds of places!"
        $ fivy = "flirt"
        hide ivy
        show ivy2
        with short
        v "Only from time to time... But today I feel like spoiling myself a bit!"
        $ flena = "n"
        h "I've never even set foot in a shop like this..."
        l "I have, but only to browse and dream..."
        v "There's a dress and a bag that I've been wanting to get since I saw them, a few days ago..."
        v "Wait a second."
        hide ivy2 with short
        "Ivy picked up the dress, a pair of high heels, and even some jewelry and disappeared into the fitting room."
        $ ivy_look = "dress"
        $ ivy_extras = "bag"
        show ivy2 with long
        $ flena = "surprise"
        $ fholly = "surprise"
        v "{i}Ta-dah{/i}! What do you think?"
        $ flena = "sad"
        "I had never seen Ivy dressed like that. In fact, I had never seen any of my friends dressed like that..."
        $ fholly = "happyshy"
        h "Looks like one of those dresses from the movies..."
        v "It does, huh? I knew I had good taste."
        l "When do you intend to wear this, exactly?"
        v "I don't know, I can put it on any night at the club. Or maybe when I get invited to cocktail parties and snobby events..."
        l "Since when do you attend cocktail parties and snobby events?"
        v "I don't attend those yet, but I will soon, I know it! I have a bright future before me!"
        $ flena = "n"
        $ fholly = "smile"
        l "I wish I was as optimistic as you are."
        $ fivy = "smile"
        v "Let me pay for this and we can get going."
        $ flena = "worried"
        l "So you're gonna really buy it?"
        v "Why do you think I made you come all this way? Of course I'm buying it!"
        v "Shoes, bag, and necklace included!"
        scene mall with long
        $ ivy_look = 1
        $ ivy_extras = 0
        $ flena = "n"
        $ fholly = "n"
        "She did indeed buy all those things..."
        "I saw the check... What Ivy just bought was worth more than an entire month's rent."
        if lena_tattoos and v7tattoobuy:
            jump v8shoptattoos
        else:
            show lena
            show ivy at rig3
            show holly2 at lef3
            with short
            jump v8shopending

## SHOPPING HUB #########################################
label v8interstice:
    scene mall
    show lena at rig3
    show ivy
    show holly at lef3
    with short
    $ flena = "n"
    $ fivy = "smile"
    $ fholly = "n"
    if lena_money > 5:
        if v8mcmnt5 == 0:
            $ v8mcmnt5 = 1
            l "I'm glad I saved up so much. I should be able to buy what I want today."
        else:
            l "I can still afford to buy some more stuff."
    elif lena_money > 3:
        if v8mcmnt4 == 0:
            $ v8mcmnt4 = 1
            l "I still have some pocket money left. I guess I can still afford to buy some stuff."
        else:
            l "I think I can still afford to buy some stuff if I want to."
    elif lena_money == 3:
        if v8mcmnt3 == 0:
            $ v8mcmnt3 = 1
            l "I'm a bit short on money. I can't afford to spend it wildly."
        else:
            l "I should be careful with how much money I'm spending."
    elif lena_money == 2:
        $ flena = "sad"
        if v8mcmnt2 == 0:
            $ v8mcmnt2 = 1
            l "I don't have much money left... I should probably stop buying stuff."
        else:
            l "I really need to watch my spending."
    elif lena_money == 1:
        $ flena = "sad"
        if v8mcmnt1 == 0:
            $ v8mcmnt1 = 1
            l "I'm almost out of money...! I don't think it's a good idea to keep spending..."
        else:
            l "I shouldn't buy anything else..."
    else:
        $ flena = "sad"
        if v8mcmnt0 == 0:
            $ v8mcmnt0 = 1
            l "I'm out of money... No more shopping for me."
            l "I shouldn't have been so careless with my money..."
        else:
            l "I can't afford to spend a single dime."

## 1 lena reflects
    if v8shopping == 1:
        if v8_jeremy_sex:
            $ flena = "flirtshy"
            "I couldn't refrain my mind from wandering back to what happened this morning with Jeremy. And no wonder..."
            "I had been so naughty...!"
            "Feelings of guilt were starting to poke my consciousness, but lust and adrenaline were still running through my body, covering them up."
            "My pulse was starting to race just thinking about what we did on Louise's bed..."
            v "Are you listening to me, Lena?"
            $ flena = "shy"
            l "Uh, yeah. Sorry, what were you saying?"
            $ fivy = "flirt"
            v "You were not... I hope you'll tell me what has you so absorbed and making that impish smile!"
            $ fivy = "smile"
            v "I was saying that we've seen enough here. Let's bail and check another shop!"
        else:
            $ flena = "n"
            "My encounter with Cherry was still bugging me. I didn't want it to get in the way of me enjoying today..."
            $ fivy = "n"
            v "Are you listening to me, Lena?"
            l "Uh, sorry, what were you saying?"
            v "Is everything alright? You look a bit gloomy."
            l "It's alright. I'll tell you later."
            v "As you wish. I think we've seen enough here. Let's bail and check another shop!"
        $ flena = "smile"
        l "Sure."
        scene mall with long
        show lena at rig3
        show ivy
        show holly at lef3
        with short
        "As I explored the mall with Ivy and Holly I couldn't help but smile."
        "A lot had been going on in my life lately, and much of it wasn't exactly good."
        "I had been feeling quite isolated, too. Unlike Ian, I lacked a solid group of old friends I could rely on..."
        "My last time out shopping had been a bit sad. I was on my own."
        "But in these past few days, I had been reminded that I wasn't alone."
        if cafe_music:
            "A lot of people came to see me play at the café. And now I was with Ivy and Holly."
        else:
            "A lot of people came to see me play at the record store. And now I was with Ivy and Holly."
        if ian_lena_dating and ian_lena_over == False:
            "I also had Ian. He cared for me and had been pretty supportive..."
        "I was meeting new people and moving forward, despite the hardships."
        "Today was a good day..."
        v "Where to, now?"
        jump v8shoppingtrip
## 2 hollytalk
    elif v8shopping == 2:
        # first time talking gym
        if ian_lena_dating and v7_holly_kiss and holly_gym:
            "As we walked outside, we put Ivy up to speed about Holly's recent situation."
            if ian_holly_dating:
                $ fivy = "flirt"
                $ flena = "smile"
                $ fholly = "happyshy"
                v "So you finally got some action! It was about time..."
                hide holly
                hide holly2
                show holly3 at lef3
                with short
                h "I guess it was..."
                $ fivy = "n"
                v "Just please, don't join the \"happy couples group\" just yet."
                l "You really have something against serious relationships."
                v "I've seen enough to know how these things go. You should be with me on this, Lena!"
                $ fholly = "shy"
                h "I don't think that'll happen..."
                v "Well, don't forget: sisters before misters!"
            elif ian_holly_sex:
                $ fivy = "flirt"
                $ flena = "sad"
                $ fholly = "blush"
                v "So he fucked you and then said he didn't want to hurt your feelings so better leave it at that?"
                v "How fucking typical. Guys, they're all the same."
                hide holly
                hide holly2
                show holly3 at lef3
                with short
                h "..."
                $ fivy = "flirt"
                v "Well, at least you cleared those cobwebs away from your sex life! It was about time..."
                v "Have you and Lena compared experiences? Was he equally good in bed?"
                $ flena = "worried"
                h "No, that's...!"
                if ian_lena_sex == False:
                    l "I never even slept with him in the first place."
                    v "That's right..."
                else:
                    l "Don't pour salt on the wound, Ivy!"
                    v "Sorry, sorry, I couldn't resist."
                $ fivy = "smile"
                v "In any case, I'm glad to see you're not at each other's throats after that incident."
                $ flena = "n"
                l "Of course not..."
                v "That's good. Sisters before misters!"
            elif v7_holly_kiss:
                $ fivy = "smile"
                $ flena = "sad"
                $ fholly = "blush"
                v "So you only got a kiss from him? Such a waste..."
                hide holly
                hide holly2
                show holly3 at lef3
                with short
                h "I guess that's all he'd consider doing with me. Any further than that was out of the question..."
                v "I'm sure you could've pressed him a bit and got him into bed."
                $ fholly = "worried"
                $ flena = "serious"
                h "There's no way I could've done that...!"
                l "Ivy, shut it, will you?"
                v "I was just joking! I know it would've made things complicated between you and Lena, ha ha."
                $ fholly = "blush"
                $ flena = "n"
                v "You did the right thing. Sisters before misters!"
            if holly_guy:
                if ian_holly_dating:
                    v "Well, don't ignore Mark now that you hooked up with your crush. I went through all the trouble to introduce you!"
                    $ fholly = "n"
                    h "About that... He seems nice, but I'm not interested..."
                    v "You don't have to be. Just talk to him, it'll help you with that awkwardness of yours."
                    $ fholly = "sad"
                    h "Uh... okay."
                else:
                    v "Well, I'm sure you're happy I introduced you to Mark now! He told me you've already talked."
                    $ fholly = "blush"
                    h "Just a bit..."
                    v "You should meet him. You'll like him. And it'll help with that awkwardness of yours."
                    $ fholly = "sad"
                    h "Uh... Maybe one of these days..."
            else:
                v "You should've let me introduce you to Mark. It would've been good for you."
                $ fholly = "n"
                h "I'm fine as is, thanks."
                v "Well, you need to work on that awkwardness of yours {i}pronto{/i}."
                $ fholly = "sad"
                h "Uh... Maybe..."
        # already talked gym
        elif holly_gym:
            if v7_holly_kiss:
                v "So, any news regarding your recent \"situation\" with Ian?"
                if ian_holly_dating:
                    v "Are you two dating, yet?"
                    $ fholly = "n"
                    hide holly
                    hide holly2
                    show holly3 at lef3
                    with short
                    h "No..."
                    h "In fact, we haven't found the chance to hang out together this week, other than Friday at the concert..."
                    v "Maybe he's not that interested after all."
                    $ fholly = "sad"
                    $ flena = "serious"
                    l "Ivy, don't be mean!"
                    $ fivy = "smile"
                    $ fholly = "n"
                    v "Sorry, I couldn't resist, ha ha."
                    v "Always remember, Holly: sisters before misters!"
                else:
                    $ fholly = "n"
                    hide holly
                    hide holly2
                    show holly3 at lef3
                    with short
                    h "No... We settled the matter already."
                    v "Good, then the door's open for new opportunities! Aren't you excited?"
                    $ fholly = "smile"
                    h "I don't know... I guess."
                    if holly_guy:
                        v "What about my guy, Mark? When are you gonna meet him?"
                        h "I, uh... I don't know. We haven't talked much this week."
                        v "I see... That's too bad."
                    else:
                        v "My offer to introduce you to a hot guy is still on the table..."
                        h "It's not necessary, really..."
                        v "As you wish."
            else:
                v "So, Holly, have you already turned the page on Ian? Do you have anyone new on your radar?"
                $ fholly = "n"
                hide holly
                show holly3 at lef3
                with short
                h "No... I, uh, I mean, I turned the page... I accepted Ian is not interested in me."
                h "But I haven't found anyone who I'm interested in... yet."
                if holly_guy:
                    v "What about my guy, Mark? When are you gonna meet him?"
                    h "I, uh... I don't know. We haven't talked much this week."
                    v "I see... That's too bad."
                else:
                    v "My offer to introduce you to a hot guy is still on the table..."
                    h "It's not necessary, really..."
                    v "As you wish."
        ## holly no gym
        else:
            v "So, Holly... What's up with you?"
            $ fholly = "sad"
            hide holly
            hide holly2
            show holly3 at lef3
            with short
            h "With me? Uh... Not much..."
            if ian_lena_dating and v7_holly_kiss:
                $ fivy = "flirt"
                v "Lena's been telling me about your recent drama with our boy Ian..."
                $ fholly = "blush"
                hide holly2
                show holly3 at lef3
                with short
                h "Oh, that... Uh..."
                l "We solved that already."
                v "Really? That's good."
                if ian_holly_dating:
                    v "Be thankful to Lena, Holly. You won't find many girls who take having their guy stolen like that in so much stride!"
                    $ fholly = "blush"
                    $ flena = "worried"
                    h "Uh... I..."
                    l "That's not what happened."
                    v "I know, I know. I was just messing with you girls."
                else:
                    v "So who gets to keep him?"
                    $ fholly = "blush"
                    $ flena = "worried"
                    if ian_lena_over == False:
                        h "That's... Lena..."
                        v "Ohh, I see! So no \"sisters before misters\" in this case, huh Lena?"
                        l "It's not like that!"
                        v "Ha ha, I know, I know. I was just messing with you girls."
                    else:
                        l "Nobody. We've decided to let go of him."
                        v "Ohh, I see! That's cool."
                        v "Sisters before misters, am I right?"
            elif ian_holly_dating:
                $ flena = "smile"
                l "That's not true. You finally confessed to Ian, and he seems to be interested in you too..."
                $ fholly = "shy"
                hide holly2
                show holly3 at lef3
                with short
                h "Uh, yeah, well..."
                v "Oh, so even you have some stories to tell when it comes to guys..."
                if lena_robert_sex == False and lena_mike_sex == False and v7_bbc == "ivy":
                    "Ivy looked at me as if to make a point."
                h "There's not much to tell, not yet at least..."
                v "I bet. I'm sure it would turn into a fascinating story... in time."
            elif v7_holly_kiss:
                l "Holly's been having a few boy problems this week..."
                hide holly2
                show holly3 at lef3
                with short
                v "Oh, so even you have some stories to tell when it comes to guys..."
                if lena_robert_sex == False and lena_mike_sex == False and v7_bbc == "ivy":
                    "Ivy looked at me as if to make a point."
                h "Sadly there's not much to tell... It seems he's not interested in me..."
                v "Oh boy. I wonder why."
            else:
                v "Nothing interesting? Are you seeing someone? Any fun hookup stories?"
                h "Uh, no... I'm not seeing anybody..."
                h "I was interested in someone, but... He doesn't like me back, so I've decided to let that boat sail..."
                v "Oh, so even you have some stories to tell when it comes to guys..."
                v "Of the sad kind though, it would seem."
            $ fholly = "blush"
            $ flena = "worried"
            "Ivy could be so tactless sometimes..."
        $ flena = "smile"
        $ fivy = "n"
        l "By the way, Holly, how's your new book coming along?"
        $ fholly = "smile"
        hide holly3
        show holly2 at lef3
        with short
        h "Oh, that. It's almost ready..."
        h "I already finished it and now we're going over it with my publisher, making sure everything is as it should be..."
        h "I always seem to take a lot of time revising my books... But I'm not confident about releasing them to the public otherwise."
        if lena_wits > 4:
            l "If you need some beta readers to help you with that, you can count on me."
            h "Oh, thank you..."
        v "Yeah, yeah, that sounds fascinating. Look, let's check out this shop now!"
        jump v8shoppingtrip
## 3 cherry
    elif v8shopping == 3:
        if v8_jeremy_sex:
            hide ivy
            show ivy2
            with short
            $ fivy = "flirt"
            v "So, are you gonna tell me what had you smiling like that before?"
            $ flena = "shy"
            l "Who, me?"
            v "Yeah, you! Don't play dumb with me!"
            $ flena = "sad"
            "I looked at Holly. I didn't wanna tell Ivy about what I did with Jeremy in front of her..."
            $ flena = "n"
            l "It's nothing... But I have something else to tell you..."
            hide ivy2
            show ivy
            with short
        else:
            $ fivy = "n"
            v "So, are you gonna tell me what has you worried today?"
            $ flena = "n"
            l "Which one of all those things?"
            v "You know, the one."
            l "Well..."
        $ flena = "sad"
        l "I saw Cherry yesterday."
        $ fivy = "surprise"
        $ fholly = "sad"
        v "No way! Where?"
        $ flena = "worried"
        l "At Ian's place..."
        "I told Ivy about what happened last night."
        $ fivy = "sad"
        v "Damn... That's some serious bad luck..."
        $ flena = "sad"
        l "Tell me about it."
        h "So this girl, Cherry... She's the reason you broke up with Axel?"
        $ fivy = "serious"
        v "Pretty much, yeah. She's a dirty slut."
        l "What happened made me feel like such a fool. And seeing her again made me remember that exact feeling..."
        l "Especially considering her seemingly close connection with the people I just recently befriended."
        v "Seems like she can't keep her hands off what's yours, huh?"
        $ fholly = "n"
        hide holly
        show holly3 at lef3
        with short
        h "Have you talked to her?"
        l "Huh?"
        h "About what happened... Have you talked to her after you found out she and your boyfriend were..."
        v "Why should she talk to her? Cherry is a lying bitch, we know that much already."
        $ flena = "serious"
        l "Yeah... I don't know what good talking to her would make. It's too late to change what happened."
        h "I know... But sometimes understanding what happened can be of help."
        $ flena = "sad"
        v "Of help to whom? To Cherry? Or to Lena?"
        $ fholly = "sad"
        h "To Lena, of course..."
        v "As she said, talking to her won't change what happened. And I think it's pretty easy to understand what went on."
        v "I don't think you have anything to talk about with that two-faced cunt, Lena. You don't need that to turn the page and move on."
        l "I guess... I just hope I don't run into her again."
        v "Yeah. Let's hope so."
        $ fivy = "n"
        v "Anyway, let's not dwell on the shitty stuff. Let's keep browsing shops!"
        $ flena = "n"
        $ fholly = "n"
        hide holly3
        show holly at lef3
        with short
        jump v8shoppingtrip

## TATTOOS ###############################################################################################################
label v8shoptattoos:

    scene sexshop with long
    "We went back to the tattoo shop. Jess should be able to work on me, finally."
    $ flena = "n"
    $ fivy = "n"
    $ fholly = "n"
    show lena at rig
    show ivy at left
    show holly2 at right
    if jess_bad:
        show jessb at lef
    else:
        show jessg at lef
    with short
    js "There you are. Come, let me show you the designs I came up with."
    l "Let's see..."
    js "Pick the one you like the most and we'll get to it."
    label v8tattoopromt:
        scene sexshop with long
    call screen tattooshopscreen
    label v8tat1:
        $ flena = "smile"
        scene sexshop
        show lena at rig
        show ivy at left
        show holly2 at right
        if jess_bad:
            show jessb at lef
        else:
            show jessg at lef
        with short
        l "I like the flower design!"
        js "That one goes on your hip. It's pretty discreet and girly."
        l "That's why I like it. The drawing is beautiful."
        js "Take off your pants and let's see how the stencil looks on you."
        $ flena = "n"
        hide lena
        show lenanude at rig
        show lena_top4 at rig
        show stencil1 at rig
        with short
        js "What do you think?"
        menu:
            "I want it!":
                $ renpy.block_rollback()
                $ flena = "happy"
                l "I love it! I really want to get it!"
                h "I like it too..."
                v "It's nice, yeah."

            "Let's try something else":
                $ renpy.block_rollback()
                $ flena = "sad"
                l "I have my doubts... Let's try something else."
                hide lenanude
                hide lena_top4
                hide stencil1
                show lena at rig
                with short
                jump v8tattoopromt

        js "Okay then, let's get this done. Lay down on the table."
        $ flena = "worried"
        l "..."
        js "What's the matter?"
        l "This is gonna hurt, right?"
        $ fivy = "smile"
        hide ivy
        show ivy2 at left
        with short
        v "Ha ha ha! Now you're scared?"
        v "I had you for a tougher girl, Lena..."
        l "I am! But I hate needles..."
        h "Me too... They're scary."
        if jess_bad:
            js "Oh, come on, don't puss out."
        else:
            js "It's painful, but having your period is probably worse. You'll be able to endure it."
        js "It'll be done in less than an hour."
        l "An hour... okay."
        scene sexshop with long
        call money(-1) from _call_money_59
        $ lena_tattoo1 = True
        jump v8tatend
    label v8tat2:
        $ flena = "smile"
        scene sexshop
        show lena at rig
        show ivy at left
        show holly2 at right
        if jess_bad:
            show jessb at lef
        else:
            show jessg at lef
        with short
        l "I like the one with the bird cage and the roses! It's beautiful!"
        v "That one's pretty cool, yeah."
        js "Let me put the stencil on your arm, see how it looks."
        $ flena = "n"
        show stencil2 at rig with short
        js "What do you think?"
        menu:
            "I want it!":
                $ renpy.block_rollback()
                $ flena = "happy"
                l "It's perfect! I really want to get this one!"
                js "I thought it would suit you. I don't know, you just gave me that impression."
                v "It looks dope! I like it."

            "Let's try something else":
                $ renpy.block_rollback()
                $ flena = "sad"
                l "I'm not sure about this one. Let's try something else."
                hide stencil2 with short
                jump v8tattoopromt

        js "Okay then, let's get this done. Lay down on the table."
        $ flena = "worried"
        l "..."
        js "What's the matter?"
        l "This is gonna hurt, right?"
        $ fivy = "smile"
        hide ivy
        show ivy2 at left
        with short
        v "Ha ha ha! Now you're scared?"
        v "I had you for a tougher girl, Lena..."
        l "I am! But I hate needles..."
        h "Me too... They're scary."
        if jess_bad:
            js "Oh, come on, don't puss out. You'll get over it."
        else:
            js "It's painful, but you should be able to endure it."
        js "It'll take around two hours at most."
        l "Two hours... okay, let's do it."
        scene sexshop with long
        call money(-2) from _call_money_60
        $ lena_tattoo2 = True
        jump v8tatend

    label v8tat3:
        $ flena = "smile"
        scene sexshop
        show lena at rig
        show ivy at left
        show holly2 at right
        if jess_bad:
            show jessb at lef
        else:
            show jessg at lef
        with short
        l "The one with the crane and the sakura flowers looks incredible! I want this one!"
        $ flena = "sad"
        l "It's expensive, though..."
        js "This one covers your thigh completely, so it's pretty big. But I'm quite proud of how this one turned out."
        js "Take off your pants and let's see how the stencil looks on that leg."
        $ flena = "n"
        hide lena
        show lenanude at rig
        show lena_top4 at rig
        show stencil3 at rig
        with short
        js "What do you think?"
        menu:
            "I want it!":
                $ renpy.block_rollback()
                $ flena = "happy"
                l "It's perfect! I really want to get it!"
                v "It's a really dope tat! You're gonna look so hot with it, Lena."
                h "It looks really... impressive."

            "Let's try something else":
                $ renpy.block_rollback()
                $ flena = "sad"
                l "Hum... I don't know. Let's try something else."
                hide lenanude
                hide lena_top4
                hide stencil3
                show lena at rig
                with short
                jump v8tattoopromt

        js "Okay then, let's get this done. Lay down on the table."
        $ flena = "worried"
        l "..."
        js "What's the matter?"
        l "This is gonna hurt, right?"
        $ fivy = "smile"
        hide ivy
        show ivy2 at left
        with short
        v "Ha ha ha! Now you're scared?"
        v "I had you for a tougher girl, Lena..."
        l "I am! But I hate needles..."
        h "Me too... They're scary."
        if jess_bad:
            js "Oh, come on, don't puss out. This one will hurt, but it's nothing you can't get over. Just grit your teeth."
        else:
            js "This one will probably get quite painful. We can take a rest if you need to."
        js "I think I should get it done in about three hours."
        l "Three hours...! This will be tough..."
        l "But okay, let's do it."
        scene sexshop with long
        call money(-3) from _call_money_61
        $ lena_tattoo3 = True
        jump v8tatend

    label v8notat:
        $ flena = "sad"
        scene sexshop
        show lena at rig
        show ivy at left
        show holly2 at right
        if jess_bad:
            show jessb at lef
        else:
            show jessg at lef
        with short
        l "Um... I'm afraid I won't be getting a tattoo, after all."
        if jess_bad:
            js "Why the fuck not?"
        else:
            $ fjess = "sad"
            js "Why not?"
        menu:
            "I can't afford it":
                $ renpy.block_rollback()
                l "The truth is I can't afford those prices right now."
                if jess_bad:
                    $ fjess = "n"
                    js "You must be really short on money if you can't even afford the discounted prices..."
                    l "I'm going through hard times."
                    js "..."
                    js "I see."
                    js "Well, come back when you've got the cash."
                else:
                    js "Really? They're discounted..."
                    l "I know, but still... I'm not in the best financial situation right now."
                    $ fjess = "n"
                    js "I understand. I've been there too, kind of."
                    js "Well, if you manage to get the money you know where to find me."
                    $ flena = "n"
                    l "Thanks."

            "I don't like the designs":
                $ renpy.block_rollback()
                $ lena_tattoos = False
                l "I'm sorry to say this, but... These designs just don't go with me."
                if jess_bad:
                    $ fjess = "mad"
                    js "Well then, fuck off outta here. You're wasting my time!"
                    $ flena = "sad"
                    call friend_xp('jess', -1) from _call_friend_xp_583
                    l "Sheesh... okay."
                else:
                    js "I see... I thought you'd like those."
                    $ fjess = "n"
                    js "Well, sorry I've missed the shot."
                    $ flena = "n"
                    l "It's okay... I'm not even sure I can afford to get a tattoo at this moment anyway."
                    js "Well, if you change your mind you know where to find me."
                    l "Thanks."
        scene mall with short
        "I left the shop a bit disappointed. I wouldn't be getting a tattoo after all..."
        if lena_tattoos:
            "Not yet, at least."
        show lena
        show ivy at rig3
        show holly2 at lef3
        with short
        jump v8shopending

    label v8tatend:
        "I took a deep breath and got ready to get some ink done for the first time. Thankfully Ivy and Holly were there to give some moral support."
        play sound "sfx/tattoo.mp3"
        "Jess' needle traced paths on my skin, embedding the lines that were bound to remain with me for the rest of my life."
        if lena_tattoo1:
            $ flena = "happy"
            "She was right: it was painful, but nothing I couldn't endure, and she finished it pretty quickly."
        if lena_tattoo2:
            $ flena = "smile"
            "It wasn't a pleasant experience, but I could withstand the pain. I felt excited and happy to finally get a tattoo, which made it easier to bear."
        if lena_tattoo3:
            $ flena = "worried"
            "I would lie if I said it wasn't a challenging experience. I had to endure the bite of the needle for several hours..."
            "But it was worth it. I felt excited and happy to finally get a tattoo."
        "After getting it cleaned and receiving instructions on how to take care of my skin during the following days, we left the store."
        $ fivy = "smile"
        $ holly = "smile"
        scene mall with long
        show lena
        show ivy at rig3
        show holly2 at lef3
        with short
        if lena_tattoo1:
            l "That wasn't so bad!"
            v "Happy with your first tat?"
            l "Yeah, a lot!"
        if lena_tattoo2:
            l "I can't believe it... I finally got my first tattoo done."
            v "It looks dope!"
            $ flena = "happy"
            l "Yeah, I love how it turned out. It's perfect..."
            l "But it feels weird looking at my arm now!"
            v "You'll get used to it."
        if lena_tattoo3:
            l "Wow, that was hard..."
            h "I don't know how you could take it... I'm amazed at you."
            v "Aren't you happy with your tat? I think it looks dope!"
            $ flena = "happy"
            l "Yeah... It feels so weird looking at my leg now..."
            v "You'll get used to it soon enough. But it's not a discreet tattoo, that's for sure!"
            l "Yeah... I love how it turned out. I think it's perfect."
        v "Welcome to the club! Will you be joining it too, Holly?"
        $ fholly = "worried"
        $ flena = "smile"
        h "What, me? Getting a tattoo?"
        v "Yeah."
        h "N-{w=0.3}no, I don't think so... I'm terrified of needles... I could barely watch."
        if lena_tattoo1:
            l "Thanks for bearing with me today, girls."
            $ fholly = "smile"
            v "Don't worry, it didn't take that long."
        if lena_tattoo2:
            l "Thanks for bearing with me today, girls. It took pretty long..."
            $ fholly = "smile"
            v "Don't worry. That's what friends are for."
        if lena_tattoo3:
            l "Thanks for bearing with me today, girls. I know that took really long..."
            $ fholly = "smile"
            v "Don't worry. That's what friends are for. Next time it'll be your turn!"
            l "Sure."
    jump v8shopending

## SHOPPING ENDING ###############################################################################################################
label v8shopending:
    if persistent.include_disabled:
        $ config.menu_include_disabled = True
    $ greyed_out_disabled = False
    stop music fadeout 2.0
    "We spent almost the whole day browsing shops and hanging out."
    $ flena = "smile"
    l "Today was so much fun... It was just what I had been needing."
    $ fholly = "happyshy"
    h "I enjoyed it a lot too... Thanks for inviting me."
    # go ivy place
    if holly_gym:
        v "You're one of the gang now, aren't you?"
        hide holly2
        show holly3 at lef3
        with short
        h "I... I am?"
        v "Sure."
        "Ivy looked at me."
        v "I'm with you, Lena. Today was a cool day, which is why it would be a shame for it to end too soon!"
        hide ivy
        show ivy2 at rig3
        with short
        v "Let's go to my place! We can't call it a day without toasting over a glass of wine, or maybe two..."
        $ fholly = "smile"
        l "Some of us have to work tomorrow..."
        v "Oh, come on, it's still early. What do you say, Holly?"
        $ fholly = "shy"
        h "I would like that..."
        v "You've heard her! Come on, jump in my car. I parked it around the corner."
        jump v8sundayivy
    # lena holly alone
    else:
        v "It surely was... {i}different{/i} having you around."
        $ fholly = "n"
        hide holly2
        show holly3 at lef3
        with short
        h "I hope I wasn't a bother..."
        l "Of course not! I'm glad you came, Holly."
        $ fholly = "happy"
        v "Well, girl, it's getting late and I gotta bail."
        if lena_tattoo1 or lena_tattoo2 or lena_tattoo3:
            v "See you at the gym, Lena! And be sure to take care of that tattoo! Ask me for help if you have any doubts."
            l "Thank you, Ivy!"
        else:
            v "See you at the gym, Lena!"
            l "Bye!"
        hide ivy with short
        show lena at rig
        show holly3 at lef
        with move
        menu:
            "Invite Holly to your place":
                $ renpy.block_rollback()
                $ v8_holly_date = True
                l "Say, Holly... Do you want to come to my place? We could watch a movie over dinner or something."
                $ fholly = "happyshy"
                h "I'd like that... I was enjoying this day so much that I wasn't ready for it to end."
                l "We feel the same way, then."
                if lena_holly < 12:
                    call friend_xp('holly', 1) from _call_friend_xp_584
                l "Hanging out with Ivy is fun, but there are some things we can't do when she's around, and I feel like chilling a bit to end the day."
                h "That's one of my favorite things to do... A movie or a book, a cozy sofa, and a big cup of cocoa..."
                l "And a friendly conversation."
                h "That'd be great."
                l "Let's go, then!"
                jump v8sundayholly

            "Say goodbye":
                $ renpy.block_rollback()
                $ flena = "n"
                $ fholly = "smile"
                if lena_tattoo1 or lena_tattoo2 or lena_tattoo3:
                    l "I'll get going, too. I'm dead tired... And I need to take care of the tattoo."
                    h "Of course."
                else:
                    l "I'll get going, too. I'm dead tired..."
                    h "Yeah, me too."
                h "And thanks again for today, Lena. I really enjoyed it..."
                l "Let's do this again soon."
                h "I'd love that."
                hide holly3 with short
                show lena at truecenter with move
                "This had been a pretty intense and weird week... And this was a nice way to end it, all things considered."
                if v8_jeremy_sex:
                    $ flena = "flirt"
                    "It had been a very enjoyable Sunday all around... Especially the morning, when I got to finally enjoy Jeremy all by myself..."
                    "I would need to tell Ivy about it!"
                    $ flena = "worried"
                    "I had no idea how I would look Louise in the face after that, but... She was none the wiser about it, so I would just play it off."
                    $ flena = "smile"
                    "Weird as some stuff might be, I finally felt like the pieces were starting to fall into place."
                else:
                    "It had been a very enjoyable Sunday all around... And I finally felt like the pieces were starting to fall into place."
                "I didn't need to face everything on my own anymore..."
                scene black
                pause 2
                jump v8ending
#########################################################################################################################################################################################################################################################################################
## HOLLY LENA ALONE #################################################################################################################################################################################################################################################################################################
##########################################################################################################################################################################################################################################################################################
label gallery_CH08_S12:
    if _in_replay:
        call setup_CH08_S12 from _call_setup_CH08_S12

label v8sundayholly:

    play sound "sfx/door_home.mp3"
    play music "music/hollys_theme.mp3" loop
    scene lenahomenight with long
    $ holly_glasses = True
    $ flena = "smile"
    if v8_holly_ivy:
        $ fholly = "blush"
    else:
        $ fholly = "smile"
    show lena at rig
    show holly2 at lef
    with short
    if v8_holly_ivy:
        l "How are you feeling, Holly? Have you sobered up a bit?"
        h "I think so. Can I get a glass of water?"
        l "Of course. Go to my room, I'll bring it to you."
    elif lena_tattoo1 or lena_tattoo2 or lena_tattoo3:
        l "Let me go to the bathroom real quick. I need to clean the tattoo and put on the cream Jess gave me."
        h "Do you need help?"
        l "Nope. You can wait for me in my room."
    else:
        l "Let's drop the bags in my room."
    h "Alright."
    play sound "sfx/door.mp3"
    scene lenaroomnight with long
    $ fholly = "happy"
    show holly at lef3
    show lola
    show lena2 at rig3
    with short
    if lena_tattoo1 or lena_tattoo2 or lena_tattoo3 or v8_holly_ivy:
        "When I came back I found Lola demanding pampers from my guest."
    else:
        h "Oh! Hello, Lola!"
    play sound "sfx/purr.mp3"
    hide lola
    show lolahappy
    "Holly scratched behind her ears, and Lola purred happily."
    l "She really likes you..."
    h "She's really nice!"
    l "Not to most people! Especially guys..."
    "I sat on the bed next to Holly and joined her in petting Lola."
    h "I'm glad she accepts me, then. It makes me feel special."
    menu:
        "{image=icon_friend.webp}You are special" if lena_holly > 8:
            $ renpy.block_rollback()
            if lena_go_holly < 3:
                $ lena_go_holly += 1
            l "Lola's not the only one who thinks you're special. I do too."
            $ fholly = "shy"
            h "Oh... Thank you, Lena..."
            l "We both can tell you have a good heart. You make people around you feel warm and cozy."
            hide holly
            show holly3 at lef3
            with short
            h "..."
            $ flena = "happy"
            l "Sorry if I made you uncomfortable with the compliments, but they're true!"
            $ fholly = "happyshy"
            hide holly3
            show holly at lef3
            with short
            "Holly smiled at me."
            h "I appreciate them."

        "She can tell you're nice":
            $ renpy.block_rollback()
            l "She can tell you have a good heart. Animals have a sense for that kind of thing."
            h "Especially cats, right? Even more so when they are black..."
            $ flena = "happy"
            l "A witch's cat!"
            $ fholly = "happy"
            h "Exactly, ha ha. But of the good kind."

        "You're good with animals?":
            $ renpy.block_rollback()
            l "You're good with animals?"
            $ fholly = "n"
            h "Not really... When I was a kid a dog bit me. I still have the scars."
            $ flena = "worried"
            l "Ouch..."
            h "And I was terrified of my grandmother's cat. He was old and really mean."
            $ flena = "smile"
            l "Well, Lola seems to love you."

    $ flena = "smile"
    $ fholly = "smile"
    h "I really like the lights in your room. They create a very cozy atmosphere."
    l "I used to have them hanging in my bedroom at my parents' place."
    l "When I first moved out, it felt so weird not having them in my room... So I took them with me and I've been carrying them over ever since."
    l "It makes me feel that no matter the place, even if it's a room in some random apartment, it's mine. My own space."
    $ fholly = "n"
    h "You've lived in many different places?"
    $ flena = "n"
    l "A few... I shared an apartment with a friend for a while and also lived in a dorm room."
    $  flena = "sad"
    l "After I had to drop out I lived with my ex for several months... And finally, I ended up here, with Louise."
    h "I see... Your life really is an adventure..."
    $ flena = "n"
    l "Yeah, too much of an adventure if you ask me! I often wish things were a bit more dull, just so I could take them easy."
    $ fholly = "sad"
    h "Meanwhile, I wish my life was more exciting."
    h "I've never moved out, and I don't have any interesting stories to tell..."
    l "What a weird thing to say, coming from a writer!"
    h "Those are just stories I come up with, not things that happened to me."
    if ian_lena_dating and v7_holly_kiss and ian_holly_dating == False:
        if ian_holly_sex:
            h "I recently thought I could live one of those stories... But some things are not meant to be."
        else:
            h "I recently thought I could live one of those stories... I came so close, but a kiss is all I could get."
        $ flena = "n"
        l "You're talking about Ian, right?"
        $ fholly = "blush"
        hide holly
        show holly3 at lef3
        with short
        h "Yes... And what's worse, I ended up causing trouble for both of you due to my selfishness."
        if ian_lena_over:
            h "Your relationship with Ian got damaged because of me..."
        l "We already settled the matter this morning. Stop kicking yourself in the teeth over it..."
        h "I can't help it. I feel ashamed of myself."
        h "You guys tried to be my friends, and what I did could've driven you away from me. And Ian, too."
    elif ian_holly_dating:
        $ flena = "smile"
        l "That's not true. Or isn't what happened with Ian a {i}thing{/i}?"
        $ fholly = "shy"
        hide holly
        show holly3 at lef3
        with short
        h "Yeah, about that... I guess I can't still fully believe it happened."
        l "Why not?"
        $ fholly = "blush"
        h "You know that feeling when you have been wishing for something... Hoping you could get it..."
        $ flena = "sad"
        h "And then you realize it was just a fantasy. Something that can only happen in your imagination."
        h "I guess I got used to that feeling. And when some of those wishes start coming true... Well, they're hard to believe."
    elif v7_holly_kiss:
        $ fholly = "blush"
        hide holly
        show holly3 at lef3
        with short
        if ian_holly_sex:
            h "I recently thought I could live one of those stories... But some things are not meant to be."
            $ flena = "serious"
            l "You're talking about Ian, right? He was a dick to you..."
            $ fholly = "n"
            h "I don't blame him. I understand why he did it, and he's not the only one at fault."
            $ flena = "sad"
            h "I wanted it to happen, too. Even if I knew it would end like this..."
            h "At least I got to be with him once, and it was wonderful."
            l "You'll get to have many other wonderful experiences, I'm sure."
            h "Yes... I know good things are happening to me, even if I don't get everything I want."
        else:
            h "I recently thought I could live one of those stories... I came so close, but a kiss is all I could get."
            l "You're talking about Ian, right?"
            h "Yeah..."
            $ fholly = "n"
            h "Don't get me wrong, though. I'm not complaining... I know good things are happening to me, even if I don't get everything I want."
        h "I'm starting to make a living doing what I like. And I've met you..."
    else:
        h "I'm not brave nor good enough to live exciting stories."
        $ fholly = "blush"
        hide holly
        show holly3 at lef3
        with short
        h "I'm just a foolish girl who won't be noticed by anyone..."
        $ flena = "sad"
        l "That's not true! There are a lot of people that admire you and love your books, myself included."
        $ fholly = "sad"
        h "And I'm grateful for that... But who I am as a writer and who I am as a person are two different things."
        h "People appreciate just one of those things... Just like Ian."
        l "I don't just appreciate you for your writing, Holly. I hope you know that."
        h "Yeah..."
    $ fholly = "blush"
    if holly_gym:
        h "Honestly, I still find myself astonished at the fact that you and Ivy would want to be my friends..."
        $ flena = "sad"
        l "Why would you say that?"
        h "You are the most incredible girls I've ever met. It's not even debatable."
        $ flena = "blush"
        h "And you are so cool..."
    else:
        h "Honestly, I still find myself astonished at the fact that you'd want to be my friend..."
        $ flena = "sad"
        l "Why would you say that?"
        h "You are the most incredible girl I've ever met, Lena. Really."
    $ flena = "blush"
    h "If this was a book, you'd be the heroine of the story. You're so much like most of the main characters I've written myself..."
    h "You have all the traits we all wish we had. You're independent and hardworking, you're confident, beautiful, smart, and really talented..."
    h "I'm nothing like that. I could never be the heroine of the story..."
    h "I'm just... {i}me{/i}."
    menu:
        "I'm far from perfect":
            $ renpy.block_rollback()
            l "The way you just described me is not exactly accurate, Holly. I'm far from perfect."
            l "I have my virtues, but I'm also a living mess! Just like anyone else. Just like you."
            if lena_wits < 8:
                call xp_up('wits') from _call_xp_up_425
            l "You seem to be very aware of your flaws, but you don't give yourself nearly enough credit!"
            h "I guess I never really learned to do that."
            h "I always relied on the image of myself other people reflected back to me... And it wasn't anything to write home about."
            l "That's not fair..."

        "Don't be so hard on yourself":
            $ renpy.block_rollback()
            $ flena = "serious"
            l "I don't like seeing how you talk about yourself, Holly. If this was anybody else saying those things about you, I'd slap them."
            h "Oh..."
            $ flena = "sad"
            if lena_go_holly > 1:
                l "And the fact that you're the one who thinks so lowly of yourself breaks my heart, because I see the wonderful person you really are, Holly."
            else:
                l "And the fact that you're the one who thinks so lowly of yourself makes me sad, because I can see that you have many cool traits."
            l "The only one who seems to fail to realize that is you."
            if lena_holly < 12:
                call friend_xp('holly', 1) from _call_friend_xp_585
            l "I know you've met a lot of people during your life that made you feel worthless... And that's not fair."

        "...":
            $ renpy.block_rollback()
            l "I don't know what to say, Holly..."
            h "You don't need to say anything... Maybe I've talked too much. I'm sorry..."
            l "No, it's not that. But you should try and give yourself a break. You're not so bland as you seem to think..."
            h "I'm not the only one who thinks it. It's what people around me showed me..."

    $ flena = "serious"
    l "It's not fair that what some shitty people said or did to you shaped your idea of who you are."
    $ flena = "smile"
    l "There's a lot of good in you, Holly. Take an honest look at yourself!"
    l "You're becoming a girl many admire. And not only for your books."
    l "It's what you do, It's who you are. You're touching a lot of people... myself included."
    h "That sounds like what Ian has been telling me..."
    if v7_holly_kiss and ian_holly_dating == False:
        if ian_lena_over or lena_ian_mad or ian_holly_sex:
            l "He might not be the most reliable guy, but I'm with him on this. You should listen to him."
        else:
            l "He might be clueless sometimes, but I'm with him on this. You should listen to him."
    else:
        l "Well, you should listen to him."
    $ fholly = "n"
    hide holly3
    show holly2 at lef3
    with short
    h "I've been trying to... And I think he's right about something."
    h "I started writing to hide from the world, trying to be happy while living in the fantasies I was creating."
    $ flena = "sad"
    h "I'm not heroine material. I'm just the shy girl who writes stories about characters much braver than herself. But..."
    h "Writing those stories has made me happy. And not just because I could lose myself in them..."
    h "Thanks to those stories I got to meet incredible people like you, Lena."
    $ flena = "blush"
    if ian_holly_dating:
        h "And I also got the chance to get close to a great guy like Ian... Those are the most wonderful things writing has given me."
    else:
        h "You thought I was worthy of your time and friendship... And that's the most wonderful thing writing has given me."
    menu:
        "{image=icon_love.webp}Kiss Holly" if lena_go_holly > 1:
            $ renpy.block_rollback()
            $ lena_go_holly = 4
            if ian_holly_dating or ian_lena_dating:
                show lena2 at rig with move
                l "..."
                $ flena = "worried"
                show lena2 at rig3 with move
                "I almost kissed her. I had been compelled by an impulse, something beyond reason..."
                "I was barely able to hold myself back."
                if ian_holly_dating:
                    "Holly and Ian had just started dating. Trying to kiss Holly was the worst thing I could do, and it made no sense... Did it?"
                elif v7_holly_kiss and ian_lena_dating:
                    "What was I thinking? Kissing Holly was probably the worst thing I could do, especially after what I told Ian for doing exactly that..."
                    "I had reacted very strongly to it, and decided to {i}forgive{/i} him... What would happen if I did the same, now?"
                elif v7_holly_kiss and ian_lena_over and lena_ian_mad == False:
                    "What was I thinking? Kissing Holly was probably the worst thing I could do, especially after what I told Ian for doing exactly that..."
                    if ian_holly_sex:
                        "Well, he had done more than just kissing her, but that was beside the point..."
                    "Things were already complicated enough between us three for me to tangle them up even more. I couldn't do that to Holly."
                else:
                    "What was I thinking? I was seeing Ian, and that made Holly give up on him."
                    "Kissing Holly would've made things super weird and complicated between her, Ian, and me."
                $ fholly = "blush"
                hide holly2
                show holly3 at lef3
                with short
                h "Did I say something weird...?"
                $ flena = "blush"
                l "No, no... Quite the opposite..."
                jump v8hollybtfl

            $ v8_holly_sex = "lena"
            $ lena_holly_sex = True
            jump v8hollylenasex

        "{image=icon_friend.webp}That's so beautiful..." if lena_holly > 8:
            $ renpy.block_rollback()
            label v8hollybtfl:
                $ flena = "worried"
            l "That's... one of the most beautiful things I've ever heard..."
            $ flena = "cry"
            l "Oh, no... It's even making me cry..."
            $ fholly = "worried"
            h "I'm so sorry...! I didn't mean to...!"
            $ flena = "smile"
            "I wiped away the two tears that were rolling down my cheeks."
            l "It's okay. That was the good kind of crying..."
            $ fholly = "blush"
            l "What you said just moved me so much. I guess you have the power of words, after all...!"
            if lena_charisma < 8:
                call xp_up('charisma') from _call_xp_up_426
            $ fholly = "happyshy"
            h "Perks of being a writer, I guess...! But I just spoke from the heart."
            l "I know. You're a beautiful person, Holly. You deserve good things happening to you."
            h "You're one of them."
            $ flena = "shy"
            l "Stop it, now you're making me blush...!"

        "I'm happy to hear that":
            $ renpy.block_rollback()
            $ flena = "smile"
            l "I'm so happy to hear that, Holly. Really."
            l "You deserve good things happening to you."
            if lena_holly < 10:
                call friend_xp('holly') from _call_friend_xp_586
                $ lena_holly = 10
            $ fholly = "happy"
            h "You're one of them."
            $ flena = "shy"
            l "Stop it, now you're making me blush for real..."

    $ fholly = "shy"
    h "Ha ha, sorry..."
    if v8_holly_ivy:
        $ fholly = "happy"
        h "Maybe it was the alcohol talking... But I think I've sobered up enough to go home now."
        h "Thank you for letting me stay here for a while, Lena."
        $ flena = "happy"
        l "No problem."
        scene lenahomenight
        show holly at lef
        show lena at rig
        with long
        "I walked Holly to the door and said goodbye."
        h "Today was a super fun day. Thanks for inviting me and for... Well, for everything else."
        l "I had a lot of fun today too. Best Sunday in a long time."
        l "See you soon, okay?"
        h "Yeah! Say thanks to Ivy on my behalf, too!"
        l "You can tell her that yourself. You have her number!"
        hide holly with short
        $ flena = "smile"
        l "She's adorable..."
        if lena_go_holly == 4:
            $ flena = "blush"
            l "I almost kissed her... I don't know what got into me."
            if v6_holly_kiss == "lena":
                l "I mean, I've already kissed her once, but now the mood was completely different."
                l "This time it felt much more... emotional."
            else:
                l "I suddenly felt like really wanting to do it..."
        jump v8hollyhomeleave
    else:
        $ flena = "smile"
        l "So, shall we decide what to eat and what movie to watch?"
        $ fholly = "happy"
        h "Sure!"
        if lena_go_holly == 4:
            $ flena = "blush"
            "I was still astonished at myself for almost kissing Holly... What got into me?"
            if v6_holly_kiss == "lena":
                "I already kissed her once, but the mood was completely different."
                "This time it felt much more... emotional."
            else:
                "I suddenly felt like really wanting to do it..."
            "I shook my head and tried not to think about it. I had to pick a movie to watch with Holly."
        scene lenahomenight with long
        "This was a very nice way to end my Sunday."
        $ flena = "smile"
        $ flouise = "happy"
        show lena
        show holly2 at lef3
        show louise at rig3
        with short
        "Louise joined me and Holly. We cooked a couple of homemade pizzas and sat in the living room to watch a movie."
        if v8_jeremy_sex:
            $ flena = "worried"
            "I didn't know how to look Louise in the face after what I did with Jeremy this morning..."
            "Thankfully she was none the wiser and I just played it off."
            $ flena = "flirt"
            "It had been a very enjoyable Sunday all around... Especially the morning, when I got to finally enjoy Jeremy all by myself..."
            "I would need to tell Ivy about it!"
            $ flena = "smile"
            "Weird as some stuff might be, I finally felt like the pieces were starting to fall into place."
        else:
            "It had been a very enjoyable Sunday all around... And I finally felt like the pieces were starting to fall into place."
    "I didn't need to face everything on my own anymore..."
    $ renpy.end_replay()
    stop music fadeout 2.0
    jump v8ending

## HOLLY SEX ###############################################################################################################################################################################################################################
label v8hollylenasex:
    $ holly_glasses = False
    stop music fadeout 2.0
    play music "music/sex_nice.mp3" loop
    scene v8_holly1
    if lena_tattoo2:
        show v8_holly1_t2
    show v8_holly1_clothes
    with long
    "I held Holly's face with my hands and leaned in for a kiss."
    "It was like my body moved on its own, completely disconnected from reason."
    "There was this feeling swelling inside of me, a feeling I needed to express. And words weren't going to be enough."
    "Before I could even realize it, I found myself locked in a deep, long kiss with Holly."
    if v6_holly_kiss == "lena":
        "This wasn't like our first kiss. I felt no hesitation from her."
        "We were both pulled by the same spontaneous and unavoidable force, a force that was pushing us together in a way we were not expecting..."
        "I was surprised at myself. But I didn't let that ruin the moment."
    else:
        "Holly twitched, surprised at my kiss. I was surprised at myself, too."
        "I was about to pull back and apologize, but then I felt Holly's fingers wrapping around my hand, and her tongue welcoming mine."
        "Her kiss brought me back into the moment, now trapped in it for good."
    "I could clearly feel Holly's emotions in the way she was kissing me back. We both wanted this."
    "For several minutes our tongues spoke by themselves, without using a single word. For once, they were not needed."
    "Holly's body told me all I needed to know. Her caresses, her increasingly disheveled breathing, the way she was pressing herself against me..."
    "We both wanted to share more than just kisses."
    scene v8_holly2
    if lena_tattoo2:
        show v8_holly2_t2
    if lena_tattoo3:
        show v8_holly2_t3
    with long
    "I leaned back and looked at Holly's eyes. She stared at me, blushing and silent."
    "I didn't want to break our silence. I let my hands continue to do the talking."
    "I snuck them under Holly's sweater and pulled it up. She raised her arms to make it easier for me."
    "She also tried to get my clothes off me with trembling fingers. I helped her."
    scene v8_holly1
    if lena_tattoo2:
        show v8_holly1_t2
    with long
    "Our kisses continued as we stripped each other down."
    "Holly gave off a mix of desire and anxiety. I could tell she wanted me, but she was also nervous, and a bit scared, even."
    "I wanted those feelings to clear away. I wanted to make Holly feel good..."
    scene v8_holly3
    if lena_tattoo2:
        show v8_holly3_t2
    if lena_tattoo3:
        show v8_holly3_t3
    with long
    "I pushed her gently onto the bed, and climbed on top of her."
    "Holly shivered when she felt my touch. My hands ran up her body, caressing her waist, her arms, her neck..."
    "I kissed her again, my chest pressed against her, sharing our warmth."
    play sound "sfx/mh1.mp3"
    h "Nhh..."
    "Her skin was so soft... Her smell so sweet... And her tiny moans so lovely..."
    "I could feel Holly's legs under me, how she pressed her knees together, how she twisted her hips and trembled with growing excitement..."
    "But it was almost like she was uncomfortable, fighting those feelings."
    "That was exactly the opposite of what I wanted."
    "I kissed her deeply again, bringing her attention back to me, and I softly whispered the first word we exchanged since this started."
    l "Relax..."
    scene v8_holly4 with long
    "I removed Holly's panties, the last piece of fabric covering her body. And then, slowly, I lowered both my hand and my kisses over her body."
    "My lips rolled down her neck and her collarbone, and my fingertips slid across her belly until they reached her mons."
    play sound "sfx/moan2.mp3"
    h "Ahh...!" with vpunch
    "Holly twitched when my tongue grazed her nipple and my fingers progressed even further."
    l "It's okay... Just enjoy it..."
    "I began licking Holly's hard nipple and brushing my finger over her slit, very softly, almost in a teasing way."
    "Holly's moans became a bit louder, and her breathing heavier."
    h "Lena... Ahnn... I feel you so much...!"
    "I had to be tactful... My finger was probably too rough to use right at that moment, so I went with something softer."
    scene v8_holly5
    if lena_tattoo2:
        show v8_holly5_t2
    with long
    "I got between Holly's legs and licked her vulva from bottom to top."
    play sound "sfx/ah3.mp3"
    h "Lena...!"
    "It was pink and pretty..."
    "I ran my tongue across once more, slowly, wetting it with my saliva."
    "Holly's breathing was getting quicker and rougher, as she reacted to my caresses."
    "I licked her pussy with long, gentle strokes, slowly spreading her lips and gaining access to her clit."
    "Her button was small and hard, and her entire body shook when my tongue flicked it."
    play sound "sfx/moan1.mp3"
    h "Ohhh, Lena...!"
    l "Do you like it?"
    h "Yes... I like it a lot...!"
    "I focused my attention on Holly's reactions. She was really sensitive, and even the slightest caress made her tremble."
    "Holly began panting loudly, squeezing her knees together, raising her hips..."
    play sound "sfx/moanlong.mp3"
    h "Ah... Ahh... Ahh...! Ahhh...!{w=1.0}{nw}"
    scene v8_holly6 with flash
    h "Ahhhh!!!{w=0.5}{nw}" with vpunch
    h "Oh God...!!{w=0.5}{nw}" with vpunch
    with vpunch
    pause 0.5
    h "Ohhh..."
    "Holly's body loosened after being struck by what seemed to be a very powerful orgasm."
    "I let her have a few seconds to enjoy the aftermath of her climax, caressing her thighs as her panting died down progressively."
    $ flena = "flirtshy"
    $ fholly = "flirtshy"
    scene lenaroomnight
    show lenanude2 at rig
    show hollynude3 at lef
    with long
    "I sat on the bed and looked at Holly. She blushed and smiled, and then looked away, shy."
    l "..."
    h "..."
    "We had always found it easy to talk to each other, but for the first time we couldn't find words."
    "Nothing we said could match the talking we had just done with our bodies..."
    "I was wondering if I should say something, when Holly broke the silence."
    h "That was wonderful..."
    $ flena = "shy"
    l "It really was..."
    if ian_lena_dating and ian_lena_over: # outdated dialogue tree with some touching lines, accessible only with console
        $ fholly = "blush"
        h "But... What about Ian?"
        $ flena = "blush"
        l "What about him?"
        h "I mean... Are you and him still...?"
        $ flena = "sad"
        l "No... That's over."
        if lena_ian_love:
            h "Really...?"
            l "Ian, he's... I thought he could be someone special. But I don't want to keep things going after what happened."
            l "I still consider him my friend, and I think it's better if we stay just like that... friends."
            l "To protect my own feelings, and because... Because you're also special to me, Holly."
            h "Oh..."
            l "I didn't want my relationship with him to drive a wedge between you and me."
        else:
            l "I still consider him my friend, and I think it's better if we stay just like that... friends."
            l "I know your feelings for Ian go deeper than just that, and I didn't like him hurting you..."
            l "I also don't want my relationship with Ian to drive a wedge between you and me. Because you're someone I really value, Holly..."
        h "Sorry. Maybe I shouldn't have asked... But I, uh..."
        h "I'm a bit confused right now."
        "I felt confused, too."
        "I had no idea what this was, but my desire for Holly was real..."
        "A peculiar flavor of desire, notably different from what I had experienced before."
        l "Do you regret what just happened...?"
        $ fholly = "worried"
        h "No, not at all! I mean...!"
        $ fholly = "blush"
        h "I just can't help but overthink stuff, and..."
        $ fholly = "shy"
        h "I'm having trouble making sense of things right now. This was just so..."
        h "Gosh, I can't find words to describe it."
        menu:
            "Unexpected?":
                $ renpy.block_rollback()
                $ flena = "shy"
                l "Is \"unexpected\" the word you're looking for, maybe?"
                $ fholly = "shy"
                h "Yeah, for sure, this was unexpected, but also... It was amazing..."

            "Amazing?":
                $ renpy.block_rollback()
                $ flena = "flirtshy"
                l "Is \"amazing\" the word you're looking for, maybe? Because it felt amazing to me..."
                $ fholly = "shy"
                h "Yes, amazing is definitely a perfect word to describe it... And also unexpected..."
                if lena_lust < 8:
                    call xp_up('lust') from _call_xp_up_427
                h "Bust most of all, amazing..."

            "Weird?":
                $ renpy.block_rollback()
                l "Is \"weird\" the word you're looking for, maybe?"
                $ fholly = "blush"
                h "No, not weird... I mean, maybe a bit, and also unexpected, but..."
                $ fholly = "shy"
                h "If I had to pick just one word it would be... Amazing."
                $ flena = "shy"

        h "I never thought that I... That we..."
        $ fholly = "blush"
        h "Sorry, I'm talking too much and probably ruining the mood."
        h "And I haven't... I haven't made you feel good in return."
        $ flena = "shy"
        l "Oh! You don't need to..."
        h "But I want to. Or I'd like to try..."
        l "I won't stop you..."
        $ fholly = "flirt"
        h "In that case..."
    else:
        $ fholly = "blush"
        h "I..."
        h "I want to make you feel good, too..."
    scene v8_holly7
    if lena_tattoo2:
        show v8_holly7_t2
    if lena_tattoo3:
        show v8_holly7_t3
    with long
    "To my surprise Holly took the lead, embracing me and making me lay on my back."
    "This time it was her tongue that was put to use, and I felt its tingling touch on my skin."
    "She began caressing, licking, and kissing my breasts, making me shiver."
    "The way she was doing it was a bit timid, but so arousing..."
    l "Mhhh... Holly..."
    h "Your breasts are so beautiful..."
    l "I love how you're licking them... Your tongue feels so tender..."
    "Now was my turn to pant with excitement. I had been so focused on making Holly feel comfortable that I had forgotten how horny I was..."
    "I squeezed my legs around Holly's hips, wanting to feel her even closer."
    h "Do you want me to try and... do what you did to me?"
    l "Yes, please... Lick me down there, Holly..."
    scene v8_holly8
    if lena_piercing1:
        show v8_holly8_p1
    elif lena_piercing2:
        show v8_holly8_p2
    if lena_tattoo2:
        show v8_holly8_t2
    if lena_tattoo1:
        show v8_holly8_t1
    with long
    play sound "sfx/mh1.mp3"
    "I spread my legs apart for Holly, offering her my pussy."
    "Just like I had done before, she got between them and grazed my sex with her tongue."
    "Her caresses were slow and a bit clumsy. I could feel she wasn't exactly sure what to do."
    l "Press your tongue against my clit, Holly... Yeah, right there..."
    l "Mhhh... Use your lips too... I want to feel your kisses..."
    "I held Holly's hand and caressed her hair while giving her indications. She followed them eagerly..."
    "Her timidness was still there, but I could feel it starting to loosen up."
    "Her tongue was licking me with more determination, faster, deeper..."
    l "Yes, just like that... You can do it a bit harder... From bottom to top..."
    h "Mmmhh..."
    l "Yes... Keep that rhythm...!"
    l "Oh, Holly...!"
    play sound "sfx/ah6.mp3"
    l "Mhhh!!" with vpunch
    pause 0.5
    with vpunch
    pause 0.5
    l "Ahhh, yes..."
    h "Did I do it? Did I make you cum?"
    l "Yes..."
    "I wished she would've continued to lick me a bit more, but she stopped as soon as she felt the first signs of my orgasm."
    "Still, it was a very nice feeling."
    stop music fadeout 2.0
    scene v8_holly9
    if lena_tattoo2:
        show v8_holly9_t2
    with long
    play music "music/hollys_theme.mp3" loop
    l "Come here."
    "I pulled Holly up and embraced her. This was an even better feeling..."
    "She felt so soft and delicate between my arms. Her steady breathing, her warmth, and her sweet smell made me feel so relaxed..."
    h "Thank you, Lena..."
    l "For what?"
    h "For... this. For everything."
    h "Since we started hanging out together, I..."
    h "I haven't felt like this in such a long time. So... at ease."
    h "And so hopeful."
    l "I'm so glad... You deserve to feel those things, and many more."
    l "You're a beautiful person, Holly. You deserve good things happening to you."
    h "You're one of them."
    l "Now you're gonna make me blush..."
    h "Now? After what we just did?"
    "We looked at each other and laughed. All that awkwardness from before seemed to have faded away."
    "We were back to being ourselves, two friends who enjoyed and cared for each other."
    $ fholly = "shy"
    $ flena = "smile"
    scene lenaroomnight
    show lenanude2 at rig
    show hollynude2 at lef
    with long
    if v8_holly_ivy:
        h "I think I'm good to go home, now. Thank you so much, Lena..."
    else:
        l "It's gotten a bit late, so I don't think we have time to watch that movie, but how about some dinner?"
        h "I'd love to, but I should get going..."
    l "You can stay the night, if you want to..."
    h "That'd be great, but I haven't given my parents the heads up... But if you want, next time..."
    l "We'll do a sleepover."
    $ fholly = "happyshy"
    h "Yes, please!"
    scene lenahomenight with long
    $ holly_glasses = True
    $ lena_look = 1
    show lenabra at rig
    show holly at lef
    with short
    "We put our clothes back on and I walked Holly to the door."
    h "Today was a super fun day. Thanks for inviting me and for..."
    $ fholly = "shy"
    hide holly
    show holly3 at lef
    with short
    h "Everything else."
    l "I had a lot of fun today too. Best Sunday in a long time."
    l "See you soon, okay?"
    h "Yeah!"
    $ fholly = "happyshy"
    hide holly3 with short
    show lenabra at truecenter with move
    $ flena = "shy"
    l "Wow... That really happened. Holly..."
    $ flena = "blush"
    "What did it mean, though? Was it something that happened in the spur of the moment?"
    "A way for two close friends to bond both on a deeper and physical level?"
    if ian_lena_dating and ian_lena_over:
        "Would it happen again...? And what would Ian think if he learned about this?"
        $ flena = "sad"
        "That didn't really matter anymore, did it? Our thing was over, after all..."
        if ian_holly_sex:
            l "I got mad at him for getting in bed with Holly, and now I've done the same... Am I being a big fat hypocrite here?"
        elif v7_holly_kiss:
            l "I grilled him for kissing Holly, and now I've done a lot more than that. Am I being a big fat hypocrite here?"
        l "But at least I made things clear with Ian beforehand... And now we all know where we stand."
        l "Both Holly and I were disappointed with Ian's actions. Maybe that's the reason this happened?"
    else:
        $ flena = "smile"
        l "No need to think about that now... I just want to enjoy this nice feeling."
    show lenabra at rig with move
    $ gallery_unlock_scene("CH08_S12")
    label v8hollyhomeleave:
        stop music fadeout 2.0
    if lena_louise_sex:
        $ flouise = "sad"
    else:
        $ flouise = "n"
    show louise2 at lef with short
    lo "The one who just left was Holly, right?"
    $ flena = "n"
    l "Louise... Yeah, it was her. She came over for a while after we were done shopping..."
    if lena_louise_sex:
        lo "The door to your room was closed and I didn't want to interrupt. You seemed... busy."
        $ flena = "worried"
        l "Uh, yeah... Thanks for giving us privacy."
    else:
        lo "I see... Did you have fun?"
        if v8_jeremy_sex:
            $ flena = "worried"
            "I didn't know how to look Louise in the face after what I did with Jeremy this morning..."
            "Thankfully she was none the wiser and I just played it off."
        $ flena = "smile"
        l "Yeah, it was great."
    if lena_tattoo1 or lena_tattoo2 or lena_tattoo3:
        lo "Nice tattoo, by the way. I had no idea you were planning on getting one..."
        l "Oh, yeah. I was on the fence about it..."
    lo "So... Do you want to watch that movie we talked about?"
    l "It's a bit late and I'm tired. It's been a long day."
    lo "Next time, then. Or maybe you can go shopping with me, too."
    $ flena = "smile"
    l "Sure, that would be fun. Good night, Louise."
    scene lenaroomnight with long
    show lenabra with short
    "What a strange and intense week this had been..."
    if lena_holly_sex:
        "This had been a very enjoyable Sunday all around... And with an incredible ending..."
        "I finally felt like the pieces were starting to fall into place."
    else:
        "This had been a very enjoyable Sunday all around. And I finally felt like the pieces were starting to fall into place."
    "I didn't need to face everything on my own anymore..."
    $ renpy.end_replay()
    stop music fadeout 2.0
    jump v8ending
#########################################################################################################################################################################################################################################################################################
## AT IVYS PLACE #################################################################################################################################################################################################################################################################################################
##########################################################################################################################################################################################################################################################################################

label gallery_CH08_S10:
        if _in_replay:
            call setup_CH08_S10 from _call_setup_CH08_S10

label v8sundayivy:
    stop music fadeout 2.0
    $ v8_drinks = "n" # what drinks Ivy prepares
    play sound "sfx/car.mp3"
    scene street
    show ivy_car
    with long
    "I had to admit that it felt good cruising through the streets with my friends in a flashy convertible."
    "The wind swept our hair and made me feel alive. These were the moments to treasure..."
    play sound "sfx/door_home.mp3"
    play music "music/flirty.mp3" loop
    scene ivyroom with long
    $ fholly = "smile"
    $ flena = "smile"
    $ fivy = "n"
    show ivy
    show holly2 at lef3
    show lena at rig3
    with short
    v "Here we are."
    if lena_tattoo1 or lena_tattoo2 or lena_tattoo3:
        v "How's the tattoo holding up?"
        l "It's fine. I still feel the area a bit numb..."
        v "Here, clean it a bit and put on the cream Jess gave you."
    v "Let's toast! What do you want to drink?"
    menu:
        "{image=icon_charisma.webp}Give me something strong" if lena_charisma > 4:
            $ renpy.block_rollback()
            $ v8_drinks = "gin"
            l "I'd like something strong. Maybe some gin, or vodka?"
            $ fivy = "flirt"
            v "Great idea! I'll prepare us some gin tonics!"
            if lena_ivy < 12:
                call friend_xp('ivy', 1) from _call_friend_xp_587
            pause 0.8
            play sound "sfx/icecube.mp3"
            "We watched Ivy skillfully mix the drinks, finishing them off with lime juice and a spring of mint."
            l "You're a pro at this!"
            hide ivy
            show ivy2
            with short
            v "You pick up a thing or two when you work in a nightclub!"

        "Wine":
            $ renpy.block_rollback()
            $ v8_drinks = "wine"
            l "You offered us wine, didn't you?"
            v "Yeah. I have this really nice white wine, you'll love it."
            "Ivy poured three glasses and handed them to me and Holly."
            hide ivy
            show ivy2
            with short

        "Water":
            $ renpy.block_rollback()
            $ v8_drinks = "water"
            $ flena = "n"
            l "Water for me."
            $ fivy = "sad"
            h "For me too..."
            v "What? You can't toast with water!"
            l "I have to work tomorrow, I don't want to get drunk."
            $ fivy = "serious"
            v "Oh, go to hell! You won't get drunk over a glass of wine!"
            if lena_ivy > 3:
                call friend_xp('ivy', -1) from _call_friend_xp_588
            v "I didn't invite you over to drink water. Have at least one glass!"
            l "Alright, but just one."
            $ fivy = "sad"
            v "You too, Holly."
            h "Sure."
            $ fivy = "n"
            "Ivy poured three glasses and handed them to me and Holly."
            hide ivy
            show ivy2
            with short

    # stalkfap talk
    $ fivy = "smile"
    $ flena = "smile"
    $ fholly = "smile"
    v "Cheers, girls!"
    play sound "sfx/toast.mp3"
    l "Mhhh, this is nice."
    if v8_drinks == "gin":
        if holly_change < 2:
            $ holly_change += 1
        v "My gin tonics are awesome, aren't they? Do you like it, Holly?"
        $ fholly = "happy"
        h "It's a bit strong, but it's pretty nice, yeah! I like it."
    else:
        v "See? I told you you'd like it."
        if v8_drinks == "water":
            v "And you wanted to drink water..."
        v "Do you like it, Holly?"
        $ fholly = "happy"
        h "Yes, it's good!"
    v "Let me put my new acquisitions in the wardrobe..."
    "Ivy stored the clothes she bought in her closet. She had a big shoe collection..."
    $ flena = "worried"
    "And a lot of expensive-looking handbags, too."
    $ flena = "n"
    l "Business is booming, right Ivy?"
    $ fivy = "n"
    hide ivy2
    show ivy
    with short
    v "Huh?"
    l "You have an apartment all for yourself, you can afford to buy expensive clothes and you even got a car!"
    if stalkfap_pro:
        l "How do you do it? I can't seem to get any real money from Stalkfap..."
    else:
        l "You're getting that much money from Stalkfap?"
    $ fholly = "n"
    v "It's not just Stalkfap. There's also the pole dancing classes, the gig at Blazer, the occasional photo shoot..."
    l "I don't know how lucrative the gogo dancer job is, but I know being a gym instructor and occasional photo shoots don't pay {i}that{/i} much."
    v "Well, yeah, you're right. Most of my income comes from Stalkfap, but every bit helps!"
    if stalkfap_pro:
        l "So, how do you do it?"
        v "I've already told you... Build a strong Peoplegram following, post selfies, and the occasional pro shoot, but mostly homemade stuff..."
        l "I'm already doing that. I posted a video dancing, just like you told me to."
        v "And? Did that work?"
        if v8_stalkfap_comments:
            l "Well, I got some DM's asking for more... {i}private{/i} stuff."
            v "That's where the real money is. Just give them what they ask for and jack up the prices. You'll be surprised at what some of those guys are willing to pay to see something sexy."
        else:
            $ flena = "sad"
            l "I'm not sure."
            v "Did you check the DMs?"
            l "Not really..."
            v "Then do it! Don't you listen to me? That's where the real money is."
            $ flena = "n"
            v "Just give them what they ask for and jack up the prices. You'll be surprised at what some of those guys are willing to pay to see something sexy."
    elif stalkfap:
        l "I can't fathom how one can earn so much money from Stalkfap..."
        v "I already told you my tricks! Build a strong Peoplegram following, post selfies, and the occasional pro shoot, but mostly homemade stuff..."
        v "Hot stuff!"
        v "But you want to stick to just artistic nudes, so..."
    else:
        v "I told you to get on it too, but you wouldn't listen! It would solve many of your problems..."
    $ fholly = "blush"
    h "You're talking about that social media platform where girls post their... sexy pictures?"
    v "Yeah, that's the one."
    h "I've heard it's pretty controversial."
    v "Controversial? Why?"
    $ fholly = "n"
    hide holly2
    show holly3 at lef3
    with short
    h "Well, it promotes the objectification of women and..."
    v "Bullshit. That's what the ugly hags want you to believe because they are jealous they are not getting the attention and money we get!"
    hide friend_down
    hide friend_up
    menu:
        "{image=icon_lust.webp}It's empowering" if stalkfap or lena_lust > 6:
            $ renpy.block_rollback()
            $ flena = "smile"
            l "I think it's empowering, actually."
            l "Society has always repressed female sexuality. For the love of God, in some countries, women still have to wear {i}burkas{/i} and stuff like that..."
            l "We've reached a point where we, women, have reclaimed ownership of our own bodies and sexuality."
            if stalkfap:
                l "And it's allowing girls to make a living out of it and become financially independent, like me and Ivy, for example."
                $ fivy = "smile"
                v "I couldn't have said it better!"
            else:
                l "And it's allowing girls to make a living out of it and become financially independent, like me and Ivy."
                $ fivy = "smile"
                v "I'm surprised you refused to get on Stalkfap thinking like that! But I agree with you."
            if lena_ivy < 12:
                call friend_xp('ivy', 1) from _call_friend_xp_589
            if lena_charisma < 8:
                call xp_up ('charisma') from _call_xp_up_428

        "People should be free to do what they want":
            $ renpy.block_rollback()
            l "I think people should be able to do what they want, as long as they don't hurt others."
            v "That's the problem. People who don't like what you do will always claim they're being hurt by it."
            l "I guess it's complicated... I can see why some people are concerned about this issue."
            l "I mean, they have a point when they say this trend places a lot of value on looks and sexualizes women, turning them into consumables..."
            l "But, at the same time, it's allowing young girls to take ownership of their bodies and sexuality and take advantage, which is arguably a good thing."
            if stalkfap:
                l "I'm trying to support myself using that platform, after all..."
            l "I don't know, you can argue for both perspectives. Personally, I say live and let live."
            if lena_charisma < 8:
                call xp_up ('charisma') from _call_xp_up_429

        "I can see why it's problematic":
            $ renpy.block_rollback()
            $ flena = "sad"
            l "I can see why it's problematic. It places a lot of value on looks and sexualizes women, turning them into consumables..."
            v "Hey, it's not like anybody is forcing me to do this. And I'm my own boss, I only post and share what I'm comfortable with."
            l "That's not the point. The point is that it creates a trend where girls will follow what society tells them is valuable, forcing an overly sexualized view of themselves..."
            if lena_wits < 8:
                call xp_up('wits') from _call_xp_up_430
            $ fivy = "serious"
            if stalkfap:
                v "Where's this coming from? You are on Stalkfap, too! Don't be hypocritical now..."
                l "I'm not. I'm just telling you what concerned people say about it."
            else:
                v "Come on, Lena. Do you really think like that?"
                l "All I'm saying is I understand why people are concerned about it..."
            v "Concerned people can eat my ass. I don't need them to take away my livelihood!"
            if lena_ivy > 3:
                call friend_xp('ivy', -1) from _call_friend_xp_590
            $ fholly = "sad"
            h "Sorry... I didn't want to spark controversy..."
            $ flena = "n"
            $ fivy = "n"
            l "It's okay."

    # holly sex talk
    $ fholly = "blush"
    if stalkfap:
        h "I didn't know you were on that platform, Lena..."
        l "Yeah, well... As Ivy said, every bit helps. And I'm in need of some extra income right now."
    h "I suppose it can be a very freeing way of making a living, but people have to be interested in you in the first place..."
    h "Girls like me wouldn't make a cent in that business."
    $ fivy = "flirt"
    hide ivy
    show ivy2
    with short
    v "You'd be surprised! Guys like all sorts of things... And you're petite and rather cute..."
    if stalkfap and lena_wits > 3:
        l "And don't forget about your fame as a writer! Can you imagine if your fans learned that they can discover a secret side of their favorite author?"
        $ fholly = "worried"
        l "You'd probably attract followers even faster than me."
        v "Oh damn, that would create some buzz for sure, ha ha! But you'd still need to work on your confidence to retain their interest."
        $ fholly = "blush"
        v "I'm sure if you follow the advice I gave you earlier you'll multiply your audience!"
    else:
        v "I'm sure if you follow the advice I gave you earlier you'll get an audience!"
    ## holly ian sex
    if ian_holly_sex:
        v "And speaking about you having an audience... What's up with you and Ian?"
        if ian_holly_dating:
            $ fholly = "shy"
            h "Oh, that..."
            v "You finally got some action! I wanna know how that went!"
            if ian_lena_over:
                $ fholly = "blush"
                h "I'm not sure Lena wants to hear about it..."
                v "I'm sure she does, but she won't admit to it. You can tell me."
                l "Go ahead."
                $ fholly = "shy"
            h "Um, well..."
        else:
            $ flena = "sad"
            $ fholly = "sad"
            l "I don't know if Holly wants to talk about it..."
            v "So they fucked and then he bailed. Dick move, yeah, but at least she got some action!"
            v "I wanna know how that went!"
            if ian_lena_over:
                h "I'm not sure Lena wants to hear about it..."
                v "I'm sure she does, but she won't admit to it. You can tell me."
            else:
                h "It's okay... I can talk about it..."
                $ flena = "n"
            h "So, um..."
        h "There was this misunderstanding and we ended up sharing the hotel room. We were talking and I kissed him..."
        h "One thing led to another and..."
        v "Details! I want the details."
        menu:
            "Let Holly tell the story":
                $ renpy.block_rollback()
                $ fholly = "blush"
                h "I was pretty nervous. I tried to hide it, but I think he noticed..."
                h "In any case, he didn't try to rush things, which was nice... He tried to make me feel comfortable..."
                if v7_holly_lick:
                    h "He even licked me down there. It felt good, but a bit awkward, maybe because I was so nervous..."
                    v "He \"licked you down there\", how cute! So he ate your pussy, that earns him a point. Not all guys do that."
                else:
                    v "At least it sounds like he knew what he was doing."
                if v7_holly_bj or v8_holly_bj:
                    if v7_holly_bj:
                        h "He also asked me to... suck his dick. But I don't think he liked it."
                    else:
                        h "He also let me... suck his dick. But I don't think he liked it."
                    v "Sucking dick is an art few people are really proficient at, even if they boast otherwise!"
                    v "I know I'm not mistaken when I say you need more practice. A lot of it."
                v "So, what else? Did he fuck you?"
                h "Yes... He got on top of me and..."
                h "It was a bit difficult to put it in at first. Thankfully he was very gentle."
                $ fivy = "smile"
                v "Are you that tight? Or is he really big?"
                h "I'm not sure if I'm tight or not... But he's definitely big."
                v "So big it didn't fit?"
                h "It did fit after a while... As I said, I was pretty nervous..."
                $ fivy = "sad"
                v "So he didn't manage to get you horny?"
                h "No, he did... But I couldn't help but feel somewhat ashamed, too..."
                if v7_holly_rough:
                    h "Especially when he... got me on all fours and started, you know..."
                    h "He got pretty excited. I was sore the next morning..."
                $ fivy = "n"
                v "Did you cum?"
                h "Yeah..."
                hide ivy2
                show ivy
                with short
                v "I see! So, not a bad lay, I'd say. But you were too awkward to properly enjoy it and had him do all the work."

            "No need for details":
                $ renpy.block_rollback()
                $ flena = "n"
                l "Don't listen to her, Holly. No need for details."
                l "Just give us the short story."
                $ fivy = "n"
                v "Party pooper."
                h "It was nice... He was gentle and didn't try to rush things, which helped me a lot, since I was really nervous..."
                v "He didn't manage to get you horny?"
                h "No, I was... But I couldn't help but feel somewhat ashamed, too..."
                v "Did you cum?"
                h "Yeah..."
                hide ivy2
                show ivy
                with short
                v "Well, that's something. From what I gather, it wasn't a bad lay, but you were too awkward to properly enjoy it and had him do all the work."

        h "I guess..."
        # holly dating ian
        if ian_holly_dating:
            if ian_lena_over:
                v "Well, he liked it enough to want to do it again! Even if that meant rejecting Lena..."
                $ flena = "serious"
                l "Will you stop that already?"
                v "Sorry, sorry! I just find it funny that you lost to Holly..."
                $ flena = "sad"
                h "It's not like that, I..."
                v "I'm just pulling your leg, girls."
            else:
                v "Well, he liked it enough to want to do it again!"
            v "It'll be good for you to keep hooking up with him. It'll give you some much-needed experience..."
            v "Have you been practicing with the dildo we bought?"
            h "Not really..."
            $ flena = "n"
            v "Well, you should. I'm sure he'll appreciate it if you learn how to properly suck dick..."
            v "And you need to stop being so awkward in bed. Learn to let loose!"
            h "I'm not sure how to do that..."
            v "Have you tried some sexting with him?"
            h "No..."
            v "Do that. He'll like it and it's the easiest step you can take to be a bit more assertive."
            h "I see..."
            $ fivy = "flirt"
            v "And that pretty much wraps up our first session. Thanks for dropping by Dr. Ivy's sexology seminar, ha ha!"
            $ fholly = "shy"
            h "Thanks for the advice..."
            $ fivy = "n"
            v "Anyway..."
        # ian rejected holly after sex
        else:
            h "Maybe that's why he told me this would be a one-time thing... He probably didn't like it at all."
            l "I don't think that's the case..."
            v "Could be. I doubt you gave him the best experience of his life..."
            $ flena = "serious"
            l "Ivy!"
            $ fivy = "n"
            v "What? I'm just spitting facts. Better to address the problem than to be willfully blind to it, don't you think?"
            $ flena = "sad"
            h "Address the problem?"
            $ fivy = "smile"
            v "Yeah. You need to stop being so awkward in bed. Learn to let loose!"
            h "I'm not sure how to do that..."
            v "Easy. You need to get more experience. Go out of your comfort zone!"
            v "Have you been practicing with the dildo we bought?"
            h "Not really..."
            $ fivy = "n"
            v "You need to step up your game! Start going out, meeting some guys, hooking up..."
            if holly_guy:
                v "I'm getting tired of pointing it out, but I introduced you to Mark exactly for that reason."
            v "Take your nose out of your books! You have a lot to learn!"
            $ fholly = "n"
            h "You might be right..."
            v "Anyway..."
    ## no ian holly
    else:
        v "And speaking about that... Have you turned the page on Ian yet?"
        $ fholly = "sad"
        if ian_lena_dating and ian_lena_over == False:
            v "I hope so, since Lena claimed him first..."
            $ flena = "worried"
            l "Ivy, please..."
            v "What? You could always share..."
            $ flena = "serious"
            l "That's enough of that."
            v "Ha ha, sorry, I was just pulling your leg."
        elif ian_lena_over:
            v "Though there shouldn't be a problem with you going for it now that Lena kicked him to the curb, am I right?"
            $ flena = "worried"
            h "That's not..."
            $ flena = "serious"
            l "Ivy, please!"
            v "Ha ha, sorry, I was just pulling your leg."
        h "There's nothing to turn the page on."
        $ flena = "sad"
        $ fivy = "n"
        h "He was never interested in the first place, and it's not like something ever happened between us..."
        if v7_holly_kiss:
            v "Well, you kissed him, right?"
            h "Yeah, but... That probably was a mistake."
            l "At least you were honest about your feelings, and now the cards are on the table."
            h "Too bad I didn't have a winning hand."
            if ian_lena_dating:
                $ fholly = "worried"
                h "Not that I wanted to steal him from you or anything...!"
                $ flena = "n"
                l "I know, Holly. Don't fret."
                $ fholly = "blush"
        elif v7_holly_trip:
            v "Well, you spent the weekend with him, right?"
            h "He came with me to the book fair, but that's it... We're just colleagues."
        h "I've just accepted I'm not the kind of girl who he'd find attractive. I'm happy just being his friend."
        v "I guess not many guys would find you attractive, yeah..."
        $ flena = "serious"
        l "Ivy!"
        $ fivy = "n"
        v "What? I'm just spitting facts. Better to address the problem than to be willfully blind to it, don't you think?"
        $ flena = "sad"
        h "Address the problem?"
        $ fivy = "smile"
        v "Yeah. You are unattractive, so learn to be attractive."
        h "How does one learn that...?"
        v "Joining my pole dancing class, for starters! So you're already on the right path."
        v "You also need to go out of your comfort zone, and get some experience!"
        v "Have you been practicing with the dildo we bought?"
        h "Not really..."
        $ fivy = "n"
        v "You need to step up your game! Start going out, meeting some guys, hooking up..."
        if holly_guy:
            v "I'm getting tired of pointing it out, but I introduced you to Mark exactly for that reason."
        v "Take your nose out of your books! You have a lot to learn!"
        $ fholly = "n"
        h "I see..."
        v "Anyway..."
    ## continue menu
    hide ivy
    show ivy2
    hide holly3
    show holly2 at lef3
    with short
    $ fholly = "n"
    $ flena = "n"
    v "Will you look at this! Our glasses are empty... Shall I pour us another round?"
    menu:
        "{image=icon_friend.webp}Go home with Holly and watch a movie" if lena_holly > 8 or lena_go_holly > 1:
            $ renpy.block_rollback()
            $ v8_holly_date = True
            l "No more drinks for me..."
            hide ivy2
            show ivy
            with short
            l "I told Louise I would watch a movie with her later today, and Holly wants to come too."
            $ fivy = "sad"
            v "Ugh, really? A movie? Is there a more boring plan?"
            l "I'd tell you to come, but you and Louise are not on good terms..."
            label v8byeivy:
                play sound "sfx/sms.mp3"
            "Ivy's phone buzzed with a text."
            $ fivy = "n"
            v "Wait a second..."
            $ flena = "n"
            v "..."
            $ fivy = "smile"
            v "Okay, you can go. I have plans."
            l "That was convenient."
            hide ivy
            show ivy2
            with short
            v "I hate to break it to you, but there are always people who want to see me!"
            v "You should feel honored that I choose to spend my time with you, from all the options I have available!"
            if lena_ivy > 8:
                $ flena = "happy"
                l "Do I need to prostrate myself before you?"
                v "I won't stop you if you do."
                l "Ha ha, you're such an idiot sometimes."
            elif lena_ivy > 5:
                $ flena = "smile"
                l "Glad to see you don't depend on me!"
                v "Oh, come on, you know you're my favorite person."
                l "I know."
            else:
                $ flena = "worried"
                l "I don't need to be your charity work..."
                $ fivy = "n"
                v "Ugh, come on, that was a joke."
            stop music fadeout 2.0
            $ flena = "n"
            l "Anyway, we'll get going."
            $ fholly = "smile"
            h "Today was super fun... Thanks for having me, Ivy."
            v "No problem. And think about what I told you!"
            h "I will."
            v "See you at the gym!"
            $ renpy.end_replay()
            if v8_holly_date:
                jump v8sundayholly
            else:
                scene streetnight with long
                show lena with short
                "This had been a pretty intense and weird week... And this was a nice way to end it, all things considered."
                if v8_jeremy_sex:
                    $ flena = "flirt"
                    "It had been a very enjoyable Sunday all around... Especially the morning, when I got to finally got to enjoy Jeremy all by myself..."
                    "I still needed to tell Ivy about it!"
                    $ flena = "worried"
                    "I had no idea how I would look Louise in the face after that, but... She was none the wiser about it, so I would just play it off."
                    $ flena = "smile"
                    "Weird as some stuff might be, I finally felt like the pieces were starting to fall into place."
                else:
                    "It had been a very enjoyable Sunday all around... And I finally felt like the pieces were starting to fall into place."
                "I didn't need to face everything on my own anymore..."
                jump v8ending

        "Stay at Ivy's place and have another drink":
            $ renpy.block_rollback()
            $ v8_holly_ivy = True
            if holly_change == 0:
                $ holly_change += 1
            $ flena = "smile"
            l "Yeah, why not? Let's have another."

        "Go home":
            $ renpy.block_rollback()
            l "No more drinks for me... I think I'm gonna call it a day."
            $ fivy = "sad"
            hide ivy2
            show ivy
            with short
            v "What, already?"
            l "It's been a long day... And a long week."
            h "Yeah, I'm tired too..."
            v "What are you, eighty years old? Where's your youthful energy?"
            $ flena = "happy"
            l "Wasted after a whole day shopping!"
            jump v8byeivy

    if v8_drinks == "gin":
        v "Another round of gin tonics on the way!"
        play sound "sfx/icecube.mp3"
        "We helped Ivy prepare the drinks and went back to the couch."
    if v8_drinks == "wine":
        v "It won't take much to finish off this bottle of wine between the three of us!"
        "Ivy poured us another glass. A rather large one."
    if v8_drinks == "water":
        v "Alright, but you're not drinking water!"
        $ flena = "happy"
        l "Alright, alright... But bring me a glass just in case!"
        "Ivy poured us another glass of wine. A rather large one."
    hide ivy2
    show ivy
    with short
    v "So, since we were already talking about sex..."
    $ fivy = "flirt"
    v "I'm curious, Holly. Do you have any sexual fantasies?"
    $ flena = "n"
    $ fholly = "worried"
    h "W--{w=0.3}what? I...!" with vpunch
    $ fholly = "blush"
    h "What a question to ask out of the blue...!"
    v "It's no biggie! You'll see."
    v "Lena, tell us about one of your sexual fantasies!"
    if v7_game:
        v "You already told us one of your fantasies last time at my place..."
        if lena_lust > 5:
            $ flena = "flirt"
            l "Sure. I have no problem talking about it."
        else:
            $ flena = "shy"
            l "Well, yeah..."
            v "Come on, tell us again! I'm sure you have more than one!"
    else:
        l "Who, me?"
        v "Yeah, you! Come on, lead by example!"
        if lena_lust > 5:
            $ flena = "flirt"
            l "Sure, I have no problem talking about it."
        else:
            $ flena = "shy"
            l "If I really have to... My sexual fantasy would be..."
    if lena_fty_show:
        l "I think I like the feeling of being watched. I might have an exhibitionist streak..."
        v "You don't say? That's hardly surprising..."
    if lena_fty_slave:
        l "I like it when my partner is dominant in bed. I really enjoy playing submissive, having him take control and use me..."
        v "Who doesn't like that? Right, Holly?"
        h "Uh..."
    if lena_fty_lesbo:
        l "I'm turned on both by men and women, but I've had a lot more experience with the former, so..."
        l "The idea of sleeping with a girl is always appealing to me."
        v "I would know."
    if lena_fty_3some == 1:
        l "I must admit I've fantasized about having a threesome with two guys. That must be crazy."
        v "It is, indeed!"
    if lena_fty_3some == 2:
        l "I'm curious about what a threesome with another guy and a girl would feel like."
        v "Best of both worlds, am I right?"
    if lena_fty_bbc:
        l "This one is a fantasy I've been having recently... But I'm kinda turned on by big black cocks."
        v "So the color is important?"
        l "I mean, not really. It's about the size, but you know..."
        v "I do know, ha ha ha!"
        if v8_jeremy_sex:
            v "Good thing there's someone fitting the criteria, right?"
            l "I would say {i}barely fitting{/i}, ha ha..."
            v "That sounds like you know something I don't... yet!"
            l "{i}Yet{/i}."
    if lena_fty_show or lena_fty_slave or lena_fty_lesbo or lena_fty_3some > 0 or lena_fty_bbc:
        v "Do you have any more fantasies?"
    menu:
        "A threesome" if lena_fty_3some == 0:
            $ renpy.block_rollback()
            $ flena = "shy"
            l "A threesome."
            v "Not too original..."
            v "But wait, with two guys or two girls?"
            menu:
                "With two guys":
                    $ renpy.block_rollback()
                    $ lena_fty_3some = 1
                    $ flena = "flirtshy"
                    l "Two guys, of course."
                    v "That is something a bit kinkier, ha ha!"

                "With two girls":
                    $ renpy.block_rollback()
                    $ lena_fty_3some = 2
                    $ lena_fty_lesbo = True
                    $ flena = "smile"
                    l "Preferably with another girl, yeah."
                    v "Those are the easy ones to get. No guy will say no!"

        "Having sex with a girl" if lena_fty_lesbo == False:
            $ renpy.block_rollback()
            $ lena_fty_lesbo = True
            $ flena = "smile"
            l "Yeah. Having sex with a girl."
            v "Really? Just that?"
            v "You've already done it... With me!"
            $ fholly = "surprise"
            h "Really!?"
            $ flena = "shy"
            l "It was a long time ago! We were teenagers, and we were experimenting..."
            v "You've been with a girl before too, Holly. You shouldn't be so surprised."
            $ fholly = "blush"
            h "I guess..."

        "Exhibitionism" if lena_fty_show == False:
            $ renpy.block_rollback()
            $ lena_fty_show = True
            $ flena = "shy"
            l "Well, lately, I've been having this fantasy about being watched..."
            if stan_simp == 4:
                "Was that the reason I had let Stan watch that night...?"
            v "Exhibitionism? But that's basically what you do with your modeling job, ha ha! How boring!"
            l "Not like that! Being seen in more... intimate situations."
            $ fivy = "flirt"
            v "Like having sex in public?"
            $ flena = "blush"
            l "No, that would be..."
            if v7_mike_bj:
                "No different at all from what happened at the club with Mike that night."
            $ flena = "shy"
            l "It's just a fantasy."
            h "I think I'd be so ashamed I could die if someone watched me while doing... stuff."
            v "Don't be so dramatic. It's not that big of a deal. In fact, it's pretty thrilling..."

        "Big cocks" if (v3_bbc and lena_fty_bbc == False) or (lena_bdick > 0 and lena_fty_bbc == False):
            $ renpy.block_rollback()
            $ lena_fty_bbc = True
            $ flena = "slutshy"
            l "I can't believe I'm about to say this..."
            $ flena = "shy"
            l "Big cocks."
            $ fholly = "surprise"
            if v7_bbc == "lena":
                v "I should've known you're a size queen, ha ha."
                l "I just find them appealing... There's something about a big, thick, veiny cock..."
                $ fholly = "blush"
                v "Add \"black\" to those adjectives. I can guess who you're talking about."
                l "Shut up."
            else:
                v "Oh Lordy! So turns out you're a size queen, huh?"
                l "I just find them appealing... There's something about a big, thick, veiny cock..."
                $ fholly = "blush"
                v "Add \"black\" to those adjectives and I know someone you might like, in that case... In fact, you already know him."
                l "I'm aware."

        "Being dominated" if (lena_axel_desire and lena_fty_slave == False) or (seymour_disposition > 1 and lena_fty_slave == False):
            $ renpy.block_rollback()
            $ lena_fty_slave = True
            $ flena = "blush"
            l "It's, uh..."
            $ flena = "shy"
            l "I like to fantasize about being dominated in bed."
            $ fholly = "surprise"
            $ fivy = "flirt"
            v "We all like that, don't we...?"
            $ fholly = "worried"
            h "Dominated... how?"
            l "I don't know. Wearing a collar, being ordered around... That sort of thing."
            v "You like playing the sex slave, huh? Ha ha ha!"
            $ fholly = "blush"

        "I can't think about anything":
            $ renpy.block_rollback()
            $ flena = "n"
            l "I can't think about anything right now..."
            $ fivy = "n"
            v "Really? I don't believe you..."
            l "I can make something up if you want, but it won't be true!"
            v "How boring..."

    $ flena = "happy"
    l "What about you, Ivy? Now's your turn to tell us!"
    $ fivy = "smile"
    v "Oh, so many!"
    v "Having sex in public, or in a high-rise window, being watched while fucking, all kinds of roleplaying, threesomes, orgies..."
    v "You name it!"
    l "Damn. I should've asked if there's a fantasy you don't have..."
    v "We'd end sooner, probably!"
    hide ivy
    show ivy2
    with short
    $ flena = "smile"
    v "Now's your turn, Holly! Tell us!"
    h "Um... Oh, gosh..."
    h "I've sometimes fantasized about... having sex with a hot stranger..."
    $ flena = "shy"
    $ fivy = "flirt"
    v "Oh! Tell us more!"
    h "I don't know, it's weird...!"
    l "Come on, don't be shy!"
    h "Well, uh... It's just a fantasy, but..."
    h "In it, a man whose face is shrouded comes at me from behind, and grabs me..."
    h "He's so strong, and there's no way I can resist him, but I don't need to, because he's also gentle."
    h "And he'd make me lay face down on the bed and... make love to me from behind..."
    v "Look at you! You do have a naughty streak, after all, Holly!"
    $ flena = "smile"
    l "It's not a weird fantasy to have, I think it's pretty common, in fact. Nothing wrong with it at all."
    $ fholly = "shy"
    h "I hadn't told this to anyone before... It feels weird, ha ha."
    $ fivy = "smile"
    hide ivy2
    show ivy
    with short
    v "That's what friends do: share stuff they wouldn't share with other people."
    $ fholly = "happyshy"
    h "Yeah..."
    scene ivyroomnight
    show lena at rig3
    show ivy
    show holly at lef3
    with short
    "The afternoon went by as we chatted and joked. We finished our second drink and Ivy poured a third."
    "I could tell Holly was feeling more comfortable by the minute. The drinks were probably helping with that."
    "In any case, it was good seeing Holly let loose and have a good time. I never imagined she and Ivy would mix this well..."
## HOLLY IVY PRE SEX
    h "I'm having a blast... You girls are so cool."
    h "The coolest I've ever met!"
    $ fholly = "sad"
    h "I've never been around the cool girls on campus. Nor during high school."
    $ fivy = "n"
    h "That's why I can't believe I'm here right now, hanging out with you..."
    $ fholly = "happyshy"
    h "The coolest girls in the world!"
    $ flena = "happy"
    $ fivy = "flirt"
    v "I like the things this girl says sometimes."
    l "You're also cool, Holly, in your own way."
    $ fholly = "blush"
    hide holly
    show holly3 at lef3
    with short
    h "I know you mean well, but that's a lie."
    $ flena = "sad"
    h "I'm not even remotely cool. Not like you, girls..."
    h "Not even close."
    $ fivy = "n"
    v "Don't be so hard on yourself. It's true that you're on the plain side of the spectrum..."
    $ flena = "worried"
    v "... but you're trying to change that! And you've already taken some steps."
    if ian_holly_dating:
        $ flena = "smile"
        l "And a pretty big one at that! I doubt Ian thinks you're \"not cool\"."
        $ fholly = "shy"
        h "That's true..."
        $ fholly = "flirtshy"
        h "I still can't believe it happened... Oh, Ian..."
        $ fivy = "flirt"
        hide ivy
        show ivy2
        with short
        v "Oh Lord, I think she's getting all horny!"
        $ fholly = "blush"
        h "Uh, no, I..."
        v "It's cool, it's cool. It's good you finally found a guy who's interested in you."
        if ian_ivy > 4:
            v "And with Ian, no less. He seems like a somewhat cool guy himself, so you got lucky!"
        elif ian_ivy > 3:
            v "He seems okay, so you can consider yourself lucky."
        else:
            v "Maybe he's nothing to really boast about, but still... It'll serve you wonders."
        v "Be sure to pick up with him all that experience you've been lacking till now!"
        $ fholly = "shy"
        h "I'm hoping for that..."
        hide ivy2
        show ivy
        with short
        jump v8ivyendcall
    else:
        $ flena = "n"
    v "And you can take a few more, right now."
    scene v6_holly1_ivya with long
    $ holly_glasses = False
    v "First, let's lose these ugly glasses."
    scene v6_holly1_ivyb with long
    v "I think I've told you before. You should listen."
    h "Alright..."
    scene ivyroomnight
    show lena at rig3
    show ivy
    show holly2 at lef3
    with long
    v "In fact, I think you should take off something more than just your glasses."
    h "What else? The sweater?"
    v "Yeah, with the rest of your clothes. Right now."
    $ flena = "worried"
    $ fholly = "worried"
    h "Right now...? Why?"
    v "You want to be cool like us, right? Both Lena and I are completely cool with being naked. We make money off of it, in fact!"
    l "I don't think Holly is interested in becoming a model..."
    v "That's not the point. She needs some basic confidence in herself."
    scene v8_ivy1 with long
    v "Like this, see?"
    h "Uh..."
    v "What's the matter? Never seen a pair of boobs?"
    l "Ivy, you're intimidating her..."
    v "Me, intimidating her? Why?"
    scene v8_ivy1b with long
    v "Look at them. They don't bite."
    h "Um... I see..."
    v "Does looking at my boobs make you nervous?"
    h "Yeah..."
    v "That's not good. What will you do when you see a guy's dick?"
    v "A frightened look is not what they're hoping to see when they put it in front of your face..."
    v "Well, most of them at least!"
    scene ivyroomnight
    show lena at rig3
    show ivybra2
    show ivy_pants
    show holly2 at lef3
    with long
    v "You can't act like a scaredy-cat in bed. You need to get used to these things."
    v "And you need to get used to feeling naked and exposed. Otherwise, how will you be able to enjoy yourself in that situation?"
    l "Some people have sex with the lights off."
    hide ivybra2
    hide ivy_pants
    show ivybra
    show ivy_pants
    with short
    v "Some people make a life-long vote of chastity, too. And I don't think Holly wants to be that kind of person."
    v "You're with me on this, Lena: when you enjoy being naked in front of the person you desire you're eager to show him everything."
    $ flena = "shy"
    hide ivybra
    hide ivy_pants
    show ivybra2
    show ivy_pants
    with short
    v "Every part of your body, every nook, and cranny... Everything he asks for."
    $ flena = "flirtshy"
    $ fholly = "flirtshy"
    v "You show it to him and see the effect you get. How his eyes widen, how his cock gets super hard..."
    v "And the way he fucks you, you get him like that... Well, you know how that goes."
    v "Come on, help me with this. Strip down with us."
    menu:
        "Strip to underwear":
            $ renpy.block_rollback()
            $ v8_holly_strip = True
            if holly_change < 3:
                $ holly_change += 1
            $ flena = "smile"
            l "If you think this will help Holly with her confidence..."
            hide lena
            show lenabra at rig3
            with short
            $ fholly = "blush"
            l "See, Holly? It's really not a big deal."
            l "It's just us. There's nothing you should be ashamed of."
            v "Exactly!"
            hide ivybra2
            hide ivy_pants
            show ivynude2
            with short

        "Refuse":
            $ renpy.block_rollback()
            if holly_change > 0:
                $ holly_change -= 1
            $ flena = "n"
            l "No, thanks, I'm not doing it."
            $ fivy = "sad"
            $ fholly = "blush"
            v "What? Why? You work as a nude model, do I really need to remind you of that?"
            l "Hey, I'm just not feeling like it and that's final."
            $ fivy = "serious"
            v "You're not helping here, you know that?"
            l "Don't involve me in this!"
            v "Such a friend!"
            if lena_ivy > 3:
                call friend_xp('ivy', -1) from _call_friend_xp_591
            $ fivy = "smile"
            v "Okay, I'll just do it myself."
            hide ivybra2
            hide ivy_pants
            show ivynude2
            with short
            v "See? Nothing bad happened!"
    v "Now's your turn."
    h "I don't know if... if I can."
    menu:
        "Let Ivy play with Holly":
            $ renpy.block_rollback()
            if v8_holly_strip:
                v "Jeez, relax, Holly! As Lena said, it's just us! Don't be embarrassed!"
            else:
                v "Jeez, relax, Holly! Even if Lena is acting like a prude today you don't have to!"
            v "You said it yourself: you're tired of being so insecure, aren't you?"
            if holly_change == 0:
                h "I'm sorry, I can't do this...!"
                $ flena = "sad"
                "Holly looked really overwhelmed by the situation. I felt bad that I let Ivy take things this far."
                l "Okay, Ivy, that's enough. Leave her alone, she's uncomfortable."
                $ fivy = "n"
                v "I'm just trying to help her!"
                hide ivynude2
                show ivynude
                with short
                l "Help her, not force her. Let her do things at her own pace."
                h "I'm sorry..."
                jump v8hollynotprepared
            jump v8ivyhollynaked

        "{image=icon_friend.webp}Protect Holly" if lena_holly > 6:
            $ renpy.block_rollback()
            $ flena = "n"
            l "Ivy, leave her alone. You don't have to do it, Holly."
            hide ivynude2
            show ivynude
            with short
            $ fivy = "sad"
            if v8_holly_strip:
                v "Then what did you strip down for?"
                l "To help you get Holly out of her comfort zone."
                v "So? That's exactly what you're telling her not to do now!"
            if holly_change == 3 or (holly_change == 2 and v8_drinks == "gin"):
                $ fholly = "serious"
                h "No... It's okay, I wanna do it."
                $ flena = "worried"
                $ fholly = "mad"
                $ fivy = "flirt"
                h "I'm tired of being so insecure and... so plain!"
                $ flena = "surprise"
                "I wasn't expecting Holly to say something like that! She looked... determined."
                $ flena = "worried"
                hide ivynude
                show ivynude2
                with short
                v "Well said, Holly. You're starting to make me proud, ha ha."
                jump v8ivyhollynaked
            l "I want to help her, not force her. Let her do things at her own pace."
            label v8hollynotprepared:
                v "What's wrong with you today? You're no fun..."
            label v8ivyendcall:
                play sound "sfx/sms.mp3"
            "Ivy's phone buzzed with a text."
            $ fivy = "n"
            v "Wait a second..."
            $ fholly = "n"
            $ flena = "n"
            v "..."
            $ fivy = "n"
            v "Okay, girls, it's getting late. I'm meeting somebody, so you'll need to get going."
            l "Oh, so you had plans and you didn't tell us? Who's the lucky guy?"
            v "Just some guy from the club."
            $ flena = "happy"
            l "So, some random guy is more important than us?"
            v "You know it's not like that. Now stop breaking my leg and get moving!"
            l "Alright, alright! It's getting late anyway."
            stop music fadeout 2.0
            $ fholly = "blush"
            h "Oh, no...!"
            $ flena = "sad"
            l "What's wrong?"
            h "I think I'm more than slightly tipsy..."
            $ fivy = "smile"
            v "Yeah, you are! Do you realize just now?"
            h "I can't go home like this... My parents will get mad."
            v "Holly, you're supposed to be twenty-three. What are your parents gonna do to you, ground you?"
            h "I... Still I'd prefer if they didn't see me like this..."
            l "It's okay. It's not too late, you can come to my place until you've sobered up a bit."
            h "Thanks, Lena..."
            jump v8sundayholly

    label v8ivyhollynaked:
        v "And remember what I said about stepping out of your comfort zone? This is it."
    $ fholly = "blush"
    stop music fadeout 2.0
    h "..."
    h "Alright, I'll do it."
    v "Let me help you..."
    play music "music/shooting.mp3" loop
    scene v8_ivy2 with long
    v "Come here."
    "Ivy began stripping Holly without hesitation."
    "Holly still looked a bit frightened, but I could see she was determined to face her insecurity."
    "Maybe Ivy's shenanigans would be of some help to her after all..."
    v "You're doing good, Holly. Now take your pants off..."
    scene ivyroomnight
    show hollynude3 at lef3
    show holly_panties at lef3
    show ivynude2
    if v8_holly_strip:
        $ flena = "smile"
        show lenabra at rig3
    else:
        $ flena = "n"
        show lena at rig3
    with long
    v "There."
    l "How are you feeling, Holly?"
    h "I'm... It's okay."
    hide ivynude2
    show ivynude
    with short
    v "See? You're making a big deal about stupid things."
    $ fholly = "shy"
    h "Yeah... I guess so..."
    v "Put your arm down. Let us see you."
    $ fholly = "blush"
    hide hollynude3
    hide holly_panties
    show hollynude2 at lef3
    show holly_panties at lef3
    with short
    h "Like this...?"
    v "Good! See, you're learning to let loose."
    h "It's hard... I feel so plain compared to you girls..."
    h "You're both so beautiful, I mean, you look like Goddesses!"
    h "And I'm... I'm nothing like you girls."
    v "Listen!"
    v "I have a word for you: \"attitude!\""
    v "You might not be the hottest girl around, but you will get far with the right attitude!"
    l "That's true... You need to start believing in yourself, Holly. You are an attractive girl!"
    v "You can {i}become{/i} an attractive girl. You know the saying, \"fake it till you make it\"!"
    l "You'll become more confident the more you get out of your comfort zone, as Ivy said. And the more you do it, the easier it will become."
    $ fholly = "shy"
    h "Yeah... I feel better already..."
    h "Thanks, girls."
    v "It's just like pole dancing. Practice makes perfect..."
    hide ivynude
    show ivynude2
    with short
    $ fivy = "flirt"
    v "You know, I'm thinking..."
    v "Why wait for a guy to get you some experience? You have us..."
    v "We could help you out. You like girls, don't you?"
    h "I do..."
    if lena_lust > 6:
        $ flena = "shy"
        l "Wait, you mean help her like... sleep with her?"
        "Ivy showed me a malicious smile."
        v "We could teach her stuff, you know."
    else:
        $ flena = "worried"
        l "Wait, what are you talking about? Help her?"
        "Ivy showed me a malicious smile."
        v "You know what I mean. We could teach her stuff."
        $ flena = "blush"
    $ fholly = "worried"
    h "Teach me stuff? Like how...?"
    v "The only way these things are taught! Practical application!"
    menu:
        "{image=icon_lust.webp}I'm willing to help" if (lena_lust > 5 and lena_go_holly > 0 and v8_holly_strip and ian_lena_dating == False) or (lena_lust > 5 and lena_go_holly > 0 and v8_holly_strip and ian_lena_over):
            $ renpy.block_rollback()
            $ v8_holly_sex = "lenaivy"
            $ flena = "flirt"
            l "I guess we can lend her a hand if she wants..."
            v "See? Lena's willing to help, too."
            h "I don't know what to say... I..."
            v "I think we've talked enough. Some things have to be learned through experience."
            v "And it's time for you to learn how to relax and have fun..."

        "I won't take part":
            $ renpy.block_rollback()
            $ v8_holly_sex = "ivy"
            $ flena = "smile"
            $ fivy = "n"
            l "Pump the brakes, roadrunner. I don't know what exactly you have in mind, but I'm not taking part!"
            hide ivynude2
            show ivynude
            with short
            v "What? Why not?"
            if lena_fty_lesbo:
                v "Didn't you say you wanted to have sex with a girl?"
                l "Yeah, but not {i}any{/i} girl! And Holly's my friend..."
                if lena_fty_3some == 2:
                    l "And it'd be better if a guy was involved, too."
                    v "What, threesomes are only valid when there's a dick involved?"
                    l "Oh, shut up!"
            else:
                l "Because? Sometimes you have the weirdest ideas!"
            v "Well, it's a bit weird if you just stay and watch, don't you think?"
            $ flena = "worried"
            l "Wait, are you really gonna go through with this?"
            $ fivy = "smile"
            v "Yeah! Holly is serious about improving herself, ain't that right?"
            h "Yes... Yes, I am."
            l "I guess I'll give you two some intimacy then..."
            h "Wait, uh, I..."
            $ flena = "sad"
            "Holly looked at me, blushing and with a silent plea in her eyes."
            $ fivy = "flirt"
            hide ivynude
            show ivynude2
            with short
            v "I think she'll feel more comfortable if you stay."
            $ flena = "blush"
            l "That's..."
            v "Come on, don't be a buzzkill. We're just friends doing some exploring together... It's not like we've never done stuff like this before!"
            l "Okay..."
            v "Alright, Holly, come here."
            v "It's time for you to learn how to relax and have fun..."
## HOLLY IVY SEX SCENE ############################################################################################################################################################################
    stop music fadeout 2.0
    play music "music/sex_deep2.mp3" loop
    play sound "sfx/mh1.mp3"
    scene v6_holly2_ivy
    show v6_holly2_v8
    with long
    "Ivy held Holly's face and planted a deep kiss on her mouth."
    # lena + ivy
    if v8_holly_sex == "lenaivy":
        "I wasn't expecting the day to end like this when we decided to go shopping together... Ivy had a way to turn every situation into one of her games!"
        "But I felt like playing, too, and as she said, we could provide Holly with a kind of help she wouldn't find anywhere else..."
        "A rather naughty kind of help!"
        $ fholly = "flirtshy"
        scene ivyroomnight
        show ivynude
        show hollynude3 at lef3
        show holly_panties at lef3
        show lenabra2 at rig3
        with long
        v "So, how was that? Not bad, huh?"
        h "No... Not at all."
        v "Now you do it, Lena."
        l "Alright."
        scene v8_ivy3b
        if lena_tattoo2:
            show v8_holly1_t2
        with long
        pause 1
        "This time it was me who planted a deep kiss on Holly's mouth."
        "Her lips responded to mine, reciprocating my kisses, as our tongues played together."
        if v6_holly_kiss == "lena":
            "This was noticeably different from the first time I had kissed her... I felt a lot more want in the way Holly was kissing me."
        else:
            "I was surprised by the want I felt in the way Holly was kissing me."
        "The way her tongue was searching mine, twirling against it in wet caresses, wasn't timid at all..."
        "She really wanted to go through with this! Ivy's pep talk really worked..."
        $ fholly = "flirt"
        scene ivyroomnight
        show ivynude2
        show hollynude2 at lef3
        show holly_panties at lef3
        show lenabra2 at rig3
        with long
        v "Wow, girls! How would you rate that kiss, Lena?"
        l "It was pretty hot... You're learning fast, Holly."
        h "Kissing you felt... just so nice..."
        v "Now I'm getting jealous... Let me try again."
        scene v8_ivy3 with long
        pause 1
        "This time Ivy really went for it."
        "She almost devoured Holly with her intense kisses, digging deep into her mouth with her lustful tongue."
    # ivy
    if v8_holly_sex == "ivy":
        $ flena = "blush"
        "How did I end up in such a surreal situation, having to look at my friends making out in front of me?"
        "And not in a tame way, precisely."
        "They were both naked, their bodies in full contact, and Ivy's kisses were quickly becoming intense and passionate, wild, even."
        scene v8_ivy3 with long
        "It was almost like she was trying to devour Holly, digging deep into her mouth with her lustful tongue."
    # common
    "Holly looked a bit tense, but I could see how she was forcing herself to resist her fear, responding to Ivy's kisses."
    "However, it got a bit challenging when Ivy's hand fondled her breast."
    h "Ahn...!"
    "And when Ivy pinched her erect nipple and rubbed it slightly, Holly's entire body twitched and she took a step backward."
    play sound "sfx/moan2.mp3"
    h "Ahhh...!"
    $ fholly = "blush"
    scene ivyroomnight
    show ivynude
    show hollynude2 at lef3
    show holly_panties at lef3
    if v8_holly_strip:
        show lenabra2 at rig3
    else:
        show lena2 at rig3
    with long
    v "What's the matter? I barely touched you..."
    h "It's... Sorry, I felt like my entire body had been jolted."
    play sound "sfx/giggle.mp3"
    "Ivy giggled, amused."
    v "Don't tell me your nipples are that sensitive?"
    hide hollynude2
    hide holly_panties
    show hollynude3 at lef3
    show holly_panties at lef3
    with short
    h "I guess they are..."
    v "Take off your panties."
    h "Huh?"
    v "Give them to me. I want to see if you're moist down there."
    h "..."
    hide holly_panties with short
    "Holly did as instructed and handed her underwear to Ivy."
    hide ivynude
    show ivynude2
    with short
    v "Oh my God, you're not moist, you're drenched! Are you liking this so much?"
    $ flena = "blush"
    $ fholly = "worried"
    h "No, I...!"
    v "That's okay, It's exactly what I was hoping for... You're doing good, Holly."
    v "Now come here, I'll make you feel even better..."
    scene v8_ivy4 with long
    pause 1
    "Ivy moved the action to her bed, where she made Holly lay down."
    if v8_holly_sex == "ivy":
        "I was wondering if I should stop her... But Holly seemed decided to go along with her. What a crazy situation..."
    else:
        "I let her take the lead and take things one step further. What a crazy situation..."
    "Ivy began caressing Holly's pussy. Her fingers quickly became shiny with moisture."
    "So she was not exaggerating... Holly was really aroused."
    play sound "sfx/moan1.mp3"
    h "Aahn...!"
    "Holly moaned and twitched when Ivy inserted a finger."
    v "{i}Shhh{/i}, relax... Don't tense up."
    v "Yes, just like that... Feel my finger moving inside of you... Nope, you're tensing up again!"
    v "Get involved in this. Your nipples were really sensitive, right?"
    v "Play with them."
    play sound "sfx/ah3.mp3"
    show v8_ivy4_hand with long
    "Holly did as Ivy had told her, rubbing her nipple timidly, while Ivy slid another finger inside."
    if v8_holly_sex == "ivy":
        "I didn't know where to look. This scene was getting me kind of aroused..!"
    else:
        "I was getting so aroused watching this scene. It was so hot..."
    v "Good... You like it, don't you?"
    h "Y-{w=0.3}yeah..."
    if ian_holly_sex:
        v "The reason you were so tight with Ian was because that you were nervous. Learn to let loose and you'll be able to take a dick with no issue."
    else:
        v "The reason you're so tight is because that you're nervous. You need to let loose or you'll have trouble taking a dick."
    v "Seems like you're still having trouble with it... I think you need some extra help."
    h "What are you...?"
    scene v8_ivy5 with long
    pause 1
    play sound "sfx/moan2.mp3"
    h "Ahh!" with vpunch
    v "Calm down, you'll like this."
    "Ivy held Holly's legs up as she put her naughty tongue to use."
    "It slithered across Holly's slit with dexterity, spreading her lips and stroking her clit playfully."
    # lena + ivy
    if v8_holly_sex == "lenaivy":
        v "Hey, Lena!"
        v "Don't just stand over there looking at us! Help me make Holly feel comfortable, will you?"
        l "Alright..."
        play sound "sfx/mh1.mp3"
        scene v8_ivy6 with long
        pause 1
        "I leaned over Holly and kissed her while Ivy ate her out."
        "She welcomed my kiss and we started making out profusely. Holly panted in my mouth, her moans muffled by my lips."
        "Trapped between two tongues, mine and Ivy's, Holly's entire body began to shake, jolted by pleasure."
        "Now she was using both hands to play with her erect nipples, causing her spasms to be even more intense."
        h "Mhhnn... Ahnn..."
        "Her moans also became louder as she kissed me almost like she wanted to eat me up."
        "But the one who was being devoured was her, by Ivy and me."
        scene v8_ivy5 with long
        v "It seems she's finally gotten into it..."
        scene v8_ivy5b with long
        play sound "sfx/oh1.mp3"
        h "Mhhhh!!" with vpunch
        pause 1
        "Holly shivered when Ivy decided to shift gears and dove into her pussy, stabbing her with her tongue."
        "She seemed determined to give Holly the most complete and intense experience, and judging by Holly's reaction, she was succeeding."
        "Her whole body started convulsing violently like she had been struck by an electric current."
        play sound "sfx/moanlong.mp3"
        h "Ah... Ahh...! Ahhh...!{w=1.0}{nw}"
        scene v8_ivy5c with flash
        h "Ahhhh!!!{w=0.5}{nw}" with vpunch
        h "Oh God...!!{w=0.5}{nw}" with vpunch
        with vpunch
        pause 0.5
        h "Ohhh..."
        "Holly's body loosened after being struck by a jolt of mind-melting pleasure."
        "Ivy's tongue must've felt so damn good..."
        scene v8_ivy5 with long
        "Ivy looked satisfied at her handiwork before letting Holly rest."
        "She panted laying on the bed, recovering from a very powerful orgasm."
        $ flena = "flirt"
        $ fholly = "flirt"
        scene ivyroomnight
        show hollynude2 at lef3
        show ivynude2
        show lenanude at rig3
        with long
        v "So, how was that? I'm sure you're feeling way more relaxed now, ha ha!"
        h "It was... It was..."
        h "I can't find words to describe it right now... I'm still out of it..."
        v "You lucky girl! Seems like your orgasms are pretty potent, huh?"
        v "And it was so easy to make you cum..."
        v "But now it's time to return the favor, don't you think?"
        v "Lena, come over here..."
        scene v8_ivy8
        if lena_tattoo1:
            show v8_ivy8_t1
        if lena_tattoo2:
            show v8_ivy8_t2
        if lena_tattoo3:
            show v8_ivy8_t3
        if lena_piercing1:
            show v8_ivy8_p1
        elif lena_piercing2:
            show v8_ivy8_p2
        with long
        pause 1
        "Ivy took my hand and made me sit next to her. And then she guided Holly's head between my legs..."
        v "You need to learn how to take it, and also how to dish it out!"
        "Holly's wet lips enveloped my pussy, getting even wetter with my moisture. This whole ordeal had me burning...!"
        play sound "sfx/mh1.mp3"
        l "Mhhh..."
        "I wasn't sure if making Holly do this was right... But she didn't seem to be bothered by it at all. Quite the opposite."
        "Her awkward vibe had completely vanished, leaving no trace of her previous hesitation."
        "She just devoted herself to eating me out, eyes closed, letting Ivy guide her head."
        "There was no shilly-shallying in her tongue, which lapped up my pussy from top to bottom, outside and inside, squeezing out my juices."
        l "Ohhh... Holly..."
        v "She's putting her heart into it, huh?"
        "She was. Her lack of skill was noticeable, though. It was enjoyable, and I was really horny, but I probably wouldn't be able to cum like that..."
        "Ivy didn't give me the chance, anyway."
        scene v8_ivy7 with long
        pause 1
        v "That's enough practice with one pussy... Now do mine."
        h "Mhpff..."
        "Holly didn't argue. Still guided by Ivy's hand, she glued her lips to her wanting pussy and gave her the same treatment as me."
    # ivy
    else:
        "I was still sitting on the couch, watching the perplexing scene that was unfolding before me."
        "Victim to Ivy's expert caresses Holly's entire body began to shake, jolted by pleasure."
        "She continued to play with her erect nipples, just as Ivy had instructed her, while she gave her oral pleasure."
        h "Mhhnn... Ahnn..."
        "Her moans became louder and her spasms even more intense."
        scene v8_ivy5b with long
        play sound "sfx/oh1.mp3"
        h "Mhhhh!!" with vpunch
        pause 1
        "And both reached an even higher level when Ivy decided to shift gears."
        "She dove into her pussy, sucking with her lips, stabbing her with her tongue..."
        "Ivy was not pulling any punches. She seemed determined to give Holly the most complete and intense experience..."
        "And judging by Holly's reaction, she was succeeding."
        play sound "sfx/moanlong.mp3"
        h "Ah... Ahh...! Ahhh...!{w=1.0}{nw}"
        scene v8_ivy5c with flash
        h "Ahhhh!!!{w=0.5}{nw}" with vpunch
        h "Oh God...!!{w=0.5}{nw}" with vpunch
        with vpunch
        pause 0.5
        h "Ohhh..."
        "Holly's body loosened after being struck by a jolt of mind-melting pleasure."
        "Ivy's tongue must've felt so damn good..."
        scene v8_ivy5 with long
        "Ivy looked satisfied at her handiwork before letting Holly rest."
        "She panted laying on the bed, recovering from a very powerful orgasm."
        $ fholly = "flirt"
        scene ivyroomnight
        show hollynude2 at lef3
        show ivynude2
        if v8_holly_strip:
            show lenabra2 at rig3
        else:
            show lena2 at rig3
        with long
        v "So, how was that? I'm sure you're feeling way more relaxed now, ha ha!"
        h "It was... It was..."
        h "I can't find words to describe it right now... I'm still out of it..."
        v "You lucky girl! Seems like your orgasms are pretty potent, huh?"
        v "And it was so easy to make you cum..."
        v "But now it's time to return the favor, don't you think?"
        scene v8_ivy7 with long
        pause 1
        "Ivy sat down on the bed, legs spread apart, and held Holly's head, guiding it toward her crotch..."
        play sound "sfx/mh1.mp3"
        v "Mhhh..."
        v "You need to learn how to take it, and also how to dish it out, too..."
        "Holly's wet lips enveloped Ivy's pussy, getting even wetter with her moisture. This whole ordeal was nuts..."
        "I felt so out of place being there, and at the same moment, I was too enthralled by what was going on..."
        "It was so... hot..."
        "I wasn't sure if making Holly do this was right... But she didn't seem to be bothered by it at all. Quite the opposite."
        "Her awkward vibe had completely vanished, leaving no trace of her previous hesitation."
        "She just devoted herself to eating Ivy out, eyes closed, letting Ivy guide her head."
    # both end
    v "That's it, good girl... Lick me good, taste me..."
    v "Do you like it?"
    h "Mhhf... Yes..."
    v "Put more passion into it... I want to feel how much you like eating me out."
    "Holly dug her face deeper, eating Ivy's pussy profusely, giving her tongue a good workout."
    v "Concentrate on my clit... No, not there. Up. Up."
    v "There. Don't be afraid, lick harder..."
    v "Mhhh, yeah..."
    if v8_holly_sex == "lenaivy":
        "Ivy played with her breasts while she enjoyed Holly's mouth. And I watched, my hand dangerously close to my own sex, tempted to pleasure myself..."
        "I don't know why I didn't do it. Maybe I still felt a bit weird in this unexpected situation. But I was so turned on..."
    else:
        "Ivy played with her breasts while she enjoyed Holly's mouth. And I watched, my hand dangerously close to my own crotch..."
        "I couldn't admit to myself how turned on this situation had gotten me... Damn Ivy...!"
    v "Okay, that's enough for today..."
    $ flena = "blush"
    $ fholly = "flirtshy"
    scene ivyroomnight
    show hollynude3 at lef3
    show ivynude
    if v8_holly_sex == "lenaivy":
        show lenanude2 at rig3
    elif v8_holly_strip:
        show lenabra2 at rig3
    else:
        show lena2 at rig3
    with long
    stop music fadeout 2.0
    "Ivy released her hold on Holly's head and stood up."
    h "You... You didn't come, right?"
    $ fivy = "smile"
    v "What? Of course not!"
    $ fholly = "blush"
    h "Oh..."
    v "Sadly it takes more than an inexperienced mouse to make me cum... But this was very fun nonetheless."
    if v8_holly_sex == "lenaivy":
        $ fivy = "flirt"
        v "Right, Lena?"
        $ flena = "shy"
        l "Um, yeah... Yes, it was."
    else:
        v "Too bad Lena didn't wanna join the fun, huh?"
        l "It's, uh... It's okay."
        "I felt so weird after having witnessed that scene that I didn't even know what to say."
        "I was ashamed and horny, which made me even more ashamed...!"
    v "You surprised me, Holly. I must confess I wrote you off straight away when I first met you, but turns out you can be pretty cool."
    h "You really think so?"
    v "Of course! I only hang out with cool people. And I only get in bed with cool people."
    $ fholly = "shy"
    v "Anyway, it's getting late. I hate to kick you out, but I have plans..."
    $ flena = "n"
    l "Oh, so you had plans and you didn't tell us? Who's the lucky guy?"
    v "Just some guy from the club."
    if v8_holly_sex == "ivy":
        "I tried to force myself to get back to normalcy. I would finish processing what had just happened later."
    $ flena = "happy"
    l "So, some random guy is more important than us?"
    v "You know it's not like that. Now stop breaking my leg and get moving!"
    $ flena = "n"
    l "Alright, alright! You're right, it's getting late anyway..."
    scene streetnight with long
    if v8_holly_strip:
        "We got dressed and said goodbye to Ivy."
    else:
        "I waited for Holly to get dressed and said goodbye to Ivy."
    show lena with short
    if v8_holly_sex == "ivy":
        $ flena = "blush"
        "Holly was still in a daze after her experience with Ivy. But she seemed happy..."
    else:
        $ flena = "shy"
        "Holly seemed to still be in a daze after what just happened... But she seemed happy."
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH08_S10")
    play sound "sfx/door_home.mp3"
    scene lenahomenight_dark with long
    show lena with short
    "I finally arrived home after a long, tiresome day."
    "This had been a pretty intense and weird week... And this was an even weirder way to finish it."
    if v8_holly_sex == "ivy":
        "I wondered if Ivy had taken things too far with Holly... She seemed sincere in wanting to help her, but was that what Holly really needed...?"
        $ flena = "n"
        l "I'm just overthinking things... These things can happen between friends."
        l "Especially if Ivy's involved, it would seem..."
    else:
        $ flena = "blush"
        "I wondered if we had taken things too far with Holly... Somehow it seemed like a good idea while Ivy was talking us into it, but now, reflecting on it..."
        $ flena = "n"
        l "I'm just overthinking things... We're simply friends who played together a bit..."
        l "This is nothing new... Not completely, at least."
    if v8_jeremy_sex:
        $ flena = "flirt"
        "And that wasn't even the weirdest thing that had happened today..."
        "I still had trouble believing what I had done this morning, when I got to finally enjoy Jeremy all by myself..."
        "I still needed to tell Ivy about it!"
        $ flena = "worried"
        "I had no idea how I would look Louise in the face after that, but... She was none the wiser about it, so I would just play it off."
        $ flena = "smile"
        "Weird as some stuff might be, I finally felt like the pieces were starting to fall into place."
    else:
        "It had been a very enjoyable Sunday all around... And I finally felt like the pieces were starting to fall into place."
    "I didn't need to face everything on my own anymore..."
    jump v8ending

## ENDING #################################################################################################################################################################################################################
label v8ending:
    scene black with long
    "But, as it turns out, life often likes to pull the rug from under you when you think you are finally figuring things out."
    "And this seemed to be true in my case, especially."
    "Like a sinking boat full of holes: no matter how much water you manage to pump out, more keeps rushing in through the leaks."
    "That's how things always were."
    $ lena_look = 2
    $ lena_makeup = 0
    $ flena = "n"

    call calendar(_day="Monday", _week=2) from _call_calendar_61

    play music "music/normal_day4.mp3" loop
    scene lenaroom with long
    show lenaunder with short
    l "Ahhh... I finally managed to get a good night's sleep!"
    if cafe_help:
        $ flena = "smile"
        l "I wonder how many customers we'll get today at the café..."
        if cafe_nude:
            l "We've been getting quite a few more since the life drawing event. We'll be hosting another one this week..."
        else:
            l "There were quite a lot of people last Friday at the concert..."
    else:
        l "I don't know for how much longer I'll keep my job at the café. I'm gonna miss it... and the money, most of all."
    $ flena = "smile"
    l "This week I'll have all the afternoons for myself... I'm not sure what to do with all this free time!"
    l "I could drop by the record store and see how Emma's doing. I need to properly thank her for her help..."
    if ian_lena_dating and ian_lena_over == False:
        l "Or maybe I'll call Ian... I would like to hang out with him later today."
    elif lena_mike_dating:
        l "Or maybe I'll call Mike... I wonder if he'll be up to hang out or something."
    elif lena_robert_dating:
        l "Or maybe I'll call Robert... I'm sure he'll want to hang out or something."
    if v8_holly_sex == "lena":
        l "On second thought, maybe I should call Holly... I'm not sure how what happened yesterday will impact our relationship."
    play sound "sfx/ring.mp3"
    "I was pondering the many possibilities when I got a call."
    $ flena = "n"
    l "It's from home. It must be Mom again..."
    hide lenaunder
    show lenabra_phone
    with short
    l "Hi Mom, what's up?"
    show phone_dad_sad at lef3 with short
    ld "Lena, it's Dad."
    $ flena = "sad"
    l "Dad? You sound worried. Is everything alright?"
    stop music fadeout 2.0
    ld "We'll need to ask you to come home, honey."
    $ flena = "worried"
    ld "Mom is in the hospital."
    $ flena = "surprise"
    pause 1
    scene black with long
    pause 2
    jump master_script

## SCREENS #################################################################################################################################################################################################################################################################################################
screen v8clothingshop():
    if lena_wits > 2 and lena_wardrobe_wits1 == False:
        imagebutton idle "wardrobe_lena_wits1.webp" hover "wardrobe_lena_wits1_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v8shopwits')
    elif lena_wardrobe_wits1:
        imagebutton idle "wardrobe_lena_wits1.webp"
        imagebutton idle "wardrobe_lena_wits1_owned.webp"
    else:
        imagebutton idle "wardrobe_lena_wits1_block.webp"
    if lena_charisma > 2 and lena_wardrobe_charisma1 == False:
        imagebutton idle "wardrobe_lena_charisma1.webp" hover "wardrobe_lena_charisma1_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v8shopcharisma')
    elif lena_wardrobe_charisma1:
        imagebutton idle "wardrobe_lena_charisma1.webp"
        imagebutton idle "wardrobe_lena_charisma1_owned.webp"
    else:
        imagebutton idle "wardrobe_lena_charisma1_block.webp"
    if lena_athletics > 2 and lena_wardrobe_athletics1 == False:
        imagebutton idle "wardrobe_lena_athletics1b.webp" hover "wardrobe_lena_athletics1b_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v8shopathletics')
    elif lena_wardrobe_athletics1:
        imagebutton idle "wardrobe_lena_athletics1b.webp"
        imagebutton idle "wardrobe_lena_athletics1_owned.webp"
    else:
        imagebutton idle "wardrobe_lena_athletics1b_block.webp"
    if lena_lust > 2 and lena_wardrobe_lust1 == False:
        imagebutton idle "wardrobe_lena_lust1.webp" hover "wardrobe_lena_lust1_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v8shoplust')
    elif lena_wardrobe_lust1:
        imagebutton idle "wardrobe_lena_lust1.webp"
        imagebutton idle "wardrobe_lena_lust1_owned.webp"
    else:
        imagebutton idle "wardrobe_lena_lust1_block.webp"
    imagebutton idle "v7shopback.webp" hover "v7shopback_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v8leaveshop')
    add "sexshop_money.webp" pos (1799, 29)
    text "{font=[font_big_noodle_oblique]}{color=#000000}[lena_money]{/color}":
        size 30
        xpos 1815 ypos 56


# TATOOS###############################################################################
screen tattooshopscreen():
    if lena_tattoo1 == False and lena_money > 0:
        imagebutton idle "tattooshop1.webp" hover "tattooshop1_hover_cheap_comp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v8tat1')
    elif lena_tattoo1:
        add "tattooshop1_owned.webp"
    else:
        add "tattooshop1_blocked.webp"
    if lena_tattoo2 == False and lena_money > 1:
        imagebutton idle "tattooshop2.webp" hover "tattooshop2_hover_comp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v8tat2')
    elif lena_tattoo2:
        add "tattooshop2_owned.webp"
    else:
        add "tattooshop2_blocked.webp"
    if lena_tattoo3 == False and lena_money > 2:
        imagebutton idle "tattooshop3.webp" hover "tattooshop3_hover_comp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v8tat3')
    elif lena_tattoo3:
        add "tattooshop3_owned.webp"
    else:
        add "tattooshop3_blocked.webp"
    imagebutton auto "sexshop_leave_%s.webp" pos (856, 730) action Play ("ch_one", "sfx/paper_click.mp3"), Jump ('v8notat')
    add "sexshop_money.webp" pos (1799, 29)
    text "[lena_money]" style "sexshop_money"

image tattooshop1_hover_cheap_comp = Composite(
(1920, 1080),
(0, 0), "tattooshop1_hover.webp",
(0, 0), "tattooshop1_hover_cheap.webp",
)
image tattooshop2_hover_comp = Composite(
(1920, 1080),
(0, 0), "tattooshop2_hover.webp",
(0, 0), "tattooshop2_hover_cheap.webp",
)
image tattooshop3_hover_comp = Composite(
(1920, 1080),
(0, 0), "tattooshop3_hover.webp",
(0, 0), "tattooshop3_hover_cheap.webp",
)

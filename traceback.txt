﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "game/JDMOD/scripts/JD_IGG_custom.rpy", line 149, in script
    init python:
  File "game/JDMOD/scripts/JD_IGG_custom.rpy", line 149, in script
    init python:
  File "game/JDMOD/scripts/JD_IGG_custom.rpy", line 150, in <module>
    for route in CHAR:
NameError: name '<PERSON><PERSON>' is not defined

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\bootstrap.py", line 277, in bootstrap
    renpy.main.main()
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\main.py", line 558, in main
    renpy.game.context().run(node)
  File "game/JDMOD/scripts/JD_IGG_custom.rpy", line 149, in script
    init python:
  File "/home/<USER>/ab/renpy-build/tmp/install.linux-x86_64/lib/python3.9/site-packages/future/utils/__init__.py", line 441, in raise_
  File "game/JDMOD/scripts/JD_IGG_custom.rpy", line 149, in script
    init python:
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\ast.py", line 1131, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\python.py", line 1061, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "game/JDMOD/scripts/JD_IGG_custom.rpy", line 150, in <module>
    for route in CHAR:
NameError: name 'CHAR' is not defined

Windows-10-10.0.26100 AMD64
Ren'Py 8.0.3.22090809
Our Red String v13.3.6
Wed Jun 11 12:20:05 2025

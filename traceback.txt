﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 8, in execute
    screen load():
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 8, in execute
    screen load():
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 11, in execute
    if main_menu:
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 12, in execute
    use file_slots(_("Load"))
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 18, in execute
    screen file_slots(title, end=False):
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 18, in execute
    screen file_slots(title, end=False):
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 22, in execute
    use game_menu(title):
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 12, in execute
    screen game_menu(title, scroll=None, yinitial=0.0, cols=1, vpgrid_xspacing=50, vpgrid_yspacing=20, left_margin=120, gallery_nav=False, gallery_nav_custom=False, gallery_char=None, gallery_page=1, gallery_char_page=1, gallery_chapter=0):
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 12, in execute
    screen game_menu(title, scroll=None, yinitial=0.0, cols=1, vpgrid_xspacing=50, vpgrid_yspacing=20, left_margin=120, gallery_nav=False, gallery_nav_custom=False, gallery_char=None, gallery_page=1, gallery_char_page=1, gallery_chapter=0):
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 20, in execute
    frame:
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 23, in execute
    hbox:
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 30, in execute
    frame:
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 33, in execute
    if scroll == "viewport":
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 72, in execute
    transclude
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 22, in execute
    use game_menu(title):
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 23, in execute
    vbox style "file_slots_vbox":
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 36, in execute
    grid gui.file_slot_cols gui.file_slot_rows:
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 40, in execute
    for i in range(gui.file_slot_cols * gui.file_slot_rows):
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 42, in execute
    button:
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 42, in keywords
    button:
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 47, in <module>
    tooltip save_desc(slot)
NameError: name 'save_desc' is not defined

-- Full Traceback ------------------------------------------------------------

Full traceback:
  File "renpy/common/_layout/screen_main_menu.rpym", line 28, in script
    python hide:
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\ast.py", line 1131, in execute
    renpy.python.py_exec_bytecode(self.code.bytecode, self.hide, store=self.store)
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\python.py", line 1061, in py_exec_bytecode
    exec(bytecode, globals, locals)
  File "renpy/common/_layout/screen_main_menu.rpym", line 28, in <module>
    python hide:
  File "renpy/common/_layout/screen_main_menu.rpym", line 35, in _execute_python_hide
    ui.interact()
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\ui.py", line 299, in interact
    rv = renpy.game.interface.interact(roll_forward=roll_forward, **kwargs)
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\display\core.py", line 3377, in interact
    repeat, rv = self.interact_core(preloads=preloads, trans_pause=trans_pause, pause=pause, pause_start=pause_start, pause_modal=pause_modal, **kwargs) # type: ignore
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\display\core.py", line 3810, in interact_core
    root_widget.visit_all(lambda i : i.per_interact())
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\display\core.py", line 582, in visit_all
    d.visit_all(callback, seen)
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\display\core.py", line 582, in visit_all
    d.visit_all(callback, seen)
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\display\core.py", line 582, in visit_all
    d.visit_all(callback, seen)
  [Previous line repeated 1 more time]
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\display\screen.py", line 451, in visit_all
    callback(self)
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\display\core.py", line 3810, in <lambda>
    root_widget.visit_all(lambda i : i.per_interact())
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\display\screen.py", line 462, in per_interact
    self.update()
  File "Z:\BaiduNetdiskDownload\OurRedString-v13.3.6-pc\renpy\display\screen.py", line 653, in update
    self.screen.function(**self.scope)
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 8, in execute
    screen load():
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 8, in execute
    screen load():
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 11, in execute
    if main_menu:
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 12, in execute
    use file_slots(_("Load"))
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 18, in execute
    screen file_slots(title, end=False):
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 18, in execute
    screen file_slots(title, end=False):
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 22, in execute
    use game_menu(title):
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 12, in execute
    screen game_menu(title, scroll=None, yinitial=0.0, cols=1, vpgrid_xspacing=50, vpgrid_yspacing=20, left_margin=120, gallery_nav=False, gallery_nav_custom=False, gallery_char=None, gallery_page=1, gallery_char_page=1, gallery_chapter=0):
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 12, in execute
    screen game_menu(title, scroll=None, yinitial=0.0, cols=1, vpgrid_xspacing=50, vpgrid_yspacing=20, left_margin=120, gallery_nav=False, gallery_nav_custom=False, gallery_char=None, gallery_page=1, gallery_char_page=1, gallery_chapter=0):
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 20, in execute
    frame:
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 23, in execute
    hbox:
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 30, in execute
    frame:
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 33, in execute
    if scroll == "viewport":
  File "game/scripts/gui/renpy/screen_gamemenu.rpy", line 72, in execute
    transclude
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 22, in execute
    use game_menu(title):
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 23, in execute
    vbox style "file_slots_vbox":
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 36, in execute
    grid gui.file_slot_cols gui.file_slot_rows:
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 40, in execute
    for i in range(gui.file_slot_cols * gui.file_slot_rows):
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 42, in execute
    button:
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 42, in keywords
    button:
  File "game/JDMOD/scripts/gui/renpy/screen_saveload.rpy", line 47, in <module>
    tooltip save_desc(slot)
NameError: name 'save_desc' is not defined

Windows-10-10.0.26100 AMD64
Ren'Py 8.0.3.22090809
Our Red String v13.3.6
Wed Jun 11 12:41:55 2025

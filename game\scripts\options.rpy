﻿## This file contains options that can be changed to customize your game.
##
## Lines beginning with two '#' marks are comments, and you shouldn't uncomment
## them. Lines beginning with a single '#' mark are commented-out code, and you
## may want to uncomment them when appropriate.


## CUSTOM Basics ######################################################################

## A human-readable name of the game. This is used to set the default window
## title, and shows up in the interface and error reports.
##
## The _() surrounding the string marks it as eligible for translation.

define config.name = _("Our Red String")
define chapters = 13 # used in the gallery navigation code (for filtering gallery scenes)

default preferences.fullscreen = True


## Determines if the title given above is shown on the main menu screen. Set
## this to False to hide the title.

define gui.show_name = False

## greyed out functionality
default persistent.include_disabled = True          # Remembers the setting
default greyed_out_disabled = False                 # Remembers if the feature is temp disabled in individual save

default persistent.gallery_force_unlock = False     # Cheat to unlock all gallery scenes
default persistent.cheat_rollback = False           # Cheat to force unlock rollback

init:                                               # sets var correctly to start new save file
    if persistent.include_disabled:
        $ config.menu_include_disabled = True
    elif True:
        $ config.menu_include_disabled = False

label after_load:                                   # sets vars correctly on save file loading
    if persistent.include_disabled:
        if greyed_out_disabled:
            $ config.menu_include_disabled = False
        else:
            $ config.menu_include_disabled = True
    else:
        $ config.menu_include_disabled = False

init python:                                        # function for setting greyed out funtionality
    def greyed_out_setting():
        if persistent.include_disabled:
            if greyed_out_disabled:
                config.menu_include_disabled = False
            else:
                config.menu_include_disabled = True
        else:
            config.menu_include_disabled = False

    original_block_rollback = renpy.block_rollback  # Save original rollback behaviour

    def cheat_rollback():                           # Overwrite rollback behaviour
        if persistent.cheat_rollback:
            # Code from unren
            def unren_noblock( *args, **kwargs ):
                return
            renpy.block_rollback = unren_noblock
        else:
            renpy.block_rollback = original_block_rollback  # Restore rollback behaviour

    if persistent.cheat_rollback:                   # Set behaviour on game boot up
        # Code from unren
        def unren_noblock( *args, **kwargs ):
            return
        renpy.block_rollback = unren_noblock
    else:
        pass

init 999:
    python:
        update_all_gallery_images()

define config.language = "schinese"
## The version of the game. ########################################################################################################################################################################################################## build name

define config.version = "v13.3.6" 
# Format it like this: vAA.B.C
# - AA = Chapter (e.g 13)
# - B = Whose half of the chapter it is. (e.g. 0 = prologue, 1 = first PoV, 2 = second PoV, 3 = epilogue). Skip prologue or epilogue if not used.
# - C = The release ( 1= first Alpha build en then increase the number every time you make a new distribution with fixes etc)
#       may include "(Alpha)" or "(Beta)" at the end but refrain from using full.


## Text that is placed on the game's about screen. Place the text between the
## triple-quotes, and leave a blank line between paragraphs.

define gui.about = _p("""
""")


## A short name for the game used for executables and directories in the built
## distribution. This must be ASCII-only, and must not contain spaces, colons,
## or semicolons.

define build.name = "OurRedString"


## Sounds and music ############################################################

## These three variables control which mixers are shown to the player by
## default. Setting one of these to False will hide the appropriate mixer.

define config.has_sound = True
define config.has_music = True
define config.has_voice = True


## To allow the user to play a test sound on the sound or voice channel,
## uncomment a line below and use it to set a sample sound to play.

# define config.sample_sound = "sample-sound.ogg"
# define config.sample_voice = "sample-voice.ogg"


## Uncomment the following line to set an audio file that will be played while
## the player is at the main menu. This file will continue playing into the
## game, until it is stopped or another file is played.

define config.main_menu_music = "music/main_menu.mp3"


## Transitions #################################################################
##
## These variables set transitions that are used when certain events occur.
## Each variable should be set to a transition, or None to indicate that no
## transition should be used.

## Entering or exiting the game menu.

define config.enter_transition = fps
define config.exit_transition = fps


## Between screens of the game menu.

define config.intra_transition = fps


## A transition that is used after a game has been loaded.

define config.after_load_transition = Fade(0.5, 0.5, 0.5)


## Used when entering the main menu after the game has ended.

define config.end_game_transition = Fade(0.5, 0.5, 0.5)


## A variable to set the transition used when the game starts does not exist.
## Instead, use a with statement after showing the initial scene.


## Window management ###########################################################
##
## This controls when the dialogue window is displayed. If "show", it is always
## displayed. If "hide", it is only displayed when dialogue is present. If
## "auto", the window is hidden before scene statements and shown again once
## dialogue is displayed.
##
## After the game has started, this can be changed with the "window show",
## "window hide", and "window auto" statements.

define config.window = "auto"


## Transitions used to show and hide the dialogue window

define config.window_show_transition = Dissolve(.2)
define config.window_hide_transition = Dissolve(.2)


## Preference defaults #########################################################

## Controls the default text speed. The default, 0, is infinite, while any other
## number is the number of characters per second to type out.

default preferences.text_cps = 80


## The default auto-forward delay. Larger numbers lead to longer waits, with 0
## to 30 being the valid range.

default preferences.afm_time = 15


## Save directory ##############################################################
##
## Controls the platform-specific place Ren'Py will place the save files for
## this game. The save files will be placed in:
##
## Windows: %APPDATA\RenPy\<config.save_directory>
##
## Macintosh: $HOME/Library/RenPy/<config.save_directory>
##
## Linux: $HOME/.renpy/<config.save_directory>
##
## This generally should not be changed, and if it is, should always be a
## literal string, not an expression.

define config.save_directory = "RedString-1574621830"


## Icon ########################################################################
##
## The icon displayed on the taskbar or dock.

define config.window_icon = "gui/window_icon.png"


## Build configuration #########################################################
##
## This section controls how Ren'Py turns your project into distribution files.

init python:

    ## The following functions take file patterns. File patterns are case-
    ## insensitive, and matched against the path relative to the base directory,
    ## with and without a leading /. If multiple patterns match, the first is
    ## used.
    ##
    ## In a pattern:
    ##
    ## / is the directory separator.
    ##
    ## * matches all characters, except the directory separator.
    ##
    ## ** matches all characters, including the directory separator.
    ##
    ## For example, "*.txt" matches txt files in the base directory, "game/
    ## **.ogg" matches ogg files in the game directory or any of its
    ## subdirectories, and "**.psd" matches psd files anywhere in the project.
    ## Classify files as None to exclude them from the built distributions.
    build.classify('**~', None)
    build.classify('**.bak', None)
    build.classify('**/.**', None)
    build.classify('**/#**', None)
    build.classify('**/thumbs.db', None)
    build.classify('**.example', None) # .example files
    build.classify('game/cache/**', None) # cache
    build.classify('game/saves/**', None) # saves
    build.classify('**/_ignore/**', None) # unused assets
    build.classify('**desktop.ini', None) # desktop ini

    # Put script files into the scripts archive.
    build.archive("scripts", "all")
    build.classify('game/scripts/**', 'scripts')

    build.archive("gui", "all")
    build.classify('game/gui/**', 'gui')

    # Put images into the images archive.
    build.archive("images", "all")
    build.classify("game/**.jpg", "images")
    build.classify("game/**.png", "images")
    build.classify("game/**.webp", "images")

    build.archive("audio", "all")
    build.classify('game/**.ogg', 'audio')
    build.classify('game/**.mp3', 'audio')
    build.classify('game/**.wav', 'audio')

    build.archive("videos", "all")
    build.classify('game/**.webm', 'videos')
    build.classify('game/**.mkv', 'videos')
    build.classify('game/**.mp4', 'videos')
    build.classify('game/**.mov', 'videos')

    build.archive("fonts", "all")
    build.classify('game/fonts/**', 'fonts')
    build.classify('game/**.ttf', 'fonts')
    build.classify('game/**.otf', 'fonts')


    ## Files matching documentation patterns are duplicated in a mac app build,
    ## so they appear in both the app and the zip file.
    build.documentation('*.html')
    build.documentation('*.txt')

## Set this to a string containing your Apple Developer ID Application to enable
## codesigning on the Mac. Be sure to change it to your own Apple-issued ID.

# define build.mac_identity = "Developer ID Application: Guy Shy (XHTE5H7Z42)"


## A Google Play license key is required to download expansion files and perform
## in-app purchases. It can be found on the "Services & APIs" page of the Google
## Play developer console.

# define build.google_play_key = "..."


## The username and project name associated with an itch.io project, separated
## by a slash.

# define build.itch_project = "renpytom/test-project"

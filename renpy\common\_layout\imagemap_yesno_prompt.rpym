﻿# Copyright 2004-2022 <PERSON> <<EMAIL>>
#
# Permission is hereby granted, free of charge, to any person
# obtaining a copy of this software and associated documentation files
# (the "Software"), to deal in the Software without restriction,
# including without limitation the rights to use, copy, modify, merge,
# publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so,
# subject to the following conditions:
#
# The above copyright notice and this permission notice shall be
# included in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
# EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
# NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
# LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
# OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
# WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

init python hide:

    layout.provides('yesno_prompt')

    renpy.load_module("_layout/imagemap_common")

    # Define styles
    style.yesno_prompt = Style(style.prompt, help="a yes/no prompt")
    style.yesno_prompt_text = Style(style.prompt_text, help="a yes/no prompt (text)")

    # Define config variables.
    config.yesno_prompt_ground = None
    config.yesno_prompt_idle = None
    config.yesno_prompt_hover = None
    config.yesno_prompt_hotspots = None

    config.yesno_prompt_message_images = { }


    def yesno_prompt(screen, message):
        renpy.transition(config.intra_transition)

        ime = _ImageMapper(
            screen,
            config.yesno_prompt_ground,
            config.yesno_prompt_idle,
            config.yesno_prompt_hover,
            config.yesno_prompt_idle,
            config.yesno_prompt_hover,
            config.yesno_prompt_hotspots)

        ime.button("Yes", ui.returns(True), False)
        ime.button("No", ui.returns(False), False)

        ime.close()

        default = config.yesno_prompt_message_images.get(layout.ARE_YOU_SURE, None)
        message_image = config.yesno_prompt_message_images.get(message, default)

        if message_image:
            ui.add(message_image)
        else:
            layout.prompt(message, "yesno")

        rv = ui.interact(mouse="gamemenu")
        renpy.transition(config.intra_transition)
        return rv

    layout.yesno_prompt = yesno_prompt


init offset = 1









define config.menu_include_disabled = True

init python:
    def get_sceneImg(sc):
        
        
        global gallery_scenes
        
        img = im.Scale("blackbg1.webp", gallery_scene_xsize, gallery_scene_ysize) 
        for item in gallery_scenes:
            if item.unlocked_if == "gallery_scene_unlocked('%s')" % sc:
                if gallery_scene_unlocked(sc):
                    img = Image(item.img)
                else:
                    img = item.img_locked
        
        return img

    def get_sceneName(sc):
        
        
        
        
        global gallery_scenes
        
        name = sc 
        if sc == "":
            name = "TBD" 
        for item in gallery_scenes:
            if item.unlocked_if == "gallery_scene_unlocked('%s')" % sc:
                name = item.name
        
        return name










    def get_firstItem(argument):
        
        
        
        
        firstItem = None
        
        
        if type(argument) is not tuple:
            firstItem = argument
        elif argument[1] is True:
            firstItem = argument[0]
        
        return firstItem        

    def req_choice_text_color(stat):
        d = {"lust": "#c41d19",
            "athletics": "#faee80",
            "money": "#5db945",
            "pay": "#d62130",
            "wits": "#1f547d",
            "charisma": "#d47f12",
            "love": "#c41d19",
            "will": "#356184",
            "mad": "#dc3932",
            "bdick": "#401b48"}
        
        return d.get(stat, "#ead245") 

image JD_icon_tooltip = im.Scale("JDMOD/images/gui/JD.png", 40, 40, yoffset=-8)

screen choice(items):
    style_prefix "choice"

    vbox:
        for i, entry in enumerate(items, 1):


            $ col_text_color = "#cccccc"
            $ col = entry.kwargs.get("col", None)
            if col is not None and not _in_replay and persistent.JD_IGG_choice_colors:
                $ col = list(col)
                for col_int in range(0, len(col)):
                    if type(col[col_int]) is not tuple or (type(col[col_int]) is tuple and col[col_int][1] is True):
                        if col[col_int][0] == "b":
                            $ col_text_color = "#444444"
                        elif col[col_int][0] == "g":
                            $ col_text_color = "#B77D0A"


            $ req = entry.kwargs.get("req", None)
            $ cha = entry.kwargs.get("cha", None)
            $ route = entry.kwargs.get("route", None)
            $ sc = entry.kwargs.get("sc", None)
            $ tt = entry.kwargs.get("tt", None)


            $ tt_templist = []

            $ sc_addextralines = False
            if get_firstItem(sc) is not None and persistent.JD_IGG_choice_scenes_preview:
                $ tt_templist.extend(["{color=#A97C14}{u}Scene{/u}: ", get_sceneName(get_firstItem(sc)), "{/color}\n", get_sceneImg(get_firstItem(sc))])
                $ sc_addextralines = True

            if tt is not None and persistent.JD_IGG_choice_tooltips:
                if type(tt) is list:
                    for tt_int in range(0, len(tt)):
                        if get_firstItem(tt[tt_int]) is not None:

                            if get_firstItem(sc) is not None and persistent.JD_IGG_choice_scenes_preview and sc_addextralines:
                                $ tt_templist.extend(["\n", "\n"])
                                $ sc_addextralines = False
                            $ tt_templist.extend([get_firstItem(tt[tt_int]), "\n"])
                else:
                    if get_firstItem(sc) is not None and persistent.JD_IGG_choice_scenes_preview:
                        $ tt_templist.extend(["\n", "\n"])
                    if get_firstItem(tt) is not None:
                        $ tt_templist.append(get_firstItem(tt))


                if tt_templist:
                    if tt_templist[-1] == "\n":
                        $ tt_templist = tt_templist[:-1]


            $ tooltip = None
            if tt_templist:
                $ tooltip = tt_templist






            if entry.action.sensitive is False:
                button:
                    style_prefix "insensitive_choice"


                    action NullAction()


                    if i < 10 and entry.action:
                        keysym (str(i), "K_KP"+str(i))

                    if not _in_replay:

                        if tooltip is not None and persistent.JD_IGG_choice_tooltips:
                            tooltip tooltip


                        if get_firstItem(sc) is not None and persistent.JD_IGG_choice_scenes:
                            background Frame(Transform("JDMOD/images/gui/scene_choice_idle_background.png", alpha=0.5), gui.button_borders, tile=gui.button_tile)









                    fixed:
                        fit_first "height"


                        text "{alpha=0.8}" + entry.caption + "{/alpha}":
                            xalign 0.50
                            xsize int( config.screen_width * 0.6 )
                            text_align 0.5
                            color col_text_color


                        if i < 10 and entry.action:
                            text "{alpha=0.8}{size=-2}[i].{/size}{/alpha}":
                                style "keybind_choice_text"
                                color col_text_color


                        if not _in_replay:

                            if req is not None:
                                $ req = list(req)
                                hbox:
                                    style "req_choice_hbox"
                                    for req_int in range(0, len(req)):
                                        if type(req[req_int]) is not tuple:
                                            if req[req_int] in ["/", "+"]:
                                                text "{alpha=0.8}" + req[req_int] + "{/alpha}":
                                                    style "req_choice_text"
                                                    outlines [ (1, "#FFFFFF80", 0, 0) ]
                                            else:
                                                add ImageReference("JD_icon_{}".format(req[req_int])) alpha 0.6
                                        else:
                                            if req[req_int][0] in ["mad"]:
                                                $ req_num = req[req_int][1]-1
                                            else:
                                                $ req_num = req[req_int][1]+1
                                            hbox:
                                                spacing 5
                                                text "{alpha=0.8}[req_num]{/alpha}":
                                                    style "req_choice_text"
                                                    outlines [ (1, "#FFFFFF80", 0, 0) ]
                                                    color req_choice_text_color(req[req_int][0])
                                                add ImageReference("JD_icon_{}".format(req[req_int][0])) alpha 0.6


                            if cha is not None and persistent.JD_IGG_choice_icons:
                                $ cha = list(cha)
                                hbox:
                                    style "cha_choice_hbox"
                                    for cha_int in range(0, len(cha)):
                                        if get_firstItem(cha[cha_int]) is not None:
                                            add ImageReference("JD_icon_{}".format(get_firstItem(cha[cha_int]))) alpha 0.6


                            if route is not None and persistent.JD_IGG_choice_routes:
                                hbox:
                                    style "route_choice_hbox"
                                    if get_firstItem(route) is not None:
                                        add ImageReference("JD_icon_{}_route_big".format(get_firstItem(route)))


                            if tooltip is not None and persistent.JD_IGG_choice_tooltips:
                                add ImageReference("JD_icon_tooltip") xoffset 22 yoffset -15 alpha 0.6





            else:
                button:

                    action entry.action, SetField(config, "menu_include_disabled", True)


                    if i < 10 and entry.action:
                        keysym (str(i), "K_KP"+str(i))

                    if not _in_replay:

                        if get_firstItem(sc) is not None:

                            idle_background Frame("JDMOD/images/gui/scene_choice_idle_background.png", gui.button_borders, tile=gui.button_tile)
                            hover_background Frame("JDMOD/images/gui/scene_choice_hover_background.png", gui.button_borders, tile=gui.button_tile)

                        if tooltip is not None and persistent.JD_IGG_choice_tooltips:
                            tooltip tooltip



                    fixed:
                        fit_first "height"


                        text entry.caption:
                            xalign 0.50
                            xsize int( config.screen_width * 0.6 )
                            text_align 0.5
                            color col_text_color


                        if i < 10 and entry.action:
                            text "{size=-2}[i].{/size}":
                                style "keybind_choice_text"
                                color col_text_color
                                outlines [ (1, JD_keybind_outlines_color, 0, 0) ]

                        if not _in_replay:

                            if req is not None:
                                $ req = list(req)
                                hbox:
                                    style "req_choice_hbox"
                                    for req_int in range(0, len(req)):
                                        if type(req[req_int]) is not tuple:
                                            if req[req_int] in ["/", "+"]:
                                                text "{alpha=0.8}" + req[req_int] + "{/alpha}":
                                                    style "req_choice_text"
                                                    outlines [ (1, "#FFFFFF80", 0, 0) ]
                                            else:
                                                add ImageReference("JD_icon_{}".format(req[req_int]))
                                        else:
                                            if req[req_int][0] in ["mad"]:
                                                $ req_num = req[req_int][1]-1
                                            else:
                                                $ req_num = req[req_int][1]+1
                                            hbox:
                                                spacing 5
                                                text "[req_num]":
                                                    style "req_choice_text"
                                                    outlines [ (1, "#FFF", 0, 0) ]
                                                    color req_choice_text_color(req[req_int][0])
                                                add ImageReference("JD_icon_{}".format(req[req_int][0]))


                            if cha is not None and persistent.JD_IGG_choice_icons:
                                $ cha = list(cha)
                                hbox:
                                    style "cha_choice_hbox"
                                    for cha_int in range(0, len(cha)):
                                        if get_firstItem(cha[cha_int]) is not None:
                                            add ImageReference("JD_icon_{}".format(get_firstItem(cha[cha_int])))


                            if route is not None and persistent.JD_IGG_choice_routes:
                                hbox:
                                    style "route_choice_hbox"
                                    if get_firstItem(route) is not None:
                                        add ImageReference("JD_icon_{}_route_big".format(get_firstItem(route)))


                            if tooltip is not None and persistent.JD_IGG_choice_tooltips:
                                add ImageReference("JD_icon_tooltip") xoffset 22 yoffset -15





        if (timeout_label is not None) and persistent.timed_choices:
            bar:
                xalign 0.5

                xsize 1100
                ysize 10
                value AnimatedValue(old_value=0.0, value=1.0, range=1.0, delay=timeout)
            timer timeout action Jump(timeout_label)

    use mouse_tooltip(screen="choice")


style choice_button:
    idle_background Frame("gui/button/choice_idle_background.png", gui.button_borders, tile=gui.button_tile)
    hover_background Frame("gui/button/choice_hover_background.png", gui.button_borders, tile=gui.button_tile)
    padding (100, 8, 100, 8)
    xsize int(config.screen_width * 0.7)

style insensitive_choice_button is choice_button:
    background Frame(Transform("gui/button/choice_idle_background.png" , alpha=0.5), gui.button_borders, tile=gui.button_tile)

style req_choice_hbox:
    spacing 8
    xanchor 0.0
    xpos 0.07
    yalign 0.5

style cha_choice_hbox:
    spacing 5
    xanchor 1.0
    xpos 1.0
    yalign 0.5

style route_choice_hbox:
    spacing 5
    xanchor 1.0
    xpos 1.07
    yalign 0.5

style choice_text is choice_button_text

style req_choice_text:
    font font_big_noodle
    size 29

style keybind_choice_text:
    xpos 0.0
    yalign 0.5

style route_choice_text:
    xpos 1.0
    yalign 0.5
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc

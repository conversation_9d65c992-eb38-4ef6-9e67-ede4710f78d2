#######################################################################################################################################################
#################################################### CHAPTER 12 PROLOGUE ###################################################################################################
#######################################################################################################################################################
label chapter_twelve:
    
    $ save_name = "Ian: Chapter 12"
    $ ian_active = True
    $ lena_active = False

    call label_chapter_title from _call_label_chapter_title_11

    show active_ian
    with long
    pause 1.0
    call calendar(_month="August", _day="Friday", _week=1) from _call_calendar_122
    
    if ian_alison_breakup: # Redundancy - Ch 11 breakup plot-hole fix
        $ ian_alison_dating = False

    if v11_holly_change:
        $ holly_hair = 2
    
    if v11_holly_change and holly_gym:
        $ holly_fit = True

    if ian_cherry_dating:
        jump v12cherryscene
    else:
        jump v12office

# CHERRY ######################################################################################################################################################
######################################################################################################################################################
label gallery_CH12_S01:
    if _in_replay:
        call setup_CH12_S01 from _call_setup_CH12_S01

label v12cherryscene:
    scene cherryhome with long
    pause 1
    ch "Wakey-wakey..."
    play music "music/sex_chill.mp3" loop
    scene v11_cherry8 with long
    pause 1
    i "Um... Hey... Good morning..."
    "Cherry lay on top of me, showering my face with tender little kisses."
    ch "Did you sleep well?"
    i "You bet... Last night left me completely spent."
    ch "Have you managed to recover some energy perhaps?"
    "I kissed her back on the lips as my hand descended across her back, holding her slender waist."
    i "You seem to be in a pretty horny mood when you wake up in the mornings..."
    ch "Guess who's fault is that?"
    i "So I wasn't able to satisfy you last night?"
    ch "Oh, you did... That's why I want more of you."
    # bj
    scene v12_cherry1 with long
    pause 1
    ch "And it seems you want more of me too..."
    i "Mhhh... How could I not?"
    "Cherry's warm touch sent jolts of sweet pleasure across my body, and her kisses grazed the shaft of my erect cock."
    "I groaned. Even her natural perfume was enough to get me aroused... We had such good chemistry."
    "Cherry continued to caress my cock, seemingly as excited as I was."
    "The more time we spent together, it became clearer how insatiable her lust actually was..."
    menu:
        "Let Cherry suck you off":
            $ renpy.block_rollback()
            scene v12_cherry3_animation with long
            play sound "sfx/bj6.mp3"
            pause 4
            i "Oh, fuck... Mhhh!"
            "Cherry began deepthroating me right away. It never failed to amaze me how she could do it so effortlessly..."
            "It took her a while to really show me what she was capable of, but now she seemed to be much more uninhibited."
            i "Mhhh... You woke up hungry indeed..."
            ch "Nhhh... Yeah... Very hungry for your cock..."
            if ian_charisma < 8:
                call xp_up ('charisma') from _call_xp_up_946
                pause 0.5

        "Eat Cherry out":
            $ renpy.block_rollback()
            i "Come here. I wanna give you good morning kisses too."
            play sound "sfx/moan5.mp3"
            scene v12_cherry2 with long
            pause 1
            "I held Cherry by the hips and turned her over, scooting between her legs. Her pretty dark pussy was moist to the touch of my lips."
            ch "Oh...! Mhhh... Seems you woke up hungry too..."
            i "You know I can't resist making you feel good."
            ch "And I can't resist you at all...! Oh, Ian...!"
            "Cherry's hips trembled when I sucked on her hard and sensitive clit."
            "Hearing her moans and feeling her body contort under my caresses made my cock throb, swollen with increasing lust."
            ch "Mhhh! I love how you eat me out..."
            if ian_wits < 8:
                call xp_up ('wits') from _call_xp_up_947
                pause 0.5

        "Fuck Cherry":
            $ renpy.block_rollback()

    ch "Fuck me, Ian... I want you inside of me."
    play sound "sfx/moan4.mp3"
    # sex
    scene v12_cherry4 with long
    pause 1
    "Her slit was as tight as her sylphlike body, but it welcomed me with a hot and wet embrace."
    "My entire cock slid inside in a single motion, burrowing into her utmost depths."
    ch "Ohhh, yes...! I can feel you reaching all the way to the end... You're so hard...!"
    "As she said those words, I felt her pussy contracting around my shaft. I began moving my hips slowly."
    "Cherry moaned and gasped as I continued to pump my hips, sliding my cock out almost entirely, only to immediately bury it as deep as it would go."
    "Watching her melt with pleasure was such an alluring sight..."    
    menu:
        "{image=icon_charisma.webp}Claim Cherry" if ian_charisma > 6:
            $ renpy.block_rollback()
            # tease
            scene v12_cherry5 with long
            pause 1
            "It made me want to impose my desire on her, push her harder... Make her mine."
            ch "Nhhh... Ian...!"
            "She gasped when I placed my hand around her neck, driving my hips forward and feeling her inner walls clamp on my throbbing dick."
            i "I want you, Cherry...!"
            "My fingers squeezed her neck a bit tighter, and she trembled."
            "Her eyes were locked with mine as I continued to slide my cock balls deep into her with slow but ardent movements."
            if ian_lust < 8:
                call xp_up ('lust') from _call_xp_up_948
                pause 1
            "Our connection was like an electric arc. I was jolted by it, trapped in its crackling tension. "
            if ian_lust > 7:
                menu:
                    "{image=icon_lust.webp}Dominate Cherry":
                        $ renpy.block_rollback()
                        label v12dominatecherry:
                            $ v12_cherry_dom = True
                        stop music fadeout 3.0
                        "My grasp on Cherry's neck tightened."
                        i "I want you to be mine, Cherry. All mine."
                        play music "music/sex_deep2.mp3" loop
                        "She held my hips, inviting me to drive harder into her."
                        ch "Yes, I am... All yours... Take me however you want!"
                        # doggy
                        scene v12_cherry7 with long
                        pause 1
                        i "In that case... I believe you want me to be a bit rough with you today, don't you?"
                        play sound "sfx/slap3.mp3"
                        with hpunch
                        play sound "sfx/mh2.mp3"
                        "Cherry squirmed when I slapped her ass, but not from discomfort. She moaned and moved her hips back, taking my cock deep inside."
                        ch "God, you're so hard... I love it...!"
                        i "Get ready to feel all of it... You're turning me on like mad today."
                        scene v12_cherry8c with long
                        "I pinned Cherry against the bed, holding her head down, preparing to unload all my lust into her."
                        scene v12_cherry8b with fps
                        play sound "sfx/oh1.mp3"
                        scene v12_cherry8a with vpunch
                        ch "Oh, God...!"
                        i "Fuck yeah... Take it like a good girl, just like this."
                        # animation
                        scene v12_cherry8_animation1 with fps
                        pause 3
                        "I let myself loose in a way I hadn't allowed myself yet."
                        "I wanted Cherry to feel the full force of my pleasure and I pounded into her harder and faster, making her yelp."
                        "Her moans were unlike any I had heard from her before, simultaneously growly and sweet, as if voracity and vulnerability mingled in a potent concoction."
                        "I felt that she was giving herself to me completely for the first time, revealing a part of herself she had kept hidden until now..."
                        if ian_athletics < 10:
                            call xp_up ('athletics') from _call_xp_up_949
                        pause 3
                        "And the harder I pounded into her, the more I revealed that part of her."
                        "I felt my cock hard as an iron rod, ravaging Cherry's pussy mercilessly, my mind numbed by the intensity of it all."
                        i "How does it feel to be mine?"
                        ch "Ahn...! Ian!! Oh, God!!{w=0.5}{nw}"
                        play sound "sfx/orgasm2.mp3"
                        scene v12_cherry8a with flash
                        with vpunch
                        pause 0.5
                        with vpunch
                        pause 0.5
                        with vpunch
                        pause 1
                        stop sound fadeout 1.0
                        if ian_cherry_love:
                            scene v12_cherry6 with long
                            pause 1
                        "When I finally felt her body shaking with pleasure, I allowed myself to let go and come deep inside her."
                        "During a a blissful instant, I melted. Trapped deep inside Cherry. There was no other place I'd rather be."
                        "After a short while, I pulled out and lay next to Cherry, embracing her and pulling her closer."
                        stop music fadeout 3.0
                        scene v11_cherry8
                        show v11_cherry8_blush1
                        with long
                        pause 1
                        "We panted and puffed together, recovering after the fiery ordeal."
                        ch "..."
                        show v11_cherry8_blush2 with short
                        i "You're quiet... Are you okay?"
                        ch "Yes, I am... It's just... My emotions are all over the place."
                        i "Are you sure you're okay?"
                        play music "music/date.mp3" loop
                        "Cherry cozied up to my chest, pressing close."
                        ch "Yes... I just feel a bit vulnerable, that's all. It was so intense... Even more than usual."
                        i "Yeah... Was I too rough? I had the impression you liked it."
                        hide v11_cherry8_blush1 with short
                        ch "I was. A little too much, even..."
                        hide v11_cherry8_blush2 with short
                        i "It's never too much. I want you to enjoy yourself as much as possible... It's what turns me on the most."
                        ch "Yeah, I noticed... You can be very dominant when you get in the mood..."
                        i "I got the feeling you were inviting me to be."
                        if ian_cherry_love:
                            ch "If it's you... I feel I can let all my barriers down."
                            i "I hope you continue to do that... I love discovering more and more about you."
                            ch "I share the sentiment..."
                        else:
                            ch "What can I say...? You have this knack for making me get carried away."
                            i "What a useful ability..."
                        if ian_cherry < 12:
                            call friend_xp ('cherry') from _call_friend_xp_1086

                    "{image=icon_love.webp}Kiss her" if ian_cherry_love:
                        $ renpy.block_rollback()
                        "I wanted to be one with Cherry."
                        jump v12cherrykiss

                    "Keep going":
                        $ renpy.block_rollback()
                        jump v12cherrydoggy
            else:
                menu:
                    "{image=icon_will.webp}Dominate Cherry" if ian_will > 0:
                        $ renpy.block_rollback()
                        call willdown from _call_willdown_60
                        jump v12dominatecherry

                    "{image=icon_love.webp}Kiss her" if ian_cherry_love:
                        $ renpy.block_rollback()
                        "I wanted to be one with Cherry."
                        jump v12cherrykiss

                    "Keep going":
                        $ renpy.block_rollback()
                        jump v12cherrydoggy

        "{image=icon_love.webp}Kiss her" if ian_cherry_love:
            $ renpy.block_rollback()
            label v12cherrykiss:
                i "You're so beautiful."
            # kiss
            scene v12_cherry6 with long
            pause 1
            "I leaned forward and planted a passionate kiss on her lips, a kiss she reciprocated."
            "My cock was as deep inside her as my tongue, entangled with hers in a frantic and heartfelt dance."
            "I felt Cherry's hands grasping at my body, holding me close as she moaned, her fingers digging into me as the pleasure continued to mount."
            "I could tell she was close to orgasm, and I would follow shortly..."
            play sound "sfx/oh3.mp3"
            with flash
            with vpunch
            pause 0.5
            with vpunch
            pause 1
            stop music fadeout 2.0
            "We basked in the joy of climax in each other's embrace."
            "I felt like the luckiest guy alive, wishing I could wake up like this time and time again, sharing my pleasure with Cherry..."
            if ian_will == 0:
                call will_up() from _call_will_up_11

        "Keep going":
            $ renpy.block_rollback()
            label v12cherrydoggy:
                # doggy
                scene v12_cherry7 with long
                pause 1
            "Our bodies matched perfectly, entangled in the most arousing back-and-forth."
            "I held Cherry's hips and moved her to my will, enjoying her with delight as the pleasure continued to mount."
            play sound "sfx/oh3.mp3"
            with flash
            with vpunch
            pause 0.5
            with vpunch
            pause 1
            stop music fadeout 2.0
            "We basked in the joy of climax, Cherry first and I only a few moments later."
            if ian_cherry_love:
                "I felt like the luckiest guy alive, wishing I could wake up like this time and time again, sharing my pleasure with Cherry..."
            else:
                "What a great way to start the day..."
            if ian_lust < 7:
                call xp_up ('lust') from _call_xp_up_950
                pause 1
# TALK SCENE
    if v12_cherry_dom == False:
        play music "music/date.mp3" loop
    scene cherryhome with long
    $ fian = "smile"
    $ fcherry = "happy"
    show iannude2 at lef
    show cherrynude at rig
    with long
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH12_S01")
    ch "I think we've lingered a bit too much... Do we still have time for coffee, or...?"
    if v5_ian_showup:
        i "Yeah, don't worry. No one's keeping watch over me at the office; it's no big deal if I check in a bit late."
        i "Today's my last day before my vacation anyhow..."
    else:
        i "Yeah, don't worry. I don't care if the boss gets mad at me for being late... My internship ends today anyway."
    ch "I'll prepare some breakfast, then. But let's take a shower first..."
    play sound "sfx/shower.mp3"
    scene cherryhome with long
    if ian_cherry_love:
        "The shower helped me freshen up a bit. After such an intense wake-up, what I wanted was to stay in bed with Cherry, not go to the office..."
        "This thing we had seemed to be going somewhere... We were both eager to spend time together, that much was true."
        "And finally, yesterday, we found some time... Too bad she would be spending her summer vacation overseas."
    else:
        "The shower helped me freshen up a bit. After such an intense wake-up, what I wanted was to go back to bed, not to the office..."
        "After our date on the day of the exhibition, Cherry and I had agreed to see each other more often. And finally, yesterday, we found some time..."
        "Too bad I wouldn't be able to see her in a while, since she was spending her summer vacation overseas."
    $ ian_look = 2
    $ cherry_look = 1
    $ fian = "smile"
    $ fcherry = "smile"
    show cherrybra at rig with short
    ch "Coffee's ready."
    show ianunder at lef3 with short
    show ianunder at lef with move
    i "Thanks..."
    if v5_ian_showup:
        ch "So you're starting your summer vacation tomorrow?"
        i "Yeah, but I'm only taking a week off. I've just started working in the publishing industry, so I can't afford to be too lazy."
        $ fcherry = "happy"
        ch "\"Lazy\" is not a word I would use to describe you."
    else:
        ch "So your internship is ending today? Do you know what you're gonna do next?"
        $ fian = "n"
        i "It's not entirely clear yet... I've applied for a job in the publishing industry, but I still don't know if I'll get the position."
        $ fian = "smile"
        i "I have reasons to be confident, though..."
        $ fcherry = "happy"
        ch "That's good to hear."
    i "What about you? When are you leaving?"
    $ fcherry = "smile"
    ch "My flight is next Wednesday... And I'll be back in three weeks."
    if v11_cherrytalk2:
        i "So you're going to visit your family... Do you travel back to your home country often?"
        ch "Once a year, if I'm able... And summer vacation is always the best moment to do so."
    else:
        $ fian = "n"
        i "Um... Where were you going again?"
        $ fcherry = "n"
        ch "To visit my family... They live abroad."
        i "I didn't know. So you're here all by yourself?"
        ch "Not originally... I moved to this country when I was only three or four years old. My uncle had come here searching for work, and a few years later my mother and aunt moved in with him, taking me with them."
        $ fian = "smile"
        i "I see. So you grew up here."
        ch "In another city, but yeah. I moved to Baluart to attend college. During that time, my family decided to go back to their hometown, since they had made some savings and life is much cheaper there."
        ch "I decided to stay here to finish my studies and find work. My life's here now..."
        $ fcherry = "smile"
        ch "But I try to visit them at least once a year. And summer vacation is always the best moment to do so."
    # lena status
    $ fian = "n"
    if ian_holly_dating:
        ch "What about you? Perry told me he's invited you guys to spend a few days at his parents' beach house..."
        $ fian = "smile"
        i "Yeah. It's been a while since we all took a vacation together."
        ch "I'm sure it'll be a great time... Perry invited me too, but..."
        $ fcherry = "sad"
        ch "There's no way I could go if Lena is going too. She wouldn't have it."
    else:
        i "I would've liked you to join me at Perry's beach house..."
        $ fcherry = "sad"
        ch "I know... He invited me, but there's no way I could go if Lena is going too. She wouldn't have it."
    if v11_perry_invite > 3:
        i "It's still not clear if she's going to join us..."
        ch "Still, I don't wanna risk it. I'd feel like I'm intruding somehow..."
    elif v11_perry_invite == 0:
        i "Actually, I'm not sure if she's coming at all..."
        ch "Still, I don't wanna risk it. I'd feel like I'm intruding somehow..."
    if v11_lena_breakup:
        ch "How are things between you two, by the way?"
        i "Well... She didn't like hearing what I had to say, but I knew that would be the case. There was no other way around it..."
        i "Things are uncomfortable between us at the moment, but I hope we can remain friends... or something like that."
        if ian_lena_couple_over:
            ch "And do you think that can work? You two were in a relationship, after all..."
            i "We tried, but it wasn't meant to work..."
            i "In any case, I followed what my heart told me. I know I caused quite a stir, but hopefully, I can make amends."
        ch "I hope you manage to make it work... She and I will never be friends, tough."
    elif ian_lena_breakup:
        ch "How are things between you two, by the way?"
        if ian_lena_couple_over:
            ch "You two got into a relationship after all..."
            $ fian = "sad"
            i "We tried to, at least... We probably rushed into it when we shouldn't have..."
            $ fian = "n"
            i "We had very different perspectives on things. It's clear to me we were after different things."
            i "Now... Well, I'd lie if I said things aren't a bit uncomfortable between us."
            i "It will take some time, but I believe we can remain friends."
        elif v10_ian_left:
            i "They're... alright I guess. It's been rather uncomfortable, but we talked things over..."
            i "We had very different perspectives on things. It's clear to me we were after different things, and that's alright..."
            i "It will take some time, but I believe we can remain friends."
        else:
            i "They're alright... We hooked up for a while, but we were looking for different things."
            $ fian = "smile"
            i "It's been a while since that. I think both of us moved on, so we can be just friends now..."
        ch "I'm glad that's the case... She and I will never be friends, tough."
    else:
        i "Not at all... Perry likes you a lot, and the same goes for Emma. And I'm sure you'd get along great with Holly."
        ch "Lena and I will never be friends, tough..." 
    $ fian = "n"
    menu:
        "Why don't you try to make amends?":
            $ renpy.block_rollback()
            $ v12_cherry_lena = True
            $ fian = "sad"
            i "Why don't you try to make amends with her? If you two sat down and talked..."
            ch "You saw how she reacted that day... Do you think she'd agree to sit down and hear me out?"
            ch "It's not like I can tell her anything that will change how she feels about me and what went down."
            i "Are you really okay with leaving things like this? Lena blames you, but I think Axel is the one she's really mad with."
            i "He's the main culprit in all of this, if you ask me..."
            ch "I'm at fault too. I can't deny my mistakes, and I can't justify them in front of Lena."
            i "I'm sure there's a way to make her understand..."
            ch "No. Besides, I decided I didn't want to have anything to do with that drama anymore, so please... Don't ask again."
            call friend_xp ('cherry',-1) from _call_friend_xp_1087
            $ fian = "worried"
            i "Sorry... I was just thinking of a way to fix things..."
            if ian_lena_over or ian_lena_breakup:
                ch "I know, and I appreciate it. But Lena and I will never be friends... Especially now that you and I are seeing each other."
            else:
                ch "I know, and I appreciate it. But Lena and I will never be friends..."

        "It's a shame":
            $ renpy.block_rollback()
            $ fian = "sad"
            i "It's a shame... But you're probably right."
            ch "You saw how she reacted that day... It's the same every time we cross paths, but I don't blame her."
            $ fian = "n"
            i "Maybe she'll get over it with time... The one she's really mad with is Axel, if you ask me."
            ch "You're probably right, but still... I think the farther apart we are from each other, the better."
            if ian_lena_over or ian_lena_breakup:
                ch "Especially now that you and I are seeing each other..."

        "There's nothing you can do":
            $ renpy.block_rollback()
            i "Well, there's nothing more you can do about it. You tried, right?"
            ch "I guess... I still feel at fault for what happened. I don't blame her for hating me."
            i "The main culprit in all of this is Axel, if you ask me. I think he's the one she's really mad with..."
            ch "Could be. In any case, I decided I didn't want to have anything to do with that drama anymore..."
            i "Don't beat yourself over it... You've done that enough as it is."
            if ian_cherry < 12:
                call friend_xp ('cherry') from _call_friend_xp_1088

    $ fcherry = "n"
    if v12_cherry_lena:
        ch "Anyway... It's not worth going on about it. Let's talk about something else."
        $ fian = "n"
        i "Yeah..."
    else:
        ch "Anyway... It's not worth going on about it. I do appreciate your concern, though."
        $ fian = "smile"
        i "It's alright..."
    "I finished my coffee and put the rest of my clothes back on."
    hide ianunder
    show ian at lef
    with short
    $ fian = "smile"
    $ fcherry = "smile"
    i "Well... I should get going. Thanks for the breakfast and everything."
    if v11_cherrytalk3:
        $ v12_cherry_painting = True
        $ fcherry = "blush"
        ch "Oh, wait. Before you go... I have something for you."
        i "Something for me?"
        ch "Yeah..."
        show cherrybra at rig3
        show ian at lef3 
        with move
        show v12_cherry_painting behind ian, cherrybra with long
        $ fian = "happy"
        ch "Here... You asked me to do a painting for you..."
        if v11_cherry_art > 0:
            ch "I tried to come up with something I thought you'd like, but I wasn't sure..."
            i "It's great! Especially considering you did it for me..."
        else:
            $ fcherry = "n"
            ch "Hopefully, you like this one better than my other ones."
            $ fian = "smile"
            i "Yeah, it's pretty cool... Especially considering you did it for me."
        $ fian = "smile"
        i "How much do I owe you?"
        $ fcherry = "smile"
        ch "Nothing... It's a gift."
        $ fian = "n"
        i "But I told you I wanted to buy it... I want to support your art."
        $ fcherry = "flirt"
        ch "You support me in other ways... You give me inspiration, and orgasms!"
        $ fian = "happy"
        i "If that's the case, then... I'll accept it. Thank you so much, Cherry."
        if ian_charisma < 10:
            call xp_up ('charisma') from _call_xp_up_951
        $ fcherry = "smile"
        ch "I enjoyed painting it..."
    else:
        ch "Of course..."
    $ fian = "smile"
    if ian_cherry_love:
        i "I'll miss you this summer."
        $ fcherry = "smile"
        ch "Me too... But I'll be back before you know it. Let's do something together then."
        i "I can't wait. I'll keep in touch meanwhile."
        ch "Please, do... Take care, handsome."
        i "You too."
        stop music fadeout 2.0
        scene cherryhome with long
        "I kissed Cherry goodbye and left for the office."
    else:
        i "I guess I'll see you when you get back from your trip."
        ch "Sure. I hope to hear from you before that, though."
        i "Of course. Let's keep in touch."
        ch "Take care, handsome."
        stop music fadeout 2.0
        scene cherryhome with long

    scene street with long
    pause 0.5

# MAGAZINE WORK ######################################################################################################################################################
label v12office:
    scene magazine with long
    play music "music/normal_day5.mp3" loop
    $ ian_look = 2
    if ian_cherry_dating:
        show ian with short
        "Cherry's aroma and the recent memories of her body lingered within me when I sat at my desk."
        "I thought back to the night I first met her. I had to thank Alison for that."
        $ fian = "n"
        if ian_holly_dating:
            "Her tense relationship with Lena troubled me, but there wasn't much I could do about it..."
            "Too bad she couldn't join our vacation, but I was grateful for that too..."
            "What would've happened if she and Holly tagged along? The only possible outcome of that situation would've been disaster."
            if ian_cherry_love:
                "I felt my relationship with Cherry was taking a turn, but I was also dating Holly..."
                if ian_holly_love:
                    "I couldn't deny how special she was to me, and I didn't want to. But then, what didn't I feel the same for Cherry?"
                    "As much as I wanted to resist, I knew I couldn't have everything. Whatever I chose, I would be losing something I valued..."
                    "And also hurting someone I cared about."
                else:
                    "I wasn't sure where that was leading, if anywhere, but what about Holly? I had the feeling we had different expectations..."
                    "Did that mean I would need to break things off with Holly? I knew doing that would mean hurting her."
            else:
                if ian_holly_love:
                    "I felt my relationship with Holly was taking a turn, and that wasn't compatible with me continuing to date Cherry casually."
                    "I would need to address that fact, and sooner than later..."
                else:
                    "I wasn't into a committed relationship with any of them, but I hadn't told them about me dating both simultaneously."
                    "I told myself I wasn't doing anything wrong, but that didn't mean I could be careless. And I had dodged a bullet right there..."
        else:
            "I wished she could come on vacation with me, but that was impossible from the get-go."
            "Her tense relationship with Lena troubled me, but there wasn't much I could do about it..."
        play sound "sfx/keyboard.mp3"
        if v5_ian_showup:
            $ fian = "smile"
            "I sat at my desk and began putting the finishing touches on another literary analysis for the publishing company."
        else:
            "Like every other morning, I sat at my desk, looking at the computer screen with a vacant expression."
            "Since today was the last day of my internship, my motivation was nowhere to be found. What was the point?"
    else:
        if v5_ian_showup:
            $ fian = "smile"
            play sound "sfx/keyboard.mp3"
            show ian with short
            "I sat at my desk that morning, putting the finishing touches on another literary analysis for the publishing company."
        else:
            $ fian = "n"
            play sound "sfx/keyboard.mp3"
            show ian with short
            "I sat at my desk that morning, looking at the computer screen with a vacant expression."
            "Today was the last day of my internship, and my motivation was nowhere to be found. What was the point?"
    # common
    if ian_minerva_sex:
        $ minerva_look = "hot"
    else:
        $ minerva_look = 1
    if v5_ian_showup:
        "I had been striving to land that rumored promotion, and my hopes were up. My conversation with Seymour Ward kept replaying in my mind..."
        $ fian = "n"
        "Had I really managed to capture his attention...?"
        "That would help me enormously, not only to climb the corporate ladder, but to make my dream come true: become a published author once and for all."
        "The literary competition had me on tenterhooks..."
        "With the manuscript already in, all that was left was to wait for the jury's decision, which would be announced in September."
        show ian at lef with move
        "I got up for a coffee break."
        if ian_minerva_dating:
            $ fminerva = "n"
            show minerva at rig with short
            "I crossed paths with Minerva in front of the coffee machine. She gave me a look and I looked back."
            if ian_holly_dating:
                mi "..."
                i "..."
                "I could see Minerva was thinking about our date, and so was I: we shared a night of passion in that hotel room..."  
                "But we both agreed that would be our last time, for Holly's sake. I had been pushing my luck thus far, and I couldn't keep risking it."
                hide minerva with short
                if ian_minerva_dating == 3:
                    i "It's a pity... But it's better this way. I'll treasure those memories, but Holly is much more important to me."
                else:
                    i "It's better this way. Minerva was just a fun distraction, nothing more."   
                    i "Holly's way more important to me."
                $ ian_minerva_dating = False
                $ ian_minerva_over = True
            elif ian_minerva_dating == 3:
                mi "Good morning..."
                $ fian = "smile"
                i "Hey... Looking nice this morning."
                mi "You too..."
                "Minerva looked around after returning my compliment. It was clear she was worried about someone seeing us getting along during office hours."
                "I played it cool, waiting for the machine to finish serving my coffee."
                mi "You're starting your vacation tomorrow, aren't you?"
                i "Yes... I didn't know you were keeping track of your ex-employees."
                mi "Only the ones I'm interested in... Besides, we still share the same office."
                i "For now, yeah."
                mi "I wouldn't mind losing sight of you a bit, to be honest..."
                $ fian = "confident"
                i "You're not being serious."
                mi "Who knows..."
                hide minerva with short
                "We were keeping up appearances during office hours, but it was hard not to think back to our last night together in that hotel room..."
                "And I knew for a fact that Minerva had the same thoughts in her mind."
                $ fian = "n"
                i "She's a member of the jury in this year's contest... I wonder if I could ask her for some {i}help{/i}..."
            else:
                mi "..."
                hide minerva with short
                i "..."
                "We were keeping up appearances during office hours, but it was hard not to think back to our last night together in that hotel room..."
                $ fian = "confident"
                "And, despite her resting bitch face, I knew Minerva had the same thoughts in her mind."
                $ fian = "n"
                i "She's a member of the jury in this year's contest... I wonder if I could ask her for some {i}help{/i}..."
        else:
            $ fminerva = "mad"
            show minerva at rig with short
            $ fian = "serious"
            "I crossed paths with Minerva in front of the coffee machine. She gave me a sour look and I looked back."
            i "What's the matter? There's something you wanna say?"
            mi "You and I have nothing left to discuss."
            i "I thought so."
            hide minerva with short
            if ian_minerva_over:
                "Minerva had served her purpose... It was fun to put her in her place and redeem all the frustration she had made me feel."
                "I was moving on, leaving her trapped in her unsatisfying life and her unfulfilling marriage."
            else:
                i "Hopefully, I lose sight of her soon..."
            $ fian = "n"
            i "She's a member of the jury in this year's contest, though... I hope she doesn't try to fuck me over somehow..."
    else:
        "I was still waiting for a response from Hierofant, but I was hoping they'd hire me when I got back from vacation."
        "Otherwise, I wasn't quite sure what I'd do..."
        "Yet my main concerns were tied to the literary competition."
        "With the manuscript already in, all that was left was to wait for the jury's decision, which would be announced in September."
        "My conversation with Seymour Ward kept replaying in my mind... Had I really managed to capture his attention?"
        show ian at lef with move
        "I got up for a coffee break."
        $ fminerva = "mad"
        show minerva at rig with short
        mi "Ian, how's the magazine formatting coming along? I'll need the last section before you wrap up for the day."
        i "It's almost done... I'll send the documents in a couple of hours."
        mi "Hurry up, don't dawdle."
        hide minerva with short
        $ fian = "serious"
        i "I'm actually happy my internship is over today. I can't wait to lose sight of her..."
        $ fian = "n"
        i "She's a member of the jury in this year's contest, though... I hope she doesn't try to fuck me over somehow..."
# holly
    # holly dating
    if ian_holly_dating:
        # love
        if ian_holly_love == 2 and lena_go_holly < 5:
            $ fholly = "happy"
            show holly at rig with short
            h "Good morning!"
            $ fian = "smile"
            i "Hey, good morning Holly... You look happy today. Did something good happen?"
            $ fholly = "smile"
            hide holly
            show holly2 at rig
            with short
            h "Something good? Hm... Not really..."
            $ fholly = "happy"
            h "But I'm excited for tomorrow! I've been looking forward to the beach trip with you and the guys..."
            i "Me too. It'll be the first time you and I get to spend a few days together."
            $ fholly = "happyshy"
            hide holly2
            show holly3 at rig
            with short
            h "Yeah..."
            if ian_cherry_dating:
                $ fian = "sad"
                "I felt a sting of guilt as I said those words. What would she think if she knew I spent the night at Cherry's place?"
                if lena_go_holly == 3:
                    "I pushed those thoughts to the back of my head and focused on something more positive."
                    $ fian = "smile"
                    "I couldn't detect the awkwardness Holly had shown me during last week..."
                    "She seemed to have gotten over what was troubling her, whatever it was."
                else:
                    "I pushed those thoughts to the back of my head."
                    $ fian = "smile"
            elif lena_go_holly == 3:
                "I couldn't detect the awkwardness Holly had shown me during last week..."
                $ fian = "smile"
                "She seemed to have gotten over what was troubling her, whatever it was."
            i "I'm glad to see you this cheerful. You're so cute when you're happy."
            h "Stop it, I'm blushing so much I'll be mistaken for a traffic light!"
            $ fian = "happy"
            i "That makes you even more adorable."
            $ fian = "smile"
            $ fholly = "smile"
            hide holly3
            show holly at rig
            with short
            i "So, about tomorrow... We'll meet Wade and Emma at the train station so we can all go together."
        else:
            $ fholly = "n"
            show holly2 at rig with short
            h "Good morning..."
            $ fian = "smile"
            i "Good morning, Holly..."
            $ fian = "n"
            hide holly2
            show holly3 at rig 
            with short
            "I noticed Holly's awkwardness. She had been acting like that around me during the last few days..."
            if ian_holly_love: # if lena_go_holly == 5
                "I thought things between us were going great so far, but she made me wonder if there actually was something wrong..."
                "I noticed it for the first time during our last date, at my place."
                "Was she maybe worried about Gillian or was it something else...?"
                if ian_cherry_dating:
                    $ fian = "sad"
                    "I couldn't help but feel the sting of guilt. What would she think if she knew I spent the night at Cherry's place?"
                    "Could she have noticed something was up...?"
                hide holly3
                show holly at rig
                with short
                h "So, um... What's the plan for tomorrow?"
                $ fian = "smile"
                i "Oh, yeah. We'll meet Wade and Emma at the train station so we can all go together."
            else:
                if v11_holly_sex == False:
                    "I had apologized for shutting her off the other day at my place, but the damage was done."
                    "No wonder she had been more closed off with me... It would take a bit for things to go back to normal."
                    $ fian = "smile"
                    i "So, um... Are you ready for tomorrow? We'll meet Wade and Emma at the train station so we can all go together."
                    hide holly3
                    show holly at rig
                    with short
                else:
                    if lena_go_holly == 5:
                        "I wasn't sure what the reason was... I thought our last date went well."
                        "However, I was a bit surprised when Holly told me she wasn't looking to get into a serious relationship..."
                        if ian_wits > 6:
                            "It was like something changed, but what could it be? Was there someone else, maybe...?"
                        else:
                            "I assumed that was what she wanted, judging by the way she acted, but maybe I had been mistaken all along. "
                    else:
                        "I understood why... I failed to open up to her about my feelings for Gillian."
                        if v3_gillian_stop:
                            "I thought I had turned the page, but I didn't feel ready to get into another relationship. And that made me keep my distance..."
                        else:
                            "To be honest, I wasn't sure of what I felt myself... How was I supposed to explain it to someone?"
                        if ian_cherry_dating:
                            $ fian = "sad"
                            "I couldn't help but feel the sting of guilt. I was keeping things from her, including my affair with Cherry..."
                            "Could she have noticed something was up...?"
                    hide holly3
                    show holly at rig
                    with short
                    h "So, um... What's the plan for tomorrow?"
                    $ fian = "smile"
                    i "Oh, yeah. We'll meet Wade and Emma at the train station so we can all go together."
        
    # holly single
    else:
        # sexy holly
        if v11_holly_change:
            # clark
            if holly_clark:
                $ fholly = "happy"
                show ian at lef3 with move
                play sound "sfx/giggle.mp3"
                show clark
                show holly at rig3
                with short
                "Holly's laughter made me turn around. She and Clark seemed to be joking with each other as they walked towards the coffee machine."
                cl "Seriously, you look so much better with your hair loose! Why did you ever tie it up?"
                h "I don't know, ha ha..."
                $ fholly = "n"
                cl "Oh, hey there..."
                i "Hey. What's up?"
                cl "I need to get back to work... See you at lunch, Holly."
                hide clark with short
                show ian at lef
                show holly at rig
                with move
            # no clark
            else:
                $ fholly = "n"
                if holly_ivy:
                    show holly at rig
                else:
                    show holly at rig
                with short
                h "Good morning..."
                $ fian = "smile"
                i "Oh, hi there Holly."
            if holly_ivy:
                $ fian = "n"
                "It was impossible not to notice how much Holly had changed in the last few weeks. She came across as being way more lively and confident."
                "And sexy too..."
                "Looking at her now, it was hard to believe she had once been the quiet nerdy girl I had met when I first set foot in this office."
            else:
                "It was difficult not to notice how much Holly had changed in a few months. She looked way more lively and confident."
            if ian_holly_sex:
                "It was weird thinking back to that night at the hotel, when we had our one-night stand... Would it be different if we slept together again now?"
                if ian_lena_dating:
                    "I pushed that thought away. It had already caused trouble between me and Lena once. I knew I couldn't repeat that mistake."
            elif v7_holly_kiss:
                "It was weird thinking back to that night at the hotel, when we shared a kiss... That was as far as I wanted to take things, though."
                "I wondered if I would make the same choice if it were to happen now..."
                if ian_lena_dating:
                    "I pushed that thought away. It had already caused trouble between me and Lena once. I knew I couldn't repeat that mistake."
            h "So, about tomorrow... Any clue about the plan?"
            $ fian = "smile"
            i "Tomorrow, yeah... We'll meet Wade and Emma at the train station so we can all go together."
        # normal holly
        else:
            $ fholly = "n"
            show holly at rig with short
            h "Good morning..."
            $ fian = "smile"
            i "Oh, hi there. Ready for tomorrow?"
            $ fholly = "happy"
            h "Yes! Any clue about the plan?"
            i "We'll meet Wade and Emma at the train station so we can all go together."
    # common holly
    if ian_holly_love and lena_go_holly < 5:
        h "Alright... Again, thank you for inviting me!"
        i "I wouldn't have it otherwise. Besides, my friends really like you. I didn't even need to ask them to invite you."
    else:
        $ fholly = "happy"
        h "Cool... Thank you guys for inviting me. I've been looking forward to this trip."
        if ian_holly_dating:
            i "Me too. It'll be the first time you and I get to spend a few days together."
            $ fholly = "happyshy"
            h "Yeah..."
            i "And my friends really like you. I didn't even need to ask them to invite you."
        else:
            i "The more, the merrier... They like you a lot, so it was easy to invite you."
    h "I like them too... They're very nice and easygoing!"
    i "Wait until you get to know them better..."
    if v11_perry_invite < 3:
        $ fian = "n"
        i "Speaking of which... any word on Lena? Will she join us after all?"
        $ fholly = "n"
        if v11_perry_invite == 2:
            h "Yes, she will! But she'll join us a bit later. She has to visit her parents this weekend..."
        elif v11_perry_invite == 1:
            h "I think she will! But she'll have to join us a bit later. She has to visit her parents this weekend..."
        else:
            h "She wasn't sure about it, since she has to visit her parents this weekend..."
            h "But I told her she could join us later, since we'll be spending a few days there, and I think that convinced her."
        $ fian = "smile"
        i "Good..."
        if v11_lena_breakup or v10_ian_left:
            $ fholly = "sad"
            h "Do you think you'll be comfortable around each other? I mean..."
            $ fian = "n"
            if ian_lena_couple_over:
                h "You two broke up very recently..."
            else:
                h "I know you decided to stop seeing each other..."
            if v11_lena_breakup:
                i "Well, that was mostly my choice, and yeah... I don't expect things to be exactly comfortable."
                i "But if Lena agreed to come after all, I suppose she's willing to try and be friends. That's what I'd like too."
            else:
                i "We already talked about that, she and I, and well... I don't expect things to be exactly comfortable, but we want to try and remain friends."
            $ fholly = "n"
            h "I hope you two manage to do that."
        elif ian_lena_breakup:
            $ fholly = "sad"
            h "Do you think you'll be comfortable around each other? I mean..."
            $ fian = "n"
            h "I suppose it's been a while since you two decided to stop dating. How have you been dealing with it?"
            if v10_wc_bj == "ian":
                "That wasn't entirely true, though."
                "I thought back to the blowjob Lena gave me at the nightclub... We hadn't spoken about it since, and I wasn't sure if we should."
            elif v10_lena_dance:
                "I thought back to the night of Ivy's birthday party, and how Lena and I danced together. It felt rather intimate, but nothing came of it..."
            i "It's alright... Things have been rather awkward, and I guess they still are in some ways."
            i "But we went over it already, and well... Things haven't been exactly comfortable, but we want to try and remain friends."
            h "I believe you can manage to do that."
        elif ian_lena_over:
            h "Things between you are back to normal, right?"
            i "Yeah, I would say so..."
            if v10_wc_bj == "ian":
                $ fian = "n"
                "That wasn't entirely true, though."
                "I thought back to the blowjob Lena gave me at the nightclub... We hadn't spoken about it since, and I wasn't sure if we should."
            elif v10_lena_dance:
                "I thought back to the night of Ivy's birthday party, and how Lena and I danced together. It felt rather intimate, but nothing came of it..."
    $ fian = "smile"
    i "Well, coffee break is over for me. Time to get back to work."
    $ fholly = "smile"
    h "Talk to you later..."
    hide holly
    with short
    $ fian = "n"
# JOB
    if v5_ian_showup:
        $ fnat = "n"
        show ian at lef3 with move
        play sound "sfx/door.mp3"
        show clark at rig3
        show nat 
        with short
        nat "There you are!"
        if v10natflirt == 2:
            $ fian = "smile"
            i "Oh, hey Nat... Nice to see you. What do we owe the pleasure to?"
            $ fnat = "smile"
            nat "Routine check how everything's going around the office."
            i "Well, I think you'll find everything's going smoothly. I believe I've been doing good work..."
        else:
            i "Oh, Nat... When did you get here?"
            nat "Just now. How's everything going around the office?"
            $ fian = "smile"
            i "Great... I believe I've been doing good work."
        if v11_book_review2 == 1 and v11_book_review4 == 1 and v11_book_review1 == 0 and v11_book_review3 == 0:
            $ fnat = "smile"
            nat "So I've heard! Your reviews have been very helpful... The department is pleased with you guys!"
            i "That's good to hear."
        elif v11_book_review2 == 1 and v11_book_review4 == 1:
            $ fnat = "n"
            nat "I'm glad that's the case! I've heard the department is pleased with you guys!"
            cl "Good."
        else:
            $ fnat = "n"
            nat "Is that so? That's good... I've also heard praise for Clark's reviews. The department is pleased with you guys!"
            $ fian = "n"
            cl "That's good to hear."
        nat "I'm also bringing some news: Hierofant is expanding the magazine, and we need to make space for the new interns starting in September."
        nat "That's why the department has decided to move you guys to our main offices at the end of this month."
        cl "Excellent."
        nat "I think that's all on my part... If you need anything, you know how to reach me."
        nat "Best of luck, team!"
        hide nat with short
        cl "So they'll be moving us to the main offices... Looks like that promotion is getting closer."
        if v11_book_review2 == 1 and v11_book_review4 == 1 and v11_book_review1 == 0 and v11_book_review3 == 0:
            cl "And it seems like you've been doing good for yourself, huh?"
            i "Just trying to do the job I was hired for."
            cl "Sure... Aren't we all?"
        elif v11_book_review2 == 1 and v11_book_review4 == 1:
            cl "I guess we'll see who gets it, huh?"
            i "I guess. It's not up to us to make the choice."
            cl "But it's up to us to be good enough to deserve it."
        else:
            $ fian = "n"
            cl "I have a good feeling about it."
            i "We'll see..."
        hide clark with short
        if ian_cindy_dating == False:
            show ian at truecenter with move
    else:
        if ian_cindy_dating == False:
            show ian at truecenter with move
        i "Let's finish this damn layout for Minerva... This'll be the last thing I do for her."
    
# cindy sms
    if ian_cindy_dating:
        play sound "sfx/sms.mp3"
        $ fian = "smile"
        i "Cindy just texted me..."
        show ian at left with move
        show v12_cindy_selfie1 at right with short
        $ ian_cindy_pics.append("v12_cindy_selfie1.webp")
        $ fian = "confident"
        i "Damn! She's beautiful like an angel... A very sexy one."
        nvl clear 
        c_p "What do you think about this bikini? Does it suit me?"
        menu:
            "{image=icon_charisma.webp}/{image=icon_lust.webp}Show me more" if ian_charisma > 6 or ian_lust > 6:
                $ renpy.block_rollback()
                $ v6_cindy_pg = 4
                i_p "I don't have enough info to give you a good opinion... I need to see more {image=emoji_devil.webp}"
                c_p "Of course. Here you go {image=emoji_flirt.webp}"
                play sound "sfx/sms.mp3"
                hide v12_cindy_selfie1 
                show v12_cindy_selfie2 at right 
                with short
                $ ian_cindy_pics.append("v12_cindy_selfie2.webp")
                i "Good gracious... She's just perfect."
                i_p "Is it me or are you getting hotter? {image=emoji_fire.webp}"
                c_p "I've started going to the gym three days a week! Can you tell the difference?"
                i_p "All I can tell is that I'm crazy about you. You're just perfect."
                c_p "Good! {image=emoji_flirt.webp} I'm trying to pick a bikini that will appeal to the modeling agency..."
                $ fian = "n"
                c_p "I want to make sure I look stylish during the photo sessions!"
        
            "It's perfect":
                $ renpy.block_rollback()
                $ v6_cindy_pg = 3
                $ fian = "smile"
                i_p "It looks perfect on you. I can't take my eyes off the picture you just sent me."
                if ian_charisma < 7:
                    call xp_up ('charisma') from _call_xp_up_952
                c_p "Good! I'm out shopping, but I'm not sure which bikini is the most appropriate. Do you think this one will appeal to the modeling agency?"
                $ fian = "n"
                c_p "I want to make sure I look stylish during the photo sessions..."
        
            "It's a bit too sexy...":
                $ renpy.block_rollback()
                $ v6_cindy_pg = 1
                $ fian = "n"
                i_p "Isn't it a bit too daring?"
                c_p "You think so? {image=emoji_disgust.webp}"
                c_p "I thought this one was appropriate... I should get something that will appeal to the modeling agency."
                c_p "I want to make sure I look stylish during the photo sessions..."
        
            "Everything looks good on you":
                $ renpy.block_rollback()
                $ v6_cindy_pg = 2
                i_p "Everything looks good on you, so yeah."
                c_p "Are you sure? I'm out shopping, but I'm not sure which bikini is the most appropriate. Do you think this one will appeal to the modeling agency?"
                $ fian = "n"
                c_p "I want to make sure I look stylish during the photo sessions... {image=emoji_disgust.webp}"
        
        hide v12_cindy_selfie1
        hide v12_cindy_selfie2
        with short
        show ian at truecenter with move
        i "That's true... She's leaving for a week to work on fashion shoots on a bunch of beautiful islands or something."
        i "She's been very excited about it. Apparently, she's been picked up by a pretty important modeling agency..."
        if ian_cindy_love:
            "I was happy for her, and I certainly wasn't going to moan about having a model girlfriend... But what bugged me was the guy who had brought her into this."
        else:
            "It was cool that Cindy got this opportunity, but what bugged me was the guy who had brought her into this."
        "Axel was going to accompany her throughout the trip, and this time I wouldn't be there to keep an eye on the situation."
        $ fian = "serious"
        i "I know that guy can't be trusted... It's obvious he's been trying to hook up with her."
        if ian_cindy_love:
            i "I really don't like the idea of them being together for so long... Especially when our situation is still undefined."
            $ fian = "sad"
            i "As far as I know, she hasn't broken up with Wade yet..."
            "I hated feeling insecure, but there were simply too many concerning signs to ignore."
            $ fian = "n"
        else:
            i "It's not like Cindy owes me loyalty or anything, but... I really don't like the idea of them being together for so long."
            "Sharing her with Wade was one thing, but Axel... I didn't want to admit it, but I was concerned about the possibility of losing her."
        nvl clear
        play sound "sfx/sms.mp3"
        c_p "Anyway, are you free this afternoon?"
        $ fian = "smile"
        if ian_cindy_love:
            i_p "For you, always."
            c_p "Wanna drop by my place later? {image=emoji_heart.webp}"
            i_p "You already know the answer to that {image=emoji_kiss.webp}"
            c_p "I'll see you later then {image=emoji_smile.webp}"
            if ian_wits > 6:
                i "Alright... Things are going well. I shouldn't let intrusive thoughts get in my way."
            elif ian_chad > 3:
                i "Alright... Things are going well. I can't afford to act like a pussy."
            else:
                i "Alright... Things are going well. I shouldn't fret so much."
        else:
            i_p "For you, yes."
            c_p "Wanna drop by my place later? {image=emoji_flirt.webp}"
            $ fian = "confident"
            i_p "You already know the answer to that {image=emoji_devil.webp}"
            c_p "Cool! I'll see you later then!"
            if ian_chad > 3:
                i "Alright... She still wants to see me. I can't afford to act like a pussy."
            elif ian_wits > 6:
                i "Alright... She still wants to see me. I shouldn't let intrusive thoughts get in my way."
            else:
                i "Alright... She still wants to see me. I shouldn't fret so much."
            
    scene magazine with long
    play sound "sfx/keyboard.mp3"
    if v5_ian_showup:
        "Knowing I wouldn't be coming back to this office made me feel excited about the future. I was finally starting to climb the ladder..."
        "I focused back on work, eager to finish my workday and kick off my vacation."
    else:
        "I focused back on work, eager to finish my workday and kick off my vacation. I couldn't wait to leave this office behind..."
    if tournament:
        "However, I had the tournament to attend first... I was both eager and nervous at the same time."

# alison sms
    if ian_alison_dating or ian_alison_fuck:
        $ fian = "n"
        play sound "sfx/sms.mp3"
        show ian with short
        nvl clear
        if ian_alison_dating:
            $ fian = "smile"
            i "Alison just texted me..."
            a_p "Hi, handsome {image=emoji_kiss.webp}"
            a_p "Today I'm off work at five, for the first time this week. Wanna meet?"
            a_p "My parents left today for a small vacation, so you could drop by this afternoon."
            $ config.menu_include_disabled = False
            $ greyed_out_disabled = True
            if ian_cindy_dating:
                $ fian = "n"
                jump v12textalisonreject
            else:
                menu:
                    "Visit Alison":
                        $ renpy.block_rollback()
                        if persistent.include_disabled:
                            $ config.menu_include_disabled = True
                        $ greyed_out_disabled = False
                        $ v12_alison_sex = True
                        i_p "Of course. I'll see you later {image=emoji_wink.webp}"
                        a_p "{image=emoji_lips.webp} {image=emoji_lips.webp} {image=emoji_lips.webp}"
                        if ian_alison_love:
                            i "Great... I haven't seen Alison all week. I was starting to miss her."
                        elif ian_alison_dom:
                            $ fian = "confident"
                            i "Great... I haven't seen Alison all week. I'll be fucking those udders this afternoon."
                        else:
                            i "Great... I haven't seen Alison all week. It's about time to give her some attention."
                
                    "{image=icon_broken.webp} I'm busy" if ian_alison_love:
                        $ renpy.block_rollback()
                        $ fian = "n"
                        i "I don't know why, but I don't really feel like seeing her today..."
                        label v12textalisonreject:
                            if persistent.include_disabled:
                                $ config.menu_include_disabled = True
                            $ greyed_out_disabled = False
                        i_p "I can't this afternoon, I already have plans."
                        a_p "Really? When do I get to see you, then? {image=emoji_sad.webp}"
                        if ian_alison > 0:
                            call friend_xp ('alison',-1) from _call_friend_xp_1089
                        a_p "You're leaving tomorrow to spend a few days at Perry's beach house with the guys, right?"
                        a_p "I wish I could join, but I can't afford to, not when half of the staff is already on vacation {image=emoji_mad.webp}"
                        a_p "Honestly, sometimes I feel the only reason the company hasn't shut down is because of me."
                        if ian_alison_dom:
                            i "Her complaining goes on and on... Whatever."
                        i_p "Yeah, that sucks. I'll see you next week when I'm back."
                        i_p "Take care!"
                        if ian_alison_love:
                            $ ian_alison_love = False
                            i "At first I was so excited about Alison, but lately... Did I get bored?"
                            i "Does meeting Gillian have something to do with it...?"
                        elif ian_cindy_dating:
                            i "Sorry, Alison, but Cindy takes precedence... I'll give her attention some other day."
                        else:
                            i "I lied, but I didn't want to risk rustling her feathers. I'll give her attention some other day..."

                    "I'm busy" if ian_alison_love == False:
                        $ renpy.block_rollback()
                        $ fian = "n"
                        i "I'm not in the mood to meet Alison today..."
                        jump v12textalisonreject
        
        else:
            show ian at left with move
            show v12_alison_selfie1 with short
            $ fian = "confident"
            i "What's this...? Alison just sent me a nude."
            $ ian_alison_pics.append("v12_alison_selfie1.webp")
            nvl clear
            i_p "Someone's feeling horny right now?{image=emoji_flirt.webp}"
            a_p "Hopefully, that would be you right now {image=emoji_tongue.webp}"
            i_p "I like what I see, that much is true."
            a_p "Wanna see more? {image=emoji_crazy.webp}"
            if ian_chad > 3:
                i_p "Hell yeah, give me more."
            else:
                i_p "Of course, I want to."
            a_p "Why don't you drop by my place this afternoon?"
            a_p "Today I'm off work at five, for the first time this week. My parents left today for a small vacation, so..."
            hide v12_alison_selfie1 with short
            show ian at truecenter with move
            if ian_cindy_dating:
                $ fian = "n"
                jump v12textalisonreject2
            else:
                menu:
                    "{image=icon_lust.webp}Visit Alison":
                        $ renpy.block_rollback()
                        $ v12_alison_sex = True
                        i_p "I'll drop by a bit later then."
                        a_p "Great {image=emoji_love.webp}"
                        a_p "See you later, handsome!"
                        i "It's going to be a fun afternoon... Alison's become a really horny girl it seems!"
                        if ian_lena_couple:
                            $ fian = "n"
                            i "I'm putting my relationship with Lena at risk, though... I need to be careful."
                            i "Thankfully Alison's not coming to our little vacation. I'd like to keep Alison and Lena from running into each other as much as I can."
                        elif ian_holly_dating:
                            $ fian = "n"
                            i "I need to be careful with how I play this... I doubt Holly would appreciate knowing about my fling with Alison."
                            i "What would happen if they were around me at the same time? Thankfully Alison's not coming to our little vacation."

                    "I'm busy":
                        $ renpy.block_rollback()
                        $ fian = "n"
                        if ian_lena_couple:
                            i "I don't feel like meeting Alison today... I shouldn't have followed her to her place the other day, to begin with."
                            i "I'm putting my relationship with Lena at risk. Thankfully Alison's not coming to our little vacation."
                        elif ian_holly_dating:
                            i "I don't feel like meeting Alison today... I'm not sure following her to her place the other night was a good idea to begin with."
                            i "What would happen if she and Holly were around me at the same time? Thankfully Alison's not coming to our little vacation."
                        else:
                            i "I don't feel like meeting Alison today... I was feeling especially horny that last night, and maybe a bit drunk too..."
                        label v12textalisonreject2:
                            i_p "I can't this afternoon, I already have plans."
                            a_p "Really? That's too bad... Seems you've become Mr. Popular recently!"
                            if ian_alison > 0:
                                call friend_xp ('alison',-1) from _call_friend_xp_1090
                            i_p "I've got a bunch of stuff to wrap up before I leave for Perry's beach house tomorrow."
                            if ian_cindy_dating:
                                $ fian = "confident"
                                i "And I'm meeting Cindy already... Sorry, but she takes precedence."
                                $ fian = "n"
                            a_p "I wish I could join you and the guys, but I can't afford to, not when half of the staff is already on vacation {image=emoji_sad.webp}"
                            a_p "Sometimes I feel the only reason the company hasn't shut down is because of me."
                            i_p "That sounds like it sucks."
                            a_p "It does. So, when do I get to see you?"
                            i_p "Let's talk when I'm back next week. Take care, and thanks for the pic {image=emoji_flirt.webp}"
                            i "There... I'll give her some attention some other time."
        
        $ fian = "n"
    else:
        show ian with short
    pause 1.0
    i "Finally... Time to leave."
    $ fminerva = "n"
##MINERVA SEX
    label gallery_CH12_S02:
        if _in_replay:
            call setup_CH12_S02 from _call_setup_CH12_S02
    if ian_minerva_dating:
        play sound "sfx/door.mp3"
        show minerva at rig3 with short
        mi "Ian. Could you come to my office for a moment?"
        if ian_minerva_dating == 3:
            $ v12_minerva_sex = True
            $ fian = "smile"
            i "Sure..."
            mi "Thanks."
            hide minerva with short
            i "It's been a while since she called me to her office... I wonder what's this about."
        else:
            menu v12minervatalk:
                "Sure":
                    $ renpy.block_rollback()
                    label v12mvyes:
                        $ v12_minerva_sex = True
                    i "Sure, alright. Give me a minute."
                    $ fminerva = "n"
                    mi "Thanks."
                    if ian_minerva < 12:
                        call friend_xp ('minerva') from _call_friend_xp_1091

                "What for?":
                    $ renpy.block_rollback()
                    i "What for?"
                    $ fminerva = "mad"
                    mi "There's... something I need to discuss with you."
                    menu:
                        "Sure":
                            $ renpy.block_rollback()
                            jump v12mvyes
                        
                        "I'm in a hurry":
                            $ renpy.block_rollback()
                            jump v12mvhurry
            
                "I'm in a hurry":
                    $ renpy.block_rollback()
                    label v12mvhurry:
                        i "Sorry, I'm in a hurry today."
                    $ fminerva = "mad"
                    mi "Alright. Forget it, then."
                    hide minerva with short
                    if ian_minerva > 0:
                        call friend_xp ('minerva',-1) from _call_friend_xp_1092
                    i "I don't know what she wants, but I have other things on my mind right now."
                    jump v12minervaexit

            hide minerva with short
            i "What does she want now...?"
    
        hide ian with short
        pause 1
        $ fminerva = "n"
        play sound "sfx/door.mp3"
        show minerva at rig
        show ian at lef
        with short
        i "Here I am."
        mi "You've been informed about the news, right?" 
        i "The office relocation? Yes."
        mi "Seems our time sharing the workplace is coming to an end..."
        if ian_minerva_dating == 3:
            i "Are you sad about it?"
            $ fminerva = "serious"
            mi "Don't be ridiculous..."
            i "Come on, don't give me that. I thought we'd gotten past that. Can't you admit you'll miss me?"
            $ fminerva = "n"
            mi "I won't... provided we continue to meet up outside the office."
            $ fian = "confident"
            i "See? I already know you have a sweet spot for me. No need to keep hiding it."
            $ fminerva = "happy"
            mi "And I'm the only one?"
            $ fian = "happy"
            i "I guess that's the clearest admission I can elicit from you."
            stop music fadeout 2.0
            $ fminerva = "flirt"
            mi "You think? I believe I can offer you even more compelling proof..."
            play music "music/sex_perilous.mp3" loop
            scene v12_minerva1 #kiss neck
            with long
            pause 1
            "A pleasant shiver ran down my spine when Minerva placed her lips on my neck."
            "I felt her hand cupping my dick over my pants, which was getting harder with every kiss she gave me."
            "She squeezed it as her tongue traced lewd paths around my ear."
            mi "Seems like I'm getting a pretty good confession from you too..."
            i "I can't hide it: you really manage to drive me crazy..."
            mi "Oh yeah? I'd love for you to show me."
            scene v12_minerva2 # kiss tease
            with long
            pause 1
            i "Be careful what you wish for..."
            "Her elegant perfume, her deep voice, her confident attitude..."
            "All of those contributed to her mature appeal, which never failed to get me all fired up."
            mi "Nhhh...! I can't believe I fell for someone like you..."
            i "You mean someone who actually gets you? Who knows the kind of woman you are, and the kinky desires you have..."
            i "Someone who makes you this wet; someone who can make you feel things you haven't felt in years..."
            "I could feel Minerva's body melting like butter beneath my hands as I teased and fingered her."
            mi "This is awful... And the worst part is that you're completely right about me."
        else:
            if ian_minerva_dating == 2:
                $ fian = "confident"
                i "Don't tell me you're gonna miss me now..."
                $ fminerva = "mad"
            elif ian_minerva_dating == 1:
                i "Is that all you wanted to tell me?"
                $ fminerva = "mad" 
                mi "Ugh. I don't know why I even bother."
                $ fian = "confident"
                i "What's the matter? Don't tell me you're gonna miss me now..."  
            mi "Don't be ridiculous..."
            i "Then what's this? Aren't you gonna give me a memorable send-off?"  
            $ fminerva = "n"
            mi "You'd like that, don't you?"  
            i "Maybe. But I thought we agreed it was too risky to keep doing it at the office..."
            stop music fadeout 2.0
            i "Though today is the last chance we'll ever have, it seems."
            play music "music/sex_perilous.mp3" loop
            $ fminerva = "flirt"
            mi "Indeed... So, what are you going to do about it?"
            scene v12_minerva2 # kiss tease
            with long
            pause 1
            i "Be careful... That cocky attitude can get you into trouble."
            mi "Said the pot to the kettle. You're the most impertinent and conceited employee I ever had..."
            i "And that's exactly why you can't stop wanting my impertinent cock inside of you..."
            i "Inside of your mean, petty, cheating pussy... You love the way I make you feel."
            mi "Ugh, how am I supposed not to hate you? You're insufferable..."
            i "Say what you will, but your drenched pussy is telling me otherwise."
        menu:
            "{image=icon_love.webp}Eat her out" if ian_minerva_dating > 2:
                $ renpy.block_rollback()
                scene v12_minerva3 with long
                pause 1
                i "You deserve a reward for being honest."
                "I made Minerva bend over the table as I pulled her panties down, gluing my mouth to her burning slit."
                "My tongue got drenched in her tangy flavor, swirling swiftly around her sensitive areas."
                "Minerva moaned and raised her leg over the table, granting me easier access to her pussy."
                mi "God, yes...! Devour my pussy... It's all yours!"
                "My hands clasped her hips as I focused on her swollen clit, sensing Minerva shake and tremble."
                "Giving her pleasure really turned me on..."
                if ian_lust < 10:
                    call xp_up ('lust') from _call_xp_up_953
                menu:
                    "{image=icon_lust.webp}Make her suck you off" if ian_lust > 6:
                        $ renpy.block_rollback()
                        jump v12minervabj
                    
                    "Fuck Minerva":
                        $ renpy.block_rollback()
                        i "Well now... I believe it's time to give you what you've been really wanting..."

            "{image=icon_lust.webp}Make her suck you off" if ian_lust > 6:
                $ renpy.block_rollback()
                label v12minervabj:
                    scene v12_minerva4 with long
                    pause 1
                "I took control of Minerva's body grabbing her by the hair and made her kneel before me."
                i "Now's your turn to make my cock drenched."
                play sound "sfx/bj6.mp3"
                mi "{i}\*Mphff...!\*{/i}"
                if ian_minerva_dating == 3:
                    "Minerva obeyed my desires with a newfound docility, allowing me to use her mouth as I pleased."
                    "I pushed her head and shoved my cock deeper into her throat, fucking her face slowly but mercilessly."
                    i "That's it, take as much of it as you can... Can you feel how hard you make me?"
                    i "I dreamed about putting you in your place, and now I can't get enough of it. And I can see the same goes for you..."
                else:
                    "I could feel her resistance as I imposed my desires on her, but she still obeyed willingly."
                    i "It turns you on, right? You get so fucking excited when I boss you around."
                    i "You tried to do it to me, but what you really wanted was for someone to put you in your place."
                    i "Well, congratulations. You got what you wanted."
                    "Minerva glared at me, but I pushed her head and shoved my cock deeper into her throat."
                i "Well now... I believe it's time to give you what you really want."
                
            "Fuck Minerva":
                $ renpy.block_rollback()
                i "Let's give you what you've been wanting..."
        
        scene v12_minerva5a with long
        pause 1
        mi "Yes, fuck me..."
        play sound "sfx/oh2.mp3"
        scene v12_minerva5b with vpunch 
        mi "Ahhh...! Yes...!"
        "My fingers sank into Minerva's tender flesh as I penetrated her."
        i "Keep your voice down..."
        scene v12_minerva5_animation with fps
        pause 4
        if ian_athletics < 10:
            call xp_up ('athletics') from _call_xp_up_954
        "Each thrust made me shudder, feeling Minerva's pussy enveloping my hard cock with a wet and warm embrace."  
        "This woman was capable of pushing me to the edge, awakening intense emotions in me."
        if ian_minerva_dating == 3:
            "As I drove my manhood into her, I felt passion swelling inside of me. Our chemistry was insane..."
            "I wanted to hold her, kiss her, inject all of my seed deep inside her, filling her uterus with my being..."
            "I wanted to claim this woman as my own. And I could tell the same emotions overflowed from her as well."
        elif ian_minerva_dating == 2:
            "As I drove my cock into her, I felt passion swelling inside of me. And I could clearly tell she was feeling the same."
            "We said we would stop doing it in the office, but we were both slaves to the morbid desire we felt for each other."
            "We had found the perfect outlet for our lust and frustrations in each other, coming together in pleasant violence."
        elif ian_minerva_dating == 1:
            "It was hard for me to control myself. I wanted to smash her, release all my frustration, fuck her until I subdued her..."
            "The only reason I was holding back was the danger of being discovered."
            "We said we would stop doing it in the office, but we were both slaves to the morbid desire we felt for each other."
        mi "God, you're gonna make me cum...!"
        i "I can tell... Your pussy is squeezing me down..."
        mi "Yes, yes...! I want it... I want your cock...!"
        play sound "sfx/oh4.mp3"
        scene v12_minerva5b with vpunch
        mi "Mhhhh!!!{w=1}{nw}" with flash
        pause 0.6
        with vpunch
        pause 0.6
        with vpunch 
        pause 1
        "We came one after the other, our pleasures coordinated almost perfectly."
        if ian_minerva_dating == 3:
            "My brain was invaded by a sweet and pleasant sensation, intoxicated by Minerva's body."
        elif ian_minerva_dating == 2:
            "The satisfaction I felt each time Minerva and I had sex was hard to deny."
        elif ian_minerva_dating == 1:
            "The satisfaction I felt each time I had my way with Minerva was impossible to deny."
        # end minerva sex
        stop music fadeout 2.0
        scene magazine with long
        $ fian = "n"
        $ fminerva = "n"
        "We fixed our clothing after the passionate encounter."
        show ian at lef
        show minerva at rig
        with short
        $ renpy.end_replay()
        $ gallery_unlock_scene("CH12_S02")
        "Thankfully, no one had walked in on us, but I couldn't help but wonder if anyone had caught wind of our secret..."
        "For better or worse, we'd be far from this temptation once I got transferred to a new office."
        # contest cheating
        if ian_minerva > 7:
            i "Well, time for me to get going..."
            $ fminerva = "n"
            mi "Wait, before you leave..."
            i "Yeah?"
            mi "You entered the literary contest. Do you believe in your chances?"
            if book_points == 8:
                i "I do... There's no way for me to be certain, but the reviews I got on the manuscript were pretty good."
                mi "I see... In that case, I guess you don't need any kind of... help."
            elif book_points > 5:
                i "I have to. That's why I'm participating... And I'm confident in my work being pretty good."
                mi "I see. Sounds like you're not in need of any kind of... help."
            # normal
            elif book_points > 3:
                i "Who knows... I won't find out unless I try, right?"
                i "I've done all I could, but ultimately, the decision isn't in my hands." 
                mi "That's right, but maybe you could benefit from some kind of... help."
            else:     
                $ fian = "sad"
                i "There's no way for me to be sure, is it? I've done all I could, but..."
                i "Ultimately, the decision isn't in my hands."  
                mi "That's right, but maybe you could benefit from some kind of... help."
            $ fian = "worried"
            i "Wait... Are you suggesting what I think you're suggesting?"
            mi "All I'm saying is my insights as a jury member could prove... beneficial to you."
            menu:
                "I could use some help":
                    $ renpy.block_rollback()
                    $ v12_minerva_help = True
                    $ fian = "n"
                    i "Certainly... All the help I can get is welcomed..."
                    mi "Noted."
            
                "I'm not a cheater":
                    $ renpy.block_rollback()
                    $ fian = "n"
                    i "I appreciate your offer, but I'm not comfortable with it."
                    i "If I win, I want to know it's due to the quality of my writing, not because I cheated."
                    if ian_wits < 10:
                        call xp_up ('wits') from _call_xp_up_955
                    mi "As you wish... But you should know contests like these are rarely conducted impartially."
            
            i "I should get going. I guess I'll see you sometime soon."
            $ fminerva = "happy"
            mi "You know how to reach me, whenever you want to make it happen."
            $ fian = "smile"
            i "The same goes for you."
            mi "Certainly. Enjoy your summer vacation, Ian."
        elif ian_minerva_dating == 3:
            $ fian = "smile"
            i "Well, time for me to get going... I guess I'll see you sometime soon."
            $ fminerva = "happy"
            mi "You know how to reach me, whenever you want to make it happen."
            i "The same goes for you."
            mi "Certainly. Enjoy your summer vacation, Ian."
        else:
            i "Well, time for me to get going. I guess I'll see you around."
            mi "Yeah, I guess. Bye, Ian."
        # if holly_clark:
            # findme add scene ?
    # MINERVA ENEMY
    elif v5_ian_showup == False:
        i "I just need to deliver this to Minerva, pack my things, and get the hell out of here."
        $ minerva_look = 1
        show ian at lef with move
        play sound "sfx/door.mp3"
        show minerva at rig with short
        mi "Mh? Are you done?"
        i "The layout is mostly finished. There are still some adjustments that need to be made, but someone else will have to take care of it."
        if ian_honest_review or ian_switch_review:
            mi "I'm sure I'll find another intern to finish your tasks. I can't say the magazine will miss your contribution..."
        else:
            mi "I'm sure I'll find another intern who can take care of your tasks. You haven't been the most involved worker..."
        menu:
            "{image=icon_charisma.webp}Let her have it" if ian_charisma > 7:
                $ renpy.block_rollback()
                stop music fadeout 1.0
                $ fian = "serious"
                play music "music/tension.mp3" loop
                i "I'd say the biggest problem this magazine has is you."
                $ fminerva = "mad"
                mi "Excuse me?"
                i "You created an unhealthy working environment, and you went out of your way to target me repeatedly."
                if ian_minerva > 0:
                    call friend_xp ('minerva',-1) from _call_friend_xp_1093
                mi "Oh, is that so? Have you ever thought that the issue might originate from your end?"
                mi "I don't know what you were trying to prove, but it's you who went out of your way to challenge me at every step."
                menu:
                    "{image=icon_wits.webp}Break her down" if ian_wits > 7:
                        $ renpy.block_rollback()
                        $ fian = "mad"
                        i "I'm sure you know the reason. You realize you're everything that's wrong with this industry, don't you?"
                        mi "And who are you to judge, exactly? You're just a simple intern; you know nothing about the industry!"
                        $ fian = "serious"
                        i "I know enough about you. I've had plenty of time to observe you, and it's obvious you're a sad, frustrated woman."
                        $ fminerva = "furious"
                        mi "What did you say!?"
                        i "I don't know if you ever stood by any principles, but at present you have none."
                        i "Is that what you hate so much about me? That I refuse to bend over and take it like you did?"
                        mi "What would you know? I can't tolerate your self-importance! You're just a petty intern with delusions of grandeur!"
                        $ fian = "evil"
                        i "Aren't you projecting again? I think that's your problem... You're the pettiest one here."
                        i "You've been venting your frustrations on me from the get-go. I've been wondering why, but I believe I figured it out."
                        i "You're an unsatisfied middle-aged woman who regrets her life choices but won't admit it to herself."
                        i "You thought being in charge of this magazine would make you happy, but you're bitter at the fact that you're only spewing corporate propaganda and promoting shitty literature."
                        i "But that's not the only reason... You've been taking out your sexual frustration on me, right?"
                        $ fminerva = "sad"
                        i "Well, it's not my fault that your husband won't even touch you... I can definitely sympathize with him."
                        $ fian = "furious"
                        i "Why don't you just go out and get laid already? Maybe that way you'll stop breaking people's balls!" 
                        $ fminerva = "furious"
                        mi "Get the hell out of this office! Now!" with vpunch
                        $ fian = "evil"
                        i "I struck a nerve, didn't I?"
                        mi "I said OUT!"
                        call friend_xp ('minerva',-1) from _call_friend_xp_1094
                        $ ian_minerva = 0
                        $ fian = "serious"
                        i "Good riddance."
                        stop music fadeout 2.0
                        play sound "sfx/door.mp3"
                        hide minerva with short
                        show ian at truecenter with move
                        i "I needed that... It was about time I let her have it!"
                        if ian_will < 2:
                            call will_up() from _call_will_up_12

                    "Challenge her":
                        $ renpy.block_rollback()
                        $ fian = "mad"
                        i "I'm sure you know the reason. You realize you're everything that's wrong with this industry, don't you?"
                        $ fminerva = "furious"
                        mi "And what would you know? I can't tolerate your self-importance."
                        mi "Gain some knowledge before you presume to give me lessons... Someone as idealistic and naive as you won't make it anywhere."
                        i "Is that what gets on your nerves so much? That, unlike you, I have principles to stand by?"
                        i "I don't know if you ever had them, but I recommend you reconsider where you stand. All you're doing is contributing to the erosion of the publishing world."
                        i "And if that doesn't say enough about you, you also vent your frustrations on your employees."
                        i "Do you feel proud of the person you've become? I don't think so. But I won't continue to pay for your insecurities."
                        i "Fortunately, my time here is almost over."
                        if ian_wits < 8:
                            call xp_up ('wits') from _call_xp_up_956
                        mi "What are you waiting for? Begone! And close the door on your way out!"
                        if ian_minerva > 0:
                            call friend_xp ('minerva',-1) from _call_friend_xp_1095
                        stop music fadeout 2.0
                        play sound "sfx/door.mp3"
                        hide minerva with short
                        show ian at truecenter with move
                        $ fian = "serious"
                        i "That felt good... It was about time I let her have it."

                    "Dismiss her":
                        $ renpy.block_rollback()
                        $ fian = "n"
                        i "It's obvious it's not possible to reason with you, and I don't intend to try right now."
                        i "Thankfully, I won't have to deal with you anymore. Time for me to go."
                        mi "Good riddance."
                        stop music fadeout 2.0
                        play sound "sfx/door.mp3"
                        hide minerva with short
                        show ian at truecenter with move
                        i "That felt good... It was about time I let her have it."

                $ fian = "n"
                i "I'm finally outta here."

            "The feeling is mutual":
                $ renpy.block_rollback()
                i "I'm not pleased with you as a boss, either."
                $ fminerva = "mad"
                mi "What?"
                i "I don't know the reasons behind your attitude, but you created an unhealthy working environment."
                i "I suggest you treat the new interns better. You won't get much productivity out of them through humiliation and disrespect."
                if ian_charisma < 8:
                    call xp_up ('charisma') from _call_xp_up_957
                mi "Haven't you considered the problem might be your attitude? You're the only one complaining about this."
                i "If that's what you want to believe, I won't try to reason with you. Thankfully, I don't work here anymore."
                mi "That's right. Close the door on your way out."
                if ian_minerva > 0:
                    call friend_xp ('minerva',-1) from _call_friend_xp_1096
                i "Sure."
                stop music fadeout 2.0
                play sound "sfx/door.mp3"
                hide minerva with short
                show ian at truecenter with move
                $ fian = "serious"
                i "Good riddance... I'm so glad I won't have to see that ugly hag's face anymore."
                $ fian = "n"
                i "I'm free now..."

            "Apologize":
                $ renpy.block_rollback()
                i "I apologize for not meeting your expectations. I have tried to fulfill my duties to the best of my ability."
                mi "I'm glad to see I've managed to instill a bit of humbleness in you, after all."
                call friend_xp ('minerva',2) from _call_friend_xp_1097
                mi "I hope this served as a learning experience for you, at least."
                i "Yeah, it certainly was... Anyway, I bid you farewell. Perhaps our paths will cross again in the future."
                mi "Who knows. Bye, Ian."
                stop music fadeout 2.0
                play sound "sfx/door.mp3"
                hide minerva with short
                show ian at truecenter with move
                $ fian = "sad"
                "I didn't want to risk upsetting Minerva, so I swallowed my pride."
                i "I don't want to have her as an enemy... I still want to get into the publishing industry."
                $ fian = "n"
                i "At least I won't have to deal with her again, or so I hope..."
 
# SHOPPING ###############################
    label v12minervaexit:
        scene magazine with long
    pause 1
    $ fian = "n"
    stop music fadeout 2.0
    scene street with long
    show ian with short

    play sound "sfx/ring.mp3"
    hide ian
    show ian_phone
    with short
    pause 0.6
    i "Yes?"
    show phone_perry at lef3 with short
    p "Hey, w--{w=0.5}what's up? Are you all set for tomorrow?"
    i "I still have to pack my things, but other than that..."
    p "What t--{w=0.5}time will you guys be taking the train?"
    if tournament:
        i "The first round of the MMA tournament is tomorrow morning, so we'll meet at the station after lunch."
    else:
        i "Emma said she had some stuff to take care of at the community center during the morning, so we'll meet at the station after lunch."
    p "I see. So you'll arrive in the afternoon... Anyway, I need to ask you a f--{w=0.5}favor."
    p "My folks have a grill at their house, and it'd be awesome to f--{w=0.5}fire it up."
    $ fian = "smile"
    i "That'd be cool, yeah."
    p "I'm going to do some sh--{w=0.5}shopping this afternoon. I wanted to get beers for everyone and m--{w=0.5}meat for the barbecue: some sausages, chops, burgers..."
    p "I'm rather short on m--{w=0.5}money, as you know... Could you p--{w=0.5}pay me back for the food and beers?"
    $ fian = "n"
    menu:
        "{image=icon_pay.webp}I got it" if ian_money > 0:
            $ renpy.block_rollback()
            $ v12_bbq = 2
            call money (-1) from _call_money_143
            i "Alright, I'll take care of it... Buy whatever is necessary and I'll give you the money back."
            hide phone_perry
            show phone_perry_smile at lef3
            p "Awesome, I knew I could c--{w=0.5}count on you! See you tomorrow, then."
            if ian_perry < 12:
                call friend_xp ('perry') from _call_friend_xp_1098
            hide phone_perry_smile
            hide ian_phone
            show ian 
            with short
            if ian_perry < 4:
                $ fian = "serious"
                i "He's really useless... I always have to take care of everything."
            else:
                $ fian = "smile"
                i "A barbecue, huh? That sounds great..."
            if ian_charisma < 8:
                call xp_up ('charisma') from _call_xp_up_958

        "Pay for half of it":
            $ renpy.block_rollback()
            $ v12_bbq = 1
            i "I can pay for half of it."
            hide phone_perry
            show phone_perry_meh at lef3
            p "Only half?"
            i "Yeah, man. Sounds fair to me."
            p "Well, I'm inviting you to s--{w=0.5}spend some days at my place, at the house for f--{w=0.5}free..."
            i "At {i}your parents'{/i} place at the beach."
            p "Well, yeah, the thing is I don't have enough m--{w=0.5}money for everything..."
            i "Then ask your parents for it."
            p "Okay, whatever. I'll f--{w=0.5}figure it out."
            if ian_perry > 0:
                call friend_xp ('perry',-1) from _call_friend_xp_1099
            p "See you guys tomorrow."
            hide phone_perry_meh
            hide ian_phone
            show ian 
            with short
            if ian_perry < 4:
                $ fian = "serious"
                i "He's fucking useless... I always have to take care of everything. I'm doing enough as it is."
            else:
                i "He always makes me take care of everything... When will he grow up?"

        "{image=icon_mad.webp}I'm not paying for shit" if ian_perry < 6:
            $ renpy.block_rollback()
            $ v12_bbq = 0
            $ fian = "serious"
            i "No way, man. You pay for it."
            hide phone_perry
            show phone_perry_serious at lef3
            p "Come on, I'm inviting you to s--{w=0.5}spend a few days at my place for free... You can pitch in a bit too, can't you?"
            $ fian = "mad"
            i "First off, it's your folks' place. Just like the apartment where you're making me pay rent."
            i "You invited us, remember? So, handle the bills, mate. It's time to step up."
            if ian_perry > 0:
                call friend_xp ('perry',-1) from _call_friend_xp_1100
            hide phone_perry_serious
            show phone_perry_mad at lef3
            p "Alright, alright. You don't have to be a dick about it."
            p "But don't come whining if we run out of grub later."
            hide phone_perry_mad
            hide ian_phone
            show ian 
            with short
            if ian_perry < 4:
                i "He's fucking useless... I always have to take care of everything."
            else:
                i "This guy... He always makes me take care of everything."
            $ fian = "serious"
            i "Well, not this time."
            if ian_charisma < 8:
                call xp_up ('charisma') from _call_xp_up_959
    
    $ fian = "n"
    i "Now that I think about it... I've been needing some summer clothes. It's getting pretty hot these days."
    if ian_cindy_dating or v12_alison_sex:
        i "Since I'm here already, I think I'll wander around the mall for a bit. I still have some time before the date."  
    else:
        i "Since I'm here already, I think I'll wander around the mall for a bit."
    hide ian with short
    play music "music/normal_day3.mp3" loop
    scene mall with long
    pause 1
    show ian with short
    pause 1
    $ config.menu_include_disabled = False
    $ greyed_out_disabled = True
    menu v12ianshopping:
        "Buy summer clothes" if ian_summer_look == 0:
            $ renpy.block_rollback()
            i "Let's see what I can get."
            label v12ianclothingshop:
                show ian at left with move
                call screen v12ian_clothingshop

            label v12ian_clothing_wits:
                hide ian with short
                $ ian_summer_look = "wits"
                $ ian_look = "summer"
                $ fian = "smile"
                show ian with short
                jump v12summershopconfirm
            label v12ian_clothing_charisma:
                hide ian with short
                $ ian_summer_look = "charisma"
                $ ian_look = "summer"
                $ fian = "smile"
                show ian with short
                jump v12summershopconfirm
            label v12ian_clothing_athletics:
                hide ian with short
                $ ian_summer_look = "athletics"
                $ ian_look = "summer"
                $ fian = "smile"
                show ian with short
                jump v12summershopconfirm
            label v12ian_clothing_lust:
                hide ian with short
                $ ian_summer_look = "lust"
                $ ian_look = "summer"
                show ian with short
                $ fian = "smile"
                jump v12summershopconfirm
            menu v12summershopconfirm:
                "Get this one":
                    $ renpy.block_rollback()
                    i "It's decided."
                    hide ian with short
                    $ fian = "n"
                    $ ian_look = 2

                "Try something else":
                    $ renpy.block_rollback()
                    $ fian = "n"
                    i "Hm... I'm not convinced. Let's see this one..."
                    hide ian with short
                    $ ian_look = 2
                    show ian with short
                    jump v12ianclothingshop
                       
            show ian with short
            # jess
            if jess_bad and v10_jess_date:
                $ fjess = "n"
                $ jess_look = 2
                "As I left the shop I crossed paths with a familiar face."
                show ian at lef with move
                show jessb at rig3 with short
                pause 1
                if ian_jess > 4:
                    $ fian = "smile"
                    i "Hey... What's up, Jess?"
                    show jessb at rig with move
                    js "Hey... Just going to the studio. I have an appointment."
                    i "Of course, don't let me keep you... How about we get another beer one of these days, though?"
                    js "Sure, why not..."
                    js "Gotta run now. Bye."
                    hide jessb with short
                    $ fian = "n"
                    show ian at truecenter with move
                    i "She didn't seem very enthusiastic... I think our previous date went well, but who knows?"
                    i "She's very hard to read."
                    if ian_lena_couple:
                        $ fian = "sad"
                        i "What am I even trying to accomplish here? I shouldn't be flirting with her now that I'm dating Lena..."
                        if ian_cheating:
                            i "I've already cheated on her, but doing so with Jess seems rather risky. She seems to be close with Ivy."
                    elif ian_holly_dating or ian_cindy_love or ian_alison_love or ian_cherry_love:
                        i "What am I even trying to accomplish here? I'm already interested in someone else..."
                    if ian_chad > 3:
                        $ fian = "confident"
                        i "I'd love to get a go at her, though. I could brag about having fucked a pornstar... Anyone would be jealous."
                    elif ian_chad > 2:
                        $ fian = "smile"
                        i "She makes me curious, though... I can only imagine what it's like to fuck a pornstar!"
                    else:
                        $ fian = "shy"
                        i "I can't deny my curiosity, though. What would it be like to have sex with a pornstar...?"
                    $ fian = "smile"
                elif ian_jess < 2:
                    $ fian = "worried"
                    i "Oh. Hey..."
                    $ fjess = "serious"
                    "Jessica shot me an unfriendly look and walked on without a word."
                    hide jessb with short
                    i "..."
                    show ian at truecenter with move
                    i "I guess I can't blame her... I messed up big time on that date we had."
                    if ian_jess == 0:
                        $ fian = "disgusted"
                        i "I can't believe I offered to pay her for sex... What the hell was I thinking?"
                        i "I hope she never tells anyone about it..."
                    else:
                        $ fian = "insecure"
                        i "I don't know what I was thinking. It's embarrassing just to remember..."
            jump v12ianshopping
                    
        "{image=icon_wits.webp}{image=icon_pay.webp}Buy fireworks" if ian_money > 0 and ian_wits > 5 and v12_fireworks == False:
            $ renpy.block_rollback()
            $ v12_fireworks = True
            $ fian = "smile"
            i "I just got an idea... I could buy some fireworks to liven up the nights."
            i "The beach seems like the perfect setting to light some sparklers and set off some rockets... I'm sure the guys will like it."
            call money (-1) from _call_money_144
            jump v12ianshopping

        "{image=icon_love.webp}{image=icon_pay.webp}Buy a gift for Cindy" if ian_cindy_love and ian_money > 0 and v12_gift == "n":
            $ renpy.block_rollback()
            $ v12_gift = "cindy"
            "While strolling through the shopping mall a jewelry store's showcase caught my attention."
            $ fian = "smile"
            i "This necklet would look great around Cindy's neck... I'm sure she'd love a gift like this. In fact, I could do something special for her..."
            label v12buynecklet:
                i "I'll get it."
            hide ian with short
            call money (-1) from _call_money_145
            pause 0.5
            if v3_gillian_stop == False:
                $ fian = "n"
                show ian with short
                i "..."
                "Coming out of the store, I couldn't help but reminisce about Gillian."
                $ fian = "depress"
                "About a day very similar to this one, when, on a whim, I bought a silver ring for her."
                "A ring she'd wear as a reminder of my feelings for her, and the bond that existed between us."
                if v11_gillian_talk == 3:
                    $ fian = "worried"
                    "A ring that she was still wearing. I had seen it on her finger that night, there was no doubt about it."
                    i "That has to mean something, right...?"
                else:
                    $ fian = "mad"
                    i "Fuck... Why won't she leave my subconscious alone? I have someone else that I love now, and it's not her."
                    $ fian = "n"
                    if v12_gift == "cindy":
                        i "Thanks to breaking up with Gillian, now I can be with Cindy. There's no comparison..."
                    if v12_gift == "lena":
                        i "Thanks to breaking up with Gillian, now I can be with Lena. There's no comparison..."
                    if v12_gift == "alison":
                        i "Thanks to breaking up with Gillian, now I can be with Alison. There's no comparison..."
                    if v12_gift == "holly":
                        i "Thanks to breaking up with Gillian, now I can be with Holy. There's no comparison..."
                    if v12_gift == "cherry":
                        i "Thanks to breaking up with Gillian, now I can be with Cherry. There's no comparison..."
                    if v12_gift == "minerva":
                        i "Thanks to breaking up with Gillian, I've got the chance to discover a woman like Minerva. There's no comparison..."
            else:
                show ian with short
            jump v12ianshopping

        "{image=icon_love.webp}{image=icon_pay.webp}Buy a gift for Lena" if ian_lena_dating and ian_lena_love and ian_money > 0 and v12_gift == "n":
            $ renpy.block_rollback()
            $ v12_gift = "lena"
            "While strolling through the shopping mall a jewelry store's showcase caught my attention."
            $ fian = "smile"
            i "This necklet would look great around Lena's neck... I've never had a gesture like this with her. I wonder if she would like it..."
            jump v12buynecklet

        "{image=icon_love.webp}{image=icon_pay.webp}Buy a gift for Holly" if ian_holly_dating and ian_money > 0 and v12_gift == "n":
            $ renpy.block_rollback()
            $ v12_gift = "holly"
            "While strolling through the shopping mall a jewelry store's showcase caught my attention."
            $ fian = "smile"
            i "I wonder how this necklet would look on Holly... Would she appreciate it if I gave it to her as a gift?"
            i "I could show her some appreciation, after all..."
            jump v12buynecklet

        "{image=icon_love.webp}{image=icon_pay.webp}Buy a gift for Alison" if ian_alison_love and ian_money > 0 and v12_gift == "n":
            $ renpy.block_rollback()
            $ v12_gift = "alison"
            "While strolling through the shopping mall a jewelry store's showcase caught my attention."
            $ fian = "smile"
            i "I wonder how this necklet would look on Alison... I'm sure she'd be thrilled if I showed her some appreciation."
            jump v12buynecklet

        "{image=icon_love.webp}{image=icon_pay.webp}Buy a gift for Cherry" if ian_cherry_love and ian_money > 0 and v12_gift == "n":
            $ renpy.block_rollback()
            $ v12_gift = "cherry"
            "While strolling through the shopping mall a jewelry store's showcase caught my attention."
            $ fian = "smile"
            i "This necklet would look great around Cherry's neck... I wonder if she would like me making a gesture like this for her."
            jump v12buynecklet

        "{image=icon_love.webp}{image=icon_pay.webp}Buy a gift for Minerva" if ian_minerva_dating == 3 and ian_money > 0 and v12_gift == "n":
            $ renpy.block_rollback()
            $ v12_gift = "minerva"
            "While strolling through the shopping mall a jewelry store's showcase caught my attention."
            $ fian = "smile"
            i "This necklet would look great around Minerva's neck... I feel like having a gesture with her."
            i "I wonder if she'll appreciate something like this."
            jump v12buynecklet

        "Leave" if ian_summer_look != 0:
            $ renpy.block_rollback()
            if persistent.include_disabled:
                $ config.menu_include_disabled = True
            $ greyed_out_disabled = False

    i "I'm done here."
    # jeremy phonecall
    if tournament == False:
        play sound "sfx/ring.mp3"
        $ fian = "n"
        hide ian
        show ian_phone
        with short
        i "Hey."
        show phone_jeremy_smile at lef3 with short
        j "What's up, man? Tomorrow's the big day! Are you coming or what?"
        if ian_jeremy < 3:
            i "Coming to what?"
            hide phone_jeremy_smile
            show phone_jeremy at lef3
            j "The tournament, dude. Tomorrow's the first round..."
            i "You know I'm not participating."
            j "But you could come and cheer me up! Come on, we've been training together!"
            "I wasn't too thrilled about the idea. I was still quite annoyed with Jeremy, and I hadn't been feeling like like hanging out with him lately."
            i "I don't know... Tomorrow I'm leaving to spend a few days at Perry's..."
            wen "Hand the phone to me."
            hide phone_jeremy
            show phone_wen at lef3
            with short
            wen "Ian! You have to show up tomorrow."
            wen "We're hosting the first round at the gym, first thing in the morning. You have no excuse not to come!"
            "I could think about a few, but the truth was I had nothing to do during the morning."
            "Maybe if I showed up I could witness Jeremy getting his face punched in for a change..."
            i "Alright, alright. I'll drop by and check it out."
            wen "Good. See you tomorrow!"
            hide phone_wen 
            hide ian_phone
            show ian
            with short
        elif ian_jeremy < 7:
            i "You mean the tournament? Is that tomorrow?"
            j "Yeah! The first round... I'm kinda nervous!"
            i "I'm not sure I can drop by... Tomorrow I'm leaving to spend a few days at Perry's..."
            j "I'm fighting first thing in the morning. Can you make it?"
            i "Yes, I guess so... I'm free until lunch."
            hide phone_jeremy_smile
            show phone_jeremy_happy at lef3
            j "Perfect then! I'll see you tomorrow!"
            hide phone_jeremy_happy
            hide ian_phone
            show ian
            with short   
        else:
            $ fian = "smile"
            i "Tomorrow's the tournament? At what time?"
            j "First thing in the morning."
            i "Cool... I'll swing by to cheer you on."
            hide phone_jeremy_smile
            show phone_jeremy_happy at lef3
            j "Awesome! I'm quite excited, and a bit nervous...!"
            i "I'm sure you'll do fine. You always kick ass at the gym."
            j "I hope so! I'm gonna get back to practice. See you tomorrow!"
            hide phone_jeremy_happy
            hide ian_phone
            show ian
            with short 
    if ian_cindy_dating:
        jump v12cindydate
    if v12_alison_sex:
        jump v12alisondate
    stop music fadeout 2.0
    scene street with long
    pause 0.5
    jump v12iannighthome

# CINDY ######################################################################################################################################################
######################################################################################################################################################
label v12cindydate:
    if ian_cindy_love:
        $ fian = "smile"
    else:
        $ fian = "confident"
    i "Alright... Time to go meet Cindy."
    stop music fadeout 2.0
    hide ian with long
    pause 0.6

    label gallery_CH12_S03:
        if _in_replay:
            call setup_CH12_S03 from _call_setup_CH12_S03
    $ cindy_look = 2
    $ fcindy = "n"
    scene cindyroom with long
    play sound "sfx/doorbell.mp3"
    pause 1
    show ian at lef
    show cindy at rig
    with long
    c "Hey..."
    i "Can I come in?"
    $ fcindy = "flirt"
    c "What do you think I've been waiting for?"
    $ fian = "confident"
    if ian_cindy_love:
        play music "music/sex_elation.mp3" loop
    else:
        play music "music/sex_passion.mp3" loop
    show ian at centerlef with move
    i "I can only guess... Why don't you enlighten me?"
    c "You already know the answer to that."
    # kiss
    scene v12_cindy1 with long
    pause 1
    "Cindy jumped into my arms and planted a passionate kiss on my lips."
    "My hands traced down her sides until they encircled her slender waist. It nestled perfectly in my grasp, as if molded just for them."
    "The simple act of holding Cindy like this filled me with a heady sense of satisfaction..."
    "And the way she showed her desire for me turned me on like mad."
    # tease
    scene v12_cindy2a with long
    pause 1
    "My hands slipped under her clothes, eager to caress her smooth and soft skin."
    i "So is this the answer...? Is this what you've been waiting for?"
    c "Yes... I wanted to feel your hands on my body... Your lips... Your..."
    # tease2
    play sound "sfx/ah1.mp3"
    scene v12_cindy2b with long
    c "Mhhh...!"
    "Cindy let out a sweet moan, overflowing with contained pleasure."
    "My fingers had finally reached her pussy, and she shivered against my chest."
    i "I see... Good thing I'm here, then."
    c "God, the way you touch me is... so good...!"
    if cindy_satisfaction == 3:
        # bj
        scene v11_cindy5a 
        show v11_cindy5_stockings 
        with long
        pause 1
        "Suddenly, Cindy took the lead and pushed me down to the bed, then climbed on top of me and held my cock in her hand."
        c "You're so big and hard... Must be because you were also eager for this."
        i "You have no idea..."
        # bj
        scene v12_cindy3 with long
        play sound "sfx/bj3.mp3"
        pause 1
        i "Oh, yes...!"
        "A pleasant shiver ran through me when Cindy's wet tongue brushed against my glans."
        "Without me having to ask for it and of her own initiative, she started sucking me off, filling my cock with lewd kisses and devilish licks."
        scene v11_cindy6 with long
        pause 1
        c "Looks like you really love it when I do this..."
        play sound "sfx/bj1.mp3"
        scene v11_cindy7 with long
        pause 1
        "Cindy wrapped her lips around my cock and started to suck on it enthusiastically, bobbing her head up and down."
        i "Your mouth feels so fucking good... It's like I'm in Heaven right now..."
        "My words seemed to please Cindy, who continued to delight me with her mouth."
        i "I feel like I could cum if you keep this up..."
        scene v11_cindy6 with long
        c "We don't want that, do we...? I've been waiting way too long to feel you inside of me again..."
    else:
        i "That's not the only thing I know how to do well."
    menu:
        "Eat Cindy out":
            $ renpy.block_rollback()
            if cindy_satisfaction == 3:
                i "Not so fast... I need to taste you again, too."
            # cunni
            scene v12_cindy4 with long
            pause 1
            "I swiftly parted Cindy's legs, covering her pussy with my mouth and making her moan."
            play sound "sfx/mh1.mp3"
            c "Nhhhh, yes...!"
            "I savored her warm, wet flesh, running my tongue up and down her slit first, before focusing on her aroused clitoris."
            if ian_lust < 10:
                call xp_up ('lust') from _call_xp_up_960
            c "God, how are you so good at this? You're driving me to the edge already..."
            "I had been very careful to learn how Cindy liked it, the best angle in which to flick her clit, the right rhythm and pressure, and when to change them up."
            "Although I could question her words, her reactions left no room for doubt: I knew how to delight her."
            if cindy_satisfaction == 3:
                $ cindy_satisfaction = 4
                $ cindy_ass = True
                c "I... I want you to eat me out the same way you did that day, at your place..."
                i "I know exactly what you want, you dirty girl..."
                # cunni ass
                scene v12_cindy5 with long
                "I grabbed Cindy by the hips and spun her around, spread her cheeks apart, and pressed my tongue down onto her anus."
                play sound "sfx/ah6.mp3"
                c "Ohhhh, fuck...!" with vpunch
                "Her body shook and trembled as breathy and raspy groans escaped her lips. This dirty pleasure seemed to really turn her on..."
                "And I didn't mind giving it to her, quite the opposite: discovering the depths of Cindy's lust was a fascinating experience."
                "And I followed her down that rabbit hole. I had a raging boner that was demanding to be satisfied."
                i "I can't wait any longer... I need to fuck you, Cindy."
                c "Yes, do it...! Please, fuck me!"
            else:
                menu:
                    "{image=icon_lust.webp}Eat her ass" if ian_lust > 7:
                        $ renpy.block_rollback()
                        $ cindy_satisfaction += 1
                        $ cindy_ass = True
                        # cunni ass
                        play sound "sfx/ah6.mp3"
                        scene v12_cindy5 with vpunch
                        c "Oh!{w=0.7}{nw}"
                        pause 0.5
                        "I pushed Cindy to the bed and got behind her, spreading her buttcheeks with my hands."
                        c "Ian! Oh...!"
                        "It seemed like Cindy was about to say something, but her voice turned into a sexy squeal as soon as my mouth covered her wet pussy."
                        "She tasted so good... I could eat her out for hours."
                        "I rolled my tongue boldly up her slit, venturing into her natal cleft even. She tensed up and trembled."
                        c "Wait, that's dirty..."
                        i "There's not a part of you that's dirty, Cindy."
                        c "Oh, God...!"
                        "She gripped the bedsheets while I continued to lick both her pussy and asshole, exposing her to a new kind of pleasure she wasn't used to."
                        c "I can't wait any longer... Fuck me, Ian...!"

                    "Fuck Cindy":
                        $ renpy.block_rollback()
                        $ cindy_satisfaction += 1
                        c "I can't wait any longer... Fuck me, Ian...!"
                        i "I was just thinking about that."

        "Fuck Cindy":
            $ renpy.block_rollback()
            if cindy_satisfaction == 3:
                i "Let's not keep you waiting any longer, then..."
    
    if ian_cindy_love:
        # sex
        scene v9_cindy13b
        show v9_cindy13_stockings
        play sound "sfx/moan4.mp3"
        with long
        pause 1
    else:
        # fuck 
        scene v12_cindy7a with long
        pause 1
    "My cock made its way between Cindy's tight lips, slowly penetrating her."
    "I felt her sex embracing my shaft, receiving me with trembling desire."
    c "Slowly... Ahh... It's so big..."
    "I kept pushing patiently until all of my manhood was embedded deep within Cindy. Her body rewarded me with a pulsating and thrilling sensation."
    if ian_cindy_love:
        i "You feel incredible... I love you, Cindy."
        c "Ah, yes...! I love you too...!"
        "I could clearly feel her arousal growing as I kept grinding my hips with increasing need."
        # fuck
        scene v12_cindy7a with long
        pause 1
    else:
        i "Fuck, you feel incredible... Your pussy is just perfect for my cock."
        c "Yes... Mhhh, yes...!"
        "I could clearly feel her arousal growing as I kept grinding my hips with increasing need."
    "I squeezed the muscles around my cock, pumping blood and making it swell even more, stretching Cindy's tight insides."
    with hpunch
    play sound "sfx/moan5.mp3"
    c "Ahh, God... I felt that... You're so fucking big!"
    i "You look so damn beautiful right now... Lying down with your legs spread and my cock deep inside of you..."
    "Cindy bit her lips, excited at my words, and moaned as I caressed her body and her perfect breasts, teasing her small nipples."
    "I continued to clench my muscles, tapping Cindy's deepest insides with my penis. With each tap, a spasm of pleasure ran through my shaft, making me shudder."
    if ian_cindy_love:
        i "Fuck, you feel so good...! It's like your pussy was made for me. I could cum right now...!"
    else:
        i "Fuck, you feel so good! I'm about to cum just by looking at you...!"
    c "Yes, Ian! Cum! I want to make you cum!"
    if ian_lust < 8:
        menu:
            "{image=icon_will.webp}Cum all over Cindy" if ian_will > 0:
                $ renpy.block_rollback()
                call willdown from _call_willdown_61
                jump v12cindycumoutside
            
            "Cum inside Cindy":
                $ renpy.block_rollback()
                jump v12cindycuminside
    menu:
        "{image=icon_lust.webp}Cum all over Cindy" if ian_lust > 7:
            $ renpy.block_rollback()
            label v12cindycumoutside:
                $ v12_cindy_cum = True
            # cum outside
            scene v12_cindy7b with long
            "I was at the edge, and couldn't endure it anymore. I pulled out and started jerking off, rushing towards the abyss of ecstasy."
            i "Oh, fuck yes! I'm cumming!! Yesss!! {w=0.8}{nw}"
            show v12_cindy7_cum1 with flash
            pause 0.2
            hide v12_cindy7_cum1
            show v12_cindy7_cum2
            with fps 
            show v12_cindy7_cum1 with flash
            pause 0.3
            hide v12_cindy7_cum1
            with short
            pause 1
            "I continued stroking my cock, riding the jolts of pleasure and shooting my load all over Cindy."
            c "Oh, wow...!"
            "I looked down to watch the results of my lust, painting Cindy's pristine skin with a lewd color."
            menu:
                "{image=icon_athletics.webp}I'm not done yet" if ian_athletics > 4:
                    $ renpy.block_rollback()
                    if cindy_satisfaction < 4:
                        $ cindy_satisfaction += 1
                    c "Look at what you've done... You splashed me all over. So gross!"
                    i "Gross? You have no idea how beautiful you look right now..."
                    c "Beautiful?"
                    i "The most beautiful sight I've seen in my entire life. Look, my cock is still hard as a rock..."
                    play sound "sfx/mh2.mp3"
                    scene v12_cindy7a 
                    show v12_cindy7_cum2
                    with short
                    i "That's how much I like you."
                    c "Ahh... What are you doing? You just came..."
                    scene v12_cindy6 with long
                    pause 1
                    i "Once is not enough. I want you, Cindy... I want you to be mine!"
                    if ian_cindy_love:
                        c "Yes! I'm yours...! All yours, Ian...!"
                    else:
                        c "Yes! I want you inside of me...! Fuck me, Ian! Fuck me!"
                    "Her hands grabbed my hips, pulling me towards her and pleading for harder and deeper thrusts."
                    c "Yes, yes, yes...!"
                    "I surrendered to passion, unleashing all my fervor upon the beautiful golden-haired nymph lying beneath me."
                    "My body crashed against hers, sinking into the bed, making her shudder violently." with vpunch
                    c "Oh God! I'm cumming!! I'm cumming!!!"
                    play sound "sfx/orgasm2.mp3" fadeout 1.0
                    scene v12_cindy6b with flash
                    c "Ahhhh!!! {w=0.5}{nw}" with hpunch
                    c "Ahhhh!!! {w=0.2}{nw}" with flash
                    c "Ahhhh!!! {w=0.5}{nw}" with hpunch
                    c "Ahhhh!!! {w=0.5}{nw}" with hpunch
                    "I felt Cindy's legs quaking uncontrollably and her entire body convulsing as she was struck by orgasm."
                    # end
                    scene v12_cindy7a 
                    show v12_cindy7_cum2
                    with long
                    pause 1
                    "I slowed down, letting her enjoy her ecstasy as it slowly receded."
                    c "Oh God, Ian... That was... You give me the best sex I've had in my life."
                    if ian_cindy_love:
                        i "And I intend to keep giving it to you... I love making you feel good, but I bet you can tell."
                        i "I want you to experience as much pleasure as you can imagine, and even more."
                    else:
                        i "Oh, yeah? Lucky you... It was about time you found a man who can fuck you like you deserve."
                        i "Turns out, I love doing just that."
                    if ian_charisma < 10:
                        call xp_up ('charisma') from _call_xp_up_961
                        pause 0.5
                    scene v12_cindy8 with long
                    pause 1
                    "Cindy ran her fingers across her chest, staining her fingertips with my still-warm semen."
                    "She raised them, examining the translucent substance in the evening light."
                    c "So you think I look beautiful covered in your jizz?"
                    i "Are you trying to provoke me to go at it again?"
                    c "Could you?"
                    i "Wanna try?"
                    c "No, I need a break...! My pussy's gonna be sore tomorrow!"
                    
                "Rest":
                    $ renpy.block_rollback()
                    # pose
                    scene v12_cindy8 with long
                    pause 1
                    "Cindy ran her fingers across her chest, staining her fingertips with my still-warm semen."
                    "She raised them, examining the translucent substance in the evening light."
                    c "Look at what you've done... You splashed me all over. So gross!"
                    "Despite her words, Cindy didn't seem too bothered by it..."
                    i "You have no idea how beautiful you look right now, with my desire painted all over your body..."
                    c "Really? If you put it that way, it sounds rather sexy..."
                    stop music fadeout 2.0
                    i "It is."
                    if ian_charisma < 10:
                        call xp_up ('charisma') from _call_xp_up_962
                        pause 0.5

        "Cum inside Cindy":
            $ renpy.block_rollback()
            label v12cindycuminside:
                "Finally, I couldn't take it anymore and decided to let all my lust for her loose."
            i "Oh, fuck yes! I'm cumming!! Yesss!! {w=0.8}{nw}" with flash
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            with vpunch
            stop music fadeout 2.0
            if ian_lust < 8:
                call xp_up ('lust') from _call_xp_up_963
            pause 1
    
# cindy talk
    if ian_cindy_love:
        $ fcindy = "shy"
    else:
        $ fcindy = "flirt"
    play music "music/shine_again1.mp3" loop
    $ fian = "confident"
    $ ian_look = 3
    scene cindyroom
    show iannude at lef
    show cindynude2 at rig
    if v12_cindy_cum:
        show cindy_cum1 at rig
    with long
    "Cindy rose unsteadily to her feet."
    if v12_cindy_cum:
        c "I need a shower... You've made quite a mess out of me!"
        i "As I said, you look so damn beautiful right now."
        c "You're so mean..."
        i "I think you like it."
    else:
        c "My legs feel weak... That was great."
        i "As it always is."
        c "I need to clean myself up a bit..."
    hide cindynude2
    hide cindy_cum1
    with short
    play sound "sfx/shower.mp3"
    $ fian = "smile"
    show iannude at truecenter with move
    hide iannude
    show ianunder 
    with short
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH12_S03")
    "I sat down on the bed, enjoying the satisfying buzz that coursed through my body. Doing it with Cindy was always such a thrill..."
    $ fian = "n"
    if ian_cindy_love:
        "However, a sense of unease lingered. Not just because I was sleeping with Wade's girlfriend, but because she hadn't broken up with him yet..."
        "What was holding her back from taking that decisive step? What would our relationship be like when she finally did?"
    else:
        "Too bad she was Wade's girlfriend, but that wasn't going to last for long."
        "It was plain to see... She would dump him very soon. And then..."
    $ fcindy = "n"
    $ cindy_look = "comfytopless"
    show ianunder at lef with move
    show cindybra at rig with short
    $ fian = "smile"
    "Cindy came back from the bathroom and plopped down onto the bed, sprawling out next to me."
    i "You look quite happy and relaxed..."
    c "I am. I was feeling stuck in a rut, but things have taken a turn for the better. And this summer's shaping up to be fantastic..."
    $ fian = "n"
    i "It's a pity you can't join us at the beach this week."
    c "Of course not. Wade will be there..."
    c "Besides I'm off on a modeling gig with the agency this week!"
    c "It's still sinking in; five days of cruising around the islands, taking photos, and living the high life... It's hard to believe!"
# cindy love
    if ian_cindy_love:
        menu:
            "{image=icon_charisma.webp}Play it cool" if ian_charisma > 7 or ian_charisma > 6 and ian_chad > 3:
                $ renpy.block_rollback()
                $ v12_cindy_rel = 2
                $ fian = "confident"
                i "Sounds awesome, yeah... I can't wait to brag about my model girlfriend."
                $ fcindy = "flirt"
                c "Oh, so you wanna brag about me?"
                i "Who wouldn't? You're a girl anyone would want to claim as their own..."
                i "And that's exactly what I want to do, as soon as you break up with Wade."
                $ fcindy = "n"
                hide cindybra
                show cindybra2 at rig
                with short
                c "Yeah, I still need to get around sorting that out... I've been waiting for the best moment to do it."
                $ fian = "smile"
                i "And when do you think that moment will be?"
                c "After I come back from the trip, I guess... Even so, we can't come out of the closet right away."
                c "You know that would stir up so much drama, and I want to avoid that at all costs."
                $ fian = "n"
                i "So no bragging for me, huh? That sucks."
                c "Is it that important to you?"
                i "Not that, but I'd like to be able to hold your hand in public, and post a picture with you... That sort of thing."
                $ fcindy = "sad"
                c "Me too, but it's not so easy..."
                i "So you keep saying, but it doesn't change the fact that I don't want to hide our relationship from the world."
                i "I'm serious about you, Cindy, and I want to openly call you my girlfriend."
                c "That's... I, um..."
                i "What's the matter? Don't tell me you're having second thoughts about this. About us."
        
            "I don't like it":
                $ renpy.block_rollback()
                i "You seem thrilled about this modeling gig with Axel... quite thrilled."
                c "Well, yeah. It's a fantastic opportunity."
                i "I'm glad you're excited about it but... I have to admit, I'm not entirely comfortable with it."
                $ fcindy = "sad"
                c "What? Why?"
                i "I'm well aware of Axel's reputation. These trips, they can be pretty intense... Are you sure you can handle it?"
                stop music fadeout 2.0
                $ fcindy = "serious"
                hide cindybra
                show cindybra2 at rig
                with short
                c "What are you trying to say? Don't beat around the bush, speak clearly."
                call friend_xp ('cindy',-2) from _call_friend_xp_1101
                i "Well, if you must know... I'm concerned you're having second thoughts about this. About us."
                $ fcindy = "sad"

            "When are you gonna break up with Wade?":
                $ renpy.block_rollback()
                $ v12_cindy_rel = 1
                i "So, what's the deal with Wade? You've been saying you'd sort things out, but... well, nothing's happened."
                $ fcindy = "sad"
                hide cindybra
                show cindybra2 at rig
                with short
                c "We've already talked about this... It's complicated."
                c "I don't want to hurt him, but I don't want him to suspect us either. I'm waiting for the right time to break it off with him..."
                i "So you've been saying, but you seem more concerned about this modeling gig..."
                $ fcindy = "serious"
                c "Of course, it's a fantastic opportunity... What are you trying to say?"
                call friend_xp ('cindy',-1) from _call_friend_xp_1102
                i "It's just... I don't know. I guess I'm concerned you're having second thoughts about this. About us."
                $ fcindy = "sad"

        c "It's not like that... I know I like you..."
        $ fian = "sad"
        i "{i}Like{/i}, not {i}love{/i}? Honestly, Cindy, your words don't sound convincing at all..."
        c "No, I am, I mean... It's... complicated."
        menu:
            "{image=icon_love.webp}I trust you":
                $ renpy.block_rollback()
                $ v12_cindy_rel = 1
                $ fian = "sad"
                i "Look, I get it... I knew things wouldn't be easy when I signed up for this, but it gets frustrating at times."
                $ fian = "n"
                i "I just want you to know that I trust you, and I want to be with you."
                $ fcindy = "blush"
                i "I just hope we can figure things out and make this work."
                if ian_cindy < 12:
                    call friend_xp ('cindy') from _call_friend_xp_1103
                c "Yeah... Me too..."
                scene cindyroomnight with long
                "I wanted to stay longer with Cindy, but the mood turned a bit awkward after that conversation."
                if tournament:
                    "I had to get up early to be at the tournament the next day, so I decided to head home."
                else:
                    "In the end, I decided to leave and get my bag ready for the upcoming trip."
                if v12_gift == "cindy":
                    $ fian = "n"
                    $ fcindy = "sad"
                    $ cindy_look = 2
                    $ ian_look = 2
                    show ian at lef
                    show cindy at rig
                    with short
                    i "Oh, before I forget... I got you something."
                    c "You did?"
                    $ fian = "smile"
                    i "Yes... I saw this necklet and thought about you. I imagined it would look good on you..."
                    $ fcindy = "smile"
                    c "Thanks... That's nice of you."
                    i "It's just a little something, but I wanted to give you a present."
                    c "I love how thoughtful you are."
                    if ian_cindy < 12:
                        call friend_xp ('cindy') from _call_friend_xp_1104
                        pause 0.5
                    scene cindyroomnight with long
        
            "{image=icon_wits.webp}You're playing with me..." if ian_wits > 7 or ian_wits > 6 and v12_cindy_rel == 1 or ian_wits > 5 and v12_cindy_rel == 2:
                $ renpy.block_rollback()
                $ v12_cindy_rel = 2
                stop music fadeout 2.0
                i "I have the feeling I'm being played with, and honestly, I'm quite tired of having the same conversation over and over again."
                $ fcindy = "serious"
                hide cindybra2
                show cindybra at rig
                with short
                c "Well, excuse me. I can't just snap my fingers and make it all go away. It's not that simple."
                $ fian = "serious"
                c "Besides, you knew what you were signing up for when we started this... thing."
                $ fian = "n"
                "I took a deep breath and fought to control my emotions."
                i "Look... I'm not trying to start a fight. That's not what I want. But I have to set my boundaries."
                $ fcindy = "sad"
                c "Boundaries?"
                i "You know how I feel about you. I've been very clear about it... But you haven't been transparent with me."
                $ fcindy = "serious"
                c "What are you trying to say? Are you accusing me?"
                i "No, I'm not. All I'm saying is that I know what I want."
                i "Do you?"
                $ fcindy = "blush"
                c "I..."
                i "..."
                i "I thought so."
                stop music fadeout 3.0
                hide ianunder with short
                $ ian_look = 2
                "I picked up my clothes and got dressed."
                $ fcindy = "sad"
                hide cindybra2
                show cindybra at rig
                show ian at lef
                with short
                c "Are you leaving?"
                i "Yeah. I don't feel comfortable staying at this moment... And I believe I've been clear about my reasons."
                i "I need the same from you, Cindy. But you can't give something you don't have... Clarity."
                i "If you find it, and want to share it with me... Well, you know where to find me."
                if v12_gift == "cindy":
                    i "Oh, before I leave... I got you something."
                    $ fcindy = "blush"
                    c "You did?"
                    i "Yeah... It's just a little something, but I wanted to show you my appreciation."
                    i "I saw this necklet in a shop window, and I couldn't help but think of you and how great it would look on you."
                    hide cindybra
                    show cindybra2 at rig
                    with short
                    c "And... do you still want me to have it?"
                    i "Yes. That's why I bought it."
                    c "Thank you..."
                    i "Well, then... Bye, Cindy."
                    hide ian with short
                    if ian_will < 2:
                        call will_up() from _call_will_up_13
                else:
                    i "Bye."
                    hide ian with short
                pause 0.5

            "{image=icon_mad.webp}You have the hots for Axel, don't you?":  
                $ renpy.block_rollback() 
                $ ian_cindy_over = 2
                $ v12_cindy_rel = 0
                $ fian = "mad"
                play music "music/tension.mp3" loop
                i "I know what the problem is. You have the hots for Axel, don't you?"
                $ fcindy = "blush"
                i "Don't lie to me... It's obvious what's going on. You're trying to play both sides, is that it?"
                $ fcindy = "mad"
                hide cindybra2
                show cindybra at rig
                with short
                c "What the hell are you talking about? Are you accusing me?"
                i "You haven't denied it. You may be able to lie to Wade, but not to me. I can see right through you..."
                c "What would you know?! You say you love me, but you don't get me at all...!"
                i "I think I'm starting to, but not because you're transparent with me."
                i "I have the feeling you're just using me to scratch an itch after being unsatisfied for so long. And now you're planning to use Axel too, right?"
                c "That's enough! Get out of here!" with vpunch
                call friend_xp ('cindy',-1) from _call_friend_xp_1105
                $ ian_cindy = 1
                $ fian = "furious"
                i "Fine! I can see everything I said is true. You can't even come up with a way to defend yourself."
                c "I don't have to hear this. You're just like Wade."
                c "Scram, now!"
                hide ianunder with short
                stop music
                play sound "sfx/door_slam.mp3"
                with vpunch
                $ fian = "mad"
                "I picked up my clothes and left, swelling with indignation. I had been such a fool."
# cindy cheating
    else:
        i "So you'll become a pro model now?"
        $ fcindy = "smile"
        c "Why not? I was offered the chance and I have what it takes, so..."
        c "It's a fantastic opportunity, like something out of a dream!"
        $ fcindy = "n"
        c "It surely beats working at my dad's office, or getting any other boring desk job..."
        if ian_charisma > 7 or ian_charisma > 6 and ian_chad > 2:
            jump v12cindycheatmenu
        else:
            menu:
                "{image=icon_will.webp}Play it cool" if ian_will > 0:
                    $ renpy.block_rollback() 
                    call willdown from _call_willdown_62
                    jump v12cindycheatcool

                "I don't like it":
                    $ renpy.block_rollback() 
                    jump v12cindycheatno
                
                "What about Wade?":
                    $ renpy.block_rollback() 
                    jump v12cindycheatwade

        menu v12cindycheatmenu:
            "{image=icon_charisma.webp}Play it cool" if ian_charisma > 7 or ian_charisma > 6 and ian_chad > 2:
                $ renpy.block_rollback() 
                label v12cindycheatcool:
                $ fian = "smile"
                i "It does... You're beautiful enough to work as a model, there's no doubt about that."
                $ fcindy = "smile"
                c "So they say!"
                if ian_cindy < 12:
                    call friend_xp ('cindy') from _call_friend_xp_1106
                $ fcindy = "sad"
                c "To be honest, I'm not sure if I'll measure up. I have very little experience, and I've never modeled professionally."
                $ fian = "n"
                c "Wildcats is a top agency, and I'll be surrounded by stunning and seasoned models..."
                menu:
                    "{image=icon_love.webp}You're perfect to me..." if ian_lena_couple == False and ian_alison_love == False and ian_cherry_love == False and ian_holly_love == False:
                        $ renpy.block_rollback() 
                        $ v12_cindy_rel = 3
                        $ fian = "smile"
                        i "Well, you're perfect to me."
                        $ fcindy = "n"
                        c "I appreciate that, but it's still important what they think about me, especially if I'm to cut it in the modeling world..."
                        $ fian = "n"
                        i "What I'm trying to say is... I want more from this relationship. I want you, Cindy."
                        stop music fadeout 2.0
                        $ fcindy = "sad"
                        c "What...?"
                        i "I feel something for you. Something real."
                        jump v12cindypress
                        
                    "{image=icon_friend.webp}You'll do fine":
                        $ renpy.block_rollback() 
                        $ v12_cindy_rel = 2
                        i "I'm sure you'll do fine..."
                        $ fcindy = "n"
                        c "I hope so. I'm excited..."
                        "She was a bit too excited, in my opinion, but I didn't say anything."
                        "I knew Axel had ulterior motives to invite Cindy to such a trip, but pointing my finger at it would only make Cindy get defensive."
                        "After all, I was there mainly for one reason, and I had gotten what I wanted already."
                        "As long as I could keep fucking Cindy, what she did or didn't do wasn't really my business..."
                        stop music fadeout 2.0
                        scene cindyroomnight with long
        
            "I don't like it":
                $ renpy.block_rollback() 
                label v12cindycheatno:
                    i "You seem thrilled about this modeling gig with Axel... quite thrilled."
                c "Well, yeah. As I said, it's a fantastic opportunity."
                i "I'm glad you're excited about it but... Aren't you a bit concerned?"
                $ fcindy = "sad"
                c "Concerned? About what?"
                i "These trips, they can be pretty intense... Are you sure you can handle it?"
                stop music fadeout 1.0
                $ fcindy = "serious"
                hide cindybra
                show cindybra2 at rig
                with short
                play music "music/tension.mp3" loop
                c "What are you trying to say? I can handle myself perfectly well."
                i "In that case, I'm sure you're aware of Axel's reputation. It's obvious he has some ulterior motives when it comes to all of this..."
                c "Oh, yeah? And what makes you think that?"
                i "Come on, Cindy, you're not that naive. I, for one, am not, and neither is Axel."
                i "He meets you one night in a club, offers you free photo sessions, and now he's plugging you into a modeling agency..."
                hide cindybra2
                show cindybra at rig
                with short
                c "Plugging me in? So you think I don't merit it at all?"
                c "That the only reason I got this opportunity is because Axel wants to get in my panties?"
                call friend_xp ('cindy',-2) from _call_friend_xp_1107
                $ fian = "worried"
                i "I didn't say that... But that last thing you said about Axel is true, and you know it."
                c "Even if it was, why are you concerned with my choices? I don't remember asking for your opinion..."
                if ian_lena_sex or ian_alison_sex:
                    c "Besides, it's not like you haven't been fooling around with some skanks out there! What about your reputation, huh?"
                menu v12cindyfight:
                    "{image=icon_love.webp}I care about you" if ian_lena_couple == False and ian_alison_love == False and ian_cherry_love == False and ian_holly_love == False:
                        $ renpy.block_rollback()
                        $ v12_cindy_rel = 3
                        $ fian = "n"
                        i "I'm concerned about your choices because... I care about you, Cindy."
                        $ fcindy = "serious"
                        c "What's that supposed to mean?"
                        i "It means... I feel something for you. Something real."
                        stop music fadeout 2.0
                        $ fcindy = "sad"
                        c "What?"
                        i "I want more from this relationship... I want you, Cindy."
                        label v12cindypress:
                            c "You... Why are you telling me this right now?"
                        $ fian = "sad"
                        i "Why? Because it's how I feel, that's why."
                        c "It's not what you told me before. When we started this... thing."
                        $ fian = "n"
                        i "That was then, and this is now. Things have changed. I..."
                        c "Stop it, Ian... Things are already complicated enough."
                        if ian_lena_dating or ian_alison_dating:
                            c "Besides, aren't you dating someone else already? Don't play with me..."
                        i "Sorry, but I can't just ignore it. I want you to be mine."
                        $ fcindy = "serious"
                        c "That's enough. Please. I can't deal with that right now."
                        call friend_xp ('cindy',-1) from _call_friend_xp_1108
                        $ fian = "sad"
                        i "Will you at least think about what I said?"
                        $ fcindy = "sad"
                        c "Yes, I... I will."
                        $ fian = "n"
                        i "Alright. That's all I ask."
                        stop music fadeout 3.0
                        scene cindyroomnight with long
                        "After that, the mood turned pretty awkward and a bit tense."
                        "I didn't want to make Cindy more uncomfortable, so I decided to take my leave."
                        "It was hard not feeling like an idiot after being shut down like that... But what was I expecting?"
                        "My confession caught Cindy off guard, and no wonder: I hadn't even expected to say those words myself."
                        "What I was clear about at this point, however, was that I wanted Cindy to belong to me."

                    "Do whatever you want":
                        $ renpy.block_rollback()
                        $ v12_cindy_rel = 1
                        $ fian = "serious"
                        i "Do whatever you want... As you said, it's not really my business."
                        $ fcindy = "serious"
                        if ian_lena_dating or ian_alison_dating:
                            c "You're right about that... After all, you're also doing whatever you want."
                        else:
                            c "You're right about that..."
                        stop music fadeout 3.0
                        scene cindyroomnight with long
                        "After that, the mood turned pretty tense and awkward. Cindy was on edge, and I felt the same way..."
                        "She was being unreasonable, but as she said herself, what she did or didn't do wasn't really my business."
                        "I was there mainly for one reason, and I had gotten what I wanted already. It was time to leave."

                    "No need to be a bitch":
                        $ renpy.block_rollback()
                        $ v12_cindy_rel = 0
                        $ ian_cindy_over = 2
                        $ fian = "mad"
                        i "Hey, no need to be a bitch about it."
                        $ fcindy = "mad"
                        c "Excuse me? What did you just call me?"
                        i "What? Aren't you acting a bit bitchy right now?"
                        c "That's enough! Get out of here!" with vpunch
                        call friend_xp ('cindy',-1) from _call_friend_xp_1109
                        $ ian_cindy = 1
                        i "Just like that? You're just proving me right..."
                        c "I don't have to hear this! You're just like Wade. Scram, now!"
                        $ fian= "furious"
                        i "I can't believe this. You really don't know what you want, do you?"
                        i "Fine, I won't waste any more of your time, or mine for that matter."
                        c "Good! Just leave already!"
                        hide ianunder with short
                        stop music
                        play sound "sfx/door_slam.mp3"
                        with vpunch
                        "I picked up my clothes and left, swelling with indignation. Who did she think I was?"
                        $ fian = "mad"
        
            "What about Wade?":
                $ renpy.block_rollback() 
                label v12cindycheatwade:
                    i "And what does Wade think about it?"
                stop music fadeout 2.0
                $ fcindy = "serious"
                c "Why are you bringing up Wade right now? You know we're taking some time apart at the moment..."
                call friend_xp ('cindy',-1) from _call_friend_xp_1110
                i "Well, yeah... What I don't get is why you don't break up with him already. It's obvious you've grown out of that relationship."
                $ fcindy = "sad"
                c "It's not so simple... I don't want to hurt him, so I'm trying to handle things with caution..."
                i "Leaving on an island cruise with Axel to shoot bikini pictures doesn't seem too cautious if you ask me..."
                play music "music/tension.mp3" loop
                $ fcindy = "serious"
                c "Well, I don't remember asking for your opinion. Why are you concerned with my choices to begin with?"
                jump v12cindyfight
            
    stop music fadeout 2.0
    scene street2night with long
    $ ian_look = 2
    pause 1
    jump v12iannighthome

# ALISON ######################################################################################################################################################
######################################################################################################################################################
label gallery_CH12_S04:
    if _in_replay:
        call setup_CH12_S04 from _call_setup_CH12_S04

label v12alisondate:
    $ v12_alison_sex = 0
    $ fian = "smile"
    i "Alright, time to go meet Alison..."
    stop music fadeout 2.0
    if ian_alison_fuck:
        jump v12alisonfucking
    else:
        jump v12alisondating
# FUCK BUDDIES
label v12alisonfucking:
    $ falison = "n"
    $ alison_look = "bimbo"
    $ alison_makeup = 1
    scene alisonhome with long
    play sound "sfx/doorbell.mp3"
    pause 0.5
    play music "music/alisons_theme.mp3" loop
    show alison at rig with short
    a "Coming!"
    show ian at lef3 with short
    i "Hey..."
    show ian at lef with move
    i "Wow, what's with those clothes?"
    a "What's wrong with them? I needed some summer clothes and did a bit of shopping the other day..."
    menu:
        "{image=icon_charisma.webp}I like your bimbo look" if alison_blonde > 1 and ian_charisma > 6:
            $ renpy.block_rollback()
            $ fian = "confident"
            i "Well, I for one love your bimbo look..."
            $ falison = "smile"
            a "A bimbo? That's what I look like to you?"
            i "Blonde hair, tall, big boobs, classy and very sexy... Yeah, you look like a bimbo alright."
            $ falison = "flirt"
            a "So that's what you like, huh? I never knew..."
            if ian_chad > 4:
                i "I didn't either, until you started to turn into one. And now all I want is to crush your pussy so bad."
            else:
                i "I didn't either, until you started to turn into one. And now all I want is to fuck you so bad..."
            if ian_lust < 8:
                call xp_up ('lust') from _call_xp_up_964
            a "Look at you... Seems I really turn you on, don't I?"
            i "Wanna come and check?"
            $ alison_blonde = 3

        "You look so hot":
            $ renpy.block_rollback()
            $ fian = "confident"
            if ian_chad > 3:
                i "There's nothing wrong with them. You look so fucking hot..."
            else:
                i "There's nothing wrong with them. You look so hot..."
            $ falison = "flirt"
            if alison_blonde == 1:
                a "Really? I thought you didn't like my new look..."
                i "Well, after seeing you right now, I've changed my mind... But now I want to see you out of those clothes."
            else:
                a "I had a feeling you might approve of my choice of attire..."
                i "You know me very well indeed. But now I want to see you out of those clothes..."
            if ian_charisma < 8:
                call xp_up ('charisma') from _call_xp_up_965
                pause 0.5
            $ alison_blonde = 2

        "{image=icon_sad.webp}You look... different":
            $ renpy.block_rollback()
            i "Well, they're... different."
            $ falison = "sad"
            if alison_blonde > 1:
                a "What? I thought you said you liked my new look... Do I look that weird?"
                $ fian = "n"
                i "No, I mean... You look sexy, but quite different from your usual self."
            else:
                a "I knew it... Jeremy said I looked so hot, but I guess it's {i}too{/i} hot, right?"
                $ fian = "n"
                i "No, I mean... You look sexy, but quite different from your usual self."
                a "Yeah, you already told me you don't like my new look..."
                $ falison = "n"
                a "You must be the only one, though. Everyone told me they loved it, especially Jeremy..."
            $ fian = "smile"
            i "I simply prefer a more natural look, I think it suits you better. But you're still very hot, of course..."
            $ falison = "flirt"
            a "I was hoping you'd say that..."
            $ alison_blonde = 1
            if ian_wits < 8:
                call xp_up ('wits') from _call_xp_up_966
                pause 0.5
    
    play music "music/sex_slow.mp3" loop
    scene v12_alison1
    with long
    "Alison wrapped her arms around me and planted a kiss on my lips."
    "Soon our tongues met, and our kisses turned more and more steamy. Alison's heat was ramping up, and mine with hers..."
    a "I needed this... You have no idea."
    i "I can tell how horny you are..."
    a "Are you gonna do something about it?"
    i "Come, let's go to the bedroom."
    scene v12_alison2_blonde
    with long
    pause 1
    "Seeing Alison so eager and excited was such a turn-on. I loved this naughty side of her I had discovered only recently..."
    if ian_alison_sex:
        "I had lost interest in her after our first experiences together, but time had transformed Alison into a woman worth considering."
    else:
        "I never had too much interest in her beyond a simple friendship, but time had transformed Alison into a woman worth considering."
    "I took my time fondling her succulent body and playing with my tongue on her neck, our clothes coming off one piece at a time."
    "My hands kneaded her boobs and squeezed her thighs, caressing her pubis and inner thighs, but never reaching all the way down to her slit."
    "I wanted to fan her desire even more, eager to reap the rewards."
    a "You're mean... Why are you teasing me this much?"
    i "Because you're so hot when you get all turned on and slutty... Like a bitch in heat."
    a "See? You're too mean..."
    i "I think you like it."
    if ian_lust < 10:
        call xp_up ('lust') from _call_xp_up_967
        pause 0.5
    scene v12_alison4_blonde
    with long
    pause 1
    "Alison scooted between my legs and held my cock, looking at me with a flirty smile."
    a "What if I tease you now?"
    "I looked at her as she parted her lips and enveloped my glans, stimulating it pleasantly."
    i "I don't think I'm gonna complain about it."
    a "Does that mean you like it?"
    menu:
        "{image=icon_charisma.webp}Take a picture" if ian_charisma > 6 or v9_alison_trip or v6_alison_pics > 0:
            $ renpy.block_rollback()
            if alison_blonde:
                $ v12_alison_pics = "blonde"
            else:
                $ v12_alison_pics = True
            i "I do... You look so hot. In fact... Let me take a picture."
            a "You want to take a picture of me sucking your cock?"
            if v9_alison_trip or v6_alison_pics > 0: # after breakup
                i "Yeah. You posed for me before..."
            elif alison_voyeur or v11_alison_voyeur:
                i "Yeah. You let Jeremy do it, after all. Why not me?"
            else:
                i "Yeah..."
            a "You dirty boy..."
            i "I'll take that as a yes."
            show v12_alison4_blonde_phone with long
            "I wasn't sure if she'd be down for it, but I was counting on her being turned on enough. The bet paid off."
            "I pulled up my phone camera and snapped a few pics."
            a "Do I look hot?"
            i "Hell yeah... Can you feel how hard I am right now?"
            a "Hmm... Yeah..."
            i "I want to see your boobs around my cock now..."
            a "I knew you were gonna ask for that."

        "Let me feel your boobs":
            $ renpy.block_rollback()
            i "I do... But you know what I like even more?"
            a "Let me guess: my boobs."
            i "Bingo. I want to feel them..."

    scene v12_alison5_blonde
    with long
    pause 1
    "Alison wrapped her breasts around my cock, cushioning it when she pressed them together."
    if v12_alison_pics:
        play sound "sfx/camera.mp3"
        with flash
        i "Damn, that's so hot..."
    else:
        i "Mhhh, nice..."
    a "You're a bit obsessed with my boobs, aren't you?"
    i "And you're fueling my obsession, dressing like that... I bet all the guys at the office are staring at your cleavage."
    a "Maybe... But they don't get to fuck my boobs like you do."
    scene v12_alison6_animation_blonde 
    if v12_alison_pics:
        show v12_alison6_hand
    with short
    pause 4
    "Alison began tit-fucking with a lewd smile on her face."
    if v12_alison_pics:
        a "Are you still taking pictures?"
        i "I might as well film this..."
        a "I'm starting to feel like some sort of porn star..."
        if alison_blonde == 3:
            i "My bimbo porn star... That's so fucking hot."
        else:
            i "That sounds pretty hot, actually."
        a "Yeah? Do I look hot fucking you with my tits like this?"
        if v12_alison_pics:
            hide v12_alison6_hand with short
        i "You know you do. Your boobs are made to be fucked."
        $ ian_alison_pics.append("v12_alison6_phone")
    else:
        i "Damn... You look sexy as hell when you do this."
        a "You seem to really like it... Does it really feel that good?"
        i "You know it does... Your boobs are made to be fucked."
    a "And your cock feels so hard between them... I can't wait to feel it inside me, fucking my pussy like no other..."
    if v9askanalalison: # ch 9 trip
        i "Alright. I'll give you what you want..."
    else:
        menu:
            "{image=icon_lust.webp}I'd rather fuck your ass" if ian_lust > 6:
                $ renpy.block_rollback()
                i "Actually, I'd rather fuck your ass..."
                jump v12alisontryanal

            "Give it to Alison":
                $ renpy.block_rollback()
                i "Alright. I'll give you what you want..."

    label v12alisonfucking2:
        play sound "sfx/ah6.mp3"
    scene v12_alison7_blonde
    with long
    pause 1
    if v9askanalalison and v9_alison_trip == False:
        if v9_alison_anal:
            "I was a bit disappointed at not being unable to destroy Alison's ass... Lewd as she was, she still had her limitations."
            "I was still determined to enjoy the rest of her, though."
        else:
            "I thought Alison might let me try anal, but I could still enjoy the rest of her."
        a "Ahhh, yes...! Your cock feels perfect in my pussy..."
    else:
        a "Ahhh, yes...! Your cock feels perfect..."
    i "And your pussy's so wet... It seems it's true you were eager for this."
    "I teased Alison's body while I continued to fuck her, fondling her boobs, pinching her nipples and kissing her neck."
    "She groaned and squirmed under my caresses, and I felt her pussy squeezing my dick as she moved her hips urgently."
    a "Ian...! No one fucks me like you do... You make me so crazy...!"
    "Alison seemed to be at the brink of her pleasure, but I was focused on enjoying myself."
    scene v12_alison8_blonde
    with long
    pause 1
    "I wanted to let all my lust loose, dump it all into Alison, thrust after thrust."
    play sound "sfx/oh3.mp3"
    a "Ahhh, Ian...! So hard...!" with vpunch
    "My fingers dug into the soft flesh of her hips as I kept up my pistoning motion, continuing to elicit loud moans from Alison."
    if ian_athletics < 10:
        call xp_up ('athletics') from _call_xp_up_968
    a "Yeah, fuck me...! Fuck me as hard as you want!"
    "The loud and rhythmic slaps of my avid appetite filled the room, mixed with grunts and whimpers."
    "Alison was completely devoted to me. She was a vessel for my lust, and I intended to fill her with it until it overflowed."
    menu:
        "Cum inside Alison":
            $ renpy.block_rollback()
            $ v12_alison_sex = 2
            i "Fuck yes! I'm gonna cum!"
            a "Yes! Give it to me! Give it all to me, Ian!"
            i "Yes..!!{w=0.5}{nw}"
            with flash
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            with vpunch
            show v12_alison8_cum with long
            pause 1
            a "Oh, fuck..."
            scene v11_alison4 
            if alison_blonde:
                show v11_alison4_blonde
            show v11_alison4_cum1   
            with long
            pause 1
            a "You came inside of me again... There's a lot..."
            if ian_cherry_dating == False and v12_minerva_sex == False:
                i "I know how much you like it, so I was saving up for you."
                a "Mhhh, really...?"

        "Cum on Alison's tits":
            $ renpy.block_rollback()
            $ v12_alison_sex = 1
            "I was on the verge, and I took matters into my own hands to get the satisfaction I was craving."
            scene v12_alison9_blonde
            with long
            "I pulled out and began jacking off vigorously."
            i "I'm gonna cum... Open wide! Ahh, yes!!"
            show v9_alison15_cum1 with flash
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            with vpunch
            hide v9_alison15_cum1
            show v9_alison15_cum2 
            with fps
            pause 1
            "I continued to jerk myself off, riding the jolts of pleasure and shooting my load all over Alison."
            i "{i}Whew...!{/i} Damn, finally..."
            "I looked down to watch the results of my lust, painting Alison's skin with a lewd color."
            i "Hell, you look so sexy..."
    # end
    if ian_lust < 8:
        call xp_up ('lust') from _call_xp_up_969
        pause 0.5
    stop music fadeout 2.0
    $ falison = "flirt"
    $ fian = "confident"
    scene alisonhomenight with long
    show iannude at lef
    show alisonnude at rig
    if v12_alison_sex == 2:
        show alison_cum3 at rig
    else:
        show alison_cum1 at rig
        show alison_cum2 at rig
    with short
    a "Did you like it?"
    if v12_alison_sex == 2:
        i "Do you really need to ask me? You have the answer leaking out of your pussy..."
    else:
        i "Do you really need to ask me? You have the answer all over your boobs..."
    if ian_lena_couple:
        a "Yeah... We have such great chemistry, don't you think? Too bad you're already dating the model..."
        $ fian = "n"
        i "Yeah, too bad... That's why this thing we have must remain between you and me only, got it?"
        $ falison = "n"
        a "Yeah, yeah, I got it. Don't worry, the last thing I need right now is to bring drama into my life."
        $ fian = "smile"
        i "We're on the same page on that."
    else:
        a "Yeah... We have such great chemistry, don't you think?"
        i "More than expected, yeah... Who knew you'd become so hot?"
    a "How long have we been going at it? It got pretty late..."
    a "Wanna stay over? I can cook some dinner."
    $ fian = "n"
    if tournament:
        i "No, thanks... I need to get up early tomorrow. It's the first round of the MMA tournament..."
    else:
        i "No, thanks... I still need to pack my bag for the trip. And I have to get up early tomorrow..."
    a "Of course. You'll take a shower at least, won't you?"
    $ fian = "smile"
    i "I'll wait until I get home."
    $ fian = "confident"
    i "I had a lot of fun today too. I'll see you when I get back from the beach."
    a "Of course... Hit me up whenever you're free."
    scene alisonhomenight with long
    scene street2night with long
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH12_S04")
    pause 1
    jump v12iannighthome 

# DATING ALISON ################################################################################################################################
label v12alisondating:
    $ falison = "smile"
    $ alison_look = 1
    $ alison_makeup = 0
    if alison_blonde:
        $ alison_makeup = 1
    scene alisonhome with long
    play sound "sfx/doorbell.mp3"
    pause 0.5
    show alison at rig with short
    a "Coming!"
    show ian at lef3 with short
    i "Hey..."
    show ian at lef with move
    $ falison = "n"
    a "How was your day? Mine was pretty stressful..."
    menu:
        "Why?":
            $ renpy.block_rollback()
            $ fian = "n"
            i "Why? Does work keep piling up?"
            a "Yeah, there's no end to it... I swear, that office is a mess."
            $ falison = "serious"
            a "And now half of the staff is on vacation... Well, I can't juggle any more responsibility! If they paid me extra for it, at least..."
            $ falison = "sad"
            a "I would've liked to join your little beach trip, but I can't afford to. Not right now..."
            a "Sometimes I feel the only reason the company hasn't shut down is because of me."
            i "You keep complaining about it... Why don't you search for another job? You don't seem happy at all with this one."
            if ian_wits < 8:
                call xp_up ('wits') from _call_xp_up_970
            a "If it only were that easy... If I find something better I'll make the jump, but right now I need the money."
            a "I want to move out of my parents' place as soon as possible. It's no fun going back to living with them after having been independent..."
            a "But rents are sky-high now, so I need to save, and I need to work. If my old boss paid me what he owes me..."
            i "Do you think he will?"
            a "At this point? I would be very surprised. As far as I know, he's still tied up in legal battles due to his company's fraudulent practices."
            a "I'm sure I look awful. I haven't gotten too much sleep this week..."
            if alison_blonde > 1:
                a "I want to look hot for you, I really do. I've just had to deal with some extra stuff this week."
                menu v12alisonblondemenu:
                    "{image=icon_charisma.webp}I love your bimbo look" if alison_blonde > 1 and ian_charisma > 6:
                        $ renpy.block_rollback()
                        $ fian = "confident"
                        i "It's alright... I really liked where that was going. I for one love your bimbo look..."
                        if alison_blonde == 3:
                            $ falison = "flirt"
                            a "Bimbo look... You used that word the other day, too. Is that what I look like to you?"
                        else:
                            $ falison = "smile"
                            a "A bimbo? That's what I look like to you?"
                        i "Blonde hair, tall, big boobs, classy and very sexy... Yeah, you look like a bimbo alright."
                        $ falison = "smile"
                        a "So that's what you like, huh? I never knew..."
                        if ian_chad > 4:
                            i "I didn't either, until you started to turn into one. And now all I want is to crush your pussy so bad."
                        else:
                            i "I didn't either, until you started to turn into one. And now all I want is to fuck you so bad..."
                        if ian_lust < 8:
                            call xp_up ('lust') from _call_xp_up_971
                        a "Look at you... Seems I really turn you on, don't I?"
                        i "Wanna come and check?"
                        $ falison = "n"
                        a "I'd love to..."
                        $ alison_blonde = 3

                    "You look good":
                        $ renpy.block_rollback()
                        $ alison_blonde = 2
                        $ fian = "smile"
                        i "Hey, don't beat yourself up about it. You can't be in tip-top shape all the time."
                        $ fian = "confident"
                        i "I know how sexy you can be... And no matter what clothes you wear, I'm gonna remove them at some point."
                        $ falison = "flirt"
                        a "You're so sweet... I was eager to see you, you know?"
                        if ian_charisma < 8:
                            call xp_up ('charisma') from _call_xp_up_972

                    "Stop worrying about it":
                        $ renpy.block_rollback()
                        $ alison_blonde = 1
                        i "Just stop worrying about that... You have more important things to deal with, it seems."
                        a "But I thought you liked my new look..."
                        $ fian = "smile"
                        i "I do, but it's not that important to me. You also look really beautiful with a more natural style."
                        $ falison = "n"
                        a "You're so sweet... I was eager to see you, you know?"
                        if ian_wits < 8:
                            call xp_up ('wits') from _call_xp_up_973

            elif alison_blonde == 1:
                a "I thought you didn't like my new look anyway."
                i "I simply prefer a more natural look. I think it suits you better, that's all."
                $ falison = "n"
                a "I guess that's a compliment in a way, too..."
                $ fian = "smile"
                i "It is."
                a "I was eager to see you, you know..."
            elif ian_alison_love:
                $ fian = "smile"
                i "You look alright to me..."
                $ falison = "n"
                a "Really? Thanks, I appreciate it... I was really eager to see you, you know..."
            else:
                i "It's alright... You seem to be pretty stressed, judging by what you say."
                $ falison = "n"
                a "Yeah... Thankfully, you're here today. I was really eager to see you..."

        "{image=icon_sad.webp}I can see that..." if alison_sexy > 0 or alison_blonde > 0:
            $ renpy.block_rollback()
            $ fian = "n"
            i "Yeah, I can see that..."
            $ falison = "sad"
            a "You can see it? Why? Do I look that awful?"
            a "I haven't slept much this week...  Can you tell if my dark circles are showing? I hoped they weren't..."
            i "I don't think they are... But I can see you went back to your usual, plain clothing style."
            a "Is it that plain?"
            if alison_sexy == 2:
                i "I was getting used to your sexier look. You looked really hot in it..."
            else:
                "I was getting used to your sexier look... Honestly, you looked really nice."
            a "And now I don't?"
            if ian_alison > 0:
                call friend_xp ('alison',-1) from _call_friend_xp_1111
            a "I mean, I've been so caught up with work lately that I haven't really given my clothing much thought."
            a "After all, why bother when I'm only going back and forth between the office and home?"
            if alison_blonde == 1:
                a "Besides, I thought you didn't like my change of style..."
                i "I prefer your natural hair, that's true."
            elif alison_blonde > 1:
                i "Didn't people at the office like your new look?"
                a "Oh, they did... But I don't care too much about their opinion, to be honest. I want to look hot for you, though."
                jump v12alisonblondemenu
            i "So work seems piling up, huh? Have you thought about finding some other job? You don't seem too happy with this one."
            a "I want to move out of my parents' place as soon as possible. It's no fun going back to living with them after having been independent..."
            a "But rents are sky-high now, so I need to save, and I need to work. If my old boss paid me what he owes me..."
            i "Do you think he will?"
            a "At this point? I would be very surprised. As far as I know, he's still tied up in legal battles due to his company's fraudulent practices."
            i "Sounds like you're in quite a stressful situation, yeah..."
            a "You think? I would've liked to join your little beach trip, but I can't afford to. Not right now..."
            $ falison = "n"
            a "Thankfully, you're here today. I was really eager to see you..."

        "{image=icon_lust.webp}Cut her off" if ian_alison_love == False:
            $ renpy.block_rollback()
            if ian_chad < 4:
                $ ian_chad += 1
            $ fian = "n"
            "I wasn't in the mood to hear Alison's complaints about her daily life. That wasn't the reason I came to see her..."
            $ fian = "confident"
            i "Well, I'm here to help you relieve you of some of that stress."
            $ falison = "flirt"
            a "Is that so...?"
            i "Why did you invite me to come if not for that?"
            a "Maybe you're right..."

    show alison at centerrig with move
    "Alison leaned into me and gave me a soft kiss on the cheek. Then another one."
    play music "music/alisons_theme.mp3" loop
    if alison_blonde:
        scene v12_alison1 
        show v12_alison1_green
    else:
        scene v10_alisonian1
        show v10_alisonian1_alison
        show v10_alisonian1_base
    with long
    "Her playful kisses moved closer to my lips, and I turned my head to meet hers."
    "I felt a small shiver run through Alison's body, and she pressed her lips harder against mine."
    "Soon our tongues met, and our kisses turned more and more steamy. Alison's heat was ramping up, and mine with hers..."
    a "I needed this... You have no idea."
    if ian_alison_love:
        i "Me too... You're so sexy... I can't get my hands off of you."
        a "Come... let's go to the bedroom."
    else:
        i "Come, let's go to the bedroom."
    if alison_blonde:
        scene v12_alison2_blonde
    else:
        scene v12_alison2
    with long
    pause 1
    "My fingers unbuttoned Alison's blouse skillfully, revealing bit by bit her soft and heavy breasts."
    "I took my time fondling her body and playing with my tongue on her neck as our clothes continued to come off."
    "My hands kneaded her boobs and squeezed her thighs, caressing her pubis and inner thighs, but never reaching all the way down to her slit."
    "I wanted to fan her desire even more, eager to reap the rewards."
    a "You're mean... Why are you teasing me this much?"
    if ian_alison_dom:
        i "Because you're so hot when you get all turned on and slutty... Like a bitch in heat."
    else:
        i "Because you're so hot when you get all turned on..."
    a "You're definitely succeeding at that..."
    if alison_blonde:
        scene v12_alison3_blonde
    else:
        scene v12_alison3
    with long
    pause 1
    "Alison turned around and pushed me down to the bed, suddenly taking the lead."
    "She climbed on top of me and went straight to my lips, planting a deep kiss full of lewdness."
    "I also felt her desire in her hips, wiggling impatiently."
    "My hard dick was trapped under her belly, alluringly close to her sex..."
    a "I can't wait to feel you inside of me... I love your cock so much."
    menu v12alisondatingmenu:
        "{image=icon_lust.webp}Not yet" if ian_lust > 6 or ian_alison_dom or ian_chad > 3:
            $ renpy.block_rollback()
            stop music fadeout 2.0
            if v12_alison_sex == 1:
                i "Hold off on that. I want to take my time and enjoy all of you."
            else:
                i "Not yet... I want to take my time and enjoy all of you."
            a "All of me... What do you want?"
            play music "music/sex_raunchy.mp3" loop
            $ config.menu_include_disabled = False
            $ greyed_out_disabled = True
            menu v12alisonsexmenu1:
                "Blowjob":
                    $ renpy.block_rollback()
                    if persistent.include_disabled:
                        $ config.menu_include_disabled = True
                    $ greyed_out_disabled = False
                    i "How about a blowjob?"
                    a "That's what you really like, huh...? Alright."
                    if alison_blonde:
                        scene v12_alison4_blonde
                    else:
                        scene v12_alison4
                    with long
                    pause 1
                    "Alison granted my request without qualms, scooting between my legs and holding my cock."
                    "I looked at her as she parted her lips and enveloped my glans, stimulating it pleasantly."
                    i "Use your tongue too..."
                    a "Who's impatient now?"
                    menu:
                        "{image=icon_charisma.webp}Ask to take a picture" if ian_charisma > 6 or v9_alison_trip or v6_alison_pics > 0:
                            $ renpy.block_rollback()
                            if alison_blonde:
                                $ v12_alison_pics = "blonde"
                            else:
                                $ v12_alison_pics = True
                            i "Hey... Can I take a picture of you like this?"
                            a "You want to take a picture of me sucking your cock?"
                            if v9_alison_trip or v6_alison_pics > 0:
                                i "Yeah. You posed for me before..."
                            else:
                                i "Yeah..."
                            a "You dirty boy... Alright, but just one."
                            if alison_blonde:
                                show v12_alison4_blonde_phone
                            else:
                                show v12_alison4_phone
                            with long
                            i "Great..."
                            "I wasn't sure if she'd be down for it, but I was counting on her being turned on enough. The bet paid off."
                            "I pulled up my phone camera and snapped a few pics."
                            a "Are you still taking pictures?"
                            i "You look so damn hot... I love capturing you like this. Can you feel how hard I am right now?"
                            a "Hmm... Yeah..."
                            i "I want to see your boobs around it..."

                        "Let me feel your boobs":
                            $ renpy.block_rollback()
                            i "Let me feel your boobs now..."

                    a "I knew you were gonna ask for that."
                    jump v11alisonboobjob

                "Boobjob":
                    $ renpy.block_rollback()
                    if persistent.include_disabled:
                        $ config.menu_include_disabled = True
                    $ greyed_out_disabled = False
                    i "How about one of your amazing boobjobs?"
                    a "Somehow, I knew you were gonna ask for that..."
                    label v11alisonboobjob:
                        if alison_blonde:
                            scene v12_alison5_blonde
                        else:
                            scene v12_alison5
                        with long
                        pause 1
                    "Alison wrapped her breasts around my cock, cushioning it when she pressed them together."
                    if v12_alison_pics:
                        play sound "sfx/camera.mp3"
                        with flash
                        i "Damn, that's so hot..."
                    else:
                        i "Mhhh, nice..."
                    a "You're obsessed with my boobs, aren't you?"
                    if ian_lust < 8:
                        call xp_up ('lust') from _call_xp_up_974
                        pause 0.5
                    if alison_blonde:
                        scene v12_alison6_animation_blonde
                    else:
                        scene v12_alison6_animation
                    if v12_alison_pics:
                        show v12_alison6_hand
                    with short
                    pause 3
                    if v12_alison_pics:
                        i "I might as well film this..."
                        a "If you show it to someone, I'll kill you."
                        "Despite her threats, Alison continued tit-fucking me with a lewd smile on her face."
                        a "How do I look?"
                        if v12_alison_pics:
                            hide v12_alison6_hand with short
                        pause 3
                        i "Sexy as hell..."
                        $ ian_alison_pics.append("v12_alison6_phone")
                    else:
                        "Alison began tit-fucking with a lewd smile on her face."
                        i "Damn... You look sexy as hell when you do this."
                    a "You seem to really like it... Does it really feel that good?"
                    if ian_alison_dom or ian_chad > 2:
                        i "You know it does. Your boobs are made to be fucked."
                    elif ian_alison_love:
                        i "You know it does. Your boobs are so soft. No one else can make me feel like this..."
                    else:
                        i "It feels the best... Your boobs are so soft."
                    a "And your cock feels so hard between them... I can't wait to feel it inside me, fucking my pussy like no other..."
                    menu:
                        "Cum on Alison's tits":
                            $ renpy.block_rollback()
                            label v12cumalisontits:
                                $ v12_alison_sex = 1
                                if ian_chad < 5:
                                    $ ian_chad += 1
                            i "Too late, I'm about to burst!"
                            a "What, really?!"
                            if alison_blonde:
                                scene v12_alison9_blonde
                            else:
                                scene v12_alison9
                            show v12_alison9_sad
                            with long
                            pause 1
                            with long
                            "I was on the verge, and I took matters into my own hands to get the satisfaction I was craving."
                            i "Open wide! Ahh, yes!!"
                            show v9_alison15_cum1 with flash
                            with vpunch
                            pause 0.6
                            with vpunch
                            pause 0.6
                            with vpunch
                            hide v9_alison15_cum1
                            show v9_alison15_cum2
                            with fps
                            pause 1
                            stop music fadeout 4.0
                            "I continued to jerk myself off, riding the jolts of pleasure and shooting my load all over Alison."
                            i "{i}Whew...!{/i} Damn, that was a nice one."
                            "I looked down to watch the results of my lust, painting Alison's skin with a lewd color."
                            i "Hell, you look so sexy..."
                            if ian_lust < 10:
                                call xp_up ('lust') from _call_xp_up_975
                                pause 0.5
                            $ falison = "blush"
                            $ fian = "confident"
                            scene alisonhome
                            show iannude at lef
                            show alisonnude at rig
                            show alison_cum1 at rig
                            show alison_cum2 at rig
                            with long
                            a "Well now... I need to shower again."
                            i "Not my fault your boobs feel as good as a pussy..."
                            a "That's nice and all, but who's gonna take care of {i}my{/i} pussy now?"
                            $ fian = "n"
                            if v11_alison_condom == False or ian_alison_dom:
                                i "I would... if you didn't make me wear a condom."
                                $ falison = "serious"
                                a "Again with that? You know I'd prefer to go without too, but we can't."
                                i "You know I'll pull out, right? It's not like I can't control myself."
                                a "It's still risky! Especially when I'm ovulating!"
                                i "I see. That's where that mood swing is coming from..."
                                $ falison = "mad"
                                a "Mood swing?! It's you who's acting like a jerk for no reason!"
                                $ fian = "serious"
                                i "The fact that I'll be forced to wear a rubber each time I wanna have sex with you isn't reason enough?"
                                a "It's not like you don't get to feel good at all! Look, you just came all over me!"
                                i "Yeah, you were complaining about that just now."
                                if v11_alison_condom == False:
                                    a "That's twice you do this to me in a row... The other day, too..."
                                else:
                                    a "Last time you had no problem with wearing one, and suddenly now you do?"
                                i "Sorry that I prioritize my pleasure from time to time. I didn't realize I wasn't allowed to."
                                $ falison = "sad"
                                a "That's not it... Look, let's drop it for now, okay? Last thing I want right now is a fight with you."
                                if ian_alison > 2:
                                    call friend_xp ('alison',-3) from _call_friend_xp_1112
                                elif ian_alison > 1:
                                    call friend_xp ('alison',-2) from _call_friend_xp_1113
                                elif ian_alison > 0:
                                    call friend_xp ('alison',-1) from _call_friend_xp_1114
                            else:
                                i "I will... Just not right now."
                                i "Besides, you know it doesn't feel nearly as good wearing a condom..."
                                $ falison = "sad"
                                a "I know, I'd prefer to go without too, but you know we can't."
                                i "I won't cum inside. I can pull out anytime, you know I have it under control..."
                                a "It's still risky. Especially when I'm ovulating..."
                                $ fian = "serious"
                                i "So that's it, then? I'll have to wear a condom every time I wanna have sex with you now?"
                                a "Why are you making such a big deal out of it? It's not like you can't feel good at all..."
                                a "Look, you just came all over me!"
                                i "Yeah, you were complaining about that just now."
                                a "I wasn't complaining...! Look, let's drop it for now, okay? I don't want to start a fight..."
                                call friend_xp ('alison',-1) from _call_friend_xp_1115
                            $ fian = "n"
                            i "Alright..."
                            scene alisonhomenight with long
                            if ian_chad > 3:
                                "The mood was a bit tense after that... I ended up leaving with a sour taste in my mouth, but at least I got to cum."
                            else:
                                "The mood was a bit tense after that... I ended up leaving with a sour taste in my mouth."
                            scene street2night with long
                            pause 1
                            jump v12iannighthome

                        "Give it to Alison":
                            $ renpy.block_rollback()
                            i "Let's not wait... I want to fuck you too."
                            $ falison = "flirt"
                            scene alisonhome
                            show iannude at lef
                            show alisonnude at rig
                            with long
                            a "Wait, let me get a condom..."
                            hide alisonnude with short
                            jump v12alisoncondom1

                "{image=icon_lust.webp}Anal" if v9askanalalison == False and ian_lust > 5:
                    $ renpy.block_rollback()
                    i "You know what...? I'd really like to enjoy all of you, all your holes..."
                    a "Huh?"
                    label v12alisontryanal:
                        $ v9askanalalison = True
                        $ fian = "confident"
                        $ falison = "sad"
                        scene alisonhome
                        show iannude at lef
                        show alisonnude at rig
                        with long
                    if ian_alison_fuck == False:
                        i "I mean, I would like to try your ass too..."
                    if v8_alison_sext == 3:
                        a "Anal sex? No, thanks... I already told you I'm not into it."
                        a "Besides, we don't have any lube, do we?"
                    else:
                        a "Anal sex? No, thanks..."
                        $ fian = "sad"
                        i "How come?"
                        a "I'm not into it. And we don't have any lube, do we?"
                    menu:
                        "{image=icon_charisma.webp}Insist" if ian_charisma > 5:
                            $ renpy.block_rollback()
                            $ v9_alison_anal = True
                            if ian_chad < 5:
                                $ ian_chad += 1
                            $ fian = "smile"
                            i "But have you tried it before?"
                            $ falison = "n"
                            a "Yeah, a couple of times... It never worked out."
                            $ fian = "confident"
                            i "But you've never tried it with {i}me{/i}."
                            $ falison = "sad"
                            a "I couldn't do it with Milo, and you're quite bigger than him... I don't think we'll get anywhere!"
                            a "And about the lube..."
                            i "Don't worry about that. I bet you're really soaked down there..."
                            $ falison = "blush"
                            a "I don't know about this..."
                            if ian_alison_fuck:
                                i "Come on. Do it for me."
                            else:
                                i "Come on. Do it for me. This way I don't need to wear a condom..."
                            a "Jeez, alright..."
                            hide alisonnude with short
                            "Alison lay down on the bed, her back turned to me."
                            a "Be careful, okay?"
                            show iannude at truecenter with move
                            i "Yeah, yeah. Let's go..."
                            play sound "sfx/pain.ogg"
                            scene v9_alison20
                            if alison_blonde:
                                show v9_alison20_blonde 
                            with vpunch
                            a "Ahhh!! Ouch!"
                            "Alison screamed when I pushed my cock into her asshole."
                            "I had lubricated the tip with her juices, but when I pressed it down it felt very tight, so I had to force it a bit..."
                            "Seems it didn't feel like just \"a bit\" to Alison."
                            "She pushed me over and jumped out of the bed."
                            $ fian = "worried"
                            $ falison = "mad"
                            scene alisonhome
                            show iannude at lef
                            show alisonnude at rig
                            with long
                            a "Ouch, that hurt! I told you to be careful!"
                            call friend_xp('alison', -2) from _call_friend_xp_1116
                            i "I'm sorry..."
                            $ falison = "serious"
                            a "No, I told you I didn't like it... I shouldn't have let you convince me."
                            "Damn, it seemed like anal was definitely off the menu."
                            i "You're right, I apologize. I just..."
                            $ falison = "n"
                            a "It's okay. Now we know for sure we can't do it, but there's plenty of other stuff we can do..."
                            $ fian = "smile"
                            i "Yeah."
                            if ian_alison_fuck:
                                jump v12alisonfucking2
                            jump v12alisonsexmenu1

                        "Let it be":
                            $ renpy.block_rollback()
                            $ fian = "smile"
                            i "Oh, alright, if that's the case."
                            $ falison = "n"
                            a "It's just it really hurt the times I tried with Milo. We could never get anywhere."
                            a "And you're much bigger than him, so... I don't think that would go well!"
                            i "As I said, no problem."
                            if ian_charisma < 6:
                                call xp_up ('charisma') from _call_xp_up_976
                            if ian_alison_fuck:
                                jump v12alisonfucking2
                            jump v12alisonsexmenu1

        "Do I have to wear a condom?" if v12_alison_sex == 0:
            $ renpy.block_rollback()
            $ v12_alison_sex = 1
            $ fian = "n"
            $ falison = "n"
            i "Does that mean I don't have to wear a condom?"
            if v11_alison_condom:
                a "You know you have to..."
            else:
                a "Unfortunately, you have to..."
            i "I'll pull out. You know I will."
            a "As tempting as that is, you can't. Besides, today is a risky day..."
            a "Come on, be reasonable."
            jump v12alisondatingmenu

        "{image=icon_love.webp}I want to be inside you!" if ian_alison_love:
            $ renpy.block_rollback()
            $ v12_alison_sex = 2
            "I pressed her against my body and kissed her deeply."
            i "I want to be inside of you too, Alison...!"
            "Alison moved her hips, rubbing my shaft against her wet slit. If she only moved a bit higher..."
            a "Oh, fuck... Wait... I'll get a condom."
            $ falison = "flirt"
            $ fian = "n"
            scene alisonhome
            show iannude at lef
            with long
            jump v12alisoncondom1

        "{image=icon_friend.webp}Give it to Alison":
            $ renpy.block_rollback()
            $ v12_alison_sex = 2
            i "If that's the case... I'll have to give it to you."
            a "Yes, please... Wait, let me get you a condom."
            $ falison = "flirt"
            $ fian = "n"
            scene alisonhome
            show iannude at lef
            with long
            label v12alisoncondom1:
                stop music fadeout 2.0
            if v11_alison_condom:
                "This moment always killed the mood a bit. Besides, it was much more enjoyable doing it raw..."
                $ fian = "smile"
                i "It is what it is... I can still enjoy myself."
            else:
                i "What a way to kill the mood... This sucks."
                i "Hopefully, she'll let me go raw at some point..."
            show alisonnude at rig with short
            a "Here. Put it on..."
            play music "music/sex_slow.mp3" loop
            if alison_blonde:
                scene v12_alison8_blonde
            else:
                scene v12_alison8
            show v12_alison8_condom
            with long
            pause 1
            i "Come here now."
            "I took control of Alison's body, turning her around and pushing my cock against her slit."
            "I felt the warmth of her pussy radiating through the rubber layer that covered my cock when it slid in."
            "I pushed it as deep as I could, trying to feel her as much as possible."
            if ian_athletics < 10:
                call xp_up ('athletics') from _call_xp_up_977
            a "Oh, fuck... I love it..."
            if ian_alison_love:
                "The condom put a damper on my enjoyment, and my lust waned a bit with it... But I was determined to ensure Alison had a great time."
            else:
                "The condom put a damper on my enjoyment, and my lust waned a bit with it... Still, I tried to get back in the mood."
            if alison_blonde:
                scene v12_alison7_blonde
            else:
                scene v12_alison7
            show v12_alison7_condom
            with long
            pause 1
            "I teased Alison's body while I continued to fuck her, fondling her boobs, pinching her nipples and kissing her neck."
            "She groaned and squirmed under my caresses, and I felt her pussy squeezing my dick as she moved her hips eagerly."
            "How I wished I could strip off that latex restraint and fuck her raw..."
            "That thought, and Alison's display of mounting arousal, kept me hard and focused."
            "I kept up my pistoning motion, continuing to elicit loud moans from Alison, who seemed to be on the brink of her pleasure."
            if ian_alison_love:
                i "Are you gonna cum? I want you to... Come on, give it to me."
            else:
                i "Are you gonna cum? Come on, show me how crazy my dick makes you... I wanna see it."
            a "Oh, Ian! Yes...! Yes!!"
            play sound "sfx/orgasm1.mp3"
            with flash
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            a "Oh my God, yes..."
            if v12_alison_sex == 2:
                "I felt Alison's body relax, but my cock was still throbbing inside her..."
                if alison_blonde:
                    scene v12_alison4_blonde
                else:
                    scene v12_alison4
                with long
                pause 1
                a "Now's your turn to cum..."
                "Alison rolled on the bed and scooted between my legs, holding my cock and stripping the condom off."
                "I looked at her as she parted her lips and enveloped my glans, stimulating it pleasantly."
                menu:
                    "{image=icon_charisma.webp}Ask to take a picture" if ian_charisma > 6 or v9_alison_trip or v6_alison_pics > 0:
                        $ renpy.block_rollback()
                        if alison_blonde:
                            $ v12_alison_pics = "blonde"
                        else:
                            $ v12_alison_pics = True
                        i "Hey... Can I take a picture of you like this?"
                        a "You want to take a picture of me sucking your cock?"
                        if v9_alison_trip or v6_alison_pics > 0:
                            i "Yeah. You posed for me before..."
                        else:
                            i "Yeah..."
                        a "You dirty boy... Alright, but just one."
                        if alison_blonde:
                            show v12_alison4_blonde_phone
                        else:
                            show v12_alison4_phone
                        with short
                        i "Great..."
                        "I wasn't sure if she'd be down for it, but Alison was very accommodating after orgasming."
                        "I pulled up my phone camera and snapped a few pics."
                        a "Are you still taking pictures?"
                        i "You look so damn hot... I love capturing you like this. Can you feel how hard I am right now?"
                        a "Hmm... Yeah..."
                        i "I want to see your boobs around it..."

                    "Let me feel your boobs":
                        $ renpy.block_rollback()
                        i "Let me feel your boobs now..."

                a "I knew you were gonna ask for that."
                if alison_blonde:
                    scene v12_alison5_blonde
                else:
                    scene v12_alison5
                with long
                pause 1
                "Alison wrapped her breasts around my cock, cushioning it when she pressed them together."
                if v12_alison_pics:
                    play sound "sfx/camera.mp3"
                    with flash
                    i "Damn, that's so hot..."
                else:
                    i "Mhhh, nice..."
                a "You're obsessed with my boobs, aren't you?"
                if ian_lust < 8:
                    call xp_up ('lust') from _call_xp_up_978
                    pause 0.7
                if alison_blonde:
                    scene v12_alison6_animation_blonde
                else:
                    scene v12_alison6_animation
                if v12_alison_pics:
                    show v12_alison6_hand
                with short
                pause 3
                if v12_alison_pics:
                    i "I might as well film this..."
                    a "If you show it to someone I'll kill you."
                    "Despite her threats, Alison continued tit-fucking me with a lewd smile on her face."
                    a "How do I look?"
                    hide v12_alison6_hand with short
                    pause 3
                    i "Sexy as hell..."
                    $ ian_alison_pics.append("v12_alison6_phone")
                else:
                    "Alison began tit-fucking with a lewd smile on her face."
                    i "Damn... You look sexy as hell when you do this."
                a "You seem to really like it... Does it really feel that good?"
                if ian_alison_dom or ian_chad > 2:
                    i "You know it does. Your boobs are made to be fucked."
                elif ian_alison_love:
                    i "You know it does. Your boobs are so soft. No one else can make me feel like this..."
                else:
                    i "It feels the best... Your boobs are so soft."
                a "Then use them to cum... Shoot your load all over me..."
            "I was on the verge too, and I took matters into my own hands to get the satisfaction I was craving."
            if alison_blonde:
                scene v12_alison9_blonde
            else:
                scene v12_alison9
            with long
            if v12_alison_sex < 2:
                $ v12_alison_sex = 2
                "I pulled out, peeled the condom off, and began jacking off vigorously."
            i "I'm gonna cum... Open wide! Ahh, yes!!"
            show v9_alison15_cum1 with flash
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            with vpunch
            hide v9_alison15_cum1
            show v9_alison15_cum2 
            with fps
            pause 1
            "I continued to jerk myself off, riding the jolts of pleasure and shooting my load all over Alison."
            i "{i}Whew...!{/i} Damn, finally..."
            "I looked down to watch the results of my lust, painting Alison's skin with a lewd color."
            i "Hell, you look so sexy..."
            if ian_lust < 8:
                call xp_up ('lust') from _call_xp_up_979
                pause 0.5
            stop music fadeout 2.0
            $ falison = "flirt"
            $ fian = "confident"
            scene alisonhome
            show iannude at lef
            show alisonnude at rig
            show alison_cum1 at rig
            show alison_cum2 at rig
            with long
            a "Well now... I need to shower again."
            if v11_alison_condom:
                i "Sorry, I couldn't help it. I wanted to cum so much."
            else:
                i "Sorry, but there was no way I was gonna cum while wearing that."
            a "I'm not complaining... I love it when you make me all dirty."
            if ian_alison < 12:
                call friend_xp ('alison') from _call_friend_xp_1117
            a "Are you joining me?"
            i "Yeah, I could use another shower too..."
            play sound "sfx/shower.mp3"
            scene alisonhomenight with long
            if v12_gift == "alison":
                play music "music/ourredstring.mp3" loop
                $ ian_look = 3
                $ fian = "smile"
                $ falison = "n"
                pause 1
                show alisonnude at rig
                show ianunder at lef
                with long
                a "Ahh... I feel so refreshed. What a nice way to wrap up the workweek!"
                $ falison = "sad"
                a "Tough I need to finish drafting some contracts tomorrow..."
                $ falison = "n"
                a "Anyway, are you sure you don't want to stay over?"
                if tournament:
                    i "I'd like to, but I can't. I still need to pack my suitcase for tomorrow, and I have to get up early for the tournament..."
                    a "That's right... I hope I can come to cheer you on next time! I'd love to see you in action; I'm sure you look amazing."
                else:
                    i "I'd like to, but I can't. I still need to pack my suitcase for tomorrow..."
                    a "That's right... I wish I could join you guys! I could really use a vacation at the beach..."
                i "Yeah, I will miss you..."
                i "By the way, before I go... I have something for you."
                a "You do? What is it?"
                i "I saw this necklet in a shop window, and I couldn't help but think of you and how great it would look on you."
                $ falison = "smile"
                a "Really? You got it for me?"
                i "It's just a little something, but I wanted to show you my appreciation."
                a "I love it! Thank you so much!"
                "Alison jumped into my arms, hugging and kissing me."
                $ fian = "happy"
                a "You're so cute! And thoughtful! You're the best!"
                if ian_alison < 12:
                    call friend_xp ('alison') from _call_friend_xp_1118
                i "I'm glad you like it..."
                scene v12_alison_necklet
                if alison_blonde:
                    show v12_alison_necklet_blonde
                with long
                a "So... Does it look good on me?"
                i "Absolutely."
                a "Now I need to compensate you somehow..."
                i "No need... I got it because I felt like it, not to get something in return."
                a "So you don't want it?"
                i "Want what? Do you have something for me too?"
                a "I do, if you have the energy for it..."
                "I felt blood rushing to my cock once again."
                "My desire for Alison was truly insatiable, and it looked like she felt the same way about me."
                stop music fadeout 2.0
                if ian_will < 2:
                    call will_up() from _call_will_up_14
                scene alisonhomenight with long
            pause 1
            scene street2night with long
            stop sound fadeout 1.0
            $ renpy.end_replay()
            $ gallery_unlock_scene("CH12_S04")
            pause 1
            jump v12iannighthome

# IAN HOME ######################################################################################################################################################
######################################################################################################################################################
label v12iannighthome:
    play sound "sfx/door_home.mp3"
    $ ian_look = 2
    play music "music/calm.mp3" loop
    # cindy
    if ian_cindy_dating:
        scene ianroomnight with long
        show ian with short
        if ian_cindy_love:
            if v12_cindy_rel == 2:
                $ fian = "worried"
                "I arrived home with my spirits down. How had things ended up like this?"
                "It had all started so well the moment Cindy opened the door..."
                $ fian = "n"
                i "It had to be done. I want to be with Cindy, but I also need to have some self-respect."
                $ fian = "sad"                    
                i "She's fumbling around, and that's not how I like to be treated. Even if that means losing her..."
                if v11_lena_breakup == "cindy":
                    i "To think I dropped Lena to be with her... I hope I don't end up regretting my choice..."
                i "Now the ball's on Cindy's court. She needs to decide what she wants."
                if ian_holly_love:
                    i "But I'm not even sure what I really myself, either... I've been pushing for a relationship with Cindy, but what about Holly?"
                    if ian_cherry_love:
                        i "And Cherry..."
                elif ian_cherry_love:
                    i "But I'm not even sure what I really myself, either... I've been pushing for a relationship with Cindy, but what about Cherry?"
            elif v12_cindy_rel == 1:
                "When I got back home, I wasn't as ecstatic as I'd hoped to be."
                "My time with Cindy had been wonderful, but I couldn't shake off my reasons for concern..."
                i "I have the feeling she doesn't know what she wants, but what more can I do to convince her?"
                if v11_lena_breakup == "cindy":
                    i "I dropped Lena to prove I really wanted to be with her... I hope I don't end up regretting my choice."
                i "I knew I was signing up for a tough time when I decided to start this thing with Cindy..."
                if ian_holly_love:
                    i "Things are so confusing... I've been pushing for a relationship with Cindy, but what about Holly?"
                    if ian_cherry_love:
                        i "And Cherry..."
                elif ian_cherry_love:
                    i "Things are so confusing... I've been pushing for a relationship with Cindy, but what about Cherry?"
            elif v12_cindy_rel == 0:
                $ ian_cindy_dating = False
                "When I got home I still felt outraged at Cindy's disrespect."
                i "I can't believe how selfish and spoiled she is...! After all I've done to be with her, and she has the audacity to say that to me?"
                "I was completely justified in my anger, but that didn't change the fact that I had just lost Cindy."
                $ fian = "worried"
                i "Fuck... How had things ended up like this? It was going wonderfully, and then..."
                $ fian = "mad"
                i "No, things weren't that wonderful. Cindy has been acting up since the get-go. She's such a spoiled brat!"
                i "I don't want to be with a girl like that, that much I know..."
                if v12_gift == "cindy":
                    i "In the end, I bought that stupid necklet for nothing. How fitting."
                    $ v12_gift = "n"
                if v11_lena_breakup == "cindy":
                    i "To think I dropped Lena to be with her... I knew I would regret my choice."
                $ fian = "serious"
                i "Well... At least I got to fuck her."
        else:
            if v12_cindy_rel == 3:
                $ fian = "sad"
                "Pondering about what just happened had me feeling kind of embarrassed at myself."
                i "I was hoping for a better reaction from Cindy... But at least I told her what I really want."
                if ian_holly_love:
                    i "Maybe I shouldn't have, though... I feel I'd like to be in a relationship with Cindy, but what about Holly?"
                    if ian_cherry_love:
                        i "And Cherry..."
                elif ian_cherry_love:
                    i "Maybe I shouldn't have, though... I feel I'd like to be in a relationship with Cindy, but what about Cherry?"
            elif v12_cindy_rel == 2:
                $ fian = "sad"
                "When I got back home, I wasn't as ecstatic as I'd hoped to be."
                "My time with Cindy had been great, but I couldn't shake off my reasons for concern..."
            elif v12_cindy_rel == 1:
                $ fian = "sad"
                "When I got back home, I wasn't as ecstatic as I'd hoped to be."
                "My time with Cindy had been great, up to the point when we started talking. Things got a bit tense..."
            elif v12_cindy_rel == 0:
                $ ian_cindy_dating = False
                "When I got home I still felt outraged at Cindy's disrespect."
                i "She thinks she can handle things however she wants, everyone else be damned? She's such a spoiled brat!"
                "I was completely justified in my anger, but that didn't change the fact that I had just lost Cindy."
                $ fian = "worried"
                i "Fuck... How had things ended up like this? It was going wonderfully, and then..."
                $ fian = "serious"
                i "No, fuck that. I shouldn't have expected more from Cindy; she's only trouble."
                i "Well, at least I got to fuck her..."
        $ fian = "n"
        "I tried to put those thoughts aside. Going over them again and again wasn't helpful at all."
    # alison
    elif v12_alison_sex:
        scene ianroomnight with long
        show ian with short
        if ian_alison_dating:
            if v12_alison_sex == 2:
                i "Home sweet home... And Perry's gone, so I have the entire flat to myself."
                if ian_alison_love:
                    if v12_gift == "alison":
                        i "I'm glad I got that gift for Alison... She really appreciated it."
                        i "We ended up going at it again... Man, I'm exhausted!"
                        if v12_alison_pics:
                            $ fian = "confident"
                            i "And I got some nice pictures and a video today..."
                        $ fian = "n"
                        i "I'm a bit worried about her, though... She complains about work a lot, but I have the feeling she's actually playing it off."
                    else:
                        if v12_alison_pics:
                            $ fian = "confident"
                            i "I got some nice pictures and a video today... That was so hot. Still..."
                        $ fian = "n"
                        i "I'm a bit worried about Alison... She complains about work a lot, but I have the feeling she's actually playing it off."
                    i "I think she's dealing with way more stress than she lets out. I wish I could help her somehow."
                elif ian_alison_dom and v12_alison_pics:
                    $ fian = "confident"
                    i "What a great fuck today with Alison. And I got some nice pics and a video to show from it..."
                elif ian_alison_dom == False:
                    i "That was fun today with Alison... But I can see she's quite stressed lately."
                    if v12_alison_pics:
                        $ fian = "confident"
                        i "But I got some nice pictures and a video today..."
                    else:
                        $ fian = "n"
                        i "She'll deal with it. Anyway..."
            else:
                "When I arrived home I was still in a sour mood."
                $ fian = "serious"
                i "Alison can be so frustrating sometimes... It must be all that stress she's always complaining about."
                if v12_gift == "alison":
                    i "I don't even know why I got her a gift for..."
                if v12_alison_pics:
                    $ fian = "confident"
                    i "At least I got some nice pictures and a video from today..."
                else:
                    $ fian = "n"
                    i "Anyway..."   
        if ian_alison_fuck:
            i "Home sweet home... And Perry's gone, so I have the entire flat to myself."
            if v12_alison_pics:
                i "What a great fuck today with Alison. And I got some nice pics and a video to show from it..."
            else:
                i "What a great fuck today with Alison. It's great to have her as a sex friend..."
        if ian_alison_fuck or ian_alison_dom or v10_alison_3some != "n":
            if v12_alison_pics and ian_jeremy > 7 and alison_no_voyeur == False and ian_alison_love == False:
                menu:
                    "{image=icon_friend.webp}Share the pictures with Jeremy":
                        $ renpy.block_rollback()
                        if ian_lena_couple:
                            $ fian = "n"
                            i "I'd like to share my exploits with Jeremy, but I shouldn't... Not while I'm dating Lena."
                            i "It's too risky."
                        else:
                            $ fian = "evil"
                            $ v12_alison_pics_jeremy = True
                            if alison_voyeur:
                                i "This time it's my turn to share my exploits with Alison. I'm sure Jeremy will appreciate these images."
                            else:
                                i "I'm sure Jeremy will appreciate these images."
                            play sound "sfx/sms.mp3"
                            show ian at left with move
                            show v12_alison6_phone with short
                            nvl clear
                            i_p "Enjoyed some big-tiddy action this afternoon {image=emoji_glasses.webp}"
                            if v10_alison_3some != "n":
                                j_p "Damn! Invite me to join next time, will you? {image=emoji_crazy.webp}"
                                if alison_jeremy_3some == 3:
                                    j_p "I want to enjoy those juicy udders again..."
                                    i_p "Yeah, we need to try that again."
                                    j_p "Eskimo brothers! {image=emoji_dance.webp}"
                                else:
                                    i_p "Sorry, she's mine now {image=emoji_devil.webp}"
                                    j_p "You're too selfish, bro {image=emoji_laugh.webp} Anyway, I'm glad to see you're having fun!"
                            elif ian_alison_fuck:
                                if ian_alison_sex:
                                    j_p "Damn! When did you and Alison start hooking up again?"
                                else:
                                    j_p "Damn! When did you and Alison start hooking up?"
                                i_p "Last week. She made an offer I couldn't refuse."
                                j_p "Eskimo brothers! You owe me a high-five {image=emoji_laugh.webp}"
                                if alison_jeremy_3some == 1:
                                    j_p "We should double-team her this time, don't you think? {image=emoji_crazy.webp}"
                                    i_p "We were considering it..."
                                else:
                                    j_p "We should double-team her some time, don't you think? {image=emoji_crazy.webp}"
                                    i_p "The ideal number of cocks in bed is just one, sorry."
                                j_p "It would be fun, just saying! Good going anyway, bro! Keep grinding {image=emoji_strong.webp}"
                            else: # ian_alison_dating
                                j_p "Damn! Lucky you {image=emoji_crazy.webp}"
                                if alison_jeremy:
                                    j_p "It's been a while since I got to enjoy those juicy udders..."
                                    i_p "Sorry, they're mine now {image=emoji_devil.webp}"
                                else:
                                    j_p "I'd like to get a taste of those juicy udders!"
                                    i_p "Sorry, they're mine {image=emoji_devil.webp}"
                                j_p "You're too selfish, bro {image=emoji_laugh.webp} Anyway, I'm glad to see you're having fun!"
                                j_p "Keep grinding {image=emoji_strong.webp}"
                            if ian_jeremy < 12:
                                call friend_xp ('jeremy') from _call_friend_xp_1119
                                pause 0.7
                            hide v12_alison6_phone with short
                            show ian at truecenter with move
                        
                    "Forget it":
                        $ renpy.block_rollback()
                        $ fian = "smile"
                        i "Anyway..."
    else:
        scene ianhomenight with long
        "I arrived home, happy to enjoy the beginning of my vacation. With Perry gone, I had the apartment all to myself..."
        if v12_cherry_painting:
            "I hung the painting that Cherry had given me on my bedroom wall."
            $ fian = "smile"
        play sound "sfx/door.mp3"
        scene ianroomnight with long
        show ian with short
    if v12_cherry_painting:
        "Looking at it knowing she had painted it especially for me put a smile on my face."
    if tournament:
        if ian_chad > 2:
            $ fian = "smile"
            i "I need to get a good night's sleep to be in top condition for tomorrow's tournament..."
            if ian_chad > 3:
                $ fian = "confident"
                i "I can't wait to smash some faces!"
            else:
                i "I'm a bit too excited to sleep, though..."
        else:
            i "I need to get a good night's sleep, but I don't know if I'll be able to fall asleep. I'm rather anxious about the tournament..."
            if ian_chad > 1:
                i "It'll be fine... Just another day sparring at the gym."
            else:
                $ fian = "sad"
                i "Why did I sign up for this in the first place?"
        i "But before that I need to pack things up for tomorrow's trip."
    else:
        $ fian = "n"
        i "I still need to pack things up for tomorrow's trip."
    # cherry
    if ian_cherry_love:
        $ fian = "sad"
        if ian_holly_dating:
            i "I couldn't invite both her and Holly... Not that Cherry would've joined, since Lena's coming too."
            i "That means it'll be a while until I get to see her again. Now that things seem to start getting good between us..."
        else:
            i "Too bad Cherry can't join us... It'll be a while until I get to see her again."
            $ fian = "smile"
            i "Things are starting to get really good between us..."
        if v12_gift == "cherry":
            $ fian = "smile"
            i "I'll have to wait quite a bit before I can give her my gift, but it'll be worth it."
    hide ian with short
    play sound "sfx/zipper.mp3"
    $ fian = "n"
    pause 1
    show ian with short
    i "There... Done."
    if v5_ian_showup:
        $ fian = "smile"
        i "And now to check my finances... Good, my last paycheck just went through."
        call money(2) from _call_money_146
        if ian_stipend == 2:
            i "And Dad sent his generous stipend too... That's a really good security net."
            $ fian = "sad"
            i "It's me who asked him for some extra cash, but I can't say I feel proud about it. At this point, I should be able to support myself by my own means..."
            call money(2) from _call_money_147
            pause 0.8
        elif ian_stipend == 1:
            i "Dad sent his monthly stipend too. It's always good to have some extra cash, but..."
            i "I can't say I feel proud about it. At this point, I should be able to support myself by my own means..."
            call money(1) from _call_money_148
            pause 0.8
        else:
            i "It was about time I sustained myself on my own... No more stipends from Dad."
    else:
        i "And now to check my finances... My last paycheck from the magazine just went through."
        call money(2) from _call_money_149
        i "I'll need to be mindful of my expenses for a while, at least until I figure out my job situation."
        if ian_stipend == 2:
            call money(2) from _call_money_150
            i "I should be fine as long as I have Dad's stipend. He's been very generous as of late..."
            $ fian = "sad"
            i "It's me who asked him for some extra cash, but I don't feel proud about it. At this point, I should be able to support myself by my own means..."
        elif ian_stipend == 1:
            call money(1) from _call_money_151
            i "Dad's stipend surely helps, but I can't say I feel proud about it. I hope I'm able to support myself by my own means soon..."
        else:
            i "I can't count on Dad's stipends anymore. It was about time I sustained myself on my own."
    call money(-2) from _call_money_152
    if ian_money > 5:
        $ fian = "smile"
        i "And that covers rent and utilities. I've managed to save up quite some money..."
        i "Hopefully, it's enough to rent a place of my own soon!"
    elif ian_money > 3:
        $ fian = "smile"
        i "And that covers rent and utilities. My bank account doesn't look half bad."
        i "If I keep saving up, I'll have enough to rent a place of my own at some point."
    elif ian_money > 1:
        $ fian = "n"
        i "And that covers rent and utilities... I hoped I'd have some more money saved up."
        i "If I want to rent a place of my own soon, I should start watching my spending."
    else:
        $ fian = "worried"
        i "And that covers rent and utilities... Damn, my bank account is dry!"
        i "What have I been doing with my money? I won't be able to ever rent a place of my own at this rate."    
    # jess sms
    if jess_bad and v10_jess_date and ian_jess > 4:
        play sound "sfx/sms.mp3"
        $ fian = "n"
        pause 0.6
        i "Huh?"
        i "Jessica just texted me..."
        nvl clear
        js_p "Hey. Are you free right now?"
        i_p "I am."
        js_p "Do you mind if I drop by your place?"
        $ fian = "worried"
        i "What...? She wants to come to my place right now?"
        if ian_chad < 3:
            $ fian = "confident"
            i "Hell yeah! That can only mean one thing..."
            i "Scoring with a porn star is within my reach!"
        else:
            $ fian = "n"
            i "That can only mean one thing, right...?"
        menu:
            "Invite Jessica":
                $ renpy.block_rollback()
                $ v12_jess_date = True
                if ian_chad < 3:
                    $ fian = "n"
                    i "I need to calm down... There's no way to know what her intentions are until she's here."
                else:
                    i "Still... There's no way to know what her intentions are until she's here. Let's find out."
                i_p "Sure, you can come by. This is my address."
                i "..."
                i "No answer..."
                if ian_lena_dating:
                    play sound "sfx/sms.mp3"
                    i "Oh, and now it's Lena who's texting me."
                    if ian_lena_couple:
                        i "Maybe I'm messing up, inviting Jess to my place tonight, but..."

            "Turn her down":
                $ renpy.block_rollback()
                $ fian = "n"
                if ian_chad < 3:
                    i "As awesome as that sounds... I don't think it's a good idea to hook up with Jessica."
                else:
                    i "Maybe I'm wrong, but in any case, I'm not looking to score with her..."
                if ian_lena_couple:
                    i "I was curious about her, but I'm already dating Lena, and I won't risk that by getting involved with a girl like Jessica."
                elif ian_holly_dating:
                    i "I was curious about her, but I'm already dating Holly, and I won't risk that by getting involved with a girl like Jessica."
                elif ian_cherry_dating:
                    i "I was curious about her, but I'm already dating Cherry, and I won't risk that by getting involved with a girl like Jessica."
                else:
                    i "I was curious about Jessica, but after our last date, I don't think it's a good idea to get involved with a girl like her."
                i "I have to come up with some excuse."
                i_p "I'm sorry, but my flatmate Perry wouldn't like it. Maybe we can grab a beer some other day?"
                js_p "It's alright. Forget I said anything."
                call friend_xp ('jess',-1) from _call_friend_xp_1120
                $ ian_jess = 3
                i "Well... This is as far as that goes."
                if ian_lena_dating:
                    play sound "sfx/sms.mp3"
                    i "Oh, and now it's Lena who's texting me."
    
    # lena sms
    elif ian_lena_dating:
        play sound "sfx/sms.mp3"
        i "Oh, Lena just texted me."
    if ian_lena_dating:
        $ fian = "smile"
        nvl clear
        if tournament:
            l_p "Hi! How are you feeling? Ready for the tournament? {image=emoji_strong.webp}"
            i_p "As ready as I can be. I just hope nobody ends up at the hospital {image=emoji_laugh.webp}"
            l_p "Please, don't {image=emoji_disgust.webp}"
            i_p "Don't worry, I won't. I need to be in one piece for our beach escapade. Are you ready for it?"
        else:
            l_p "Hi! How was the last day at the office? {image=emoji_smile.webp}"
            if v5_ian_showup:
                i_p "A day just like any other. Office work never was especially exciting..."
            else:
                i_p "It was pretty good, actually. I feel like I've taken a weight off my shoulders." 
            i_p "What about you? Ready for our beach escapade?"
        l_p "I will be, after this weekend is over... I've been putting it off, but I need to check on my family {image=emoji_sad.webp}"
        $ fian = "n"
        i_p "How's your mom feeling? Any news on her condition?"
        l_p "She has a colonoscopy scheduled for tomorrow morning. I'm on the train heading home right now to spend the night there."
        l_p "My plan is to stay on Saturday and take the train on Sunday to join you guys." 
        if ian_lena_couple or lena_ian_love:
            l_p "I can't wait to see you {image=emoji_smile.webp}"
            $ fian = "smile"
            i_p "Yeah, me too. It'll be great to spend some days away with you."
            i_p "No work, no worries, just us, some friends, and the beach."
        else:
            l_p "I can't wait to take a dip in the sea. I'm so glad you invited me {image=emoji_smile.webp}"
            $ fian = "smile"
            i_p "Of course. I've been looking forward to spending some days together with you. It'll be fun."
        l_p "I'm getting off the train! See you really soon {image=emoji_kiss.webp}"
        i_p "Goodnight, beautiful."
        $ fian = "n"
        i "I'm worried about her... Seems like she can't catch a break."
        i "She's been struggling for a while, and to top it off her mother gets sick..."
        if seymour_desire:
            i "At least she's found a good patron in Seymour. That should alleviate things in good measure."
        elif lena_seymour_dating:
            i "For a moment it looked like she had found a good patron in Seymour, but that fell through for some reason."
        if ian_cuck == 2 or ian_lena_sex == False:
            $ fian = "sad"
            i "She's so sweet and nice to me... Except when it comes to sex."
            i "It's like she loves being cruel to me or something like that... But she claims that's what makes me special..."
            i "She really manages to mess with my head, but the one thing that's clear is that I'm crazy about her..."
            if v10_lena_mad == "stalkfap":
                i "The fact she's on Stalkfap is also a hard pill to swallow... But I can't bring it up or she'll get mad."
            if ian_stalkfap_on == 2:
                $ fian = "n"
                i "At least I get to be her photographer for Stalkfap content... That's really hot."
            if ian_stalkfap_on == 1:
                i "The fact she's on Stalkfap is also a hard pill to swallow... Though I'm helping her create content now."
        # couple
        elif ian_lena_couple:
            i "Sometimes I have the feeling she doesn't have anyone she can really count on..."
            # cheating
            if ian_cheating:
                $ fian = "sad"
                "I felt guilty for cheating on her, but that hadn't stopped me..."
                if ian_cindy_over == 2 and ian_alison_fuck == False and ian_minerva_dating == False:
                    $ ian_cheating = False
                    $ fian = "n"
                    i "Well, I'm not cheating on her anymore. My affair with Cindy is over, so..."
                    i "I should try and be a good boyfriend from now on."
                if ian_cindy_dating:
                    "But how could I refuse the chance to be with Cindy, now that it manifested? She was too precious to let go."
                if ian_alison_fuck:
                    "I wasn't expecting Alison to jump on me that night, but I hadn't refused her."
                    if ian_alison_sex:
                        "The truth was I missed having sex with her, but maybe I should think about cutting it short..."
                    else:
                        "The truth was I had been dying to fuck her, but now that I had done so, maybe I should think about cutting it short..."
                    i "Either way I need to be careful..."
                if ian_minerva_dating:
                    "At least I could feel more at ease about my affair with Minerva. The chances of Lena finding out about it were almost non-existent."
            # faithful
            else:
                i "I want to try and be that person to her. Share her burden and put a smile on her face..."
                if v11_lena_openup:
                    "I felt a very real connection with her, one that had allowed me to open up to someone again."
                else:
                    "I had been hesitant to open up to her, but if not Lena, who else deserved it?"
            if v10_lena_mad == "stalkfap":
                $ fian = "n"
                i "We've had some bumps in the road, though... The fact that she's on Stalkfap is a hard pill to swallow."
            if ian_stalkfap_on == 2:
                $ fian = "confident"
                i "And I hope she asks me to film some more content for Stalkfap. That was so hot."
            if ian_stalkfap_on == 1:
                $ fian = "n"
                i "She's not a conventional girlfriend... I'm still trying to deal with the fact she's on Stalkfap."
                i "Even though I'm helping her create content for it now..."
        # dating
        else:
            if ian_lena_love:
                $ fian = "sad"
                i "I'd like to be the one to take that place, but I guess I'm in no position to do so."
            else:
                i "I wonder if I could be the one to take that place. Is that even right for me...?"
            if v11_lena_openup:
                "I felt a very real connection with her, one that had allowed me to open up to someone again."
                if ian_cindy_love and v12_cindy_rel > 0:
                    i "But, right now, it's Cindy who I want to take a chance at being with..."
                    i "Giving up a girl like Lena, though... It's not easy to do that."
                else:
                    i "I'm still not sure if I'm ready for another relationship yet."
                    $ fian = "sad"
                    if v12_cindy_rel == 3:
                        i "Even though what I just told Cindy... Man, what the hell do I even want?"
                        i "Lena's incredible too. Where else could I find another girl like her?"
                    else:
                        i "But if I ever am again... I could never find another girl like Lena, or at least that's how I feel..."
            else:
                if ian_cindy_love and v12_cindy_rel > 0:
                    i "Right now, it's Cindy who I want to take a chance at being with..."
                    i "Giving up a girl like Lena, though... It's not easy to do that."
                else:
                    i "I'm not ready for another relationship. Not yet."
                    $ fian = "sad"
                    i "But if I ever am again... I could never find another girl like Lena, or at least that's how I feel..."
            if v10_lena_mad == "stalkfap":
                $ fian = "n"
                i "Still, not everything's perfect. The fact that she's on Stalkfap is a hard pill to swallow."
            if ian_stalkfap_on == 2:
                $ fian = "confident"
                i "Where else could I find a girl who asks me to film content for Stalkfap with her? That was so hot."
            if ian_stalkfap_on == 1:
                i "She's far from conventional... I'm still trying to deal with the fact she's on Stalkfap."
                i "Even though I'm helping her create content for it now..."
            if ian_alison_love:
                $ fian = "n"
                i "But... I've been dating Alison for a while now, and I... She's very dear to me."
                i "My thing with Lena must end sooner or later..."
        if v11_louise_3some == "ian":
            $ fian = "shy"
            i "I still can't believe she invited me to a threesome with Louise... That was probably the hottest experience in my life."
            $ fian = "smile"
            i "I got to fulfill every guy's fantasy, and I have to thank Lena for that... It's surely boosted my confidence."
            if ian_charisma < 7:
                call xp_up ('charisma') from _call_xp_up_980
            $ fian = "n"
            if v7_bbc == "lena":
                i "As much as I'd like to brag about it to Jeremy, I don't think I should. He's dating Louise... even though he never seemed to take her seriously."
            else:
                i "As much as I'd like to brag about it to Jeremy, I wonder if I should. He used to date Louise after all, even though he never took her seriously..."
            if ian_jeremy < 3:
                $ fian = "serious"
                i "Whatever. It's not like I owe him any explanations."
            elif ian_jeremy < 7:
                i "I don't want to lie to him, but I guess there's no need to bring it up unless the topic comes up."
            else:
                i "Still, I don't want to lie to him..."
    # lena breakup
    if v11_lena_breakup:
        $ fian = "sad"
        "I sat down and thought about Lena. She had been on my mind quite frequently in the past few days, and it couldn't be any other way."
        if v11_lena_breakup == "cindy":
            "She was by far one of the most extraordinary girls I had ever met, but I had to end things with her. My heart was pointing me in Cindy's direction..."
            if v12_cindy_rel == 0:
                $ fian = "mad"
                "And that had proved to be such a stupid mistake. I had given Lena up for someone who didn't know what the hell she wanted, and now I had nothing."
            elif v12_cindy_rel == 1:
                i "I hope I made the right choice..."
            elif v12_cindy_rel == 2:
                "And following it could prove to be such a stupid mistake. I had given Lena up for someone who didn't know what she really wanted..."
                $ fian = "n"
                i "Right now I feel like I lost both of them... But I made those choices, and I need to stand by them. Otherwise, I won't be able to respect myself."
            $ fian = "n"
            i "I just hope my friendship with Lena won't crumble, but... I'm not sure I can see her as a mere friend, either"
        if v11_lena_breakup == "cherry":
            "She was by far one of the most extraordinary girls I had ever met, but I had to end things with her. My heart was pointing me in Cherry's direction..."
            $ fian = "n"
            "Even so, giving up Lena wasn't a simple matter. I only hoped that our friendship wouldn't crumble, but..."
            "I wasn't certain I could view her as a mere friend."
        if ian_lena_sex:
            if ian_lena_couple:
                "We had shared much, and our chemistry was undeniable. I even thought we could make a serious relationship work..."
                "I tried telling myself that, despite everything, our relationship wasn't going anywhere. We needed different things."
            else:
                "We had shared much, and our chemistry was undeniable. I had started to think she could be the one, but..."
                i "I needed to accept that our relationship wasn't going anywhere, as much as I would've liked to. We needed different things."
        else:
            "We had shared much, even though she never allowed me to go all the way with her."
            if v10_lena_mad == "bothered" or v10_lena_mad == "sex":
                i "We had issues with that... She didn't like me complaining, but what was I supposed to do?"
            i "I needed to accept that our relationship wasn't going anywhere, as much as I would've liked to."
        i "I wonder how this trip to the beach will go... Things will be tense, no doubt, but at least we won't be alone."
    elif v10_ian_left:
        $ fian = "sad"
        "I sat down and thought about Lena. She still crossed my mind quite frequently, and it couldn't be any other way."
        if ian_lena_sex:
            if ian_lena_couple:
                "We had shared much, and our chemistry was undeniable. I even thought we could make a serious relationship work..."
                "I tried telling myself that, despite everything, our relationship wasn't going anywhere. We needed different things."
            else:
                "We had shared much, and our chemistry was undeniable. I had started to think she could be the one, but..."
                i "I needed to accept that our relationship wasn't going anywhere, as much as I would've liked to. We needed different things."
            $ fian = "n"
            i "I can't be comfortable with someone who exposes herself sexually like that on Stalkfap. I know I made her feel judged, but..."
            i "She never tried to get in my shoes and respect my feelings. I needed to set my boundaries, and Lena didn't like them."
        else:
            "We had shared much, even though she never allowed me to go all the way with her."
            $ fian = "serious"
            i "I still can't understand her reasons... As much as I wanted to be with her, I need a girlfriend I can have sex with."
            if stalkfap:
                i "She's on Stalkfap but won't get in bed with me? How absurd is that...?"
            $ fian = "n"
        i "I guess our relationship wasn't going anywhere... But I'm not sure if I'll be able to see her as a mere friend."
        i "I wonder how this trip to the beach will go... Things will be tense, no doubt, but at least we won't be alone."
    elif ian_lena_breakup:
        "I sat down and thought about Lena. Our failed relationship still crossed my mind from time to time."
        if ian_lena_sex:
            "We had shared much, and our chemistry was undeniable. I had started to think she could be the one, but..."
        else:
            "We had shared much, even though she never allowed me to go all the way with her."
        i "In the end, we wanted different things, and I had to be true to my feelings. At least it seems we can continue to be friends..."
        if v10_wc_bj == "ian":
            $ fian = "blush"
            i "I still have trouble viewing her as a mere friend, though..."
            "How could I possibly forget the night when she sucked me off in the club's bathroom? She was wild that day."
            i "We've been acting like nothing happened, and maybe that's for the best... Maybe it's wrong, but I wouldn't mind if something like that happened again..."
            $ fian = "n"
            i "We'll be spending a few days together at Perry's beach house. I hope that goes well."
        elif v10_lena_dance == "ian":
            $ fian = "blush"
            i "I still have trouble viewing her as a mere friend, though..."
            "I clearly recalled the night of Ivy's birthday celebration, when Lena and I danced together."
            "The sensation of her body pressed against mine, her waist in my hands, and her lips so near to mine once more..."
            $ fian = "n"
            i "But that was all. A simple dance between friends..."
            i "We'll be spending a few days together at Perry's beach house. I hope that goes well."
        else:
            i "I still have trouble viewing her as a mere friend, though. I hope this trip to the beach goes well..."
    elif ian_lena_over:
        "I sat down and thought about Lena. Our fling had been short-lived, but it still crossed my mind from time to time."
        if ian_lena_sex:
            "We had maintained our friendship, but I still struggled to see her as a mere friend. We had shared much, and our chemistry was undeniable..."
        else:
            "We had maintained our friendship, but I still struggled to see her as a mere friend. We had shared much, even though she never allowed me to go all the way with her..."
        if v10_wc_bj == "ian":
            $ fian = "blush"
            "How could I possibly forget the night when she sucked me off in the club's bathroom? She was wild that day."
            i "We've been acting like nothing happened, and maybe that's for the best... Maybe it's wrong, but I wouldn't mind if something like that happened again..."
        if v10_lena_dance == "ian":
            $ fian = "blush"
            "I clearly recalled the night of Ivy's birthday celebration, when Lena and I danced together."
            "The sensation of her body pressed against mine, her waist in my hands, and her lips so near to mine once more..."
            $ fian = "n"
            i "But that was all. A simple dance between friends..."
        $ fian = "n"
        i "We'll be spending a few days together at Perry's beach house. I hope that goes well."
            
# stalkfap
    if ian_stalkfap:
        label gallery_CH12_S05:
            if _in_replay:
                call setup_CH12_S05 from _call_setup_CH12_S05
        $ fian = "n"
        if ian_lena_dating:
            if v11_louise_3some == "ian":
                i "I haven't checked Lena's Stalkfap profile in a while..."
            else:
                i "Speaking of Stalkfap... I haven't checked Lena's profile in a while..."
        else:
            i "..."
            "I hadn't checked Lena's Stalkfap in a while, but I was still subscribed."
        "I felt a knot of concern and a thrill in my stomach as I logged into the app."
        if seymour_desire:
            stop music fadeout 2.0
            $ fian = "worried"
            i "Wait... Where's Lena's profile? I can't seem to find it..."
            i "Looks like she deactivated her account! When did she decide to do it?"
            if ian_lena_dating:
                if ian_stalkfap_on == 2:
                    i "And why? After all, I told her I was on board with it... I enjoyed how kinky it was."
                    $ fian = "n"
                    i "Well, this is probably for the better."
                    $ fian = "confident"
                    i "Hopefully, she'll still let me film some private content..."
                elif ian_stalkfap_on == 1:
                    $ fian = "sad"
                    i "It's true I was a bit on the fence about her being on Stalkfap, but I never asked her to get off it..."
                    $ fian = "n"
                    i "Well, I guess the problem solved itself. I think I feel more at ease now."
                else:
                    $ fian = "sad"
                    i "Is it because we had a fight over it? It's true that her being on Stalkfap made me feel uncomfortable..."
                    $ fian = "n"
                    i "I feel more at ease now, that much I can say for sure."
        else:
            show ian at left with move
            show v12_stalkfap1
            if stalkfap_pro == 2:
                show v12_stalkfap1c
            if stalkfap_pro == 1:
                show v12_stalkfap1b
            if lena_tattoo2:
                show v12_stalkfap1_t2
            with short
            if stalkfap_pro == 2:
                i "There it is... Seems she's been pretty active since the last time I checked."
            elif stalkfap_pro == 1:
                i "There it is... Seems like she's posted a few things more since the last time I checked."
            else:
                i "There it is... Seems she hasn't posted much since the last time I checked."
            if ian_stalkfap_on == 2:
                $ fian = "confident"
            elif ian_stalkfap_on == 1:
                $ fian = "n"
            else:
                $ fian = "blush"
            scene ianroomnight
            show ian at left
            show v12_stalkfap2
            with short
            pause 1
            i "Damn... She's so hot!"
            if ian_lena_dating:
                $ fian = "smile"
                i "I'm lucky to have a girl like her... How many guys would kill to be in my shoes?"
                if ian_lena_couple or ian_lena_love:
                    if ian_lena_couple:
                        $ fian = "n"
                        i "That's something I'm a bit worried about, actually... Sometimes I wonder..."
                        i "Can you make a relationship with a girl who's on Stalkfap actually work?"
                    elif ian_lena_love:
                        $ fian = "n"
                        i "I've often thought about how I'd like to have a serious relationship with her, but..."
                        i "Can you really date a girl who's on Stalkfap?"
                    if ian_stalkfap_on == 2:
                        $ fian = "confident"
                        i "I can't deny how hot and kinky it is to be the one who helps her create content, though."
                    elif ian_stalkfap_on == 1:
                        $ fian = "n"
                        i "I still don't know how I feel about helping her create content... It's kinda hot and kinky, but at the same time..."
                    else:
                        $ fian = "sad"
                        i "I can't help but feel uncomfortable seeing what she shares with other guys, but I guess that's what dating a model is like."
                else:
                    if ian_stalkfap_on == 2:
                        $ fian = "confident"
                        i "Being the one helping her create content is so hot and kinky."
                    elif ian_stalkfap_on == 1:
                        $ fian = "n"
                        i "I still don't know how I feel about helping her create content... It's kinda hot and kinky, but at the same time..."
                    else:
                        $ fian = "sad"
                        i "I can't help but feel uncomfortable seeing what she shares with other guys, but I guess that's what dating a model is like."
            elif ian_lena_breakup:
                $ fian = "sad"
                i "To think I had the chance of being with a girl like her... If only things had been different..."
                i "I wonder how many guys are trying to get in her panties now that our thing is over..."
            elif ian_lena_over:
                $ fian = "n"
                i "To think I had the chance of being with a girl like her... How many guys would kill to be in my shoes?"
            hide v12_stalkfap2
            show v12_stalkfap3
            if lena_tattoo1:
                show v12_stalkfap3_t1
            if lena_tattoo2:
                show v12_stalkfap3_t2
            if lena_tattoo3:
                show v12_stalkfap3_t3
            if lena_piercing1:
                show v12_stalkfap3_p1
            elif lena_piercing2:
                show v12_stalkfap3_p2
            with short
            pause 1
            # stalkfap pro
            if stalkfap_pro:
                $ fian = "n"
                i "I can see she has some hardcore fans... These guys comment on all her posts."
                "They showered her with compliments, declaring their love and fascination with her beauty, and expressing some less chivalrous desires."
                if ian_stalkfap_on == 2:
                    i "They're so desperate... Keep dreaming guys, but I'm afraid I'm the one who'll be enjoying her in the flesh."
                elif ian_stalkfap_on == 1:
                    i "I wonder how Lena feels about these comments... Is it possible she enjoys getting some of them, or she's just indifferent?"
                else:
                    i "I have trouble imagining what Lena feels like when reading some of these comments."
                    i "Is she disgusted by them, or do they boost her self-esteem?"
                # custom vids
                if stalkfap_pro == 2:
                    # custom vids
                    scene ianroomnight
                    show ian at left
                    show v12_stalkfap4
                    with short
                    if ian_stalkfap_on > 0:
                        i "She keeps advertising custom videos... How many guys are actually paying her for these?"
                        if ian_lena_couple or ian_stalkfap_on == 1:
                            $ fian = "n"
                            i "This is the part I'm not entirely comfortable with..."
                            i "One thing is me being the one taking the pictures and videos, but I have no idea what kind of stuff she does for these other guys."
                        if ian_stalkfap_on == 2:
                            i "She should send me those videos too... Or better yet, make some especially for me, for free, of course."
                            $ fian = "evil"
                            i "I wonder what kinky things I could ask her to perform..."    
                            i "Fuck, I'm getting horny just thinking about it."
                        else:
                            jump v12stalkfapreflect
                    else:
                        $ fian = "blush"
                        i "She's been advertising custom videos lately... How many guys are actually paying her for these?"
                        i "And what kind of stuff do they ask her to do for them?"
                        "I had no answer to that, but what I imagined made that knot in my stomach feel even tighter."
                        label v12stalkfapreflect:
                            if ian_lena_couple:
                                $ fian = "disgusted"
                                i "I still haven't figured out how to digest the fact that my girlfriend is sharing this kind of content for everyone to see."
                                i "Everyone who's willing to pay, that is..."
                            elif ian_lena_dating:
                                $ fian = "worried"
                                i "I still haven't figured out how I feel about dating a girl who shares this kind of content for everyone to see..."
                                i "Everyone who's willing to pay, that is."
                            elif ian_lena_breakup:
                                $ fian = "depress"
                                "It feels so weird seeing the girl I dated sharing  this kind of content for everyone to see."
                                i "Everyone who's willing to pay, that is..."
                            else:
                                $ fian = "worried"
                                i "Am I just like these guys...? I feel dirty creeping on Lena like this, but then again, she's posting these things for everyone to see..."
                                i "Everyone who's willing to pay, that is."

                    # v10 stalkfap dildo
                    scene ianroomnight
                    show ian at left
                    if v10_shoot_look =="bunny":
                        show v12_stalkfap5b
                    elif v10_shoot_look =="lingerie":
                        show v12_stalkfap5c
                    else:
                        show v12_stalkfap5a
                    if lena_tattoo1:
                        show v12_stalkfap5_t1
                    if lena_tattoo3:
                        show v12_stalkfap5_t3
                    if lena_piercing1:
                        show v12_stalkfap5_p1
                    if lena_piercing2:
                        show v12_stalkfap5_p2
                    with short
                    pause 1
                    # ian filmed
                    if v10_stalkfap == "ian":
                        if ian_stalkfap_on == 2:
                            $ fian = "confident"
                            i "Here they are... She posted some of the naughty pics I took for her."
                            i "It was so hot seeing Lena acting all naughty and slutty for the camera..."
                            $ fian = "n"
                            i "And for her subscribers. It feels kinda weird seeing these images in here, after all."
                        elif ian_stalkfap_on == 1:
                            $ fian = "smile"
                            i "Oh, here it is... She posted some of the pics from our shoot."
                            $ fian = "sad"
                            i "It was a really hot and naughty experience... It feels kinda weird seeing these images in here, though."
                        else:
                            $ fian = "blush"
                            i "This is... She really did post some of the pics from our shoot..."
                            i "It was a very hot experience, but it feels really weird to see these images in here..."
                        jump v12stalkfapunlock
                       
                    # ian didn't film
                    else:
                        i "Wow... When did she upload this?"
                        if v10_shoot_look == "bunny":
                            $ fian = "blush"
                            if ian_lena_dating or ian_lena_sex:
                                i "And since when does she have this bunny outfit? She's never wore it for me... It looks so slutty."
                            else:
                                i "I never imagined she'd own this kind of outfit. A sexy bunny, no less... It looks so slutty."
                        elif v10_shoot_look =="lingerie":
                            $ fian = "shy"
                            i "She looks incredible in this set of lingerie..."
                        else:
                            $ fian = "shy"
                            i "She looks so sexy in fishnet stockings..."
                        $ fian = "blush"
                        i "And she says she's playing with her toys?"
                        label v12stalkfaplock:
                            show v12_stalkfap6_blocked with short
                            $ fian = "worried"
                            i "Damn...! There's a paywall to see her content..."
                        menu:
                            "{image=icon_pay.webp}Unlock the post" if ian_money > 0:
                                $ renpy.block_rollback()
                                i "I need to see this..."
                                call money (-1) from _call_money_153
                                pause 0.8
                                label v12stalkfapunlock:
                                    scene ianroomnight
                                    show ian at left
                                    if v10_shoot_look =="bunny":
                                        show v12_stalkfap6b
                                    elif v10_shoot_look =="lingerie":
                                        show v12_stalkfap6c
                                    else:
                                        show v12_stalkfap6a
                                    if lena_tattoo1:
                                        show v12_stalkfap6_t1
                                    if lena_tattoo2:
                                        show v12_stalkfap6_t2
                                    if lena_tattoo3:
                                        show v12_stalkfap6_t3
                                    if lena_piercing1:
                                        show v12_stalkfap6_p1
                                    if lena_piercing2:
                                        show v12_stalkfap6_p2
                                    with short
                                pause 1
                                if v10_stalkfap == "ian":
                                    if ian_stalkfap_on == 2:
                                        $ fian = "shy"
                                        i "I'm as hard right now as I was the moment I was taking these pics..."
                                        i "I love seeing her like this."
                                    else:
                                        $ fian = "blush"
                                        i "Lena can be so naughty and slutty when she wants to."
                                else:
                                    $ fian = "shy"
                                    i "Fuck me, this angle is crazy... She's an instant turn-on."
                                    if v10_stalkfap == "mike" or v10_stalkfap == "stan":
                                        i "Wait, is this a selfie? Looks like a difficult angle, unless she used a tripod..."
                                # dildo
                                if v10_stalkfap_dildo > 0 or v10_stalkfap == "n" or v10_stalkfap == 0:
                                    scene ianroomnight
                                    show ian at left
                                    if v10_shoot_look =="bunny":
                                        show v12_stalkfap7b
                                    elif v10_shoot_look =="lingerie":
                                        show v12_stalkfap7c
                                    else:
                                        show v12_stalkfap7a
                                    if lena_tattoo1:
                                        show v12_stalkfap6_t1
                                    if lena_tattoo2:
                                        show v12_stalkfap6_t2
                                    if lena_tattoo3:
                                        show v12_stalkfap6_t3
                                    if lena_piercing1:
                                        show v12_stalkfap6_p1
                                    if lena_piercing2:
                                        show v12_stalkfap6_p2
                                    if v10_stalkfap_dildo == 2:
                                        show v12_stalkfap7_d2
                                    else:
                                        show v12_stalkfap7_d1
                                    with short
                                    pause 1
                                    if v10_stalkfap == "ian":
                                        i "I remember this well... She's such a tease."
                                    else:
                                        if v10_stalkfap_dildo == 2:
                                            $ fian = "blush"
                                            i "Damn... That's a big dildo."
                                        else:
                                            $ fian = "shy"
                                            i "Damn... This is seriously hot."
                                        i "She's such a tease... To think Lena has this side to her..."
                                    # penetration pussy
                                    if v10_stalkfap_dildo_action or v10_stalkfap == 0 or v10_stalkfap == "n":
                                        scene ianroomnight
                                        show ian at left
                                        if v10_shoot_look =="bunny":
                                            show v12_stalkfap8b
                                        elif v10_shoot_look =="lingerie":
                                            show v12_stalkfap8c
                                        else:
                                            show v12_stalkfap8a
                                        if lena_tattoo1:
                                            show v12_stalkfap8_t1
                                        if lena_tattoo3:
                                            show v12_stalkfap8_t3
                                        if lena_piercing1:
                                            show v12_stalkfap8_p1
                                        if lena_piercing2:
                                            show v12_stalkfap8_p2
                                        if v10_stalkfap_dildo == 2:
                                            show v12_stalkfap8_pussy2
                                        else:
                                            show v12_stalkfap8_pussy1
                                        with short
                                        pause 1
                                        if v10_stalkfap == "ian":
                                            i "She really looks like one of these amateurs pornstars... Maybe because she's actually one herself..."
                                            i "My own little pornstar..."
                                        else:
                                            $ fian = "blush"
                                            i "She's really doing it..."
                                            i "Fucking herself with that dildo... And she seems to be enjoying herself quite a lot."
                                        if v10_stalkfap_anal > 1 or (v10_stalkfap == "n" and lena_anal > 1) or (v10_stalkfap == 0 and lena_anal > 1):
                                            # penetration anal
                                            scene ianroomnight
                                            show ian at left
                                            if v10_shoot_look =="bunny":
                                                show v12_stalkfap8b
                                            elif v10_shoot_look =="lingerie":
                                                show v12_stalkfap8c
                                            else:
                                                show v12_stalkfap8a
                                            if lena_tattoo1:
                                                show v12_stalkfap8_t1
                                            if lena_tattoo3:
                                                show v12_stalkfap8_t3
                                            if lena_piercing1:
                                                show v12_stalkfap8_p1
                                            if lena_piercing2:
                                                show v12_stalkfap8_p2
                                            if v10_stalkfap_dildo == 2:
                                                show v12_stalkfap8_anal2
                                            else:
                                                show v12_stalkfap8_anal1
                                            with short
                                            pause 1
                                            if v10_stalkfap == "ian":
                                                i "And an amazing one at that. Look at that..."
                                                i "How many guys have jizzed while looking at these pictures? Pictures I took..."
                                                $ fian = "disgusted"
                                                i "..."
                                                i "What a weird fucking thought."
                                            else:
                                                i "Yeah, this is straight-up porn..."
                                                i "Does that make Lena some kind of... amateur pornstar?"
                                                "The way she showed the world how that dildo stretched her ass made it difficult to think otherwise."
                                        else:
                                            if v10_stalkfap == "ian":
                                                i "How many guys have jizzed while looking at these pictures? Pictures I took..."
                                                $ fian = "disgusted"
                                                i "..."
                                                i "What a weird fucking thought."
                                            else:
                                                i "This is straight-up porn..."
                                                i "Does that make Lena some kind of... amateur pornstar?"
                                    else:
                                        i "She really looks like one of these amateurs pornstars... Maybe because she's actually one herself..."
                                        if v10_stalkfap == "ian":
                                            i "How many guys have jizzed while looking at these pictures? Pictures I took..."
                                            $ fian = "disgusted"
                                            i "..."
                                            i "What a weird fucking thought."
                                    if v10_stalkfap == "mike" and ian_lena_couple == False or v10_stalkfap == "stan" and ian_lena_couple == False or v10_stalkfap == "ian":
                                        # final pose
                                        scene ianroomnight
                                        show ian at left
                                        if v10_shoot_look =="bunny":
                                            show v12_stalkfap9b
                                        elif v10_shoot_look =="lingerie":
                                            show v12_stalkfap9c
                                        else:
                                            show v12_stalkfap9a
                                        if lena_tattoo1:
                                            show v12_stalkfap9_t1
                                        if lena_tattoo2:
                                            show v12_stalkfap9_t2
                                        if lena_tattoo3:
                                            show v12_stalkfap9_t3
                                        if lena_piercing1:
                                            show v12_stalkfap9_p1
                                        if lena_piercing2:
                                            show v12_stalkfap9_p2
                                        if v10_stalkfap_dildo == 2:
                                            show v12_stalkfap9_d2
                                        if v10_stalkfap_dildo > 0:
                                            show v12_stalkfap9_d1
                                        if v10_stalkfap_anal > 0 or (v10_stalkfap == "n" and lena_anal > 1) or (v10_stalkfap == 0 and lena_anal > 1):
                                            show v12_stalkfap9_d3
                                        with short
                                        pause 1
                                        if v10_stalkfap == "ian":
                                            $ fian = "confident"
                                            i "But I'm the one behind the camera, there with her."
                                            i "All these guys are living vicariously through me. They wish they could be me."
                                            i "They can only see what I get to do. And I get to do much more than they are able to see."
                                            if v10_stalkfap_facial:
                                                $ fian = "confident"
                                                scene ianroomnight
                                                show ian at left
                                                show v12_stalkfap10
                                                with short
                                                $ ian_lena_pics.append("v12_stalkfap10")
                                                pause 1
                                                i "Only Lena and I have this picture of what happened that day."
                                                i "And while my jizz goes all over her face, all these guys blow their load alone, on their hands."
                                        else:
                                            i "This angle... This picture had to be taken by someone else!"
                                            i "Who could've been? Louise? Or maybe Ivy..."
                                            if ian_lena_couple:
                                                i "I don't like knowing someone else is behind the camera. If anything, it should be me, right...?"
                                            else:
                                                i "It feels weird knowing someone else is behind the camera. Lena was doing this in front of someone..."
                                elif v10_stalkfap != "ian":
                                    $ fian = "n"
                                    i "That's it? I thought she would use a dildo or something..."
                                if v10_stalkfap == "ian":
                                    $ fian = "shy"
                                    i "Remembering that day is enough to turn me on like mad. And all these images are a good testament to it..."
                                    i "There's no way around it. I need to rub one out right now."
                                else:
                                    "I went back and forth between Lena's pictures, taking in the images she had uploaded."
                                    i "There's no way around it... She deserves that I rub one out in her honor."
                                stop music fadeout 2.0
                                if ian_lust < 10:
                                    call xp_up ('lust') from _call_xp_up_1001
                                    pause 0.8
                                scene ianroomnight with long
                                pause 1
                                $ renpy.end_replay()
                                if v10_stalkfap == "mike" and ian_lena_couple == False or v10_stalkfap == "stan" and ian_lena_couple == False or v10_stalkfap == "ian":
                                    $ gallery_unlock_scene('CH12_S05')
                                if v12_jess_date == False:
                                    jump v12tournament

                            "Forget it":
                                $ renpy.block_rollback()
                                scene ianroomnight
                                show ian at left
                                with short
                                show ian at truecenter with move
                                $ fian = "n"
                                if ian_money < 3:
                                    i "Forget it... It's not like I have the money to spend on such a thing anyway..."
                                else:
                                    i "Forget it... I can't be wasting money on such a thing."
                                                           
                                if ian_lena_dating:
                                    i "I should ask Lena to show me directly. I should have the right to see her erotic content too!"
                                    if ian_cuck:
                                        $ fian = "blush"
                                    else:
                                        $ fian = "disgusted"
                                    i "But... What kind of weirdo would ask his girl for such a thing?"
                                    $ fian = "shy"
                                    "Despite how awkward all this was... What I had seen had been enough to get me all hot and bothered."
                                else:
                                    $ fian = "shy"
                                    "What I had seen had been enough to get me all hot and bothered."
                                "Lena deserved I rubbed one out in her honor."
                                stop music fadeout 2.0
                                if ian_lust < 8:
                                    call xp_up ('lust') from _call_xp_up_1002
                                    pause 0.8
                                scene ianroomnight with long
                                pause 1
                                $ renpy.end_replay()
                                if v12_jess_date == False:
                                    jump v12tournament
                
                else:
                    $ fian = "shy"
                    if ian_stalkfap_on == 2:
                        "Needless to say, seeing Lena's posts had been enough to get me all hot and bothered."
                    else:
                        "Despite any concerns I could have, seeing Lena's posts had been enough to get me all hot and bothered."
                    "She deserved I rubbed one out in her honor."
                    stop music fadeout 2.0
                    scene ianroomnight with long
                    if ian_lust < 8:
                        call xp_up ('lust') from _call_xp_up_1003
                    pause 1
                    $ renpy.end_replay()
                    if v12_jess_date == False:
                        scene ianroomnight_dark with long
                        pause 1
                        jump v12tournament

        # stalkfap mild
            else:
                $ fian = "n"
                "I noticed her post had fewer comments than usual."
                i "Seems like she doesn't have much engagement... I wonder how many subscribers she has right now."
                scene ianroomnight
                show ian at left
                show v12_stalkfap1
                if lena_tattoo2:
                    show v12_stalkfap1_t2
                with short
                i "Not many from the looks of it..."
                scene ianroomnight
                show ian at left
                with short
                stop music fadeout 2.0
                show ian at truecenter with move
                i "They've been asking for some nasty content, but there are certain lines Lena doesn't want to cross."
                i "I guess having self-respect isn't exactly profitable in this line of work..."
                if v12_jess_date == False:
                    i "..."
                    i "I should get some sleep."
                    scene ianroomnight with long
                    if ian_lust < 7:
                        call xp_up ('lust') from _call_xp_up_1004
                    pause 1
                    $ renpy.end_replay()
                    jump v12tournament
    else:
        stop music fadeout 2.0
    $ renpy.end_replay()
    if v12_jess_date:
        jump v12jesshome
    else:
        scene ianroomnight with long
        scene ianroomnight_dark with long
        pause 1
        jump v12tournament

########################################################################################################################################################################
## JESS ######################################################################################################################################################################
label v12jesshome:
    scene ianhomenight with long
    $ fian = "n"
    "I finished getting everything ready for tomorrow and I microwaved some food for dinner."
    show ian with short
    i "..."
    "I wondered if Jess would show up at all. It wouldn't surprise me if she had changed her mind without even letting me know."
    play sound "sfx/doorbell.mp3"
    "But when I was least expecting it, the doorbell rang."
    i "That must be her..."
    show ian at lef with move
    play sound "sfx/door_home.mp3"
    show jessb at rig3 with short
    i "Hey..."
    js "What's up? Can I come in?"
    i "Of course."
    play music "music/tension.mp3" loop
    show jessb at rig with move
    i "Do you want something to drink, or...?"
    js "A beer would be nice."
    i "I think Perry left some on the fridge."
    hide jessb with short
    show ian at truecenter with move
    if ian_chad > 3:
        $ fian = "confident"
        i "She's actually here... Things look promising!"
        if v10_jess_porn:
            i "I have the chance to score with a real porn star, or so it looks to me... Those videos were crazy!"
        else:
            i "If what Jeremy said is true, I might have the chance to score with a real porn star, or so it looks to me... How crazy is that?"
    else:
        i "She actually came... What now?"
        if v10_jess_porn:
            i "I might have the chance to score with a real porn star... Those videos were crazy."
        else:
            i "If what Jeremy said is true, I might have the chance to score with a real porn star..."
    if ian_lena_couple:
        if ian_cheating:
            $ fian = "n"
            i "I know I'm risking it by cheating on Lena, but... Can I really pass up this opportunity?"
        else:
            $ fian = "worried"
            i "As tempting as that might sound, I shouldn't. I can't do that to Lena..."
    elif ian_cindy_love:
        $ fian = "worried"
        i "As tempting as that might sound, do I really want to do it? I was with Cindy just a few hours ago..."
    elif ian_holly_dating:
        $ fian = "worried"
        i "As tempting as that might sound, do I really want to do it? I'm about to spend a few days with Holly for the first time..."
    $ fian = "n"
    "I grabbed a beer and went back to the living room, where I found Jessica sitting on the sofa."
    show ian at lef with move
    show jessb at rig with short
    play sound "sfx/beer.mp3"
    i "Here you go."
    js "You didn't get one? I hate drinking alone."
    if tournament:
        i "There was only one left. Besides, I don't want to drink. I have to be at the gym first thing in the morning tomorrow."
        js "..."
    else:
        i "There was only one left."
    js "I see."
    "Jessica took a long sip, staring at the wall, silently."
    menu:
        "{image=icon_lust.webp}Make a move on her" if ian_lust > 5:
            $ renpy.block_rollback()
            $ fian = "confident"
            i "You're pretty quiet, so I take it you didn't come here to talk."
            "Jessica took another sip before looking at me."
            js "No, I didn't."
            if ian_lena_couple:
                "This was a chance I couldn't let slip past me... It would be a one-time thing, nothing that could jeopardize my relationship with Lena."
            elif ian_cindy_love and v12_cindy_rel > 0:
                "This was a chance I couldn't let slip past me... It would be a one-time thing, nothing that could jeopardize my relationship with Cindy."
            elif ian_holly_dating:
                "This was a chance I couldn't let slip past me... It would be a one-time thing, nothing that could jeopardize my relationship with Holly."
            else:
                "This was a chance I couldn't let slip past me... I was determined to see this through."
            i "And I guess you didn't come here just for beer, either."
            js "That's right... I needed a place to crash for the night."
            i "You chose the right place, then."
            jump v12jesshiton

        "{image=icon_charisma.webp}You wanted to see me?" if ian_charisma > 6:
            $ renpy.block_rollback()
            if ian_lena_couple or (ian_cindy_love and v12_cindy_rel > 0) or ian_holly_dating:
                "I decided there was no harm in testing the waters a bit..."
            $ fian = "smile"
            i "So, what do I owe the pleasure of your visit to? I take it you wanted to see me?"
            js "You wish... Don't get cocky."
            call friend_xp ('jess',-1) from _call_friend_xp_1121
            i "Well, I wasn't expecting you to come by tonight. It's been unexpected."
            js "It's not like I planned for it. I just needed a place to crash for the night."
            jump v12jesstalk

        "{image=icon_wits.webp}What aren't you telling me?" if ian_wits > 7:
            $ renpy.block_rollback()
            label v12jesswits:
                $ v12_jess_talk = True
            i "Hey... Is everything alright?"
            js "Yes... Why do you ask?"
            i "I have the feeling there's something you're not telling me."
            js "Oh, yeah? What makes you think that, Mr. Detective?"
            i "Let's see... First of all, I had the feeling you were keeping your distance during our prior date."
            i "Asking to drop by my place out of the blue is quite a sudden turn. Besides, I can tell this isn't the first beer you've had tonight."
            js "I've been drinking a bit... So what?"
            i "I have no idea what prevents you from crashing at your own place, or at Ivy's for that matter..."
            i "The logical explanation would be to think you're looking to get laid, and as much as I'd like to believe that's your real reason for being here..."
            i "I've got this feeling that's not what's going on here."
            $ fjess = "mad"
            js "Alright, Sherlock. You got me."
            js "I'm kinda having a shitty night, and I can't go back to my apartment because if I saw my flatmate right now I couldn't hold myself back from punching her mouth. Repeatedly."
            i "Did you fight with her?"
            js "Among other things. Look..."
            $ fjess = "n"
            js "I decided to come here because I thought you'd let me crash on the couch without having to give you any explanations."
            js "Stupid of me to expect that, coming from the guy who wanted to interview me, I know..."
            js "Maybe I should just leave."
            jump v12jesssleep

        "Why are you here?":
            $ renpy.block_rollback()
            i "Tell me something... How come you texted me out of the blue?"
            js "Is that a problem?"
            i "Not really, but if I'm honest, the last thing I was expecting was to have you here tonight."
            js "It's not like I planned for it. I just needed a place to crash for the night."
            menu v12jesstalk:
                "{image=icon_lust.webp}Make a move on her" if ian_lust > 4 or ian_chad > 3:
                    $ renpy.block_rollback()
                    $ fian = "confident"
                    i "Is that the real reason? I'm starting to think you're here for something else."
                    "Jessica took another sip before looking at me."
                    js "And what would that reason be?"
                    if ian_lena_couple:
                        "After all, this was a chance I couldn't let slip past me... It would be a one-time thing, nothing that could jeopardize my relationship with Lena."
                    elif ian_cindy_love and v12_cindy_rel > 0:
                        "After all, this was a chance I couldn't let slip past me... It would be a one-time thing, nothing that could jeopardize my relationship with Cindy."
                    elif ian_holly_dating:
                        "After all, this was a chance I couldn't let slip past me... It would be a one-time thing, nothing that could jeopardize my relationship with Holly."
                    elif ian_cherry_love:
                        "After all, this was a chance I couldn't let slip past me... It would be a one-time thing, nothing that could jeopardize my relationship with Cherry."
                    else:
                        "After all, this was a chance I couldn't let slip past me... I was determined to see this through."
                    jump v12jesshiton

                "{image=icon_wits.webp}What aren't you telling me?" if ian_wits > 7:
                    $ renpy.block_rollback()
                    jump v12jesswits

                "Are you in trouble?":
                    $ renpy.block_rollback()
                    $ fian = "sad"
                    i "Wait... Did you get in trouble or something?"
                    js "No, I didn't. What makes you think that?"
                    i "The fact that you decided to drop by out of the blue..."
                    $ fjess = "mad"
                    js "I texted you before showing up, didn't I?"
                    $ fian = "n"
                    i "Yeah, but I mean... I had the feeling you were keeping your distance during our prior date. This is a bit sudden."
                    show jessb at rig3 with move
                    js "If it's a problem for you I'll get going."
                    call friend_xp ('jess',-1) from _call_friend_xp_1122
                    $ fian = "sad"
                    i "Wait, I didn't say that... I'm just curious is all."
                    jump v12jesscrash

                "But why here?":
                    $ renpy.block_rollback()
                    i "Alright, but... why here?"
                    $ fjess = "mad"
                    show jessb at rig3 with move
                    js "If it's a problem for you I'll get going."
                    $ fian = "sad"
                    i "Wait, I didn't say that... I'm just a bit confused is all."
                    label v12jesscrash:
                        show jessb at rig with move
                        $ fjess = "serious"
                        js "Well, I'm not really up for explaining tonight, so mind if I crash on the couch?" 
                        js "If not, I'll figure something out, no problem."
                        menu v12jesssleep:
                            "{image=icon_lust.webp}Make a move on her" if ian_lust > 4:
                                $ renpy.block_rollback()
                                $ fian = "confident"
                                if v12_jess_talk:
                                    i "I know that's not why you're here, but since you're spending the night here... It would be such a shame to not share the bed with you."
                                else:
                                    i "It's alright... Actually, it'd be best if you crashed on my bed."
                                if ian_lena_couple:
                                    "This was a chance I couldn't let slip past me... It would be a one-time thing, nothing that could jeopardize my relationship with Lena."
                                elif ian_cindy_love and v12_cindy_rel > 0:
                                    "This was a chance I couldn't let slip past me... It would be a one-time thing, nothing that could jeopardize my relationship with Cindy."
                                elif ian_holly_dating:
                                    "This was a chance I couldn't let slip past me... It would be a one-time thing, nothing that could jeopardize my relationship with Holly."
                                elif ian_cherry_love:
                                    "This was a chance I couldn't let slip past me... It would be a one-time thing, nothing that could jeopardize my relationship with Cherry."
                                else:
                                    "This was a chance I couldn't let slip past me... I was determined to see this through."
                                jump v12jesshiton

                            "{image=icon_friend.webp}You can use the bed" if ian_jess > 3:
                                $ renpy.block_rollback()
                                $ v12_jess_date = 3
                                if ian_chad > 3:
                                    $ ian_chad -= 1
                                $ fian = "n"
                                stop music fadeout 3.0
                                i "You can use the bed, actually. My flatmate's room is free."
                                $ fjess = "n"
                                js "I see... Thanks."
                                call friend_xp ('jess',+2) from _call_friend_xp_1123
                                js "Where's the bathroom?"
                                i "That door over there."
                                stop music fadeout 2.0
                                play sound "sfx/door.mp3"
                                hide jessb with short
                                i "Goodnight..."
                                if ian_will < 2 and v12_jess_talk:
                                    call will_up() from _call_will_up_15
                                hide ian with short
                                scene ianhomenight_dark with long
                                pause 1
                                jump v12tournament

                            "You can spend the night":
                                $ renpy.block_rollback()
                                $ v12_jess_date = 2
                                stop music fadeout 3.0
                                $ fian = "n"
                                i "It's alright... If that's what you need, you can crash on the couch."
                                $ fjess = "n"
                                i "I won't ask any more questions."
                                js "Thanks."
                                call friend_xp ('jess') from _call_friend_xp_1124
                                i "So... I guess I'll go get some sleep. Goodnight."
                                stop music fadeout 2.0
                                play sound "sfx/door.mp3"
                                hide ian with short
                                scene ianhomenight_dark with long
                                pause 1
                                jump v12tournament

                            "You should leave":
                                $ renpy.block_rollback()
                                $ v12_jess_date = 1
                                stop music fadeout 3.0
                                $ fian = "n"
                                i "Yeah, actually... I don't know what's going on here, so I think it'd be best if you left."
                                js "I shouldn't have come here in the first place... I won't bother you anymore."
                                play sound "sfx/door_slam.mp3"
                                hide jessb with vpunch
                                pause 0.5
                                if ian_jess > 3:
                                    call friend_xp ('jess',-1) from _call_friend_xp_1125
                                    $ ian_jess = 3
                                i "That was weird as fuck... She's all over the place. I have no idea what's going through her head."
                                i "I don't think it's a good idea to get involved with a girl like her..."
                                stop music fadeout 2.0
                                scene ianhomenight with long
                                scene ianhomenight_dark with long
                                pause 1
                                jump v12tournament
    
# JESS SEX #######################################################################
label gallery_CH12_S06:
    if _in_replay:
        call setup_CH12_S06 from _call_setup_CH12_S06

label v12jesshiton:
    $ v12_jess_date = 4
    if ian_chad < 5:
        $ ian_chad += 1
        if ian_chad < 2:
            $ ian_chad += 1
    stop music fadeout 3.0
    show ian at centerlef with move
    $ fian = "worried"
    play sound "sfx/punchgym.mp3"
    show ian at lef with hpunch
    "I leaned forward, about to make my move, but Jess put her hand on my chest and held me at bay."
    js "Wait..."
    $ fian = "n"
    js "Where's the bathroom?"
    i "That door over there..."
    js "Alright. Give me a minute."
    play sound "sfx/door.mp3"
    hide jessb with short
    i "..."
    show ian at truecenter with move
    "I sat on the couch, not knowing what I was waiting for exactly."
    i "..."
    i "Did she just turn me down, or...?"
    play sound "sfx/door.mp3"
    show ian at lef with move
    play music "music/sex_vixen.mp3" loop
    $ fjess = "flirt"
    $ jess_look = "under"
    $ fian = "surprise"
    show jessb at rig3 with long
    pause 0.5
    js "Alright... So are we doing this or not?"
    if ian_chad > 3:
        $ fian = "evil"
        i "Hell yeah, we are..."
    else:
        $ fian = "confident"
        i "Yes... Let's do it."
    show jessb at rig with move
    # tease
    scene v12_jess1 with long
    pause 1
    "Jess walked up to me and knelt, and knelt down, unbuttoning my pants and freeing my semi-erect cock."
    "My heart was pounding with anticipation as I watched this goddess of sex caress the shaft and bring her lips close to it."
    "I was about to live out what I had seen in those videos... The ultimate fantasy of countless guys was about to become my reality."
    "I shivered when I felt Jess' hot breath on my cock, which was hard as an iron rod by this point."
    js "So, what do you want to do to me?"
    menu:
        "Fuck her mouth":
            $ renpy.block_rollback()
            i "Easy... For starters, I want to fuck that slutty mouth of yours."
            js "I expected as much."
            "Jessica parted her lips, ready to comply with my demand, but I wasn't going to let her take the reins."
            label v12jessbj:
                $ v12_jess_bj = True
            # mouth
            scene v12_jess2a with long
            "Brimming with hunger, I suddenly stood up, grabbing her wrists and trapping her against the wall."
            i "Open wide...!"
            play sound "sfx/dp1.mp3"
            # bj animation
            scene v12_jess2_animation1 with fps
            pause 4
            i "Mhhh, fuck yes..."
            "Right away, I pushed my cock all the way. Just like I expected, she was able to take it balls-deep without trouble or hesitation."
            "I moved my hips without reservation, enjoying this lewd scene in which I played the lead role."
            "I wondered if I could go even harder, and I decided I could."
            "I had seen guys go much harder at her in some videos."
            play sound "sfx/gag1.mp3"
            scene v12_jess2_animation3 with fps
            pause 4
            if ian_lust < 10:
                call xp_up ('lust') from _call_xp_up_981
            js "{i}\*Ngggh...! Ghhhk!!\*{/i}"
            "I let myself loose, face-fucking her as roughly as my lust dictated."
            if ian_chad > 3:
                i "Damn, that's fucking amazing... You're made to swallow cock!"
            "She gagged and drooled, but I didn't let up, and she didn't ask me to."
            scene v12_jess2_animation2 with fps
            pause 4
            "I kept using her to my heart's content, pounding her throat like I would any other of her other holes."
            scene v12_jess2d with hpunch
            pause 1
            play sound "sfx/ah6.mp3"
            scene v12_jess2e with long
            pause 1
            js "Agh, fuck..."
            "She gasped, catching her breath back as a cascade of gooey saliva dripped down her chin."
            "She looked so fucking filthy and hot!"
            menu:
                "Use her pussy" if v12_jess_pussy == False:
                    $ renpy.block_rollback()
                    i "Time to use your pussy... I want to get a taste of it."
                    jump v12jesspussy

                "{image=icon_lust.webp}Destroy her ass" if v12_jess_anal == False and ian_lust > 6:
                    $ renpy.block_rollback()
                    i "Alright, time to destroy that gaping ass of yours!"
                    jump v12jessanal

                "Cum in her mouth":
                    $ renpy.block_rollback()
                    i "Open your mouth! I'm gonna use this filthy hole to cum!"
                    scene v12_jess2_animation2 with fps
                    pause 4
                    i "Fuck, yes, yes...!!"
                    i "I'm cumming! Yeahhh!!!{w=0.5}{nw}" with flash
                    with vpunch
                    pause 0.5
                    with vpunch
                    pause 0.5
                    with vpunch
                    scene v12_jess2d with hpunch
                    pause 0.5
                    scene v12_jess2e 
                    show v12_jess2_cum 
                    with long
                    pause 1
                    jump v12jesssexend

        "Use her pussy":
            $ renpy.block_rollback()
            i "I need to feel that pussy, right now."
            js "Straight to the main act... alright."
            label v12jesspussy:
                $ v12_jess_pussy = True
            # pussy
            scene v12_jess3 with long
            pause 1
            if v12_jess_bj or v12_jess_anal:
                "This time Jess took the lead, pushing me down to the couch, where she held my cock and pointed it at her slit, sitting on top of it."
            else:
                "Jessica stood up, turned around, held my cock, and sat on top of it, pushing it into her slit."
            "Her pussy engulfed me in a swift motion, and no wonder. That hole had swallowed countless cocks of all shapes and sizes."
            "I felt my spearhead pushing through her soft, wet flesh, settling deep inside her with no problem whatsoever"
            if ian_chad > 3:
                i "So this is how a porn star's pussy feels like... Now show me what you can do with it."
            else:
                i "What a lewd pussy... I want you to show me what you can do with it."
            js "Let's see if you can handle it."
            # pussy animation
            scene v12_jess4_animation1 with fps
            pause 4
            "As she started moving her hips, I felt her loose pussy suddenly tensing up, clamping down on my swollen phallus."
            "The movement of her butt bouncing against my pelvis was hypnotic..."
            "Almost as much as watching my cock appear and disappear in that ravenous pussy with each thrust."
            scene v12_jess4b with fps
            js "So how's that? Do you like it?"
            js "What if I do this?"
            scene v12_jess4_animation2 with fps
            pause 3
            i "Fuck...!"
            "She changed up the rhythm and the motion, grinding her hips from side to side, then in circles, then up and down..."
            i "It's clear you're a real pro at this... You've got all the moves. And this fucking ass..."
            play sound "sfx/slap2.mp3"
            with hpunch
            pause 0.5
            if ian_lust < 10:
                call xp_up ('lust') from _call_xp_up_982
            "I gave her butt a good slap, which only made her move her hips harder."
            i "Mhhh!!"
            js "What's that? Are you gonna cum? Do it already!"
            menu:
                "Fuck her mouth" if v12_jess_bj == False:
                    $ renpy.block_rollback()
                    i "Not until I've enjoyed all of your holes."
                    jump v12jessbj

                "{image=icon_lust.webp}Destroy her ass" if v12_jess_anal == False and ian_lust > 6:
                    $ renpy.block_rollback()
                    i "Not before destroying that gaping ass of yours!"
                    jump v12jessanal

                "Cum in her pussy":
                    $ renpy.block_rollback()
                    i "Fuck yes! I'm gonna blow my load in this filthy pussy of yours!"
                    scene v12_jess4c with flash
                    with vpunch
                    pause 0.5
                    with vpunch
                    pause 0.5
                    with vpunch
                    pause 0.5
                    show v12_jess4_cum 
                    with long
                    pause 1
                    jump v12jesssexend

        "{image=icon_lust.webp}Destroy her ass" if ian_lust > 7:
            $ renpy.block_rollback()
            i "I'm gonna destroy that slutty ass of yours!"
            js "What? Wait...!"
            label v12jessanal:
                $ v12_jess_anal = True
                if ian_chad < 5:
                    $ ian_chad += 1
            # ass
            scene v12_jess5 with vpunch
            play sound "sfx/pain.ogg"
            pause 1
            js "Ahh, fuck! You bastard...!"
            if ian_chad > 3:
                i "What are you complaining about? I know you can take this and more!"
                i "You took half of my cock straight away, without lube or anything..."
            else:
                i "Did it hurt? I thought you could take this and more..."
                i "Look, you took half of it right away, without lube or anything."
            "I pushed my hips forward, spreading her anus even more as more of my tool penetrated her."
            i "See? Your asshole is so lewd and stretchy... It wants my cock!"
            # ass1
            scene v12_jess6a with long
            "I held her head down, pinning her on the sofa while taking an appropriate position."
            # ass2
            scene v12_jess6b with vpunch
            js "Ughh, God! I can feel it in my stomach!"
            # ass animation
            scene v12_jess6_animation with fps
            if ian_athletics < 10:
                call xp_up ('athletics') from _call_xp_up_983
            pause 4
            "I had never been able to vent like this with a woman before, using her without any consideration, knowing she could take it."
            "After all, she had been a professional porn actress. She was used to this."
            "Carried away by a voracious and violent desire, I pounded her ass as hard as I could. I felt like the star of a hardcore porn movie myself." 
            "Jess grunted and moaned beneath me, taking the pounding like a good submissive slut."
            js "Are you enjoying yourself? Are you gonna cum while fucking me like a whore?"
            js "Do it. Shoot your load in my fucking asshole!"
            if v10_jess_porn:
                menu:
                    "{image=icon_will.webp}Rough her up" if ian_will > 0:
                        $ renpy.block_rollback()
                        if ian_chad < 5:
                            $ ian_chad += 1
                            if ian_chad < 3:
                                $ ian_chad = 3
                        call willdown from _call_willdown_67
                        i "That's what you want, huh? Alright."
                        play sound "sfx/oh2.mp3"
                        scene v12_jess7 with long
                        if ian_lust < 10:
                            call xp_up ('lust') from _call_xp_up_1005
                        pause 1
                        "I turned Jess around without pulling out and dug my fingers into her flesh, wrapping my hand around her neck."
                        js "Ugh...!"
                        i "Look at me! I want to see the face you make while I make you my whore...!"
                        i "I know this is how you like it, you dirty slut. I've seen your videos..."
                        "I choked her harder as I kept using her asshole."
                        if ian_athletics < 8:
                            call xp_up ('athletics') from _call_xp_up_1006
                        "Images of her most extreme videos came to my mind. This was my chance to make my fantasies a reality."
                        menu:
                            "{image=icon_lust.webp}Spit on her" if ian_chad == 5 or ian_lust > 7:
                                $ renpy.block_rollback()
                                i "Fuck, you're so filthy... You're made to take it like this!"
                                i "Open wide...!"
                                play sound "sfx/spit.mp3"
                                show v12_jess7_spit1 with vpunch
                                hide v12_jess7_spit1
                                show v12_jess7_spit2
                                with fps3
                                hide v12_jess7_spit2
                                show v12_jess7_spit3
                                with short
                                "I spat on her mouth, staining her with my contemptuous lust."
                                js "This is what you really like? I knew you were hiding under sheep's clothing... Ugh!"
                                "I squeezed her neck tighter as I felt my wild excitement swelling, about to reach the melting point."
                                play sound "sfx/spit.mp3"
                                show v12_jess7_spit1 
                                with fps
                                hide v12_jess7_spit1
                                show v12_jess7_spit2
                                with fps
                                hide v12_jess7_spit2 
                                with fps
                                if ian_charisma < 10:
                                    call xp_up ('charisma') from _call_xp_up_1007
                                i "Yeah, this is what I like... And you're about to make me cum!"
                                label v12jessxtremecum:
                                    scene v12_jess2a with long
                                    i "Open your mouth! I'm gonna use this filthy hole to cum!"
                                    scene v12_jess2_animation2 with fps
                                    play sound "sfx/gag1.mp3"
                                    pause 4
                                    i "Fuck, yes, yes...!!"
                                    i "I'm cumming! Yeahhh!!!{w=1}{nw}" with flash
                                    with vpunch
                                    pause 0.5
                                    with vpunch
                                    pause 0.5
                                    with vpunch
                                    scene v12_jess2d with hpunch
                                    pause 0.5
                                    scene v12_jess2e
                                    show v12_jess2_cum
                                    with long
                                    pause 1
                                    jump v12jesssexend

                            "Cum!":
                                $ renpy.block_rollback()
                                i "I'm about to cum... Open your mouth!"
                                jump v12jessxtremecum
                        
                    "Fuck her mouth" if v12_jess_bj == False:
                        $ renpy.block_rollback()
                        i "Not so fast. You know what would be really hot right now? Some ass-to-mouth!"
                        jump v12jessbj

                    "Use her pussy" if v12_jess_pussy == False:
                        $ renpy.block_rollback()
                        i "Not yet. I still haven't got to enjoy this slutty pussy of yours..."
                        jump v12jesspussy

                    "Cum in her ass":
                        $ renpy.block_rollback()
                        label v12jesscumass:
                            i "Fuck yes! Take it, you filthy whore!"
                        scene v12_jess5 with flash
                        with vpunch
                        pause 0.5
                        with vpunch
                        pause 0.5
                        with vpunch
                        pause 0.5
                        show v12_jess5_cum 
                        with long
                        pause 1
                        jump v12jesssexend
            else:
                menu:
                    "Fuck her mouth" if v12_jess_bj == False:
                        $ renpy.block_rollback()
                        i "Not so fast. You know what would be really hot right now? Some ass-to-mouth!"
                        jump v12jessbj

                    "Use her pussy" if v12_jess_pussy == False:
                        $ renpy.block_rollback()
                        i "Not yet. I still haven't got to enjoy this slutty pussy of yours..."
                        jump v12jesspussy

                    "Cum in her ass":
                        $ renpy.block_rollback()
                        jump v12jesscumass

label v12jesssexend:
    if ian_chad > 3:
        i "God damn... You drained my balls. What a way to fuck..."
    else:
        i "God damn... That was incredible."
    $ fian = "worried"
    $ fjess = "mad"
    stop music
    play sound "sfx/punchgym.mp3"
    scene ianhomenight
    show iannude at lef
    show jessbnude at rig
    with vpunch
    "Suddenly, Jessica pushed me off her."
    js "Alright, are you finally satisfied?"
    $ fian = "disgusted"
    i "Uh... Yeah?"
    js "Then fulfill your part of the deal. I'll take the bedroom."
    js "And don't you dare bother me."
    play sound "sfx/door_slam.mp3"
    hide jessbnude with vpunch
    pause 0.5
    call friend_xp ('jess',-1) from _call_friend_xp_1126
    $ ian_jess = 0
    hide iannude
    show iannude2 at lef
    with short
    i "Wait, that's my room..."
    $ fian = "n"
    i "What the hell...?"
    i "She's psycho... But then again, it's not that surprising, all things considered."
    i "Well, I guess I'll just use Perry's bed tonight..."
    hide ian with short
    scene ianhomenight_dark with long
    pause 1
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH12_S06")
    jump v12tournament

########################################################################################################################################################################
## TOURNAMENT ######################################################################################################################################################################
label v12tournament:
    call calendar(_month="August", _day="Saturday", _week=1) from _call_calendar_123
    scene ianhome with long
    if tournament:
        if ian_chad > 2:
            "Next morning I woke up with the buzzer. I didn't have any trouble getting out of bed; I felt ready and excited."
        else:
            "Next morning I woke up with the buzzer. I didn't have any trouble getting out of bed; I felt anxious and excited."
    else:
        "Next morning I woke up with the buzzer. I forced myself to get out of bed, yawning."
    $ fian = "n"
    if v12_jess_date > 1:
        $ ian_look = 3
        $ ian_look = "summer"
        play sound "sfx/door.mp3"
        show ianunder with short
        if v12_jess_date == 4:
            "The door to my bedroom was open, and Jess was nowhere to be found."
            $ fian = "evil"
            i "I guess she left already... Man, what a night. Jeremy would be so jealous if I told him."
            $ fian = "n"
            if v12_jess_talk:
                i "Jess didn't wanna get into the details, but it seems she has a troubled life..."
                i "Not my problem, though. I already got a piece of her."
            else:
                i "I wonder what's going on with Jess' life, but I was more interested in getting a piece of her..."
            $ fian = "n"
            if ian_holly_dating:
                i "I feel kinda bad for doing that when I have Holly... But I could never fuck Holly like that."
                if ian_cherry_love:
                    i "And I won't be able to see Cherry in a while..."
                i "It was just a one-time thing, anyway."
            elif ian_cindy_love and v12_cindy_rel == 1:
                i "I feel kinda bad for doing that when I have Cindy... But I could never fuck Cindy like that."
                i "It was just a one-time thing, anyway."
            elif ian_cherry_love:
                i "I feel kinda bad for doing that when I have Cherry... But I won't be able to see her for a while."
                i "It was just a one-time thing, anyway."
            elif ian_lena_couple:
                i "I shouldn't have cheated on Lena, but I could never fuck her like I did Jess. Let's hope she doesn't learn about this."
                i "It was just a one-time thing, anyway."
            elif ian_lena_dating:
                i "I hope Lena doesn't learn about this... I have the feeling she wouldn't exactly like it."
                i "It was just a one-time thing, anyway."
            else:
                i "Anyway, I have stuff to do."
            hide ianunder with short
            show ian with short
        elif v12_jess_date == 2:
            "I looked around for Jess, but she was nowhere to be found."
            i "I guess she left already... I wonder what was going on with her last night."
            if v12_jess_talk:
                i "She didn't wanna get into the details, but it seems she has a troubled life..."
            else:
                i "It doesn't look like she's gonna tell me... She's a weird one."
            i "Anyway, I have stuff to do."
        elif v12_jess_date == 3:
            if tournament:
                "I brewed some coffee and prepared some breakfast. I was gonna need the energy."
            else:
                "I started my day by brewing some coffee and preparing some breakfast."
            $ fjess = "n"
            $ jess_look = "under2"
            show ianunder at lef with move
            play music "music/date.mp3" loop
            show jessb at rig3 with long
            js "Hey... Is that coffee?"
            $ fian = "smile"
            i "Good morning... Yeah, want some?"
            js "I could use a cup."
            show jessb at rig with move
            i "Here... I can see you're not a morning person, huh?"
            $ fjess = "mad"
            if v12_jess_talk:
                js "Sherlock keeps making deductions... Get off my back, will you?"
                $ fjess = "n"
            else:
                js "Yeah? What makes you think so, Sherlock?"
                $ fjess = "n"
                js "Scratch that... I don't even wanna know."
            $ fian = "n"
            i "Not the best night's sleep, huh?"
            $ fjess = "sad"
            js "Insomnia and I are old friends, as you can surely tell by these damn dark circles I'm sporting..."
            $ fjess = "n"
            js "But I've slept decently. Thanks for letting me crash at your place."
            if v12_jess_talk:
                i "It's alright... I don't know what you're going through and I'm not gonna pry."
            else:   
                i "Sure, no problem. I still don't know why you needed it, but I'm not gonna pry..."
            $ fian = "smile"
            i "But if you ever feel like getting something out of your chest... Well, you can try me."
            if v12_jess_talk:
                js "I wouldn't want to bore you with it... But thanks anyway."
                i "I doubt your life is boring at all, but even if it is, I'll listen."
                js "You just want some stories for your books, don't you?"
            else:   
                js "Don't count on it... I suspect you just want some stories for your books, don't you?"
            menu:
                "{image=icon_friend.webp}I'm interested in you" if ian_jess > 4:
                    $ renpy.block_rollback()
                    $ ian_jess_love = True
                    i "I'm interested in your stories, but not because of that."
                    i "I'm just interested in you."
                    js "In me? What are you talking about?"
                    js "You don't know shit about me..."
                    i "That's why I want to get to know you better. You know you caught my attention from the very beginning..."
                    js "Oh, yeah? I wonder why's that..."
                    js "Tell me: what do you know about me, exactly? You're aware of what I used to do, aren't you?"
                    $ fian = "n"
                    if v10_jess_porn:
                        i "You mean porn, right? Well, yeah, I'm aware of it... But I only learned about it after meeting you."
                        js "Of course, you did... And that makes you super curious about me, is that it?"
                        i "I'm not gonna lie: it's the first time I met someone with your life experiences, and yeah, it makes me curious..."
                        i "But that's not the reason I approached you at the bar, nor why I let you crash at my place today."
                        js "How noble of you."
                    else:
                        i "You mean...? Well, I heard some rumors..."
                        js "Rumors about what? Come on, spit it out. You know I used to do porn, right?"
                        i "As I said, I heard some rumors, but I never took the time to check them out."
                        js "Then maybe you're not as curious about me as you're making it to be."
                    i "Hey, believe what you will... I'm sure you have plenty of reasons to be skeptical, but I'm not out to convince you of anything."
                    i "I'm just being straightforward."
                    "Jess looked at me for a moment, silent."
                    js "You're a weird one..."
                    if ian_charisma < 10:
                        call xp_up ('charisma') from _call_xp_up_984

                "You got me":
                    $ renpy.block_rollback()
                    i "Yeah, you got me."
                    $ fian = "happy"
                    if book_scifi:
                        i "Normally I like to write about spaceships and aliens, but I'm sure your life story is equally fascinating."
                    elif book_fantasy:
                        i "Normally I like to write about dragons, but I'm sure your life story is equally fascinating."
                    elif book_historical:
                        i "Normally I like to write about the great periods in History, but I'm sure your life story is equally fascinating."
                    js "Ha ha, you're so funny. Not."
                    i "Hey, you knew from the start my interest in you was professional."
                    "Jess looked at me for a moment, silent."
                    $ fian = "n"
                    js "You're a weird one..."
                    call friend_xp ('jess') from _call_friend_xp_1127

                "Not really":
                    $ renpy.block_rollback()
                    if book_scifi:
                        i "Not really... I like to write about other subjects, you know. Spaceships and aliens..."
                    elif book_fantasy:
                        i "Not really... I like to write about other subjects, you know. Dragons, knights, and kingdoms at war..."
                    elif book_historical:
                        i "Not really... I like to write about other subjects, you know. Explore the great periods in History..."
                    js "Nerd."
                    i "Hey, you should feel relieved."
                    js "Yeah, I guess..."
                    if ian_wits < 8:
                        call xp_up ('wits') from _call_xp_up_985

            "I took one last sip of my coffee."
            $ fian = "smile"
            if tournament:
                i "As much as I enjoy talking to you, I need to get going. I can't show up late for the tournament." 
            else:
                i "As much as I enjoy talking to you, I need to get going. I should get to the gym before Jeremy's turn to fight."
            js "Sure. I'm done bothering you anyway... I should get going too."
            stop music fadeout 2.0
            scene ianhome with long
            if tournament:
                "I packed all my gear into the bag, slung it over my shoulder, and set off to go to the gym."
    else:
        $ ian_look = "summer"
        show ian with short
    if tournament and v12_jess_date != 3:
        "I packed all my gear into the bag, slung it over my shoulder, and set off to go to the gym."
        if ian_chad == 5:
            $ fian = "confident"
        elif ian_chad < 2:
            $ fian = "smile"
        i "Let's do this."
    else:
        i "I told Jeremy I'd go watch his fight... I should leave now or I'll be late."
## TOURNAMENT
    scene street with long
    pause 0.5
    play music "music/jeremys_theme.mp3" loop
    scene gym with long
    $ ian_look = "summer"
    $ fian = "n"
    $ fjeremy ="smile"
    $ jeremy_look = "mma"
# NO TOURNAMENT
    if tournament == False:
        show ian with short
        "The gym was unusually crowded that morning. Guys from different clubs had come to participate in the tournament, and they were warming up in preparation."
        show ian at lef with move
        show jeremy at rig with short
        j "Hey! There you are!"
        if ian_jeremy < 3:
            i "Yeah... Seems like you're ready."
            play sound "sfx/miss.mp3"
            $ fjeremy = "flirt"
            show jeremy at rig5b with move
            show jeremy at rig with move
            "Jeremy threw a flurry of punches into the air, getting himself ready for action."
            j "Yeah! My turn will be any moment now!"
        elif ian_jeremy < 7:
            $ fian = "smile"
            i "Yup... How are you feeling? Ready?"
            play sound "sfx/miss.mp3"
            $ fjeremy = "flirt"
            show jeremy at rig5b with move
            show jeremy at rig with move
            "Jeremy threw a flurry of punches into the air, getting himself ready for action."
            j "Yeah! I'm stepping onto the mat any moment now...!"
        else:
            $ fian = "smile"
            i "Of course. You look ready to go!"
            play sound "sfx/miss.mp3"
            $ fjeremy = "flirt"
            show jeremy at rig5b with move
            show jeremy at rig with move
            "Jeremy threw a flurry of punches into the air, getting himself ready for action."
            j "I am! I'll be stepping onto the mat any moment now...!"
        
## IAN TOURNAMENT
    else:
        "The gym was unusually crowded that morning. Guys from different clubs had come to participate in the tournament, and they were warming up in preparation."
        show ian with short
        i "I still don't know who I'm fighting today, but he's someone among these guys..."
        show ian at lef with move
        show jeremy at rig with short
        j "Hey! There you are!"
        if ian_jeremy < 3:
            i "Hey... Seems like you're already ready to go."
            play sound "sfx/miss.mp3"
            $ fjeremy = "flirt"
            show jeremy at rig5b with move
            show jeremy at rig with move
            "Jeremy threw a flurry of punches into the air, getting himself warmed up to fight."
            j "Yeah! My turn will be any moment now!"
        elif ian_jeremy < 7:
            $ fian = "smile"
            i "Hey there... How are you feeling? Ready?"
            play sound "sfx/miss.mp3"
            $ fjeremy = "flirt"
            show jeremy at rig5b with move
            show jeremy at rig with move
            "Jeremy threw a flurry of punches into the air, getting himself warmed up to fight."
            j "Yeah! I'm stepping onto the mat any moment now...!"
        else:
            $ fian = "smile"
            i "What's up, dude? Ready to go?"
            play sound "sfx/miss.mp3"
            $ fjeremy = "flirt"
            show jeremy at rig5b with move
            show jeremy at rig with move
            "Jeremy threw a flurry of punches into the air, getting himself warmed up to fight."
            j "I am! I'll be stepping onto the mat any moment now...!"
    # mike
    play sound "sfx/fall.mp3"
    $ fian = "worried"
    $ fjeremy = "sad"
    with vpunch
    "One of the fierce matches already underway caught our attention."
    "After an impressive takedown, one of the fighters choked out his rival and was declared the winner."
    "He looked familiar..."
    $ mike_look = "mma"
    $ mike_extras = 0
    $ fmike = "smile"
    $ fian = "n"
    $ fjeremy = "happy"
    show ian at lef3
    show jeremy at rig3
    with move
    show mike with short
    j "Dude, Mike! What are you doing here? I didn't know you trained!"
    mk "Hey there... I started recently. I've been wanting to give it a try, and this tournament thing looked like it could be fun."
    j "Damn, really? That was an impressive win!"
    $ fmike = "happy"
    mk "I was lucky. My opponent was even more of a noob than I am."
    $ fmike = "smile"
    mk "Have you guys fought yet?"
    $ fjeremy = "flirt"
    j "Not yet... I'm about to!"
    if tournament:
        i "Yeah, I still need to get changed."
        mk "Don't let me keep you then. Good luck, guys."
    else:
        mk "And you?"
        i "Nah... I only came to show some support."
        mk "That's cool. Well, don't let me keep you. Good luck!"
    $ fjeremy = "smile"
    j "Thanks!"
    hide mike with short
    show ian at lef
    show jeremy at rig
    with move
    if tournament == False:
        $ fjeremy = "smile"
        i "Where's Yuri, by the way?"
        show ian at lef3
        show jeremy at rig3
        with move
        show wen2 with short
        wen "He's gone to Thailand for another one of his training camps. But don't worry, I'll be your second today."
        j "I'm not worried! I got this!"
        wen "Well, wrap up your warm-up. You're up next."
    else:
        j "We need to get a win too... How are you feeling? Nervous?"
        if ian_chad > 3:
            $ fian = "confident"
            i "Nah... I've been looking forward to this. I'm excited!"
        elif ian_chad > 1:
            $ fian = "smile"
            i "Nervous, and excited... It's hard to tell the difference."
        else:
            $ fian = "n"
            i "Quite a bit, yeah... I'm starting to feel the pressure."
        show ian at lef3
        show jeremy at rig3
        with move
        show wen2 with short
        wen "You should get changed and start warming up. Your weight bracket is up after Jeremy's."
        if ian_chad > 3:
            $ fian = "smile"
        $ fjeremy = "n"
        j "Where's Yuri? He's not coaching today?"
        wen "He's off to Thailand for his summer training camp. But don't worry, I'll be your second today."
        $ fjeremy = "flirt"
        j "I'm not worried! I got this!"
        i "I'll go get changed, then..."
        $ fjeremy = "n"
        hide ian with short
        $ ian_look = "mma"
        pause 1
        show ian at lef3 with long
        $ fjeremy = "happy"
        j "Nice shorts!"
        wen "Get ready, Jeremy. You're up next."
## JEREMY FIGHT
    $ fjeremy = "n"
    hide wen2 with short
    show jeremy at rig
    show ian at lef
    with move
    j "..."
    menu:
        "You've got this":
            $ renpy.block_rollback()
            $ fian = "smile"
            i "You've got this, man. Keep your guard up and use your reach to keep him away and pick him out from the outside."
            i "And if he manages to close the distance, hit him in the stomach and push him off before he can get a clinch."
            $ fjeremy = "smile"
            j "Good advice. Let's do this!"
            if ian_jeremy < 12:
                call friend_xp ('jeremy') from _call_friend_xp_1128
                pause 0.5
            
        "You look nervous":
            $ renpy.block_rollback()
            $ fian = "n"
            i "You look nervous..."
            j "Nervous? Not at all..."
            j "I mean, just a bit. It's normal, right?"
            if ian_wits < 7:
                call xp_up ('wits') from _call_xp_up_986
            i "Don't freak out. It's just an amateur interclub tournament..."
            j "I'm not freaking out. I'm used to performing under pressure..."
            j "Shit, it's my turn. Wish me luck."
            
        "Good luck":
            $ renpy.block_rollback()
            i "Good luck, dude."
            j "Yeah..."

    hide jeremy with short
    show ian at lef with move
    show wen2 at rig with short
    i "What do you think? Does he have a chance?"
    wen "He'll do fine. He's a bit nervous, but that'll fade once he lands a couple of shots."
    wen "Look, they're starting..."
    play sound "sfx/punch.mp3"
    scene v12_mma_jeremy with hpunch
    pause 1
    "Wen was right. As soon as the fight started, Jeremy kept his rival at bay with crisp jabs and punished his mistakes with kicks."
    "As he gained confidence, his shots became more solid and precise, frustrating the other guy, who was barely able to lay a hand on Jeremy."
    "After two rounds, it was obvious who the winner was, and the judges awarded the fight to Jeremy."
    $ fjeremy = "happy"
    $ fian = "smile"
    scene gym
    show jeremy 
    show ian at lef3
    show wen2smile at rig3
    with long
    j "I won!"
    if ian_jeremy < 3:
        i "Well done..."
    elif ian_jeremy < 7:
        i "Well done! That was a good showing."
    else:
        i "Awesome, man! Easy win!"
    j "I was a bit tense at first, but all that training paid off. I think I could've finished him if I put my foot on the gas a bit harder...!"
    hide wen2smile
    show wen2 at rig3
    wen "You did well. Technique was on point, and you defended almost everything. That's what's important."
    j "Man, it left me wanting more! I can't wait for the next round of the tournament...! I wish it were today!"
    if tournament:
        jump v12iantournament
    wen "I need to coach another guy now. See you two in class!"
    j "Thanks, Wen!"
    hide wen2 with short
    show ian at lef
    show jeremy at rig
    with move
    j "Did you see how I dodged that uppercut? And then, {i}WHACK{/i}! I nailed him with a body kick!"
    j "I thought that would fold him, but he was a tough dude..."
    if ian_jeremy < 3:
        i "Yeah, that was cool..."
    elif ian_jeremy < 7:
        i "You had the fight on the bag from the get-go."
    else:
        i "You dominated every second of the fight. He didn't stand a chance."
    $ fjeremy = "smile"
    stop music fadeout 2.0
    j "I'm gonna get changed... Wait for me, it'll only take a minute."
    stop music fadeout 2.0
    hide jeremy with short
    jump v12jeremytalk

## IAN TOURNAMENT FIGHT ##########################################################################################

label v12iantournament:
    wen "You're up next, Ian. Get ready."
    $ fian = "n"
    $ fjeremy = "smile"
    i "I still don't know who I'm fighting..."
    wen "I just checked. It's that guy over there."
    stop music fadeout 2.0
    scene gym
    show fighter
    with long
    pause 0.5
    "I looked to where Wen was pointing."
    play music "music/tension.mp3" loop
    "A menacing-looking guy stared right back at me. He seemed ready and eager to get into a fight."
    $ fjeremy = "sad"
    if ian_chad > 3:
        $ fian = "n"
    elif ian_chad > 1:
        $ fian = "insecure"
    else:
        $ fian = "disgusted"
    scene gym
    show ian
    show jeremy behind ian at lef3
    show wen2smile at rig3
    with long
    i "That's the guy?"
    wen "So it seems."
    if ian_chad > 3:
        i "He looks tough..."
    elif ian_chad > 1:
        i "He looks like he means business..."
    else:
        i "Are you kidding? He looks like he wants to kill me...!"
    hide wen2smile
    show wen2 at rig3
    wen "Don't let his looks intimidate you. Remember, technique is what matters most."
    $ fjeremy = "n"
    j "He looks big... I thought he might be in my weight division."
    wen "He probably cuts weight to make it. But don't worry about that right now."
    wen "Just remember what we practiced and keep your cool. Everything else will fall in place, as long as you don't hesitate."
    if ian_chad > 3:
        i "Got it..."
    elif ian_chad > 1:
        i "Alright..."
    else:
        i "Easier said than done..."
    $ fjeremy = "happy"
    j "Go get him!"
    hide jeremy with short
    show ian at lef
    show wen2 at rig
    with move
    wen "Alright. Ready?"
    menu:
        "Yeah!":
            $ renpy.block_rollback()
            $ fian = "serious"
            i "Yeah! Let's do this!"
            hide wen2
            show wen2smile at rig
            wen "That's the spirit! Show him what you've got!"
            if ian_chad > 3:
                "I started hopping in place, releasing tension from my joints. I had been looking forward to this moment."
            elif ian_chad > 1:
                $ ian_chad += 1
                "I started hopping in place, releasing tension from my joints and trying to calm my jitters down."
                "Despite my nerves, I had been looking forward to this moment..."
            else:
                $ ian_chad += 1
                "I started hopping in place, releasing tension from my joints and trying to calm my jitters down."
                "It was too late to back down now. Despite my nerves, I was determined to see this through."

        "Any advice?":
            $ renpy.block_rollback()
            i "Do you have any advice?"
            wen "We don't know if he favors striking or grappling. He could be good at both as far as we know."
            wen "The good thing is he doesn't know anything about you, either. Stick to what you've trained and what you're good at."
            wen "Getting into a striking contest is always a bit of a gamble, while grappling with him can help you maintain in control of the fight." 
            wen "Use the first minute of the fight to study him, see how he reacts, and what his strengths are."
            wen "Or you could rush him you take him by surprise..."
            wen "It's up to you, but, in any case, keep a cool head and show him different looks to keep him on his toes."
            i "Got it."
            if ian_wits < 8:
                call xp_up ('wits') from _call_xp_up_987

        "I'm not sure":
            $ renpy.block_rollback()
            $ fian = "sad"
            i "Actually... I'm not sure."
            if ian_chad > 3:
                $ ian_chad -= 1
                "I had been trying to play tough, but now I wasn't feeling so confident."
            elif ian_chad > 1:
                $ ian_chad -= 1
                "I had been trying to stay calm, but nerves were threatening to take over."
            hide wen2
            show wen2smile at rig
            wen "Well, it's too late to hesitate now! Go get him!"
            wen "And remember: getting into a striking contest is always a bit of a gamble, while grappling with him can help you maintain control of the fight."
            wen "See what he does and play to your own strengths!"
            $ fian = "n"

    stop music fadeout 4.0
    hide wen2 
    hide wen2smile
    with short
    $ jeremy_look = 2
    show ian at lef3 with move
    show fighter at rig3 with short
    "I stepped into the mat and faced my rival."
    "I saw in his stare his intention to finish me. It was clear he was taking this fight really seriously."
    if ian_chad > 3:
        $ fian = "mad"
        "I stared back at him, refusing to be intimidated. I was serious about this too."
    elif ian_chad > 1:
        $ fian = "serious"
        "I took a deep breath and stared back at him. I wasn't gonna show him any weakness before the start of the fight."
    else:
        "I took a deep breath and tried to calm down my nerves. It was too late to doubt myself now."
    ref "Let's go over the rules. This is a two-round, three-minute bout, under amateur MMA rules."
    ref "Obey my instructions at all times. No elbows or knees are allowed. Strikes on the ground are also forbidden."
    ref "Don't hit the back of the head. Avoid eye-pokes, head-butts, and strikes to the groin."
    ref "I want a good clean fight. Blue corner, are you ready?"
    show fighter at rig with move
    fg "Yes."
    ref "Red corner, are you ready?"
    show ian at lef with move
    i "Yeah."
    play music "music/fight3.mp3" loop
    ref "Fight!{w=0.5}{nw}" with hpunch
    jump v12mmafight


# END FIGHT ###############################################################################################################################################################

label v12mmafightend:
    hide screen mma_clock
    stop music fadeout 2.0
    $ jeremy_look = 2
    if tournament == "winko" or tournament == "winsub":
        $ fian = "n"
        scene gym
        show ian at lef 
        with long
        i "..."
        ref "Winner...! Red corner!!"
        play music "music/jeremys_theme.mp3" loop
        $ fian = "happy"
        jump v12tournamentwin
    elif tournament == "loseko" or tournament == "losesub":
        $ fian = "insecure"
        $ fjeremy = "sad"
        show ian with long
        i ".{w=0.5}.{w=0.5}.{w=0.5}"
        show jeremy at rig3
        show wen2 at lef3
        with short
        if tournament == "loseko":
            j "Dude, are you okay?!"
            wen "How's your equilibrium? Can you stand?"
            i "Yeah, I'm okay... My jaw hurts, though. What happened?"
            j "He caught you with a head kick. That dude has nasty striking..."
            wen "He was very good."
            "Wen finished checking me for damage and injuries."
            wen "You'll be fine, aside from some bruises and swelling. You might want to take a couple of painkillers, though!"
            i "I think I will... And I could use some ice too."
            j "Let's take you to the medical area."
            scene gym with long
        else:
            j "Dude, are you okay?"
            i "Yeah. Well... For the most part."
            wen "He sank the choke very deeply. There was no way of getting out of that."
            "I coughed."
            i "Yeah... He was strong...!"
            wen "Well, you'll be fine, aside from a few bruises and a sore throat."
            if v12_ian_score >= v12_rival_score and jiujitsu < 4:
                j "Sucks that he resorted to jiu-jitsu... You were schooling him in the standup!"
            elif v12_ian_score >= v12_rival_score:
                j "It was brutal. You were schooling him until he choked you like that!"
            else:
                j "It was brutal. I thought you might actually get this..."
            i "It is what it is, I guess... I'll go get changed."
            hide ian with short
            scene gym with long
        pause 1
    elif tournament == "tapout":
        $ fian = "n"
        $ fjeremy = "sad"
        show ian at truecenter with move
        show jeremy at rig3
        show wen2 at lef3
        with short
        j "Dude, what was that? Why did you quit like that?"
        if ian_jeremy > 3:
            call friend_xp ('jeremy',-1) from _call_friend_xp_1129
        wen "Did you injure yourself?"
        i "Yeah, well... I don't feel good."
        i "I'll go get changed..."
        hide ian with short
        if ian_chad > 1:
            $ ian_chad -= 1
        scene gym with long
        pause 1
    else:
        show v12_mma_clock_b0
        show v12_mma_clock_0
        ref "Time!"
        ref "Fight's over! Get back to your corners."
        $ fian = "n"
        scene gym
        show ian 
        with long
        play music "music/jeremys_theme.mp3" loop
        show ian at lef3 with move
        show wen2 with short
        wen "Well done coming back in one piece!"
        if v12_position == "armbar" or v12_position == "guillotine":
            i "Damn, I was about to finish him."
            wen "That was a great position to end the fight with. Let's see how the judges have scored it..."
        elif v12_position == "rnc":
            $ fian = "worried"
            i "Saved by the bell...!"
            wen "That wasn't a good position to end the fight with... Let's see how the judges have scored it."
        elif v12_position == "grounbottom":
            $ fian = "sad"
            i "His ground game is pretty good too..."
            wen "That wasn't the best position to end the fight with, but let's see how the judges have scored it..."
        elif v12_position == "groundtop":
            wen "That was a good position to end the fight with. Let's see how the judges have scored it..."
        if v12_ian_score >= v12_rival_score + 3:
            wen "You showed him a good fight! You should be proud."
            $ fian = "smile"
            $ tournament = "win2"
        elif v12_ian_score >= v12_rival_score + 2:
            wen "You showed him a good fight. You should be proud."
            $ tournament = "win1"
        elif v12_rival_score >= v12_ian_score + 3:
            $ fian = "worried"
            i "He gave me quite a beating..."
            wen "You hung in there, you should be proud."
            $ tournament = "lose2"
        elif v12_rival_score >= v12_ian_score + 2:
            wen "It was a close one... He was very good, but you were no pushover."
            $ tournament = "lose1"
        else:
            $ tournament = "draw"
        "After a few seconds of deliberation, the referee called us back to the center of the mat to announce the scorecards."
        hide wen2 with short
        $ fian = "n"
        show ian at lef with move
        show fighter at rig with short
        if tournament == "win2" or tournament == "win1":
            if tournament == "win2":
                ref "The judges have scored this bout for the winner, by unanimous decision..."
            else:
                ref "The judges have scored this bout for the winner, by split decision..."
            ref "Red corner!"
            $ fian = "happy"
            i "Yes!" with hpunch
            hide fighter with short
            label v12tournamentwin:
                show ian at truecenter with move
            $ fjeremy = "happy"
            show wen2smile at lef3
            show jeremy at rig3
            with short
            j "Awesome, dude! You did it!"
            i "Yeah!"
            wen "Good job. That was a fight!"
            i "It was... Damn, he was trying to take my head off!"
            hide wen2smile
            show wen2 at lef3
            if tournament =="winko":
                j "But it was you who took his instead. What a kick! When did we train these?"
                $ fian = "smile"
                i "I don't know, it just came out spontaneously at that moment..."
                wen "You were zoned in. Seems like all that training paid off, huh?"
                if ian_will < 2:
                    call will_up() from _call_will_up_16
                i "Yeah..."
            elif tournament == "winsub":
                wen "You took him to school on the mat, though. That submission was perfect, well done!"
                $ fian = "smile"
                if ian_will < 2:
                    call will_up() from _call_will_up_17
                i "Thanks..."
                j "He made you work for it, but in the end, you got him! He'll be hurting tomorrow after hitting the mat so much..."
            elif tournament == "win2":
                wen "He was pretty good, and very motivated, but everyone saw you got this win in the bag."
                j "When did you get this good, man? That was sick. Congrats!"
                $ fian = "smile"
                if ian_will < 1:
                    call will_up() from _call_will_up_18
                i "Thanks..."
            else:
                $ fian = "smile"
                wen "It was a close one... He was pretty good, and very motivated."
                j "Well, the judges thought you did enough to get the win in the bag! Congrats, man."
                i "Thanks..."
            wen "Come on, get changed, and let's get going."
            hide ian with short
        elif tournament == "draw":
            ref "The judges have scored this bout..."
            ref "A draw!"
            $ fian = "sad"
            i "..."
            hide fighter with short
            show ian at truecenter with move
            $ fian = "n"
            $ fjeremy = "smile"
            show wen2 at lef3
            show jeremy at rig3
            with short
            j "You did great!"
            i "I'm a bit disappointed with the decision. I wasn't expecting it to be a draw..."
            wen "These things can happen. Either way, don't be sour about it."
            wen "As Jeremy said, you did good back there. That was a fight!"
            $ fian = "smile"
            i "It sure was..."
            wen "Come on, get changed, and let's get going."
            hide ian with short
        else:
            if tournament == "lose2":
                ref "The judges have scored this bout for the winner, by unanimous decision..."
            else:
                ref "The judges have scored this bout for the winner, by split decision..."
            ref "Blue corner!"
            $ fian = "worried"
            fg "Yes!" with hpunch
            hide fighter with short
            "My opponent leaped with excitement and joined his coach to celebrate the win."
            $ fian = "sad"
            show ian at truecenter with move
            $ fjeremy = "sad"
            show wen2 at lef3
            show jeremy at rig3
            with short
            j "Fuck! I thought you might actually get this..."

    stop music fadeout 2.0
    jump v12jeremytalk

## JEREMY CONVERSATION ###############################################################################################################################################################
label v12jeremytalk:
    $ timeout_label = None
    scene street with long
    $ jeremy_look = 1
    $ ian_look = "summer"
    if tournament == "tapout":
        $ fian = "n"
        $ fjeremy = "n"
    elif tournament == "loseko" or tournament == "losesub":
        $ fian = "sad"
        $ fjeremy = "n"
    elif tournament == "win2" or tournament == "win1" or tournament == "winko" or tournament == "winsub":
        $ fian = "smile"
        $ fjeremy = "smile"
    elif tournament == False:
        $ fjeremy = "smile"
    else:
        $ fian = "n"
        $ fjeremy = "smile"
    play music "music/normal_day4.mp3" loop
    if tournament:
        show ian at lef3
        show wen2
        show jeremy at rig3
    else:
        show ian at lef
        show jeremy at rig
    with long
    if tournament == False:
        j "Ready. Let's go."
    elif tournament == "tapout":
        wen "Well... You did well today, Jeremy. We'll keep training for the next round of the tournament."
        $ fjeremy = "smile"
        wen "See you guys in class!"
        j "Bye!"
        hide wen2 with short
        show ian at lef
        show jeremy at rig
        with move
        i "Let's go..."
    elif tournament == "loseko" or tournament == "losesub":
        j "Are you feeling better now?"
        $ fian = "n"
        i "Yeah, I said I'm okay."
        wen "It sucks he finished you, but as they say, you win or you learn."
        if ian_athletics < 10:
            call xp_up ('athletics') from _call_xp_up_988
            if ian_athletics < 7:
                call xp_up ('athletics') from _call_xp_up_989
        i "I would've liked to win instead of learning this time, though."
        $ fjeremy = "smile"
        wen "Experiences like these are what forge your character. Besides, you can still redeem yourself in the next round of the tournament."
        i "I can still participate?"
        wen "You'll fight another guy in your bracket, and the one who loses will be eliminated from the tournament. You still have a chance!"
        wen "Anyway, I gotta go. See you guys in class!"
        j "Bye!"
        hide wen2 with short
        show ian at lef
        show jeremy at rig
        with move
        i "Let's go..."
    elif tournament == "winko" or tournament == "winsub":
        "We left the gym while discussing with euphoria the matches we had just fought."
        j "That was amazing. Man, I wish I had gotten a finish too!"
        wen "Your fight was more like a sparring session, but Ian fought a war. His opponent really brought it on."
        j "I want to fight someone like that in the next round!"
        "I felt happy and excited after my match, relishing the sensation of the spectacular victory I had achieved."
        if ian_athletics < 10:
            call xp_up ('athletics') from _call_xp_up_990
            if ian_athletics < 9:
                call xp_up ('athletics') from _call_xp_up_991
            if ian_athletics < 8:
                call xp_up ('athletics') from _call_xp_up_992
        if tournament == "winko" and kickboxing < 6:
            $ kickboxing += 1
        if tournament == "winsub" and jiujitsu < 5:
            $ jiujitsu += 1
        "Not only had I tested my skills, but also my character and determination, and the outcome was very gratifying."
        wen "Well done, guys. I hope this serves as motivation for you two to keep training hard!"
        wen "Anyway, I gotta go. See you guys in class!"
        i "Thanks, Wen."
        j "Bye!"
        hide wen2 with short
        show ian at lef
        show jeremy at rig
        with move
        i "Come on, let's get going."
    elif tournament == "win2" or tournament == "win1":
        "We left the gym while discussing animatedly the matches we had just fought."
        if tournament == "win2":
            i "I still can't believe I won... That guy was dangerous. He could've knocked me out at any moment!"
            j "He was really tough! A win over a guy like that is no easy feat, and you dominated!"
        else:
            j "Seems both of us did well today! It's so cool we got a win."
            i "I'm happy I managed to edge out the victory, but that was close... He could've knocked me out at any moment!"
            j "He was really tough! A win over a guy like that is no easy feat!"
        wen "That was a real fight! A good experience to put your training to the test and get better."
        if ian_athletics < 10:
            call xp_up ('athletics') from _call_xp_up_993
            if ian_athletics < 8:
                call xp_up ('athletics') from _call_xp_up_994
            if ian_athletics < 7:
                call xp_up ('athletics') from _call_xp_up_995
        i "Yeah, I feel I learned a lot... And winning feels awesome, I'm not gonna lie."
        wen "Well done, guys. I hope this serves as motivation for you two to keep training hard!"
        wen "Anyway, I gotta go. See you guys in class!"
        i "Thanks, Wen."
        j "Bye!"
        hide wen2 with short
        show ian at lef
        show jeremy at rig
        with move
        i "Come on, let's get going."
    else:
        "We left the gym while going over the matches we had just fought."
        if tournament == "draw":
            i "If I had done just a bit more... I feel like I could've edged out the victory."
            j "He was really tough... Your match was a war!"
            $ fian = "smile"
            i "Yeah... I guess it's not a bad result after all."
        elif tournament == "lose2":
            i "I feel all beat up... That guy was too good for me."
            i "Well, at least I didn't get knocked out."
            j "That guy was really tough, but you hung in there... I'm not sure I would've been able to beat him myself."
        else:
            i "It sucks that I lost... But at least he didn't get the finish."
            j "That guy was really tough, but you managed to hang in there with him. I think the fight was pretty close..."
        if ian_athletics < 10:
            call xp_up ('athletics') from _call_xp_up_996
            if ian_athletics < 8:
                call xp_up ('athletics') from _call_xp_up_997
        wen "You both did well today. I hope this serves as motivation for you two to keep training hard!"
        j "Hell yeah! I can't wait for the next round. I want to fight someone tough too!"
        i "Be careful what you wish for!"
        wen "Anyway, I gotta go. See you guys in class!"
        i "Thanks, Wen."
        j "Bye!"
        hide wen2 with short
        show ian at lef
        show jeremy at rig
        with move
        i "Come on, let's get going."
## street talk
    scene street2 with long
    show ian at lef
    show jeremy at rig
    with short
    if tournament == False:
        j "Thanks for showing up today, man. I appreciate it."
        if ian_jeremy < 3:
            $ fian = "n"
            i "Yeah, well... I had nothing better to do, I guess."
            j "Hey, are you in the mood for some clubbing tonight? I took this weekend off to participate in the tournament."
        else:
            i "I just wanted to check if someone finally knocked some sense into you, but it seems there was no luck this time."
            $ fjeremy = "happy"
            if ian_jeremy < 7:
                j "Such a friend you are!"
                i "Well, you know me..."
            else:
                j "Thank you for worrying so much about me!"
                $ fian = "happy"
                i "Of course. I won't miss the next round either... Maybe I'll get to see something good then!"
            j "Let's party tonight! I took this weekend off to participate in the tournament!"
    else:
        i "I'm gonna be sore tomorrow... That guy hit pretty hard."
        if tournament == "win1" or tournament == "win2" or tournament == "winsub" or tournament == "winko":
            j "We should celebrate our victories! Let's party tonight. I took this weekend off to participate in the tournament!"
        else:
            j "I know things didn't go as planned, but we should celebrate nonetheless... We've been training hard for this, after all!"
            j "Let's party tonight! I took this weekend off to participate in the tournament!"
    # tense
    if ian_jeremy < 3:
        $ fian = "n"
        i "I can't, dude. I'm leaving for the beach after lunch."
        $ fjeremy = "n"
        j "To the beach?"
        i "Yeah, me and the guys are spending a few days at Perry's house."
        j "Oh, that's right... I totally forgot."
        i "Why don't you call and ask him if you can join? If you're not working this weekend..."
    # normal
    elif ian_jeremy < 7:
        $ fian = "smile"
        i "I can't, dude. I'm leaving for the beach after lunch, have you forgotten?"
        $ fjeremy = "n"
        j "Oh, that's true... You're spending a few days with the guys at Perry's."
        i "You could join us since you're not working this weekend..."
    # friends        
    else:
        $ fian = "smile"
        i "I can't, dude. I'm leaving for the beach after lunch, have you forgotten?"
        $ fjeremy = "n"
        j "Oh, that's true... You're spending a few days with the guys at Perry's."
        i "Why don't you come? If you're free this weekend..."
    j "Nah, I'll stay in the city... It's not like I was invited anyway."
    $ fian = "n"
    i "So Perry didn't tell you?"
    j "No, but I'm not surprised. We haven't been hanging out lately, and I think he never really liked me to begin with."
    if ian_perry > 7:
        i "Both of you are quite different... But despite his flaws, you know Perry can be a good friend."
        j "To you, maybe... But it's his damn mood, man. He's such a fusser, always complaining and never doing anything to change it."
        j "Don't you get tired of hanging out with him?"
        i "I live with him... At some point or another, I hope to move to a place of my own, but still..."
        i "We've been friends for a long time, and I appreciate him."
    elif ian_perry > 3:
        i "You two don't have much in common anymore, that much is obvious."
        j "And you? I don't think you two have much in common anymore either. Why do you keep hanging out with him?"
        i "I live with him. But hopefully, I'll be able to move to a place of my own sooner than later."
    else:
        i "I'd say the feeling's mutual..."
        j "I mean, can you blame me? I know you're quite fed up with him yourself..."
        $ fian = "serious"
        i "He's a damn fusser. I'm quite fed up with him too, to be honest."
        j "Yeah, he always brings the mood down. Always complaining and never doing anything to change it..."
        j "Why do you hang out with him that much, then? Screw him and let's go party!"
        $ fian = "n"
        i "Believe me, I'm not going on this trip to spend time with him."
    $ fjeremy = "smile"
    if ian_jeremy < 7:
        j "Well, too bad you're out of town tonight. I guess I'll ask Mark or Mike if they're dropping by Blazer..."
    else:
        j "It's about time you and I went out partying for real! I guess I'll have to ask Mark or Mike if they're dropping by Blazer tonight..."
    i "Dude, you're obsessed. You really want to spend your free time at the same place you work at?"
    $ fjeremy = "happy"
    j "Where else can you find an endless supply of hot chicks ready for the taking? I'm telling you, that club is the place to be!"
    menu: 
        "{image=icon_friend.webp}You know how to enjoy yourself!" if ian_jeremy > 6:
            $ renpy.block_rollback()
            $ fian = "smile"
            i "Sounds like you're living your best life... I'm glad for you, man. Enjoy yourself!"
            $ fjeremy = "flirt"
            j "Hell yeah, that's exactly what I'm doing. Every night there's so many new opportunities, challenges, and fresh pussy to conquer..."
            $ fjeremy = "happy"
            j "This is the moment to go get them!"
            $ fian = "happy"
            i "And you're making good use of your time. You're a man on a mission, ha ha ha!"

        "To each their own":
            $ renpy.block_rollback()
            i "Well, to each their own, or so they say. As long as you don't get bored..."
            $ fjeremy = "flirt"
            j "No way, man. There's still so much left for me to accomplish, so many opportunities, challenges, and fresh pussy to conquer..."
            $ fjeremy = "happy"
            j "This is the moment to go get them!"
            i "You've made that your mission in life..."

        "That's all you think about...":
            $ renpy.block_rollback()
            $ fian = "n"
            i "As I said, that's all you think about. There are other things in life aside from getting girls and partying, you know?"
            $ fjeremy = "flirt"
            j "Maybe, but none are nearly as good! Come on, what does every guy want?"
            $ fjeremy = "happy"
            j "To get some pussy! And if you say otherwise, you're lying."
            i "I'm just saying you could try to find some balance."
            j "Screw that! It's far too early for me to settle. There's still so much left for me to accomplish."
            $ fjeremy = "flirt"
            j "So many opportunities, challenges, and fresh pussy to conquer... This is the moment to go get them!" 
            i "I know, I know... You've made it your mission in life." 

    if ivy_jeremy == 2:
        $ fjeremy = "happy"
        j "And my biggest mission is almost accomplished! Ivy's all but ready to fall!"
        $ fian = "smile"
        i "Ready to, but hasn't fallen yet?"
        $ fjeremy = "smile"
        j "She drove me home from work last weekend and I put all my cards on the table."
        i "And?"
        $ fjeremy = "n"
        j "I only got a kiss... But she said she'll think about it."
        i "About what?"
        $ fjeremy = "happy"
        j "About having sex with me!"
        i "Really? That sounds kinda cold to me... Are you sure she's not just stringing you along?"
        $ fjeremy = "n"
        j "I don't think so... I mean, I'm not sure. She keeps giving me mixed signals, you know?"
    elif ivy_jeremy == 1:
        $ fjeremy = "n"
        j "Though Ivy keeps resisting... She's a tough nut to crack!"
        $ fian = "n"
        i "Have you considered that maybe she just doesn't like you?"
        j "But I think she does. She keeps giving me mixed signals, you know?"
    else:
        $ fjeremy = "n"
        j "It's a harsh mission, you know? I can't seem to find a way to make Ivy open up to me."
        $ fian = "n"
        i "At some point, you just have to accept defeat and cut your losses, man."
        j "That'd be the wise thing to do..."
    $ fjeremy = "flirt"
    j "But if my experiences with women taught me something, it's that when there's a will, there's a way!"
    $ fjeremy = "n"
    j "After all, Mike scored her... Perks of being the DJ, I guess!"
    $ fjeremy = "smile"
    $ fian = "n"
    j "Man, I'm so jealous..."
    i "Wait, didn't that guy mention having a girlfriend?" 
    $ fjeremy = "sad"
    j "Uh, kinda... Maybe they broke up?"
    j "Who knows."
    if louise_jeremy and v7_bbc == "lena" and v11_louise_3some != "ian":
        i "And what about you? I shouldn't even bother asking about Louise..."
        $ fjeremy = "n"
        j "Actually... You were right about that. She got mad at me for going to Ivy's birthday party, even though she said she wouldn't."
        $ fian = "sad"
        if ian_chad > 3:
            i "And you believed her? That's a noob move, dude."
        else:
            i "And you believed her?"
        j "Yeah, well... She would've gotten mad even if I hadn't gone. She can't stand us working together."
        j "I thought I could handle it, but she keeps flying off the handle about it. I'm getting pretty tired about it, honestly..."
        i "Why are you still dating her again?"
        j "It's..."
        j "..."
        j "Nevermind."
        i "Make up your mind, dude..."

    # alison
    if ian_alison_fuck: 
        if ian_holly_dating:
            $ fjeremy = "n"
            j "What about you? I thought you were head over heels for your cute writer girl..."
            $ fian = "n"
            i "Why do you say it like that?"
            if v12_alison_pics_jeremy:
                $ fjeremy = "flirt"
                j "Why, you say? Come on, man, what about that video you sent me yesterday?"
                $ fian = "evil"
                j "I saw you were enjoying a good pair of tits! What's up with that?"
            else:
                $ fjeremy = "flirt"
                "Well, a birdie told me you got a taste of Alison last week!"
        else:
            $ fjeremy = "flirt"
            if v12_alison_pics_jeremy:
                j "So, that video you sent me yesterday... Seems like you're enjoying yourself a good pair of tits, huh?"
                $ fian = "evil"
            else:
                j "By the way, a birdie told me... You got a taste of Alison last week!"  
        if ian_alison_sex:
            j "So you're hooking up again? I thought you got tired of her!"  
        if v12_alison_pics_jeremy:
            if ian_alison_sex:
                i "Her tits were too good to let you have them all to yourself."
            else:
                i "They were too good to let you have them all to yourself."
            j "See? We're not so different, you and I!"
            $ fian = "confident"
            i "It was all her doing... She made a move on me and I went with it... Why not?"
        else:
            $ fian = "worried"
            i "Who told you?"
            j "She did."
            $ fian = "serious"
            i "Can't she keep anything to herself?"
            $ fjeremy = "n"
            j "You know women, they like to talk..."
            $ fian = "n" 
            i "Well, it's true. She made a move on me and I went with it... Why not?"
        $ fjeremy = "happy"
        j "That's my eskimo brother!"
        $ timeout = 3.0
        $ timeout_label = "v12eskimofail"
        menu:
            "High five!":
                $ renpy.block_rollback()
                play sound "sfx/high5.mp3"
                scene street2
                show v12_high5 
                with hpunch
                i "Yeah!"
                if ian_jeremy < 12:
                    call friend_xp ('jeremy') from _call_friend_xp_1130
                scene street2
                show ian at lef
                show jeremy at rig
                with short

            "Leave him hanging":
                $ renpy.block_rollback()
                label v12eskimofail:
                    $ fian = "n"
                    i "..."
                    $ fjeremy = "n"
                    j "Okay, never mind."
                    if ian_jeremy > 6:
                        call friend_xp ('jeremy',-1) from _call_friend_xp_1131
    # alison breakup
    elif ian_alison_breakup:
        $ alison_jeremy = True
        if ian_alison_breakup == "cindy":
            j "By the way, I heard you dumped Alison the other day."
            $ fian = "n"
            i "Did she tell you?"
            j "Yup... She was quite angry with you."
            i "Yeah, well... It had to be done."
            i "Wait, did you meet her?"
            $ fjeremy = "n"
            j "Yeah, she called and asked me to take her out for drinks..."
            i "And?"
        else:
            j "By the way... You and Alison are done and over for good, right?"
            $ fian = "n"
            i "Yeah... Why do you ask?"
            j "She called and asked me to take her out for drinks..."
            i "So?"
        $ fjeremy = "smile"
        j "Well, I fucked her, of course."
        if alison_jeremy_block:
            $ fian = "mad"
            i "Dude, seriously?"
            $ fjeremy = "sad"
            if ian_jeremy > 0:
                call friend_xp ('jeremy',-1) from _call_friend_xp_1132
            j "What? You just said you're done with her!"
            $ fian = "serious"
            i "I also told you I didn't like you hounding Alison..."
            if v10_alison_3some != "n":
                j "Come on, dude, we had a threesome with her! Besides, that was a long time ago."
            else:
                j "That was a long time ago! And you can't call dibs on a girl you tossed aside, that's not how the bro code works."
            if ian_chad > 3:
                i "Well, if you wanna have my leftovers, go ahead..."
                $ fjeremy = "n"
                j "That's cold."
            else:
                i "Whatever..."
                $ fjeremy = "n"
        else:
            $ fian = "sad"
            i "Come on, dude... Show some respect."
            $ fjeremy = "n"
            if v10_alison_3some != "n":
                j "I didn't think you'd have a problem with it... I mean, we had a threesome with her!"
            else:
                j "I didn't think you'd have a problem with it... I mean, you were never serious about her, or so you told me."
            j "Besides, it's you who dumped her. That means you don't want her anymore."
            $ fian = "n"
            i "Whatever... It's a bit more complicated than that."
            i "But you're right. It was me who decided to toss her aside."
    # alison dating
    elif ian_alison_dating:
        if v12_alison_pics_jeremy:
            j "So, that video you sent me yesterday... Seems like you're enjoying yourself a good pair of tits, huh?"
            $ fian = "evil"
            if alison_voyeur:
                i "I wasn't gonna let you be the only one to brag about it."
            else:
                i "First-grade quality."
            j "Damn, I'm jealous..."
            $ fjeremy = "smile"
            j "I see your thing with her is still going on... It's been quite a while since you two started hooking up!"
        else:  
            j "Are you still hooking up with Alison, by the way? It's been quite a while since you two started this thing..."
        if ian_alison_love:
            $ fian = "smile"
            i "Actually... I've been liking where things have been going so far. I like being with Alison..."
            i "I wouldn't mind calling her my girlfriend."
            $ fjeremy = "surprise"
            j "Really? Alison?"
            $ fian = "n"
            i "What's up with that face?"
            $ fjeremy = "smile"
            j "Nothing... I just wasn't expecting it. You've been friends for years, why now?"
            $ fian = "smile"
            i "Things change..."
        else:
            j "Do you plan on keeping her for long?"
            $ fian = "n"
            i "I don't know... The current situation seems to suit both of us thus far."
            if ian_alison_dom:
                i "As long as she doesn't ask more from me... I like to fuck her, but I'm not gonna be her boyfriend."
            else:
                i "She hasn't asked more from me, and I'm not gonna ask more from her..."
            j "What do you think she'll do if you dump her?"
            i "I don't know. Why do you even care?"
            j "Just curious."
    $ timeout_label = None

    # holly
    if ian_holly_dating:
        if ian_alison_fuck:
            $ fjeremy = "smile"
            j "So then... Are you gonna dump Holly and join the single guys club again or...?"
        else:
            $ fjeremy = "smile"
            j "And what about you? You're head over heels for your cute writer girl, aren't you?"
        if ian_holly_love == 2 and v12_jess_date < 4 and ian_cherry_dating == False:
            $ fian = "smile"
            i "I think so... I think Holly and I make a really good match, and we've been getting closer and closer."
            j "I see... She's like your girlfriend already, pretty much."
            $ fian = "n"
            i "I guess she is... Though we haven't had \"the talk\" yet, at least not properly..."
            j "Well, if you're happy with her, it's all good. It'll be a shame to lose a partner in crime, though!"
        else:
            $ fian = "sad"
            i "About that... I still don't know what to make of it. I like Holly a lot, but..."
            $ fjeremy = "flirt"
            j "You still want to enjoy some action, right?"
            if ian_holly_love:
                i "I don't know. Sometimes I feel she's the right one, and sometimes I feel... It might be someone else."
                $ fjeremy = "n"
                j "They say women can smell that shit... They know when you have doubts."
            else:
                i "Could be... Things have been a bit tense between us lately, I can feel it."
            $ fjeremy = "n"
            j "That means \"the talk\" is incoming..."
            i "You're probably right. We've been dancing around it, but I guess it's coming sooner than later."
            i "If I only knew what to tell her..."
            $ fjeremy = "smile"
            j "Well, don't fret too much. Always remember, there's a lot of fish in the sea!"
            if ian_cherry_dating:
                i "Indeed..."
                "I thought about Cherry. Things were looking pretty good with her right now, but..."

    # cindy
    if ian_cindy_sex:
        $ fjeremy = "n"
        j "Speaking of which... I don't even wanna ask, but..."
        $ fian = "n"
        j "Your... {i}thing{/i} with you-know-who... Have you settled that or is it still a thing?"
        if ian_cindy_dating:
            i "It's better if you don't ask."
            $ fjeremy = "sad"
            j "Fuck, Ian... If Wade or Perry end up finding out about it..."
            $ fian = "serious"
            i "That's why you're not gonna ask about it, and I won't talk about it, just like we agreed."
            if ian_cindy_love:
                $ fian = "n"
                i "Look, if at some point I have anything to tell, I will. But now's not the moment."
                if v12_cindy_rel == 0:
                    "I should've told Jeremy it was settled, but I still didn't want to accept Cindy and I were done..."
            $ fjeremy = "n"
            j "Alright, dude..."
        else:
            i "You don't have to worry about that anymore. It's settled."
            j "I hope so..."
  
    # louise 3some
    if v11_louise_3some == "ian":
        $ fian = "sad"
        i "By the way... I feel I need to tell you something."
        $ fjeremy = "smile"
        j "Sure, go ahead."
        $ fian = "n"
        i "It's about Louise."
        $ fjeremy = "n"
        j "Louise? What's wrong with Louise?"
        i "I had a threesome with her and Lena."
        $ fjeremy = "surprise"
        j "You did what!?" with vpunch
        if v10_jeremy_3some:
            $ ian_lena_crisis = True
            $ fjeremy = "sad"
            $ fian = "sad"
            i "Sorry. Lena set things up without telling me, and when I found myself in that situation, I couldn't say no..."
            i "I know you and Louise are supposedly dating, but I thought... Since you're not really serious about her..."
            j "It's alright, man... I don't care about Louise. In fact, she's been giving me a lot of attitude lately."
            j "I'm getting pretty tired about it, honestly..."
            stop music fadeout 2.0
            $ fian = "n"
            i "Why are you still dating her again?"
            j "It's... Well, I need to tell you something, too."
            play music "music/tension.mp3" loop
            j "I've also had a threesome with Louise and Lena."
            $ fian = "worried"
            i "What!?"
            if ian_lena_love:
                $ fian = "disgusted"
                "I was baffled by that information. Jeremy and Lena? Since when?"
                $ fjeremy = "sad"
                $ fian = "mad"
                i "Why haven't I heard about this until now? How many more secrets are you keeping from me, {i}friend{/i}?"
                if ian_jeremy > 0:
                    call friend_xp ('jeremy',-1) from _call_friend_xp_1133
                    if ian_jeremy > 0:
                        $ ian_jeremy -= 1
                    if ian_jeremy > 0:
                        $ ian_jeremy -= 1
                j "It's not like that... I wanted to tell you, but she made me promise I wouldn't."
                i "She did, huh? It's clear where your priorities are. You're such a great bro to have, really."
                i "For how long have you been hooking up with her?"
                j "It's only happened a couple times... It all started one night at Ivy's place."
                j "Things got a bit heated while playing a drinking game, and..."
                i "I can't believe this... You knew I had feelings for her!"
                $ fjeremy = "n"
                j "And I told you not to fall for her... She's not girlfriend material, man."
                j "If you didn't know, word is she likes to get around..."
                jump v12lenagetaround
            else:
                i "You've been with Lena too?"
                $ fjeremy = "smile"
                j "Yeah... It all started one night at Ivy's place. Things got a bit heated while playing a drinking game, and..."
                "I was baffled by that information. Jeremy and Lena? Since when?"
                i "Why haven't I heard about this until now? You both were keeping it a secret from me..."
                $ fjeremy = "n"
                j "She asked me to keep quiet about it... I wanted to tell you, but she made me promise."
                $ fjeremy = "smile"
                j "I should've told you anyway... I feel better now that I did."
                $ fian = "n"
                i "Seems she likes to play games and keep secrets..."
                $ fjeremy = "n"
                j "You're not mad at me, right bro? I mean, I know you've been hooking up with Lena, but that's it, right?"
                i "Yeah... We never had exclusivity or anything like that..."
                "Getting mad at Jeremy would be hypocritical. After all, I had fucked the girl he was dating in a threesome too."
                "Though Lena and Louise were not the same thing... If anything, I was bothered by Lena for keeping that information from me."
                "It seemed like I made the right call when I chose to keep my distance from Lena. She was not girlfriend material..."
                $ fian = "serious"
                i "Still... I'd appreciate it if you had told me earlier."
                $ fjeremy = "sad"
                j "I know... I wanted to, but you know..."
                i "Yeah, she asked you not to, and you didn't want to risk upsetting her and losing the chance of hooking up with her."
                i "Kinda shows where your priorities are... Such a friend you are."
                j "Come on, man... You would've done the same..."
                i "Well, I told you about Louise, right?"
                j "That's right..."
                i "Between this and Gillian... I'm starting to think I can't trust you as much as I believed."
                if ian_jeremy > 0:
                    call friend_xp ('jeremy',-1) from _call_friend_xp_1134
                j "..."
                j "I'm sorry, man."
                $ fian = "n"
                i "Anyway... I'm glad I have this piece of information now."
                if lena_axel_desire or lena_mike_dating or v11_mark_sex or lena_jack > 2 or v11_bbc or v8_jeremy_sex:
                    $ fjeremy = "n"
                    j "If you want some more info about that... Well, the word is Lena likes to get around, if you know what I mean."
                    i "So she has some other hookups aside from us. I'm not surprised..."
                    if lena_axel_desire:
                        j "And I've also heard rumors that she might still be somewhat hung up on her ex..."
                        $ fian = "disgusted"
                        i "Axel... Fuck."
                i "You've given me much to think about..."
                scene street2 with long
                pause 1
                jump v12trainstation

        else:
            $ fjeremy = "happy"
            j "Fucking A, dude! You're the man!"
            $ timeout = 3.0
            $ timeout_label = "v12high5fail"
            menu:
                "High five!":
                    $ renpy.block_rollback()
                    play sound "sfx/high5.mp3"
                    scene street2
                    show v12_high5 
                    with hpunch
                    i "Yeah!"
                    if ian_jeremy < 12:
                        call friend_xp ('jeremy') from _call_friend_xp_1135
                    $ fian = "smile"
                    scene street2
                    show ian at lef
                    show jeremy at rig
                    with short

                "Leave him hanging":
                    $ renpy.block_rollback()
                    label v12high5fail:
                        i "..."
                        $ fjeremy = "n"
                        j "Okay, never mind."

            i "So you're not mad?"
            $ fjeremy = "happy"
            j "Mad? I'm jealous!"
            $ fjeremy = "flirt"
            j "I wish I had the chance to do something like that when I was dating Louise... And with Lena, no less!"
            $ fian = "smile"
            i "Okay, I see I was worrying for nothing."
            $ fjeremy = "smile"
            j "Nah, man... Louise and I are done and over, and have been for a long time. I was never really serious about her anyway."
            if v10_alison_3some != "n":
                $ fjeremy = "flirt"
                j "Besides, it's not the first time we share a chick! Don't forget Alison!"
            $ fjeremy = "happy"
            j "But a threesome with her and Lena...! That's wild."
    
    $ timeout_label = None
    # lena couple
    if ian_lena_couple:
        if lena_cheating:
            $ fjeremy = "n"
            j "So... What's the deal with you and Lena? Is that boat still sailing?"
        else:
            $ fjeremy = "smile"
            j "So what's the deal with you and Lena? Is that boat still sailing?"
        if ian_cuck > 1:
            $ fian = "n"
            i "It is, yeah..."
            $ fjeremy = "n"
            j "Why don't you sound too enthusiastic about it?"
            i "No, I am, I mean..."
            i "Lena's a bit... special. It's the first time I have a relationship like this with someone."
            j "Like what?"
            i "Nothing... Never mind."
            if ian_lena_sex:
                "It felt too embarrassing to tell Jeremy about my weird situation with Lena. What would he think if he knew that Lena started denying me sex?"
            else:
                "It felt too embarrassing to tell Jeremy about my weird situation with Lena. What would he think if he knew I wasn't getting any sex from her?"
            "I was getting some other things, though... And I still didn't know how I felt about them, exactly."
            if ian_cheating:
                "What I knew was that I had some unfulfilled desires that made me search for satisfaction in other women... But it was best to be discreet about it."
            if lena_cheating:
                stop music fadeout 2.0
                $ fjeremy = "n"
                j "Look man, I don't mean to be the party pooper, but... Are you sure about this?"
                $ fian = "sad"
                i "About my relationship with Lena?"
                $ fjeremy = "sad"
                j "Yeah... I know you're head over heels for her and all that, and that you want a girlfriend, but... I don't believe she's the right one for that."
                i "What makes you say that?"
        elif v11_lena_openup:
            $ fian = "smile"
            i "It is."
            if lena_cheating:
                stop music fadeout 2.0
                $ fjeremy = "n"
                j "Look man, I don't mean to be the party pooper, but... Are you sure about this?"
                $ fian = "n"
                i "Again with that? I know you're not a fan of relationships, but I feel differently. I thought I already made that clear."
                $ fjeremy = "sad"
                j "I know you're head over heels for her and all that, and that you want a girlfriend, but... I don't believe she's the right one for that."
                $ fian = "serious"
                i "Oh, yeah? And what makes you say that?"
            else:
                j "Seems like you two are serious about this, after all..."
                i "Yes. I wasn't expecting it to happen, but... She's unlike any other girl I've ever met."
                j "She's a good catch indeed... You're a lucky guy!"
                j "It's a shame I lost a partner in crime, though! I'll be sure to enjoy the bachelor's life for both of us, ha ha!"
                i "I'm sure you will..."
                if ian_cheating:
                    "Even though I was enjoying some pussy on the side, too... But it was best to be discreet about it."
        else:
            $ fian = "n"
            i "It is..."
            $ fjeremy = "n"
            j "Why don't you sound too enthusiastic about it?"
            $ fian = "sad"
            i "I am. I mean..."
            $ fian = "n"
            i "Lena's great. I really like her, and I want to be with her..."
            j "You said {i}like{/i}, but not {i}love{/i}?"
            $ fian = "sad"
            i "No... Yes. I love her. Or so I believe... But lately, I've been feeling a bit... lost."
            j "Lost?"
            $ fian = "n"
            i "I think meeting Gillian stirred up some things inside of me. Things I still need to deal with."
            if ian_cheating:
                "And not only that. My desires had made me cheat on Lena, but it was best to be discreet about it."
            if lena_cheating:
                stop music fadeout 2.0
                j "Look man, I don't mean to be the party pooper, but I don't think Lena's girlfriend material."
                $ fian = "disgusted"
                i "What...? What makes you say that?"
            else:
                $ fjeremy = "smile"
                j "Well, I hope you figure those out... Just don't overthink it too much."
                j "Women come and go. What's important is that you're okay with yourself!"
                $ fian = "smile"
                i "That's some sound advice coming from you, for once... Thanks."
        # lena cheating
        if lena_cheating:
            $ ian_lena_crisis = True
            $ fjeremy = "n"
            j "Word is... she likes to get around."
            play music "music/tension.mp3" loop
            $ fian = "worried"
            i "What...?"
            $ fian = "serious"
            i "Word? Whose word?"
            j "People talk, especially at the club... And well, it seems Lena has given some guys reasons to talk."
            $ fian = "worried"
            i "Which guys?"
            j "That's not the point..."
            $ fian = "mad"
            i "Stop hiding things from me, dude."
            $ fjeremy = "sad"
            if lena_axel_desire:
                if lena_mike_dating or v11_mark_sex or lena_jack > 2 or v11_bbc == "marcel":
                    j "I'm not... Look, I... I've heard she's... still somewhat hung up on her ex, among other things."
                else:
                    j "I'm not... Look, I... I've heard she's... still somewhat hung up on her ex."
                $ fian = "worried"
                i "Axel?"
                j "Look, she's been fooling around at the club. And outside, too, so far as I know."
            else:
                j "I'm not... Look, I've heard she's been... fooling around and at the club. And outside, too."
            $ fjeremy = "n"
            j "But listen: I know how much Gillian fucked you up, that's why I'm telling you this."
            j "Girls like Lena aren't girlfriend material, dude. You know that's how it is, so..."
            j "Stop fooling yourself."
            menu:
                "{image=icon_friend.webp}Thanks for the info" if ian_jeremy > 3:
                    $ renpy.block_rollback()
                    $ fian = "sad"
                    "Jeremy's news felt like a kick in the gut. He had no reason to be lying, and I was inclined to believe him."
                    "Suddenly, that eerie feeling I had been trying to ignore manifested itself in full force. I knew it was true."
                    i "..."
                    i "Thank you for the info... You've given me much to think about."
                    j "I'm your bro... I'm just trying to help."
                    i "Yeah, well... I'll... I think I'll get going."
                    $ fjeremy = "sad"
                    j "Sure. And man... If you need anything, you know where to find me."
                    stop music fadeout 3.0
                    scene street2 with long
                    "My thoughts were like a dense fog in my brain. I was having trouble processing all the implications of what I had just been told."
                    
                "Shut up":
                    $ renpy.block_rollback()
                    $ fian = "serious"
                    i "Shut up, dude. I don't like your tone, or the way you're talking about Lena."
                    j "I'm just trying to make you see things."
                    i "Like you made me see Gillian was back in town? You're full of shit."
                    $ fjeremy = "sad"
                    if ian_jeremy > 0:
                        call friend_xp ('jeremy',-1) from _call_friend_xp_1136
                    j "That's why I don't want to make the same mistake again. I'm telling you..."
                    i "I've heard what you said. I'll think about it."
                    j "Sure, man... I'm just trying to help."
                    stop music fadeout 3.0
                    scene street2 with long
                    "I continued walking alone, finding it difficult to process all the implications of what I had just been told."

                "{image=icon_mad.webp}Don't talk about Lena like that!" if ian_jeremy < 6: 
                    $ renpy.block_rollback()
                    $ v12_jeremy_crisis = 1
                    if ian_chad < 5:
                        $ ian_chad += 1
                    $ fian = "mad"
                    i "Watch out, dude! Don't talk about Lena like that!"
                    $ fjeremy = "sad"
                    j "What...? Dude, I'm just telling you how it is!"
                    i "Like you told me about Gillian being back in town? You're full of shit."
                    if ian_jeremy > 0:
                        call friend_xp ('jeremy',-1) from _call_friend_xp_1137
                        if ian_jeremy > 2:
                            $ ian_jeremy = 2
                    $ fjeremy = "serious"
                    j "I already apologized for that. That's why I'm telling you this shit now."
                    i "Such a good friend you are..."
                    j "Dude, what the hell are you getting mad at me for? The one getting dick behind your back is her!"
                    $ fian = "furious"
                    $ fjeremy = "sad"
                    play sound "sfx/punch.mp3"
                    show jeremy at rig3 with hpunch
                    i "I said watch it!"
                    $ fjeremy = "mad"
                    j "Alright, dude, have it your way. I won't try to interfere with your life."
                    hide jeremy with short
                    stop music fadeout 3.0
                    i "..."
                    $ fian = "serious"
                    i "It's about time I let him have it. Who does he think he is...?"
                    $ fian = "worried"
                    "My anger didn't distract me from the distress I felt at what Jeremy had told me."
                    "As much as I wanted to, I couldn't ignore the implications of those words, if they were true at all."

            if ian_cheating or ian_cindy_over == 2:
                "I felt wronged, but even more so, I felt guilty. I had been cheating on Lena, too."
                "Was I being paid with the same coin...?"
            else:
                "Was the story repeating itself?  Was I being cheated on once more...?"
            scene street2 with long
            pause 0.5
            jump v12trainstation

    # lena casual dating
    elif ian_lena_dating:
        $ fjeremy = "smile"
        j "So Lena will be joining you guys these next few days... You lucky guy!"
        $ fian = "smile"
        i "Yeah. I've been looking forward to having her all to myself for a few days in a row..."
        # casual love
        if ian_lena_love:
            j "You really are head over heels for her..."
            i "How can I not be? She's incredible in many ways... I've never met a girl like her."
            $ fjeremy = "n"
            # sleeping around
            if lena_axel_desire or lena_mike_dating or v11_mark_sex or lena_jack > 2 or v11_bbc or v8_jeremy_sex or v10_jeremy_3some:
                $ ian_lena_crisis = True
                j "Look, man... I don't mean to be the party pooper, but I think you're a bit blinded when it comes to her."
                $ fian = "worried"
                i "What's that supposed to mean?"
                j "Word is... she likes to get around."
                stop music fadeout 2.0
                label v12lenagetaround:
                    $ fian = "serious"
                i "Word? Whose word?"
                j "People talk, especially at the club... And well, it seems Lena has given some guys reasons to talk."
                $ fian = "worried"
                i "Which guys?"
                j "That's not the point..."
                if v11_louise_3some != "ian" or v10_jeremy_3some == False:
                    play music "music/tension.mp3" loop
                if v11_louise_3some == "ian" and v10_jeremy_3some:
                    i "Cut the crap and say what you've got to say."
                else:
                    i "What's this, you're taking her side now? Stop hiding things from me!"
                    if ian_jeremy > 0:
                        call friend_xp ('jeremy',-1) from _call_friend_xp_1138
                $ fjeremy = "sad"
                # axel branch
                if lena_axel_desire:
                    j "Well... I think she's still somewhat hung up on her ex..."
                    $ fian = "worried"
                    i "Axel?"
                    if v11_louise_3some == "ian" and v10_jeremy_3some:
                        j "So I've heard. And well... I've also hooked up with her, as I just told you..."
                        $ fian = "disgusted"
                        i "The threesome with Louise... yeah."
                        jump v12lenagetaround3
                    elif v8_jeremy_sex or v10_jeremy_3some or v11_bbc == "jeremy":
                        j "So I've heard. And, well... If you must know..."
                        jump v12lenagetaround2
                    else:
                        j "So I've heard. But in any case, that's something you should discuss with her, don't you think?"
                        $ fian = "sad"
                        i "..."
                        i "You've given me much to think about."
                        scene street2 with long
                        pause 1
                        jump v12trainstation
                # lena jeremy
                elif v8_jeremy_sex or v10_jeremy_3some or v11_bbc == "jeremy":
                    # already confessed (ian_lena_love)
                    if v11_louise_3some == "ian" and v10_jeremy_3some:
                        j "Well, I already told you about the threesome with Louise and her..."
                        $ fian = "disgusted"
                        label v12lenagetaround3:
                            if v8_jeremy_sex or v11_bbc == "jeremy":
                                j "And not only that... We've also fooled around a couple of times, too..."
                                $ fian = "mad"
                                i "Fuck, this keeps getting better and better..."
                        jump v12jeremylenaconfess
                    # jeremy confess lena 
                    else:
                        j "Well... If you must know..." 
                        label v12lenagetaround2:
                            $ fjeremy = "n"
                            j "I hooked up with Lena too."
                            $ fian = "surprise"
                            i "What!?"
                            $ fian = "disgusted"
                            "I was baffled by that information. Jeremy and Lena? Since when?"
                            $ fjeremy = "sad"
                            $ fian = "mad"
                            i "Why haven't I heard about this until now? How many more secrets are you keeping from me, {i}friend{/i}?"
                            if ian_jeremy > 0:
                                call friend_xp ('jeremy',-1) from _call_friend_xp_1139
                                if ian_jeremy > 0:
                                    $ ian_jeremy -= 1
                                if ian_jeremy > 0:
                                    $ ian_jeremy -= 1  
                            j "It's not like that... I wanted to tell you, but she made me promise I wouldn't."
                            i "She did, huh? It's clear where your priorities are. You're such a great bro to have, really."
                            i "For how long have you been hooking up with her?"
                            j "It's only happened a couple times... It all started one night at Ivy's place."
                            j "Things got a bit heated while playing a drinking game, and..."
                            if ian_lena_love:
                                i "I can't believe this... You knew I have feelings for her!"
                                $ fjeremy = "sad"
                                j "And that's why I told you not to fall for her... She's not girlfriend material, man!"
                            else:
                                i "Dude... what the hell? You know she and I have been seeing each other..."
                                j "But you're keeping it casual, right? Look, I didn't think it was that big of a deal..."
                                $ fian = "serious"
                                i "Did you, or you just kept your mouth shut because you knew you were doing me dirty?"
                                $ fjeremy = "sad"
                                j "See? This is why I didn't want to talk about it..."

                            menu v12jeremylenaconfess:
                                "{image=icon_friend.webp}Thanks for the info" if ian_jeremy > 3:
                                    $ renpy.block_rollback()
                                    $ fian = "sad"
                                    i "..."
                                    i "In any case, thank you for the info. You've given me much to think about..."
                                    if ian_lena_love:
                                        $ fjeremy = "sad"
                                        j "So you're not mad?"
                                        i "Honestly, I don't know how to feel right now. Scratch that, I'm feeling rather stupid."
                                        j "I'm sorry, man... It was time I told you. I didn't feel good keeping this from you..."
                                        i "So you had some real reasons to tell me not to fall for her, huh?"
                                        $ fjeremy = "n"
                                        j "Yeah. She's clearly not looking for a boyfriend right now."
                                        i "And she likes to keep secrets. I wonder what else she's been hiding."
                                    else:
                                        $ fjeremy = "n"
                                        j "Yeah, well... I didn't feel good not telling you. It was about time I did."
                                        i "I wonder if there's more stuff Lena has been keeping secret from me."
                                    j "Don't lose sleep over it, man. It's not like she's your girlfriend or anything like that."
                                    $ fian = "n"
                                    i "No, it's clear she isn't."

                                "This is fucked up":
                                    $ renpy.block_rollback()
                                    $ fian = "disgusted"
                                    i "This is fucked up, man."
                                    if v7_bbc_insist:
                                        j "I tried to resist, I swear. But she didn't care about my excuses."
                                        $ fian = "serious"
                                        i "Yeah, I'm sure you were helpless before her."
                                        j "In a way, yes... t's not like one can turn a girl of Lena's caliber down..."
                                    else:
                                        j "What was I supposed to do? It's not like one can turn a girl of Lena's caliber down..."
                                        $ fian = "serious"
                                        i "Yeah, I'm sure you were helpless before her."
                                    $ fjeremy = "sad"
                                    j "Understand me, bro. You would've done the same in my place..."
                                    i "That includes keeping quiet about it like a fucking coward? You're showing me you can't be trusted."
                                    if ian_jeremy > 0:
                                        call friend_xp ('jeremy',-1) from _call_friend_xp_1140
                                        if ian_jeremy > 6:
                                            $ ian_jeremy = 6
                                    if ian_lena_love:
                                        j "That's why I'm telling you now, even though she asked me not to..."
                                        j "You're right, I should've told you sooner, but this is why I've been warning you not to fall for her."
                                    else:
                                        j "I didn't think it'd be such a big deal! It's not like you're dating her or anything like that..."
                                        j "In fact, I've been warning you not to fall for her..."
                                    i "Yeah, and now I know the reasons behind it. I wonder what other secrets Lena is keeping from me..."
                                    j "Don't lose sleep over it, man. Luckily, she's not your girlfriend."
                                    if ian_lena_love:
                                        $ fian = "sad"
                                        i "No, she's not... And I don't believe she ever will."
                                    else:
                                        $ fian = "n"
                                        i "No, she's not... And she's giving me good reasons to remain single."
                                    
                                "{image=icon_mad.webp}You're a piece of shit" if ian_lena_love or ian_jeremy < 7:
                                    $ renpy.block_rollback()
                                    $ v12_jeremy_crisis = 2
                                    if ian_chad < 5:
                                        $ ian_chad += 1
                                    $ fian = "mad"
                                    i "You're a piece of shit, dude."
                                    $ fjeremy = "n"
                                    call friend_xp ('jeremy',-1) from _call_friend_xp_1141
                                    $ ian_jeremy = 0
                                    j "Come on man, don't be like that..."
                                    show jeremy at centerrig with move
                                    $ fjeremy = "sad"
                                    play sound "sfx/punch.mp3"
                                    show jeremy at rig with hpunch
                                    "Jeremy tried to put a hand on my shoulder, but I swatted it away."
                                    i "Fuck off!"
                                    j "I didn't think it'd be such a big deal! It's not like you're dating her or anything like that..."
                                    j "In fact, I've been warning you not to fall for her!"
                                    i "All while keeping quiet about what was truly going on, like a fucking scoundrel."
                                    if ian_lena_love:
                                        i "All while I was telling you how I felt about her..."
                                        i "I see Lena and you have been having a laugh at me."
                                    else:
                                        i "I see Lena and you have been having a laugh at me..."
                                    if ian_alison_dating or ian_cindy_sex or ian_minerva_sex or ian_alison_breakup or ian_emma_sex:
                                        $ fjeremy = "serious"
                                        j "Relax, dude. As I said, it's not like she was your girlfriend. You've been sleeping around too..."
                                        i "Is that how you're justifying being a shitty friend?"
                                        j "All I'm saying is Lena's not your property just because you've got a major crush on her."
                                        $ fian = "furious"
                                    else:
                                        $ fjeremy = "n"
                                        j "Relax, dude. It's not like that..."
                                    i "Fuck you!"
                                    show jeremy at rig3 with move
                                    j "Alright... I'm gonna go. You're acting like a dick-head right now."
                                    i "Yeah, get the hell outta here before I smack you."
                                    $ fjeremy = "serious"
                                    j "Sure, dude. Sure."
                                    hide jeremy with short
                                    i "..."
                                    $ fian = "disgusted"
                                    i "Fuck..."
                                    "My anger didn't distract me from the distress I felt at what Jeremy had told me."
                                    i "What now...?"
                                    hide ian with short
                        
                            scene street2 with long
                            pause 1
                            jump v12trainstation
                # worried
                else:
                    j "I mean, if anything, that's something you should discuss with her, don't you think?"
                    $ fian = "sad"
                    i "Probably... You've given me much to think about."
                    scene street2 with long
                    pause 1
                    jump v12trainstation                    
            # faithful
            else:
                j "And does she feel the same for you?"
                if lena_ian_love:
                    i "I think she does feel something special for me too... But I know that doesn't change our situation."
                    i "At least not at the moment."
                    j "Alright... But be careful. I don't want to see you crashing down because of a chick again."
                else:
                    j "Be careful... I don't want to see you crashing down because of a chick again."
                i "Don't worry... I'll be fine."
        # casual
        else:
            $ fian = "confident"
            i "I just hope the guys don't bother us too much!"
            $ fjeremy = "flirt"
            j "I bet you won't be leaving the bedroom at all. She's such a bombshell..." 
            # sleeping around   
            if lena_axel_desire or lena_mike_dating or v11_mark_sex or lena_jack > 2 or v11_bbc or v8_jeremy_sex or v10_jeremy_3some:
                $ ian_lena_crisis = True
                j "I know some guys who'll be missing her these days!"
                stop music fadeout 2.0
                $ fian = "worried"
                i "Huh? What's that supposed to mean?"
                $ fjeremy = "n"
                j "Well... Word is she likes to get around."
                play music "music/tension.mp3" loop
                $ fian = "serious"
                i "Get around? Seems like you know more than you're telling me..."
                i "Should I be worried you're keeping things I should know from me again?"
                $ fjeremy = "sad"
                j "What? No, not really... Just some rumors I've heard at the club..."
                $ fian = "worried"
                i "What kind of rumors?"
                if lena_axel_desire:
                    j "Well... I think she's still somewhat hung up on her ex..."
                    $ fian = "worried"
                    i "Axel?"
                    if v8_jeremy_sex or v10_jeremy_3some or v11_bbc == "jeremy": 
                        j "And well... If you must know..." 
                # jeremy confess
                if v8_jeremy_sex or v10_jeremy_3some or v11_bbc == "jeremy":  
                    if lena_axel_desire == False:
                        j "Well... If you must know..."
                    jump v12lenagetaround2
                else:
                    if lena_axel_desire:
                        j "I don't know, man. That's what I've heard, that she's been fooling around the club."
                    else:
                        j "I don't know, man. What I said: that she likes to fool around with guys."
                    $ fjeremy = "n"
                    j "I'm just giving you a heads-up, but if you're that worried about it you should bring it up with her."
                    i "You're probably right... You've given me much to think about."
                    scene street2 with long
                    pause 1
                    jump v12trainstation
            else:
                i "Yeah, and she's all mine."
    
    i "Well, it's time for me to get going. I have a train to catch."
    $ fjeremy = "smile"
    j "Have fun!"
    scene street2 with long
    pause 0.5
    jump v12trainstation

## TRAIN STATION ########################################################################################################################################################################
label v12trainstation:

    $ timeout_label = None
    stop music fadeout 2.0
    "I stopped by my place to pick up the suitcase and then headed to the train station to meet up with my friends."
    if lena_cheating:
        $ fian = "worried"
    elif ian_lena_crisis:
        $ fian = "sad"
    else:
        $ fian = "smile"
    if ian_emma_sex or emma_bikini:
        $ emma_hair = "pink"
    else:
        $ emma_hair = 2
    if ian_emma_sex:
        $ emma_bikini = True
    $ femma = "n"
    $ emma_look = "summer"
    $ fwade = "n"
    $ wade_look = "summer"
    $ perry_look = "summer"
    play music "music/normal_day4.mp3" loop
    scene street with long
    show ian with short
    show ian at lef3 with move
    show emma 
    show wade at rig3
    show dog at rig
    with short
    e "There you are!"
    if lena_cheating or ian_lena_crisis:
        $ femma = "sad"
        e "Are you okay?"
        i "What?"
        e "You look worried or something."
        $ fian = "n"
        i "No, it's alright..."
    else:
        i "Hey, guys..."
    play sound "sfx/bark.mp3"
    i "What's up with the dog?"
    e "Her name is Jenthe! I'm taking care of her for a friend, and I thought she'd love a trip to the beach!"
    # emma look
    if emma_hair == "pink":
        $ fian = "smile"
        i "Wow, by the way. Nice hair!"
        $ femma = "smile"
        e "Thanks! I've been wanting to change my style a bit lately..."
    menu:
        "Your dress...":
            $ renpy.block_rollback()
            if emma_hair == "pink":
                i "That dress, though..."
            else:
                i "I see. By the way, your dress..."
            $ femma = "sad"
            e "What about it?"
            menu:
                "It looks good on you":
                    $ renpy.block_rollback()
                    $ v12_emma_dress = 2
                    $ fian = "smile"
                    i "It looks good on you! So cute."
                    $ femma = "smile"
                    "Emma grabbed the hem of her skirt and twirled around, flashing a smile."
                    e "Thanks! I felt like going with the summer vibe!"
                    if ian_emma < 12:
                        call friend_xp ('emma') from _call_friend_xp_1142
                    w "I think it's the first time I've seen you wearing a dress..."
                    $ femma = "n"
                    e "Yeah, I wasn't sure about it myself at first, but I decided to give it a go..."
                    e "You have to experiment and keep changing through time. Otherwise, you get stuck and become boring, you know?"
                    if ian_wits < 8:
                        call xp_up ('wits') from _call_xp_up_998

                "You look ridiculous":
                    $ renpy.block_rollback()
                    $ v12_emma_dress = 1
                    $ fian = "happy"
                    i "What's up with it? You look ridiculous!"
                    $ fwade = "happy"
                    $ femma = "surprise"
                    i "It's like a giant water lily grew legs! Ha ha ha!"
                    $ femma = "sad"
                    w "That's cold, man!"
                    i "When have you seen Emma wearing a dress? This is a first!"
                    $ femma = "mad"
                    e "Alright, that's enough. You don't need to be a jerk about it."
                    if ian_emma > 0:
                        call friend_xp ('emma',-1) from _call_friend_xp_1143
                    $ fian = "smile"
                    i "Sorry, it's just... This is the last thing I was expecting to see today."
                    e "I wanted to go with the summer vibe and try something more feminine, so what?"
                    $ femma = "sad"
                    e "I like experimenting and changing all the time. Otherwise, you get stuck and become boring, you know?"
                    i "I know, I know."
                    if ian_charisma < 8:
                        call xp_up ('charisma') from _call_xp_up_999

                "Nothing":
                    $ renpy.block_rollback()
                    i "No, nothing."

        "What's up, Wade?":
            $ renpy.block_rollback()
            i "What's up, Wade? It's been a while since you joined us for something like this."
            if v10_encourage_wade and v6_confess_wade == False:
                w "I was tempted to stay since I needed to practice for the next big tournament..."
                $ fwade = "happy"
                w "But I'm in the mood for a small holiday at the beach."
                i "That's good."
                if ian_charisma < 8:
                    call xp_up ('charisma') from _call_xp_up_1000
            else:
                w "Yeah, well, without having found a job yet, and with Cindy away, I don't have anything better to do, so..."
                $ fian = "n"
                i "Sure..."

        "Ready?":
            $ renpy.block_rollback()

    if ian_lena_crisis:
        $ fian = "n"
    else: 
        $ fian = "smile"   
    i "Are you guys ready?"
    $ femma = "n"
    e "Yeah!"
    $ fwade = "smile"
    w "Let's get on the train. The beach and a barbecue are waiting for us."
    i "Wait, Holly's still not here..."
    
    # holly
    if v11_holly_change:
        $ holly_glasses = 2
    else:
        $ holly_glasses = True

    if holly_guy > 1 or holly_robert:
        play sound "sfx/car2.mp3"
        "A car pulled up in front of us and a girl came out of the passenger seat."
        "She said goodbye to the driver before turning around, and then I recognized her."
        $ fholly = "happyshy"
        $ fian = "worried"
        show ian at left 
        show wade at right
        show emma at rig
        show dog at rig3
        with move
        show holly at lef with short
        h "Oh, hi, guys..."
        e "Everyone's here. Let's go!"
        $ fian = "n"
    else:
        $ fholly = "smile"
        show ian at left 
        show wade at right
        show emma at rig
        show dog at rig3
        with move
        show holly at lef with short
        h "Hi, guys... I'm not late, am I?"
        $ fwade = "happy"
        if ian_holly_dating:
            $ fian = "smile"
            i "No, you're just in time."
            "I placed my hand on Holly's hip and greeted her with a kiss. I had been looking forward to spending this vacation with her."
            w "Everyone's here, so let's go."
        else:
            w "You're just in time. Everyone's here, so let's go."
    $ femma = "smile"
    e "To the beach!"
    stop music fadeout 2.0
    scene street with long
    pause 1
    play sound "sfx/train.mp3"
    scene train_travel with Dissolve (1)
    pause 2.5
    # scene beachsky at beachskybg1 with Dissolve (1.5)
    # pause 1
    # scene beachsky at beachskybg2
    # play sound "sfx/sea.mp3"
    # pause 5
    # $ fperry = "happy"
    # show perry with long
    # p "This is gonna be a{w=0.5}--awesome...{w=1.0}{nw}"
    # hide perry with long
    # pause 0.5
    # scene black with long

    jump chapter_twelve2


## SUMMER CLOTHING SHOP ########################################################################################################################################################################

screen v12ian_clothingshop():
    if ian_wits > 6 or ian_wits >= ian_charisma:
        imagebutton idle "v12_ianclothes_wits.webp" hover "v12_ianclothes_wits_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v12ian_clothing_wits')
    elif ian_charisma > ian_wits:
        imagebutton idle "v12_ianclothes_wits_block.webp"
    
    if ian_charisma > 6 or ian_charisma > ian_wits:
        imagebutton idle "v12_ianclothes_charisma.webp" hover "v12_ianclothes_charisma_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v12ian_clothing_charisma')
    elif ian_wits >= ian_charisma:
        imagebutton idle "v12_ianclothes_charisma_block.webp"
    
    if ian_athletics > 4:
        imagebutton idle "v12_ianclothes_athletics.webp" hover "v12_ianclothes_athletics_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v12ian_clothing_athletics')
    else:
        imagebutton idle "v12_ianclothes_athletics_block.webp"
    
    if ian_lust > 6:
        imagebutton idle "v12_ianclothes_lust.webp" hover "v12_ianclothes_lust_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v12ian_clothing_lust')
    else:
        imagebutton idle "v12_ianclothes_lust_block.webp"
    
    
    add "sexshop_money.webp" pos (1799, 29)
    text "{font=[font_big_noodle_oblique]}{color=#000000}[ian_money]{/color}":
        size 30
        xpos 1815 ypos 56

screen v12ian_clothingshop_gal():
    imagebutton idle "v12_ianclothes_wits.webp" hover "v12_ianclothes_wits_hover.webp" focus_mask True action SetVariable("ian_summer_look", "wits") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    imagebutton idle "v12_ianclothes_charisma.webp" hover "v12_ianclothes_charisma_hover.webp" focus_mask True action SetVariable("ian_summer_look", "charisma") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    imagebutton idle "v12_ianclothes_athletics.webp" hover "v12_ianclothes_athletics_hover.webp" focus_mask True action SetVariable("ian_summer_look", "athletics") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    imagebutton idle "v12_ianclothes_lust.webp" hover "v12_ianclothes_lust_hover.webp" focus_mask True action SetVariable("ian_summer_look", "lust") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    
    add "sexshop_money.webp" pos (1799, 29)
    text "{font=[font_big_noodle_oblique]}{color=#000000}[ian_money]{/color}":
        size 30
        xpos 1815 ypos 56
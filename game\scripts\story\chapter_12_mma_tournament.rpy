#####################################################################################################################################################
## MMA FIGHT ##########################################################################################################################################
label v12mmafight:

    $ ian_look = "mma"   

    screen mma_clock():
        if v12_round == 1 or v12_round == 3 or v12_round == 5 or v12_round == 7 or v12_round == 9 or v12_round == 11:
            imagebutton idle "v12_mma_clock_b0.webp"
        if v12_round == 2 or v12_round == 4 or v12_round == 6 or v12_round == 8 or v12_round == 10 or v12_round == 12:
            imagebutton idle "v12_mma_clock_b3.webp"

        if v12_round == 1 or v12_round == 7:
            imagebutton idle "v12_mma_clock_3.webp"
        if v12_round == 2 or v12_round == 3 or v12_round == 8 or v12_round == 9:
            imagebutton idle "v12_mma_clock_2.webp"
        if v12_round == 4 or v12_round == 5 or v12_round == 10 or v12_round == 11:
            imagebutton idle "v12_mma_clock_1.webp"
        if v12_round == 6 or v12_round == 12:
            imagebutton idle "v12_mma_clock_0.webp"

    screen mma_values():
        vbox xpos 900 ypos 60:
            text "Round [v12_round]":
                size 20
        vbox xpos 60 ypos 650:
            text "Position [v12_position]":
                size 20
        vbox xpos 60 ypos 700:
            text "Attack count [v12_attack_count]":
                size 20
        vbox xpos 60 ypos 750:
            text "Guard guard [v12_guard_count]":
                size 20
        vbox xpos 60 ypos 800:
            text "Grappling count [v12_grappling_count]":
                size 20
        vbox xpos 60 ypos 850:
            text "Provoke count [v12_provoke_count]":
                size 20
        vbox xpos 60 ypos 900:
            text "Takedown count [v12_takedown_count]":
                size 20
        vbox xpos 60 ypos 950:
            text "Submission count [v12_sub_count]":
                size 20

        vbox xpos 60 ypos 10:
            text "{color=#0B0D7C}Ian score [v12_ian_score]{/color}":
                size 20
        vbox xpos 60 ypos 40:
            text "{color=#0B0D7C}Ian damage [v12_ian_hurt]{/color}":
                size 20
        vbox xpos 60 ypos 70:
            text "{color=#0B0D7C}Ian tired [v12_ian_tired]{/color}":
                size 20

        vbox xpos 1700 ypos 10:
            text "{color=#f00}Rival score [v12_rival_score]{/color}":
                size 20
        vbox xpos 1700 ypos 40:
            text "{color=#f00}Rival damage [v12_rival_hurt]{/color}":
                size 20
        vbox xpos 1700 ypos 70:
            text "{color=#f00}Rival tired [v12_rival_tired]{/color}":
                size 20

    $ tournament = True                     # status ---> "winsub" "winko" "win2" "win1" "draw" "lose1" "lose2" "loseko" "losesub" "tapout"

    default v12_round = 0                   # tracker for active round (12)
    default v12_position = "standing"       # tracks the fight's current position in this round "standing" "clinch" "groundtop" "groundbottom"
    default v12_attack_count = 0            # marker of Ian's attack attempts
    default v12_guard_count = 0             # marker of Ian letting the rival take the initiative
    default v12_grappling_count = 0         # marker of times Ian tried to shoot on the rival
    default v12_takedown_count = 0          # marker of times times Ian landed a takedown
    default v12_sub_count = 0               # marker of times Ian tried submitting his rival
    default v12_provoke_count = 0           # marker of times Ian's tried provoking the rival
    default v12_tapout_count = 0            # marker of times Ian's tried to tap out
    default v12_ian_dropped = False         # marker for Ian getting ko'ed but reviving

    default v12_ian_hurt = 0                  # tracks Ian's HP before K0
    default v12_rival_hurt = 0                # tracks Rival's HP before K0
    default v12_ian_tired = 0                 # tracks Ian's STAMINA -resistance and effectiveness
    default v12_rival_tired = 0               # tracks Ian's STAMINA -resistance and effectiveness

    default v12_ian_score = 0               # hits landed by Ian (jab,low kick-takedown += 1/ punch-kick-slam += 2)
    default v12_rival_score = 0             # hits landed by rival (jab,low kick += 1/ punch-kick += 2)
        
    default v12_rival_takedown = 0            # times rival has taken down Ian
    default v12_rival_sub = 0                 # times rival has tried to submit Ian
    default v12_rival_vengeance = 0   # counter for rival attack if losing score
    default v12_mma_wen = 0                 # counter for Wen callouts
    default v12_mma_status_tired = 0              # counter for Ian's tired status commentary
    default v12_mma_status_hurt = 0              # counter for Ian's damahe status commentary
    
    
    # $ jiujitsu = 4      # test values findme
    # $ kickboxing = 5    # test values
    # $ ian_athletics = 7  # test values
    # $ ian_wits = 7
    # $ ian_will = 1

#####################################################################################################################################################
## ROUND 1 ########################################################################################################################################## 
    $ renpy.block_rollback()
    if ian_wits < 7:
        $ timeout = 3.5
    elif ian_wits > 6:
        $ timeout = 3.0
    else:
        $ timeout = 2.5
    $ timeout_label = "v12_hit_hub"
    $ v12_round = 1

    # show screen mma_values()

    show screen mma_clock()
    scene v12_mma_base_bg at mmabg
    show v12_mma_base_rival at mmarival
    show v12_mma_base_ian at mmaian 
    with long
    "I stood in front of my rival and put up my guard."
    scene v12_mma_base_bg at mmabganim
    show v12_mma_base_rival at mmarivalanim 
    show v12_mma_base_ian at mmaiananim 
    with fps
    "I felt my heart and the adrenaline pumping, completely focused on what was about to go down."
    show phase_initiative with short
    $ renpy.block_rollback()
    menu v12_action_menu:
        "{image=icon_attack.webp}Attack!":
            $ renpy.block_rollback()
            "I came out aggressive, trying to catch him before he had a chance to get into his rhythm."
            label v12mmaattack1:
                hide phase_initiative
                show phase_attack
                with short
                $ config.menu_include_disabled = False
                $ greyed_out_disabled = True
            menu:
                "{image=icon_wits.webp}Test the waters" if v12_rival_score == 0 and v12_ian_score == 0 and ian_wits > 6:
                    $ renpy.block_rollback()
                    if persistent.include_disabled:
                        $ config.menu_include_disabled = True
                    $ greyed_out_disabled = False
                    hide phase_attack with fps3
                    "I wanted to test the waters. I threw a jab at him to see how he would react."
                    if kickboxing > 4:
                        play sound "sfx/punch.mp3"
                        scene v12_mma_jab_bg at mmarightbg
                        show v12_mma_jab_hit at mmaright
                        with hpunch
                        "I got a solid connection right away."
                        play sound "sfx/miss.mp3"
                        scene v12_mma_jab_bg at mmaleftbg
                        show v12_mma_jab_dodge at mmaleftdodge
                        show v12_mma_jab_dodge_rival at mmaleft
                        with fps
                        pause 0.5
                        play sound "sfx/miss.mp3"
                        scene v12_mma_right_bg at mmaleftbg
                        show v12_mma_right_dodge_rival at mmaleft
                        show v12_mma_right_dodge at mmaleftdodge
                        with fps
                        if kickboxing > 5:
                            play sound "sfx/punch.mp3"
                            scene v12_mma_lowkick_bg at mmarightbg
                            show v12_mma_lowkick_hit at mmalk
                            with vpunch
                            $ v12_ian_score += 1
                            "He tried to retaliate with a one-two, but I dodged it and landed a sneaky low kick." 
                        else:   
                            "He tried to retaliate with a one-two, but I dodged it with ease."
                        "I could see he didn't like that."
                        $ v12_ian_score += 1
                    else:
                        play sound "sfx/miss.mp3"
                        scene v12_mma_jab_bg at mmarightbg
                        show v12_mma_jab_miss at mmaright
                        with fps3
                        "He dodged it without trouble. He moved well..."
                        if kickboxing > 3:
                            play sound "sfx/punch.mp3"
                            scene v12_mma_lowkick_bg at mmarightbg
                            show v12_mma_lowkick_hit at mmalk
                            with vpunch
                            "However, I caught him with a quick follow-up low kick."
                            $ v12_ian_score += 1
                            play sound "sfx/miss.mp3"
                            scene v12_mma_jab_bg at mmaleftbg
                            show v12_mma_jab_dodge at mmaleftdodge
                            show v12_mma_jab_dodge_rival at mmaleft
                            with fps
                            pause 0.5
                            play sound "sfx/punch.mp3"
                            scene v12_mma_right_bg at mmaleftbg
                            show v12_mma_right_block at mmaleftpower
                            with hpunch
                            "He tried to retaliate with a one-two, but I was able to avoid it. He was dangerous..."
                        else:
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_jab_bg at mmaleftbg
                            show v12_mma_jab_damage at mmaleft
                            with hpunch
                            pause 0.5
                            play sound "sfx/punch.mp3"
                            scene v12_mma_right_bg at mmaleftbg
                            show v12_mma_right_block at mmaleftpower
                            with vpunch
                            "Before I could reset he retaliated with a one-two, scoring a hit too. He was dangerous..."
                            $ v12_rival_score += 1
                        
# STRIKING action
                "Strike":
                    $ renpy.block_rollback()
                    label v12_mma_strike_hub:
                        if persistent.include_disabled:
                            $ config.menu_include_disabled = True
                        $ greyed_out_disabled = False
                        if v12_round == 1:
                            "I went hard right away."
                       
                        if v12_attack_count == 0 and v12_round < 3:
                            jump v12_strike_0
                        elif v12_attack_count == 1 or (v12_attack_count == 0 and v12_round > 2):
                            jump v12_strike_01
                        elif v12_attack_count > 1:
                            jump v12_strike_hub
                        
                        # 1st strike (not random)
                        label v12_strike_0:
                            $ v12_attack_count += 1
                            if kickboxing == 6:
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_jab_bg at mmarightbg
                                show v12_mma_jab_hit at mmaright
                                with hpunch
                                pause 0.6
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_right_hit_bg at mmaleftbg
                                show v12_mma_right_hit at mmarightpower
                                with vpunch
                                "My boldness paid off and I connected with a solid one-two right away."
                                "My opponent gritted his teeth and glared back at me, tightening his guard."
                                $ v12_ian_score += 2
                            elif kickboxing > 3:
                                play sound "sfx/punch.mp3"
                                scene v12_mma_jab_bg at mmarightbg
                                show v12_mma_jab_hit at mmaright
                                with hpunch
                                pause 0.6
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_right_bg at mmaleftbg
                                show v12_mma_right_miss at mmaleftpower
                                with hpunch
                                "I connected with a swift jab, but he parried the right straight skillfully."
                                if v12_round == 1:
                                    play sound "sfx/punch.mp3"
                                    scene v12_mma_jab_bg at mmaleftbg
                                    show v12_mma_jab_block at mmaleft
                                    with vpunch
                                    "He replied with a jab of his own, but I managed to block it."
                                $ v12_ian_score += 1
                            else:
                                play sound "sfx/miss.mp3"
                                scene v12_mma_jab_bg at mmarightbg
                                show v12_mma_jab_miss at mmaright
                                with fps
                                pause 0.5
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_right_bg at mmaleftbg
                                show v12_mma_right_miss at mmaleftpower
                                with hpunch
                                "He dodged my punches trouble. He moved well..."
                            if kickboxing < 3 or (v12_round > 1 and kickboxing < 6):
                                play sound "sfx/punch.mp3"
                                scene v12_mma_jab_bg at mmaleftbg
                                show v12_mma_jab_damage at mmaleft
                                with hpunch
                                "Before I could reset, he retaliated with a jab of his own, scoring a hit too."
                                $ v12_rival_score += 1
                                "I had to be on my guard..."
                            jump v12_hub_exit

                        # 2nd strike  - (not random)
                        label v12_strike_01:
                            $ v12_attack_count = 2
                            if kickboxing > 3:
                                "My last attack had worked nicely, so I went on an all-out assault this time as well."
                            else:
                                "My last attack hadn't been successful. I decided to try harder this time."
                            if kickboxing > 2:
                                if kickboxing > 4:
                                    play sound "sfx/punchgym.mp3"
                                    scene v12_mma_jab_bg at mmarightbg
                                    show v12_mma_jab_hit at mmaright
                                    with hpunch
                                    $ v12_ian_score += 1
                                else:
                                    play sound "sfx/punchgym.mp3"
                                    scene v12_mma_jab_bg at mmarightbg
                                    show v12_mma_jab_miss at mmaright
                                    with fps3
                                pause 0.6
                                play sound "sfx/punch.mp3"
                                scene v12_mma_lowkick_bg at mmarightbg
                                show v12_mma_lowkick_hit at mmalk
                                with hpunch
                                pause 0.6
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_body_bg at mmaleftbg
                                show v12_mma_counter_damage at mmaleftcounter
                                with hpunch
                                i "...!{w=1.0}{nw}"
                                play sound "sfx/slap2.mp3"
                                scene v12_mma_headkick_bg at mmaleftbg
                                show v12_mma_headkick_block at mmaup
                                with vpunch
                                "He caught me at the end of my combination with a counterpunch, and almost hit me with a vicious head kick."
                                "I would need to be very careful when engaging him!"
                                $ v12_rival_score += 1
                                $ v12_ian_score += 1
                                if kickboxing < 4:
                                    $ v12_ian_tired += 1
                            else:
                                play sound "sfx/miss.mp3"
                                scene v12_mma_jab_bg at mmarightbg
                                show v12_mma_jab_miss at mmaright
                                with fps3
                                pause 0.6
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_right_bg at mmaleftbg
                                show v12_mma_right_miss at mmaleftpower
                                with hpunch
                                pause 0.6
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_body_bg at mmaleftbg
                                show v12_mma_counter_damage at mmaleftcounter
                                with hpunch
                                play sound "sfx/big_punch.mp3"
                                i "...!{w=0.8}{nw}"
                                scene v12_mma_headkick_bg at mmaleftbg
                                show v12_mma_headkick_damage at mmaup
                                with vpunch
                                $ v12_rival_score += 2
                                $ v12_ian_hurt += 1
                                "My flurry was countered by two hits that left me paralyzed for a moment, pain radiating through my head."
                                "All I could do was jump back and cover up, trying to get my bearings back."
                                i "What the fuck was that...!?"
                            jump v12_hub_exit

                        # 3rd strike 
                        label v12_strike_hub:
                            $ v12_attack_count += 1

                            if v12_attack_count == 3 and ian_athletics < 6:
                                $ v12_ian_tired += 1
                            elif v12_attack_count == 4 and ian_athletics > 5:
                                $ v12_ian_tired += 1
                            if v12_attack_count == 6 and ian_athletics < 7:
                                $ v12_ian_tired += 1
                            elif v12_attack_count == 7 and ian_athletics > 6:
                                $ v12_ian_tired += 1
                            if v12_attack_count == 9 and ian_athletics < 7:
                                $ v12_ian_tired += 1
                            elif v12_attack_count == 10 and ian_athletics > 6:
                                $ v12_ian_tired += 1
                            if v12_attack_count == 5:
                                $ v12_rival_tired += 1
                            if v12_attack_count == 8:
                                $ v12_rival_tired += 1

                            call mma_randomizer(mma_randomizer_strike) from _call_mma_randomizer
                            jump v12_hub_exit                                 
                        
    
# GRAPPLING action
                "Grapple":
                    $ renpy.block_rollback()
                    label v12mmagrappling1:
                        $ v12_grappling_count += 1
                        if persistent.include_disabled:
                            $ config.menu_include_disabled = True
                        $ greyed_out_disabled = False
                        if v12_grappling_count == 1:
                            "I could see he had a reach advantage over me. If dealing with Jeremy had taught me anything, it was to close the distance to negate it."                   
                    menu:
                        "{image=icon_wits.webp}Feint and shoot" if kickboxing > 3 and v12_grappling_count == 1 or kickboxing > 4:
                            $ renpy.block_rollback()
                            play sound "sfx/miss.mp3"
                            scene v12_mma_jab_bg at mmarightbg
                            show v12_mma_jab_miss at mmaright
                            with fps3
                            pause 0.6
                            if v12_takedown_count == 0:
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with hpunch
                                "I made him raise up his arms with a feint, which cleared the way for me to shoot in deep and get a strong body lock on him."
                                $ v12_position = "clinch"
                            else:
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with hpunch
                                pause 0.8
                                play sound "sfx/slap.mp3"
                                scene v12_mma_takedown_miss with vpunch
                                "He sprawled, defending my takedown, but I used my momentum to keep pushing and drive him to the ground."
                                play sound "sfx/fall.mp3"
                                scene v12_mma_ground_bottom with hpunch
                                "However, he used his leverage to twist and turn, ending up with top position."
                                if jiujitsu > 3 and v12_sub_count == 0:
                                    play sound "sfx/slap.mp3"
                                    scene v12_mma_sub1 with hpunch
                                    "I was quick to get a headlock, preventing him to pass guard. He tried to escape, but I had his neck trapped in tight."
                                    $ v12_position = "guillotine"
                                    jump v12mmaguillotine1
                                else:
                                    $ v12_position = "groundbottom"
                                    jump v12mmabottom1
                            if v12_round > 1:
                                jump v12mmaclinchattack1
                        
                        "{image=icon_attack.webp}Attack and shoot":
                            $ renpy.block_rollback()
                            if kickboxing > 4 and v12_grappling_count < 3 or kickboxing > 3 and v12_grappling_count < 2:
                                play sound "sfx/punch.mp3"
                                scene v12_mma_jab_bg at mmarightbg
                                show v12_mma_jab_hit at mmaright
                                with hpunch
                                pause 0.6
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with fps
                                "I connected with a swift jab, which startled him and allowed me to shoot in deep and get a strong body lock on him."
                                $ v12_position = "clinch"
                            else:
                                play sound "sfx/miss.mp3"
                                scene v12_mma_jab_bg at mmarightbg
                                show v12_mma_jab_miss at mmaright
                                with fps3
                                pause 0.6
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with hpunch
                                "I tried hitting him with a jab, but he saw it coming and parried it. However, I closed the distance and tried to get a body lock on him."
                                if v12_grappling_count < 3:
                                    if jiujitsu > 3 and ian_athletics > 5:
                                        "He tried to resist, but I was able to secure my hold on him."
                                        $ v12_position = "clinch"
                                        if jiujitsu < 5:
                                            $ v12_ian_tired += 1
                                    else:
                                        play sound "sfx/punchgym.mp3"
                                        scene v12_mma_base_bg at mmabg
                                        show v12_mma_base_rival at mmarival
                                        show v12_mma_base_ian at mmaian 
                                        with hpunch
                                        "He was prepared and shook me off easily."
                                        $ v12_position = "standing"
                                else:
                                    scene v12_mma_takedown_miss with short
                                    "But he was aware of my intentions and countered me, holding my head and trying to wrestle me down."
                                    if v12_ian_tired < 2:
                                        $ v12_ian_tired += 1
                                        "I fought his hands and after a painful struggle, I was able to get free."
                                        $ v12_position = "standing"
                                    else:
                                        play sound "sfx/fall.mp3"
                                        scene v12_mma_ground_bottom with hpunch
                                        "I wasn't able to prevent him from dragging me to the mat."
                                        $ v12_position = "groundbottom"
                            if v12_round > 1 and v12_position == "clinch":
                                jump v12mmaclinchattack1

                        "{image=icon_athletics.webp}Shoot directly":
                            $ renpy.block_rollback()
                            if jiujitsu < 4 and v12_round > 3 or jiujitsu < 5 and v12_round > 4:
                                $ v12_ian_tired += 1
                            "I decided to go straight for it, jumping forward as quickly as I could."
                            if v12_grappling_count == 1:
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with hpunch
                                "I was quicker than him, and managed to shoot in deep and get a body lock on him."
                                if jiujitsu > 3 and ian_athletics > 5:                                    
                                    "He tried to resist, but I was able to secure my hold on him."
                                    $ v12_position = "clinch"
                                else:
                                    play sound "sfx/punchgym.mp3"
                                    scene v12_mma_base_bg at mmabg
                                    show v12_mma_base_rival at mmarival
                                    show v12_mma_base_ian at mmaian 
                                    with hpunch
                                    "However, he saw it coming and shook me off easily. I had been too obvious..."
                                    $ v12_position = "standing"
                            else:
                                if jiujitsu > 3 and v12_grappling_count < 3:
                                    play sound "sfx/punchgym.mp3"
                                    scene v12_mma_takedown_bg at mmarightbg
                                    show v12_mma_shoot at mmaright
                                    with hpunch
                                    "I shot at him again, but he saw it coming. We scrambled as I tried to manhandle him."
                                    $ v12_position = "clinch"
                                    if ian_athletics < 6:
                                        $ v12_ian_tired += 1
                                    jump v12mmaclinchattack1
                                else:
                                    play sound "sfx/strongpunch.mp3"
                                    scene v12_mma_takedown_fail with hpunch
                                    i "...!"
                                    $ v12_ian_hurt += 1
                                    $ v12_rival_score += 1
                                    if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                                        jump v12mmakoed
                                    else:
                                        "I stumbled back, dizzy and covering up in desperation to avoid taking any more hits."
                                        if v12_round > 6:
                                            play sound "sfx/punch.mp3"
                                            scene v12_mma_jab_bg at mmaleftbg
                                            show v12_mma_jab_damage at mmaleft
                                            with hpunch
                                            $ v12_rival_score += 1
                                            if v12_rival_tired < 3:
                                                pause 0.5
                                                play sound "sfx/strongpunch.mp3"
                                                scene v12_mma_kick_bg at mmaleftbg
                                                show v12_mma_kick_damage at mmaleft
                                                with hpunch
                                                i "...!!"
                                                $ v12_rival_score += 1
                                                $ v12_ian_tired += 1
                                            else:
                                                play sound "sfx/punch.mp3"
                                                scene v12_mma_takedown_bg at mmaleftbg
                                                show v12_mma_lowkick_damage at mmaleft
                                                with vpunch
                                                $ v12_rival_score += 1
                                            
# GUARD action
        "{image=icon_defend.webp}Guard up":
            $ renpy.block_rollback()
            label v12mmaguard1:
                $ v12_guard_count += 1
                hide phase_initiative with fps3
            if v12_round == 1:
                "I had no idea of what kind of guy I was facing... I wanted to see what he'd do."
        # 1st guard -done
            if v12_guard_count == 1 and v12_round == 1:
                "I watched him closely behind my guard. He looked back at me, and I could see how focused he was."
                "Neither of us seemed to want to make the first move, looking for a good opening before going in recklessly."
                i "..."
                jump v12_hub_exit
        # 2nd guard - done
            if v12_guard_count == 2 and v12_ian_score <= 3 or (v12_guard_count == 1 and v12_round > 1 and v12_ian_score <= 3):
                $ v12_guard_count = 2
                "I stood behind my guard, studying his next move."
                if v12_round < 3:
                    $ timeout_label = "v12_hit_hub"
                "My opponent took the initiative, testing me with a range finder."
                if kickboxing > 3:
                    play sound "sfx/miss.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_dodge at mmaleftdodge
                    show v12_mma_jab_dodge_rival at mmaleft
                    with fps
                    pause 0.6
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_takedown_bg at mmaleftbg
                    show v12_mma_lowkick_dodge at mmaleft
                    with fps
                    "I parried both his jab and legkick, which gave me a window to counter."
                else:
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_block at mmaleft
                    with fps
                    pause 0.6
                    play sound "sfx/punch.mp3"
                    scene v12_mma_takedown_bg at mmaleftbg
                    show v12_mma_lowkick_damage at mmaleft
                    with vpunch
                    pause 0.5
                    $ v12_rival_score += 1
                    if kickboxing < 3:
                        play sound "sfx/strongpunch.mp3"
                        scene v12_mma_kick_bg at mmaleftbg
                        show v12_mma_kick_damage at mmaleft
                        with hpunch
                        i "Agh!!"
                        "He baited me to parry his jab, only to deliver a painful combination."
                        $ v12_rival_score += 1
                        if ian_athletics < 7:
                            $ v12_ian_tired += 1
                    else:
                        "I parried the jab, but it was a distraction to hide a stinging legkick."
                    "I took the hit and tried to retaliate."
                show phase_counter with short
                $ timeout_label = "v12guardd2"
                menu:
                    "{image=icon_counter.webp}Punch":
                        $ renpy.block_rollback()
                        play sound "sfx/miss.mp3"
                        scene v12_mma_jab_bg at mmarightbg
                        show v12_mma_jab_miss at mmaright
                        with fps3
                        pause 0.6
                        if kickboxing > 4:
                            play sound "sfx/punch.mp3"
                            scene v12_mma_body_bg at mmarightbg
                            show v12_mma_body_hit at mmabody
                            with vpunch
                            $ v12_ian_score += 1
                            if kickboxing > 5:
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_right_hit_bg at mmaleftbg
                                show v12_mma_right_hit at mmarightpower
                                with vpunch
                                fg "...!"
                                $ v12_ian_score += 1
                                $ v12_rival_hurt += 1
                            else:
                                pause 0.5
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_right_bg at mmaleftbg
                                show v12_mma_right_miss at mmaleftpower
                                with hpunch
                                "I threw a combination and managed to sneak a good body shot."
                        else:
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_right_bg at mmaleftbg
                            show v12_mma_right_miss at mmaleftpower
                            with hpunch
                            "He blocked both punches."
                        if v12_ian_score > 2:  # retaliate
                            play sound "sfx/punch.mp3"
                            scene v12_mma_takedown_bg at mmaleftbg
                            show v12_mma_lowkick_damage at mmaleft
                            with vpunch
                            "He retaliated with a sneaky low kick."
                            $ v12_rival_score += 1

                    "{image=icon_counter.webp}Kick":
                        $ renpy.block_rollback()
                        if kickboxing > 4:
                            play sound "sfx/strongpunch.mp3"
                            scene v12_mma_kick_bg at mmarightbg
                            show v12_mma_kick_hit at mmaup
                            with hpunch
                            if kickboxing > 5:
                                pause 0.6
                                play sound "sfx/punch.mp3"
                                scene v12_mma_lowkick_bg at mmarightbg
                                show v12_mma_lowkick_hit at mmalk
                                with vpunch
                                "I threw a roundhouse kick that sank deep into his midsection, and snuck in a low kick too."
                                $ v12_ian_score += 1
                            else:
                                "I threw a roundhouse kick that sank deep into his midsection."
                            $ v12_ian_score += 1
                            $ v12_rival_tired += 1
                        else:
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_kick_bg at mmarightbg
                            show v12_mma_kick_miss at mmaup
                            with hpunch
                            if kickboxing == 4:
                                play sound "sfx/punch.mp3"
                                scene v12_mma_lowkick_bg at mmarightbg
                                show v12_mma_lowkick_hit at mmalk
                                with vpunch
                                "He blocked my kick to the midsection, but I managed to sneak in a low kick after it."
                                $ v12_ian_score += 1
                            else:
                                "I threw a roundhouse kick to the midsection, but he tucked his arm and absorbed it."
                        if v12_ian_score > 2:  # retaliate
                            play sound "sfx/punch.mp3"
                            scene v12_mma_takedown_bg at mmaleftbg
                            show v12_mma_lowkick_damage at mmaleft
                            with vpunch
                            "He retaliated with a sneaky low kick."
                            $ v12_rival_score += 1
                            
                    
                    "{image=icon_athletics.webp}Tackle him":
                        $ renpy.block_rollback()
                        $ v12_grappling_count += 1
                        play sound "sfx/punch.mp3"
                        scene v12_mma_takedown_bg at mmarightbg
                        show v12_mma_shoot at mmaright
                        with hpunch
                        if jiujitsu > 3:
                            "I jumped in, trying to take him down."
                            $ v12_position = "clinch"
                            if v12_takedown_count == 0:
                                if v12_round < 3:
                                    $ v12_round += 1
                                jump v12rd2grapple
                            else:
                                jump v12rd2_hit_grappling
                        else:
                            "I jumped in, trying to take him down, but he moved back and avoided my attempt."
                            if v12_ian_score > v12_rival_score:
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_kick_bg at mmaleftbg
                                show v12_mma_kick_damage at mmaleft
                                with hpunch
                                i "Agh!!"
                                "As he stepped back, he landed a clean kick to my gut."
                                $ v12_rival_score += 1
                                $ v12_ian_tired += 1
                
                    "{image=icon_defend.webp}Guard up":
                        $ renpy.block_rollback()
                        label v12guardd2:
                            hide phase_counter with fps3
                        "I wasn't sure if he would keep throwing, so I moved back."
                        if v12_ian_tired > 0:
                            $ v12_ian_tired -= 1
                            "That allowed me to get some fresh air in my lungs and recover a bit."
        
        # 3rd guard
            else:
                $ v12_guard_count = 3
                "I prepared myself to react and counter his next move."
                call mma_randomizer(mma_randomizer_defend) from _call_mma_randomizer_1
                jump v12_hub_exit                            
                    
                        
##PROVOKE action
        "{image=icon_provoke.webp}Provoke" if v12_provoke_count < 7:
            $ renpy.block_rollback()
            label v12mmaprovoke:
                $ v12_provoke_count += 1
                hide phase_initiative with fps3
            if v12_round == 1:
                "I had no idea of what kind of guy I was facing..."
            if v12_provoke_count < 5:
            # PROVOKE 1-done
                if v12_provoke_count == 1:
                    "I decided to lower my guard and show him an opening to see if he would take it."
                    "He did."
                    play sound "sfx/miss.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_dodge at mmaleftdodge
                    show v12_mma_jab_dodge_rival at mmaleft
                    with fps
                    "I dodged his crisp jab by the skin of my teeth."
                    $ timeout_label = "v12prvk1"
                    menu:
                        "{image=icon_counter.webp}Strike back":
                            $ renpy.block_rollback()
                            if kickboxing > 2 and v12_ian_tired < 2 or kickboxing > 3:
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_jab_bg at mmarightbg
                                show v12_mma_jab_hit at mmaright
                                with hpunch
                                if kickboxing == 6:
                                    pause 0.6
                                    play sound "sfx/punch.mp3"
                                    scene v12_mma_body_bg at mmarightbg
                                    show v12_mma_body_hit at mmabody
                                    with hpunch
                                    pause 0.6
                                    play sound "sfx/punch.mp3"
                                    scene v12_mma_lowkick_bg at mmarightbg
                                    show v12_mma_lowkick_hit at mmalk
                                    with vpunch
                                    "I countered with a quick combination, landing flush with three swift strikes."
                                    $ v12_ian_score += 2
                                elif kickboxing > 4:
                                    pause 0.6
                                    play sound "sfx/punch.mp3"
                                    scene v12_mma_lowkick_bg at mmarightbg
                                    show v12_mma_lowkick_hit at mmalk
                                    with hpunch
                                    "I countered with a swift combination, landing flush with both strikes."
                                    $ v12_ian_score += 1
                                else:
                                    "I countered with a jab of my own, landing flush on his face."
                                $ v12_ian_score += 1
                            else:
                                play sound "sfx/miss.mp3"
                                scene v12_mma_jab_bg at mmarightbg
                                show v12_mma_jab_miss at mmaright
                                with vpunch
                                "I countered with a jab of my own, but he dodged it easily."
                                if kickboxing > 2:
                                    play sound "sfx/punch.mp3"
                                    scene v12_mma_lowkick_bg at mmarightbg
                                    show v12_mma_lowkick_hit at mmalk
                                    with hpunch
                                    "However, I managed to sneak in a low kick that landed on his thigh."
                                    $ v12_ian_score += 1
                    
                        "{image=icon_counter.webp}Grapple":
                            $ renpy.block_rollback()
                            $ v12_grappling_count += 1
                            if jiujitsu > 3 or kickboxing > 3:
                                play sound "sfx/miss.mp3"
                                scene v12_mma_right_bg at mmaleftbg
                                show v12_mma_right_dodge_rival at mmaleft
                                show v12_mma_right_dodge at mmaleftdodge
                                with fps
                                "I ducked under his one-two and shot for a takedown."
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with fps
                                if jiujitsu > 3:
                                    "I got on deep and got a strong body lock on him."
                                    $ v12_position = "clinch"
                                    if v12_grappling_count == 1 or v12_round < 3:
                                        $ v12_round += 1
                                    jump v12rd2grapple
                                else:
                                    "However, he saw it coming and shook me off easily. His defense was on point..."
                            elif kickboxing > 3:
                                play sound "sfx/miss.mp3"
                                scene v12_mma_right_bg at mmaleftbg
                                show v12_mma_right_dodge_rival at mmaleft
                                show v12_mma_right_dodge at mmaleftdodge
                                with fps
                                "I had to duck to the side to avoid his one-two and wasn't able to close the distance."
                            else:
                                play sound "sfx/punch.mp3"
                                scene v12_mma_right_bg at mmaleftbg
                                show v12_mma_right_damage at mmaleftpower
                                with vpunch
                                "I tried to shoot for a takedown, but I ran into his fist."
                                "I needed to be more careful...!"
                                $ v12_rival_score += 2
                    
                        "{image=icon_defend.webp}Stand back":
                            $ renpy.block_rollback()
                            label v12prvk1:
                                "I kept my distance and my defense, cautious, but couldn't avoid a sneaky leg kick."
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_takedown_bg at mmaleftbg
                            show v12_mma_lowkick_damage at mmaleft
                            with vpunch
                            "It stung, but my leg wasn't compromised."
                            $ v12_rival_score += 1
                            if v12_ian_tired > 0:
                                $ v12_ian_tired -= 1
                            if v12_round < 3:
                                "He was quick with his striking..."
            # PROVOKE 2  - done
                elif v12_provoke_count == 2:
                    label v12prvk2:
                        i "Come and get some. Are you afraid or what?"
                    "He ignored my words, his focused expression unchanging. It was obvious he wasn't gonna fall for such a provocation."
                    "Instead, he moved forward carefully and I saw him preparing to throw a jab..."
                    menu:
                        "Block high":
                            $ renpy.block_rollback()
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_jab_bg at mmaleftbg
                            show v12_mma_jab_block at mmaleft
                            with fps
                            pause 0.6
                            play sound "sfx/punch.mp3"
                            scene v12_mma_takedown_bg at mmaleftbg
                            show v12_mma_lowkick_damage at mmaleft
                            with vpunch
                            "I fell for his feint and got hit with a low kick."
                            $ v12_rival_score += 1
                            $ v12_ian_tired += 1

                        "Block low":
                            $ renpy.block_rollback()
                            play sound "sfx/miss.mp3"
                            scene v12_mma_jab_bg at mmaleftbg
                            show v12_mma_jab_dodge at mmaleftdodge
                            show v12_mma_jab_dodge_rival at mmaleft
                            with fps
                            pause 0.6
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_takedown_bg at mmaleftbg
                            show v12_mma_lowkick_dodge at mmaleft
                            with fps
                            "I read his feint and checked his low kick."
                            if kickboxing > 4:
                                play sound "sfx/punch.mp3"
                                scene v12_mma_jab_bg at mmarightbg
                                show v12_mma_jab_hit at mmaright
                                with hpunch
                                pause 0.5
                                play sound "sfx/punch.mp3"
                                scene v12_mma_kick_bg at mmarightbg
                                show v12_mma_kick_hit at mmaup
                                with hpunch
                                fg "...!"
                                $ v12_ian_score += 2
                                if v12_rival_tired < 2:
                                    $ v12_rival_tired += 1
                                if v12_ian_score >= v12_rival_score + 2:
                                    label v12provoke2retaliate:
                                        if v12_ian_score >= v12_rival_score + 3:
                                            if (kickboxing > 4 and v12_ian_tired < 4) or (kickboxing > 3 and v12_ian_tired < 3):
                                                play sound "sfx/punch.mp3"
                                                scene v12_mma_jab_bg at mmaleftbg
                                                show v12_mma_jab_damage at mmaleft
                                                with hpunch
                                                pause 0.4
                                                play sound "sfx/punchgym.mp3"
                                                scene v12_mma_right_bg at mmaleftbg
                                                show v12_mma_right_block at mmaleftpower
                                                with vpunch
                                                i "Ngh!"
                                                $ v12_rival_score += 1
                                            else:
                                                play sound "sfx/punchgym.mp3"
                                                scene v12_mma_jab_bg at mmaleftbg
                                                show v12_mma_jab_block at mmaleft
                                                with hpunch
                                                pause 0.4
                                                play sound "sfx/strongpunch.mp3"
                                                scene v12_mma_right_bg at mmaleftbg
                                                show v12_mma_right_damage at mmaleftpower
                                                with vpunch
                                                i "Umpf!!"
                                                $ v12_rival_score += 1
                                                $ v12_ian_hurt += 1
                                                if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                                                    jump v12mmakoed
                                        else:
                                            play sound "sfx/punchgym.mp3"
                                            scene v12_mma_jab_bg at mmaleftbg
                                            show v12_mma_jab_damage at mmaleft
                                            with hpunch
                                            $ v12_rival_score += 1
                                            "His jab was fast!"
                            elif kickboxing > 3:
                                play sound "sfx/miss.mp3"
                                scene v12_mma_jab_bg at mmarightbg
                                show v12_mma_jab_miss at mmaright
                                with fps3
                                pause 0.5
                                play sound "sfx/punch.mp3"
                                scene v12_mma_takedown_bg at mmaleftbg
                                show v12_mma_lowkick_damage at mmaleft
                                with vpunch
                                "I landed a kick as we disengaged, catching him by surprise with a quick combination."
                                $ v12_rival_score += 1
                                if v12_ian_score >= v12_rival_score + 2:
                                    jump v12provoke2retaliate
                            else:
                                play sound "sfx/punch.mp3"
                                scene v12_mma_jab_bg at mmaleftbg
                                show v12_mma_jab_damage at mmaleft
                                with hpunch
                                i "...!{w=1.0}{nw}"
                                play sound "sfx/punch.mp3"
                                scene v12_mma_takedown_bg at mmaleftbg
                                show v12_mma_lowkick_damage at mmaleft
                                with vpunch
                                "He managed to score with a quick combo as we disengaged."
                                $ v12_rival_score += 2

                # PROVOKE 3  - done
                elif v12_provoke_count == 3:
                    label v12prvk3:
                        i "Come on! Hit me! Is that all you got!?"
                        if ian_chad < 5:
                            $ ian_chad += 1
                    "This time, he took the bait and launched forward with an attack."
                    play sound "sfx/miss.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_dodge at mmaleftdodge
                    show v12_mma_jab_dodge_rival at mmaleft
                    with fps
                    menu:
                        "{image=icon_counter.webp}Strike back":
                            $ renpy.block_rollback()
                            label v12prvk3hit:
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_body_bg at mmaleftbg
                                show v12_mma_counter_damage at mmaleftcounter
                                with hpunch
                            i "Oof!!"
                            $ v12_rival_score += 1
                            $ v12_ian_hurt += 1
                            if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                                jump v12mmakoed
                            if kickboxing > 4 or jiujitsu > 3:
                                play sound "sfx/miss.mp3"
                                scene v12_mma_right_bg at mmaleftbg
                                show v12_mma_right_dodge_rival at mmaleft
                                show v12_mma_right_dodge at mmaleftdodge
                                with hpunch
                                "He caught me coming in, but I managed to escape his follow-up punch."
                            else:
                                play sound "sfx/punch.mp3"
                                scene v12_mma_takedown_bg at mmaleftbg
                                show v12_mma_lowkick_damage at mmaleft
                                with vpunch
                                $ v12_rival_score += 1
                            "That had been too reckless... This guy was sharp!"
                                
                        "{image=icon_athletics.webp}Tackle him":
                            $ renpy.block_rollback()
                            jump v12prvk3hit

                        "{image=icon_defend.webp}Defend":
                            $ renpy.block_rollback()
                            if kickboxing > 4:
                                play sound "sfx/miss.mp3"
                                scene v12_mma_right_bg at mmaleftbg
                                show v12_mma_right_dodge_rival at mmaleft
                                show v12_mma_right_dodge at mmaleftdodge
                                with fps
                                "I knew a second strike was coming, and I dodged it."
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_right_hit_bg at mmaleftbg
                                show v12_mma_right_hit at mmarightpower
                                with vpunch
                                pause 0.6
                                $ v12_ian_score += 1
                                $ v12_rival_hurt += 1
                                if kickboxing == 6:
                                    play sound "sfx/punch.mp3"
                                    scene v12_mma_body_bg at mmarightbg
                                    show v12_mma_body_hit at mmabody
                                    with vpunch
                                    pause 0.3
                                    play sound "sfx/strongpunch.mp3"
                                    scene v12_mma_kick_bg at mmarightbg
                                    show v12_mma_kick_hit at mmaup
                                    with hpunch
                                    "I unleashed a combo before he could recover, punishing his midsection. That would surely diminish his stamina."
                                    $ v12_rival_tired += 1
                                elif jiujitsu == 5:
                                    scene v12_mma_takedown_bg at mmarightbg
                                    show v12_mma_shoot at mmaright
                                    with fps
                                    pause 0.6
                                    scene v12_mma_takedown_bg at mmakickkobg
                                    show v12_mma_takedown1 at mmarightpower
                                    with fps
                                    play sound "sfx/fall.mp3"
                                    with hpunch
                                    "I followed up my strike with a sweep, making him trip and fall down on his knees."
                                    "He sprung back up with urgency, and I could the see frustration on his face."
                                    $ v12_rival_tired += 1
                                else:
                                    "He dropped his guard, and I took advantage of his mistake, landing a strong right straight."
                            else:
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_right_bg at mmaleftbg
                                show v12_mma_right_block at mmaleftpower
                                with fps
                                "I knew a second strike was coming, and I guarded it."
                                play sound "sfx/punch.mp3"
                                scene v12_mma_lowkick_bg at mmarightbg
                                show v12_mma_lowkick_hit at mmalk
                                with vpunch
                                $ v12_ian_score += 1
                                "I punished him with a low kick before getting out of range to avoid any counterattack."

                        "Stand back":
                            $ renpy.block_rollback()
                            "I saw his intentions to throw a one-two, so I stood back, avoiding his straight."
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_right_bg at mmaleftbg
                            show v12_mma_right_block at mmaleftpower
                            with hpunch
                            pause 0.6
                            if kickboxing > 4:
                                "He tried throwing a low kick to end his combination, but I jumped back and avoided it."
                            else:
                                play sound "sfx/punch.mp3"
                                scene v12_mma_takedown_bg at mmaleftbg
                                show v12_mma_lowkick_damage at mmaleft
                                with vpunch
                                "He was at the right distance to connect with a low kick I wasn't able to avoid."
                                $ v12_rival_score += 1

            # PROVOKE 4  - done 
                elif v12_provoke_count == 4:
                    i "What's the matter? Getting tired?"
                    fg "Shut your fucking mouth already."
                    i "Oh, so you can talk after all!"
                    if kickboxing > 3:
                        play sound "sfx/miss.mp3"
                        scene v12_mma_right_bg at mmaleftbg
                        show v12_mma_right_dodge_rival at mmaleft
                        show v12_mma_right_dodge at mmaleftdodge
                        with fps
                        "He threw a right straight with ill intentions, but I managed to dodge it."
                    else:
                        play sound "sfx/punchgym.mp3"
                        scene v12_mma_right_bg at mmaleftbg
                        show v12_mma_right_block at mmaleftpower
                        with vpunch
                        "He threw a right straight with ill intentions. Even though I blocked it, it made my head rumble."
                    menu:
                        "{image=icon_counter.webp}Counterpunch":
                            $ renpy.block_rollback()
                            if kickboxing > 4:
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_right_bg at mmaleftbg
                                show v12_mma_right_miss at mmaleftpower
                                with hpunch
                                "He was expecting my counterpunch and parried it."
                                if kickboxing == 6:
                                    play sound "sfx/punchgym.mp3"
                                    scene v12_mma_right_bg at mmaleftbg
                                    show v12_mma_right_block at mmaleftpower
                                    with hpunch
                                    "I also managed to avoid his next strike and jumped back, resetting."
                                else:
                                    play sound "sfx/punch.mp3"
                                    scene v12_mma_body_bg at mmaleftbg
                                    show v12_mma_counter_damage at mmaleftcounter
                                    with hpunch
                                    i "Ngh!!"
                                    $ v12_rival_score += 2
                                    if v12_ian_tired > 2:
                                        $ v12_ian_hurt += 1
                                        if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                                            jump v12mmakoed
                            else:
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_body_bg at mmaleftbg
                                show v12_mma_counter_damage at mmaleftcounter
                                with hpunch
                                i "Ngh!!"
                                "He read my intentions and countered me with a hook that hit me really hard."
                                $ v12_rival_score += 2
                                $ v12_ian_hurt += 1
                                if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                                    jump v12mmakoed
                    
                        "{image=icon_athletics.webp}Tackle him":
                            $ renpy.block_rollback()
                            $ v12_grappling_count += 1
                            if jiujitsu > 3:
                                $ v12_ian_tired += 1
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with fps
                                pause 0.6
                                play sound "sfx/fall.mp3"
                                scene v12_mma_takedown_miss with vpunch
                                "He read my intentions and defended my takedown attempt, trying to get me to the ground instead."
                                "I scrambled and managed to get free, resetting."
                            else:
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_takedown_fail with vpunch
                                i "Oof!!"
                                "He read my intentions and met my attempt with a kick that hit me really hard."
                                $ v12_rival_score += 2
                                $ v12_ian_hurt += 1
                                if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                                    jump v12mmakoed
                                else:
                                    "I stumbled back, dizzy and covering up in desperation to avoid taking any more hits."
                    
                        "{image=icon_defend.webp}Block high":
                            $ renpy.block_rollback()
                            if kickboxing > 3:
                                play sound "sfx/high5.mp3"
                                scene v12_mma_headkick_bg at mmaleftbg
                                show v12_mma_headkick_block at mmaup
                                with vpunch
                                "I raised my guard at the last instant, blocking a whipping roundhouse kick."
                                if v12_ian_tired < 2:
                                    $ v12_ian_tired += 1
                                play sound "sfx/punch.mp3"
                                scene v12_mma_lowkick_bg at mmarightbg
                                show v12_mma_lowkick_hit at mmalk
                                with fps
                                if kickboxing > 4:
                                    pause 0.6
                                    play sound "sfx/punch.mp3"
                                    scene v12_mma_body_bg at mmarightbg
                                    show v12_mma_body_hit at mmabody
                                    with vpunch
                                    "I retaliated with a combination of my own, connecting two solid hits."
                                    $ v12_ian_score += 2
                                    if v12_rival_tired < 4:
                                        $ v12_rival_tired += 1
                                else:
                                    "I retaliated with a kick of my own, striking his thigh."
                                    $ v12_ian_score += 1
                                if v12_ian_score >= v12_rival_score + 3:
                                    jump v12provoke2retaliate
                            else:
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_kick_bg at mmaleftbg
                                show v12_mma_kick_damage at mmaleft
                                with hpunch
                                i "Oof!!"
                                $ v12_rival_score += 2
                                $ v12_ian_tired += 1
                                if v12_ian_tired > 2 and v12_ian_hurt < 2:
                                    $ v12_ian_hurt += 1

                        "{image=icon_defend.webp}Block low":
                            $ renpy.block_rollback()
                            if kickboxing > 3:
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_jab_bg at mmaleftbg
                                show v12_mma_jab_damage at mmaleft
                                with fps
                                i "...!{w=1.0}{nw}"
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_kick_bg at mmaleftbg
                                show v12_mma_kick_damage at mmaleft
                                with hpunch
                                i "Oof!!"
                                $ v12_rival_score += 2
                                $ v12_ian_tired += 1
                                if v12_ian_tired > 2 and v12_ian_hurt > 2:
                                    $ v12_ian_hurt += 1
                            else:
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_takedown_bg at mmaleftbg
                                show v12_mma_lowkick_damage at mmaleft
                                with fps
                                "I raised my leg and managed to check his low kick, nullifying any damage."
                                if jiujitsu > 3:
                                    play sound "sfx/slap.mp3"
                                    scene v12_mma_takedown_bg at mmakickkobg
                                    show v12_mma_takedown1 at mmarightpower
                                    with fps
                                    "I used this chance to trip him again."
                                    "He stumbled across the mat, trying not to fall down. I moved to engage him again."
                                    if v12_rival_tired < 4:
                                        $ v12_rival_tired += 1
                                else:
                                    play sound "sfx/miss.mp3"
                                    scene v12_mma_jab_bg at mmarightbg
                                    show v12_mma_jab_miss at mmaright
                                    with fps3
                                    "I tried to catch him with a jab, but he dodged it skillfully."
                                if v12_ian_score >= v12_rival_score + 2:
                                    jump v12provoke2retaliate
                
            # PROVOKE 5 - done    
            else:
                if v12_provoke_count == 5:
                    i "Come closer. I'll rearrange that ugly face of yours!"
                    fg "I'm tired of hearing you yap, motherfucker!"
                    ref "Hey! Both of you, any more of that and I'll disqualify you."
                    i "Alright, alright."
            # PROVOKE 6  - done
                elif v12_provoke_count == 6:
                    "I knew I couldn't use verbal warfare anymore without risking being disqualified..."
                    "I lowered my guard and showed my rival an opening, but he was wise to my intentions and didn't take the bait."
                    "Provoking him wouldn't be of use anymore..."
                jump v12_action_menu


## TAPOUT action
        "{image=icon_tap.webp}Tap out!":
            $ renpy.block_rollback()
            label v12mmatapout:
                $ v12_tapout_count += 1
                hide phase_initiative with fps3
            if v12_tapout_count == 1 and v12_ian_hurt < 2:
                i "..."
                "Could I really do this...? What the hell had I gotten myself into?"
            elif v12_tapout_count == 2 and v12_ian_hurt < 2:
                i "..."
                "I couldn't focus. I was too nervous. Should I even be doing this?"
            elif v12_tapout_count == 3 and v12_ian_hurt < 1:
                "I didn't really wanna be here..."
            else:
                $ tournament = "tapout"
                "I had enough. This hurt."
                "I was risking getting seriously injured for no real reason..."
                stop music fadeout 2.0
                $ fian = "worried"
                scene gym
                show ian at lef
                show fighter at rig
                with short
                show ian at lef3 with move
                i "Wait, wait..."
                "I knelt down and tapped on the mat, which prompted the referee to stop the fight."
                show fighter at rig3 with move
                hide fighter with short
                jump v12mmafightend
            jump v12_hit_hub
                
            label v12_hit_hub:
                if v12_rival_score == 0 and v12_round < 3:
                    jump v12rd1_hit
                call mma_randomizer(mma_randomizer_hit) from _call_mma_randomizer_2
                jump v12_hit_hub
                
label v12_hub_exit:
    # ian status commentary
    
    if v12_ian_hurt == 2 and v12_mma_status_hurt == 0:
        $ v12_mma_status_hurt += 1
        "My head was ringing... That last strike hurt!"
    elif v12_ian_hurt == 3 and v12_mma_status_hurt < 2:
        $ v12_mma_status_hurt = 2
        "I felt disoriented after taking so many hits. I couldn't afford to be hit like that again!"
    elif v12_ian_hurt > 3 and v12_mma_status_hurt < 3:
        $ v12_mma_status_hurt = 3
        "My legs felt wobbly and my mind was foggy, making it hard to focus or even move effectively..."
    elif v12_ian_tired == 2 and v12_mma_status_tired == 0:
        $ v12_mma_status_tired += 1
        "I took a deep breath. I was starting to get a bit tired..."
    elif v12_ian_tired == 3 and v12_mma_status_tired < 2:
        $ v12_mma_status_tired = 2
        "I was starting to breath heavily and slow down a bit. My cardio was being tested, but I still had some gas in the tank."
    elif v12_ian_tired == 4 and v12_mma_status_tired < 3:
        $ v12_mma_status_tired = 3
        "I fought to get air in my lungs, wheezing. I couldn't go on for much longer...!"   
    elif v12_ian_tired > 4 and v12_mma_status_tired < 4:
        $ v12_mma_status_tired = 4
        "I felt my muscles aching and my lungs demanding oxygen. Tiredness was making me feel dizzy."
    elif v12_mma_status_tired > 2 and v12_ian_tired < 4 and v12_mma_status_tired < 5:
        $ v12_mma_status_tired = 5
        "I was able to get some air back, but I wouldn't be able to keep pushing myself for much longer."

    if v12_round == 1:
        jump v12mmafight_rd2
    if v12_round == 2:
        jump v12mmafight_rd3
    if v12_round == 3:
        jump v12mmafight_rd4
    if v12_round == 4:
        jump v12mmafight_rd5
    if v12_round == 5:
        jump v12mmafight_rd6
    if v12_round == 6:
        jump v12mmafight_break
    if v12_round == 7:
        jump v12mmafight_rd8
    if v12_round == 8:
        jump v12mmafight_rd9
    if v12_round == 9:
        jump v12mmafight_rd10
    if v12_round == 10:
        jump v12mmafight_rd11
    if v12_round == 11:
        jump v12mmafight_rd12
    if v12_round == 12:
        jump v12mmafightend

####################################################################################################################################################
## ROUND 2 #########################################################################################################################################
label v12mmafight_rd2:
    $ renpy.block_rollback()
    if ian_wits < 7:
        $ timeout = 3.5
    elif ian_wits > 6:
        $ timeout = 3.0
    else:
        $ timeout = 2.5
    $ timeout_label = "v12_hit_hub"
    $ v12_round = 2
    
# STANDING
    if v12_position == "standing":
        if v12_guard_count == 0:
            scene v12_mma_base_bg at mmabganim
            show v12_mma_base_rival at mmarivalanim 
            show v12_mma_base_ian at mmaiananim 
            with short
        "We circled each other, trying to find our rhythm without getting caught in the other's."
        show phase_initiative with short
        $ renpy.block_rollback()
        menu:
            "{image=icon_attack.webp}Attack!":
                $ renpy.block_rollback()
                hide phase_initiative with fps3
                jump v12mmaattack1

            "{image=icon_defend.webp}Guard up":
                $ renpy.block_rollback()
                hide phase_initiative with fps3
                jump v12mmaguard1                

            "{image=icon_provoke.webp}Provoke":
                $ renpy.block_rollback()
                jump v12mmaprovoke

            "{image=icon_tap.webp}Tap out!":
                $ renpy.block_rollback()
                jump v12mmatapout
                

# CLINCH
    if v12_position == "clinch":
        show phase_initiative with short
        label v12rd2grapple:
            $ timeout_label = "v12rd2_hit_grappling"
        "My rival fought to free himself, and I felt he would succeed if I didn't do something soon."
        $ renpy.block_rollback()
        menu v12mmaclinchattack1:
            "{image=icon_wits.webp}Trip him" if jiujitsu > 3 and v12_takedown_count == 0 or jiujitsu > 4:
                $ renpy.block_rollback()
                play sound "sfx/slap.mp3"
                scene v12_mma_takedown_bg at mmakickkobg
                show v12_mma_takedown1 at mmarightpower
                with short
                fg "...!{w=1.0}{nw}"
                $ v12_rival_tired += 1
                $ v12_ian_score += 1
                if v12_takedown_count == 0 and v12_ian_tired < 2:
                    play sound "sfx/fall.mp3"
                    scene v12_mma_ground_top with vpunch
                    "I swept him off his feet with a smooth trip and managed to take him to the ground."
                    "He tried to get up as soon as his butt touched the floor, but I was quick to get on top of him."
                    $ v12_takedown_count += 1
                    $ v12_ian_score += 1
                    $ v12_position = "groundtop"
                else:
                    play sound "sfx/fall.mp3"
                    scene v12_mma_base_bg at mmabg
                    show v12_mma_base_ian at mmaian
                    with vpunch
                    "I swept him, making him stumble and fall. He rolled on the mat, trying to get up before I could get on top of him."
                    show v12_mma_base_rival at mmarival with short
                    pause 0.5
                    play sound "sfx/miss.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_dodge at mmaleftdodge
                    show v12_mma_jab_dodge_rival at mmaleft
                    with fps
                    pause 0.4
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_takedown_bg at mmaleftbg
                    show v12_mma_lowkick_damage at mmaleft
                    with fps
                    "He tried to keep me off him with strikes, but they were ineffective."

            "{image=icon_athletics}Throw him":
                $ renpy.block_rollback()
                hide phase_initiative with fps3
                if jiujitsu < 5:
                    $ v12_ian_tired += 1
                if jiujitsu == 5 and v12_takedown_count == 0:
                    "I knew just what to do. I had drilled it with Wen a thousand times."
                    play sound "sfx/slap.mp3"
                    scene v12_mma_takedown_bg at mmaslambg1
                    show v12_mma_takedown2 at mmaslam1
                    with hpunch
                    "I dropped my center of gravity, hoisted the opponent onto my shoulder, and extended my knees, lifting him off the ground with no chance to resist."
                    fg "...!{w=1.0}{nw}"
                    play sound "sfx/throw.mp3"
                    scene v12_mma_ground_top with vpunch
                    "And then I dropped him on the mat, letting my weight fall on top of him."
                    "He tried to scramble, but I was already on top and opposed him."
                    $ v12_takedown_count += 1
                    $ v12_rival_tired += 1
                    $ v12_ian_score += 2
                    $ v12_position = "groundtop"
                elif ian_athletics > 5 and jiujitsu > 3 and v12_ian_tired < 2:
                    if ian_athletics > 6 and v12_ian_tired > 0:
                        $ v12_ian_tired -= 1 # negate prior point 
                    "We battled each other with urgency, but finally I was able to capitalize on a mistake he made and take him off balance."
                    play sound "sfx/slap.mp3"
                    scene v12_mma_takedown_bg at mmakickkobg
                    show v12_mma_takedown1 at mmarightpower
                    with short
                    fg "...!{w=1.0}{nw}"
                    play sound "sfx/fall.mp3"
                    scene v12_mma_ground_top with vpunch
                    "He tried to get up as soon as his butt touched the floor, but I was quick to get on top of him."
                    $ v12_takedown_count += 1
                    $ v12_ian_score += 1
                    $ v12_position = "groundtop"
                else:
                    play sound "sfx/fall.mp3"
                    scene v12_mma_takedown_miss with vpunch
                    i "...!"
                    "Before I could react, he wrapped one arm around my neck and forced me to my knees. This was bad...!"
                    if jiujitsu > 2 and v12_ian_tired < 2 or ian_athletics > 5 and v12_ian_tired < 2:
                        "I fought his hold with both of my arms, squirmed, and managed to break free."
                        play sound "sfx/punchgym.mp3"
                        scene v12_mma_base_bg at mmabg
                        show v12_mma_base_rival at mmarival 
                        show v12_mma_base_ian at mmaian 
                        with vpunch
                        if v12_grappling_count > 2:
                            "That had been a close one..."
                        label v12takedownfail1:
                            $ v12_position = "standing"
                            pause 0.8
                        play sound "sfx/punch.mp3"
                        scene v12_mma_takedown_bg at mmaleftbg
                        show v12_mma_lowkick_damage at mmaleft
                        with vpunch
                        pause 0.6
                        if kickboxing > 3 and v12_grappling_count < 2:
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_right_bg at mmaleftbg
                            show v12_mma_right_block at mmaleftpower
                            with hpunch
                            "As I was getting up, my opponent punished me with a kick and a right straight which I managed to block."
                            $ v12_rival_score += 1
                        else:
                            play sound "sfx/strongpunch.mp3"
                            scene v12_mma_right_bg at mmaleftbg
                            show v12_mma_right_damage at mmaleftpower
                            with vpunch
                            i "Umpf!!"
                            "Before I could put up my guard my opponent punished me with two accurate strikes."
                            $ v12_rival_score += 2
                            if v12_ian_hurt < 2:
                                $ v12_ian_hurt += 1
                    else:
                        "I tried to free myself frantically. I couldn't get caught like this, not this early!" 
                        "However, I couldn't shake him off, and to protect my head, I ended up giving him top position."
                        scene v12_mma_ground_bottom with short
                        if jiujitsu < 4 or ian_athletics < 6:
                            $ v12_ian_tired += 1
                        $ v12_rival_score += 1
                        $ v12_position = "groundbottom"
            
            "Wrestle him down":
                $ renpy.block_rollback()
                hide phase_initiative with fps3
                "Without letting go, I tried wrestling him down, tussling, and trying to make him lose his balance."
                if (ian_athletics > 5 and jiujitsu > 3 and v12_ian_tired < 1) or jiujitsu == 5 and v12_ian_tired < 2:
                    "We battled each other with urgency, but finally I was able to capitalize on a mistake he made and drag him to the ground."
                    scene v12_mma_ground_top with short
                    $ v12_rival_tired += 1
                    $ v12_takedown_count += 1
                    $ v12_ian_score += 1
                    $ v12_position = "groundtop"
                    if ian_athletics < 7:
                        $ v12_ian_tired += 1
                    jump v12mmagroundtop1
                else:
                    if ian_athletics < 7:
                        $ v12_ian_tired += 1
                    label v12rd2_hit_grappling:
                        hide phase_initiative with fps3
                        if jiujitsu > 2 and v12_grappling_count < 2:
                            "I wasn't able to though, and he whisked away, getting on striking range again."
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_base_bg at mmabganim
                            show v12_mma_base_rival at mmarivalanim
                            show v12_mma_base_ian at mmaiananim
                            with hpunch
                            jump v12takedownfail1
                        else:
                            play sound "sfx/fall.mp3"
                            scene v12_mma_takedown_miss with vpunch
                            i "...!"
                            "Before I could react, he wrapped one arm around my neck and forced me to my knees. This was bad...!"
                            if ian_athletics > 5 and v12_ian_tired < 2 or jiujitsu > 2 and v12_ian_tired < 2:
                                "I fought his hold with both of my arms, squirmed, and managed to break free."
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_base_bg at mmabg
                                show v12_mma_base_rival at mmarival 
                                show v12_mma_base_ian at mmaian 
                                with vpunch
                                jump v12takedownfail1
                            else:
                                "I tried to free myself frantically. I couldn't get caught like this, not this early!" 
                                "However, I couldn't shake him off, and to protect my head, I ended up giving him top position."
                                scene v12_mma_ground_bottom with short
                                $ v12_ian_tired += 1
                                $ v12_rival_score += 1
                                $ v12_position = "groundbottom"
    
    jump v12_hub_exit

####################################################################################################################################################
## ROUND 3 #########################################################################################################################################
label v12mmafight_rd3:
    $ renpy.block_rollback()
    if ian_wits < 7:
        $ timeout = 3.5
    elif ian_wits > 6:
        $ timeout = 3.0
    else:
        $ timeout = 2.5
    $ timeout_label = "v12_hit_hub"
    $ v12_round = 3
    
    if v12_position == "standing":
        scene v12_mma_base_bg at mmabganim
        show v12_mma_base_rival at mmarivalanim 
        show v12_mma_base_ian at mmaiananim
        with short
        if v12_guard_count!= 1:
            "My opponent looked at me intently, measuring his next move."
        show phase_initiative
        with short
        $ renpy.block_rollback()
        menu:
            "{image=icon_attack.webp}Attack!":
                $ renpy.block_rollback()
                hide phase_initiative 
                show phase_attack
                with short
                jump v12mmaattack1

            "{image=icon_defend.webp}Guard up":
                $ renpy.block_rollback()
                hide phase_initiative with fps3
                jump v12mmaguard1

            "{image=icon_provoke.webp}Provoke":
                $ renpy.block_rollback()
                jump v12mmaprovoke            

            "{image=icon_tap.webp}Tap out!":
                $ renpy.block_rollback()
                jump v12mmatapout
            ## RD 3 IAN HIT
                
    # CLINCH                   
    if v12_position == "clinch":
        jump v12rd2grapple

    # GROUND TOP
    if v12_position == "groundtop":
        show phase_initiative
        with short
        $ renpy.block_rollback()
        label v12mmagroundtop1:
            $ timeout_label = "v12mmagroundtime1"
            $ config.menu_include_disabled = False
            $ greyed_out_disabled = True
        menu:
            "{image=icon_athletics.webp}Submit him!":
                $ renpy.block_rollback()
                if persistent.include_disabled:
                    $ config.menu_include_disabled = True
                $ greyed_out_disabled = False
                $ v12_sub_count += 1
                hide phase_initiative with fps3
                "I was in position to finish this right away. I fought his arms, trying to get a hold of one so I could move into a submission..."
                play sound "sfx/slap.mp3"
                scene v12_mma_sub2 with hpunch
                fg "...!"
                if v12_round == 6:
                    $ v12_position = "armbar"
                    jump v12_hub_exit
                play sound "sfx/fall.mp3"
                scene v12_mma_ground_bottom with hpunch
                "I was too hasty and didn't secure the arm correctly. That gave him the opportunity to escape and twist his hips, turning the position around."
                "He was strong!"
                if jiujitsu > 3:
                    $ v12_position = "guillotine"
                    play sound "sfx/slap.mp3"
                    scene v12_mma_sub1 with vpunch
                    "However, he gave me what I was looking for."
                    "He exposed his neck, and I wrapped a guillotine choke around it."
                    "I began to squeeze as hard as I could, trying to suffocate him, but he wasn't tapping out."
                    "That guy was determined to resist!"
                elif ian_athletics > 7:
                    $ v12_position = "standing"
                    "But so was I, and I managed to push him away from me and scramble, getting back on my feet."
                else:
                    $ v12_position = "groundbottom"
                    "He was still fresh and full of energy. I couldn't get him off...!"

            "Secure the position" if jiujitsu < 5 and ian_wits < 7:
                $ renpy.block_rollback()
                jump v12mmasecureposition1

            "{image=icon_wits.webp}Secure the position" if ian_wits > 6 or jiujitsu == 5:
                $ renpy.block_rollback()
                label v12mmasecureposition1:
                    if persistent.include_disabled:
                        $ config.menu_include_disabled = True
                    $ greyed_out_disabled = False
                hide phase_initiative with fps3
                "I couldn't let him escape. I put my weight on his chest, trying to pin him down."
                "However, he was strong and still fresh, and he fought the position ferociously."
                if jiujitsu > 3:
                    $ v12_rival_tired += 1
                    with hpunch
                    pause 0.6
                    "He wasn't able to get rid of me, though. Wen had trained me well."
                    label v12mmagroundtop2:
                        $ v12_sub_count += 1
                        $ v12_position = "armbar"
                    "I took my time searching for an opening, applying pressure on his neck with my forearm, and when he tried to defend it..."
                    play sound "sfx/slap.mp3"
                    scene v12_mma_sub2 with hpunch
                    jump v12mmaarmbar1
                elif jiujitsu > 2 and ian_athletics > 6:
                    with hpunch
                    pause 0.6
                    $ v12_ian_tired += 1
                    "I matched his ferocity, scrambling and pinning him down against the mat."
                    "It was a struggle, but I was able to maintain the position, at least for now."
                    jump v12mmagroundtop2
                elif jiujitsu < 2:
                    label v12mmagroundtime1:
                        play sound "sfx/punch.mp3"
                        scene v12_mma_ground_bottom with hpunch
                    $ v12_ian_tired += 1
                    $ v12_rival_score += 1
                    "He suddenly exploded and twisted his hips, escaping and turning the position around."
                    "I hadn't been expecting that!"
                    "I tried to get him off me, but he was still fresh and full of energy..."
                    $ v12_position = "groundbottom"
                else:
                    "He was able to push me off just enough to hip-escape and get back to his feet."
                    $ v12_position = "standing"

            "Rest":
                $ renpy.block_rollback()
                if v12_ian_tired > 0 and jiujitsu > 2:
                    $ v12_ian_tired -= 1
                if v12_ian_tired > 0:
                    $ v12_ian_tired -= 1 
                "I used this chance to take a small breather, getting some stamina back."
                if (jiujitsu == 5 and v12_ian_tired < 3 and v12_grappling_count < 10) or (jiujitsu > 3 and v12_ian_tired < 2 and v12_grappling_count < 10):
                    with hpunch
                    "He tried to escape the position, but I maintained control."
                    "After a while, and seeing there wasn't much action going on, the referee decided to stand us up."
                    scene v12_mma_base_bg at mmabg
                    show v12_mma_base_rival at mmarival
                    show v12_mma_base_ian at mmaian
                    with short
                    pause 0.5
                    $ v12_position = "standing"
                else:
                    play sound "sfx/punch.mp3"
                    scene v12_mma_ground_bottom with hpunch
                    $ v12_rival_score += 1
                    "He suddenly exploded and twisted his hips, escaping and turning the position around."
                    $ v12_position = "groundbottom"
                    
    
        
    jump v12_hub_exit
####################################################################################################################################################
## ROUND 4 #########################################################################################################################################
label v12mmafight_rd4:
    
    $ renpy.block_rollback()
    if ian_wits < 7:
        $ timeout = 3.5
    elif ian_wits > 6:
        $ timeout = 3.0
    else:
        $ timeout = 2.5
    $ timeout_label = "v12_hit_hub"
    $ v12_round = 4

    if v12_position == "standing":
        scene v12_mma_base_bg at mmabganim
        show v12_mma_base_rival at mmarivalanim 
        show v12_mma_base_ian at mmaiananim 
        with short
        # vengeance
        if v12_ian_score >= v12_rival_score + 3 and v12_rival_vengeance == 0:
            jump v12mmavengeance1
        show phase_initiative
        with short
        $ renpy.block_rollback()
        menu:
            "{image=icon_attack.webp}Attack!":
                $ renpy.block_rollback()
                hide phase_initiative with fps3
                jump v12mmaattack1

            "{image=icon_defend.webp}Guard up":
                $ renpy.block_rollback()
                hide phase_initiative with fps3
                jump v12mmaguard1                

            "{image=icon_provoke.webp}Provoke":
                $ renpy.block_rollback()
                jump v12mmaprovoke

            "{image=icon_tap.webp}Tap out!":
                $ renpy.block_rollback()
                jump v12mmatapout
            ## RD 4 IAN HIT
                
# CLINCH                   
    if v12_position == "clinch":
        jump v12rd2grapple
# ARMBAR
    if v12_position == "armbar":
        label v12mmaarmbar1:
            $ v12_rival_tired += 1
            $ v12_ian_score += 1
        "I locked my knees and tried to extend his arm, but he held on."
        "He was using all his strength to resist. He knew I was close to finishing this fight..."
        if jiujitsu > 3 and ian_athletics > 6 or jiujitsu == 5:
            with vpunch
            "I drove my hips up, overextending his elbow. He should've tapped by now, but he was either very stubborn or very flexible."
            $ v12_rival_tired += 1
        play sound "sfx/fall.mp3"
        scene v12_mma_ground_bottom with hpunch
        "He managed to turn his wrist and drive his elbow in, escaping from danger, and in turning his hips he used it as a fulcrum and turned the position over."
        "I almost had him!"
        $ v12_position = "groundbottom"
        jump v12mmabottom1            
            
# GROUND TOP
    if v12_position == "groundtop":
        jump v12mmagroundtop1

# GROUND BOTTOM  
    if v12_position == "groundbottom":
        label v12mmabottom1:
            $ timeout_label = "v12mmagrbottomtime"
        menu:
            "{image=icon_athletics.webp}Go for a choke!" if v12_sub_count < 2 and jiujitsu > 2:
                $ renpy.block_rollback()
                if v12_position == "armbar":
                    "Maybe he thought he was off the hook, but I still had more for him."
                scene v12_mma_sub1 with short
                "He exposed his neck, and I wrapped a guillotine choke around it."
                "I began to squeeze as hard as I could, trying to suffocate him, but he wasn't tapping out."
                "That guy was determined to resist!"
                $ v12_position = "guillotine"
                $ v12_sub_count += 1
                if jiujitsu < 4:
                    $ v12_ian_tired -= 1
                jump v12mmaguillotine1

            "{image=icon_counter.webp}Reverse the position":
                $ renpy.block_rollback()
                "I tried to get back in top position and keep controlling the fight."
                if jiujitsu > 3 and v12_takedown_count < 2:
                    play sound "sfx/fall.mp3"
                    scene v12_mma_ground_top with vpunch
                    $ v12_ian_score += 1
                    "I reversed the position with a smooth and well-timed sweep that Wen had me practice hundreds of times."
                    if v12_sub_count > 0:
                        if v12_rival_tired < 3:
                            $ v12_rival_tired += 1
                        "However, my opponent used the brief moment my weight wasn't on him to push me off and roll backward, getting back up to his feet."
                        play sound "sfx/punch.mp3"
                        scene v12_mma_base_bg at mmabg
                        show v12_mma_base_rival at mmarival
                        with hpunch
                        show v12_mma_base_ian at mmaian with short
                        "It was clear he didn't want to risk getting caught in my ground game again..."
                        "Frustrated, he came forward with ill-intention in his eyes."
                        if v12_rival_tired < 4:
                            $ v12_rival_tired += 1
                        if kickboxing > 2:
                            play sound "sfx/miss.mp3"
                            scene v12_mma_jab_bg at mmaleftbg
                            show v12_mma_jab_dodge at mmaleftdodge
                            show v12_mma_jab_dodge_rival at mmaleft
                            with fps
                        else:
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_jab_bg at mmaleftbg
                            show v12_mma_jab_damage at mmaleft
                            with hpunch
                            $ v12_rival_score += 1
                        pause 0.6
                        if kickboxing > 3:
                            play sound "sfx/miss.mp3"
                            scene v12_mma_right_bg at mmaleftbg
                            show v12_mma_right_dodge_rival at mmaleft
                            show v12_mma_right_dodge at mmaleftdodge
                            with fps
                        else:
                            play sound "sfx/punch.mp3"
                            scene v12_mma_right_bg at mmaleftbg
                            show v12_mma_right_damage at mmaleftpower
                            with vpunch
                            $ v12_rival_score += 1
                            $ v12_ian_hurt += 1
                        pause 0.6
                        if v12_ian_score >= v12_rival_score:
                            play sound "sfx/punch.mp3"
                            scene v12_mma_takedown_bg at mmaleftbg
                            show v12_mma_lowkick_damage at mmaleft
                            with vpunch
                            pause 0.6
                            $ v12_rival_score += 1
                        if v12_ian_tired == 0 and v12_ian_hurt < 2:
                            play sound "sfx/strongpunch.mp3"
                            scene v12_mma_kick_bg at mmarightbg
                            show v12_mma_kick_damage at mmaup
                            with hpunch
                            pause 0.6
                            i "Agh!!"
                            $ v12_rival_score += 1
                            $ v12_ian_tired += 1
                        $ v12_position = "standing"
                    else:
                        $ v12_position = "groundtop"
                        jump v12mmagroundtop1
                else:
                    $ v12_ian_tired += 1
                    with hpunch
                    "As much as I tried, I wasn't able to get him off me. He knew how to press all his weight down on me, suffocating me."
                    if (jiujitsu > 3 and v12_ian_tired < 3) or (ian_athletics > 6 and v12_ian_tired < 4):
                        "After struggling against him and using more strength than technique, I pushed him off me, sliding my hips to escape back to my feet."
                        jump v12mmastandup1
                    if v12_ian_tired > 1 and v12_rival_sub == 0:
                        "I tried to get on all fours to get up, but..."
                        play sound "sfx/slap.mp3"
                        scene v12_mma_sub_damage with vpunch
                        i "...!"
                        $ v12_position = "rnc"
                        $ v12_rival_score += 1
                        jump v12mmarnc

            "{image=icon_defend.webp}Tie him up":
                $ renpy.block_rollback()
                if jiujitsu < 3 or jiujitsu < 4 and ian_athletics < 6:
                    $ v12_ian_tired += 1
                "I held onto him, trying to slow down the fight and smother any offense he could mount up."
                if jiujitsu > 2 or ian_athletics > 5 or v12_rival_sub > 0:
                    if v12_round < 6:
                        $ v12_round += 1
                    with hpunch
                    pause 0.6
                    with hpunch
                    pause 0.6
                    "Seeing as he wasn't able to get anything going on the ground, my opponent decided to stand back up."
                    scene v12_mma_base_bg at mmabg
                    show v12_mma_base_rival at mmarival
                    with short
                    show v12_mma_base_ian at mmaian with short
                    pause 0.5
                    if v12_ian_score >= v12_rival_score:
                        play sound "sfx/punch.mp3"
                        scene v12_mma_takedown_bg at mmaleftbg
                        show v12_mma_lowkick_damage at mmaleft
                        with vpunch
                        "He welcomed me up with a solid leg kick I wasn't quick enough to avoid."
                        $ v12_rival_score += 1
                    $ v12_position = "standing"
                else:
                    label v12mmagrbottomtime:
                        "I wasn't able to prevent him from passing guard. He managed to get on my back and..."
                        play sound "sfx/slap.mp3"
                        scene v12_mma_sub_damage with vpunch
                        i "...!"
                        $ v12_position = "rnc"
                        $ v12_rival_score += 1
        
            "Get back to your feet":
                $ renpy.block_rollback()
                if ian_athletics > 6 and jiujitsu < 4:
                    "Using more strength than technique, I pushed him off me, sliding my hips to escape back to my feet."
                else:
                    "I pushed him off me, sliding my hips to escape back to my feet."
                if jiujitsu < 4:
                    $ v12_ian_tired += 1
                label v12mmastandup1:
                    if jiujitsu > 2 or ian_athletics > 6 or v12_rival_sub > 0:
                        play sound "sfx/punchgym.mp3"
                        scene v12_mma_base_bg at mmabg
                        show v12_mma_base_rival at mmarival
                        show v12_mma_base_ian at mmaian 
                        with vpunch
                        pause 0.6
                        if v12_ian_score >= v12_rival_score:
                            play sound "sfx/punch.mp3"
                            scene v12_mma_takedown_bg at mmaleftbg
                            show v12_mma_lowkick_damage at mmaleft
                            with vpunch
                            if v12_ian_score > v12_rival_score:
                                pause 0.5
                                play sound "sfx/punch.mp3"
                                scene v12_mma_jab_bg at mmaleftbg
                                show v12_mma_jab_damage at mmaleft
                                with hpunch
                                $ v12_rival_score += 1
                            "I was hit by a revenge strike before I could reset my guard, but I least I was standing again."
                            $ v12_rival_score += 1
                        elif kickboxing > 3:
                            play sound "sfx/punch.mp3"
                            scene v12_mma_lowkick_bg at mmarightbg
                            show v12_mma_lowkick_hit at mmalk
                            with hpunch
                            "My opponent stood up too, and I met him with a solid leg kick."
                            $ v12_ian_score += 1
                        $ v12_position = "standing"
                    else:
                        with hpunch
                        "My opponent wasn't gonna let me off the hook so easily. He controlled the position, making me fight to escape."
                        "I tried to get on all fours to get up, but..."
                        play sound "sfx/slap.mp3"
                        scene v12_mma_sub_damage with vpunch
                        i "...!"
                        $ v12_position = "rnc"
                        $ v12_rival_score += 1

    # GUILLOTINE
    if v12_position == "guillotine":
        show phase_initiative
        with short
        $ renpy.block_rollback()
        label v12mmaguillotine1:
            $ timeout_label = "v12rd4_hit_guillotine"
            if v12_rival_tired < 3:
                $ v12_rival_tired += 1
        menu:
            "{image=icon_athletics.webp}Squeeze!":
                $ renpy.block_rollback()
                hide phase_initiative with fps3
                $ v12_ian_tired += 1
                "I squeezed down with all my strength, trying to finish the submission. I almost had him...!"
                with hpunch
                if jiujitsu == 5 or ian_athletics > 5 and jiujitsu > 3 or ian_athletics > 7:
                    if v12_rival_tired < 4:
                        $ v12_rival_tired += 1
                    "I could feel his resistance giving way, but he held on, refusing to quit. I continued to squeeze his neck with all I had..." 
                    if v12_round == 6:
                        jump v12_hub_exit
                    "But in the end, I realized I would burn my arms out before I could make him tap, so I ended up letting go of the choke."
                else:
                    "But he wasn't tapping. He was resisting with all his might, and no matter how much effort I put into it, it didn't seem like he wanted to tap."
                    if ian_athletics < 6:
                        $ v12_ian_tired += 1
                    if v12_round == 6:
                        jump v12_hub_exit
                    "My arms started to get tired, and in the end, I was forced to let go."

            "Let go of the choke":
                $ renpy.block_rollback()
                hide phase_initiative with fps3
                label v12rd4_hit_guillotine:
                    "My opponent was resisting with all his might. I realized I would burn my arms out before I could make him tap, so I let go of the choke."
        play sound "sfx/fall.mp3"
        scene v12_mma_ground_bottom with hpunch
        "I found myself with my opponent now on top, eager to get some payback."
        $ v12_position = "groundbottom"
    
    jump v12_hub_exit
        
####################################################################################################################################################
## ROUND 5 #########################################################################################################################################
label v12mmafight_rd5:
    $ renpy.block_rollback()
    if ian_wits < 7:
        $ timeout = 3.5
    elif ian_wits > 6:
        $ timeout = 3.0
    else:
        $ timeout = 2.5
    $ timeout_label = "v12_hit_hub"
    $ v12_round = 5
    
    if v12_position == "standing":
        scene v12_mma_base_bg at mmabganim
        show v12_mma_base_rival at mmarivalanim 
        show v12_mma_base_ian at mmaiananim 
        with short
        # vengeance
        if v12_ian_score >= v12_rival_score + 3 and v12_rival_vengeance == 0:
            jump v12mmavengeance1
        show phase_initiative
        with short
        $ renpy.block_rollback()
        menu:
            "{image=icon_attack.webp}Attack!":
                $ renpy.block_rollback()
                hide phase_initiative 
                show phase_attack
                with short
                jump v12mmaattack1

            "{image=icon_defend.webp}Guard up":
                $ renpy.block_rollback()
                hide phase_initiative with fps3
                jump v12mmaguard1

            "{image=icon_provoke.webp}Provoke":
                $ renpy.block_rollback()
                jump v12mmaprovoke            

            "{image=icon_tap.webp}Tap out!":
                $ renpy.block_rollback()
                jump v12mmatapout
            ## RD 5 IAN HIT
                
    # CLINCH                   
    if v12_position == "clinch":
        jump v12rd2grapple
    # GROUND TOP
    if v12_position == "groundtop":
        jump v12mmagroundtop1
    # BOTTOM
    if v12_position == "groundbottom":
        jump v12mmabottom1
    # GUILLOTINE
    if v12_position == "guillotine":
        jump v12mmaguillotine1
    # ARMBAR
    if v12_position == "armbar":
        jump v12mmaarmbar1
    # RNC
    if v12_position == "rnc":
        label v12mmarnc:
            $ v12_rival_sub += 1
            "I felt his arms trapping and squeezing down on my neck. I couldn't breathe!"
            "I dug my fingers into his forearms, trying desperately to break his hold. My head felt like it was about to burst."
            $ timeout = 2.5
            $ timeout_label = "v12_rnc_tap"
            menu:
                "{image=icon_athletics.webp}Resist!":
                    $ renpy.block_rollback()
                    "I held on, not wanting to give up. I could still fight...!"
                    if (jiujitsu > 3 and v12_ian_tired < 4) or (jiujitsu > 4 and v12_ian_tired < 5) or (ian_athletics > 5 and v12_ian_tired < 3) or (ian_athletics > 6 and v12_ian_tired < 4):
                        label v12rncescape1:
                            $ v12_ian_tired += 1
                        play sound "sfx/fall.mp3"
                        scene v12_mma_ground_top with hpunch
                        "Finally, I managed to create enough space for me to twist my hips and reverse the position with an explosive movement."
                        "That had been too close...!"
                        if jiujitsu == 5:
                            "I took a deep breath, trying to clear my head."
                            play sound "sfx/punchgym.mp3"
                            with hpunch
                            "My opponent tried pushing me off, but I held onto him. He wouldn't get away so easily."
                            $ v12_position = "groundtop"
                            jump v12mmagroundtop1
                        else:
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_base_bg at mmabganim
                            show v12_mma_base_rival at mmarivalanim 
                            show v12_mma_base_ian at mmaiananim 
                            with vpunch
                            "My opponent pushed me off with his legs, getting back to his feet."
                            "I took a deep breath, trying to clear my head."
                            $ v12_position = "standing"
                            jump v12_action_menu
                    elif jiujitsu > 2 and v12_ian_tired < 3:
                        scene v12_mma_ground_bottom with short
                        "He gave up the choke, and I could finally take a deep breath, trying to clear my head."
                        "That had been too close...!"
                        "I didn't have the energy to get him off me right away, but I needed to get out of this position somehow."
                        $ v12_position = "groundbottom"
                    else:
                        i "...!" with hpunch
                        if v12_rival_sub < 3:
                            menu:
                                "{image=icon_will.webp}Resist!" if ian_will > 0:
                                    $ renpy.block_rollback()
                                    call willdown from _call_willdown_63
                                    i "Ughhh!!!" with hpunch
                                    jump v12rncescape1
                            
                                "{image=icon_tap.webp}Tap out!":
                                    $ renpy.block_rollback()
                        jump v12_rnc_tap

                "{image=icon_tap.webp}Tap out!":
                    $ renpy.block_rollback()
                    label v12_rnc_tap:
                        $ tournament = "losesub"
                    "I could feel consciousness slipping away from me. There was no getting out of this."
                    "I did the only thing I could: tap out."
                    stop music fadeout 2.0
                    scene gym with long
                    "The referee jumped in to rescue me, signaling the end of the fight, and my defeat."
                    jump v12mmafightend

    jump v12_hub_exit
####################################################################################################################################################
## ROUND 6 #########################################################################################################################################
label v12mmafight_rd6:
    $ renpy.block_rollback()
    if ian_wits < 7:
        $ timeout = 3.5
    elif ian_wits > 6:
        $ timeout = 3.0
    else:
        $ timeout = 2.5
    $ timeout_label = "v12_hit_hub"
    $ v12_round = 6
    
    if v12_position == "standing":
        scene v12_mma_base_bg at mmabganim
        show v12_mma_base_rival at mmarivalanim 
        show v12_mma_base_ian at mmaiananim 
        with short
    # vengeance
        if v12_ian_score >= v12_rival_score + 3 and v12_rival_vengeance == 0:
            label v12mmavengeance1:
                if v12_round < 7:
                    $ v12_rival_vengeance = 1
            "My opponent was feeling pressured, and he suddenly lashed out to try and steal the momentum of the fight."
            if v12_ian_score >= v12_rival_score + 3:
                if kickboxing > 3 and v12_ian_tired < 3:
                    play sound "sfx/miss.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_dodge at mmaleftdodge
                    show v12_mma_jab_dodge_rival at mmaleft
                    with fps3
                else:
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_damage at mmaleft
                    with hpunch
                    $ v12_rival_score += 1
                pause 0.5
                play sound "sfx/slap2.mp3"
                scene v12_mma_headkick_bg at mmaleftbg
                show v12_mma_headkick_block at mmaup
                with vpunch
                pause 0.7
                if kickboxing > 4 and v12_ian_tired < 3:
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_right_bg at mmaleftbg
                    show v12_mma_right_block at mmaleftpower
                    with vpunch
                else:
                    play sound "sfx/strongpunch.mp3"
                    scene v12_mma_right_bg at mmaleftbg
                    show v12_mma_right_damage at mmaleftpower
                    with vpunch
                    $ v12_rival_score += 1
                    if v12_ian_hurt < 2:
                        $ v12_ian_hurt += 1
                pause 0.6
            elif v12_ian_score >= v12_rival_score + 2:
                if kickboxing > 3 and v12_ian_tired < 3:
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_block at mmaleft
                    with vpunch
                else:
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_damage at mmaleft
                    with vpunch
                    $ v12_rival_score += 1
                pause 0.5
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_kick_bg at mmaleftbg
            show v12_mma_kick_damage at mmaleft
            with hpunch
            i "Umpf!!{w=0.6}{nw}"
            $ v12_rival_score += 1
            if v12_ian_tired < 2 and ian_athletics < 6 or v12_ian_tired == 0:
                $ v12_ian_tired += 1
            if v12_ian_score >= v12_rival_score + 1:
                play sound "sfx/punch.mp3"
                scene v12_mma_takedown_bg at mmaleftbg
                show v12_mma_lowkick_damage at mmaleft
                with vpunch
                $ v12_rival_score += 1
                pause 0.6
            "He jumped back after delivering that nasty combo."
            if v12_round > 6:
                jump v12_hub_exit
            else:
                scene v12_mma_base_bg at mmabganim
                show v12_mma_base_rival at mmarivalanim 
                show v12_mma_base_ian at mmaiananim 
                with short
            
        show phase_initiative
        with short
        $ renpy.block_rollback()
        menu:
            "{image=icon_attack.webp}Attack!":
                $ renpy.block_rollback()
                hide phase_initiative 
                show phase_attack
                with short
                jump v12mmaattack1

            "{image=icon_defend.webp}Guard up":
                $ renpy.block_rollback()
                hide phase_initiative with fps3
                jump v12mmaguard1

            "{image=icon_provoke.webp}Provoke":
                $ renpy.block_rollback()
                jump v12mmaprovoke            

            "{image=icon_tap.webp}Tap out!":
                $ renpy.block_rollback()
                jump v12mmatapout
            ## RD 6 IAN HIT
                

    # CLINCH                   
    if v12_position == "clinch":
        jump v12rd2grapple
    # GROUND TOP
    if v12_position == "groundtop":
        jump v12mmagroundtop1
    # BOTTOM
    if v12_position == "groundbottom":
        jump v12mmabottom1
    # GUILLOTINE
    if v12_position == "guillotine":
        jump v12mmaguillotine1
    # ARMBAR
    if v12_position == "armbar":
        jump v12mmaarmbar1
    # RNC
    if v12_position == "rnc":
        jump v12mmarnc

####################################################################################################################################################
## BREAK ROUND #########################################################################################################################################

label v12mmafight_break:
    if v12_position == "standing":
        scene v12_mma_base_bg at mmabg
        show v12_mma_base_rival at mmarival 
        show v12_mma_base_ian at mmaian 
    $ v12_rival_vengeance = 0
    stop music 
    hide screen mma_clock
    show v12_mma_clock_b0
    show v12_mma_clock_0
    ref "Time!" with vpunch
    ref "Round's over. Get back to your corners."
    scene gym with long
    "The first round came to a close before I could realize..."
    if v12_ian_hurt > 1 or v12_ian_hurt > 9 or v12_ian_tired > 2:
        $ fian = "insecure"
    elif v12_ian_hurt > 0 or v12_ian_tired > 1:
        $ fian = "worried"
    else:
        $ fian = "n"
    show wen2 with short
    show ian at left with short
    show ian at lef3 with move
    if v12_position == "armbar":
        i "Fuck, I almost had him."
        wen "He was saved by the bell... How are you holding up?"
    elif v12_position == "guillotine":
        i "I almost had him..."
        wen "That was a tricky submission to pull off. How are you holding up?"
    elif v12_position == "rnc":
        wen "You were saved by the bell! How are you holding up?"
    else:
        wen "Come here. How are you holding up?"
    if v12_ian_hurt > 9:
        wen "Well done getting up after getting nailed like that. I thought you were out!"
        i "I was, kind of... Somehow I managed to stay in there."
    elif v12_ian_hurt > 1:
        wen "You got nailed with some nasty shots back there..."
        i "Yeah... My head's still a bit foggy."
    elif v12_ian_hurt > 0:
        wen "You got hit with a good one. Are you okay?"
        i "Yeah, my head is ringing a bit, but that's it."
    else:
        i "I'm fine. I didn't get hit by anything significant..."
    if v12_ian_tired > 2:
        "I huffed and puffed, trying to catch my breath back."
        wen "Don't talk. Use this minute to recover as much as you can. Deep breaths..."
    elif v12_ian_tired > 1:
        wen "How's your cardio holding up?"
        i "He's pushing the pace..."
        wen "Try to relax and use this minute to recover as much as you can. Deep breaths..."
    elif v12_ian_tired > 0:
        wen "Use this minute to catch your breath and recover as much as you can."
        if v12_ian_hurt < 2:
            i "I still have gas in the tank."
        else:
            i "Okay..."
    else:
        wen "Use this minute to catch your breath."
        if v12_ian_hurt < 2:
            i "It's alright. I'm not even breathing heavily."
        else:
            i "Okay..."
    if v12_tapout_count > 1:
        wen "You're a bit nervous... Are you sure you want to keep going?"
        "I nodded, but I wasn't as convinced as I was trying to look."
        wen "Come on, you're halfway there. Don't give up on yourself. Focus."
    elif v12_tapout_count == 1:
        wen "You got a bit nervous back there... As I said, don't give up on yourself. Focus."
    $ fian = "n"
    if v12_ian_score > v12_rival_score:
        # 5 or more points difference
        if v12_ian_score >= v12_rival_score + 5 and v12_ian_hurt == 0:
            i "So... How am I doing?"
            hide wen2
            show wen2smile at lef
            wen "Excellent! You're wiping the floor with him!"
            hide wen2smile
            show wen2 at lef
        # 3 or more points difference
        elif v12_ian_score >= v12_rival_score + 3 and v12_ian_hurt < 2:
            wen "Alright, you're dominating this fight so far. Well done!"
        else:
            wen "Alright, I think you're ahead on the scorecards, but you can never be too sure."
    if v12_rival_score > v12_ian_score:
        # 4 or more points difference
        if v12_rival_score >= v12_ian_score + 4 or v12_ian_hurt > 1:
            wen "Alright, he's lighting you up right now. You need to do things differently if you want to have a chance at winning this."
        # 2 or more points difference
        elif v12_rival_score >= v12_ian_score + 2:
            wen "Alright, listen closely. It's obvious you're behind on the scorecards, but you can still turn this around."
        else:
            wen "Alright, listen closely. You're behind on the scorecards, but not by much."
    if v12_rival_score == v12_ian_score:
        if v12_ian_hurt > v12_rival_hurt:
            wen "Alright, listen closely. You've taken more damage, but I think you're both evenly matched."
        else:
            wen "Alright, listen closely. You're both evenly matched right now."
    if v12_takedown_count > 0:
        wen "You did well taking him down! I can see he favors striking over grappling, but be on your guard."
        wen "He knows you can take him down, so he'll be looking for that. It won't be so easy next time."
        if v12_rival_tired > 0:
            wen "He's starting to get tired, though. If you manage to get him down and put pressure on him, you might be able to submit him."
        else:
            wen "He's still fresh, so be careful if you decide to go for a submission. He will surely try to resist."
    elif v12_grappling_count > 0:
        wen "You've got the upper hand in the clinch. I can see he favors striking over grappling, but he's no pushover."
        wen "He knows you want to close the distance with him, so be on your guard."
    if v12_rival_hurt > 0:
        if v12_rival_hurt > 1:
            wen "You nailed him with some good shots! But don't get sloppy just because you're able to hit him."
        else:
            wen "You nailed him with a good shot! But don't get sloppy just because you were able to hit him."
        wen "And be careful. I could see he moves very well on his feet, and he's looking for counters."
    else:
        wen "Be careful with the striking. I could see he moves very well on his feet, and he's looking for counters."
    if v12_rival_sub > 0:
        wen "He's dangerous on the mat, too. He'll go for a submission if you let him, so don't let him control you on the ground!"
    i "Got it."
    if v12_ian_score >= v12_rival_score:
        wen "Time's almost over. Get ready."
        wen "And be prepared! He'll come out guns blazing, no doubt."
    else:
        wen "Time's almost over. Get ready."
        wen "You need to put your gas on the pedal, but don't be reckless. He'll be waiting for you to make a mistake."
    wen "Now go. You've got this!"
    hide wen2 with short

# recover
    if v12_ian_hurt > 9:
        $ v12_ian_hurt = 1
    elif v12_ian_hurt > 1 and ian_athletics > 6:
        $ v12_ian_hurt -= 1
    elif v12_ian_hurt > 0 and ian_athletics > 5:
        $ v12_ian_hurt -= 1

    if v12_ian_tired > 0 and ian_athletics > 5:
        $ v12_ian_tired -= 1
    
    if v12_ian_tired > 1 and ian_athletics > 6:
        $ v12_ian_tired -= 1
    
    if v12_rival_tired > 4:
        $ v12_rival_tired -= 1
    if v12_rival_tired > 1:
        $ v12_rival_tired -= 1
    if v12_rival_hurt > 3:
        $ v12_rival_hurt -= 1
    if v12_rival_hurt > 1:
        $ v12_rival_hurt -= 1

    #stop music fadeout 3.0
    $ fian = "serious"
    show ian at lef with move
    show fighter at rig with short
    ref "Fighters, are you ready?"
    scene v12_mma_base_bg at mmabg
    show v12_mma_base_rival at mmarival 
    show v12_mma_base_ian at mmaian 
    with long
    ref "Second and final round. Fight!"
    $ v12_position = "standing"
    play music "music/fight.mp3" loop
 
####################################################################################################################################################
## ROUND 7 #########################################################################################################################################
label v12mmafight_rd7:
    $ renpy.block_rollback()
    show screen mma_clock()
    if ian_wits < 7:
        $ timeout = 3.5
    elif ian_wits > 6:
        $ timeout = 3.0
    else:
        $ timeout = 2.5
    $ timeout_label = "v12_hit_hub"
    $ v12_round = 7
    
    scene v12_mma_base_bg at mmabganim
    show v12_mma_base_rival at mmarivalanim 
    show v12_mma_base_ian at mmaiananim 
    with short
    pause 1

    # rival attack -done
    if v12_ian_score > v12_rival_score:
        label v12mmarivalvengeance:
            $ v12_rival_vengeance = 1 
        if v12_round == 7:
            "Just as Wen said, my opponent came out aggressive, trying to make a statement from the get-go."
        else:
            "My opponent launched an all-out offensive, trying to make a statement."
        if (kickboxing > 3 and v12_ian_tired < 2) or (kickboxing > 4 and v12_ian_tired < 3) or v12_rival_tired > 1:
            play sound "sfx/miss.mp3"
            scene v12_mma_jab_bg at mmaleftbg
            show v12_mma_jab_dodge at mmaleftdodge
            show v12_mma_jab_dodge_rival at mmaleft
            with fps3
        else:
            play sound "sfx/punchgym.mp3"
            scene v12_mma_jab_bg at mmaleftbg
            show v12_mma_jab_damage at mmaleft
            with hpunch
            $ v12_rival_score += 1
        pause 0.4
        play sound "sfx/punch.mp3"
        scene v12_mma_takedown_bg at mmaleftbg
        show v12_mma_lowkick_damage at mmaleft
        with vpunch
        $ v12_rival_score += 1
        
        if ian_wits < 6 or kickboxing > 3:
            "I saw him cocking his right fist back and..."
        $ renpy.block_rollback()
        $ timeout_label = "v12rd7dodgehit"
        menu:
            "{image=icon_defend.webp}Block!":
                $ renpy.block_rollback()
                play sound "sfx/punchgym.mp3"
                scene v12_mma_right_bg at mmaleftbg
                show v12_mma_right_block at mmaleftpower
                with vpunch
                pause 0.5
                play sound "sfx/slap2.mp3"
                scene v12_mma_headkick_bg at mmaleftbg
                show v12_mma_headkick_block at mmaup
                with vpunch
                pause 0.6
                play sound "sfx/strongpunch.mp3"
                scene v12_mma_kick_bg at mmaleftbg
                show v12_mma_kick_damage at mmaleft
                with hpunch
                i "Umpf!!"
                $ v12_rival_score += 1
                if v12_ian_tired < 2:
                    $ v12_ian_tired += 1

            "{image=icon_defend.webp}Dodge!":
                $ renpy.block_rollback()
                play sound "sfx/miss.mp3"
                scene v12_mma_right_bg at mmaleftbg
                show v12_mma_right_dodge_rival at mmaleft
                show v12_mma_right_dodge at mmaleftdodge
                with fps3
                pause 0.5
                if kickboxing > 3:
                    menu:
                        "{image=icon_defend.webp}Block!":
                            $ renpy.block_rollback()
                            play sound "sfx/slap2.mp3"
                            scene v12_mma_headkick_bg at mmaleftbg
                            show v12_mma_headkick_block at mmaup
                            with vpunch
                            pause 0.5
                            $ v12_position = "guard"

                        "{image=icon_defend.webp}Dodge!":
                            $ renpy.block_rollback()
                            jump v12rd7dodgehit

                        "{image=icon_counter.webp}Counter!" if kickboxing > 3 and v12_ian_tired < 2 or kickboxing > 4:
                            $ renpy.block_rollback()
                            jump v12rd7counter

                else:
                    label v12rd7dodgehit:
                        play sound "sfx/strongpunch.mp3"
                        scene v12_mma_takedown_fail with vpunch
                    i "Umpf!!"
                    $ v12_rival_score += 1
                    $ v12_ian_hurt += 1
                    if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                        jump v12mmakoed

            "{image=icon_counter.webp}Counter!":
                $ renpy.block_rollback()
                if kickboxing > 4 and v12_ian_tired < 2 or kickboxing == 6 and v12_ian_tired < 3:
                    label v12rd7counter:
                        play sound "sfx/strongpunch.mp3"
                        scene v12_mma_right_bg at mmaleftbg
                        show v12_mma_counter at mmaleftpower
                        with vpunch
                    fg "Ugh!!"
                    $ v12_ian_score += 1
                    $ v12_rival_hurt += 1
                    play sound "sfx/slap2.mp3"
                    scene v12_mma_headkick_bg at mmaleftbg
                    show v12_mma_headkick_block at mmaup
                    with vpunch
                    pause 0.5
                else:
                    play sound "sfx/strongpunch.mp3"
                    scene v12_mma_body_bg at mmaleftbg
                    show v12_mma_counter_damage at mmaleftcounter
                    with hpunch
                    i "Umpf!!"
                    "I tried countering him, but he was faster."
                    $ v12_rival_score += 1
                    if v12_ian_hurt < 2:
                        $ v12_ian_hurt += 1
                    if kickboxing > 4 and v12_ian_tired < 5 or kickboxing > 3 and v12_ian_tired < 4:
                        play sound "sfx/slap2.mp3"
                        scene v12_mma_headkick_bg at mmaleftbg
                        show v12_mma_headkick_block at mmaup
                        with vpunch
                        pause 0.5
                    else:
                        play sound "sfx/slap2.mp3"
                        scene v12_mma_headkick_bg at mmaleftbg
                        show v12_mma_headkick_damage at mmaup
                        with vpunch
                        pause 0.5
                        $ v12_ian_hurt += 1
                        $ v12_rival_score += 1
                        if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                            jump v12mmakoed

        # jab
        if v12_ian_score >= v12_rival_score + 1:
            if (kickboxing > 3 and v12_ian_tired < 2) or (kickboxing > 4 and v12_ian_tired < 3):
                play sound "sfx/punchgym.mp3"
                scene v12_mma_jab_bg at mmaleftbg
                show v12_mma_jab_block at mmaleft
                with hpunch
                pause 0.5
            else:
                play sound "sfx/punch.mp3"
                scene v12_mma_jab_bg at mmaleftbg
                show v12_mma_jab_damage at mmaleft
                with hpunch
                pause 0.5
                $ v12_rival_score += 1
        # right
        if v12_ian_score >= v12_rival_score + 2:
            if (kickboxing > 3 and v12_ian_tired < 2) or (kickboxing > 4 and v12_ian_tired < 3):
                play sound "sfx/punchgym.mp3"
                scene v12_mma_right_bg at mmaleftbg
                show v12_mma_right_miss at mmaleftpower
                with hpunch
                pause 0.5
            else:
                play sound "sfx/strongpunch.mp3"
                scene v12_mma_right_bg at mmaleftbg
                show v12_mma_right_damage at mmaleftpower
                with vpunch
                pause 0.5
                $ v12_rival_score += 1
                if v12_ian_hurt < 2:
                    $ v12_ian_hurt += 1
        if v12_ian_score >= v12_rival_score and v12_ian_tired == 0:
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_kick_bg at mmaleftbg
            show v12_mma_kick_damage at mmaleft
            with hpunch
            $ v12_rival_score += 1
            if ian_athletics < 7 or v12_ian_hurt > 0:
                $ v12_ian_tired += 1
            pause 0.5
        if v12_ian_score >= v12_rival_score:     
            play sound "sfx/punch.mp3"
            scene v12_mma_takedown_bg at mmaleftbg
            show v12_mma_lowkick_damage at mmaleft
            with vpunch
            $ v12_rival_score += 1
            pause 0.5
        if v12_ian_hurt < 1 and v12_position != "guard":
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_damage at mmaleftpower
            with vpunch
            i "...!"
            $ v12_ian_hurt += 1
            $ v12_rival_score += 1
        else:
            play sound "sfx/punch.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_block at mmaleftpower
            with hpunch
        "I took a step back, tightening my guard after taking that ferocious combination."
        scene v12_mma_base_bg at mmabganim
        show v12_mma_base_rival at mmarivalanim 
        show v12_mma_base_ian at mmaiananim 
        with short
        $ v12_position = "standing"
        if v12_rival_hurt > 2:
            "However, I could see my opponent's knees buckle a little bit. I had him hurting."
    elif v12_ian_score < v12_rival_score:
        "My rival studied me from behind his guard. He knew he was leading on the scorecards..."
        "It was up to me to change that."
    show phase_initiative with short
    menu v12_action_menu2:
        "{image=icon_attack.webp}Attack!":
            $ renpy.block_rollback()
            hide phase_initiative
            show phase_attack
            with short   
            menu:
                "Attack":
                    $ renpy.block_rollback()
                    label v12mmaattack2:
                        if v12_rival_takedown == 0 and v12_rival_hurt > 1:
                            play sound "sfx/miss.mp3"
                            scene v12_mma_jab_bg at mmarightbg
                            show v12_mma_jab_miss at mmaright
                            with fps3
                            pause 0.5
                            play sound "sfx/slap.mp3"
                            scene v12_mma_takedown_damage_bg at mmaleftbg
                            show v12_mma_takedown_damage at mmaleft
                            with hpunch
                            i "...!!"
                            $ v12_rival_score += 1
                            play sound "sfx/fall.mp3"
                            scene v12_mma_ground_bottom with vpunch
                            "I suddenly found myself on the mat with my rival on top. He had timed that takedown perfectly..."
                            "I wasn't expecting it at all!"
                            $ v12_position = "groundbottom"
                            $ v12_rival_takedown += 1
                            jump v12groundbottom2

                        $ v12_attack_count += 1
                        if v12_attack_count == 3 and ian_athletics < 6:
                            $ v12_ian_tired += 1
                        elif v12_attack_count == 4 and ian_athletics > 5:
                            $ v12_ian_tired += 1

                        if v12_attack_count == 5:
                            $ v12_rival_tired += 1

                        if v12_attack_count == 6 and ian_athletics < 7:
                            $ v12_ian_tired += 1
                        elif v12_attack_count == 7 and ian_athletics > 6:
                            $ v12_ian_tired += 1
                        if v12_attack_count == 9 and ian_athletics < 7:
                            $ v12_ian_tired += 1
                        elif v12_attack_count == 10 and ian_athletics > 6:
                            $ v12_ian_tired += 1
                    
                    call mma_randomizer(mma_randomizer_strike) from _call_mma_randomizer_3
                    jump v12_hub_exit                             

                "Grapple":
                    $ renpy.block_rollback()
                    label v12mmagrappling2:
                        $ v12_grappling_count += 1
                    if v12_sub_count > 9:
                        $ timeout_label = "v12grappletimeout2"
                        "I moved forward, looking to clinch him and take the fight back to the ground."
                        play sound "sfx/punch.mp3"
                        scene v12_mma_takedown_bg at mmaleftbg
                        show v12_mma_lowkick_damage at mmaleft
                        with hpunch
                        pause 0.5
                        $ v12_rival_score += 1
                        "He saw my intentions and stepped back and hit me with a low kick."
                        scene v12_mma_base_bg at mmabganim
                        show v12_mma_base_rival at mmarivalanim 
                        show v12_mma_base_ian at mmaiananim 
                        with short
                        "That wasn't going to deter me. I kept the pressure and chased him down."
                        menu:
                            "{image=icon_wits.webp}Feint and shoot" if jiujitsu > 3 and v12_ian_tired < 4 or jiujitsu == 5:
                                $ renpy.block_rollback()
                                play sound "sfx/miss.mp3"
                                scene v12_mma_jab_bg at mmarightbg
                                show v12_mma_jab_miss at mmaright
                                with fps3
                                pause 0.6
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_right_bg at mmaleftbg
                                show v12_mma_right_miss at mmaleftpower
                                with vpunch
                                pause 0.5
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with hpunch
                                jump v12mmaclinchattack2

                            "{image=icon_attack.webp}Feint and strike":
                                $ renpy.block_rollback()
                                "I closed the distance and faked a shot, making him react to my feint."
                                # right dodge
                                if (kickboxing > 3 and v12_ian_tired < 3) or (kickboxing > 3 and v12_ian_tired < 4) or kickboxing == 6:
                                    "He showed me the perfect opening for a strike."
                                    play sound "sfx/strongpunch.mp3"
                                    scene v12_mma_right_hit_bg at mmaleftbg
                                    show v12_mma_right_hit at mmarightpower
                                    with vpunch
                                    pause 0.8
                                    $ v12_ian_score += 1
                                    $ v12_rival_hurt += 1
                                    if (kickboxing > 3 and v12_ian_tired < 4 and v12_rival_hurt > 3) or kickboxing == 6 and v12_rival_hurt > 2:
                                        jump v12finishmim2
                                    else:
                                        jump v12_hub_exit
                                else:
                                    if v12_ian_tired > 4:
                                        "However, that had been a feint too."
                                        jump v12rd7sprawlkick
                                    else:
                                        play sound "sfx/miss.mp3"
                                        scene v12_mma_jab_bg at mmarightbg
                                        show v12_mma_jab_miss at mmaright
                                        with fps3
                                        pause 0.6
                                        play sound "sfx/punchgym.mp3"
                                        scene v12_mma_jab_bg at mmaleftbg
                                        show v12_mma_jab_block at mmaleft
                                        with hpunch
                                        pause 0.5
                                        play sound "sfx/punch.mp3"
                                        scene v12_mma_lowkick_bg at mmarightbg
                                        show v12_mma_lowkick_hit at mmalk
                                        with hpunch
                                        pause 0.5
                                        $ v12_rival_score += 1
                                        "However, he managed to defend, punishing me with another kick before escaping to the center of the mat."
                                        jump v12_hub_exit

                            "{image=icon_athletics.webp}Shoot for a takedown":
                                $ renpy.block_rollback()
                                "I wouldn't let him get away...!"
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_takedown_fail with vpunch
                                i "...!!!"
                                $ v12_ian_hurt += 1
                                $ v12_rival_score += 1
                                if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                                    jump v12mmakoed
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with hpunch
                                "Still, I got on the inside and locked my hands around his waist. He wasn't going anywhere."
                                jump v12mmaclinchattack2
                        
                    menu:
                        "{image=icon_wits.webp}Feint and shoot" if kickboxing > 4 and jiujitsu > 3:
                            $ renpy.block_rollback()
                            play sound "sfx/miss.mp3"
                            scene v12_mma_jab_bg at mmarightbg
                            show v12_mma_jab_miss at mmaright
                            with fps3
                            pause 0.6
                            if v12_takedown_count < 2 and v12_rival_takedown == 0 and v12_ian_tired < 4 or v12_grappling_count > 9 and v12_rival_tired > 2:
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with hpunch
                                "I made him raise up his arms with a feint, which cleared the way for me to shoot in deep and get a strong body lock on him."
                                $ v12_position = "clinch"
                                jump v12mmaclinchattack2 
                            elif jiujitsu == 5 and v12_grappling_count > 3 or jiujitsu == 4 and v12_grappling_count > 2 or v12_grappling_count > 1:
                                jump v12rd7sprawlkick
                            else:
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with fps
                                pause 0.4
                                play sound "sfx/slap.mp3"
                                scene v12_mma_takedown_miss with vpunch
                                "He saw through my feint and sprawled, defending the takedown."
                                jump v12rd7sprawl
                            
                        "{image=icon_attack.webp}Attack and shoot" if kickboxing > 3 and jiujitsu > 3 or kickboxing > 4:
                            $ renpy.block_rollback()
                            if v12_takedown_count == 0 and v12_rival_takedown == 0 and v12_ian_tired < 3 or v12_grappling_count > 9 and v12_rival_tired > 3:
                                play sound "sfx/punch.mp3"
                                scene v12_mma_jab_bg at mmarightbg
                                show v12_mma_jab_hit at mmaright
                                with hpunch
                                pause 0.6
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with fps
                                "I connected with a swift jab, which startled him and allowed me to shoot in deep and get a strong body lock on him."
                                $ v12_position = "clinch"
                                $ v12_ian_score += 1
                                jump v12mmaclinchattack2
                            elif jiujitsu == 5 and v12_grappling_count > 3 or jiujitsu == 4 and v12_grappling_count > 2 or v12_grappling_count > 1:
                                jump v12rd7sprawlkick
                            else:
                                play sound "sfx/miss.mp3"
                                scene v12_mma_jab_bg at mmarightbg
                                show v12_mma_jab_miss at mmaright
                                with fps3
                                pause 0.6
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with fps
                                play sound "sfx/slap.mp3"
                                pause 0.4
                                scene v12_mma_takedown_miss with vpunch
                                "He saw my attack coming and sprawled, defending the takedown."
                                label v12rd7sprawl:
                                    "I felt his arm around my neck, trying to get me in a compromised position. I had to do something!"
                                    $ timeout_label = "v12grappletimeout"
                                    menu:
                                        "{image=icon_athletics.webp}Slam him!":
                                            $ renpy.block_rollback()
                                            $ v12_ian_tired += 1
                                            if (jiujitsu == 5 and v12_rival_tired > 2 and v12_ian_tired < 5) or (jiujitsu > 3 and v12_rival_tired > 3 and v12_ian_tired < 4):
                                                play sound "sfx/slap.mp3"
                                                scene v12_mma_takedown_bg at mmaslambg1
                                                show v12_mma_takedown2 at mmaslam1
                                                with hpunch
                                                pause 0.5
                                                fg "...!!{w=0.5}{nw}" 
                                                play sound "sfx/big_throw.mp3"
                                                with flash
                                                scene v12_mma_takedown3 with vpunch
                                                pause 1
                                                "I managed to pick him up and slam him head-first on the mat. I saw a dazed expression on his face."
                                                $ v12_ian_score += 2
                                                $ v12_rival_hurt += 1
                                                if v12_rival_hurt < 2:
                                                    $ v12_rival_hurt += 1
                                                $ timeout_label = "v12lethimup"
                                                menu:
                                                    "{image=icon_wits.webp}Go for a submission!" if jiujitsu > 2:
                                                        $ renpy.block_rollback()
                                                        $ v12_position = "armbar"
                                                        $ v12_sub_count += 1
                                                        jump v12mmaarmbar2

                                                    "Let him up":
                                                        $ renpy.block_rollback()
                                                        label v12lethimup:
                                                            scene v12_mma_base_bg at mmabg
                                                            show v12_mma_base_ian at mmaian
                                                            with short
                                                        "I decided to finish him off on the feet. I wanted to put his lights out with a good punch!"
                                                        show v12_mma_base_rival at mmarival with short
                                                        "He stumbled back to his feet, eyes wide open and his guard up."
                                                        $ v12_position = "standing"
                                                        jump v12_hub_exit
                                            else:
                                                with vpunch
                                                "I tried picking him up, but he was strong and resisted fiercely. I wasn't able to lift him off the ground!"
                                                if v12_ian_tired < 3 and ian_athletics > 5 or v12_ian_tired < 4 and jiujitsu > 3:
                                                    play sound "sfx/punchgym.mp3"
                                                    scene v12_mma_takedown_bg at mmarightbg
                                                    show v12_mma_shoot at mmaright
                                                    with vpunch
                                                    "I managed to escape and get back to the clinch position, where I had a chance to attack..."
                                                    $ v12_position = "clinch"
                                                    jump v12mmaclinchattack2 
                                                else:
                                                    jump v12grappletimeout
                                                        
                                        "Trip him!":
                                            $ renpy.block_rollback()
                                            "I tried to trip him, but he had the dominant position and I was having trouble pulling it off."
                                            play sound "sfx/fall.mp3"
                                            if jiujitsu > 3 and v12_ian_tired < 2 and v12_rival_tired > 2 or jiujitsu == 5 and v12_ian_tired < 3 and v12_rival_tired > 2:
                                                scene v12_mma_ground_top with vpunch
                                                "As we tussled for control, I felt him lose his balance and pushed all my weight onto him, getting him to the mat like I wanted."
                                                $ v12_ian_score += 1
                                                $ v12_position = "groundtop"
                                                jump v12_hub_exit
                                            else:
                                                scene v12_mma_ground_bottom with vpunch
                                                "In the end, I was the one tripping, and my opponent fell on top of me, getting side control."
                                                $ v12_rival_score += 1
                                                $ v12_position = "groundbottom"
                                                jump v12_hub_exit

                                        "Wrestle him!":
                                            $ renpy.block_rollback()
                                            $ v12_ian_tired += 1
                                            with hpunch
                                            "I battled him for position, fighting his hand to get my head free."
                                            if jiujitsu > 3 and v12_ian_tired < 2 and v12_rival_tired > 2 or jiujitsu == 5 and v12_ian_tired < 3 and v12_rival_tired > 2:
                                                "As we pushed against each other, I felt my opponent lose his balance."
                                                play sound "sfx/fall.mp3"
                                                scene v12_mma_ground_top with vpunch
                                                "I pushed all my weight onto him, getting him to the mat like I wanted."
                                                $ v12_ian_score += 1
                                                $ v12_position = "groundtop"
                                                jump v12_hub_exit
                                            elif v12_ian_tired < 3 and ian_athletics > 5:
                                                play sound "sfx/punchgym.mp3"
                                                scene v12_mma_takedown_bg at mmarightbg
                                                show v12_mma_shoot at mmaright
                                                with fps
                                                "I managed to escape and get back to the clinch position, where I had a chance to attack..."
                                                $ v12_position = "clinch"
                                                jump v12mmaclinchattack2 
                                            else:
                                                label v12grappletimeout:
                                                    play sound "sfx/fall.mp3"
                                                    scene v12_mma_ground_bottom with vpunch
                                                "I ended up losing my balance and my opponent used this chance to trip me and get side control."
                                                $ v12_rival_score += 1
                                                $ v12_position = "groundbottom"
                                                jump v12_hub_exit

                        "{image=icon_athletics.webp}Shoot directly":
                            $ renpy.block_rollback()
                            if jiujitsu < 4 and v12_round > 1 or jiujitsu < 5 and v12_round > 4:
                                $ v12_ian_tired += 1
                            "I exploded on my feet and shot for a takedown right away."
                            if v12_takedown_count == 0 and v12_rival_takedown == 0 and jiujitsu > 3 and v12_ian_tired < 3:
                                play sound "sfx/punchgym.mp3"
                                scene v12_mma_takedown_bg at mmarightbg
                                show v12_mma_shoot at mmaright
                                with hpunch
                                "I was quicker than him, and managed to shoot in deep and get a body lock on him."
                                play sound "sfx/slap.mp3"
                                scene v12_mma_takedown_miss with vpunch
                                "However, he managed to sprawl and avoid the takedown."
                                $ v12_rival_tired += 1
                                jump v12rd7sprawl
                            else:
                                if jiujitsu == 5 and v12_grappling_count < 3 or jiujitsu == 4 and v12_grappling_count < 2:
                                    play sound "sfx/punchgym.mp3"
                                    scene v12_mma_takedown_bg at mmarightbg
                                    show v12_mma_shoot at mmaright
                                    with hpunch
                                    "I shot for a takedown, but he saw it coming. We scrambled as I tried to manhandle him."
                                    play sound "sfx/slap.mp3"
                                    scene v12_mma_takedown_miss with vpunch
                                    i "...!"
                                    jump v12rd7sprawl
                                else:
                                    label v12rd7sprawlkick:
                                        play sound "sfx/strongpunch.mp3"
                                        scene v12_mma_takedown_fail with vpunch
                                    i "...!"
                                    $ v12_ian_hurt += 1
                                    $ v12_rival_score += 1
                                    if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                                        jump v12mmakoed
                                    else:
                                        scene v12_mma_base_bg at mmabg
                                        show v12_mma_base_rival at mmarival 
                                        show v12_mma_base_ian at mmaian
                                        with hpunch 
                                        "I stumbled back, dizzy and covering up in desperation to avoid taking any more hits."


        "{image=icon_defend.webp}Guard up":
            $ renpy.block_rollback()
            if v12_rival_score >= v12_ian_score + 2 and v12_round < 10 and v12_position != "guard":
                $ v12_position = "guard"
                fg "..."
                "He didn't seem eager to lead the dance. He was playing it safe, forcing me to take chances..."
                if v12_ian_tired > 0 and ian_athletics > 6 or v12_ian_tired > 1 and ian_athletics > 5 or v12_ian_tired > 2 and ian_athletics > 4:
                    "I used this chance to catch my breath back..."
                    $ v12_ian_tired -= 1
                jump v12_hub_exit
            if v12_position == "guard":
                "I took another step back, trying to recover, but this time my opponent decided to put pressure on me."
                if kickboxing > 3 and v12_ian_tired < 3:
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_block at mmaleft
                    with vpunch
                else:
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_damage at mmaleft
                    with vpunch
                    $ v12_rival_score += 1
                pause 0.5
                if kickboxing > 4 and v12_ian_tired < 2:
                    play sound "sfx/miss.mp3"
                    scene v12_mma_takedown_bg at mmaleftbg
                    show v12_mma_lowkick_dodge at mmaleft
                    with fps
                else:
                    play sound "sfx/punch.mp3"
                    scene v12_mma_takedown_bg at mmaleftbg
                    show v12_mma_lowkick_damage at mmaleft
                    with vpunch
                    $ v12_rival_score += 1
                pause 0.6

            jump v12mmaguard1

        "{image=icon_provoke.webp}Provoke" if v12_provoke_count < 8:
            $ renpy.block_rollback()
            if v12_provoke_count < 2:
                $ v12_provoke_count += 1
                jump v12prvk2
            elif v12_provoke_count < 3:
                $ v12_provoke_count += 1
                jump v12prvk3
            elif v12_provoke_count < 4:
                $ v12_provoke_count = 6
                i "What's the matter? Getting tired?"
                fg "Shut your fucking mouth already."
                i "Oh, so you can talk after all!"
                ref "Hey, enough of that! Stop it or I will disqualify you."
                i "Alright, alright."
                jump v12_action_menu2
            else:
                if v12_provoke_count == 6:
                    "I knew I couldn't use verbal warfare anymore without risking being disqualified..."
                $ v12_provoke_count = 7
                "I lowered my guard and showed my rival an opening, but he didn't take the bait."
                if v12_rival_hurt > 1:
                    "He was too worried about getting nailed again. I had him against the ropes..."
                else:
                    "He looked completely zoned in, ready to finish this fight."
                jump v12_action_menu2

        "{image=icon_tap.webp}Tap out!":
            $ renpy.block_rollback()
            jump v12mmatapout

    jump v12_hub_exit


####################################################################################################################################################
## ROUND 8  #########################################################################################################################################
label v12mmafight_rd8:
    $ renpy.block_rollback()
    if ian_wits < 7:
        $ timeout = 3.5
    elif ian_wits > 6:
        $ timeout = 3.0
    else:
        $ timeout = 2.5
    $ timeout_label = "v12_hit_hub"
    $ v12_round = 8
    
# STANDING   
    if v12_position == "standing" or v12_position == "guard":
        if v12_rival_hurt > 3 and v12_ian_hurt < 10 and v12_rival_vengeance < 2 or v12_sub_count > 9 and v12_ian_hurt < 10 and v12_rival_vengeance < 2:
            label v12rivallastattack:
                $ v12_rival_vengeance = 2
                "I had my opponent cornered. He knew it, and made a desperate attack."
                if kickboxing > 4:
                    play sound "sfx/miss.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_dodge at mmaleftdodge
                    show v12_mma_jab_dodge_rival at mmaleft
                    with fps
                else:
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_block at mmaleft
                    with hpunch
                pause 0.6
                if ian_wits < 7:
                    $ timeout = 3.0
                elif ian_wits > 6:
                    $ timeout = 2.5
                else:
                    $ timeout = 2.0
                $ timeout_label = "v12rrv1"
                menu:
                    "Block!":
                        $ renpy.block_rollback()
                        $ v12_position = "guard"
                        if kickboxing < 3 or kickboxing < 6 and v12_ian_tired > 3:
                            label v12rrv1:
                                $ v12_ian_hurt += 1
                                $ v12_rival_score += 1
                            play sound "sfx/strongpunch.mp3"
                            scene v12_mma_right_bg at mmaleftbg
                            show v12_mma_right_damage at mmaleftpower
                            with hpunch
                        else:
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_right_bg at mmaleftbg
                            show v12_mma_right_block at mmaleftpower
                            with hpunch

                    "Dodge!":
                        $ renpy.block_rollback()
                        play sound "sfx/miss.mp3"
                        scene v12_mma_right_bg at mmaleftbg
                        show v12_mma_right_dodge_rival at mmaleft
                        show v12_mma_right_dodge at mmaleftdodge
                        with hpunch

                pause 0.7 
                if ian_wits < 7:
                    $ timeout = 2.5
                elif ian_wits > 6:
                    $ timeout = 2.0
                $ timeout_label = "v12rrv2"
                menu:
                    "Block!":
                        $ renpy.block_rollback()
                        play sound "sfx/slap2.mp3"
                        scene v12_mma_headkick_bg at mmaleftbg
                        show v12_mma_headkick_block at mmaup
                        with vpunch
                        if v12_position == "guard" and v12_ian_tired < 2:
                            $ v12_ian_tired += 1
                        pause 1

                    "Dodge!":
                        $ renpy.block_rollback()
                        label v12rrv2:
                            play sound "sfx/strongpunch.mp3"
                            scene v12_mma_takedown_fail with vpunch
                            i "...!!"
                        $ v12_rival_score += 1
                        $ v12_ian_hurt += 1
                        if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                            jump v12mmakoed

                if kickboxing > 4:
                    play sound "sfx/miss.mp3"
                    scene v12_mma_takedown_bg at mmaleftbg
                    show v12_mma_lowkick_dodge at mmaleft
                    with short
                else:
                    play sound "sfx/punch.mp3"
                    scene v12_mma_takedown_bg at mmaleftbg
                    show v12_mma_lowkick_damage at mmaleft
                    with hpunch
                    $ v12_rival_score += 1
                "He continued to rush me, forcing me to step back."
                play sound "sfx/miss.mp3"
                scene v12_mma_jab_bg at mmaleftbg
                show v12_mma_jab_dodge at mmaleftdodge
                show v12_mma_jab_dodge_rival at mmaleft
                with fps
                pause 0.8
                if ian_wits < 7:
                    $ timeout = 2.0
                elif ian_wits > 6:
                    $ timeout = 1.8
                else:
                    $ timeout = 1.5
                $ timeout_label = "v12rrv3"
                menu:
                    "Protect your head":
                        $ renpy.block_rollback()
                        label v12rrv3:
                            $ v12_ian_tired += 1
                            $ v12_rival_score += 1
                        play sound "sfx/strongpunch.mp3"
                        scene v12_mma_kick_bg at mmaleftbg
                        show v12_mma_kick_damage at mmaleft
                        with hpunch
                        i "Ugh!"
                        
                    "Protect your body":
                        $ renpy.block_rollback()
                        "I saw his kick coming and I protected my midsection."
                        play sound "sfx/punch.mp3"
                        scene v12_mma_base_bg at mmabganim
                        show v12_mma_base_rival at mmarivalanim 
                        show v12_mma_base_ian at mmaiananim 
                        with hpunch
                        pause 0.5
                        play sound "sfx/punchgym.mp3"
                        scene v12_mma_right_bg at mmaleftbg
                        show v12_mma_right_miss at mmaleftpower
                        with vpunch
                        "I threw a potent right at him, putting a stop to his forward rush."
                        if ian_chad > 3:
                            i "Is that all you've got?"
                        
                $ v12_position = "standing"
                $ timeout_label = "v12_hit_hub"
                if ian_wits < 7:
                    $ timeout = 3.5
                elif ian_wits > 6:
                    $ timeout = 3.0
                else:
                    $ timeout = 2.5
                
        label v12mmaaction2:
            if v12_position == "standing":
                scene v12_mma_base_bg at mmabganim
                show v12_mma_base_rival at mmarivalanim 
                show v12_mma_base_ian at mmaiananim 
                with short
            else:
                $ v12_position = "standing"
        if v12_rival_hurt > 2 and v12_mma_wen < 2:
            if v12_mma_wen == 1:
                $ v12_mma_wen = 2
                wen "He's on wobbly legs! Finish him off!"
            else:
                $ v12_mma_wen = 1
                wen "He's hurt! Finish him off!"
        elif v12_rival_hurt > 1 and v12_mma_wen == 0:
            $ v12_mma_wen = 1
            wen "He's getting tired! Go for it!"
        if v12_ian_score >= v12_rival_score and v12_rival_vengeance == 0:
            jump v12mmarivalvengeance
        show phase_initiative with short
        jump v12_action_menu2

# CLINCH 
    if v12_position == "clinch":
        label v12mmaclinchattack2:
            if v12_sub_count > 9 and v12_rival_hurt > 2 or v12_sub_count > 9 and v12_rival_tired > 3:
                $ timeout_label = "v12grappletimeout"
                "He tried to escape my grip, wanting to avoid being taken down at all costs."
                menu:
                    "{image=icon_athletics}Slam him!" if jiujitsu > 3 and v12_ian_tired < 3 or jiujitsu == 5 and v12_ian_tired < 4:
                        $ renpy.block_rollback()
                        "Resisting wouldn't help him. I was determined to finish this."
                        jump v12mmaslamfinish
                
                    "{image=icon_wits.webp}Trip him" if jiujitsu > 3 and v12_ian_tired < 4:
                        $ renpy.block_rollback()
                        "I used his desperation in my favor, waiting for him to be out of balance and..."
                        play sound "sfx/slap.mp3"
                        scene v12_mma_takedown_bg at mmakickkobg
                        show v12_mma_takedown1 at mmarightpower
                        with short
                        fg "...!{w=1.0}{nw}"
                        $ v12_rival_tired += 1
                        $ v12_ian_score += 1
                        play sound "sfx/fall.mp3"
                        scene v12_mma_sub2 
                        with vpunch  
                        "I swept him off his feet and sinched in an armbar immediately."
                        "I arched my back and raised my hips, cranking with all I had."
                        fg "...!!!" with hpunch
                        "This time, I felt him tap immediately."
                        "The referee was quick to jump in and break us apart, calling a stop the the fight."
                        $ tournament = "winsub"
                        jump v12mmafightend

                    "Take him down":
                        $ renpy.block_rollback()
                        play sound "sfx/fall.mp3"
                        scene v12_mma_ground_top
                        with vpunch
                        "I manhandled him to the ground, where I took top position."
                        $ v12_position = "groundtop"
                        $ v12_takedown_count += 1
                        jump v12groundtop2
            else:
                "I wrestled him, trying to take the fight to the mat, but he resisted tooth and nail."
        if jiujitsu > 3 and v12_ian_tired < 3 or jiujitsu > 4 and v12_ian_tired < 4:
            play sound "sfx/slap.mp3"
            scene v12_mma_takedown_bg at mmakickkobg
            show v12_mma_takedown1 at mmarightpower
            with hpunch
            fg "...!{w=0.6}{nw}"
            play sound "sfx/fall.mp3"
            scene v12_mma_ground_top with vpunch
            $ v12_ian_score += 1
            $ v12_takedown_count += 1
            $ v12_position = "groundtop"
            if v12_rival_tired < 2:
                $ v12_rival_tired += 1
        elif jiujitsu > 2 and v12_ian_tired < 2:
            $ v12_ian_tired += 1
            "As we pushed against each other, I felt my opponent lose his balance."
            play sound "sfx/fall.mp3"
            scene v12_mma_ground_top with vpunch
            "I pushed all my weight onto him, getting him to the mat like I wanted."
            $ v12_takedown_count += 1
            $ v12_position = "groundtop"
        else:
            play sound "sfx/slap.mp3"
            scene v12_mma_takedown_damage_bg at mmaleftbg
            show v12_mma_takedown_damage at mmaleft
            with hpunch
            i "...!!"
            $ v12_rival_score += 1
            play sound "sfx/fall.mp3"
            scene v12_mma_ground_bottom with vpunch
            "I suddenly found myself on the mat with my rival on top. I wasn't expecting him to try and take me down!"
            $ v12_position = "groundbottom"
            $ v12_rival_takedown += 1
        jump v12_hub_exit
            
# GROUND BOTTOM - done
    if v12_position == "groundbottom":
        label v12groundbottom2:
            "He smothered me, pressing his chest down on my face, making it hard for me to breathe."
            $ timeout_label = "v12mmarnc2"
        if (v12_rival_vengeance == True and v12_rival_sub < 3 and v12_ian_tired > 1) or (v12_rival_vengeance == True and v12_rival_sub < 3 and jiujitsu < 4):
            "He tried to grab a hold of my arm, working aggressively towards a submission. I defended, but it was a trap."
            jump v12mmarnc2
        menu:
            "{image=icon_athletics.webp}Go for a choke!":
                $ renpy.block_rollback()
                $ v12_position == "guillotine"
                $ v12_sub_count+= 1
                play sound "sfx/slap.mp3"
                scene v12_mma_sub1 with vpunch
                pause 0.6
                if v12_sub_count > 1:
                    "I went for another submission, hoping I could finish him this time...!"
                else:
                    "I used this chance to sinch up a guillotine choke on him. I could try and finish him from this position...!"
                with hpunch
                pause 0.6
                with hpunch
                if jiujitsu > 2 and v12_ian_tired < 5 or ian_athletics > 5 and v12_ian_tired < 4:
                    menu:
                        "{image=icon_athletics.webp}Keep squeezing!":
                            $ renpy.block_rollback()
                            $ v12_ian_tired += 1
                            $ v12_ian_score += 1
                            $ v12_rival_tired += 1
                            "I committed to the choke with all I had left."
                            with hpunch
                            pause 0.6
                            with hpunch
                            "I was putting a lot of pressure on his neck, but he wasn't tapping...!"
                            menu:
                                "{image=icon_athletics.webp}Keep squeezing!":
                                    $ renpy.block_rollback()
                                    $ v12_ian_tired += 1
                                    i "Ughhh!!!" with vpunch
                                    "I squeezed his neck for dear life, using every fiber in my body."
                                    if jiujitsu == 5 and v12_rival_tired > 2 and v12_ian_tired < 5 or jiujitsu == 4 and v12_rival_tired > 3 and v12_ian_tired < 4 or jiujitsu == 3 and v12_rival_tired > 4 and v12_ian_tired < 3:
                                        fg "...!!{w=0.8}{nw}" with hpunch
                                        with hpunch
                                        pause 0.6
                                        with hpunch
                                        "I felt his hand desperately tapping on my side. A second later, the referee jumped in and forced me to let go of the choke, breaking us apart."
                                        $ tournament = "winsub"
                                        jump v12mmafightend
                                    else:
                                        "I felt my arms burning and my fingers slipping..."
                    
                                "Let go of the choke":
                                    $ renpy.block_rollback()
                                    "I was burning my arms out... I wasn't sure I could make him tap, so I let go of the choke."
                    
                        "Let go of the choke":
                            $ renpy.block_rollback()
                            "I was burning my arms out... I wasn't sure I could make him tap, so I let go of the choke."
                else:
                    $ v12_ian_tired += 1
                play sound "sfx/punchgym.mp3"
                scene v12_mma_ground_bottom with vpunch
                pause 0.6
                "He popped his head out, escaping my attempt at submitting him."
                if v12_round == 12:
                    jump v12mmafightend
                "I took a deep breath, trying to get some oxygen to my sore muscles before trying to get my opponent off me."
                if (jiujitsu == 5 and v12_ian_tired < 4) or (jiujitsu > 3 and v12_ian_tired < 3) or (ian_athletics > 6 and v12_ian_tired < 4) or (ian_athletics > 5 and v12_ian_tired < 3):
                    play sound "sfx/slap.mp3"
                    scene v12_mma_ground_top with hpunch
                    pause 0.6
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_base_bg at mmabg
                    show v12_mma_base_rival at mmarival 
                    show v12_mma_base_ian at mmaian
                    with vpunch
                    "We scrambled and we ended up back up on our feet."
                    $ v12_position = "standing"
                    jump v12_mma_defend_3
                else:
                    "He didn't grant me that chance, though."
                    $ v12_position = "rnc"
                    jump v12_hub_exit

            "Get up!":
                $ renpy.block_rollback()
                with hpunch
                "I fought to get him off me, trying to get my legs between us so I could push him away."
                if (jiujitsu > 3 and v12_ian_tired < 4) or (jiujitsu > 2 and v12_ian_tired < 3) or (ian_athletics > 6 and v12_ian_tired < 4) or (ian_athletics > 5 and v12_ian_tired < 3):
                    play sound "sfx/slap.mp3"
                    scene v12_mma_ground_top with hpunch
                    pause 0.6
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_base_bg at mmabg
                    show v12_mma_base_rival at mmarival
                    show v12_mma_base_ian at mmaian
                    with vpunch
                    "We scrambled and we ended up back up on our feet."
                    $ v12_position = "standing"
                    if v12_ian_tired < 2:
                        $ v12_ian_tired += 1
                    jump v12_mma_defend_3
                elif v12_rival_sub < 2:
                    $ v12_position = "rnc"
                    jump v12_hub_exit
                else:
                    with hpunch
                    $ v12_ian_tired += 1
                    $ v12_rival_score += 1
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_base_bg at mmabg
                    show v12_mma_base_rival at mmarival 
                    show v12_mma_base_ian at mmaian 
                    with vpunch
                    "After long seconds of struggle I finally managed to get back up to my feet."
                    $ v12_ian_tired += 1
                    $ v12_position = "standing"
                    jump v12_mma_defend_3
                    
            "{image=icon_wits.webp}Reverse the position":
                $ renpy.block_rollback()
                "I couldn't afford to stay in this position. I tried sweeping my opponent and getting the upper hand."
                if jiujitsu > 3:
                    play sound "sfx/fall.mp3"
                    scene v12_mma_ground_top with vpunch
                    "I reversed the position with a smooth and well-timed sweep."
                    $ v12_rival_tired += 1
                    $ v12_ian_score += 1
                    if jiujitsu == 5 or (v12_ian_tired < 3 and v12_rival_tired > 2) or (v12_ian_tired < 4 and v12_rival_tired > 3) :
                        with hpunch
                        "My rival struggled to free himself, but he wasn't able to get rid of me."
                        $ v12_position = "groundtop"
                    else:
                        "However, my opponent used the brief moment my weight wasn't on him to push me off and roll backward, getting back up to his feet."
                        play sound "sfx/punch.mp3"
                        scene v12_mma_base_bg at mmabg
                        show v12_mma_base_rival at mmarival
                        with hpunch
                        show v12_mma_base_ian at mmaian with short
                        "It was clear he didn't want to risk getting caught in my ground game."
                        $ v12_position = "standing"
                        if v12_rival_vengeance == 0:
                            jump v12mmarivalvengeance
                        jump v12mmavengeance1
                    jump v12_hub_exit
                else:
                    if jiujitsu < 3:
                        $ v12_ian_tired += 1
                    with hpunch
                    "I tried to create some space pushing him away, turning to get on my knees, and..."
                    $ v12_position = "rnc"
                    jump v12_hub_exit
            
            "Tie him up":
                $ renpy.block_rollback()
                with hpunch
                "I held onto him, trying to prevent him from passing guard."
                if jiujitsu > 3:
                    if v12_ian_tired > 0:
                        $ v12_ian_tired -= 1
                    "It wasn't the most comfortable position, but I was able to get a bit of a breather..."
                elif jiujitsu < 4 or ian_athletics < 6:
                    "I was having trouble breathing with all his weight pressing down on me..."
                    $ v12_ian_tired += 1
                if (jiujitsu > 3 and v12_ian_tired < 4) or (jiujitsu > 2 and v12_ian_tired < 2) or (ian_athletics > 6 and v12_ian_tired < 3) or (ian_athletics > 5 and v12_ian_tired < 2):
                    $ v12_round += 1
                    with hpunch
                    "I managed to defend all his attempts to advance position and nullified any techniques he tried to pull off."
                    "Finally, seeing there wasn't much action going on, the referee decided to stand us up."
                    scene v12_mma_base_bg at mmabg
                    show v12_mma_base_rival at mmarival
                    show v12_mma_base_ian at mmaian
                    with short
                    pause 0.5
                    $ v12_position = "standing"
                    jump v12_mma_defend_3
                elif v12_rival_sub < 2:
                    jump v12mmarnc2

                else:  
                    $ v12_round += 1
                    with hpunch
                    $ v12_ian_tired += 1
                    $ v12_rival_score += 1
                    "We grappled for position, but I held onto him with all I got and resisted all his attempts to sinch in a submission."
                    "Finally, seeing there wasn't much action going on, the referee decided to stand us up."
                    scene v12_mma_base_bg at mmabg
                    show v12_mma_base_rival at mmarival
                    show v12_mma_base_ian at mmaian
                    with short
                    pause 0.5
                    $ v12_position = "standing"
                    jump v12_mma_defend_3

# GROUND TOP
    if v12_position == "groundtop":
        label v12groundtop2:
            $ timeout_label = "v12mmaturnposition"
            menu:
                "{image=icon_wits.webp}Go for a submission" if jiujitsu > 2:
                    $ renpy.block_rollback()
                    label v12mmaarmbar2:
                        $ v12_sub_count += 1
                    if jiujitsu == 5 and v12_sub_count < 3 or jiujitsu > 3 and v12_sub_count < 2:
                        "I took my time pressuring my opponent, threatening with submissions and setting up traps."
                        "And when I saw the chance, I wrapped up his wrist and jumped into an armbar."
                        play sound "sfx/slap.mp3"
                        scene v12_mma_sub2 with vpunch
                        fg "...!" with hpunch
                        $ v12_ian_score += 1
                        $ v12_rival_hurt += 1
                        if (jiujitsu == 4 and v12_ian_tired < 4 and v12_rival_tired > 3) or (jiujitsu == 5 and v12_ian_tired < 4 and v12_rival_tired > 2):
                            "I trapped his arm within the vice of my knees, turned his palm up to get the correct angle and cranked hard."
                            fg "...!!!" with vpunch
                            "He had no chance but to tap to prevent me from obliterating his joint."
                            "The referee was quick to jump in and break us apart, calling a stop the the fight."
                            $ tournament = "winsub"
                            jump v12mmafightend
                        else:
                            "He still had the strength left in him to resist. He tried to free his arm, slippery with sweat."
                            "I had to get the correct angle to hyperextend his elbow..."
                    else:
                        play sound "sfx/slap.mp3"
                        scene v12_mma_sub2 with vpunch
                        "I wrapped up his wrist and jumped into an armbar straight away, hoping to finish him."
                        fg "...!" with hpunch
                        "He still had the strength left in him to resist. He tried to free his arm, slippery with sweat."
                        $ v12_ian_tired += 1
                        $ v12_ian_score += 1
                        $ v12_rival_hurt += 1
                        if (v12_ian_tired < 3 and v12_rival_tired > 3):
                            "But I knew what I had to do."
                            "I trapped his arm within the vice of my knees, turned his palm up to get the correct angle and cranked hard."
                            fg "...!!!" with vpunch
                            "He had no chance but to tap to prevent me from obliterating his joint."
                            "The referee was quick to jump in and break us apart, calling a stop the the fight."
                            $ tournament = "winsub"
                            jump v12mmafightend
                        else:
                            "I had to get the correct angle to hyperextend his elbow... "
                    if jiujitsu > 3 and v12_ian_tired < 5 and ian_will > 0:
                        with hpunch
                        "He pulled his arm away with all his strength. I was about to lose the submission!"
                        $ timeout = 2.0
                        $ timeout_label = "v12armbarescape"
                        menu:
                            "{image=icon_will.webp}Finish him!":
                                $ renpy.block_rollback()
                                call willdown from _call_willdown_64
                                "I trapped his arm within the vice of my knees, turned his palm up to get the correct angle and cranked hard."
                                play sound "sfx/slap.mp3"
                                fg "...!!!" with vpunch
                                "He had no chance but to tap to prevent me from obliterating his joint."
                                "The referee was quick to jump in and break us apart, calling a stop the the fight."
                                $ tournament = "winsub"
                                jump v12mmafightend

                            "Get up":
                                $ renpy.block_rollback()
                                label v12armbarescape:
                                    "I realized he was about to escape, so I decided to let go and roll backward, standing up."
                    
                    else:
                        play sound "sfx/fall.mp3"
                        scene v12_mma_ground_bottom with vpunch
                        "Somehow, he managed to roll over and free his arm. I thought I had him there..."
                        "But there was no way his elbow came out unscathed. I had cranked it hard."
                        "It was clear he didn't want to risk getting caught again, so he pushed me down to get back to his feet."
                    scene v12_mma_base_bg at mmabg
                    show v12_mma_base_rival at mmarival
                    show v12_mma_base_ian at mmaian
                    with short
                    pause 0.5
                    $ v12_position = "standing"
                    $ v12_sub_count = 10
                    jump v12_hub_exit

                "{image=icon_athletics.webp}Pin him down":
                    $ renpy.block_rollback()
                    "I put all my weight on my opponent, pinning him down and preventing him from escaping while making him work."
                    $ v12_ian_score += 1
                    $ v12_rival_tired += 1
                    if jiujitsu < 4 and ian_athletics < 6:
                        $ v12_ian_tired += 1
                    with hpunch
                    if v12_grappling_count < 9:
                        "He huffed and puffed, trying to get me off with urgency."
                    else:
                        pause 0.6
                    if (jiujitsu == 5 and v12_ian_tired < 4 and v12_rival_tired > 1 and v12_grappling_count < 10) or (jiujitsu > 3 and v12_ian_tired < 3 and v12_rival_tired > 2 and v12_grappling_count < 10):
                        $ v12_ian_score += 1
                        "I wasn't letting him. Carrying my weight was surely tiring him out, and I could see frustration on his face as he failed to shake me off."
                        "I had him right where I wanted."
                        $ v12_grappling_count = 10
                        jump v12groundtop2
                    elif (v12_rival_tired < 4 and jiujitsu < 4) or (v12_rival_tired < 3 and jiujitsu < 5) or (v12_rival_tired < 2 and jiujitsu < 5) or v12_grappling_count > 9:
                        label v12mmaturnposition:
                            play sound "sfx/fall.mp3"
                            scene v12_mma_ground_bottom with vpunch
                        $ v12_position = "groundbottom"
                        if v12_grappling_count > 9:
                            "This time, he was able to push me off and turn the position over."
                        else:
                            "He finally managed to make me lose my balance and turn the position over."
                        $ v12_rival_score += 1
                        jump v12_hub_exit
                    else:
                        if v12_grappling_count > 9:
                            jump v12mmaturnposition
                        with hpunch
                        "He finally managed to create some space, enough to scoot to the side and get back to his feet." 
                        play sound "sfx/punchgym.mp3"
                        with hpunch
                        jump v12mmagroundrest

                "Rest":
                    $ renpy.block_rollback()
                    if v12_ian_tired > 0 and jiujitsu > 2:
                        $ v12_ian_tired -= 1
                    if v12_ian_tired > 0:
                        $ v12_ian_tired -= 1 
                    "I used this chance to take a small breather, getting some stamina back."
                    if v12_grappling_count > 3:
                        jump v12mmaturnposition
                    elif (jiujitsu == 5 and v12_ian_tired < 3 and v12_grappling_count < 10) or (jiujitsu > 3 and v12_ian_tired < 2 and v12_grappling_count < 10):
                        with hpunch
                        "He tried to escape the position, but I maintained control. I had him right where I wanted."
                        $ v12_grappling_count = 4
                        jump v12_hub_exit
                    "He tried to escape the position, but I maintained control."
                    "After a while, and seeing there wasn't much action going on, the referee decided to stand us up."
                    label v12mmagroundrest:
                        scene v12_mma_base_bg at mmabg
                        show v12_mma_base_rival at mmarival
                        show v12_mma_base_ian at mmaian
                        with short
                        pause 0.5
                        $ v12_position = "standing"
                    "My opponent jumped straight back into the fray."
                    if v12_rival_vengeance == 0:
                        jump v12mmarivalvengeance
                    jump v12mmavengeance1
                
                "Stand up":
                    $ renpy.block_rollback()
                    "I wanted to continue the fight on the feet, so I let him off the hook."
                    jump v12mmagroundrest

# RNC
    if v12_position == "rnc":
        label v12mmarnc2:
            $ v12_position = "rnc"
        play sound "sfx/slap.mp3"
        scene v12_mma_sub_damage with hpunch
        $ v12_rival_score += 1
        i "...!!{w=0.8}{nw}"
        $ v12_rival_sub += 1
        if v12_rival_sub > 1:
            "He got me in a rear naked choke again...! He was squeezing will all he had left, trying to put me away!"
        else:
            "I felt his arms trapping and squeezing down on my neck. I couldn't breathe!"
            if v12_rival_sub < 3 and jiujitsu > 3 and v12_ian_tired < 3:
                "I dug my fingers into his forearms, trying desperately to break his hold."
                jump v12rncescape2
            else:
                "I dug my fingers into his forearms, trying desperately to break his hold. My head felt like it was about to burst."
        $ timeout = 2.5
        $ timeout_label = "v12_rnc_tap"
        $ v12_rival_sub = 3
        menu:
            "{image=icon_athletics.webp}Resist!":
                $ renpy.block_rollback()
                $ v12_ian_tired += 1
                "I fought with desperation, trying to escape from the jaws of defeat."
                if (jiujitsu > 3 and v12_ian_tired < 4 and v12_rival_tired > 1) or (jiujitsu > 4 and v12_ian_tired < 4) or (ian_athletics > 5 and v12_ian_tired < 3 and v12_rival_tired > 1) or (ian_athletics > 6 and v12_ian_tired < 4):
                    label v12rncescape2:
                        play sound "sfx/fall.mp3"
                        scene v12_mma_ground_top with vpunch
                    "Finally, I managed to create enough space for me to twist my hips and reverse the position with an explosive movement."
                    "That had been too close...!"
                    with hpunch
                    if v12_rival_sub < 3:
                        $ v12_rival_sub = 3
                        "My opponent tried pushing me off, but I held onto him. He wouldn't get away so easily."
                        $ v12_position = "groundtop"
                        jump v12_hub_exit
                    "My opponent scrambled and pushed me off with his legs, not wanting to let me have top position."
                    $ v12_rival_tired += 1
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_base_bg at mmabg
                    show v12_mma_base_rival at mmarival
                    show v12_mma_base_ian at mmaian
                    with vpunch
                    "He escaped and I followed him up, taking a deep breath and trying to recover."
                    $ v12_position = "standing"
                    jump v12_hub_exit
                elif (v12_ian_tired < 3 and v12_rival_tired > 1) or (v12_ian_tired < 4 and v12_rival_tired > 2) or (v12_ian_tired < 5 and v12_rival_tired > 3):
                    "I felt like I was about to pass out, but I managed to resist long enough for him to decide to give up the choke."
                    scene v12_mma_ground_bottom with short
                    "I could finally take a deep breath, trying to clear my head. That had been way too close...!"
                    "I didn't have the energy to get him off me right away, but I needed to get out of this position somehow."
                    $ v12_position = "groundbottom"
                    jump v12_hub_exit
                else:
                    i "...!" with hpunch
                    "I didn't have enough strength left in me to resist. I..."
                    if v12_rival_sub < 4:
                        menu:
                            "{image=icon_will.webp}Resist!" if ian_will > 0:
                                $ renpy.block_rollback()
                                call willdown from _call_willdown_65
                                i "Ughhh!!!" with hpunch
                                if v12_ian_tired > 0:
                                    $ v12_ian_tired -= 1
                                $ v12_rival_sub = 4
                                jump v12rncescape2
                        
                            "{image=icon_tap.webp}Tap out!":
                                $ renpy.block_rollback()
                    
                    jump v12_rnc_tap

            "{image=icon_tap.webp}Tap out!":
                $ renpy.block_rollback()
                jump v12_rnc_tap
        

    jump v12_hub_exit

####################################################################################################################################################
## ROUND 9  #########################################################################################################################################
label v12mmafight_rd9:
    $ renpy.block_rollback()
    if ian_wits < 7:
        $ timeout = 3.5
    elif ian_wits > 6:
        $ timeout = 3.0
    else:
        $ timeout = 2.5
    $ timeout_label = "v12_hit_hub"
    $ v12_round = 9
    
# STANDING   
    if v12_position == "standing" or v12_position == "guard":
        if v12_rival_hurt > 3 and v12_ian_hurt < 10 and v12_rival_vengeance < 2 or v12_sub_count > 9 and v12_ian_hurt < 10 and v12_rival_vengeance < 2:
            jump v12rivallastattack
        jump v12mmaaction2
#  CLINCH -done
    if v12_position == "clinch":
        jump v12mmaclinchattack2
# GROUND TOP
    if v12_position == "groundtop":
        jump v12groundtop2
# GROUND BOTTOM
    if v12_position == "groundbottom":
        jump v12groundbottom2
# RNC
    if v12_position == "rnc":
        jump v12mmarnc2
    jump v12_hub_exit

####################################################################################################################################################
## ROUND 10  #########################################################################################################################################
label v12mmafight_rd10:
    $ renpy.block_rollback()
    if ian_wits < 7:
        $ timeout = 3.5
    elif ian_wits > 6:
        $ timeout = 3.0
    else:
        $ timeout = 2.5
    $ timeout_label = "v12_hit_hub"
    $ v12_round = 10
   

# STANDING   
    if v12_position == "standing" or v12_position == "guard":
        if v12_rival_hurt > 3 and v12_ian_hurt < 10 and v12_rival_vengeance < 2 or v12_sub_count > 9 and v12_ian_hurt < 10 and v12_rival_vengeance < 2:
            jump v12rivallastattack
        jump v12mmaaction2
#  CLINCH -done
    if v12_position == "clinch":
        jump v12mmaclinchattack2
# GROUND TOP
    if v12_position == "groundtop":
        jump v12groundtop2
# GROUND BOTTOM
    if v12_position == "groundbottom":
        jump v12groundbottom2
# RNC
    if v12_position == "rnc":
        jump v12mmarnc2
    jump v12_hub_exit

####################################################################################################################################################
## ROUND 11  #########################################################################################################################################
label v12mmafight_rd11:
    $ renpy.block_rollback()
    if ian_wits < 7:
        $ timeout = 3.5
    elif ian_wits > 6:
        $ timeout = 3.0
    else:
        $ timeout = 2.5
    $ timeout_label = "v12_hit_hub"
    $ v12_round = 11
    
# STANDING   
    if v12_position == "standing" or v12_position == "guard":
        if v12_rival_hurt > 3 and v12_ian_hurt < 10 and v12_rival_vengeance < 2 or v12_sub_count > 9 and v12_ian_hurt < 10 and v12_rival_vengeance < 2:
            jump v12rivallastattack
        jump v12mmaaction2
#  CLINCH -done
    if v12_position == "clinch":
        jump v12mmaclinchattack2
# GROUND TOP
    if v12_position == "groundtop":
        jump v12groundtop2
# GROUND BOTTOM
    if v12_position == "groundbottom":
        jump v12groundbottom2
# RNC
    if v12_position == "rnc":
        jump v12mmarnc2
    jump v12_hub_exit


####################################################################################################################################################
## ROUND 12  #########################################################################################################################################
label v12mmafight_rd12:
    $ renpy.block_rollback()
    if ian_wits < 7:
        $ timeout = 3.5
    elif ian_wits > 6:
        $ timeout = 3.0
    else:
        $ timeout = 2.5
    $ timeout_label = "v12_hit_hub"
    $ v12_round = 12


# STANDING   
    if v12_position == "standing" or v12_position == "guard":
        if v12_rival_hurt > 3 and v12_ian_hurt < 10 and v12_rival_vengeance < 2 or v12_sub_count > 9 and v12_ian_hurt < 10 and v12_rival_vengeance < 2:
            jump v12rivallastattack
        jump v12mmaaction2
#  CLINCH -done
    if v12_position == "clinch":
        jump v12mmaclinchattack2
# GROUND TOP
    if v12_position == "groundtop":
        jump v12groundtop2
# GROUND BOTTOM
    if v12_position == "groundbottom":
        jump v12groundbottom2
# RNC
    if v12_position == "rnc":
        jump v12mmarnc2

    jump v12_hub_exit





## RANDOM LABEL LIST ##########################################################################################################################################
##########################################################################################################################################
label mma_randomizer(label_list):
    python:
        lbl = []
        for tempLbl, cond in label_list:
            times = int(eval(cond[1]) if eval(cond[0]) else eval(cond[2]))
            for tempIndex in range(times):
                lbl.append(tempLbl)
        renpy.random.shuffle(lbl)
        lbl = renpy.random.choice(lbl)
        renpy.call(lbl)
    return

define mma_randomizer_strike = [
    # (label_name, (condition, if_true, if_false))
    ("v12_mma_strike_kill", ("v12_rival_hurt > 2", "10", "0")),
    ("v12_mma_strike_kill", ("v12_rival_hurt > 2", "5", "0")),
    ("v12_mma_strike_kill", ("v12_rival_hurt > 1", "1", "0")),
    
    ("v12_mma_strike_good", ("kickboxing > 5", "1", "0")),
    ("v12_mma_strike_good", ("kickboxing > 4", "2", "1")),
    # if kickboxing is more than 4, then 2 chance, otherwise 1 chance

    ("v12_mma_strike_normal", ("kickboxing > 2", "2", "1")),
    # if kickboxing is more than 3, then 2 chance, otherwise 1 chance

    ("v12_mma_strike_bad", ("kickboxing < 4", "2", "1")),
    # if kickboxing is less than 4, then 2 chance, otherwise 1 chance
    ("v12_mma_strike_bad", ("v12_ian_tired > 1", "1", "0")),
    # if v12_ian_tired is more than 1, then 1 chance, otherwise 0 chance
    ("v12_mma_strike_bad", ("v12_ian_tired > 3", "1", "0")),
   
    

    ("v12_mma_strike_countered", ("kickboxing < 4", "2", "1")),
    # if kickboxing is less than 3, then 2 chance, otherwise 1 chance
    ("v12_mma_strike_countered", ("v12_attack_count > 4", "1", "0")),
    ("v12_mma_strike_countered", ("v12_attack_count > 8", "1", "0")),
    ("v12_mma_strike_countered", ("v12_ian_tired > 3", "1", "0")),
    

]

define mma_randomizer_defend = [
    # (label_name, (condition, if_true, if_false))
    ("v12_mma_defend_kill", ("v12_rival_tired > 4", "2", "0")),
    ("v12_mma_defend_kill", ("v12_rival_tired > 3", "1", "0")),
    ("v12_mma_defend_kill", ("v12_rival_hurt > 3", "2", "0")),
    ("v12_mma_defend_kill", ("v12_rival_hurt > 2", "1", "0")),
    

    ("v12_mma_defend_1", ("kickboxing > 5", "1", "2")),
    
    ("v12_mma_defend_2", ("kickboxing > 5", "1", "2")),
   
    ("v12_mma_defend_3", ("kickboxing < 5", "1", "2")),
    
    ("v12_mma_defend_4", ("kickboxing < 5", "1", "2")),
   
    
]

define mma_randomizer_hit = [
    # (label_name, (condition, if_true, if_false))
    ("v12rd1_hit", ("v12_ian_tired < 1", "2", "1")),
    
    ("v12rd2_hit", ("v12_ian_score > v12_rival_score", "2", "1")),
    
    ("v12rd3_hit", ("kickboxing > 3", "2", "1")),
   
    ("v12rd4_hit", ("v12_ian_tired > 1", "v12_ian_tired", "1")),
    ("v12rd4_hit", ("v12_ian_hurt > 1", "v12_ian_tired", "0")),
    

]
    # call mma_randomizer(mma_randomizer_strike)
    # call mma_randomizer(mma_randomizer_defend)
    # call mma_randomizer(mma_randomizer_hit)


# STRIKE LABELS ##########################################################################################################################################
##########################################################################################################################################
# FINISHING COMBO
label v12_mma_strike_kill:
    if v12_rival_hurt > 2:
        "He was wobbly, his eyes foggy and his guard loose. The opening was clear."
    elif v12_rival_hurt > 1:
        "He was keeping his poker face, but I could see he was hurting. I saw a clear opening in his guard."
    else:
        "Suddenly, I saw an opening."
    label v12finishmim1:
        "Without thought, my body sprung into action."
    if v12_rival_tired > 2 and kickboxing > 3:
        play sound "sfx/punchgym.mp3"
        scene v12_mma_jab_bg at mmarightbg
        show v12_mma_jab_hit at mmaright
        with vpunch
    else:
        play sound "sfx/miss.mp3"
        scene v12_mma_jab_bg at mmarightbg
        show v12_mma_jab_miss at mmaright
        with fps3
    pause 0.4
    play sound "sfx/punch.mp3"
    scene v12_mma_lowkick_bg at mmarightbg
    show v12_mma_lowkick_hit at mmalk
    with hpunch
    pause 0.5
    play sound "sfx/punch.mp3"
    scene v12_mma_body_bg at mmarightbg
    show v12_mma_body_hit at mmabody
    with vpunch
    pause 0.4
    play sound "sfx/strongpunch.mp3"
    scene v12_mma_kick_bg at mmarightbg
    show v12_mma_kick_hit at mmaup
    with hpunch
    pause 0.6
    play sound "sfx/strongpunch.mp3"
    scene v12_mma_right_bg at mmaleftbg
    show v12_mma_counter at mmaleftpower
    with hpunch
    label v12finishmim2:
        pause 0.2
    play sound "sfx/big_punch.mp3"
    pause 0.7
    scene v12_mma_headkick_bg at mmakickkobg
    show v12_mma_headkick at mmakickko
    with vpunch
    if cheat_mode:
        play sound "sfx/sf_ko.mp3"
    with flash
    fg "...!!!" with vpunch
    play sound "sfx/fall.mp3"
    scene v12_mma_base_bg at mmabganim
    show v12_mma_base_ian at mmaiananim
    with vpunch
    "That was the most perfect kick I had ever landed."
    "My opponent fell face-first to the mat, completely unconscious."
    "I looked at him from above, almost surprised at myself. A second later, the referee jumped in and stopped the fight, tending to my fallen rival."
    $ tournament = "winko"
    jump v12mmafightend
    
# GOOD RESULT ##############################################################
label v12_mma_strike_good:
    play sound "sfx/punch.mp3"
    scene v12_mma_jab_bg at mmarightbg
    show v12_mma_jab_hit at mmaright
    with hpunch
    pause 0.4
# perfect
    if kickboxing > 4 and v12_ian_tired < 1 or kickboxing == 6 and v12_ian_tired < 2:
        play sound "sfx/punch.mp3"
        scene v12_mma_body_bg at mmarightbg
        show v12_mma_body_hit at mmabody
        with vpunch
        pause 0.4
        play sound "sfx/strongpunch.mp3"
        scene v12_mma_kick_bg at mmarightbg
        show v12_mma_kick_hit at mmaup
        with hpunch
        fg "Ugh!!"
        play sound "sfx/strongpunch.mp3"
        scene v12_mma_right_hit_bg at mmaleftbg
        show v12_mma_right_hit at mmarightpower
        with vpunch
        pause 1
        $ v12_ian_score += 3
        $ v12_rival_hurt += 1
        $ v12_rival_tired += 1
# good
    else:
        play sound "sfx/strongpunch.mp3"
        scene v12_mma_right_hit_bg at mmaleftbg
        show v12_mma_right_hit at mmarightpower
        with vpunch
        pause 0.5
        play sound "sfx/punch.mp3"
        scene v12_mma_lowkick_bg at mmarightbg
        show v12_mma_lowkick_hit at mmalk
        with hpunch
        pause 0.6
        $ v12_ian_score += 3
        $ v12_rival_hurt += 1
    "My opponent gritted his teeth and tried to retaliate."
    if kickboxing > 4 and v12_ian_tired < 5:
        play sound "sfx/miss.mp3"
        scene v12_mma_jab_bg at mmaleftbg
        show v12_mma_jab_dodge at mmaleftdodge
        show v12_mma_jab_dodge_rival at mmaleft
        with fps
    elif kickboxing > 3 and v12_ian_tired < 4:
        play sound "sfx/punchgym.mp3"
        scene v12_mma_jab_bg at mmaleftbg
        show v12_mma_jab_block at mmaleft
        with hpunch
    else:
        play sound "sfx/punch.mp3"
        scene v12_mma_jab_bg at mmaleftbg
        show v12_mma_jab_damage at mmaleft
        with hpunch
        $ v12_rival_score += 1
    pause 0.4
    # strong vengeance
    if v12_ian_score >= v12_rival_score + 4:
        if kickboxing > 5 and v12_ian_tired < 4:
            play sound "sfx/miss.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_dodge_rival at mmaleft
            show v12_mma_right_dodge at mmaleftdodge
            with fps
        elif kickboxing > 4 and v12_ian_tired < 3:
            play sound "sfx/punchgym.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_block at mmaleftpower
            with hpunch
        else:
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_damage at mmaleftpower
            with hpunch
            $ v12_rival_score += 1
        pause 0.5
        play sound "sfx/punch.mp3"
        scene v12_mma_takedown_bg at mmaleftbg
        show v12_mma_lowkick_damage at mmaleft
        with hpunch
        pause 0.5
        $ v12_rival_score += 1
        if kickboxing > 3 and v12_ian_tired < 5 or v12_ian_tired < 6:
            play sound "sfx/slap2.mp3"
            scene v12_mma_headkick_bg at mmaleftbg
            show v12_mma_headkick_block at mmaup
            with vpunch
            if v12_ian_tired < 2:
                $ v12_ian_tired += 1
        else:
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_kick_bg at mmaleftbg
            show v12_mma_kick_damage at mmaleft
            with hpunch
            $ v12_rival_score += 1
            if v12_ian_tired < 3:
                $ v12_ian_tired += 1
        "What a combo...! He really was trying to finish me!"
    # normal vengeance
    elif v12_ian_score >= v12_rival_score + 2:
        if kickboxing > 5 and v12_ian_tired < 5:
            play sound "sfx/miss.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_dodge_rival at mmaleft
            show v12_mma_right_dodge at mmaleftdodge
            with hpunch
        elif kickboxing > 4 and v12_ian_tired < 4:
            play sound "sfx/punchgym.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_block at mmaleftpower
            with hpunch
        else:
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_damage at mmaleftpower
            with hpunch
            $ v12_rival_score += 1
        pause 0.5
        play sound "sfx/punch.mp3"
        scene v12_mma_takedown_bg at mmaleftbg
        show v12_mma_lowkick_damage at mmaleft
        with hpunch
        pause 0.5
        $ v12_rival_score += 1 
    # weak vengeance
    else:
        if kickboxing > 4 and v12_ian_tired < 5:
            play sound "sfx/miss.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_dodge_rival at mmaleft
            show v12_mma_right_dodge at mmaleftdodge
            with fps
            pause 0.5
            play sound "sfx/punchgym.mp3"
            scene v12_mma_takedown_bg at mmaleftbg
            show v12_mma_lowkick_dodge at mmaleft
            with fps
            "I dodged his jab and checked the kick."
        else:
            play sound "sfx/punchgym.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_block at mmaleftpower
            with hpunch
            pause 0.5
            play sound "sfx/punch.mp3"
            scene v12_mma_takedown_bg at mmaleftbg
            show v12_mma_lowkick_damage at mmaleft
            with hpunch
            pause 0.5
            $ v12_rival_score += 1
    
    "I jumped back and reset, prepared for the next exchange."       
    jump v12_hub_exit

# NORMAL RESULT ##############################################################
label v12_mma_strike_normal:
# good
    if kickboxing > 3 and v12_ian_tired < 3 or kickboxing > 4 and v12_ian_tired < 4 or kickboxing == 6:
        play sound "sfx/punch.mp3"
        scene v12_mma_jab_bg at mmarightbg
        show v12_mma_jab_hit at mmaright
        with hpunch
        pause 0.4
        play sound "sfx/punchgym.mp3"
        scene v12_mma_right_bg at mmaleftbg
        show v12_mma_right_miss at mmaleftpower
        with vpunch
        pause 0.5
        play sound "sfx/strongpunch.mp3"
        scene v12_mma_kick_bg at mmarightbg
        show v12_mma_kick_hit at mmaup
        with hpunch
        fg "Ugh!!"
        $ v12_ian_score += 2
        $ v12_rival_tired += 1
        if kickboxing > 4 and v12_ian_tired < 3 or kickboxing == 6:
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_right_hit_bg at mmaleftbg
            show v12_mma_right_hit at mmarightpower
            with hpunch
            pause 0.6
            $ v12_rival_hurt += 1
        play sound "sfx/punchgym.mp3"
        scene v12_mma_jab_bg at mmaleftbg
        show v12_mma_jab_block at mmaleft
        with vpunch
        pause 0.5
        play sound "sfx/miss.mp3"
        scene v12_mma_jab_bg at mmarightbg
        show v12_mma_jab_miss at mmaright
        with hpunch
        pause 0.4
        if v12_ian_score > v12_rival_score:
            play sound "sfx/punch.mp3"
            scene v12_mma_takedown_bg at mmaleftbg
            show v12_mma_lowkick_damage at mmaleft
            with vpunch
            $ v12_rival_score += 1
        else:
            play sound "sfx/miss.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_dodge_rival at mmaleft
            show v12_mma_right_dodge at mmaleftdodge
            with hpunch
        pause 0.6
# normal - rival blocks + reprisal
    elif kickboxing > 2 and v12_ian_tired < 3 or kickboxing > 4 and v12_ian_tired < 4:
        play sound "sfx/miss.mp3"
        scene v12_mma_jab_bg at mmarightbg
        show v12_mma_jab_miss at mmaright
        with fps3
        pause 0.4
        play sound "sfx/punchgym.mp3"
        scene v12_mma_right_bg at mmaleftbg
        show v12_mma_right_miss at mmaleftpower
        with vpunch
        pause 0.5
        play sound "sfx/punchgym.mp3"
        scene v12_mma_kick_bg at mmarightbg
        show v12_mma_kick_miss at mmaup
        with hpunch
        pause 0.6
        if v12_ian_tired < 2 or kickboxing > 4:
            play sound "sfx/miss.mp3"
            scene v12_mma_jab_bg at mmaleftbg
            show v12_mma_jab_dodge at mmaleftdodge
            show v12_mma_jab_dodge_rival at mmaleft
            with hpunch
            pause 0.3
            play sound "sfx/punch.mp3"
            scene v12_mma_body_bg at mmarightbg
            show v12_mma_body_hit at mmabody
            with vpunch
            pause 0.5
            $ v12_ian_score += 1
        elif v12_rival_tired < 3:
            play sound "sfx/punch.mp3"
            scene v12_mma_jab_bg at mmaleftbg
            show v12_mma_jab_damage at mmaleft
            with hpunch
            i "...!{w=1.0}{nw}"
            $ v12_rival_score += 1
        play sound "sfx/punch.mp3"
        scene v12_mma_lowkick_bg at mmarightbg
        show v12_mma_lowkick_hit at mmalk
        with vpunch
        fg "Ugh!"
        $ v12_ian_score += 1

# bad - countered
    else:
        play sound "sfx/miss.mp3"
        scene v12_mma_jab_bg at mmarightbg
        show v12_mma_jab_miss at mmaright
        with fps3
        pause 0.4
        play sound "sfx/strongpunch.mp3"
        scene v12_mma_body_bg at mmaleftbg
        show v12_mma_counter_damage at mmaleftcounter
        with hpunch
        i "...!"
        $ v12_rival_score += 1
        if v12_rival_tired < 2:
            $ v12_ian_hurt += 1
        if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
            jump v12mmakoed
        else:
            play sound "sfx/punch.mp3"
            scene v12_mma_lowkick_bg at mmarightbg
            show v12_mma_lowkick_hit at mmalk
            with hpunch
            pause 1
            $ v12_ian_score += 1
    jump v12_hub_exit

# BAD RESULT ##############################################################
label v12_mma_strike_bad:
    play sound "sfx/miss.mp3"
    scene v12_mma_jab_bg at mmarightbg
    show v12_mma_jab_miss at mmaright
    with fps3
    pause 0.4
    play sound "sfx/punchgym.mp3"
    scene v12_mma_right_bg at mmaleftbg
    show v12_mma_right_miss at mmaleftpower
    with hpunch
    pause 0.4
# good - get hit by low kick + right
    if (kickboxing > 4 and v12_ian_tired < 4) or (kickboxing == 6 and v12_ian_tired < 5):
        play sound "sfx/punch.mp3"
        scene v12_mma_kick_bg at mmarightbg
        show v12_mma_kick_miss at mmaup
        with hpunch
        pause 0.6
        play sound "sfx/punchgym.mp3"
        scene v12_mma_right_bg at mmaleftbg
        show v12_mma_right_block at mmaleftpower
        with vpunch
        pause 0.4
        play sound "sfx/punch.mp3"
        scene v12_mma_takedown_bg at mmaleftbg
        show v12_mma_lowkick_damage at mmaleft
        with vpunch
        $ v12_rival_score += 1
        if v12_ian_tired < 2 and kickboxing < 5 or v12_ian_tired == 0:
            pause 0.5
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_kick_bg at mmaleftbg
            show v12_mma_kick_damage at mmaleft
            with hpunch
            i "Ugh...!!"
            $ v12_rival_score += 1
            $ v12_ian_tired += 1
        else:
            pause 1
# mediocre - get hit by jab + head kick
    elif (kickboxing > 2 and v12_ian_hurt < 1 and v12_ian_tired < 3) or (kickboxing > 4 and v12_ian_hurt < 2  and v12_ian_tired < 4):
        play sound "sfx/punchgym.mp3"
        scene v12_mma_right_bg at mmaleftbg
        show v12_mma_right_block at mmaleftpower
        with vpunch
        pause 0.5
        play sound "sfx/punch.mp3"
        scene v12_mma_takedown_bg at mmaleftbg
        show v12_mma_lowkick_damage at mmaleft
        with vpunch
        pause 0.5
        play sound "sfx/punch.mp3"
        scene v12_mma_jab_bg at mmaleftbg
        show v12_mma_jab_damage at mmaleft
        with hpunch
        $ v12_rival_score += 1
        if v12_ian_tired < 3 and kickboxing < 5 or v12_ian_tired < 2:
            pause 0.5
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_kick_bg at mmaleftbg
            show v12_mma_kick_damage at mmaleft
            with hpunch
            i "...!!"
            $ v12_rival_score += 1
            $ v12_ian_tired += 1
        else:
            pause 0.8
# bad - body kick + combo/head kick
    else:
        play sound "sfx/punch.mp3"
        scene v12_mma_jab_bg at mmaleftbg
        show v12_mma_jab_damage at mmaleft
        with hpunch
        pause 0.5
        play sound "sfx/strongpunch.mp3"
        scene v12_mma_kick_bg at mmaleftbg
        show v12_mma_kick_damage at mmaleft
        with hpunch
        $ v12_rival_score += 2
        $ v12_ian_tired += 1
        if v12_ian_hurt > 2:
            pause 0.3
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_damage at mmaleftpower
            with vpunch
            pause 0.4
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_takedown_bg at mmaleftbg
            show v12_mma_lowkick_damage at mmaleft
            with hpunch
            pause 0.5
            play sound "sfx/strongpunchdbz.mp3"
            scene v12_mma_headkick_bg at mmaleftbg
            show v12_mma_headkick_damage at mmaup
            with vpunch
            i "...!!{w=1.0}{nw}" with vpunch
            $ v12_ian_hurt += 1
            $ v12_rival_score += 3
            jump v12mmakoed
        else:
            play sound "sfx/big_punch.mp3"
            pause 0.7
            scene v12_mma_headkick_bg at mmaleftbg
            show v12_mma_headkick_damage at mmaup
            with vpunch
            i "...!!{w=1.0}{nw}" with flash
            $ v12_rival_score += 1
            $ v12_ian_hurt += 1
            if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                jump v12mmakoed
    if v12_rival_hurt > 2:
        "He was a tough motherfucker, this guy!"
    jump v12_hub_exit

# COUNTERED RESULT ##############################################################
label v12_mma_strike_countered:
# countered but low kick
    if kickboxing > 4 and v12_ian_tired < 4 or kickboxing == 6 and v12_ian_tired < 5:
        play sound "sfx/miss.mp3"
        scene v12_mma_jab_bg at mmarightbg
        show v12_mma_jab_miss at mmaright
        with fps3
        pause 0.4
        play sound "sfx/punch.mp3"
        scene v12_mma_lowkick_bg at mmarightbg
        show v12_mma_lowkick_hit at mmalk
        with hpunch
        pause 0.4
        play sound "sfx/strongpunch.mp3"
        scene v12_mma_body_bg at mmaleftbg
        show v12_mma_counter_damage at mmaleftcounter
        with hpunch
        i "Ugh!!"
        $ v12_ian_score += 1
        $ v12_rival_score += 1
        if v12_rival_tired < 2:
            $ v12_ian_hurt += 1
            if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                jump v12mmakoed
# countered
    else:
        play sound "sfx/strongpunch.mp3"
        scene v12_mma_body_bg at mmaleftbg
        show v12_mma_counter_damage at mmaleftcounter
        with hpunch
        i "Ugh!!"
        $ v12_ian_hurt += 1
        $ v12_rival_score += 1
        # kill combo
        if (kickboxing < 7 and v12_ian_tired > 4) or (kickboxing < 5 and v12_ian_tired > 3) or (kickboxing < 4 and v12_ian_tired > 2):
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_kick_bg at mmaleftbg
            show v12_mma_kick_damage at mmaleft
            with hpunch
            pause 0.5
            play sound "sfx/punch.mp3"
            scene v12_mma_jab_bg at mmaleftbg
            show v12_mma_jab_damage at mmaleft
            with hpunch
            pause 0.4
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_damage at mmaleftpower
            with vpunch
            pause 0.5
            $ v12_ian_hurt += 1
            $ v12_ian_tired += 1
            $ v12_rival_score += 2
        # head kick
        if kickboxing < 3 or v12_ian_tired > 3:
            play sound "sfx/big_punch.mp3"
            pause 0.7
            scene v12_mma_headkick_bg at mmaleftbg
            show v12_mma_headkick_damage at mmaup
            with vpunch
            i "...!!" with flash
            $ v12_rival_score += 1
            $ v12_ian_hurt += 1
            if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                jump v12mmakoed
        else:
            play sound "sfx/slap2.mp3"
            scene v12_mma_headkick_bg at mmaleftbg
            show v12_mma_headkick_block at mmaup
            with vpunch
            pause 0.8
    if v12_rival_hurt > 2:
        "He still had fight in him...!"
    jump v12_hub_exit




# DEFEND LABELS ##########################################################################################################################################
##########################################################################################################################################

# DEFEND KILL ##############################################################
label v12_mma_defend_kill:
    if v12_round < 7:
        jump v12_mma_defend_1
    "I saw a grimace on my opponent's face as he loaded up his fist to attack, visibly tired."
    play sound "sfx/miss.mp3"
    scene v12_mma_jab_bg at mmaleftbg
    show v12_mma_jab_dodge at mmaleftdodge
    show v12_mma_jab_dodge_rival at mmaleft 
    with fps
    pause 0.5
    play sound "sfx/miss.mp3"
    scene v12_mma_right_bg at mmaleftbg
    show v12_mma_right_dodge_rival at mmaleft
    show v12_mma_right_dodge at mmaleftdodge
    with fps
    pause 0.6
    "I got past his sloppy punches. He was wide open."
    menu:
        "{image=icon_counter.webp}Counter him!" if kickboxing > 3:
            $ renpy.block_rollback()
            jump v12finishmim1

        "{image=icon_counter.webp}Take him down!" if jiujitsu > 2:
            $ renpy.block_rollback()
            if jiujitsu > 3:
                label v12mmaslamfinish:
                    play sound "sfx/slap.mp3"
                    scene v12_mma_takedown_bg at mmaslambg1
                    show v12_mma_takedown2 at mmaslam1
                    with hpunch
                pause 0.5
                fg "...!!{w=0.5}{nw}" 
                play sound "sfx/big_throw.mp3"
                with flash
                scene v12_mma_takedown3 with vpunch
                pause 1
                "I slammed my opponent head-first on the mat, leaving him dazed and defenseless, pinning him down with my knee on his belly."
                play sound "sfx/slap.mp3"
                scene v12_mma_sub2 with vpunch
                "Before he had a chance to react, I jumped to sinch in a perfect armbar and cranked hard."
                fg "...!!!" with vpunch
                "He had no chance but to tap to prevent me from obliterating his joint."
                "The referee was quick to jump in and break us apart, calling a stop the the fight as he tended to my concussed rival."
                $ tournament = "winsub"
                jump v12mmafightend
            else:
                play sound "sfx/slap.mp3"
                scene v12_mma_takedown_bg at mmaslambg1
                show v12_mma_takedown2 at mmaslam1
                with hpunch
                fg "...!{w=0.8}{nw}"
                play sound "sfx/fall.mp3"
                scene v12_mma_ground_top with vpunch
                $ v12_ian_score += 1
                $ v12_takedown_count += 1
                $ v12_position = "groundtop"
                jump v12_hub_exit

        "Hit him!":
            $ renpy.block_rollback()
            play sound "sfx/punchgym.mp3"
            scene v12_mma_right_hit_bg at mmaleftbg
            show v12_mma_right_hit at mmarightpower
            with vpunch
            pause 0.5
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_kick_bg at mmarightbg
            show v12_mma_kick_hit at mmaup
            with vpunch
            fg "Ugh!!"
            $ v12_rival_tired += 1
            $ v12_rival_hurt += 1
            $ v12_ian_score += 1
            if v12_rival_hurt > 3:
                jump v12finishmim2
                
# DEFEND 1 ##############################################################
label v12_mma_defend_1:
    play sound "sfx/miss.mp3"
    scene v12_mma_jab_bg at mmaleftbg
    show v12_mma_jab_dodge at mmaleftdodge
    show v12_mma_jab_dodge_rival at mmaleft
    with fps
    pause 0.6
    $ timeout = 2.5
    $ timeout_label = "v12rd3_guardhit"
    $ renpy.block_rollback()
    menu:
        "Block!":
            $ renpy.block_rollback()
            play sound "sfx/punchgym.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_block at mmaleftpower
            with vpunch
            pause 0.5
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_kick_bg at mmaleftbg
            show v12_mma_kick_damage at mmaleft
            with hpunch
            i "Umpf!!"
            "That kick took my breath away. I gritted my teeth and held back the pain."
            $ v12_rival_score += 1
            if ian_athletics < 7:
                $ v12_ian_tired += 1
            jump v12mmacounterchance

        "Dodge!":
            $ renpy.block_rollback()
            if kickboxing > 3:
                play sound "sfx/miss.mp3"
                scene v12_mma_right_bg at mmaleftbg
                show v12_mma_right_dodge_rival at mmaleft
                show v12_mma_right_dodge at mmaleftdodge
                with fps
                pause 0.6
                $ timeout_label = "v12guard3hithim"
                menu v12mmacounterchance:
                    "{image=icon_counter.webp}{image=icon_athletics.webp}Combo!" if (kickboxing > 4 and v12_ian_tired < 3) or (kickboxing == 6 and v12_ian_tired < 4):
                        $ renpy.block_rollback()
                        if v12_ian_tired < 2: 
                            $ v12_ian_tired += 1
                        "I saw a window of opportunity. Right there...!"
                        play sound "sfx/punchgym.mp3"
                        scene v12_mma_body_bg at mmarightbg
                        show v12_mma_body_hit at mmabody
                        with vpunch
                        pause 0.5
                        play sound "sfx/strongpunch.mp3"
                        scene v12_mma_right_hit_bg at mmaleftbg
                        show v12_mma_right_hit at mmarightpower
                        with vpunch
                        fg "Ugh!!{w=1.0}{nw}"
                        play sound "sfx/strongpunch.mp3"
                        scene v12_mma_kick_bg at mmarightbg
                        show v12_mma_kick_hit at mmaup
                        with hpunch
                        if cheat_mode:
                            "I nailed him with a three-piece-and-a-soda. He surely felt it!"
                        else:
                            "I nailed him with a solid combo. He surely felt that!"
                        $ v12_ian_score += 2
                        $ v12_rival_tired += 1
                        $ v12_rival_hurt += 1
                        if v12_ian_score >= v12_rival_score + 2:
                            jump v12guard2retaliate

                    "{image=icon_counter.webp}Strike!" if (kickboxing > 2 and v12_ian_tired < 2) or (kickboxing > 3 and v12_ian_tired < 3) or (kickboxing > 4 and v12_ian_tired < 4) or kickboxing == 6:
                        $ renpy.block_rollback()
                        "I saw a window of opportunity. Right there...!"
                        play sound "sfx/strongpunch.mp3"
                        scene v12_mma_right_hit_bg at mmaleftbg
                        show v12_mma_right_hit at mmarightpower
                        with vpunch
                        fg "Ugh!!"
                        "That was a good shot. He surely felt it!"
                        $ v12_ian_score += 1
                        $ v12_rival_hurt += 1
                        if v12_ian_score >= v12_rival_score + 2:
                            jump v12guard2retaliate

                    "{image=icon_counter.webp}Take him down!" if (jiujitsu > 3 and v12_ian_tired < 2) or (jiujitsu > 4 and v12_ian_tired < 3) or (jiujitsu > 3 and v12_ian_tired < 4):
                        $ renpy.block_rollback()
                        $ v12_grappling_count += 1
                        scene v12_mma_takedown_bg at mmarightbg
                        show v12_mma_shoot at mmaright
                        with fps
                        "I couldn't let this chance go to waste. I shot for a takedown, hoping to take the fight to the ground."
                        if v12_round < 7:
                            jump v12mmaclinchattack1 
                        else:
                            $ v12_position = "clinch"
                            jump v12mmaclinchattack2 

                    "Hit him!":
                        $ renpy.block_rollback()
                        label v12guard3hithim:
                            play sound "sfx/miss.mp3"
                            scene v12_mma_jab_bg at mmarightbg
                            show v12_mma_jab_miss at mmaright
                            with fps3
                            pause 0.5
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_right_bg at mmaleftbg
                            show v12_mma_right_miss at mmaleftpower
                            with hpunch
                            pause 0.5
                            if kickboxing > 3:
                                play sound "sfx/punch.mp3"
                                scene v12_mma_lowkick_bg at mmarightbg
                                show v12_mma_lowkick_hit at mmalk
                                with vpunch
                                "My punch missed, but at least I managed to land a low kick."
                                $ v12_ian_score += 1
                            else:
                                "My attempt to hit him back failed."
                            if v12_ian_score >= v12_rival_score + 2:
                                label v12guard2retaliate:
                                    if kickboxing > 3:
                                        play sound "sfx/miss.mp3"
                                        scene v12_mma_jab_bg at mmaleftbg
                                        show v12_mma_jab_dodge at mmaleftdodge
                                        show v12_mma_jab_dodge_rival at mmaleft
                                        with fps                                                            
                                    else:
                                        $ v12_rival_score += 1
                                        play sound "sfx/punch.mp3"
                                        scene v12_mma_jab_bg at mmarightbg
                                        show v12_mma_jab_hit at mmaright
                                        with hpunch
                                    pause 0.5
                                    play sound "sfx/punch.mp3"
                                    scene v12_mma_takedown_bg at mmaleftbg
                                    show v12_mma_lowkick_damage at mmaleft
                                    with vpunch
                                    pause 1
                                    $ v12_rival_score += 1
                        
                    "Rest":
                        $ renpy.block_rollback()
                        scene v12_mma_base_bg at mmabg
                        show v12_mma_base_rival at mmarival 
                        show v12_mma_base_ian at mmaian 
                        with short
                        "I circled around my opponent without engaging him, pacing myself and using these seconds to catch my breath back."
                        if v12_ian_tired > 0:
                            $ v12_ian_tired -= 1

            else:
                label v12rd3_guardhit:
                    play sound "sfx/punch.mp3"
                    scene v12_mma_right_bg at mmaleftbg
                    show v12_mma_right_damage at mmaleftpower
                    with vpunch
                    pause 0.5
                    play sound "sfx/strongpunch.mp3"
                    scene v12_mma_kick_bg at mmaleftbg
                    show v12_mma_kick_damage at mmaleft
                    with hpunch
                    i "Umpf!!"
                $ v12_rival_score += 2
                if ian_athletics < 7:
                    $ v12_ian_tired += 1
                $ v12_ian_hurt += 1
                if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                    jump v12mmakoed

        "Lean back":
            $ renpy.block_rollback()
            if kickboxing > 2:
                play sound "sfx/miss.mp3"
                scene v12_mma_right_bg at mmaleftbg
                show v12_mma_right_block at mmaleftpower
                with vpunch
                pause 0.6
                play sound "sfx/strongpunch.mp3"
                scene v12_mma_kick_bg at mmaleftbg
                show v12_mma_kick_damage at mmaleft
                with hpunch
                i "Umpf!!"
                $ v12_rival_score += 2
                if ian_athletics < 7:
                    $ v12_ian_tired += 1
                if kickboxing > 3 and v12_ian_tired < 3:
                    "That kick took my breath away, but I gritted my teeth and held back the pain."
                    play sound "sfx/punch.mp3"
                    scene v12_mma_lowkick_bg at mmarightbg
                    show v12_mma_lowkick_hit at mmalk
                    with vpunch
                    pause 1
                    $ v12_ian_score += 1
            else:
                jump v12rd3_guardhit
    jump v12_hub_exit

# DEFEND 2 ##############################################################
label v12_mma_defend_2:
    if kickboxing > 3:
        play sound "sfx/miss.mp3"
        scene v12_mma_jab_bg at mmaleftbg
        show v12_mma_jab_dodge at mmaleftdodge
        show v12_mma_jab_dodge_rival at mmaleft
        with fps
        pause 0.6
        play sound "sfx/punchgym.mp3"
        scene v12_mma_right_bg at mmaleftbg
        show v12_mma_right_block at mmaleftpower
        with hpunch
        pause 0.6
        if kickboxing > 4 and v12_ian_tired < 2 or kickboxing > 3 and v12_ian_tired < 1:
            play sound "sfx/punch.mp3"
            scene v12_mma_takedown_bg at mmaleftbg
            show v12_mma_lowkick_damage at mmaleft
            with vpunch
            "I was able to defend his combination, only getting hit by a low kick."
            $ v12_rival_score += 1
        else:
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_kick_bg at mmaleftbg
            show v12_mma_kick_damage at mmaleft
            with hpunch
            "He found an opening in my guard and hit me with a potent teep kick."
            $ v12_rival_score += 2
            $ v12_ian_tired += 1
        "But that had left him open. Now it was my turn to retaliate..."
        menu:
            "{image=icon_counter.webp}Punch":
                $ renpy.block_rollback()
                play sound "sfx/miss.mp3"
                scene v12_mma_jab_bg at mmarightbg
                show v12_mma_jab_miss at mmaright
                with fps3
                pause 0.6
                if kickboxing > 4 and v12_ian_tired < 5 or kickboxing > 3 and v12_ian_tired < 4:
                    play sound "sfx/punch.mp3"
                    scene v12_mma_body_bg at mmarightbg
                    show v12_mma_body_hit at mmabody
                    with vpunch
                    pause 0.6
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_right_bg at mmaleftbg
                    show v12_mma_right_miss at mmaleftpower
                    with hpunch
                    "I heard him groan. That body shot had to hurt."
                    if v12_rival_tired > 0 and v12_rival_hurt < 0:
                        $ v12_rival_hurt += 1
                    elif v12_rival_tired == 0:
                        $ v12_rival_tired += 1
                    $ v12_ian_score += 1
                    if v12_ian_score >= v12_rival_score + 2:
                        jump v12guard2retaliate
                else:
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_right_bg at mmaleftbg
                    show v12_mma_right_miss at mmaleftpower
                    with hpunch
                    "Both my punches failed to land."
        
            "{image=icon_counter.webp}Kick":
                $ renpy.block_rollback()
                if kickboxing > 4:
                    play sound "sfx/strongpunch.mp3"
                    scene v12_mma_kick_bg at mmarightbg
                    show v12_mma_kick_hit at mmaup
                    with hpunch
                    if kickboxing > 5:
                        pause 0.6
                        play sound "sfx/punch.mp3"
                        scene v12_mma_lowkick_bg at mmarightbg
                        show v12_mma_lowkick_hit at mmalk
                        with vpunch
                        "I dug my shin into his midsection with a well-placed kick and added a low kick to finish it off."
                        $ v12_ian_score += 1
                    else:
                        "I dug my shin into his midsection with a well-placed kick."
                    $ v12_ian_score += 1
                    $ v12_rival_tired += 1
                    if v12_ian_score >= v12_rival_score + 2:
                        jump v12guard2retaliate
                else:
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_kick_bg at mmarightbg
                    show v12_mma_kick_miss at mmaup
                    with hpunch
                    if kickboxing > 2 and v12_ian_tired < 2:
                        play sound "sfx/punch.mp3"
                        scene v12_mma_lowkick_bg at mmarightbg
                        show v12_mma_lowkick_hit at mmalk
                        with vpunch
                        "He blocked my kick to the midsection, but I managed to land a low kick after it."
                        $ v12_ian_score += 1
                    else:
                        "I telegraphed my kick and he blocked it."
            
            "Rest":
                $ renpy.block_rollback()
                scene v12_mma_base_bg at mmabg
                show v12_mma_base_rival at mmarival 
                show v12_mma_base_ian at mmaian 
                with short
                "I stood back and took a deep breath, trying to recover some stamina."
                if v12_ian_tired > 0:
                    $ v12_ian_tired -= 1

    else:
        if v12_ian_tired > 0:
            play sound "sfx/punchgym.mp3"
            scene v12_mma_jab_bg at mmaleftbg
            show v12_mma_jab_damage at mmaleft 
            with hpunch
            $ v12_rival_score += 1
        else:
            play sound "sfx/miss.mp3"
            scene v12_mma_jab_bg at mmaleftbg
            show v12_mma_jab_dodge at mmaleftdodge
            show v12_mma_jab_dodge_rival at mmaleft
            with fps
        pause 0.6
        play sound "sfx/punch.mp3"
        scene v12_mma_takedown_bg at mmaleftbg
        show v12_mma_lowkick_damage at mmaleft
        with vpunch
        pause 0.6
        $ v12_rival_score += 1
        if v12_ian_hurt > 0 or v12_ian_tired > 1:
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_kick_bg at mmaleftbg
            show v12_mma_kick_damage at mmaleft
            with hpunch
            "He caught me with a strong kick that almost folded me. I took a deep breath and stepped back."
            $ v12_ian_tired += 1
            $ v12_rival_score += 1
        else:
            play sound "sfx/punchgym.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_block at mmaleftpower
            with vpunch
            "I blocked that last punch and looked for a window to counter."
            menu:
                "Attack":
                    $ renpy.block_rollback()
                    play sound "sfx/miss.mp3"
                    scene v12_mma_jab_bg at mmarightbg
                    show v12_mma_jab_miss at mmaright
                    with fps3
                    pause 0.6
                    if v12_ian_tired == 0:
                        play sound "sfx/punch.mp3"
                        scene v12_mma_body_bg at mmarightbg
                        show v12_mma_body_hit at mmabody
                        with vpunch
                        $ v12_ian_score += 1
                        "I managed to score with a good body shot."
                    else:
                        play sound "sfx/punchgym.mp3"
                        scene v12_mma_right_bg at mmaleftbg
                        show v12_mma_right_miss at mmaleftpower
                        with hpunch
                        "Both my punches failed to land."
                "Rest":
                    $ renpy.block_rollback()
                    scene v12_mma_base_bg at mmabg
                    show v12_mma_base_rival at mmarival
                    show v12_mma_base_ian at mmaian
                    with short
                    "I stood back and took a deep breath, trying to recover some stamina."
                    if v12_ian_tired > 0:
                        $ v12_ian_tired -= 1
        
    jump v12_hub_exit


# DEFEND 3 ##############################################################
label v12_mma_defend_3:
    "He launched forward with clear intentions of throwing a one-two."
    $ config.menu_include_disabled = False
    $ greyed_out_disabled = True
    $ timeout_label = "v12defend3hit"
    menu:
        "{image=icon_counter.webp}Counter!":
            $ renpy.block_rollback()
            if kickboxing > 3 and v12_ian_tired < 1 or kickboxing > 4 and v12_ian_tired < 2 or kickboxing == 6:
                play sound "sfx/miss.mp3"
                scene v12_mma_jab_bg at mmarightbg
                show v12_mma_jab_miss at mmaright
                with fps3
                "I tried to stop him with a jab of my own, but he avoided it. It was just a feint!"
                play sound "sfx/slap2.mp3"
                scene v12_mma_headkick_bg at mmaleftbg
                show v12_mma_headkick_block at mmaup
                with vpunch
                "I managed to block his kick just in time."
                if v12_ian_tired < 2:
                    $ v12_ian_tired += 1
                jump v12defend3attack
            else:
                play sound "sfx/strongpunch.mp3"
                scene v12_mma_body_bg at mmaleftbg
                show v12_mma_counter_damage at mmaleftcounter
                with hpunch
                i "...!{w=1.0}{nw}"
                if kickboxing > 3 and v12_ian_tired < 2 or kickboxing > 4 and v12_ian_tired < 3:
                    play sound "sfx/slap2.mp3"
                    scene v12_mma_headkick_bg at mmaleftbg
                    show v12_mma_headkick_block at mmaup
                    with vpunch
                    "That had been close...! I stumbled back, recovering from the counterpunch I had just taken."
                    $ v12_rival_score += 1
                    $ v12_ian_hurt += 1
                    if v12_ian_tired < 2:
                        $ v12_ian_tired += 1
                else:
                    play sound "sfx/big_punch.mp3"
                    pause 0.7
                    scene v12_mma_headkick_bg at mmaleftbg
                    show v12_mma_headkick_damage at mmaup
                    with vpunch
                    $ v12_rival_score += 2
                    $ v12_ian_hurt += 2
                    if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                        jump v12mmakoed
                    else:
                        "I stumbled back, dizzy and disoriented."
                
        "{image=icon_defend.webp}Block":
            $ renpy.block_rollback()
            play sound "sfx/punchgym.mp3"
            scene v12_mma_jab_bg at mmaleftbg
            show v12_mma_jab_block at mmaleft
            with fps
            "I blocked his jab easily, but the second punch never came. It was just a feint."
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_kick_bg at mmaleftbg
            show v12_mma_kick_damage at mmaleft
            with hpunch
            i "Ugh!!{w=1.0}{nw}"
            $ v12_rival_score += 1
            $ v12_ian_tired += 1
            if kickboxing < 4 or v12_ian_tired > 1:
                play sound "sfx/punch.mp3"
                scene v12_mma_takedown_bg at mmaleftbg
                show v12_mma_lowkick_damage at mmaleft
                with vpunch
                pause 1
                $ v12_rival_score += 1
            play sound "sfx/miss.mp3"
            scene v12_mma_jab_bg at mmarightbg
            show v12_mma_jab_miss at mmaright
            with fps3
            pause 0.6
            if kickboxing > 3:
                play sound "sfx/punch.mp3"
                scene v12_mma_lowkick_bg at mmarightbg
                show v12_mma_lowkick_hit at mmalk
                with vpunch
                pause 0.6
                $ v12_ian_score += 1
                if kickboxing > 4 and v12_ian_tired < 3:
                    play sound "sfx/strongpunch.mp3"
                    scene v12_mma_kick_bg at mmarightbg
                    show v12_mma_kick_hit at mmaup
                    with hpunch
                    "I managed to score with a strong body kick of my own."
                    $ v12_ian_score += 1
                    $ v12_rival_tired += 1
                else:
                    "I managed to score with a good low kick."
            else:
                play sound "sfx/punchgym.mp3"
                scene v12_mma_right_bg at mmaleftbg
                show v12_mma_right_miss at mmaleftpower
                with hpunch
                "He avoided my attacks easily."

        "{image=icon_defend.webp}Dodge":
            $ renpy.block_rollback()
            play sound "sfx/miss.mp3"
            scene v12_mma_jab_bg at mmaleftbg
            show v12_mma_jab_dodge at mmaleftdodge
            show v12_mma_jab_dodge_rival at mmaleft
            with fps
            pause 0.5
            play sound "sfx/miss.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_right_dodge_rival at mmaleft
            show v12_mma_right_dodge at mmaleftdodge
            with fps
            if kickboxing > 3 and v12_ian_tired < 1 or kickboxing > 4 and v12_ian_tired < 2:
                "I dodged both of his punches easily, but I could see they were just feints."
                play sound "sfx/slap2.mp3"
                scene v12_mma_headkick_bg at mmaleftbg
                show v12_mma_headkick_block at mmaup
                with vpunch
                "I managed to block his kick just in time."
                if v12_ian_tired < 2:
                    $ v12_ian_tired += 1
                jump v12defend3attack
            else:
                "I dodged both of his punches easily, but they were just feints."
                play sound "sfx/strongpunch.mp3"
                scene v12_mma_takedown_fail with vpunch
                i "...!!"
                $ v12_rival_score += 1
                $ v12_ian_hurt += 1
                if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                    jump v12mmakoed
                else:
                    "I stumbled back, dizzy and disoriented."
            
        "Stay still" if ian_wits < 8:
            $ renpy.block_rollback()
            if v12_ian_tired == 0 or kickboxing > 3 and v12_ian_tired < 2 or kickboxing > 4:
                "He pulled his punches short. It was a feint!"
                play sound "sfx/slap2.mp3"
                scene v12_mma_headkick_bg at mmaleftbg
                show v12_mma_headkick_block at mmaup
                with vpunch
                if v12_ian_tired < 2:
                    $ v12_ian_tired += 1
                pause 1
                jump v12defend3attack
            else:
                label v12defend3hit:
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_damage at mmaleft
                    with hpunch
                    i "...!{w=1.0}{nw}"
                    play sound "sfx/strongpunch.mp3"
                    scene v12_mma_takedown_fail with vpunch
                    "I leaned forward to avoid the second punch, but I ate a kick instead."
                    $ v12_rival_score += 2
                    $ v12_ian_hurt += 1
                    if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                        jump v12mmakoed
                    else:
                        "I stumbled back, dizzy and disoriented."

        "{image=icon_wits.webp}Stay still" if ian_wits > 7:
            $ renpy.block_rollback()
            "I could see it was a feint. He pulled his punches short, and attempted to land a kick."
            play sound "sfx/slap2.mp3"
            scene v12_mma_headkick_bg at mmaleftbg
            show v12_mma_headkick_block at mmaup
            with vpunch
            pause 1
            label v12defend3attack:
                menu:
                    "{image=icon_counter.webp}Strike back" if kickboxing > 4 or kickboxing > 3 and v12_ian_tired < 3:
                        $ renpy.block_rollback()
                        play sound "sfx/punch.mp3"
                        scene v12_mma_body_bg at mmarightbg
                        show v12_mma_body_hit at mmabody
                        with hpunch
                        $ v12_ian_score += 1
                        pause 0.6
                        if kickboxing > 4:  
                            if kickboxing == 6:
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_right_hit_bg at mmaleftbg
                                show v12_mma_right_hit at mmarightpower
                                with hpunch
                                $ v12_ian_score += 1
                                $ v12_rival_hurt += 1
                            if kickboxing == 5:
                                play sound "sfx/strongpunch.mp3"
                                scene v12_mma_kick_bg at mmarightbg
                                show v12_mma_kick_hit at mmaup
                                with hpunch
                                $ v12_ian_score += 1
                                $ v12_rival_tired += 1
                            pause 1
                        else:
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_right_bg at mmaleftbg
                            show v12_mma_right_miss at mmaleftpower
                            with vpunch
                            pause 0.5

                    "{image=icon_athletics.webp}Trip him" if jiujitsu > 3:
                        $ renpy.block_rollback()
                        play sound "sfx/slap.mp3"
                        scene v12_mma_takedown_bg at mmakickkobg
                        show v12_mma_takedown1 at mmarightpower
                        with short
                        fg "...!{w=1.0}{nw}"
                        play sound "sfx/fall.mp3"
                        if v12_takedown_count == 0 and v12_ian_tired < 3:
                            scene v12_mma_ground_top with vpunch
                            "I swept him off his feet with a smooth trip and managed to take him to the ground."
                            "He tried to get up as soon as his butt touched the floor, but I was quick to get on top of him."
                            $ v12_takedown_count += 1
                            $ v12_ian_score += 1
                            $ v12_position = "groundtop"
                        else:
                            scene v12_mma_base_bg at mmabg
                            show v12_mma_base_ian at mmaian
                            with vpunch
                            $ v12_ian_score += 1
                            $ v12_rival_tired += 1
                            "I swept him, making him stumble and fall. He rolled on the mat, trying to get up before I could get on top of him."
                            show v12_mma_base_rival at mmarival with short
                            pause 0.5
                            play sound "sfx/miss.mp3"
                            scene v12_mma_jab_bg at mmaleftbg
                            show v12_mma_jab_dodge at mmaleftdodge
                            show v12_mma_jab_dodge_rival at mmaleft
                            with fps
                            pause 0.5
                            play sound "sfx/punchgym.mp3"
                            scene v12_mma_takedown_bg at mmaleftbg
                            show v12_mma_lowkick_dodge at mmaleft
                            with fps
                            "He tried to keep me off him with strikes, but they were ineffective."

                    "Hit him!":
                        $ renpy.block_rollback()
                        play sound "sfx/punch.mp3"
                        scene v12_mma_lowkick_bg at mmarightbg
                        show v12_mma_lowkick_hit at mmalk
                        with vpunch
                        $ v12_ian_score += 1
                        if kickboxing > 3 and v12_ian_tired < 2:
                            play sound "sfx/strongpunch.mp3"
                            scene v12_mma_kick_bg at mmarightbg
                            show v12_mma_kick_hit at mmaup
                            with hpunch
                            "I managed to score with a strong body kick of my own."
                            $ v12_ian_score += 1
                            $ v12_rival_tired += 1
                        else:
                            "I managed to score with a good low kick."

                    "Rest":
                        $ renpy.block_rollback()
                        scene v12_mma_base_bg at mmabg
                        show v12_mma_base_rival at mmarival 
                        show v12_mma_base_ian at mmaian
                        with short
                        "I stood back and took a deep breath, trying to recover some stamina."
                        if v12_ian_tired > 0:
                            $ v12_ian_tired -= 1

    if persistent.include_disabled:
        $ config.menu_include_disabled = True
    $ greyed_out_disabled = False
    jump v12_hub_exit


# DEFEND 4 ##############################################################
label v12_mma_defend_4:
    $ timeout_label = "v12defend4hit"
    play sound "sfx/miss.mp3"
    scene v12_mma_jab_bg at mmaleftbg
    show v12_mma_jab_dodge at mmaleftdodge
    show v12_mma_jab_dodge_rival at mmaleft
    with fps
    pause 0.6
    menu:
        "{image=icon_counter.webp}Counter!" if kickboxing > 4:
            $ renpy.block_rollback()
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_right_bg at mmaleftbg
            show v12_mma_counter at mmaleftpower
            with vpunch
            fg "Agh...!!{w=1.0}{nw}"
            $ v12_ian_score += 1
            $ v12_rival_hurt += 1
            if v12_rival_tired < 3:
                if kickboxing == 6 or v12_ian_tired < 2:
                    play sound "sfx/punchgym.mp3"
                    scene v12_mma_right_bg at mmaleftbg
                    show v12_mma_right_block at mmaleftpower
                    with hpunch
                    pause 0.5
                else:
                    play sound "sfx/strongpunch.mp3"
                    scene v12_mma_body_bg at mmaleftbg
                    show v12_mma_counter_damage at mmaleftcounter
                    with hpunch
                    $ v12_rival_score += 1
                    $ v12_ian_hurt += 1
                    i "Ugh!{w=1.0}{nw}"
            jump v12defend4exchange

        "{image=icon_counter.webp}Punch!":
            $ renpy.block_rollback()
            if kickboxing == 6 or (kickboxing > 4 and v12_ian_tired < 2) or (kickboxing > 3 and v12_ian_tired < 1):
                play sound "sfx/strongpunch.mp3"
                scene v12_mma_right_hit_bg at mmaleftbg
                show v12_mma_right_hit at mmarightpower
                with vpunch
                fg "...!{w=1.0}{nw}"
                $ v12_ian_score += 1
                $ v12_rival_hurt += 1
            if kickboxing == 6:
                play sound "sfx/punchgym.mp3"
                scene v12_mma_right_bg at mmaleftbg
                show v12_mma_right_block at mmaleftpower
                with hpunch
            else:
                play sound "sfx/strongpunch.mp3"
                scene v12_mma_body_bg at mmaleftbg
                show v12_mma_counter_damage at mmaleftcounter
                with hpunch
                $ v12_rival_score += 1
                $ v12_ian_hurt += 1
            pause 0.5
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_kick_bg at mmaleftbg
            show v12_mma_kick_damage at mmaleft
            with hpunch
            i "Ugh!{w=1.0}{nw}"
            $ v12_rival_score += 1
            $ v12_ian_tired += 1
            label v12defend4exchange:
                play sound "sfx/punch.mp3"
                scene v12_mma_takedown_bg at mmaleftbg
                show v12_mma_lowkick_damage at mmaleft
                with vpunch
                pause 0.6
                play sound "sfx/punch.mp3"
                scene v12_mma_lowkick_bg at mmarightbg
                show v12_mma_lowkick_hit at mmalk
                with vpunch
                pause 0.6
                if v12_ian_score > v12_rival_score:
                    play sound "sfx/punch.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_damage at mmaleft
                    with hpunch
                    $ v12_rival_score += 1
                else:
                    play sound "sfx/miss.mp3"
                    scene v12_mma_jab_bg at mmaleftbg
                    show v12_mma_jab_dodge at mmaleftdodge
                    show v12_mma_jab_dodge_rival at mmaleft
                    with fps
                pause 0.5
                play sound "sfx/miss.mp3"
                scene v12_mma_jab_bg at mmarightbg
                show v12_mma_jab_miss at mmaright
                with fps3
                pause 1

        "{image=icon_defend.webp}Defend!":
            $ renpy.block_rollback()
            if kickboxing > 3 or v12_ian_tired < 3:
                play sound "sfx/miss.mp3"
                scene v12_mma_right_bg at mmaleftbg
                show v12_mma_right_dodge_rival at mmaleft
                show v12_mma_right_dodge at mmaleftdodge
                with fps
                pause 0.5
            else:
                play sound "sfx/punchgym.mp3"
                scene v12_mma_right_bg at mmaleftbg
                show v12_mma_right_block at mmaleftpower
                with vpunch
                pause 0.5
                play sound "sfx/strongpunch.mp3"
                scene v12_mma_kick_bg at mmaleftbg
                show v12_mma_kick_damage at mmaleft
                with hpunch
                i "Ngh!"
                $ v12_rival_score += 1
                $ v12_ian_tired += 1
            if kickboxing > 4:
                play sound "sfx/miss.mp3"
                scene v12_mma_takedown_bg at mmaleftbg
                show v12_mma_lowkick_dodge at mmaleft
                with fps
            else:
                $ v12_rival_score += 1
                play sound "sfx/punch.mp3"
                scene v12_mma_takedown_bg at mmaleftbg
                show v12_mma_lowkick_damage at mmaleft
                with vpunch
            pause 1
            jump v12mmacounterchance

        "Rest":
            $ renpy.block_rollback()
            "I took a step back, trying to get some room to breathe..."
            label v12defend4hit:
                play sound "sfx/strongpunch.mp3"
                scene v12_mma_right_bg at mmaleftbg
                show v12_mma_right_damage at mmaleftpower
                with vpunch
                pause 0.5
                play sound "sfx/strongpunch.mp3"
                scene v12_mma_kick_bg at mmaleftbg
                show v12_mma_kick_damage at mmaleft
                with hpunch
                $ v12_rival_score += 2
                $ v12_ian_tired += 1
                $ v12_ian_hurt += 1
                if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                    pause 0.5
                    jump v12mmakoed
                else:
                    i "Agh...!!"

    jump v12_hub_exit


# HIT TIMEOUT LABELS ##########################################################################################################################################
##########################################################################################################################################
label v12rd1_hit:
    if v12_ian_score >= v12_rival_score:
        if kickboxing > 3:
            play sound "sfx/miss.mp3"
            scene v12_mma_jab_bg at mmaleftbg
            show v12_mma_jab_dodge at mmaleftdodge
            show v12_mma_jab_dodge_rival at mmaleft
            with fps
        else:
            play sound "sfx/punchgym.mp3"
            scene v12_mma_jab_bg at mmarightbg
            show v12_mma_jab_hit at mmaright
            with hpunch
            $ v12_rival_score += 1
        pause 0.6
        play sound "sfx/slap2.mp3"
        scene v12_mma_takedown_bg at mmaleftbg
        show v12_mma_lowkick_damage at mmaleft
        with vpunch
        $ v12_rival_score += 1
        pause 0.6
        if kickboxing > 3:
            play sound "sfx/punch.mp3"
            scene v12_mma_headkick_bg at mmaleftbg
            show v12_mma_headkick_block at mmaup
            with vpunch
            i "...!"
            if v12_ian_tired < 2:
                $ v12_ian_tired += 1
            "That high kick almost nailed me...!" 
        else:
            play sound "sfx/strongpunch.mp3"
            scene v12_mma_kick_bg at mmaleftbg
            show v12_mma_kick_damage at mmaleft
            with hpunch
            $ v12_rival_score += 2
            $ v12_ian_tired += 1
            $ v12_ian_hurt += 1
            if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                jump v12mmakoed 
    else:
        $ v12_rival_score += 1
        play sound "sfx/punchgym.mp3"
        hide phase_initiative
        scene v12_mma_jab_bg at mmaleftbg
        show v12_mma_jab_damage at mmaleft 
        with hpunch
        "Before I could react, his jab connected flush on my face."
        "I shook my head, and tightened my guard, surprised. I couldn't get distracted!"   
    jump v12_hub_exit

# hit 2
label v12rd2_hit:
    if v12_ian_hurt == 0:
        play sound "sfx/punchgym.mp3"
        scene v12_mma_jab_bg at mmaleftbg
        show v12_mma_jab_block at mmaleft
        with vpunch
        "I barely had time to react to a jab, which I blocked on instinct."
        play sound "sfx/punch.mp3"
        scene v12_mma_kick_bg at mmaleftbg
        show v12_mma_kick_damage at mmaleft
        with hpunch
        i "Ugh!"
        "I didn't see his kick coming, and he slammed the ball of his foot right into my stomach, making me want to puke."
        $ v12_ian_tired += 1
        $ v12_rival_score += 2
   
    jump v12_hub_exit
    
# hit 3
label v12rd3_hit:
    play sound "sfx/punch.mp3"
    scene v12_mma_takedown_bg at mmaleftbg
    show v12_mma_lowkick_damage at mmaleft
    with vpunch
    "I was expecting a punch, but he surprised me with a low kick that felt like a bat to the thigh."
    $ v12_rival_score += 1
    play sound "sfx/punchgym.mp3"
    scene v12_mma_jab_bg at mmaleftbg
    show v12_mma_jab_block at mmaleft
    with fps
    $ timeout = 2.0
    $ timeout_label = "v12rd3_hit2"
    menu:
        "Block!":
            $ renpy.block_rollback()
            "I kept my guard up, and I was glad I did so."
            play sound "sfx/slap2.mp3"
            scene v12_mma_headkick_bg at mmaleftbg
            show v12_mma_headkick_block at mmaup
            with vpunch
            "My forearms caught a swift high kick that would leave bruises."
            "If I had taken that to the face..."
            $ v12_ian_tired += 1
            jump v12_hub_exit

        "Duck!":
            $ renpy.block_rollback()
            "I ducked, expecting a follow-up punch. But..."
            label v12rd3_hit2:
                play sound "sfx/slap2.mp3"
                scene v12_mma_headkick_bg at mmaleftbg
                show v12_mma_headkick_damage at mmaup
                with vpunch
            "I was suddenly whacked by his foot right in the jaw."
            $ v12_ian_hurt += 1
            $ v12_rival_score += 2  
            if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
                jump v12mmakoed 
            else:
                "My vision went dark for a split second, and then little white dots appeared all over."
                "I stumbled back, dizzy, trying to keep my guard up."
            jump v12_hub_exit

# hit 4
label v12rd4_hit:
    play sound "sfx/punch.mp3"
    scene v12_mma_jab_bg at mmaleftbg
    show v12_mma_jab_damage at mmaleft
    with hpunch
    pause 0.6
    play sound "sfx/strongpunch.mp3"
    scene v12_mma_right_bg at mmaleftbg
    show v12_mma_right_damage at mmaleftpower
    with hpunch
    i "Oof!!"
    $ v12_rival_score += 2
    $ v12_ian_hurt += 1
    if v12_ian_hurt > 2 or v12_ian_hurt > 1 and ian_athletics < 6:
        jump v12mmakoed
    jump v12_hub_exit

# KOed
label v12mmakoed:
    if persistent.include_disabled:
        $ config.menu_include_disabled = True
    $ greyed_out_disabled = False
    play sound "sfx/fall.mp3"
    scene v12_mma_base_bg at mmabg
    show v12_mma_base_rival at mmarival 
    with vpunch
    pause 0.3
    scene black with fps
    pause 0.3
    scene v12_mma_base_bg at mmabg
    show v12_mma_base_rival at mmarival 
    with fps
    pause 0.3
    scene black with fps
    pause 0.3
    scene v12_mma_base_bg at mmabg
    show v12_mma_base_rival at mmarival 
    with fps
    pause 1
    if v12_ian_hurt < 9:
        "My vision went black and I dropped down to the mat, feeling a head-splitting pain and ringing in my ears."
        if ian_will > 0 and v12_ian_dropped == False:
            $ timeout = 2.5
            $ timeout_label = "v12mmakoedend"
            menu:
                "{image=icon_will.webp}Stand back up!":
                    $ renpy.block_rollback()
                    $ v12_ian_dropped = True
                    $ v12_ian_hurt = 10
                    call willdown from _call_willdown_66
                    "Desperate, I summoned all my willpower and sprung back to my feet, dizzy and trying to get my bearing back."
                    show v12_mma_base_ian at mmaian with short
                    "I stumbled back, covering up, as my opponent moved forward to pressure me and finish the fight."
                    jump v12_hub_exit
                    
                "I'm done":
                    $ renpy.block_rollback()
                    label v12mmakoedend:
                        $ v12_ian_dropped = True
                        $ tournament = "loseko"
                    stop music fadeout 2.0
                    scene black with short
                    scene gym with long
                    "My body wouldn't respond to my scattered thoughts as I tried to move, but I didn't know what was going on or where I was."
                    "A few seconds later, when I came to, I saw the referee kneeling in front of me, and my opponent looking down on me and raising his hand, victorious."
                    "It took me a moment to understand what had happened. I had been knocked out..."
                    jump v12mmafightend
        else:
            jump v12mmakoedend
    else:
        jump v12mmakoedend



## IMAGE ANIMATIONS ###############################################################################################################################################################################################

# base anim
transform mmabg:
    zoom 1.1
    xalign 0.5
transform mmaian:
    zoom 1.1
    xanchor 1.0
    xpos 1.04
transform mmarival:
    xanchor 1.0
    xpos 1.0   

transform mmabganim:
    subpixel True
    zoom 1.1
    xalign 0.5
    ease 3 xalign 0.7
    ease 3 xalign 0.5
    repeat

transform mmaiananim:
    subpixel True
    zoom 1.1
    xanchor 1.0
    xpos 1.04
    ease 3 xpos 1.12
    ease 3 xpos 1.04
    repeat

transform mmarivalanim:
    subpixel True
    xanchor 1.0
    xpos 1.0
    ease 3 xpos 0.9
    ease 3 xpos 1.0
    repeat

# right
transform mmarightbg:
    subpixel True
    zoom 1.1
    xalign 0.5
    easein 2 xalign 0.8

transform mmaright:
    subpixel True
    xanchor 1.0
    xpos 1.0
    easein 2 xpos 1.04

transform mmarightpower:
    subpixel True
    xanchor 1.0
    yanchor 1.0
    xpos 1.0
    ypos 1.02
    easein 2 xpos 1.02 ypos 1.0

transform mmabody:
    subpixel True
    xanchor 1.0
    yanchor 1.0
    xpos 1.0
    ypos 1.015
    easein 2 xpos 1.04 ypos 1.0


# left
transform mmaleftbg:
    subpixel True
    zoom 1.1
    xalign 0.5
    easein 2 xalign 0.1

transform mmaleft:
    subpixel True
    xanchor 1.0
    xpos 1.04
    easein 2 xpos 1.0

transform mmaleftdodge:
    subpixel True
    xanchor 1.0
    xpos 1.0
    easein 2 xpos 1.06

transform mmaleftpower:
    subpixel True
    xanchor 1.0
    yanchor 1.0
    xpos 1.0
    ypos 1.02
    easein 2 xpos 0.98 ypos 1.0

transform mmaleftcounter:
    subpixel True
    xanchor 1.0
    yanchor 1.0
    xpos 1.0
    ypos 1.0
    easein 2 xpos 0.98 ypos 1.02

# up
transform mmaup:
    subpixel True
    yanchor 1.0
    ypos 1.02
    easein 2 ypos 1.0

transform mmakickkobg:
    subpixel True
    zoom 1.1
    xalign 0.5
    yalign 0.5
    easein 0.5 xalign 0.55 yalign 0.6

transform mmakickko:
    subpixel True
    xanchor 1.0
    yanchor 1.0
    xpos 1.0
    ypos 1.04
    easein 0.5 xpos 1.01 ypos 1.0

# lowkick
transform mmalk:
    subpixel True
    yanchor 1.0
    ypos 1.0
    xanchor 1.0
    xpos 1.0
    easein 2 ypos 1.01 xpos 1.02

# slam

transform mmaslambg1:
    subpixel True
    zoom 1.1
    yalign 0.7
    easein 2 yalign 0.5
transform mmaslam1:
    subpixel True
    yanchor 1.0
    ypos 1.08
    easein 2 ypos 1.0


##################################################################################################################################################################################################################
## AXEL-LENA  ##################################################################################################################################################################################################################
##################################################################################################################################################################################################################

label v9lenaaxel_1:
    # HOME GETTING READY
    stop music fadeout 2.0
    scene lenaroom with long
    play sound "sfx/shower.mp3"
    "I went back home to get ready."
    $ flena = "n"
    $ lena_look = 1
    show lenanude2 with short
    "I took a shower and picked a set of underwear and a pair of high heels for the shoot."
    if v6_axel_pose > 1:
        $ flena = "blush"
    else:
        $ flena = "worried"
    "I felt the anxiety growing in my chest. I tried easing it by taking a deep breath and exhaling slowly."
    if v6_axel_work:
        $ flena = "n"
        l "We've already worked together once. And this time the camera will be between us."
        l "It's just another photo shoot, nothing else. And Ivy will be there, too."
    else:
        "I hadn't been alone with Axel since..."
        "I couldn't really remember. The closest thing was that time we met at the café, but I had had Molly to watch over me."
        $ flena = "n"
        l "This time I'll have Ivy with me..."
    if v6_axel_pose == 3:
        $ lena_look = "sexy"
        hide lenanude2 with short
        $ lena_makeup = 1
        "I decided to wear the underwear and the shoes. No point in packing them..."
        show lena with short
        "After putting some makeup on I was ready to leave."
    elif v6_axel_pose == 2:
        $ lena_look = 4
        hide lenanude2
        show lena
        with short
        "I put on the underwear and some clothes, packed the shoes, and got ready to leave."
    else:
        hide lenanude2
        show lena
        with short
        "I packed the underwear and the shoes and got dressed."
    l "Time to go."
    scene street with long
    show lena with short
    "I texted Ivy while on my way to Axel's place."
    nvl clear
    l_p "{i}I just left home, see you at Axel's door?{/i}"
    "When I arrived she hadn't answered my text yet. And she wasn't there..."
    $ flena = "sad"
    l "I'll call her..."
    hide lena
    show lena_phone
    with short
    play sound "sfx/ring.mp3"
    l "..."
    l "... ..."
    l "... ... ..."
    $ flena = "serious"
    l "She's not picking up..."
    "I tried again."
    play sound "sfx/ring.mp3"
    l "..."
    l "... ..."
    l "... ... ..."
    show phone_ivy at lef3 with short
    v "Yes?"
    "She finally answered my call. I heard the sound of traffic behind Ivy's voice."
    l "What do you mean, yes? Where are you?"
    v "I'm stuck in traffic!"
    $ flena = "sad"
    l "Where are you?"
    v "I was out of town and I'm trying to get back, but you know how the roads get during weekends!"
    $ flena = "mad"
    l "What were you doing out of town? You were supposed to come with me to the photo shoot!"
    if lena_ivy > 0:
        call friend_xp('ivy', -1) from _call_friend_xp_160
    v "I know, I'm trying to get there!"
    $ flena = "sad"
    l "How long will you take?"
    v "I don't know... Thirty minutes, maybe an hour."
    $ flena = "worried"
    l "An hour?!"
    v "Go ahead and get started, I'll be there soon!"
    l "But..."
    v "Come on, we've talked about this! I know you'll be alright."
    v "Gotta hang up, see you in a bit!"
    hide phone_ivy
    hide lena_phone
    show lena
    with short
    $ flena = "serious"
    l "She always does something like this...! Now what?"
    if v6_axel_pose > 1:
        $ flena = "blush"
    else:
        $ flena = "worried"
    l "..."
    $ flena = "serious"
    l "Come on, Lena. You're not a helpless child. Not anymore."
    $ flena = "n"
    l "I can do this."
    hide lena with short
    "I entered the building, got up the elevator, and rang Axel's doorbell."
    play sound "sfx/doorbell.mp3"
    scene axelhome with long
    $ faxel = "smile"
    pause 0.5
    show lena2 at rig
    show axel at lef
    with short
    x "Hey, Lena..."
    l "Hi..."
    # MEET IAN
    if v9_cindy_shoot == 3:
        if v9_ianwearing == "wits":
            $ ian_look = "wits1"
        elif v9_ianwearing == "charisma":
            $ ian_look = "charisma1"
        elif v9_ianwearing == "athletics":
            $ ian_look = "athletics1"
        elif v9_ianwearing == "lust":
            $ ian_look = "lust1"
        else:
            $ ian_look = 2
        $ fian = "worried"
        $ fcindy = "surprise"
        show lena2 at right
        show axel at rig
        with move
        show cindy2 at lef
        show ian at left
        with short
        i "Lena."
        $ flena = "surprise"
        l "Wha--?"
        if ian_lena_dating:
            "My heart skipped a beat. Ian was the last person I was expecting to find there. And he was with that blonde girl, Cindy..."
            if axel_knows_dating:
                "What the hell was he doing in Axel's place? Especially after I told him he and I were involved..."
            else:
                "What the hell was he doing in Axel's place? Did Axel know about us...?"
        else:
            "Ian was the last person I was expecting to find there. And he was with that blonde girl, Cindy..."
            l "What are you guys doing here?"
        x "I just finished shooting with Cindy. Do you know each other?"
        $ fcindy = "serious"
        $ flena = "worried"
        c "We met. Once."
        if axel_knows_dating or v4_ian_date == False:
            $ faxel = "n"
            x "And of course, you have Ian as a mutual friend. Small world."
            $ faxel = "smile"
            x "Anyway, they were just leaving. Right, guys?"
        else:
            x "Really? How come?"
            $ fcindy = "n"
            c "Ian and Lena are pretty well {i}acquainted{/i}."
            $ faxel = "sad"
            "Damn her! Why did she have to say that? It was the worst possible moment for Axel to learn about Ian and me...!"
            $ faxel = "n"
            x "I had no idea. How {i}acquainted{/i} are you, exactly?"
            $ flena = "serious"
            "This was bad. The last thing I needed was for Axel to cause another incident."
            l "I think it'll be better if I come back another time."
            $ faxel = "n"
            x "No, wait..."
            $ faxel = "smile"
            x "They were just leaving. Right, guys?"
            $ flena = "sad"
        $ fian = "n"
        $ fcindy = "sad"
        i "Yeah."
        c "Send me the pictures when you have them, okay?"
        x "Sure, I will."
        hide cindy2 with short
        show ian at rig
        show axel at lef3
        with move
        $ fian = "sad"
        "Ian walked up to me before leaving and talked to me quietly."
        i "Is everything okay...?"
        $ flena = "sad"
        "This wasn't the moment for elaborate explanations. The sooner he left, the better."
        l "Yeah... I needed some help finding work as a model and Ivy insisted I contact Axel."
        if ian_lena_couple:
            i "Are you sure this is a good idea?"
            $ flena = "n"
            l "It'll be fine, don't worry."
            i "Alright. Call me if you need anything, okay?"
            $ flena = "smile"
            l "I will. Thanks, Ian."
        elif ian_lena_dating:
            i "Are you sure this is a good idea?"
            $ flena = "n"
            l "It'll be fine, don't worry..."
            i "Alright. Let's talk soon, okay?"
            l "We will. Thanks, Ian."
        else:
            i "I see. Will you be fine?"
            $ flena = "n"
            l "Yes, you don't have to worry."
            i "Alright. Take care."
            l "Thanks, Ian."
        hide ian with short
        if ian_lena_couple:
            $ flena = "sad"
            "I could only imagine what was going through Ian's head. Nothing good, that much was obvious."
            "What a shitty situation... I would need to explain everything to him later."
        elif ian_lena_dating:
            $ flena = "sad"
            "Ian was probably pretty confused with the situation, and so was I..."
        play sound "sfx/door.mp3"
        show lena2 at rig
        show axel at lef
        with move
        "Axel closed the door behind Ian. We were alone."
        x "So, do you wanna get right to it, or...?"
        $ flena = "sad"
        l "Let's just wait for Ivy. She should be here any minute now."
        x "Alright. Do you want something to drink?"
        l "No, I'm good."
    # CINDY ALONE
    else:
        play music "music/cindys_theme.mp3" loop
        x "Come on in."
        $ fcindy = "blush"
        show cindynude2 at left with short
        $ flena = "worried"
        l "Oh. I see you're busy..."
        x "We were just about done when you rang, right, Cindy?"
        c "Yeah."
        "She was already putting her clothes back on."
        "It was obvious Axel had been shooting her, too. He still had the camera in his hand."
        hide cindynude2
        hide lena2
        show lena at rig
        show cindy at left
        with short
        $ fcindy = "n"
        $ flena = "n"
        show cindy at lef3
        show lena at rig3
        show axel at truecenter
        with move
        x "I was just showing the results of the session to Cindy."
        c "I think I did okay... Better than the other times, at least."
        x "Yeah, you did. You're improving so fast..."
        x "You're really talented!"
        hide cindy
        show cindy2 at lef3
        with short
        c "So you've been saying."
        $ faxel = "happy"
        x "So maybe it's time you started to believe me."
        $ flena = "worried"
        x "I'm sure one of the big agencies I work with would hire you, no doubt."
        x "You just need to build a slightly bigger portfolio and they will fight to sign you."
        c "You really think so!?" with vpunch
        $ fcindy = "blush"
        hide cindy2
        show cindy at lef3
        with short
        c "..."
        $ fcindy = "n"
        c "Anyway, I guess I should get going. You have work to do and I don't want to keep bothering you..."
        $ faxel = "smile"
        x "You couldn't be a bother to me even if you tried."
        $ fcindy = "shy"
        x "I'll send you the edited pictures as soon as I can."
        c "Thanks!"
        show cindy at truecenter
        show axel at lef3
        with move
        stop music fadeout 2.0
        $ fcindy = "flirt"
        c "Bye, Lena."
        play sound "sfx/door.mp3"
        hide cindy with short
        l "What's the matter with this girl...?"
        show axel at lef
        show lena at rig
        with move
        x "Do you want something to drink?"
        $ flena = "n"
        l "Uh? No, I'm good."
        x "So, do you wanna get right to it, or...?"
        $ flena = "sad"
        l "Let's just wait for Ivy. She should be here any minute now."
    x "Okay."
    $ flena = "n"
    l "..."
    $ faxel = "n"
    x "..."
    $ faxel = "smile"
    if v6_axel_pose == 3:
        x "You look so beautiful today, Lena. I can see you're doing good."
    elif v6_axel_pose == 2:
        x "You're looking really nice today, Lena. I can see you're doing good."
    else:
        x "You look as good as ever, Lena. I hope you're doing good."
    menu:
        "{image=icon_friend.webp}I'm managing" if lena_axel > 1:
            $ renpy.block_rollback()
            $ axel_disposition = 2
            $ flena = "sad"
            l "I'm managing... You know how things usually are for me."
            $ faxel = "n"
            x "Yeah... Is your dad still doing good?"
            l "Yeah, he is, but..."
            "I was about to tell Axel about my mother but decided against it."
            "Talking about personal stuff wasn't the reason I came here..."
            $ flena = "n"
            l "Forget it. All you need to know is I'm handling things."
            $ faxel = "smile"
            x "You always have."

        "I'm good":
            $ renpy.block_rollback()
            $ axel_disposition = 1
            "I didn't want to talk about personal stuff, so I gave him an empty answer."
            l "I'm good."
            x "Really? Is your dad still doing good?"
            l "Yeah."
            x "I'm glad."
            l "..."
            $ faxel = "n"
            x "..."
            $ faxel = "smile"

        "{image=icon_mad.webp}I'm not here to talk" if lena_axel < 2:
            $ renpy.block_rollback()
            $ axel_disposition = 0
            $ flena = "serious"
            l "I don't remember asking for your opinion on how I look today."
            $ faxel = "n"
            x "Sure, I was just trying to break the ice..."
            l "Axel, I'm not here to make small talk, so if you don't mind..."
            $ faxel = "n"
            x "Of course."
            l "..."
            x "..."

    # ask about ian
    if v9_cindy_shoot == 3:
            if axel_knows_dating:
                x "So this is the guy, huh? Ian..."
                $ flena = "worried"
                "There it was. I was hoping Axel wouldn't bring it up, but he was."
                x "He seems like a nice dude, even if he's a bit... average."
                if axel_disposition == 2:
                    l "Axel, I don't feel comfortable talking about this subject..."
                    x "Of course. My bad."
                    x "Just trying to kill some time until Ivy gets here..."
                elif axel_disposition == 1:
                    $ flena = "sad"
                    l "..."
                    $ faxel = "n"
                    x "..."
                    x "Sorry, I shouldn't pry."
                    x "Just trying to kill some time until Ivy gets here..."
                elif axel_disposition == 0:
                    $ flena = "serious"
                    l "Remember what I just told you about not having asked for your opinion?"
                    if lena_axel > 0:
                        call friend_xp('axel', -1) from _call_friend_xp_161
                    $ faxel = "n"
                    x "Sure. Sorry, just trying to kill some time until Ivy gets here."
            else:
                $ faxel = "n"
                x "So you and Ian know each other, huh? Small world..."
                if ian_lena_dating or ian_lena_over or ian_lena_breakup:
                    $ flena = "worried"
                    "That incriminating comment Cindy made had Axel on edge, of course."
                    "Even if his suspicions were right, I didn't want to entertain them."
                    l "We're friends, yeah."
                    x "You've always attracted people, but it was always hard for you to make real {i}friends{/i}."
                    if axel_disposition == 2:
                        $ flena = "sad"
                        l "Yeah. Look, Axel, can we talk about something else?"
                        x "Of course. My bad."
                        x "Just trying to kill some time until Ivy gets here..."
                    elif axel_disposition == 1:
                        $ flena = "sad"
                        l "..."
                        x "..."
                        x "Sorry, I shouldn't pry."
                        x "Just trying to kill some time until Ivy gets here..."
                    elif axel_disposition == 0:
                        $ flena = "serious"
                        l "Remember what I just told you about not having asked for your opinion?"
                        if lena_axel > 0:
                            call friend_xp('axel', -1) from _call_friend_xp_162
                        x "Sure. Sorry, just trying to kill some time until Ivy gets here."
                else:
                    l "We're friends."
                    x "You've always attracted people, but it was always hard for you to make real {i}friends{/i}."
                    if axel_disposition > 0:
                        l "Well, he {i}is{/i} my friend, and I've made some new ones as well."
                        x "I'm happy to hear."
                    elif axel_disposition == 0:
                        $ flena = "serious"
                        l "Remember what I just told you about not having asked for your opinion?"
                        if lena_axel > 0:
                            call friend_xp('axel', -1) from _call_friend_xp_163
                        $ faxel = "n"
                        x "Sure. Sorry, just trying to kill some time until Ivy gets here."
    else:
        if axel_knows_dating:
            x "I've heard you've been dating someone recently..."
            $ flena = "worried"
            "There it was. I knew Ian had told him, but I was hoping Axel wouldn't bring it up."
            if axel_disposition == 2:
                l "Axel, I don't feel comfortable talking about this subject..."
                x "Of course. My bad."
                x "Just trying to kill some time until Ivy gets here..."
            elif axel_disposition == 1:
                $ flena = "sad"
                l "..."
                $ faxel = "n"
                x "..."
                x "Sorry, I shouldn't pry."
                x "Just trying to kill some time until Ivy gets here..."
            elif axel_disposition == 0:
                $ flena = "serious"
                l "What do you care? Not that I would tell you if that was the case..."
                if lena_axel > 0:
                    call friend_xp('axel', -1) from _call_friend_xp_164
                $ faxel = "n"
                x "Sure. Sorry, I shouldn't have said anything."
                x "Just trying to kill some time until Ivy gets here."
        else:
            if ian_lena_dating or ian_lena_over or ian_lena_breakup:
                x "I've heard you've been dating someone recently..."
                $ flena = "worried"
                l "\"You've heard?\" Where?"
                $ faxel = "n"
                x "Ivy might've mentioned something..."
                $ flena = "serious"
                l "You've been questioning her?"
                x "I just wanted to know if you were doing okay, nothing else."
            else:
                x "I've heard you've been making new friends recently."
                $ flena = "worried"
                l "\"You've heard?\" Where?"
                x "Ivy mentioned something..."
                $ flena = "serious"
                l "You've been questioning her?"
                $ faxel = "n"
                x "I just wanted to know if you were doing okay, nothing else."
            if axel_disposition == 2:
                $ flena = "n"
                l "As I said, I'm handling things. You don't need to worry about me."
                $ faxel = "smile"
                x "Of course. I shouldn't pry..."
                x "Just trying to kill some time until Ivy gets here."
            elif axel_disposition == 1:
                $ flena = "n"
                l "As I said, I'm doing okay."
                x "..."
                $ faxel = "smile"
                x "Sorry, I shouldn't pry."
                x "Just trying to kill some time until Ivy gets here..."
            elif axel_disposition == 0:
                $ flena = "serious"
                l "Remember what I just told you about not being here to make small talk?"
                if lena_axel > 0:
                    call friend_xp('axel', -1) from _call_friend_xp_165
                x "Sure. Sorry, just trying to kill some time until Ivy gets here."
    l "..."
    $ faxel = "smile"
    x "..."
    $ flena = "worried"
    "I had no idea what was worse, making conversation with Axel or this tense silence. I was so uncomfortable..."
    "And Ivy wasn't showing up...!"
    x "So are you sure you don't want anything to drink...?"
    $ flena = "sad"
    l "No. I guess we can get started."
    x "Alright. I didn't want to say anything, but we're losing valuable daylight by the minute."
# PHOTO SHOOT STARTS
    label gallery_CH09_S10:
        if _in_replay:
            call setup_CH09_S10 from _call_setup_CH09_S10

    x "Are you ready?"
    $ flena = "n"
    if v6_axel_pose == 3:
        l "Yes. We can get started right away."
        "I took off my clothes while Axel prepared the camera."
    elif v6_axel_pose == 2:
        l "I just need to put on a bit of makeup..."
        x "Alright. You know where the bathroom is."
    else:
        l "I need to get changed and put on a bit of makeup."
        x "Sure. You know where the bathroom is."
    hide lena2
    hide lena
    with short
    $ lena_makeup = 1
    $ lena_look = "underwear2"
    pause 1
    show lenabra2 at rig with long
    l "Ready."
    stop music fadeout 2.0
    x "Alright. Stand over there... We have some nice sunset light."
    play music "music/shooting.mp3" loop
    scene v9_lena_shoot1 with long
    "I struck a pose in front of the camera."
    play sound "sfx/camera.mp3"
    with flash
    pause 1
    if axel_disposition == 2:
        "I tried to get into my modeling mindset like I always did when I worked with photographers."
        "This was just another photo shoot, and a potentially profitable one. Still..."
    elif axel_disposition == 1:
        "I tried to get into my modeling mindset like I always did when I worked with photographers."
        "Of course, it was proving more challenging than usual in this case..."
    else:
        "I wanted to get this over with as soon as possible..."
    "It felt so strange posing for Axel once again. So much had changed since the last time he shot me..."
    x "Can you give me a cheekier attitude, Lena?"
    x "Wildcats' preferred style is glamorous, but also playful and sultry."
    menu:
        "{image=icon_friend.webp}Follow Axel's directions" if axel_disposition > 0:
            $ renpy.block_rollback()
            $ v9_axel_pose = 1
            if axel_disposition < 2:
                $ axel_disposition += 1
            scene v9_lena_shoot2
            if lena_tattoo2:
                show v9_lena_shoot2_t2
            if lena_piercing1:
                show v9_lena_shoot2_p1
            elif lena_piercing2:
                show v9_lena_shoot2_p2
            with long
            pause 1
            "I followed Axel's instructions, adjusting my pose."
            if lena_lust < 9:
                call xp_up('lust') from _call_xp_up_116
            x "That's it! You haven't lost your intuition when it comes to posing..."
            x "Working with you, it's always so easy."
            play sound "sfx/camera.mp3"
            with flash
            "Posing for Axel was being surprisingly easy, too. Weirdly so."
            "I had reacted to his directions without hesitating, almost instinctively..."

        "Ignore Axel's directions":
            $ renpy.block_rollback()
            l "Alright..."
            "I nodded but kept doing what I was doing, ignoring his directions."
            "He probably knew best what the agency would appreciate, but I wasn't exactly comfortable getting \"cheeky\" in front of Axel."
            "It was hard enough trying to act naturally given the circumstances..."
            play sound "sfx/camera.mp3"
            with flash

        "{image=icon_mad.webp}Take charge" if axel_disposition < 2:
            $ renpy.block_rollback()
            if axel_disposition > 0:
                $ axel_disposition -= 1
            l "I know what I'm doing, thank you."
            x "Sure... You're the model."
            play sound "sfx/camera.mp3"
            with flash
            "I wasn't in the mood to indulge Axel's request, nor to get \"cheeky\" in front of him."
            "He probably knew best what the agency would appreciate, but it was I who was in control of the situation."
            "I wouldn't have it any other way."
            if lena_charisma < 9:
                call xp_up('charisma') from _call_xp_up_117

    scene v9_lena_shoot2
    if lena_tattoo2:
        show v9_lena_shoot2_t2
    if lena_piercing1:
        show v9_lena_shoot2_p1
    elif lena_piercing2:
        show v9_lena_shoot2_p2
    show v9_lena_shoot2_axel
    with long
    pause 1
    "We continued to shoot in Axel's living room as the last sunrays of the day filtered through the windows."
    if v9_axel_pose == 1:
        scene v9_lena_shoot2b
        if lena_tattoo2:
            show v9_lena_shoot2b_t2
        if lena_piercing1:
            show v9_lena_shoot2_p1
        elif lena_piercing2:
            show v9_lena_shoot2_p2
        show v9_lena_shoot2_axel
        with long
        pause 1
        "I tried to keep that playful attitude Axel had asked from me."
    else:
        "I kept doing my thing, cycling through poses I thought would fit Wildcats' aesthetic taste."
    play sound "sfx/camera.mp3"
    with flash
    "I was trying to act as if I wasn't aware of the tension in the atmosphere, but I wasn't sure I was doing a good job."
    if axel_disposition == 2:
        "I had to admit I felt more comfortable than I had anticipated, but I couldn't forget who I was posing for."
        "Axel... He had been in my thoughts during all these months, still messing with my emotions."
        "Was it still too early for me to be in this situation with him again?"
    elif axel_disposition == 1:
        "I wasn't working with some random photographer, after all."
        "Having Axel in front of me was stirring all those troubling emotions I had regarding him..."
        "It just felt weird being in this situation with him again after all that had happened."
    else:
        "Memories of all the pain and suffering Axel had caused me were poking at my brain. It was hard not focusing on them."
        "Maybe it was too early to be in this situation with him again, especially after all that had happened."
        "I didn't like having to ask him for help..."
    "And Ivy hadn't shown up yet..."
    $ flena = "n"
    $ faxel = "smile"
    scene axelhomenight
    show axel at lef
    show lenabra2 at rig
    with long
    x "It's getting dark. We need some other light sources."
    x "What do you think about taking the last pictures upstairs, on the bed?"
    x "Only if you're comfortable with it, of course."
    menu:
        "{image=icon_friend.webp}Move it to the bed" if (axel_disposition > 0 and axel_pictures_watch) or lena_axel_desire:
            $ renpy.block_rollback()
            $ v9_axel_pose = 2
            if axel_disposition < 2:
                $ axel_disposition += 1
            l "Sure..."
            x "Let's go."
            hide axel with short
            $ flena = "blush"
            "On second thought, Axel's proposition sounded a bit compromising..."
            "But he had been perfectly respectful thus far, acting like a professional. I would do the same."
            hide lenabra2 with short
            "I followed Axel to the second floor of the loft, where his bedroom space was."
            stop music fadeout 2.0
            play music "music/sensual.mp3" loop
            scene v9_lena_shoot5 with long
            pause 1
            "Up to this point, everything had been going smoothly, so I was confident. But when I lay down on that bed once again, I..."
            x "Great pose. Look at the camera..."
            play sound "sfx/camera.mp3"
            with flash
            "Conflicting emotions began swelling inside of me. Memories from the past, good and bad, sweet and sour."
            "I squirmed, trying to shake off this sense of discomfort."
            scene v9_lena_shoot3b
            if lena_tattoo2:
                show v9_lena_shoot3_t2
            if lena_tattoo3:
                show v9_lena_shoot3_t3
            with long
            pause 1
            play sound "sfx/camera.mp3"
            with flash
            if lena_axel_desire:
                "My heart was beating like I was about to jump from a bridge. I felt the arousal of imminent danger..."
                "Memories of our last photo shoot together started invading my mind. And that damn dream felt so real..."
                "As real as the images on those old Polaroids I had masturbated to."
            elif v6_axel_pose == 3:
                "My heart was beating like I was about to jump from a bridge. I felt the excitement of imminent danger."
                "Memories of our last photo shoot together started invading my mind. And the times I had masturbated looking at those old Polaroids, too..."
            elif v6_axel_pose == 2:
                "I couldn't help but think about our last photo shoot together..."
                "I felt something similar then, but I had the sense to put a stop to it before it got too... erotic."
            elif v6_axel_pose == 1:
                "I had refused to pose with him for a reason that time. Not only because I was still uncomfortable around him..."
                "But because I still wasn't completely over him. I had proved that to myself when I masturbated to those old Polaroids..."
            else:
                "I told myself it was only natural. I hadn't been in Axel's bed since..."
                "Unwanted memories started invading my mind. From the past, and from the time I had masturbated to those old Polaroids..."
                "I didn't want to think about it. I didn't want to admit it to myself."
            scene v9_lena_shoot4
            if lena_tattoo2:
                show v9_lena_shoot4_t2
            if lena_tattoo3:
                show v9_lena_shoot4_t3
            with long
            pause 1
            if lena_axel_desire:
                "I pressed my thighs together, trying to contain the warm tingling that was starting to spread through my lower body."
            elif v6_axel_pose == 3:
                "And now I was starting to feel just like those times..."
            elif v6_axel_pose == 2:
                "I had been trying to deny it, but it was obvious: I wasn't over him yet."
                "I had proved that to myself when I masturbated to those old Polaroids, and now I was starting to feel just like that time...!"
            elif v6_axel_pose == 1:
                "And now I was starting to feel just like that time...!"
            else:
                "But the truth was I wasn't over Axel yet..."
            "The bed sheets were impregnated with Axel's scent, mixed with the perfume he always used. The one I used to like."
            "I had spent so many nights in this bed, naked, sweating, and moaning under Axel's body."
            play sound "sfx/camera.mp3"
            with flash
            x "That's perfect, Lena..."
            "Hearing him praise me was like a small rush of dopamine, making me want more."
            if lena_tattoo1 or lena_tattoo2 or lena_tattoo3:
                x "I never imagined you with a tattoo, but it looks great on you. Seems like you're coming into your own more and more."
            "I was aware I was falling into the pitfalls of the past, getting trapped in memories of those intense experiences..."
            "My rational mind wanted me to snap out of it before I sunk even deeper, but it was like it had no strength to do so."
            "Emotion had taken the reigns and I was just strapped in for the ride..."
            play sound "sfx/camera.mp3"
            scene v9_lena_shoot5a with flash
            "Ironically enough, it was Axel who snapped me out of it."
            $ flena = "blush"
            stop music fadeout 2.0
            scene axelhomenight
            show lenabra2 at rig3
            show axel at lef3
            with long
            x "Alright, I think I have enough material. It's getting a bit late anyway."
            l "Sure..."
            "I got up from the bed, slowly, trying to regain my footing. This whole situation had thrown me off so much...!"
            x "Some of these shots came out really good. Do you want to check them?"
            l "Yeah."
            x "Let's go downstairs."
            hide axel with short
            l "..."
            "I let out a long exhalation before following him, trying to release the tension."
            "What the hell was I doing...?"
            show lenabra2 at rig with move
            show axel at lef with short

        "I don't feel comfortable with that":
            $ renpy.block_rollback()
            if axel_disposition == 2:
                $ axel_disposition -= 1
            $ flena = "sad"
            "Axel had been perfectly respectful thus far, acting like a professional, but I still felt that'd be a bad idea."
            l "I'm sorry, but I don't feel comfortable with that..."
            x "I understand."
            x "Let's take a couple more pics and call it done, then."
            jump v9axelsofa

        "{image=icon_mad.webp}No way" if axel_disposition < 2 and lena_axel_desire == False:
            $ renpy.block_rollback()
            $ flena = "serious"
            if axel_disposition == 1:
                $ axel_disposition = 0
                "So far Axel had been behaving himself, but this proposition was completely out of place."
                "I didn't like it."
            l "Are you serious? I'm not gonna lay down on your bed..."
            $ faxel = "n"
            x "I understand. Sorry if I was out of place, sometimes I still forget..."
            l "What?"
            $ faxel = "smile"
            x "Nothing. Let's take a couple more pics and call it done."
            label v9axelsofa:
                scene v9_lena_shoot3
                if lena_tattoo2:
                    show v9_lena_shoot3_t2
                if lena_tattoo3:
                    show v9_lena_shoot3_t3
                with long
                pause 1
            "I continued to pose on Axel's couch."
            "It was faint, but when I lay down on it, I could smell Axel's scent in it, mixed with that perfume he always used. The one I used to like."
            if lena_tattoo1 or lena_tattoo2 or lena_tattoo3:
                x "I never imagined you with a tattoo, but it looks great on you. Seems like you're coming into your own more and more."
                l "..."
            if axel_disposition == 2:
                "Conflicting emotions began swelling inside of me. Memories from the past, good and bad, sweet and sour."
                if lena_axel_desire:
                    $ flena = "blush"
                    "Memories of our last photo shoot together started invading my mind. And that damn dream felt so real..."
                    "My heart started beating like I was about to jump from a bridge. Suddenly I felt like a deer in headlights."
                    "I pressed my thighs together, trying to contain the warm tingling that was starting to spread through my lower body."
                    if axel_pictures_watch:
                        "It was the same anticipatory feeling I had when I masturbated to those old Polaroids..."
                    "I felt I was losing control of the situation again, but this time Axel and I were completely alone."
                    "I was letting things play out, unsure of what to do, but then Axel called a stop to the shoot."
                elif v6_axel_pose == 3:
                    $ flena = "blush"
                    "Memories of our last photo shoot together started invading my mind. Axel's hands on me, his naked body pressed against mine."
                    "My heart started beating like I was about to jump from a bridge. Suddenly I felt like a deer in headlights."
                    if axel_pictures_watch:
                        "It was the same anticipatory feeling I had when I masturbated to those old Polaroids..."
                    "I was wrestling with my emotions, trying to regain control, but then Axel called a stop to the shoot."
                elif v6_axel_pose == 2:
                    $ flena = "worried"
                    "I couldn't help but think about our last photo shoot together..."
                    "I felt something similar then, but I had the sense to put a stop to it before it got too... erotic."
                    if axel_pictures_watch:
                        "I had been trying to deny it, but it was obvious: I wasn't over him yet."
                        "I had proved that to myself when I masturbated to those old Polaroids, and now I was starting to feel just like that time...!"
                    else:
                        "I had been trying to deny it, but the truth was I wasn't completely over him yet."
                    "I kept going with the photo shoot while thinking about these things until Axel called a stop to it."
                elif v6_axel_pose == 1:
                    $ flena = "sad"
                    "I had refused to pose with him for a reason that time. I didn't want him to stir up my emotions. I wanted to move on."
                    "It wasn't easy after what happened, of course. But I wanted to get rid of these chains."
                    if axel_pictures_watch:
                        "Still, I ended up masturbating to those old Polaroids..."
                        "I had been trying to deny it, but it was obvious: I wasn't over him yet."
                        "I kept going with the photo shoot while thinking about these things until Axel called a stop to it."
                    else:
                        "I had decided that long ago."
                        "Still, I kept going with the photo shoot until Axel called a stop to it."
                else:
                    $ flena = "sad"
                    "I hated it. I didn't want him to stir up my emotions. I wanted to move on."
                    "It wasn't easy after what happened, of course. But I wanted to get rid of these chains."
                    if axel_pictures_watch:
                        "Still, I ended up masturbating to those old Polaroids..."
                        "I had been trying to deny it, but it was obvious: I wasn't over him yet."
                        "I kept going with the photo shoot while thinking about these things until Axel called a stop to it."
                    else:
                        "I had decided that long ago."
                        "Still, I kept going with the photo shoot until Axel called a stop to it."
                stop music fadeout 2.0
                scene axelhomenight
                show lenabra2 at rig3
                show axel at lef3
                with long
                x "Alright, I think I have enough material. It's getting a bit late anyway."
                l "Sure..."
            elif axel_disposition == 1:
                "Memories and emotions continued to fight me, wanting to be noticed. Echoes from the good, happy times, and from the painful disaster that came after..."
                if v6_axel_pose == 3:
                    if lena_axel_desire:
                        "Memories of our last photo shoot together, and that damn dream which felt so real..."
                    else:
                        "Memories of our last photo shoot together, of Axel's hands on me..."
                    "My heart started beating like I was about to jump from a bridge. Suddenly I felt like a deer in headlights."
                    if axel_pictures_watch:
                        "It was the same anticipatory feeling I had when I masturbated to those old Polaroids..."
                    "I was wrestling with my emotions, trying to regain control. I had to put a stop to this..."
                elif v6_axel_pose == 2:
                    "I couldn't help but think about our last photo shoot together..."
                    "It had threatened to get out of hand, but I had had the sense to put a stop to it before it got too... erotic."
                    if axel_pictures_watch:
                        "I had been trying to deny it, but it was obvious: I wasn't over him yet."
                        "I had proved that to myself when I masturbated to those old Polaroids, and now I was starting to feel just like that time...!"
                    else:
                        "I had been trying to deny it, but the truth was I wasn't completely over him yet."
                    "I wasn't liking this... I decided to put a stop to the situation."
                elif v6_axel_pose == 1:
                    "I had refused to pose with him for a reason that time. I didn't want him to stir up my emotions. I wanted to move on."
                    "It wasn't easy after what happened, of course. But I wanted to get rid of these chains."
                    if axel_pictures_watch:
                        "Still, I ended up masturbating to those old Polaroids..."
                        "I had been trying to deny it, but it was obvious: I wasn't over him yet."
                    else:
                        "I had decided that long ago."
                    "The shoot had gone on for long enough. I decided to put a stop to it."
                else:
                    "I hated it. I didn't want him to stir up my emotions. I wanted to move on."
                    "It wasn't easy after what happened, of course. But I wanted to get rid of these chains."
                    if axel_pictures_watch:
                        "Still, I ended up masturbating to those old Polaroids..."
                        "I had been trying to deny it, but it was obvious: I wasn't over him yet."
                        "I kept going with the photo shoot, unsure of what to do until Axel called a stop to it."
                    else:
                        "I had decided that long ago."
                    "The shoot had gone on for long enough. I decided to put a stop to it."
                $ flena = "sad"
                stop music fadeout 2.0
                scene axelhomenight
                show lenabra2 at rig3
                show axel at lef3
                with long
                l "I think we took enough pictures already. Let's stop here."
                x "Sure. It's starting to get late anyway."
            else:
                "I was annoyed by the old memories and feelings that were being brought back..."
                "I already knew this situation would be emotionally demanding, but I was starting to feel drained and impatient."
                "We had been shooting for a while now, and Ivy hadn't even shown up yet."
                "I had no idea if these shots would make Wildcats take interest in me, but this had gone on long enough."
                $ flena = "n"
                stop music fadeout 2.0
                scene axelhomenight
                show lenabra2 at rig3
                show axel at lef3
                with long
                l "I think we took enough pictures already. Let's stop here."
                x "As you wish. It's starting to get late anyway."
            x "Some of these shots came out really good. Do you want to check them?"
            $ flena = "n"
            if axel_disposition > 0:
                l "Sure..."
            else:
                l "No need. I'll get dressed if you don't mind."
            show lenabra2 at rig
            show axel at lef
            with move
            $ renpy.end_replay()
            $ gallery_unlock_scene("CH09_S10")
# end shoot
    x "Where's Ivy, by the way? Wasn't she supposed to come, too?"
    $ flena = "serious"
    l "Yeah, she was..."
    show lenabra2 at rig3 with move
    "I searched between my clothes and picked up my phone. I had received a text from her a few minutes ago."
    nvl clear
    v_p "{i}Almost there. Do you still need me?{/i}"
    play sound "sfx/sms.mp3"
    if axel_disposition == 2:
        $ flena = "sad"
        l_p "{i}No, we just finished. Everything went okay, even if you flaked on me {image=emoji_roll.webp}{/i}"
    elif axel_disposition == 1:
        l_p "{i}We just finished. I could've used you here, you know? {image=emoji_mad.webp}{/i}"
        $ flena = "sad"
        "I had only agreed to go through with this because Ivy had convinced me, and then she flaked on me..."
    else:
        l_p "{i}Come and pick me up. We just finished {image=emoji_mad.webp}{/i}"
        "I was fuming... I had only agreed to go through with this because Ivy had convinced me, and then she flaked on me..."
    x "So?"
    if axel_disposition > 0:
        l "Well, she didn't make it, obviously."
        $ faxel = "happy"
        x "I hope she makes it up to you!"
        $ flena = "n"
        l "She'd better..."
        $ renpy.end_replay()
        $ gallery_unlock_scene("CH09_S10")
    else:
        $ flena = "n"
        l "She's coming to pick me up. I'll wait for her down on the street."
        $ faxel = "n"
        hide lenabra2 with short
        if v6_axel_pose == 3:
            $ lena_look = "sexy"
        elif v6_axel_pose == 2:
            $ lena_look = 4
        else:
            $ lena_look = 1
        "The deed was done, so I got dressed quickly, eager to get going."
        show lena at rig with short
        x "Are you leaving already?"
        l "Yes. Let me know when you hear from Wildcats."
        x "Of course. It was good seeing you, Lena."
        l "Bye."
        play sound "sfx/door.mp3"
        scene streetnight with long
        $ flena = "sad"
        show lena with short
        "I let out a big sigh when I got to the street."
        l "Finally, it's over..."
        if lena_will < 2:
            call will_up() from _call_will_up_55
        $ flena = "n"
        l "It was tense, but Axel behaved himself."
        $ flena = "sad"
        l "Still, I'm not exactly looking forward to repeating the experience..."
        play sound "sfx/car.mp3"
        $ ivy_look = "sexy"
        $ fivy = "n"
        show lena at rig3 with move
        $ flena = "serious"
        show ivy_car behind lena
        show ivy at lef3
        with short
        v "Hey!"
        l "There you are!"
        l "Do you have any idea what you just made me go through? You left me hanging!"
        $ fivy = "sad"
        v "I'm sorry, I didn't know it would be so difficult to get back into the city!"
        l "I don't want to hear your excuses! Drive me home!"
        scene streetnight with long
        pause 1
        jump v9saturdayend
## SEDUCTION
    label gallery_CH09_S11:
        if _in_replay:
            call setup_CH09_S11 from _call_setup_CH09_S11

    $ faxel = "smile"
    show axel at truecenter with move
    x "Here, look at these pictures."
    "Axel pulled up the camera and started going through the photos we had been taking."
    if lena_axel_desire:
        $ flena = "blush"
        "I still felt bewildered by the cocktail of emotions that had been brewing inside of me."
    if v6_axel_pose > 1:
        $ flena = "blush"
        "I was so close to Axel... Last time we were even closer, and with much fewer clothes..."
    else:
        $ flena = "worried"
        "Suddenly I realized how close to Axel I was..."
    if axel_disposition == 2:
        "This whole thing had worked out much better than I had anticipated..."
        "I had been tense during the whole ordeal, I still was, but at least Axel had behaved himself."
        "When I managed to ignore my emotions, I almost felt at ease around him again, a small reminder of how things used to be."
        if lena_axel_desire:
            "And I couldn't avoid thinking about how other things used to be."
            "How Axel's body felt under my touch. The grip of his strong hands, the way he moved his hips when..."
        elif v6_axel_pose > 1:
            "I used to like him so much before everything was shattered. When I was with Axel, I..."
        else:
            "To think we were so close not that long ago... Before everything was shattered."
        x "Are you listening to me, Lena?"
        l "Uh, what?"
        x "I was asking which one of those two pictures you liked best."
        l "I... Which ones?"
        play music "music/danger.mp3" loop
        scene v9_axel1
        if lena_tattoo2:
            show v9_axel1_t2
        if lena_tattoo3:
            show v9_axel1_t3
        if lena_piercing1:
            show v9_axel1_p1
        elif lena_piercing2:
            show v9_axel1_p2
        with long
        pause 1
        x "I've missed these moments..."
        "Axel held my chin tenderly, tilting my head up."
    else:
        "I had managed to remain calm and collected during this whole ordeal, or at least that had been my goal."
        "Axel had behaved himself and I kept my answers short and neutral. It went better than I expected..."
        "But now I was pushing my luck. Ivy wasn't here, and the shoot was over."
        show lenabra2 at rig3 with move
        l "I think it's time for me to get going."
        $ faxel = "n"
        x "Are you sure you don't want that glass of water?"
        if v6_axel_pose > 1:
            $ flena = "blush"
            if lena_axel_desire:
                "I wanted to say no, but why was I finding it difficult to?"
            l "Yeah, no, I'm... I'm good."
        else:
            l "No, I'm good..."
        show axel at truecenter with move
        x "Lena..."
        play music "music/danger.mp3" loop
        scene v9_axel1
        if lena_tattoo2:
            show v9_axel1_t2
        if lena_tattoo3:
            show v9_axel1_t3
        if lena_piercing1:
            show v9_axel1_p1
        elif lena_piercing2:
            show v9_axel1_p2
        with long
        pause 1
        "Axel reached with his hand and held my chin tenderly, tilting my head up."
    # kiss
    "His face was merely inches away from mine...!"
    $ timeout = 3
    $ timeout_label = "v9axelwait"
    menu:
        "Let Axel kiss you" if lena_axel_desire or v9_axel_pose == 2:
            $ renpy.block_rollback()
            $ v9_axel_kiss = "kiss"
            "I closed my eyes. I knew what was coming."
            "And I let it happen."
            scene v9_axel1b
            if lena_tattoo2:
                show v9_axel1_t2
            if lena_tattoo3:
                show v9_axel1_t3
            if lena_piercing1:
                show v9_axel1_p1
            elif lena_piercing2:
                show v9_axel1_p2
            with long
            pause 1
            "Axel leaned forward and his lips found mine, softly."
            "Their touch sent a shiver down my spine, and I trembled."
            if lena_lust < 9:
                call xp_up('lust') from _call_xp_up_118
            "Axel took that as a signal, and before I could even recover, he interlocked his mouth with mine, penetrating with a deep, long kiss."
            jump v9axelkissing

        "Tell Axel to stop":
            $ renpy.block_rollback()
            label v9axelwait:
                $ v9_axel_kiss = "wait"
            l "Axel, wai--{w=0.3}{nw}"
            scene v9_axel1b
            if lena_tattoo2:
                show v9_axel1_t2
            if lena_tattoo3:
                show v9_axel1_t3
            if lena_piercing1:
                show v9_axel1_p1
            elif lena_piercing2:
                show v9_axel1_p2
            with long
            pause 1
            "He had no intention of hearing me out."
            "Before I could finish my sentence, his open mouth interlocked with mine, penetrating it with a deep, forceful kiss."
            if axel_pictures_watch:
                "I froze, overwhelmed by the sudden sensation. It felt like a battering ram was knocking down my defenses..."
            $ timeout_label = None
            $ config.menu_include_disabled = False
            $ greyed_out_disabled = True
            menu:
                "Kiss him back" if axel_pictures_watch:
                    $ renpy.block_rollback()
                    "I knew this shouldn't be happening. But I let it anyway."
                    scene v9_axel1b
                    if lena_tattoo2:
                        show v9_axel1_t2
                    if lena_tattoo3:
                        show v9_axel1_t3
                    if lena_piercing1:
                        show v9_axel1_p1
                    elif lena_piercing2:
                        show v9_axel1_p2
                    with long
                    if lena_will == 0:
                        "I had no will to resist him..."
                    "When I gave in to his kisses, a strong shiver ran down my spine, making me tremble."
                    "Axel took that as a signal, and his kisses became even more intense."
                    jump v9axelkissing

                "Push Axel away!" if lena_axel_desire == False:
                    $ renpy.block_rollback()
                    if axel_pictures_watch:
                        "I fought back against Axel, and against myself."
                        "This was a mistake. A big one."
                    else:
                        "How dared he? This was unacceptable!"
                    $ faxel = "sad"
                    jump v9axelpush

                "{image=icon_will.webp}Push Axel away!" if lena_axel_desire and lena_will > 0:
                    $ renpy.block_rollback()
                    call willdown from _call_willdown_15
                    if axel_pictures_watch:
                        "I fought back against Axel, and against myself."
                        "This was a mistake. A big one."
                    else:
                        "How dared he? This was unacceptable!"
                    $ faxel = "sad"
                    jump v9axelpush

        "Push Axel away!" if lena_axel_desire == False and v9_axel_pose < 2:
            $ renpy.block_rollback()
            $ axel_disposition = 0
            $ v9_axel_kiss = "push"
            label v9axelpush:
                stop music
            $ flena = "mad"
            play sound "sfx/punch.mp3"
            scene axelhomenight
            show lenabra at rig
            show axel at lef3
            with vpunch
            "I pushed him away with all my strength, liberating myself from his embrace."
            l "What are you doing!?"
            x "..."
            show axel at lef with move
            x "Lena, I..."
            l "Don't come any closer!"
            l "I knew this was a bad idea!"
            x "I'm sorry, I didn't mean to..."
            l "To put your hands on me? It happened by accident?"
            if lena_charisma < 9 and v9_axel_kiss == "push":
                call xp_up('charisma') from _call_xp_up_119
            x "You know you've always swayed my emotions in a way that..."
            l "Stop it, I don't want to hear it. I'm leaving."
            call friend_xp('axel',-1) from _call_friend_xp_166
            $ lena_axel = 0
            show lenabra at rig3 with move
            x "Of course. Again, I'm sorry."
            hide lenabra with short
            play sound "sfx/door_slam.mp3"
            scene streetnight with long
            if v6_axel_pose == 3:
                $ lena_look = "sexy"
            elif v6_axel_pose == 2:
                $ lena_look = 4
            else:
                $ lena_look = 1
            $ flena = "serious"
            "I picked up my clothes and left without even being completely dressed."
            show lena with short
            "Only when I was down on the street did I put my shoes on and start walking, fast."
            l "I knew this was a bad idea! Damn you, Ivy, I shouldn't have listened to you...!"
            $ flena = "worried"
            l "I should've known something like this was going to happen..."
            scene streetnight with long
            pause 1
            $ renpy.end_replay()
            jump v9saturdayend
##SEX SCENE STARTS ###############################################################################################################################################################################
label v9axelkissing:
    $ config.menu_include_disabled = True
    $ greyed_out_disabled = False
    $ timeout_label = None
    $ v9_axel_sex = True
    scene v9_axel2
    if lena_tattoo2:
        show v9_axel2_t2
    if lena_tattoo3:
        show v9_axel2_t3
    with long
    "He grabbed me with his strong hands, digging his fingers into my soft flesh with need, hunger, and lust."
    "Like a sudden burst of flames, Axel's desire engulfed me. He lifted me up and drove me against the wall, kissing me with a voracious appetite."
    "I was being devoured by the fire of his passion, and all I could do was melt under its heat."
    "Holding me against the wall, Axel's fingers found the hook of my bra and undid it, beginning to strip away what little defenses I had left."
    "I felt so light in his arms. So frail in the face of his strength."
    "So defenseless and powerless..."
    "When Axel let go of me to take his shirt off, I felt my stomach drop and a sudden flash of reason dazzled my mind with its clarity."
    $ flena = "blush"
    $ faxel = "n"
    $ lena_look = "underwear2"
    scene axelhomenight
    show lenatopless2 at rig
    show axel_topless at lef3
    with long
    "I managed to create some space between Axel and me, looking down and hiding my lips from him."
    l "Axel, wait..."
    "I fumbled for words. I tried making a last stand and offer some resistance, as I knew I should."
    "But my mind felt foggy and my body weak. The effect Axel had on me truly was like a strong narcotic..."
    "I was spiraling out of control and that made me so scared."
    show axel_topless at lef with move
    x "Lena..."
    "Axel got closer, and I cowered against the wall. I had no space to run."
    x "What do you want, Lena?"
    l "I..."
    "The words died on my lips, silent."
    "What did I want?"
    # grab
    scene v9_axel3
    if lena_tattoo1:
        show v9_axel3_t1
    if lena_tattoo2:
        show v9_axel3_t2
    if lena_tattoo3:
        show v9_axel3_t3
    if lena_piercing1:
        show v9_axel3_p1
    elif lena_piercing2:
        show v9_axel3_p2
    with long
    pause 1
    "I made a weak attempt at getting away, but Axel stopped me, holding me in place against his chest."
    "One of his hands slid down my hips, pulling my panties down, while the other one rose up to my neck..."
    "I felt his fingers wrapping around it, gently but firmly."
    "That dominant and protective grip I knew so well."
    "That grip I never knew how to escape."
    x "What do you want, babygirl?"
    "Axel's words felt like a soft, deliberate torture, demanding an answer I couldn't find."
    "My head was a total mess, thoughts racing and combusting inside my mind."
    x "Do you want to let yourself go? Is that it?"
    stop music fadeout 2.0
    "Seeing I wasn't able to find an answer to give, he offered me one. I just had to accept it."
    play music "music/sex_dark.mp3" loop
    scene v9_axel3b
    if lena_tattoo1:
        show v9_axel3_t1
    if lena_tattoo2:
        show v9_axel3b_t2
    if lena_tattoo3:
        show v9_axel3b_t3
    if lena_piercing1:
        show v9_axel3_p1
    elif lena_piercing2:
        show v9_axel3_p2
    with long
    pause 1
    "Axel's grip on my neck tightened ever so slightly, and he controlled my wrist with his other hand. I wasn't allowed to move."
    "And then I felt something hard sliding between my thighs. His cock."
    x "Don't worry, babygirl."
    x "Even if things have changed between us... You know I'll always be here for you."
    x "To catch you when you need a pair of arms to rest on. A place to release all that pain and pressure."
    x "You can let yourself go with me, Lena."
    "Letting go. Those words had a very unique and personal meaning to us both."
    "Letting go for me meant..."
    # cock
    scene v9_axel4
    if lena_tattoo1:
        show v9_axel4_t1
    if lena_tattoo3:
        show v9_axel4_t3
    if lena_piercing1:
        show v9_axel4_p1
    elif lena_piercing2:
        show v9_axel4_p2
    with long
    pause 1
    l "...!"
    "Axel continued to stroke his shaft across my slit, spreading my small labia."
    "I looked down and saw the head of his colossal cock sticking out between my legs."
    x "Some things don't change... You're drenched down there, Lena."
    "I couldn't think straight. I had lost control and my fate was in the hands of my aching desire."
    "A desire that I had been harboring since the moment I wasn't able to destroy those Polaroids and ended up masturbating to them instead."
    if lena_axel_desire:
        "A desire that had been reignited when we posed together, and a desire I had accepted subconsciously in my dreams."
    elif v6_axel_pose > 0:
        "A desire that had been reignited when we posed together, even if I tried to deny it."
    elif v6_axel_pose == 1:
        "A desire I tried to stop from reigniting, but I had failed."
    else:
        "A desire I hadn't been able to get rid of after all this time."
    if ian_lena_couple:
        "Ian flashed through my mind one last time before guilt and shame drowned into acceptance."
    "There was no point in trying to resist any longer. I had already surrendered to Axel long ago."
    play sound "sfx/oh1.mp3"
    scene v9_axel4b
    if lena_tattoo1:
        show v9_axel4_t1
    if lena_tattoo3:
        show v9_axel4_t3
    if lena_piercing1:
        show v9_axel4_p1
    elif lena_piercing2:
        show v9_axel4_p2
    with long
    pause 1
    l "Oh, God...!"
    if lena_lust < 9:
        call xp_up('lust') from _call_xp_up_120
    x "I missed you so much, babygirl."
    "Axel's spearhead began sliding inside my pussy, spreading it apart like a hot iron rod melting a block of ice."
    "That burning sensation started to spread deeper into me, inch by inch, as Axel slowly penetrated me."
    if lena_axel_desire:
        "That cock I had been dreaming of, that cock that had claimed my body like no other...!"
        "The deeper it got the more ecstatic I felt...!"
    else:
        "His cock was taking possession of my body once more...!"
        "The deeper it got the more benumbed my mind became."
    "Any thoughts I had in my mind evaporated like steam, giving way to raw sexual abandon."
    # fuck
    play sound "sfx/ah2.mp3"
    scene v9_axel5
    if lena_tattoo1:
        show v9_axel5_t1
    if lena_tattoo2:
        show v9_axel5_t2
    if lena_tattoo3:
        show v9_axel5_t3
    if lena_piercing1:
        show v9_axel5_p1
    elif lena_piercing2:
        show v9_axel5_p2
    with long
    pause 1
    "Axel lifted me up like I weighed nothing and our genitals aligned much better."
    "His cock kept sliding in, spreading my tight pussy forcefully."
    "And then he slowly pulled back, only to push forward again, each time just a bit deeper."
    "Each thrust eroded whatever fear or resentment I had left more and more, liberating me from any doubt."
    "In Axel's strong arms, I even felt liberated from gravity itself."
    play sound "sfx/ah5.mp3"
    l "Oh, fuck!!" with vpunch
    l "Oh, fuck, Axel...!"
    x "Seems you missed me, too."
    "Without setting me down or his cock ever slipping out of me, Axel started walking up the stairs, taking me to his bed."
    "His cock throbbed inside of me with each step, vibrating inside of my pussy, until he dropped me on the bed face first."
    # animation
    scene v9_axel6a with long
    "He began pumping his hips from behind, slowly, his tool drilling into me even deeper and conquering a few more inches."
    play sound "sfx/oh1.mp3"
    scene v9_axel6b with vpunch
    pause 1
    "And then he suddenly pounded me hard, tearing through the walls of my pussy!"
    "I screamed, pleasure and pain mixed in its sound."
    scene v9_axel6_animation with short
    pause 2
    "The only reason my pussy was able to take Axel's cock like that was that I was so insanely wet, but even so, it hurt..."
    "But it was a good kind of hurt."
    "A hurt I had learned to appreciate..."
    "And that rhythm...! He kept it constant, unwavering, stimulating my pussy in all its extension, just at the right intervals."
    "He remembered exactly how I liked it."
    "My legs started trembling, possessed by impending ecstasy..."
    x "What's wrong? Are you going to cum?"
    l "Y--{w=0.5}yes...!"
    x "No. You can't."
    x "Not yet."
    "Axel continued pounding me deep and hard, maintaining that rhythm that had me on the brink of cumming."
    "Just on the brink."
    "I felt my body about to burst, but the orgasm wasn't coming. I couldn't reach it, even though I was so close..."
    "Axel wouldn't let me. I didn't have his permission."
    l "Please... Can I... Can I cum?"
    l "Please... Please!"
    x "It's been too long since you enjoyed this, hasn't it?"
    x "Well, today I'm feeling generous, and since you ask me that way... Go ahead."
    x "Cum for me."
    # orgasm
    scene v9_axel7
    if lena_tattoo2:
        show v9_axel7_t2
    with short
    pause 0.4
    play sound "sfx/orgasm2.mp3"
    scene v9_axel7b
    if lena_tattoo2:
        show v9_axel7_t2
    with hpunch
    pause 0.3
    with hpunch
    pause 0.3
    with hpunch
    pause 0.3
    with hpunch
    l "Ahhh!! Oooaaahhhh!!!{w=0.5}"
    "Upon hearing Axel's command I felt all my strength go, draining out of my body between my legs."
    "I let go of everything."
    "Of my restraints. Of my pride. Of myself."
    if lena_ian_love:
        "Of Ian, even..."
    play sound "sfx/ah5.mp3"
    scene v9_axel7_a with short
    with hpunch
    "No shame, no blame. Just my mind and my body melting together in the heat of pleasure."
    "In Axel's comforting heat..."
    scene v9_axel7c
    if lena_tattoo2:
        show v9_axel7_t2
    with long
    pause 1
    x "I knew it... You're still mine, babygirl."
    l "...!"
    "The world was spinning around me. I was barely aware of where I was."
    "Slowly, I began getting my senses back as pleasure washed away, but Axel didn't give me time to recover."
    x "Look at the mess you've made...!"
    x "You ruined my bed sheets. Where am I supposed to sleep today?"
    # bj start
    scene v9_axel8 with long
    pause 1
    "Axel pulled me off the bed rather roughly, and I found myself on my knees."
    x "Look, it's all over my cock too."
    "His dick was drenched in my pussy juices. He rubbed it on my face, making it sticky with them."
    "That shaft, hard as wood, that had been ravaging my insides just a moment ago..."
    x "You know it's time for you to compensate me, right?"
    "I knew the game. And I went along with it."
    # bj static
    scene v9_axel9
    if lena_tattoo2:
        show v9_axel9_t2
    with long
    pause 1
    "It was an automatic motion. Without even thinking, I held my tongue to the base of Axel’s cock and clasped it with my lips."
    x "That's it, good girl..."
    "I felt Axel's hand sliding up my nape, entangling my hair in its fingers and clasping them together to hold me tightly in place."
    x "Deeper."
    # bj deeper
    play sound "sfx/bj2.mp3"
    scene v9_axel10
    if lena_tattoo2:
        show v9_axel10_t2
    with hpunch
    "I relaxed my throat and let Axel's cock invade it."
    # bj animation 1
    scene v9_axel_bj_animation1 with short
    play sound "sfx/bj2.mp3"
    pause 2
    "My legs were still trembling and my mind foggy while he guided my head with his controlling hand."
    "It wasn't easy taking him in my pussy, and my gullet was in a very similar predicament."
    "Axel's cock squeezed my throat's perimeter, threatening to tear it apart. It wasn't easy learning how to make room for him."
    "But I had, and my docile throat offered no resistance to his lustful assault."
    x "Come on, I know you can do better..."
    # sudden deepthroat
    play sound "sfx/dp1.mp3"
    scene v9_axel10c
    if lena_tattoo2:
        show v9_axel10_t2
    with hpunch
    pause 1
    x "Or do I have to train you again?"
    "Axel drove his hips forward, holding my head in place."
    "He shoved his cock into my mouth until his balls touched my chin. I couldn't breathe."
    play sound "sfx/gag1.mp3"
    l "{i}Ghhhhk...{/i}!"
    "My throat started to rebel, contracting around Axel's phallus."
    x "Fuck, that's what I'm talking about, babygirl!"
    "The contractions became more violent the more I was in need of air."
    "I was starting to get dizzy...!"
    # release for air
    play sound "sfx/ah6.mp3"
    scene v9_axel11
    if lena_tattoo2:
        show v9_axel11_t2
    with hpunch
    pause 1
    l "{i}Hahhhhh...{/i}!"
    "Just when I felt I couldn't take it anymore, Axel released me."
    "I gasped for precious air, a couple of tears rolling down my eyes and threads of spit down my chin."
    x "Now, that was something... Do you remember now?"
    l "Yes..."
    x "Then get back to it."
    # bj animation 2
    scene v9_axel9b
    if lena_tattoo2:
        show v9_axel9_t2
    with long
    pause 2
    "I had no space for freedom."
    "The way he spoke transformed his words into orders. The will I could not refuse."
    play sound "sfx/dp2.mp3"
    scene v9_axel_bj_animation2 with short
    "So I obeyed."
    if lena_will > 0:
        call willdown from _call_willdown_16
        if not cheat_mode:
            $ lena_will = 0
    pause 2
    "I just surrendered all control to Axel. He fucked my mouth how he pleased, using me for his pleasure."
    "And how I had loved it...! Being able to please him, knowing myself valuable and precious to him..."
    "And that hadn't changed. My body still reacted the same way to him."
    "My will still found respite when surrendered to his."
    "Nothing to take care of. Nothing to fear. Just letting go."
    "Just letting Axel take care of me..."
    x "Oh yeah, babygirl...! You're just perfect...!"
    x "Fuck, I'm almost there...!"
    # cum
    scene v9_axel10c
    show v9_axel10c_eye
    if lena_tattoo2:
        show v9_axel10_t2
    with hpunch
    x "Aggghhh!!!"
    play sound "sfx/gag1.mp3"
    with hpunch
    pause 0.6
    with hpunch
    pause 0.6
    with hpunch
    pause 0.6
    "Both Axel's body and voice trembled before me. That was the only moment they ever trembled..."
    "And that moment was mine."
    play sound "sfx/ah6.mp3"
    # show swallowed
    scene v9_axel11b
    if lena_tattoo2:
        show v9_axel11_t2
    with long
    pause 1
    l "Ahhhh..."
    x "Good girl, show me... You got it all to the last drop."
    "Not that I had a choice in that."
    x "That was incredible. I loved it..."
    # end
    scene v9_axel12
    if lena_tattoo2:
        show v9_axel12_t2
    if lena_tattoo3:
        show v9_axel12_t3
    with long
    pause 1
    "I was in a daze, unable to get a hold of myself. The intensity of the experience had left me shaken and weak..."
    "Thankfully I could find solace in Axel's body. Holding onto him made me feel protected and anchored, despite the foggy whirlwind I was still immersed in."
    "The weight of his hand on my head, caressing my hair... The intense warmth emanating from his body and his familiar scent..."
    "They felt so secure and reassuring. A place for respite."
    "Feeling the girth of his manhood under my fingers was strangely comforting."
    # zoom
    scene v9_axel12b
    if lena_tattoo2:
        show v9_axel12b_t2
    if lena_tattoo3:
        show v9_axel12b_t3
    with long
    "I couldn't help but admire his marvelous tool. I caressed the slimy shaft, shiny with my saliva."
    "I had been totally subdued by it. Its strong, bitter taste was in my mouth..."
    x "If you keep touching it like that we'll need to go for a second round..."
    stop music fadeout 2.0
    $ faxel = "happy"
    $ flena = "blush"
    scene axelhomenight
    show lenanude2 at rig
    show axelnude at lef
    with long
    "The sound of his voice suddenly took me out of the trance."
    "I jumped out of the bed, startled like someone had just poured a bucket of cold water over me."
    l "I... This...!"
    "I couldn't find the words. All those inhibitions that went offline were suddenly back up, and I was quickly becoming aware of what had truly happened."
    "I had sex with Axel. I had given myself to him, again..."
    $ flena = "drama"
    l "Oh no, this was a mistake, this was...!"
    $ faxel = "smile"
    x "Calm down, Lena. It's okay..."
    if ian_lena_couple:
        "Ian... What had I done?"
        "That sudden realization hit me like a brick. My head was spinning so fast it was making me dizzy."
        "The relationship we had been building up to this point, the feelings I had for him... I had completely stomped on all of them."
        "I had betrayed Ian, and I had betrayed myself."
    $ flena = "mad"
    l "It's not okay! This wasn't supposed to happen..."
    x "But it did. We missed each other, it's not that strange."
    $ flena = "drama"
    l "But we're done. What we had, it's over..."
    $ faxel = "n"
    x "I know. This doesn't change that fact..."
    $ flena = "blush"
    $ faxel = "smile"
    x "I guess we never got the chance to properly say goodbye to each other. To share one last moment together."
    l "..."
    "One last moment..."
    x "Don't fret too much over it. I know where we stand, you've made it abundantly clear."
    x "I'm glad we got some proper closure. I know this will help us move on."
    l "I... I should get going."
    x "Yeah. I'll call you a taxi while you get dressed."
    hide axelnude with short
    l "..."
    l "Oh my God..."
    scene fade
    if v6_axel_pose == 3:
        $ lena_look = "sexy"
    elif v6_axel_pose == 2:
        $ lena_look = 4
    else:
        $ lena_look = 1
    play sound "sfx/car.mp3"
    pause 1
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH09_S11")
    jump v9saturdayend

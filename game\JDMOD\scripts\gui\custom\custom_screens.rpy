init offset = 1

init python:
    def get_active_char():
        for main_char in MAIN_CHAR:
            if getattr(store, "{}_active".format(main_char)) == True: 
                return main_char
        else: 
            return "Error"

    def skillsup():
        char = get_active_character_name()
        
        skills_and_points = {}
        for skill in skill_array:
            skills_and_points[skill] = eval("{}_{}_xp".format(get_active_character_name(), skill))
        
        updated_skill = False
        for skill, points in skills_and_points.items():
            points_needed = get_skill_points_needed(skill)
            
            if points >= points_needed:
                var = "%s_%s" % (char, skill)
                SetVariable(var, eval(var)+1)()
                SetVariable(var+"_xp", eval(var+"_xp")-points_needed)()
                updated_skill = skill
                break
        
        if updated_skill:
            global quick_menu
            renpy.pause(0.2)
            quick_menu = False
            renpy.pause(0.2)
            renpy.call_screen("screen_skillsup", skill=updated_skill)
            renpy.pause(0.2)
            quick_menu = True

    def willup():
        global quick_menu
        renpy.pause(0.2)
        quick_menu = False
        renpy.pause(0.2)
        renpy.call_screen("screen_willup")
        renpy.pause(0.2)
        quick_menu = True

transform transform_skill_card:
    on show:
        zoom 0.0
        alpha 0.0
        block:
            parallel:
                linear 0.3 zoom 1.3
            parallel:
                linear 0.3 alpha 1.0

        linear 0.1 zoom 1.0

transform transform_skill_bg:
    on show:
        zoom 0.0
        alpha 0.9

        pause 0.1
        block:

            parallel:
                linear 0.7 zoom 2.5
            parallel:
                pause 0.5
                linear 0.2 alpha 0.0

screen screen_skillsup(skill):
    modal True
    if _in_replay:
        timer 0.01 action Return()
    else:
        timer 0.3 action Play("ch_one", "sfx/levelup.mp3")
        add "gui/levelup_popup_front.webp" at transform_skill_bg align (0.5, 0.5) yoffset -50
        add "gui/levelup_%s.webp" % skill at transform_skill_card align (0.5, 0.5) yoffset -50

        key "game_menu" action NullAction()
        key "dismiss" action Play("ch_one", "sfx/phone_back.mp3"), Return()

        if renpy.is_skipping():
            timer 0.5 action Play("ch_one", "sfx/phone_back.mp3"), Return()
        else:
            timer 3.0 action Play("ch_one", "sfx/phone_back.mp3"), Return()



screen screen_willup():
    modal True
    if _in_replay:
        timer 0.01 action Return()
    else:
        timer 0.3 action Play("ch_one", "sfx/willup.mp3")

        add "gui/levelup_popup_front.webp" at transform_skill_bg align (0.5, 0.5) yoffset -50
        add "gui/levelup_will.webp" at transform_skill_card align (0.5, 0.5) yoffset -50

        $ char = get_active_character_name()

        key "game_menu" action NullAction()
        key "dismiss" action Play("ch_one", "sfx/phone_back.mp3"), SetVariable(char+"_will", eval(char+"_will")+1), Return()

        if renpy.is_skipping():
            timer 0.5 action Play("ch_one", "sfx/phone_back.mp3"), SetVariable(char+"_will", eval(char+"_will")+1), Return()
        else:
            timer 3.0 action Play("ch_one", "sfx/phone_back.mp3"), SetVariable(char+"_will", eval(char+"_will")+1), Return()


screen screen_notif_unlock():
    timer 0.2 action Play("ch_one", "sfx/xp.mp3")

    imagebutton:
        at transform_gallery_unlock
        xalign 1.0
        yanchor 0.5
        yoffset 170
        idle "gui/scene_unlock.webp"
        action Play("ch_one", "sfx/phone_back.mp3"), Hide('screen_notif_unlock')

    if renpy.is_skipping():
        timer 0.5 action Hide('screen_notif_unlock')
    else:
        timer 5.0 action Hide('screen_notif_unlock')

screen screen_friend_xp(char_name, inc=1):
    style_prefix "friend_xp"

    if _in_replay:
        timer 0.01 action Return()
    else:
        if inc > 0:
            timer 0.01 action Play("ch_one", "sfx/friendup.mp3")
            frame at transform_stat_up:
                imagebutton idle "gui/xp_friend.webp" action Hide('screen_friend_xp') keysym "input_enter"
                text "[char_name!c]" yoffset 170
        else:
            timer 0.01 action Play("ch_one", "sfx/frienddown.mp3")
            frame at transform_stat_down:
                imagebutton idle "gui/unxp_friend.webp" action Hide('screen_friend_xp') keysym "input_enter"
                text "[char_name!c]" yoffset -46

        timer 1.0 action Hide('screen_friend_xp')

screen screen_calendar():
    style_prefix "calendar"

    if ian_active:
        $ parent = 'ian'
    else:
        $ parent = 'lena'

    frame style "empty":
        imagebutton idle "gui/%scalendar.webp" % parent action Return()
        if prev_day != day:
            text prev_day size 120 ypos 430 at prev_day_transform(direction=anim_day_dir)
            text day size 120 ypos 430 at curr_day_transform(direction=anim_day_dir)
        else:
            text day size 120 ypos 430

        vbox:
            ypos 705
            xalign 0.5
            hbox:
                frame style "empty" xsize max_len_month * 20:
                    if prev_month != month:
                        text "[prev_month]" size 60 xalign 1.0 at prev_day_transform(w=1.7, direction=anim_month_dir * 0.5)
                        text "[month]" size 60 xalign 1.0 at curr_day_transform(w=1.8, direction=anim_month_dir * 0.5)
                    else:
                        text "[month]" size 60 xalign 1.0

                text ", week " size 60

                frame style "empty" xsize 30:
                    if prev_week != week:
                        text "[prev_week]" size 60 at prev_day_transform(w=1.7, direction=anim_week_dir * 0.5)
                        text "[week]" size 60 at curr_day_transform(w=1.8, direction=anim_week_dir * 0.5)
                    else:
                        text "[week]" size 60

    key "dismiss" action Return()
    key "input_enter" action Return()
    timer 1.0 action Return()

default JD_IGG_isactive = True
screen JD_chapter_title():

    style_prefix "chapter_title" tag chapter_title

    add "gui/bg.webp"

    imagebutton idle "#0000" action Return()
    key "dismiss" action Return()



    vbox:
        align (0.5, 0.4)
        spacing 50
        text "Chapter {color=#c4002e}%s{/color}" % get_chapter_number_text() style "chapter_title_text_title"
        text get_chapter_subtitle()

    vbox:
        xalign 0.5
        yalign 0.9
        if JD_IGG_isactive:
            text "{image=JD_icon} You're currently playing the modded version." style "button_text" insensitive_color "#aaaaaa"
            textbutton "{u}Click here to switch version{/u}." xalign 0.5:
                action SetVariable("JD_IGG_isactive", False), Hide("JD_chapter_title"), Jump("chapter_{}".format(get_chapter_number_text().lower()))
        else:
            text "{image=JD_icon} You're currently playing the unmodded version. Most features of the JDMOD are unavailable." style "button_text" insensitive_color "#c4002e"
            textbutton "{u}Click here to switch version{/u}." xalign 0.5 text_idle_color "#c4002e":
                action SetVariable("JD_IGG_isactive", True), Hide("JD_chapter_title"), Jump("JD_chapter_{}".format(get_chapter_number_text().lower()))

        if persistent.JD_developer:
            textbutton "DEV MODE: Force Return()" xalign 0.5:
                action Return()



init:
    $ config.label_overrides.update({"label_chapter_title": "JD_label_chapter_title"})

label JD_label_chapter_title:
    $ quick_menu = False
    pause 0.3
    scene blackbg with long_dissolve
    $ renpy.checkpoint()
    call screen JD_chapter_title with long_dissolve
    show screen JD_chapter_title
    hide screen JD_chapter_title with long_dissolve
    $ renpy.pause(0.5)
    scene black with long_dissolve
    $ quick_menu = True
    return
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc

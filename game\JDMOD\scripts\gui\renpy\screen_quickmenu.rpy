init offset = 1






screen stats_ingame():
    style_prefix "stats_ingame"

    $ parent = get_active_char()

    if not _in_replay:
        frame at transform_show_hide((102,0), (-14,0)):
            background "JDMOD/images/gui/stat_popup.png"
            xysize (140, 527)
            vbox:
                spacing 53
                xoffset -98
                yoffset 2
                text "{size=-8}[%s_wits_xp]/%s" % (parent, get_skill_points_needed("wits")) color "#609dcb"
                text "{size=-8}[%s_charisma_xp]/%s" % (parent, get_skill_points_needed("charisma")) color "#f2a43e"
                text "{size=-8}[%s_athletics_xp]/%s" % (parent, get_skill_points_needed("athletics")) color "#f8ec82"
                text "{size=-8}[%s_lust_xp]/%s" % (parent, get_skill_points_needed("lust")) color "#ec4d3a"

            vbox:
                spacing 45
                xoffset -10
                yoffset -2
                text "[%s_wits]" % (parent) color "#609dcb"
                text "[%s_charisma]" % (parent) color "#f2a43e"
                text "[%s_athletics]" % (parent) color "#f8ec82"
                text "[%s_lust]" % (parent) color "#ec4d3a"
                text "[{0}_money]".format(parent) color "#93d87e"
                text "[{0}_will]".format(parent) color "#E0CE39"

style stats_ingame_text:
    font font_big_noodle_oblique
    color "#FFFFFF"
    size gui.label_text_size
    xalign 0.5
    outlines [(2, "#000000", 0, 0)]
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc

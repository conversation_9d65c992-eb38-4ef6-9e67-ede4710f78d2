label JD_master_script2:

    if chapter == 0:

        $ chapter = 1

        $ save_name = "Ian: Chapter 1"
        $ prologueover = True

        jump JD_chapter_one





    if chapter == 1:
        $ chapter = 2
        $ emma_tattoo = True

        jump JD_chapter_two





    if chapter == 2:

        $ chapter = 3


        if v2_robert_bj:
            $ lena_bj += 1
        if v2_robert_swallow:
            $ lena_bj += 1

        if song_1a == "real":
            $ lena_song1 += 2
        if song_1a == "precise":
            $ lena_song1 += 1
        if song_1b == "tragedy":
            $ lena_song1 += 2
        if song_1b == "story":
            $ lena_song1 += 1
        if song_1c == "abyss":
            $ lena_song1 += 2
        if song_1c == "kingdom":
            $ lena_song1 += 1


        if v2_alison_home:
            $ alison_jeremy = False

        jump JD_chapter_three





    if chapter == 3:

        $ chapter = 4




        if v3_seymour_date == False:
            $ v3_seymour_reject = True

        if v3_seymour_date and v3_seymour_reject == False:
            $ v4_seymour_date = True

        jump JD_chapter_four




    if chapter == 4:

        $ chapter = 5


        jump JD_chapter_five






    if chapter == 5:
        $ chapter = 6


        if lena_mike_sex or lena_robert_sex:
            $ jess_bad = True

        jump JD_chapter_six




    if chapter == 6:
        $ chapter = 7

        if outfit_bunny or v6_robert_date:
            $ lena_wardrobe_bunny = True
        if lena_bbc:
            $ lena_fty_bbc = True

        jump JD_chapter_seven




    if chapter == 7:
        $ chapter = 8



        if v4_seymour_date:
            $ lena_wardrobe_lingerie = True

        jump JD_chapter_eight




    if chapter == 8:
        $ chapter = 9



        if ian_lena_over:
            $ ian_lena_dating = False
        if lena_louise_sex:
            $ lena_fty_lesbo = True


        jump JD_chapter_nine





    if chapter == 9:
        $ chapter = 10

        if ian_lena_couple:
            $ ian_lena_love = True

        jump JD_chapter_ten






    if chapter == 10:
        $ chapter = 11


        if (v10_wc_bj == "mark" and ian_lena_couple and ian_lena_breakup == False) or (v10_wc_bj == "mike" and ian_lena_couple and ian_lena_breakup == False):
            $ lena_cheating = True
        if v10_ivy_sex:
            $ lena_fty_lesbo = True


        if v10_ian_left or ian_lena_breakup:
            $ ian_lena_dating = False


        if lena_ian_love:
            $ lena_ian_love = 1


        if (ian_lena_couple and ian_lena_breakup == False and ian_cindy_dating) or (ian_lena_couple and ian_lena_breakup == False and ian_minerva_dating):
            $ ian_cheating = True


        if (lena_mike_over and v10_stalkfap == "mike") or (lena_mike_over and v10_mike_sex) or (lena_mike_over and v10_wc_bj == "mike"):
            $ lena_mike_over = False
        if lena_mike_over:
            $ lena_mike_dating = False


        if v9_cindy_lie == 3:
            $ v10_perry_lie += 1
        elif v9_cindy_lie == 2 and v10_perry_lie < 2:
            $ v10_perry_lie += 1
        elif v9_cindy_lie == 0 and ian_cindy_dating and v10_perry_lie > 0:
            $ v10_perry_lie -= 1


        if holly_change > 3:
            $ v11_holly_change = True


        if alison_jeremy_3some == 2 or ian_alison_dom or ian_alison_dating == False:
            $ alison_blonde = 1


        if v10_sparring > 1 and kickboxing < 5:
            $ kickboxing += 1


        jump JD_chapter_eleven





    if chapter == 11:
        $ chapter = 12
        if v11_shower_sex:
            $ lena_fty_lesbo = True

        if ian_athletics > 5:
            $ ian_fit = 1

        if ian_alison_dating == False and ian_alison_breakup == False and ian_cherry_dating == False and alison_jeremy_3some > 1 and v11_alison_reject == 0:
            $ ian_alison_fuck = True

        if v9_alison_trip == False:
            $ v9askanalalison = False

        if holly_change > 3 or holly_ivy:
            $ v11_holly_change = True

        if ian_lena_couple and lena_axel_desire > 0:
            $ lena_cheating = True
        if holly_ivy and lena_go_holly > 3:
            $ lena_go_holly = 3

        jump JD_chapter_twelve





    if chapter == 12:
        $ chapter = 13

        if ian_lena_open == "ian" or ian_lena_open == "lena":
            $ ian_lena_crisis = "open"
        if v12_ian_lena_breakup and lena_cheating == 0.5:
            $ lena_cheating = 0
        jump JD_chapter_thirteen







    jump JD_end
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc

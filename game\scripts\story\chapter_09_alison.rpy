
##################################################################################################################################################################################################################
## ALISON WEEKEND TRIP ##################################################################################################################################################################################################################
##################################################################################################################################################################################################################

label v9tripalison_start:
    call calendar(_day="Thursday") from _call_calendar_74

    $ fian = "n"
    $ ian_look = 1
    $ falison = "n"
    if alison_sexy == 2:
        $ alison_look = "cool"
    elif alison_sexy == 1:
        $ alison_look = 2
    else:
        $ alison_look = 1
    scene street with long
    "I woke up, had breakfast, and quickly packed my things before going to the train station to meet with Alison."
    show ian with short
    if ian_lena_dating:
        $ fian = "sad"
        "I still wondered if it was a good idea to go on this trip with her... I had the feeling Lena wasn't too stoked about it, even though I had omitted the specifics about my relationship with Alison."
        if ian_lena_love:
            "We weren't dating, not really, but... Lena was becoming someone special to me, and yet here I was, fooling around with my high school friend."
        else:
            "I mean, I was free to do whatever I wanted, and so was Lena, but still..."
        $ fian = "n"
        i "Well, I guess it's too soon to decide on anything. I still need to see how things pan out."
        if v7_holly_kiss:
            $ fian = "worried"
            "Was I making the same mistake I did with Holly already?"
            $ fian = "n"
            i "Alison is not Holly... It's not the same."
    elif ian_holly_dating:
        $ fian = "sad"
        "I still wondered if it was a good idea to go on this trip with Alison... Holly didn't seem to think anything of it, but that was only because she was too naive."
        "I felt dirty doing that now that Holly and I had become close. We talked about taking things slow, so it could be said we weren't officially dating, but still..."
        "That sounded like a pretty weak excuse."
    elif ian_lena_over:
        $ fian = "n"
        "I thought about Lena, on how our incipient relationship had found a premature end."
        "I wondered if things had gone differently, maybe I would be taking this trip with Lena. That would've been..."
        i "There's no point thinking about it now. It is what it is."
    else:
        "At first, I had been a bit hesitant about the idea, but I had warmed up to it."
        $ fian = "shy"
        "Spending a weekend alone with Alison looked quite promising if I had to judge from our past experiences..."
    show ian at lef with move
    show alison at rig with short
    $ fian = "smile"
    a "There you are! When did you become so punctual?"
    i "What are you talking about? I've always been punctual."
    $ falison = "smile"
    a "Anyway, it's finally the day!"
    a "You have no idea how much I've been looking forward to this little escapade!"
    menu:
        "Me too":
            $ renpy.block_rollback()
            i "Yeah, me too. I couldn't wait to spend some time with you."
            $ falison = "blush"
            a "Really?"
            $ falison = "n"
            if ian_alison < 12:
                if ian_alison == 11:
                    call friend_xp('alison') from _call_friend_xp_660
                    $ ian_alison = 12
                else:
                    call friend_xp('alison', 2) from _call_friend_xp_661
            a "Anyway, I've been needing to get away so badly, even if it's just for a couple of days!"

        "You deserve to take a break":
            $ renpy.block_rollback()
            i "Yeah, you deserve to take a break after all the pressure you've been under lately."
            $ falison = "sad"
            a "Yeah... And it keeps piling up."
            $ falison = "n"
            a "But I don't want to talk about that this weekend. I've been needing to get away so badly, even if it's just for a couple of days!"

        "Sure":
            $ renpy.block_rollback()
            i "Sure."
            $ falison = "serious"
            a "I expected a bit more enthusiasm on your part."
            $ fian = "n"
            i "What? I am enthusiastic."
            a "How can you say that making a face like that?"
            $ fian = "sad"
            i "What's wrong with my face? This is me showing my enthusiasm!"
            $ falison = "sad"
            a "Oh, God..."
            call friend_xp('alison', -1) from _call_friend_xp_662
            $ falison = "n"
            a "Anyway, with all the pressure I've had at work, I've been needing to get away so badly, even if it's just for a couple of days!"
            $ fian = "smile"

    $ falison = "smile"
    a "I'm glad I found a victim willing to come with me."
    i "Yeah, I fell for it. Tough luck, huh?"
    a "Come on, let's get aboard the train. I want to relax, so no work-related talk, okay?"
    i "Alright, I'll keep my mouth shut--{w=0.6}{nw}"
    play sound "sfx/ring.mp3"
    $ fian = "n"
    $ falison = "sad"
    "Before I could finish the sentence, Alison's phone rang, and her expression changed."
    a "Oh, no. Please, don't tell me..."
    $ falison = "serious"
    $ fian = "n"
    a "Goddammit!"
    hide alison
    show alison_phone at rig
    with short
    a "Yes, Angela. What is it?"
    a "Wait, wait. You opened what file? Yeah, no, it's okay..."
    $ falison = "sad"
    a "Start from the beginning, Angela. Let's sort it out step by step."
    "Alison sighed and looked at me pitifully. It sounded like she had gotten a call from the office."
    scene street with long
    play sound "sfx/train.mp3"
    scene train_travel with long
    "We boarded the train and Alison was still on the phone. In fact, she spent most of the voyage trying to manage whatever issue the intern was calling about."
    "At one point it seemed like everything had been worked out, but Alison received a second call just thirty minutes later. She wasn't happy."
    "I tried to block the chatter out and do a bit of reading on one of the books I had on my schedule."
    scene village with long
    play music "music/normal_day3.mp3" loop
    "We finally arrived at our destination and got off the train, en route to our hotel room."
    $ fian = "smile"
    $ falison = "sad"
    show ian at lef
    show alison at rig
    with long
    a "I'm so sorry about that! They had me almost two hours on the phone!"
    $ fian = "n"
    i "Don't be sorry. If anything, I'm sorry for you..."
    i "You told them you were taking these days off, right?"
    $ falison = "serious"
    a "Yeah, yeah. But as I told you, they can't do shit without me. If I don't take care of things nobody does."
    $ fian = "worried"
    i "And you didn't get the promotion?"
    a "No, I didn't. Someone's niece got promoted, and now the girl who's taking her old position was freaking out on the other side of the phone."
    a "And who has to take care of it? {i}Me{/i}."
    "Alison's voice was tense like a wire, but she sighed and went back to her friendly tone."
    $ falison = "n"
    a "Anyway, I already told you I don't want to talk about work. I'm here to get away from all that."
    menu:
        "Work is work":
            $ renpy.block_rollback()
            $ fian = "n"
            i "Well, work is work, after all... And I know you're a career-driven person."
            if ian_wits < 9:
                call xp_up('wits') from _call_xp_up_499
            $ falison = "sad"
            a "I am... But I'd like to feel fulfilled by doing it, even if it's only one bit. Get some credit at least!"
            $ fian = "smile"
            i "If you keep proving yourself essential they'll have no other choice but to give you your due credit."
            i "When they realize they're lost without you, you'll get your satisfaction."
            a "I'm starting to doubt it... But enough talking about that!"
            $ falison = "n"
            a "I don't think they're gonna bother me again today. I've explained everything she needs to do to a T."
            a "If that girl doesn't get it after that, it means she's literally brain-dead. So let's hope she's not and stops bothering me for today."

        "I hope they let you":
            $ renpy.block_rollback()
            $ fian = "smile"
            i "I hope they let you."
            a "If that girl doesn't get it after me explaining it to her for two hours it means she's literally brain-dead."
            a "So let's hope she's not and stops bothering me for today."

        "Shut your phone off":
            $ renpy.block_rollback()
            $ fian = "smile"
            i "Shut your phone off."
            $ falison = "sad"
            a "Huh? But what if they call?"
            $ fian = "happy"
            i "Don't pick up!"
            $ falison = "serious"
            a "But if I don't, I'm sure they're gonna make a mess, and who will they make responsible for it?"
            a "I already told you: {i}me{/i}."
            $ fian = "smile"
            i "You're here to get away from that, so let it go at least for a couple of days. You'll worry about it on Monday."
            if ian_charisma < 9:
                call xp_up('charisma') from _call_xp_up_500
            $ falison = "sad"
            a "I suppose you have a point..."
            a "I'm sorry, Alison from the future: you'll have to be the one to suffer."
            $ falison = "n"
            a "Right now, I'm gonna be selfish."
## SIGHTSEEING
    scene village with long
    "We left our luggage at the hotel and got down to take a stroll around the city."
    show ian at lef
    show alison at rig
    with short
    if v7_holly_trip:
        "I had been there recently, so I already knew some of the streets and places. I could even show Alison around, which she liked."
        "The mood was different from the book fair, but the terraces were still pretty full of people enjoying the sunny weekend."
    else:
        "We arrived at a big square surrounded by picturesque architecture."
        "The place was cozy but vibrant, and the terraces of many bars and restaurants were quite full of people enjoying the sunny weekend."
    a "{i}Ahhh...{/i} This place feels completely different from our city. Everything feels... smaller, and more laid back."
    i "Did you have something in mind? Someplace you want to visit, or..."
    $ falison = "smile"
    a "One or two... But for now, let's just take a walk and enjoy the day."
    "I walked with Alison for several hours, wandering around the city, discovering its streets and landmarks. But I was looking at her the most."
    if alison_sexy == 2:
        "And I wasn't the only one. I noticed more than a few guys turning their heads to look at her, but Alison didn't seem to notice."
        "Since Alison changed her style she had been looking so damn sexy... Much hotter than I ever thought possible."
        "Turns out Alison was actually a bombshell of a woman, and I was the lucky guy who would enjoy her in bed tonight. I couldn't wait..."
    elif alison_sexy == 1:
        "The way she had been dressing lately made her look way sexier than she used to. Alison turned out to be way more attractive than I thought at first..."
        "And her best assets were revealed once she lost those clothes. I couldn't wait to enjoy the view again tonight..."
    else:
        "Maybe her sense of style was a bit plain, and she didn't give off that \"sexy girl\" vibe, but she was undeniably attractive..."
        "Especially knowing how she looked under those clothes. I couldn't wait to enjoy the view again tonight..."
    "We had a quick lunch next to the commercial area, and after that Alison seemed interested in checking out a few stores."
    a "I've heard this place is really famous for its chocolates. I'd like to try a few, and my mom asked me to buy some for her!"
    show alison at rig3 with move
    "I followed Alison's lead. For someone who complained a lot about how overworked she was, she seemed to have a lot of energy!"
    "We spent a couple of hours visiting stores. I found a pretty interesting book shop, a cool antiquarian, and I ended up buying some chocolates too."
## CLOTHING SHOP
    a "Look! Let's check this clothing store for a second!"
    show ian at truecenter with move
    i "Do you want to buy something?"
    $ falison = "flirt"
    a "No, but you should."
    $ fian = "n"
    i "Me?"
    a "Yeah! When was the last time you went shopping?"
    a "No offense, but I'm tired of seeing that ragged hoodie!"
    i "You really hate it, don't you?"
    a "Look at this! Don't you think it'll suit you? Let's go in!"
    $ fian = "smile"
    show ian at lef
    show alison at rig
    with move
    "I ended up following Alison inside. She picked up some clothes she thought might look good on me, and I chose a few myself too."
    a "Go into the fitting room and try those on!"
    hide alison with short
    show ian at left with move
    hide ian
    $ fian = "n"
    show ianunder at left
    with short
    i "Let's see..."
    label v9clothingian:
        call screen v9clothingshop
    label v9shopwits:
        hide ianunder with short
        $ ian_look = "wits1"
        $ fian = "smile"
        pause 0.5
        show ian with long
        i "Mhh, this one looks nice."
        menu:
            "{image=icon_pay.webp}Buy this outfit" if ian_money > 0:
                $ renpy.block_rollback()
                $ ian_wardrobe_wits1 = True
                $ v9_ianwearing = "wits"
                i "I like it. I'm getting this one."
                call money(-1) from _call_money_74
            "Put it back":
                $ renpy.block_rollback()
                $ fian = "n"
                i "I'm not sure that's my style, though. I'll look at something else first."

        $ renpy.block_rollback()
        hide money_down
        hide ian with short
        $ ian_look = 1
        $ fian = "n"
        show ianunder with short
        show ianunder at left with move
        jump v9clothingian
    label v9shopcharisma:
        hide ianunder with short
        $ ian_look = "charisma1"
        $ fian = "smile"
        pause 0.5
        show ian with long
        i "Damn, I look sharp in this one."
        menu:
            "{image=icon_pay.webp}Buy this outfit" if ian_money > 0:
                $ renpy.block_rollback()
                $ ian_wardrobe_charisma1 = True
                $ v9_ianwearing = "charisma"
                i "I'm definitely buying this one."
                call money(-1) from _call_money_75
            "Put it back":
                $ renpy.block_rollback()
                $ fian = "n"
                i "I'm not sure if this look is for me, though. Let me check something else..."
        $ renpy.block_rollback()
        hide money_down
        hide ian with short
        $ ian_look = 1
        $ fian = "n"
        show ianunder with short
        show ianunder at left with move
        jump v9clothingian
    label v9shopathletics:
        hide ianunder with short
        $ ian_look = "athletics1"
        $ fian = "smile"
        pause 0.5
        show ian with long
        i "Hey, I'm getting some gains at the gym... This shirt really makes them shine."
        menu:
            "{image=icon_pay.webp}Buy this outfit" if ian_money > 0:
                $ renpy.block_rollback()
                $ ian_wardrobe_athletics1 = True
                $ v9_ianwearing = "athletics"
                i "I like it. I'm bagging this one."
                call money(-1) from _call_money_76
            "Put it back":
                $ renpy.block_rollback()
                $ fian = "n"
                i "Not sure about this look, though. Let's see what else I have here..."
        $ renpy.block_rollback()
        hide money_down
        hide ian with short
        $ ian_look = 1
        $ fian = "n"
        show ianunder with short
        show ianunder at left with move
        jump v9clothingian
    label v9shoplust:
        hide ianunder with short
        $ ian_look = "lust1"
        $ fian = "smile"
        pause 0.5
        show ian with long
        i "Look at this... I'd say I can rock the bad boy look pretty good!"
        menu:
            "{image=icon_pay.webp}Buy this outfit" if ian_money > 0:
                $ renpy.block_rollback()
                $ ian_wardrobe_lust1 = True
                $ v9_ianwearing = "lust"
                i "I like how I feel in these. I'm getting them."
                call money(-1) from _call_money_77
            "Put it back":
                $ renpy.block_rollback()
                $ fian = "n"
                i "I wonder if other people will think the same, though. Maybe I can try something else..."
        $ renpy.block_rollback()
        hide money_down
        hide ian with short
        $ ian_look = 1
        $ fian = "n"
        show ianunder with short
        show ianunder at left with move
        jump v9clothingian
    label v9leaveshop:
        $ renpy.block_rollback()
        hide money_down
        if v9_alison_trip == False:
            jump v9ianthrusdaypregym
        hide ianunder with short
        $ ian_look = 1
        $ fian = "n"
        show ian with short
        i "Alright, I'm done here."
        show ian at lef with move
    show alison at rig with short
    a "That was quick! Are you buying something?"
    if ian_wardrobe_wits1 or ian_wardrobe_charisma1 or ian_wardrobe_athletics1 or ian_wardrobe_lust1:
        i "Yeah."
        $ falison = "sad"
        a "Without showing me?"
        i "I'll show you later."
        $ falison = "n"
    else:
        $ fian = "n"
        i "No, not really..."
        $ falison = "sad"
        a "Are you sure?"
        i "Yeah. I haven't found anything that really suits my taste. Besides, I'm pretty tight on money right now, so..."
        $ falison = "n"
        a "I still think you need to update your wardrobe, but okay."
## AFTERNOON- HIEROFANT CALL
    scene village with long
    "When we left the store the sun was starting to set."
    show ian at lef
    show alison at rig
    with short
    if v5_ian_showup:
        a "It's getting late. I..."
        play sound "sfx/ring.mp3"
        $ fian = "n"
        "This time the interrupting phone call was mine. It was from an unknown number."
        i "Sorry, let me pick this up."
        hide ian
        show ian_phone at lef
        with short
        i "Yes?"
        woman "Hello, is this Ian Watts?"
        if ian_charisma > 6 or ian_chad > 4:
            i "Yeah, the one and only."
        else:
            i "That's me, yeah."
        woman "I'm calling from human resources at Hierofant publishing."
        $ fian = "worried"
        i "...!" with vpunch
        "My heart skipped a beat when hearing that name. This was the call I had been waiting for."
        woman "We received your resume and application for the intern beta-reader position..."
        woman "And we'd like to offer you a job at the company."
        woman "Would you be interested?"
        $ fian = "happy"
        i "Y-{w=0.3}yeah, of course! That's why I contacted you guys..."
        woman "Excellent. Would you be able to fill in the position at the start of July? In two weeks, let's say."
        i "Yeah, no problem."
        woman "Alright! We'll contact you at a later date and send you all the additional information and the contract."
        woman "Have a nice end of the week!"
        i "Bye!"
        hide ian_phone
        show ian at lef
        with short
        a "What was that about? You look happy..."
        "I hugged Alison and lifted her up, laughing." with vpunch
        $ falison = "smile"
        a "Ouch! I take it was good news?"
        i "Yeah! I've gotten that position I was aiming for!"
        a "Congratulations!"
        if ian_will < 2:
            call will_up() from _call_will_up_54
        $ fian = "smile"
        i "It took a while, and I was afraid they had passed on my application, but... I got it!"
        if ian_job_magazine < 2:
            "I felt a huge load lift off my shoulders. All the risks I had taken were finally bearing fruit."
            if ian_job_magazine == 0:
                "No more food deliveries for me... I would finally get a job I was excited about."
            else:
                "I would finally escape Minerva's clutches. No more putting up with her bullshit..."
        else:
            if ian_defy_minerva or ian_minerva_sex:
                "I had managed to beat Minerva at her own game, but I still wasn't happy working at that office."
                "I was finally getting a chance to do something I felt excited about."
            else:
                "I had been hating my job for far too long. I was, at last, getting a chance to do something I felt excited about."
        i "This news really boosted my morale. I was really needing this..."
        a "I'm so glad for you! And I have the perfect plan to celebrate the good news!"
        a "I made a dinner reservation in advance at a nice restaurant that was recommended to me..."
        $ falison = "smile"
    else:
        a "It's getting late! I made a dinner reservation in advance at a nice restaurant that was recommended to me..."
    i "That's what you had planned and didn't want to tell me?"
    $ falison = "flirt"
    a "Just part of it."
    $ falison = "n"
    a "Come on, let's go to the hotel. We need to get changed!"
    i "Is it a fancy place? I haven't packed my tuxedo..."
    a "It'll be fine. Just don't wear that hoodie."
    if ian_wardrobe_wits1 or ian_wardrobe_charisma1 or ian_wardrobe_athletics1 or ian_wardrobe_lust1:
        a "It's the perfect chance for you to show me what you just bought!"
## HOTEL CLOTHES
    stop music fadeout 2.0
    scene hotelnight with long
    "We went back to our room and Alison locked herself in the bathroom to get ready."
    $ ian_look = 3
    $ alison_look = "dress"
    $ alison_makeup = 1
    show ianunder with short
    i "Let's see... What should I wear tonight?"
    show ianunder at left with move
    call screen v9ianclotheschoice
    hide ianunder with short
    pause 0.5
    show ian at left with long
    i "Alright, this will do."
    a "I'm ready."
    show ian at lef with move
    pause 0.5
    $ fian = "surprise"
    play music "music/alisons_theme.mp3" loop
    show alison at rig with long
    a "Maybe it's a bit too flashy? Or too elegant? I'm not sure..."
    menu:
        "{image=icon_charisma.webp}You look gorgeous" if ian_charisma > 5:
            $ renpy.block_rollback()
            "I didn't try to hide my surprise."
            $ fian = "happy"
            i "Wow, Alison... You look gorgeous."
            $ falison = "smile"
            a "Gorgeous... That's not a word I hear a lot, especially when used to describe me!"
            $ fian = "smile"
            i "Come on! I'm sure you're no stranger to people complimenting you."
            $ falison = "n"
            a "Well, some do on occasion... But they call me \"sexy\" or just \"hot\"."
            $ falison = "flirt"
            a "I prefer your fancy words."
            if ian_alison < 12:
                call friend_xp('alison', 1) from _call_friend_xp_663
            i "Fancy words for a fancy dress."

        "It's cool":
            $ renpy.block_rollback()
            "I tried to hide my surprise. I wasn't expecting to see Alison wearing a dress like that..."
            $ fian = "n"
            i "Um, yeah, it's cool."
            a "Just \"cool\"?"
            i "Yeah, I don't know. It suits you. Make you look... mature."
            $ falison = "sad"
            a "Mature?"
            $ fian = "smile"
            i "But in a good way!"
            $ alison = "n"
            a "Alright..."

        "You're making me horny":
            $ renpy.block_rollback()
            $ fian = "confident"
            "I got closer to Alison and held her hips, delighting myself with the view."
            scene v9_alison1_bg1
            if ian_look == "wits1":
                show v9_alison1a
            elif ian_look == "charisma1":
                show v9_alison1b
            elif ian_look == "athletics1":
                show v9_alison1c
            elif ian_look == "lust1":
                show v9_alison1d
            else:
                show v9_alison1e
            with long
            i "Damn, Alison... That dress is killer..."
            i "That cleavage is making me really horny right now."
            if ian_lust < 9:
                call xp_up('lust') from _call_xp_up_501
            "I began running my hands over her body, caressing one of her boobs, threatening to liberate it from the dress..."
            a "Wait, wait...!"
            $ falison = "flirt"
            scene hotelnight
            show ian at lef
            show alison at rig
            with short
            a "Not so fast, lady killer! We will be late for dinner... And besides, we have all night..."
            "It took some effort to get my hands off Alison, but I did as she asked."
            i "I hate waiting."
            a "I know... But they say good things are worth waiting for, don't they?"

    if ian_look == "wits1":
        a "You're looking handsome! This style seems to really suit you."
        i "It looks a bit subdued next to yours, but that means you'll only shine brighter next to me."
        $ falison = "blush"
        a "You're right, maybe I look too overdressed? Should I tone it down?"
        i "Don't even think about it. Come on, let's go."
        $ falison = "n"
        a "Alright."
    elif ian_look == "charisma1":
        $ falison = "smile"
        a "So this is what you picked for tonight? You look sharp..."
        i "The least I can do is look good for you since you're doing the same."
        a "I'd say we look pretty good together, don't you think so?"
        if ian_alison < 12:
            call friend_xp('alison', 1) from _call_friend_xp_664
        $ falison = "n"
        a "Come on, let's go!"
    elif ian_look == "athletics1":
        $ falison = "sad"
        a "So that's what you picked for tonight?"
        $ fian = "n"
        i "Yeah, what's the problem? I thought you liked it."
        $ falison = "n"
        a "Yeah, you look good... But you make me look overdressed!"
        i "I can pick something else..."
        a "No, it's okay. Come on, let's go or we'll be late!"
    elif ian_look == "lust1":
        $ falison = "flirt"
        a "I see you picked the bad boy look for tonight."
        $ fian = "confident"
        i "I'd say it goes well with your sexy vixen attire."
        $ falison = "smile"
        a "I was going for \"glamorous\" rather than \"sexy vixen\", but I guess that also works."
        $ falison = "n"
        a "Come on, let's go!"
    else:
        a "I see you're wearing your classic red T-shirt."
        i "Yeah. I think it looks pretty good, didn't you say so yourself?"
        a "Yeah, yeah. It's starting to look a bit worn out, though."
        $ fian = "n"
        i "I've owned it for quite some time, that's true..."
        a "Doesn't matter. Come on, let's go or we'll be late!"

## ALISON DINNER
    stop music fadeout 3.0
    scene villagenight with long
    pause 1
    $ fian = "smile"
    $ falison = "n"
    "The restaurant wasn't far from the hotel, situated right in the main square of the city."
    play music "music/normal_day4.mp3" loop
    show ian at lef
    show alison at rig
    with short
    if alison_sexy == 2:
        "I noticed how Alison had been turning some heads during the day. Well, now she was getting twice as many looks and no wonder..."
        "She still seemed oblivious to it, though..."
    elif alison_sexy == 1:
        "Despite her obvious attractiveness, Alison had never been the kind of girl to really stand out. But that night was different."
        "I could notice people turning their heads to look at her, and then at me. She seemed oblivious to it, though..."
    else:
        "Normally, Alison wasn't the kind of girl to really stand out. But that night it was different."
        "I could notice people turning their heads to look at her, and then at me. She seemed oblivious to it, though..."

    "We took a seat at the terrace. It was cozy and lit with candle fires."
    a "What do you think? The place is beautiful!"
    i "Let's hope the food is equally good... Look at those prices."
    $ falison = "serious"
    a "Don't be such a killjoy! Positive attitude, please!"
    $ falison = "n"
    a "And don't look at the prices. The meal's on me."
    menu:
        "{image=icon_pay.webp}I'm the one paying tonight" if ian_money > 0:
            $ renpy.block_rollback()
            if ian_chad < 5:
                $ ian_chad += 1
            $ fian = "n"
            i "No way. I'm the one paying tonight."
            a "Didn't you say you were tight on money?"
            i "Yeah, but I want to treat you to something nice..."
            if ian_charisma < 9:
                call xp_up('charisma') from _call_xp_up_729
            a "You being here is already nice enough."
            $ fian = "smile"
            i "Let's split the bill, at least."
            a "If you insist."
            call money(-1) from _call_money_78

        "Let's split the bill":
            $ renpy.block_rollback()
            $ fian = "n"
            i "You're paying? Let's split the bill, at least."
            a "Didn't you say you were tight on money?"
            $ fian = "sad"
            i "Yeah, but..."
            $ falison = "smile"
            a "Don't worry, as far as I'm concerned, your manly pride remains intact even if you let a girl pay for your dinner, ha ha!"
            $ falison = "n"
            a "No, really. I don't mind, I'm saving up quite a lot since I went back to live at my parents' place..."
            i "If you insist..."
            $ fian = "smile"

        "Awesome, thank you!":
            $ renpy.block_rollback()
            if ian_chad > 0:
                $ ian_chad -= 1
            $ fian = "happy"
            i "Awesome. Thank you, Alison."
            a "No worries. I know you said money was tight, and I'm saving up quite a lot since I went back to live at my parents' place, so..."

    a "Let's just enjoy the meal!"
    "We ordered our food and a bottle of wine. I couldn't remember when it had been the last time I had such a fancy dinner, and it was pretty good."
    if v5_ian_showup:
        a "Let's toast: to your new job!"
        i "And to nights like these!"
    else:
        a "Let's toast: to nights like these!"
    play sound "sfx/toast.mp3"
## ask about relationships
    $ falison = "flirt"
    if ian_lena_dating or ian_lena_over:
        a "I wonder if the model would get jealous of us having dinner together."
        $ fian = "worried"
        "I almost spat out the wine. Alison suddenly bringing Lena into the conversation caught me off guard."
        "She could be so blunt sometimes..."
        if ian_lena_over:
            $ fian = "n"
            i "I don't think she would. Lena and I are no longer... hooking up."
            $ falison = "n"
            a "Oh, that was short-lived. Did she break up with you or was it your choice?"
            i "No, we didn't {i}break up{/i}. We weren't dating, to begin with, so... We're still friends, though."
            a "So, friends but without the benefits. I see. Sorry for your loss."
            i "Don't be. But why is it you're always asking me about her?"
            a "I'm interested in what goes on in your life! And I also like me some gossip..."
            "I decided to shift the focus of the conversation to Alison."
        else:
            $ fian = "n"
            i "How come you always ask me about her?"
            $ falison = "n"
            a "I don't know, you seem to have a pretty interesting life! And I also like me some gossip..."
            a "You told me you two aren't officially dating, right? Just hooking up."
            if ian_lena_love:
                i "Well, yeah, you could say that's how it is..."
            else:
                i "Yeah, that's how it is..."
            a "Then she shouldn't have any reason to get jealous, right?"
            i "I guess."
            "Talking about Lena with her made me feel pretty uncomfortable, so I decided to shift the focus of the conversation to Alison."
            if ian_lena_love:
                "I felt a bit like a piece of shit, actually..."
    else:
        a "So, any new erotic adventures to tell?"
        if ian_holly_dating:
            a "I've heard you've recently gotten close to that girl from your office, the one you told me about once..."
            $ fian = "worried"
            a "The writer."
            $ fian = "disgusted"
            i "How do you know that?"
            $ falison = "n"
            a "The guys invited me to come this past Wednesday, too, but I couldn't make it. They said you brought a new girlfriend, what was her name... Holly?"
            $ fian = "n"
            "Of course... I should've expected it after introducing Holly to the group."
            i "Yeah, that's her name."
            a "So, what's the deal with you two?"
            "Alison could be so blunt. It was a rather uncomfortable topic to discuss with her, so I tried keeping it vague."
            "I felt a bit like a piece of shit, actually..."
            i "We're just taking it slow, see where it goes..."
            $ falison = "flirt"
            a "I see... Well, be sure to let me know if you end up getting somewhere with her!"
            "I decided to shift the focus of the conversation to Alison."
            i "Sure... But why is it you're always asking me about girls?"
        else:
            i "Not really..."
            if ian_minerva_sex and v7_cindy_kiss:
                $ fian = "n"
                "Not ones I could talk about, at least. Minerva..."
                $ fian = "worried"
                "And Cindy. If Alison only knew what had been happening between me and her..."
            $ falison = "n"
            a "Nothing? Too bad, huh...?"
            $ fian = "n"
            i "Why is it you're always asking me about girls?"
        a "I'm interested in what goes on in your life! And I also like me some gossip..."
        "I decided to shift the focus of the conversation to Alison."
    $ fian = "smile"
    i "What about you, do you have any interesting gossip yourself? How's your love life going?"
    $ falison = "smile"
    a "My love life? I don't have such a thing... Closest I get to that is you, ha ha."
    if v7_alison_problems:
        $ falison = "n"
        a "I haven't heard from Milo in weeks... I followed your advice and blocked him."
    else:
        $ falison = "sad"
        a "I haven't heard from Milo in weeks... I feel kinda guilty about it, but I ended up blocking him."
    $ fian = "sad"
    "Fuck. Alison's ex."
    a "I couldn't stand so much drama... I'm stressed out enough as already is!"
    i "Oh, yeah, about that... I guess you don't know what happened a couple of weeks ago, right...?"
    $ falison = "sad"
    a "What happened with what? With Milo?"
    $ fian = "n"
    i "Yeah. Your ex-boyfriend showed up when Jeremy and I were leaving the gym, looking for a fight."
    $ falison = "surprise"
    a "What!? Milo wanted to fight you? Why?"
    i "Well, it seems he knew one of us had been... sleeping with you. And he wasn't happy about that."
    $ falison = "blush"
    a "Oh, no..."
    $ falison = "mad"
    a "Did that idiot really do that? What's wrong with him?"
    i "He didn't look like he had all his wits with him, if I may say so. He was... erratic, and quite aggressive."
    $ falison = "blush"
    a "And did something happen? Did he cause any trouble for you guys?"
    if v8_alison_ex == "ian":
        i "Well, I kinda... knocked him out."
        $ falison = "surprise"
        a "You did what?"
        $ fian = "sad"
        i "He jumped at me and I defended myself instinctively! I don't think he was seriously hurt, though..."
    else:
        i "Well, Jeremy, he... ended up knocking him out."
        $ falison = "surprise"
        a "He did what?"
        i "Milo jumped at us, and Jeremy defended himself. He only took a smack, so I don't think he was seriously hurt..."
    $ falison = "blush"
    a "Oh, no... I'm so sorry that happened...!"
    a "Something must've slipped last time I was talking with him before I blocked him. I got a bit too emotional..."
    i "It's okay. If anything, I feel sorry for him."
    $ falison = "serious"
    a "Why didn't you tell me before?"
    if alison_jeremy:
        if v8_alison_ex == "ian":
            i "I don't know. I thought maybe Jeremy would tell you..."
        else:
            i "I don't know... I supposed Jeremy would tell you. He was the one who knocked him out, after all."
        a "I haven't talked with him in a while, either."
    else:
        i "I don't know... I thought you'd probably learn from Milo directly. And I didn't want to put more onto your plate, knowing how stressed you've been."
    $ falison = "sad"
    a "You should've told me... Again, I'm so sorry you guys got involved in my mess."
    $ fian = "smile"
    i "Hey, it's fine... We all have our dramas, and that's what friends are for, right?"
    $ falison = "n"
    a "To knock out my violent ex-boyfriend?"
    i "If that's what's necessary..."
    $ falison = "sad"
    a "Seriously though, I'm worried about him. Maybe I shouldn't have blocked him... I'm sure he wouldn't have lashed out at you guys if I didn't."
    $ fian = "n"
    i "You did the right thing if you ask me. He's clearly proven he's not someone who can be reasoned with right now."
    if ian_chad > 3:
        i "He needs to sort his shit out before trying to talk to you again, or to anyone."
    else:
        i "He needs to do some soul-searching and put his emotions in order before trying to talk to you again."
    a "You're right... I'll need to call Jeremy and apologize, too."
## personal drama
    a "Agh, this is so frustrating... Why do these absurd things keep happening to me?"
    $ fian = "n"
    a "I'm almost twenty-eight. I'm supposed to have my life more or less sorted out by now, and I don't have anything..."
    menu:
        "Listen to Alison":
            $ renpy.block_rollback()
            if ian_chad > 2:
                $ ian_chad -= 1
            $ fian = "sad"
            i "Come on, you know that's not true. You have a degree, two masters, and a good resume..."
            a "And what did all that amount to? I'm stuck in a dead-end job, I'm back at my parents' place and single."
            a "I've already been to the weddings of three of my friends from college, and one of them is about to give birth."
            a "Meanwhile, I feel like I haven't moved forward one step since my student days. And I don't see my situation changing anytime soon..."
            "I tried to stir Alison away from those negative thoughts."
            $ fian = "n"
            i "I thought we weren't supposed to talk about those things during this trip."
            a "You're right... I'm sorry, I just feel so frustrated. It's hard getting it off my mind."
            a "Sometimes I wish I was as carefree as Jeremy, or Perry."
            $ fian = "smile"
            i "I don't think you'd like to be in Perry's shoes. Compared to him, you're doing incredible."
            $ falison = "smile"
            a "Yeah, now that you mention it, what is his deal? He's not working, he's not studying...?"
            menu:
                "{image=icon_friend.webp}He needs to find some motivation" if ian_perry > 5:
                    $ renpy.block_rollback()
                    $ fian = "sad"
                    i "He just needs to find some motivation... whatever that motivation might be."
                    $ falison = "serious"
                    a "Yeah, he doesn't seem particularly interested in digging himself out of the pit. That attitude gets on my nerves."
                    i "Well, sometimes you can't do stuff on your own. That's what friends are for:"
                    $ fian = "smile"
                    i "To lend a helping hand."
                    $ falison = "n"
                    a "He needs more than a helping hand... A hand that drags him out of his comfort zone, rather."
                    $ fian = "sad"
                    i "Yeah, you're right..."

                "He can afford to be careless":
                    $ renpy.block_rollback()
                    i "Well, he can afford to be careless. His father's the mayor and he doesn't have to pay rent."
                    a "Yeah. It's not like his family lives in luxury, but he won't have money problems anytime soon."
                    $ falison = "serious"
                    a "But he will at some point, and then it'll be too late. That attitude gets on my nerves."
                    i "Let him enjoy the easy life while it lasts..."

                "{image=icon_mad.webp}He's a lazy fuck" if ian_perry < 6:
                    $ renpy.block_rollback()
                    $ fian = "serious"
                    i "He's a lazy fuck, honestly. He has zero motivation, the only things that get him out of bed are beer, porn, and video games."
                    $ falison = "serious"
                    a "Yeah, no motivation at all, not a single attempt to do something about his own life. That attitude gets on my nerves."
                    i "Well, he can afford to be careless. His father's the mayor and he lives off the rent I pay him. But he won't be able to leech off me forever."

            a "And what about Jeremy? Does he have a goal or does he plan on staying a bartender until he's forty?"
            if alison_jeremy:
                $ fian = "n"
                "Why was she suddenly asking me about him?"
            else:
                $ fian = "smile"
            i "I have no idea, but he doesn't seem in any hurry to move on."
            a "Pouring drinks at a nightclub sounds like a summer job you'd take during your early twenties. Not a good choice for a stable position..."
            if alison_jeremy:
                i "I don't think he's too worried about stability right now... Why haven't you asked him about this directly?"
                a "I don't know, it never came up, I guess?"
                $ fian = "worried"
                "So they hadn't talked that much, after all. Yeah, they probably used the time for other stuff..."
                a "Is everything alright?"
                $ fian = "smile"
                i "Yeah."
            else:
                i "I don't think he's too worried about stability right now."
                a "Well, I'm afraid he's in for a rude awakening."
                i "Maybe at some point... Who knows."
            $ falison = "smile"
            if v5_ian_showup:
                a "Anyway! Congratulations on your new job!"
                $ fian = "happy"
                i "Thanks! I'm still letting that sink in..."
                i "That call came at the best possible moment."
                a "See? It took a while, but something good happened!"
            else:
                a "Anyway! Congratulations on solving your workplace issue!"
                $ fian = "sad"
                i "Nothing got solved... And, to be honest, I'm not thrilled to have to spend more time at the office..."
                a "At least you won't need to keep working as a delivery guy!"
                $ fian = "n"
                i "Yeah... It's not the best job, but at least I know I can do it if I find myself in need."
                a "Come on, you can do much more than that!"
            a "And that book contest you've been talking about is near, isn't it?"

        "Pretend to listen":
            $ renpy.block_rollback()
            if ian_chad < 5:
                $ ian_chad += 1
            "Alison started ranting again about her workplace problems and venting out her frustrations."
            "Honestly, I wasn't interested in hearing again about how she didn't get along with her co-workers, her bad luck, and some other of her usual complaints."
            "I switched my brain off and started thinking about other stuff, pretending to listen."
            "At least the food was delicious."
            a "Ian?"
            i "{i}Mh-hm{/i}, yeah."
            $ falison = "serious"
            a "\"{i}Mh-hm{/i}, yeah\" what?"
            $ fian = "sad"
            i "Uh... Whatever you were saying."
            a "Are you even listening? Never mind..."
            call friend_xp('alison', -1) from _call_friend_xp_665
            $ falison = "n"
            a "So, what about that book contest you've been talking about. It's near, isn't it?"
            "Finally, something I could talk about."
    $ fian = "smile"
    i "Yeah... I'm about to finish the novel, I'm excited..."
    stop music fadeout 4.0
    scene villagenight with long
    "We continued chatting over the course of the meal. After dessert and a glass of champagne, we took a stroll, heading back to the hotel."
    show ian at lef
    show alison at rig
    with short
    label gallery_CH09_S03:
        if _in_replay:
            call setup_CH09_S03 from _call_setup_CH09_S03

    i "I really liked that restaurant. It was a good choice."
    $ falison = "smile"
    a "Yeah. I felt like a happy, sane human being tonight!"
    $ falison = "n"
    a "Thank you, Ian..."
    show ian at lef3
    show alison at rig3
    with move
    play sound "sfx/fireworks.mp3"
    show fireworks at fireworks_anim behind ian
    a "Oh, fireworks!"
    i "Is today a special day?"
    hide fireworks with long
    "Alison looked at me, and for the first time in the evening, kept quiet."
    $ timeout = 5.0
    $ timeout_label = "v9alisonsilence"
    $ renpy.block_rollback()
    menu:
        "{image=icon_lust.webp}Let's hurry to the hotel":
            $ renpy.block_rollback()
            $ v9_fireworks = "lust"
            $ fian = "confident"
            "I knew what that look meant."
            play music "music/sex_hot.mp3" loop
            show ian at centerlef with move
            i "I'm tired of waiting, Alison. I want you, now."
            $ falison = "flirt"
            a "What's up with this sudden burst of passion?"
            play sound "sfx/fireworks.mp3"
            show fireworks at fireworks_anim behind ian
            i "It's not sudden at all. I've been dying to fuck you the whole night. The whole day."
            "I took her hand and led the way."
            i "I can't wait any longer. Let's go!"

        "{image=icon_love.webp}Embrace her" if ian_alison > 9 and ian_lena_love == False and ian_holly_dating == False:
            $ renpy.block_rollback()
            $ v9_fireworks = "love"
            "I smiled at her."
            play music "music/alisons_theme.mp3" loop
            show ian at lef
            show alison at rig
            with move
            play sound "sfx/fireworks.mp3"
            i "I guess it is a special day, after all..."
            scene v9_alison1_bg2
            if ian_look == "wits1":
                show v9_alison1a
            elif ian_look == "charisma1":
                show v9_alison1b
            elif ian_look == "athletics1":
                show v9_alison1c
            elif ian_look == "lust1":
                show v9_alison1d
            else:
                show v9_alison1e
            with long
            "I wrapped my arms around Alison's waist and pulled her toward me, embracing her sweetly."
            if ian_alison < 12:
                if ian_alison == 11:
                    call friend_xp('alison', 1) from _call_friend_xp_666
                elif ian_alison < 11:
                    call friend_xp('alison', 2) from _call_friend_xp_667
            "Her eyes kept locked on mine, and her lips sealed, curved in a slight and complicit smile."
            "She also wrapped her arms around my shoulders, caressing my nape, and whispered:"
            a "It really is special."
            play sound "sfx/fireworks.mp3"
            scene v9_alison1_bg2
            if ian_look == "wits1":
                show v9_alison2a
            elif ian_look == "charisma1":
                show v9_alison2b
            elif ian_look == "athletics1":
                show v9_alison2c
            elif ian_look == "lust1":
                show v9_alison2d
            else:
                show v9_alison2e
            with long
            "She leaned in for a kiss, and I welcomed her happily."
            "Our tongues met for the first time in several weeks and I felt Alison's taste once again."
            "I could tell she had been longing for my kisses. I realized I had been, too."
            "I had never managed to get over the strange feeling of kissing someone taller than me, but that barely mattered at the moment."
            "We were letting ourselves go to the growing passion we shared."
            "I felt Alison pressing her body against mine, running her hands over my hair and my neck, tightening her grip..."
            "And my throbbing erection poking at her crotch through the fabric of our clothes."
            play sound "sfx/fireworks.mp3"
            $ falison = "flirt"
            $ fian = "confident"
            scene villagenight
            play sound "sfx/fireworks.mp3"
            show fireworks at fireworks_anim behind ian
            show ian at centerlef
            show alison at rig3
            with long
            "The thunder of an especially potent firework snapped us out of it. We were still in the middle of the street..."
            hide fireworks with long
            a "Have I already told you you're a great kisser?"
            i "I don't remember, but I'm happy to keep proving it to you."
            hide fireworks with long
            $ falison = "n"
            $ fian = "smile"
            play sound "sfx/fireworks.mp3"
            show fireworks at fireworks_anim behind ian
            "We watched the apotheosis of the firework show before going back to the hotel."
            "We were both dying to share more than just kisses..."

        "...":
            $ renpy.block_rollback()
            label v9alisonsilence:
                $ fian = "n"
            "Was she expecting something out of me?"
            play sound "sfx/fireworks.mp3"
            show fireworks at fireworks_anim behind ian
            i "..."
            a "..."
            "We kept quiet, watching the fireworks display for a few minutes."
            hide fireworks with long
            show ian at lef
            show alison at rig
            with move
            i "It's over..."
            a "It was nice. Come on, let's go back to the hotel!"
            $ fian = "smile"

## ALISON HOTEL NIGHT END #################
    $ timeout_label = None
    scene hotelnight_dark with long
    play sound "sfx/door.mp3"
    pause 0.6
    scene hotelnight
    show ian at lef
    show alison at rig
    with long
    if v9_fireworks == "lust":
        "As soon as we closed the door to the room I jumped on Alison."
        scene v9_alison1_bg1
        if ian_look == "wits1":
            show v9_alison2a
        elif ian_look == "charisma1":
            show v9_alison2b
        elif ian_look == "athletics1":
            show v9_alison2c
        elif ian_look == "lust1":
            show v9_alison2d
        else:
            show v9_alison2e
        with long
        "She welcomed my kiss and our tongues met for the first time in several weeks."
        "I could tell she had been longing for my kisses. So had I."
        "I had never managed to get over the strange feeling of kissing someone taller than me, but that barely mattered at the moment."
        "I Pressed Alison's body against mine, running my hands over her back, grabbing her butt tightly..."
        "My throbbing erection was poking at her crotch through the fabric of our clothes."
        "I felt her boobs against my chest, and I grabbed one with my hand..."
        $ falison = "flirt"
        scene hotelnight
        show ian at lef
        show alison at rig
        a "Wait, wait..."
        $ falison = "smile"
        a "I want to get it on too, but first I need to go to the bathroom!"

    elif v9_fireworks == "love":
        a "That was so nice. We were lucky to see those fireworks."
        i "A perfect evening. Or almost perfect."
        $ fian = "confident"
        i "I hope you saved up some energy..."
        scene v9_alison1_bg1
        if ian_look == "wits1":
            show v9_alison2a
        elif ian_look == "charisma1":
            show v9_alison2b
        elif ian_look == "athletics1":
            show v9_alison2c
        elif ian_look == "lust1":
            show v9_alison2d
        else:
            show v9_alison2e
        with long
        "I embraced Alison and kissed her again, continuing where we left off."
        "I felt her boobs against my chest, and I grabbed one with my hand..."
        $ falison = "flirt"
        scene hotelnight
        show ian at lef
        show alison at rig
        with short
        a "Wait, wait..."
        $ falison = "smile"
        a "I do have the energy, but first I need to go to the bathroom!"
    else:
        a "We've walked so much today! My feet hurt a bit."
        $ fian = "confident"
        i "I hope you still have some energy left..."
        $ falison = "flirt"
        a "Of course, I do..."
        $ falison = "smile"
        a "But first I need to go to the bathroom!"
    stop music fadeout 4.0
    $ fian = "smile"
    play sound "sfx/door.mp3"
    hide alison with short
    i "Okay, I'll wait."
    show ian at truecenter with move
    $ fian = "n"
    i "..."
    "I sat down on the couch and pulled up my phone."
    if ian_lena_dating:
        "I hadn't noticed I received a text from Lena a few minutes ago."
        nvl clear
        l_p "{i}How's the trip going? {image=emoji_smile.webp}{/i}"
        $ fian = "smile"
        if v7_holly_trip:
            i_p "{i}So far so good! I had been here recently, but this time I'm taking my time to enjoy the place. It's nice, you'd probably like it.{/i}"
        else:
            i_p "{i}So far so good! It's a very nice and colorful city, you'd probably like it.{/i}"
        play sound "sfx/sms.mp3"
        if lena_ian_love:
            l_p "{i}Maybe next time you can take me there and show me around {image=emoji_shy.webp}{/i}"
            i_p "{i}That sounds like a great idea. I'd love that {image=emoji_smile.webp}{/i}"
            l_p "{i}I'll hang on to your word on that! Spending so many days at my parents' place is taking a toll on me {image=emoji_disgust.webp}{/i}"
        else:
            l_p "{i}Glad you're having fun! God knows I could use a vacation, too {image=emoji_disgust.webp}{/i}"
            i_p "{i}Maybe we could spend a weekend together somewhere nice {image=emoji_glasses.webp}{/i}"
            l_p "{i}Anywhere but my parents' place. I'm so tired of being here.{/i}"
        l_p "{i}By the way, did you find time to finish your book yet?{/i}"
        "I started to type my reply, but I didn't get the chance to finish."
    elif ian_holly_dating:
        "I hadn't noticed I received a text from Holly a few minutes ago."
        nvl clear
        label v9hollynudes:
            h_p "{i}I'll probably regret sending these, but here you go  {image=emoji_shy.webp}{image=emoji_shy.webp}{/i}"
        show ian at left with move
        show v9_holly_selfie1 with short
        $ ian_holly_pics.append("v9_holly_selfie1.webp")
        $ fian = "surprise"
        i "Whoa!"
        "That caught me completely off guard. I wasn't expecting Holly to suddenly send me some sexy selfies..."
        $ fian = "shy"
        i "I'm so glad she did, though. Damn, she's so cute..."
        "Holly looked a bit awkward in the picture, it was clear she was stepping out of her comfort zone."
        "And she was doing it for me..."
        $ fian = "surprise"
        hide v9_holly_selfie1
        show v9_holly_selfie2
        with short
        $ ian_holly_pics.append("v9_holly_selfie2.webp")
        i "Hot damn!" with vpunch
        "She caught me off guard twice in a row. I wasn't prepared for that second picture."
        $ fian = "shy"
        i "Wow, Holly..."
        "Her naked body was in full display, for me, just for me..."
        "Her petite and soft figure, her smooth, pale skin, her small breasts, and..."
        if v9_alison_trip == False:
            jump v9hollynudes2
        $ fian = "worried"
        i "Fuck, now I feel bad. She's sending me these, and meanwhile, I'm..."
        i "I probably shouldn't have agreed to come on this trip. I don't want to play with Holly's feelings. I shouldn't."
        hide v9_holly_selfie2 with short
        show ian at truecenter with move
        $ fian = "n"
        i "I need to thank her for the pics, at least..."
        "I started to type my reply, but I didn't get the chance to finish."
    else:
        i "No messages..."
        i "Alison is taking her sweet time..."
        "I continued to fiddle with my phone, waiting for her."
## ALISON STRIP TEASE #################
    $ alison_look = "lingerie"
    a "Okay, I'm done."
    $ fian = "surprise"
    i "Wow! {w=0.4}{nw}" with vpunch
    play music "music/sex_slow.mp3" loop
    scene v9_alison3
    # if alison_sexy > 1:
        # show v9_alison3_p
    with long
    pause 1
    "Alison was looking at me, standing against the wall in an overly exaggerated sexy pose. Not that she didn't look good..."
    "In fact, she looked so damn sexy!"
    a "Do you like it?"
    i "You look... super hot, Alison. I didn't know you owned this kind of lingerie."
    a "I bought it specifically for today. For you."
    if v6_alison_pics == 2:
        a "Last time you really enjoyed taking some pictures of me, so I thought you'd like me to pose for you again..."
        "I picked up my phone camera, smiling. How lucky was I?"
        i "So that's why you bought this lingerie, for me to take some pictures of you with it?"
        a "{i}Uh-hu{/i}. Is like this okay?"
    elif v6_alison_pics == 1:
        a "It seems last time you kinda enjoyed asking me to pose for you, so..."
        a "I thought I could do it in a bit of a special way, and you can even take pictures if you want..."
        "I picked up my phone camera, smiling. How lucky was I?"
        i "There's no way for me to say no to that."
        a "Alright, then..."
    else:
        a "Since you seem to be into models, I thought I could model a bit for you... If you want, of course."
        i "Model, in like... You want me to take some pictures of you?"
        a "Yeah, I mean, if you feel like it..."
        "There was no way I would pass on such an offer. I picked up my phone camera, my eyes glued on Alison at every moment."
        i "Yeah, that'd be... interesting."
        a "I've never done this before... Is like this okay?"
    play sound "sfx/camera.mp3"
    with flash
    $ ian_alison_pics.append("v9_alison_pic1_comp")
    i "It is... Damn, it never ceases to amaze me how hot you actually are, Alison."
    "I saw her brush her lip with her finger and then bite it. It looked like an unconscious movement."
    scene v9_alison4 with long
    a "Really? Do you seriously mean that?"
    i "What, you don't believe me?"
    play sound "sfx/camera.mp3"
    with flash
    $ ian_alison_pics.append("v9_alison_pic2.webp")
    a "No... I do. I'm glad you think that, actually."
    a "I wasn't sure you'd like it..."
    i "Well, I hope I've dispelled all your doubts. You have me so hard right now."
    "Alison smiled, seemingly pleased."
    a "Well, that was fast..."
    scene v9_alison5
    # if alison_sexy > 1:
        # show v9_alison5_p
    with long
    pause 1
    a "Good, that means I won't be able to tease you too much..."
    play sound "sfx/camera.mp3"
    with flash
    $ ian_alison_pics.append("v9_alison_pic3_comp")
    i "I'm really enjoying it, so no need to rush it."
    a "Are you sure? Don't you want me to show you {i}these{/i}?"
    "Alison tugged the straps of her bra, teasing to reveal them."
    i "You know I do."
    a "If that's the case..."
    scene v9_alison6
    # if alison_sexy > 1:
        # show v9_alison5_p
    with long
    pause 1
    a "You know it's hard for me to say no to you."
    "Alison had always had a characteristic awkwardness about her, a certain stiffness. But that was hardly visible at that moment."
    "She seemed really into it, really eager and comfortable while seducing me. I had witnessed that before, but never to this extent."
    play sound "sfx/camera.mp3"
    with flash
    $ ian_alison_pics.append("v9_alison_pic3b_comp")
    "She was turning me on like a mad dog."
    i "I never expected you'd prepare something like this for me. A sexy photo shoot... This is so hot."
    a "I wasn't sure about it at first, but now I think it's even turning me on, too."
    i "Oh, really? Show it to me."
    i "Show me how turned on you are..."
    $ falison = "flirt"
    $ fian = "confident"
    scene hotelnight
    show alison at rig
    show ian at lef
    with long
    pause 1
    "Alison obeyed my request and attempted a sultry move by getting closer and dropping down, but keeping her balance in high heels was difficult and she almost tripped."
    hide alisonbra with short
    $ fian = "worried"
    hide alison with fps
    a "Oops!"
    "But her clumsy attempt didn't kill my libido one bit."
    scene v9_alison7
    # if alison_sexy > 1:
        # show v9_alison7_p
    with long
    "She balanced things out by introducing her hand underneath her panties, suggestively caressing her crotch..."
    a "It's kinda embarrassing, but it also makes me feel pretty horny..."
    play sound "sfx/camera.mp3"
    with flash
    $ ian_alison_pics.append("v9_alison_pic4_comp")
    "It probably couldn't compare to how horny she was getting me. Having Alison act so shamelessly in front of me was incredible..."
    "And she was letting me immortalize this moment with my camera. She was really entrusting her most intimate, vulgar side to me."
    a "Are you taking good pictures?"
    i "You bet. Maybe you should consider becoming a real model."
    a "Don't pull my leg... Besides, I only want to do this for you."
    i "Really? Just for me?"
    a "That's how I feel, yeah..."
    if alison_satisfaction > 3:
        if ian_look == "wits1":
            scene v9_alison8a
        elif ian_look == "charisma1":
            scene v9_alison8b
        elif ian_look == "athletics1":
            scene v9_alison8c
        elif ian_look == "lust1":
            scene v9_alison8d
        else:
            scene v9_alison8e
        # if alison_sexy > 1:
            # show v9_alison8_p
        with long
        pause 1
        a "I'll be your slutty model if that's what you like."
        a "As long as you keep fucking me as you do, I'll be whatever you want me to be..."
        "Alison licked her nipple lusciously while staring directly into my eyes."
        "Fuck. That was probably the hottest thing I had ever seen."
        play sound "sfx/camera.mp3"
        with flash
        $ ian_alison_pics.append("v9_alison_pic5_comp")
        "Up until now, even when we had sex, I couldn't help but still see the old image I had of Alison."
        "The girl I had known since high school. That nerdy but hot friend of mine."
        "That image finally shattered. The Alison that was before me now was completely different. She had totally unveiled herself."
    else:
        "Up until now, even when we had sex, I couldn't help but still see the old image I had of Alison."
        "The girl I had known since high school. That nerdy but hot friend of mine."
        "Very little was left now of that image. The Alison that was before me was completely different. She had unveiled herself."
    "She was so fucking hot."
## ALISON SOFT ################
    if ian_look == "wits1":
        scene v9_alison9a
    elif ian_look == "charisma1":
        scene v9_alison9b
    elif ian_look == "athletics1":
        scene v9_alison9c
    elif ian_look == "lust1":
        scene v9_alison9d
    else:
        scene v9_alison9e
    # if alison_sexy > 1:
        # show v9_alison9_p
    with long
    pause 1
    a "Okay, I think it's time to put the phone aside and get your hands on me."
    "Smiling, Alison crawled over to where I was sitting and sat on my lap."
    "My erection sank into her soft buttocks, covered only by the small thong."
    "When she started grinding her ass on my crotch, it rubbed my cock against the fabric of my pants almost painfully."
    a "You weren't lying... You are hard as a rock unless it's some other thing poking at my ass right now."
    i "I wasn't lying, and you're not mistaken... I'm dying to fuck you, Alison."
    "Alison swayed her hips in a wider angle, performing some sort of lapdance on me."
    a "Say that again. You make me feel so sexy..."
    i "I'm dying to fuck you, Alison. I've been dying to during the whole day."
    "Alison stood up and began removing her panties."
    a "In that case..."
    menu:
        "{image=icon_charisma.webp}/{image=icon_lust.webp}Ask for a boobjob" if ian_charisma > 5 or ian_lust > 5:
            $ renpy.block_rollback()
            i "Not so fast. I haven't gotten to enjoy your boobs yet."
            i "I want to see those juicy tits around my cock... now."
            a "It keeps surprising me how demanding you actually are..."
            scene v9_alison10 with long
            "Maybe I was, but she was more than willing to please me. So I wasn't afraid to ask."
            "She unzipped my pants, liberating my hungry cock, and placed it between her marvelous boobs."
            "This was something only Alison had been able to give me... Never had a boobjob felt this good, almost like fucking a pussy..."
            play sound "sfx/camera.mp3"
            with flash
            $ ian_alison_pics.append("v9_alison_pic6.webp")
            a "You're taking a picture of this, too?"
            i "Of course. It's a heavenly sight."
            a "You're such an idiot."
            "Despite her words, Alison smiled and continued to work my cock with her boobs."
            "She squeezed them together, dragging them up and down my cock, masturbating me softly between them."
            "They were perfect for this job: so soft and big enough to envelop my shaft completely, clutching it delightfully."
            if alison_jeremy:
                if alison_voyeur:
                    "No wonder Jeremy had hounded Alison to get a piece of this action. I still remembered that pic he sent me..."
                    "He got to enjoy this too, but it was me who had reaped the most benefits."
                else:
                    "No wonder Jeremy had hounded Alison to get a piece of this action. He got to enjoy it too, but it was me who had reaped the most benefits."
            else:
                "No wonder Jeremy was interested in getting a piece of this action... Sadly for him, it was only me who got to enjoy it."
            i "Fuck, this is so good... I could cum like this..."
            a "Do you want to?"
            i "No, not yet. I already told you: I'm dying to fuck you."
            "I stood Alison up and pulled her toward me, holding her from behind."
            "My cock was raging and I couldn't wait any longer..."

        "Have sex with Alison":
            $ renpy.block_rollback()
            "She had teased me enough. I needed to feel her raw pussy squeezing my dick directly."
            "I stood up as I unzipped my pants, releasing my hungry cock, and held Alison from behind."

## ALISON SEX #################
    scene v9_alison11
    # if alison_sexy == 2:
    #     show v9_alison11_p
    with long
    pause 1
    "Her being taller than me put her pussy at the perfect height for me to stick it in and fuck her just like that."
    play sound "sfx/mh1.mp3"
    a "Annhhhh!"
    "She let out a long gasp like she had been waiting for this moment eagerly."
    "My cock slid in with barely any resistance. Alison's sex was already moist and welcomed me in..."
    a "Mhhh, finally... I had been waiting for this..."
    i "Me too, but you know that already. You make me so hard..."
    "As I thrust my cock into her I massaged her big boobs, feeling how heavy, soft and squishy they were."
    "I pinched Alison's stiff nipples, making her moan and my dick got even harder. She had a body to be thoroughly enjoyed."
    "And it seemed she was loving it every bit as much as I was."
    a "Ohhh, yes... So good...! I love this!"
    a "I love being like this with you, Ian..."
    menu:
        "{image=icon_love.webp}I love it too" if v9_fireworks == "love":
            $ renpy.block_rollback()
            $ ian_alison_love = True
            stop music fadeout 2.0
            play music "music/sex_elation.mp3" loop
            i "I love it too, Alison..."
            scene v9_alison11b
            # if alison_sexy == 2:
            #     show v9_alison11_p
            with long
            pause 1
            "I went for her lips, and she met me with a steamy kiss."
            "Our tongues melted together, twirling with genuine desire for each other."
            "I squeezed her breasts and her body tighter, feeling her pussy do the same around my cock."
            call friend_xp('alison') from _call_friend_xp_668
            $ ian_alison = 12
            play sound "sfx/ah2.mp3"
            a "Oh, Ian..."
            scene v9_alison13
            # if alison_sexy == 2:
            #     show v9_alison13_p
            with long
            pause 1
            "Alison turned around, pushed me down on the couch, got on top of me, and kissed me deeply again."
            "Her lips stuck to mine as she guided my hard cock into her pussy again, and separated only to let out a moan."
            a "Mhhh...! Kissing you feels amazing..."
            i "Yeah... You are amazing, Alison."
            "It looked like my words made Alison work her hips harder as she kept bouncing on top of me."
            "Her boobs were right in my face, brushing and slapping against it. What a wonderful sight, and what a wonderful feeling..."
            "I couldn't resist holding them, licking and sucking one nipple, then the other..."
            a "Oh, yes... I'm yours, Ian..."
            "After a while Alison got off, spreading her legs for me."
            a "Make me yours."

        "{image=icon_lust.webp}Fuck her harder" if ian_lust > 5 or alison_satisfaction > 3:
            $ renpy.block_rollback()
            $ ian_alison_dom = True
            stop music fadeout 2.0
            play music "music/sex_perilous.mp3" loop
            i "Like this, you mean?"
            scene v9_alison12 with vpunch
            play sound "sfx/oh1.mp3"
            a "Oh!!"
            i "You love being fucked like this, you mean?"
            if alison_satisfaction == 5:
                i "Being fucked like a slutty bitch? Like when you barked for me?"
                a "Oh, fuck...!"
                "It looked like Alison was about to say something else, but she held back."
                "That wasn't an answer that satisfied me. I slammed my hips on Alison's ass with a loud slap."
                play sound "sfx/ah2.mp3"
                a "Ahhh!!" with vpunch
                "Alison moaned, shaken by my vigorous, almost violent thrusts."
                i "Come on, tell me! Tell me how much you love being fucked by my raw cock!"
                a "Oh, yes! I love it...!"
                a "I love being a bitch for you, Ian! I love having your raw cock inside me!"
                a "Fuck me!"
            else:
                a "Yeah..."
                "That wasn't an answer that satisfied me. I slammed my hips on Alison's ass with a loud slap."
                play sound "sfx/ah2.mp3"
                a "Ahhh!!" with vpunch
                i "That's it? Really? I thought you liked being rammed by my raw cock...!"
                "Alison moaned, shaken by my vigorous, almost violent thrusts."
                a "I like it... Your raw cock's the best... I love it...!"
                a "I love it when you fuck me like a slut, Ian...!"
            "Hearing Alison talk dirty like that was almost enough to make me cum. I had to slow down."
            i "Come here."

        "I love your boobs!":
            $ renpy.block_rollback()
            with vpunch
            "I drove my hips harder and squeezed Alison's plentiful boobs."
            i "Oh, yeah, I love these!"
            a "That's the only thing you care about, isn't it?"
            call friend_xp('alison', -1) from _call_friend_xp_669
            i "Not at all. Let me show you!"

        "...":
            $ renpy.block_rollback()
            "I was too busy fucking her to talk. But she insisted."
            a "Do you like it too...?"
            i "Yeah, of course... Can't you feel it right now?"
            a "Yeah... Wait, fuck me like this..."

    scene v9_alison14 with long
    play sound "sfx/ah6.mp3"
    pause 1
    a "Annh!"
    "Alison moaned as I rammed my cock balls-deep back into her drenched pussy."
    "The view was amazing... Alison in that sexy lingerie she had picked just for me, her juicy boobs bouncing with my thrusts and my cock sliding in and out of her pussy..."
    i "It feels amazing... It's almost like it's sucking me in..."
    i "You're so fucking wet, Alison!"
    "As if she was trying to test it herself, Alison brought her hand between her legs and started rubbing her pussy."
    a "You're so hot, Ian!"
    a "I love how you look when you fuck me! What a body...!"
    play sound "sfx/oh1.mp3"
    a "Ahhh...! And nobody has ever fucked me as you do!"
    "That ego boost drove me to empty the tank and give Alison all I had left."
    if ian_athletics > 4:
        "My body could keep pushing, but I was on the brink of cumming..."
    else:
        "I was on the brink of cumming, and my body was about to give out, too."
    if ian_alison_dom:
        if ian_athletics < 10:
            call xp_up('athletics') from _call_xp_up_502
    i "This is too good...! You're gonna make me cum, Alison!"
    "She began rubbing her clit faster after hearing me."
    if v6_alison_cum or v8_alison_sext == 2:
        a "Yes, yes! Cum!"
        a "Flood my pussy with your cum, please!"
    else:
        a "Yes, yes! Cum!"
        a "Cum for me!"
    menu:
        "{image=icon_lust.webp}Cum in Alison's pussy" if ian_lust > 6 or v6_alison_cum or v8_alison_sext == 2:
            $ renpy.block_rollback()
            $ v9_alison_creampie = True
            if v6_alison_cum:
                "I was already tempted to do that. Hearing her ask me gave me no choice."
                i "Fuck yes! I'm gonna give it all to you!"
            else:
                "A part of me knew I shouldn't do it, but I didn't listen to it."
            "Penetrating Alison felt so good reason had abandoned my mind. All I wanted was to climax, to shoot it all deep into her pussy."
            i "Ahhhh!!!" with flash
            with vpunch
            "All my muscles tensed and trembled when release hit me."
            with vpunch
            "I felt the first jet of cum shooting from my dick, injected into Alison's pussy."
            a "What...? Ian, you...!"
            with vpunch
            "Alison's voice sounded hesitant, but it was too late to stop the second load."
            with vpunch
            "And the third."
            if v6_alison_cum or v8_alison_sext == 2:
                a "You really did it? You came inside me?"
            else:
                a "Did you just come inside me?"
            stop music fadeout 2.0
            scene v9_alison16 with long
            "Alison sat up on the couch and immediately checked the interior of her pussy."
            "A drip of thick cum was already leaking out."
            a "You did...! You really did!"
            if v6_alison_cum or v8_alison_sext == 2:
                i "Wha--? You asked me to!"
                a "That's one of the things you say in the heat of the moment...! But I trusted you to pull out!"
                i "That makes no sense!"
                if v6_alison_cum:
                    a "This happened before. You know I'm not on the pill!"
                else:
                    a "We talked about this over the phone. I'm not on the pill!"
                i "I thought maybe you were since you asked me to do it!"
            else:
                i "Uh, I'm sorry, I didn't think it would be a problem..."
                a "I already told you to cum outside last time! I'm not on the pill!"
                i "Yeah, but that was last time, so I thought maybe now was different..."
                a "That makes no sense! You got carried away, didn't you?"
                i "Yeah..."
            $ fian = "worried"
            $ falison = "sad"
            $ alison_look = "lingerie2"
            scene hotelnight
            show iannude at lef
            show alison at rig
            show alison_cum3 at rig
            with long
            a "Jeez... Well, what's done is done."
            if v6_alison_cum:
                i "You can take the morning-after pill, just like last time, right...?"
            else:
                i "I guess you should take the morning-after pill, right...?"
            i "I'll go get one for you."
            a "I'll get it tomorrow. The thing is, that pill has some nasty side effects."
            a "Migraine, sickness, sometimes even puking. I don't want to spend the whole Friday feeling like shit."
            $ fian = "sad"
            i "Damn, I had no idea."
            $ falison = "n"
            a "That's why I avoid birth-control pills, too. They make my periods insanely painful, so they're not good for me."
            a "If you really want to cum inside, next time wear a condom!"
            if v6_alison_cum or v8_alison_sext == 2:
                $ fian = "serious"
                i "How was I supposed to know all that? You just asked me to cum and I followed your orders..."
                a "Yeah, yeah. As I said, it's something I said in the heat of the moment. Now you know, so don't do it again!"
                $ fian = "sad"
                i "Yeah, duly noted."
            else:
                i "Yeah... I'm sorry, you just got me way too excited and I wasn't thinking rationally. My fault."
                a "I'm flattered, but next time try to be more careful."
            i "So, about the morning-after pill...?"
            a "I'll take it this Saturday before we get back. It's still inside the effective time range, and at least I'll be at home when I start feeling like shit."
            a "Come, let's go to bed."

        "Cum on Alison's tits":
            $ renpy.block_rollback()
            if v6_alison_cum:
                "Despite what Alison just said, I knew better than to do that again."
                "The last time I did, it got really awkward, so instead, I quickly pulled away, got on top of her, and finished over her boobs."
            if v8_alison_sext == 2:
                "Despite what Alison just said, I knew better than to do that. I knew she wasn't on the pill, so doing that could lead to big trouble..."
                "Instead, I quickly pulled away, got on top of her, and finished over her boobs. She didn't seem to mind at all."
            else:
                "I quickly pulled away, got on top of her, and finished over her boobs."
            scene v9_alison15 with long
            i "Here it comes...!"
            a "Yes, cover me with your cum!"
            show v9_alison15_cum1 with flash
            i "Ahhhh!!" with hpunch
            hide v9_alison15_cum1
            show v9_alison15_cum2
            with hpunch
            pause 0.5
            with hpunch
            pause 0.5
            "I groaned, releasing every last drop, and sat down on the couch, dizzy both from effort and pleasure."
            "That had been awesome..."
            a "Mhhh, so much...!"
            "Alison pressed her boobs together, shiny with a splash of fresh cum, and licked it off of them."
            a "I love to taste you..."
            i "Alison, if you do that there's no way I'm going soft... I'll need to fuck you again right away."
            "Alison smiled at me and gave one last lick to her breasts."
            a "Don't worry, we have the whole weekend to ourselves. Just us."
            a "Lie down with me, enjoy it..."
            stop music fadeout 2.0

    scene v9_alison17 with long
    pause 1
    "We laid down on the bed and Alison hugged me tightly."
    if ian_alison_love:
        "I hugged her too, It felt so good to feel her soft body against mine."
    else:
        "I wouldn't have minded a bit more space. I needed to cool off and she was warm and heavy..."
    if v9_alison_creampie:
        if v6_alison_cum:
            "Thankfully she didn't seem too mad about me cumming inside her... again."
        else:
            "Thankfully she didn't seem too mad about me cumming inside her."
        "I should really be more careful..."
        if v6_alison_cum or v8_alison_sext == 2:
            i "You're playing a dangerous game, you know that? Asking me to cum inside when you don't really mean it..."
            a "I already said I'm sorry. You see, you're not the only one who gets insanely turned on."
            i "Glad we're on the same page on that, at least..."
    else:
        if v6_alison_cum or v8_alison_sext == 2:
            i "By the way... When you asked me to cum inside, you didn't really mean it, right?"
            a "Yeah, it's one of those things you say in the heat of the moment... I was counting on you to pull out, which you did. Thanks."
            i "Glad I interpreted that correctly... You're playing a dangerous game, you know that?"
            "Alison chuckled."
            a "Sorry... You just manage to turn me on so much..."
    "It had been a long day, topped by an amazing session of passionate sex."
    "I was satisfied and tired, so I fell asleep rather quickly."
    scene hotelnight_dark with long
    if ian_alison_love:
        "I was glad I decided to spend the weekend with Alison."
    else:
        if ian_lena_dating or ian_holly_dating:
            "Deciding to spend the weekend with Alison had been a good idea after all... right?"
        else:
            "Deciding to spend the weekend with Alison had been a good idea after all..."
## ALISON FRIDAY ###########################################################################################################################################################################################################################################################################################
    call calendar(_day="Friday") from _call_calendar_75

    if alison_sexy == 2:
        $ alison_look = "cool"
    elif alison_sexy == 1:
        $ alison_look = 2
    else:
        $ alison_look = 1
    $ alison_makeup = 0
    scene hotel with long
    $ fian = "smile"
    $ falison = "n"
    $ ian_look = 1
    play sound "sfx/shower.mp3"
    "The sound of the shower woke me up, but I lingered in bed, comfortable as I was."
    "It felt nice being in no rush to wake up, resting after the long day and the intense sex I had enjoyed last night."
    show iannude2 at lef with short
    i "This is the life..."
    play sound "sfx/door.mp3"
    show alisonnude at rig with short
    a "Good morning, sleepyhead."
    "Alison got out of the bathroom, presenting her voluptuous nakedness before me."
    hide iannude_soft
    show iannude at lef
    with long
    "My body reacted accordingly to it..."
    i "I'm not allowed to sleep in? I'm on vacation, after all..."
    $ falison = "flirt"
    a "Don't you think you could make better use of your time rather than sleep?"
    i "It depends. Do you have any other plans for today?"
    a "Yeah. But we don't need to leave the bed for any of them..."
    play music "music/sex_chill.mp3" loop
    scene v9_alison18
    # if alison_sexy == 2:
    #     show v9_alison18_p
    with long
    pause 1
    "Alison sat on the bed next to me and held my erect cock in her hand."
    a "Seems you woke up rather excited this morning..."
    i "What can I say? Seeing you naked has that effect on me..."
    a "Luckily for you, I'm more than happy to take responsibility for that..."
    play sound "sfx/bj1.mp3"
    "Alison smiled at me and started sucking my dick. I closed my eyes and groaned."
    i "Mhhh, yeah... Lucky me, indeed..."
    "I relaxed and let her work. I wasn't gonna cum anytime soon, which meant I could enjoy this for a while..."
    "I saw Alison looking at me, trying to read my reactions to make sure she was doing a good job."
    if ian_lena_sex:
        "She wasn't the most skillful at it, especially when compared to Lena. But Lena was something else..."
        "That didn't mean Alison's was bad, though. Not at all."
    elif ian_cherry_sex:
        "She wasn't the most skillful at it, especially when compared to Cherry. That had been something else..."
        "That didn't mean Alison's was bad, though. Not at all."
    "After several long minutes of this, I decided it was time to move the action forward."
    menu v9alisonanalmenu:
        "{image=icon_lust.webp}Ask for anal" if ian_lust > 5 and v9askanalalison == False:
            $ renpy.block_rollback()
            $ v9askanalalison = True
            i "Hey, Alison... Do you want to try anal?"
            a "Huh?"
            $ fian = "smile"
            $ falison = "n"
            scene hotel
            show iannude at lef
            show alisonnude at rig
            with long
            if v8_alison_sext == 3:
                a "Anal sex? No, thanks... I already told you I'm not into it."
                a "Besides, we don't have any lube, do we?"
            else:
                a "Anal sex? No, thanks..."
                $ fian = "sad"
                i "How come?"
                a "I'm not into it. And we don't have any lube, do we?"
            menu:
                "{image=icon_charisma.webp}Insist" if ian_charisma > 5:
                    $ renpy.block_rollback()
                    $ v9_alison_anal = True
                    if ian_chad < 5:
                        $ ian_chad += 1
                    $ fian = "smile"
                    i "But have you tried it before?"
                    a "Yeah, a couple of times... It never worked out."
                    $ fian = "confident"
                    i "But you've never tried it with {i}me{/i}."
                    $ falison = "sad"
                    a "I couldn't do it with Milo, and you're quite bigger than him... I don't think we'll get anywhere!"
                    a "And about the lube..."
                    i "Don't worry about that. I bet you're really soaked down there..."
                    $ falison = "blush"
                    a "I don't know about this..."
                    i "Come on. Do it for me."
                    a "Jeez, alright..."
                    hide alisonnude with short
                    "Alison laid down on the bed, her back turned to me."
                    a "Be careful, okay?"
                    show iannude at truecenter with move
                    i "Yeah, yeah. Let's go..."
                    play sound "sfx/pain.ogg"
                    scene v9_alison20 with vpunch
                    a "Ahhh!! Ouch!"
                    "Alison screamed when I pushed my cock into her asshole."
                    "I had lubricated the tip with her juices, but when I pressed it down it felt very tight, so I had to force it a bit..."
                    "Seems it didn't feel like just \"a bit\" to Alison."
                    "She pushed me over and jumped out of the bed."
                    $ fian = "worried"
                    $ falison = "mad"
                    scene hotel
                    show iannude at lef
                    show alisonnude at rig
                    with long
                    a "Ouch, that hurt! I told you to be careful!"
                    call friend_xp('alison', -1) from _call_friend_xp_670
                    i "I'm sorry..."
                    $ falison = "serious"
                    a "No, I told you I didn't like it... I shouldn't have let you convince me."
                    "Damn, it seemed like anal was definitely off the menu."
                    i "You're right, I apologize. I just..."
                    $ falison = "n"
                    a "It's okay. Now we know for sure we can't do it, but there's plenty of other stuff we can do..."
                    $ fian = "smile"
                    i "Yeah."
                    jump v9defaultalisonsex

                "Let it be":
                    $ renpy.block_rollback()
                    $ fian = "smile"
                    i "Oh, alright, if that's the case."
                    a "It's just it really hurt the times I tried with Milo. We could never get anywhere."
                    a "And you're much bigger than him, so... I don't think that would go well!"
                    i "As I said, no problem."
                    if ian_charisma < 6:
                        call xp_up ('charisma') from _call_xp_up_945
                    jump v9alisonanalmenu

        "Eat Alison out":
            $ renpy.block_rollback()
            i "Now's my turn to taste you... I haven't done so for far too long."
            scene v9_alison19
            # if alison_sexy == 2:
            #     show v9_alison19_p
            with long
            pause 1
            play sound "sfx/mh1.mp3"
            a "Be my guest... Mhhh!!"
            "We reversed positions and this time it was I who used my mouth to please Alison."
            "Her pussy emanated heat and moisture, which only increased when my tongue began slithering around the clit."
            if ian_lust < 9:
                call xp_up('lust') from _call_xp_up_503
            "Alison moaned and her legs twitched, struck by jolts of increasing pleasure, one after the other."
            a "Oh, fuck... When did you get so good at this?"
            i "Always have been."
            a "It might be that you're starting to really get what makes me tick... God, I love how you lick me, just like that...!"
            "I kept the pace and pressure, following Alison's commands. It didn't take me more than a couple more minutes to make her cum."
            play sound "sfx/orgasm1.mp3"
            a "Oh, fuck! Yeeessss...!!" with vpunch
            a "Mhhh...!"
            i "I'd say someone's satisfied."
            a "Not yet... I want to feel you inside of me!"
            jump v9defaultalisonsex

        "Have sex":
            $ renpy.block_rollback()
            i "I need to be inside of you, Alison...!"
            label v9defaultalisonsex:
                if v9_alison_creampie == False:
                    $ fian = "confident"
                    $ falison = "n"
                    scene hotel
                    show iannude at lef
                    show alisonnude at rig
                    with long
                    a "Wait a moment, though..."
                    "Alison stood up and searched for something in her purse. A condom."
                    a "Here, put this on."
                    menu:
                        "Seriously?":
                            $ renpy.block_rollback()
                            $ fian = "worried"
                            i "Seriously? We haven't been using one for quite some time now..."
                            a "Yeah, and that's dangerous! Last night you almost came inside me, I noticed it!"
                            i "Well, I pulled out, didn't I?"
                            $ falison = "serious"
                            a "Don't be one of those guys, please."
                            call friend_xp('alison', -1) from _call_friend_xp_671
                            $ falison = "sad"
                            a "We should be always using it, but we end up getting carried away... I really like fucking you raw, but we shouldn't be taking any risks..."

                        "Sure":
                            $ renpy.block_rollback()
                            $ fian = "n"
                            i "Sure, if you want to."
                            a "It's not that I want to... We should be always using it, but we end up getting carried away..."
                            a "Last night you almost came inside me, I noticed it! We shouldn't be taking any risks..."

                    $ falison = "slut"
                    a "Besides, this way I can ride you until you cum without having to worry."
                    i "Have you considered taking the pill?"
                    $ falison = "n"
                    a "Yeah, I was on it for a while. It doesn't work for me. I get some nasty side effects..."
                    $ falison = "slut"
                    a "Ready?"
                    $ fian = "confident"
                    i "Yup."
                    scene v9_alison21
                    # if alison_sexy == 2:
                    #     show v9_alison21_p
                    with long
                    pause 1
                    "Alison pushed me down to the bed, climbed on top of me, and guided my cock into her pussy."
                    play sound "sfx/mh1.mp3"
                    a "Mhhh, yes...!"
                    "She began riding me hard and fast from the get-go, impaling herself on my stiff pole."
                    a "Feeling you inside me is the best...! How can you make me feel like this?"
                    "It was not the same with the rubber, but it wasn't as bad as some people claim. I could still feel Alison's warm pussy walls squeezing my cock."
                    "I held her hips and matched her passionate movements with mine, slamming her pussy with upward thrusts."
                    "Alison's boobs bounced magnificently in front of me, almost hypnotically. I was sensitive after that long blowjob, and that view only pushed me closer to the brink."
                    i "You're driving me crazy, Alison!"
                    scene v9_alison22
                    show v9_alison22_condom
                    # if alison_sexy == 2:
                    #     show v9_alison22_p
                    with long
                    pause 1
                    "She bounced even more violently on top of me."
                    a "I'm gonna ride you until you cum! Cum inside me, Ian!"
                    "It was her who ended up cumming, though."
                    a "Ahhh!!!" with flash
                    play sound "sfx/ah5.mp3"
                    pause 0.5
                    with vpunch
                    pause 0.5
                    with vpunch
                    pause 0.5
                    scene v9_alison21
                    # if alison_sexy == 2:
                    #     show v9_alison21_p
                    with long
                    pause 1
                    "Alison kept moving her hips, grinding on me slowly, my cock still hard as a rock inside of her."
                    a "Mhhh... You haven't cum yet, did you?"
                    i "No... It's a bit hard with the condom..."
                    a "I hate we have to use it, but..."
                    i "Yeah, I know. But I'm still enjoying myself quite a lot... Though not as much as you, I'd say!"
                else:
                    scene v9_alison21
                    # if alison_sexy == 2:
                    #     show v9_alison21_p
                    with long
                    pause 1
                    "It was Alison who took the lead, pushing me down to the bed and climbing on top of me."
                    "She guided my cock into her pussy with needy quickness and soon I felt the wet inner walls of her pussy squeezing my cock."
                    play sound "sfx/ah5.mp3"
                    a "Oh, yes...!"
                    "She began riding me hard and fast from the get-go, impaling herself on my stiff pole."
                    a "Feeling you inside me is the best...! How can you make me feel like this?"
                    "I held her hips and matched her passionate movements with mine, slamming her pussy with upward thrusts."
                    "Alison's boobs bounced magnificently in front of me, almost hypnotically. I was sensitive after that long blowjob, and that view only pushed me closer to the brink."
                    i "Fuck, Alison...! If you keep this up, I'll cum in no time...!"
                    scene v9_alison22
                    # if alison_sexy == 2:
                    #     show v9_alison22_p
                    with long
                    pause 1
                    "She bounced even more violently on top of me."
                    a "Do it! Cum inside me!" with vpunch
                    "My cock throbbed like a pressure tank about to explode."
                    i "But I thought you said...!"
                    a "Fuck it! I'm gonna take the pill later anyway!"
                    a "I want to feel your cum gushing inside of me again!"
                    "Her frantic words and hip movements were all the convincing I needed."
                    i "Ahhh!!!" with flash
                    play sound "sfx/orgasm3.mp3"
                    with vpunch
                    pause 0.5
                    with vpunch
                    pause 0.5
                    with vpunch
                    pause 0.5
                    show v9_alison22_cum with short
                    "As soon as I came, Alison followed. I had never seen her cum so savagely."
                    a "Yes, yes, yes...! You're gonna knock me up...! Mhhhh!"
                    "Her legs trembled around my hips. I pushed them forward, reaching as deep as I could into Alison's pussy with my cock, and stayed like that."
                    scene v9_alison21
                    # if alison_sexy == 2:
                    #     show v9_alison21_p
                    with long
                    pause 1
                    i "Take it all... until the last drop."
                    a "All of your cum... inside of me..."
                    "Alison kept moving her hips, grinding on me slowly."
                    a "Oh, God, I could get pregnant..."
                    i "If you don't take the pill, you will."
                    "As I said that, I felt Alison's pussy twitching, and my cock started to get hard again even before it could deflate."

    scene v9_alison24
    # if alison_sexy == 2:
    #     show v9_alison23_p
    if v9_alison_creampie == False:
        show v9_alison24_condom
    with long
    pause 1
    "That day we didn't leave the room at all."
    "We used every corner of it: the bed, the couch, the chair, the table, the shower..."
    "I hadn't had this kind of privacy in a long time: nobody was there to disturb us, and we were free to enjoy ourselves for as long as we wanted."
    "Having Alison naked around me never failed to get me hard, no matter if we had just fucked a few minutes ago."
    "And she was more than willing to indulge my horniness. Reigniting her lust was as simple as touching her a bit, kissing her neck, fondling her breasts..."
    if v9_alison_creampie:
        "It made her beg to feel my bare cock inside of her in no time. And I couldn't remain satisfied for long, either."
    scene v9_alison23
    if v9_alison_creampie == False:
        show v9_alison23_condom
    with long
    pause 1
    "We had sex, rested, ordered something to eat, or took a short nap, and repeated the same steps."
    if v9_alison_creampie:
        show v9_alison23_cum with flash
        "And I lost count of the times I creampied her. She went completely wild when I did."
        a "Yes! Shoot your baby seed inside my pussy, Ian...! Put a baby inside me!"
        "The only reason we could play this game was that we already knew Alison would take the morning after pill, hopefully negating our recklessness."
        "But in the meantime, we indulged in our lust and kinkiest fantasies."
        "And for some reason having Alison asking me to knock her up had me going like a raging bull, even if I knew it was all part of the game."
    else:
        "We probably ended up going through half a box of condoms."
    "I already knew Alison had a high sex drive, but she still managed to impress me."
    "And I loved every minute of it."
    stop music fadeout 2.0
    $ fian = "confident"
    $ falison = "flirt"
    scene hotelnight
    show iannude at lef
    show alisonnude at rig
    if v9_alison_creampie:
        show alison_cum3 at rig
    with long
    a "Oh, God... I have no strength left in my legs."
    i "I'm at my limit, too... But I just couldn't get enough of you."
    if ian_alison_love:
        $ fian = "smile"
        i "I can't believe the chemistry we have."
        $ falison = "smile"
        a "Right? It's almost like we were made for each other, huh?"
        i "You really think so?"
        $ falison = "n"
        a "It's a manner of speaking... But I think we've always clicked well together, wouldn't you say so?"
        i "After today, I don't see any reason to disagree."
        if alison_jeremy_3some == 1:
            a "Oh, I just remembered... Have you been talking to Jeremy recently?"
            $ fian = "n"
            i "Yeah..."
            if alison_jeremy:
                a "I mean, I already told you I haven't seen him in like months, but he's asking me to hang out from time to time."
            else:
                a "He's been kind of hounding me a little bit... Like he wants us to hang out and such."
            i "By \"hang out\" I guess you mean \"hook up\", right?"
            a "Well, yeah, you know how he is. I've been telling him I'm busy and that I prefer to spend my free time with you."
            a "But he said he pitched you the idea of a threesome..."
            $ fian = "sad"
            i "Yeah, he did..."
            a "And what do you think about it?"
            menu:
                "We could try it":
                    $ renpy.block_rollback()
                    $ alison_jeremy_3some = 2
                    $ fian = "n"
                    i "Um... I don't know, maybe we could... try it?"
                    $ falison = "surprise"
                    a "Really?"
                    $ fian = "worried"
                    i "I thought you were interested in the idea, the way you asked..."
                    $ falison = "blush"
                    a "Um, well... I've never done such a thing, but..."
                    $ fian = "n"
                    i "But?"
                    a "I don't know, if you're into it too, maybe we could try it, as you said."
                    $ fian = "smile"
                    i "Maybe, yeah."
                    $ falison = "n"
                    a "Sure."

                "It's a stupid idea":
                    $ renpy.block_rollback()
                    $ alison_jeremy_3some = 0
                    $ fian = "smile"
                    i "I think it's a stupid idea."
                    $ falison = "smile"
                    a "That's what I told him."
                    i "Besides, I like you too much to be willing to share you with anybody..."
                    if ian_alison < 12:
                        call friend_xp('alison', 1) from _call_friend_xp_672
                    $ falison = "blush"
                    a "Oh... Really?"
                    i "Do I have to prove it to you once more?"
                    $ falison = "smile"
                    a "I would tell you to, but my pussy is completely worn out!"
                    $ fian = "happy"
                    i "And I like this \"refinement\" of yours, for some reason!"

    else:
        i "You're so damn naughty, Alison. And you manage to keep surprising me."
        a "What can I say? You know how to bring out my inner slut..."
        i "And I love her so fucking much."
        $ falison = "n"
        a "So that's the only thing you love about me?"
        $ fian = "smile"
        i "Not the only one... But surely the one I love the most."
        a "I see... Speaking of which..."
        a "Have you talked with Jeremy recently?"
        $ fian = "n"
        i "Yeah."
        if alison_jeremy:
            a "I mean, I already told you I haven't seen him in like months, but he's asking me to hang out from time to time."
        else:
            a "He's been kind of hounding me a little bit... Like he wants us to hang out and such."
        i "By \"hang out\" I guess you mean \"hook up\", right?"
        a "Well, yeah, you know how he is. I've been telling him I'm busy and that I prefer to spend my free time with you."
        if alison_jeremy_block:
            $ fian = "serious"
            i "He hasn't given up yet?"
            a "Why do you think he should give up?"
            $ fian = "worried"
            i "Well, because you're with me, and..."
            a "So I'm \"with\" you?"
            i "Well, not in \"that\" sense... You know what I mean."
            $ fian = "n"
            a "Yeah, I thought as much."
            a "So would you get mad if something happened between us?"
            $ fian = "disgusted"
            i "Jeremy and you? That would be weird..."
            i "But why are you bringing that up all of a sudden?"
            a "Never mind."
            if ian_alison > 3:
                call friend_xp('alison', -1) from _call_friend_xp_673
        else:
            a "But he said he pitched you the idea of a threesome..."
            if alison_jeremy_3some == 1:
                i "That's right... Wait, don't tell me you're interested?"
                a "Well... I've never been with two guys at the same time..."
                a "Sounds like it could be interesting, I don't know."
                menu:
                    "{image=icon_lust}We could try that" if ian_lust > 5:
                        $ renpy.block_rollback()
                        $ alison_jeremy_3some = 2
                        i "We could... maybe try that."
                        $ falison = "surprise"
                        a "Really?"
                        i "Why are you acting all surprised? You told me you'd like to!"
                        $ falison = "n"
                        a "Yeah, yeah. I just wasn't expecting you to agree straight away."
                        if alison_jeremy:
                            i "Well, you've already been with Jeremy, so..."
                            $ fian = "smile"
                            i "Besides, I'm trying to be open-minded. Maybe it's even fun, who knows..."
                        else:
                            $ fian = "smile"
                            i "I'm trying to be open-minded... Maybe it's even fun, who knows."
                        a "Yeah, who knows..."

                    "Don't count me in":
                        $ renpy.block_rollback()
                        $ alison_jeremy_3some = 0
                        $ fian = "disgusted"
                        i "Well, don't count me in! I already told Jeremy this was a crazy idea, so a hard pass for me."
                        jump v9alisonjeremytalk

            else:
                $ fian = "serious"
                i "Yeah, and I told him to shove that idea up his ass."
                a "Why?"
                i "What do you mean, why? I'm not into that sort of thing."
                label v9alisonjeremytalk:
                    a "I see. In that case, would you get mad if we hooked up on our own?"
                $ fian = "n"
                i "Is that what you want to do?"
                a "I don't know. I'm just asking how you'd feel about it."
                if ian_lena_dating:
                    a "I mean, you're dating the model too, aren't you? It's not like you have reasons to get jealous..."
                elif ian_holly_dating:
                    a "I mean, you're dating that writer girl too, aren't you? It's not like you have reasons to get jealous..."
                else:
                    a "I mean, we don't have something \"serious\" you and me, do we? It's not like you have reasons to get jealous..."
                menu:
                    "Sleep with whomever you want":
                        $ renpy.block_rollback()
                        $ alison_jeremy_block = False
                        i "You're right. You can sleep with whomever you want, I don't mind."
                        $ falison = "sad"
                        a "Yeah, of course. Why should you?"
                        call friend_xp('alison', -1) from _call_friend_xp_674
                        i "Exactly."
                        $ falison = "n"
                        a "Good. I'm glad we're clear about that."
                        $ fian = "smile"
                        i "You got it."

                    "I don't feel comfortable with that":
                        $ renpy.block_rollback()
                        $ alison_jeremy_block = True
                        $ fian = "serious"
                        i "It's not an idea I'm comfortable with, to be frank. Why are you even asking me this?"
                        call friend_xp('alison', -1) from _call_friend_xp_675
                        a "What, would you prefer me to not say anything?"
                        if alison_jeremy:
                            i "Depends... Do you want to fuck Jeremy again so badly?"
                        else:
                            i "Depends... Do you want to fuck Jeremy so badly?"
                        $ falison = "serious"
                        if ian_lena_dating or ian_holly_dating or ian_lena_over:
                            a "I just don't understand why the idea is making you mad. We're just fuck buddies and you're sleeping around with other people too, aren't you?"
                            $ fian = "sad"
                            i "Well, yeah, but..."
                            a "That's called having double standards, in case you were not aware."
                            call friend_xp('alison', -1) from _call_friend_xp_676
                            $ fian = "serious"
                        else:
                            a "I just don't understand why the idea is making you mad. We're just fuck buddies and you're free to sleep around with other people too, aren't you?"
                            $ fian = "n"
                            i "Yeah, you're right about that."
                        i "You're free to do whatever you want, same as me. That doesn't mean I have to like it."
                        $ falison = "n"
                        a "At least we can agree on that..."
                        $ fian = "n"

    if v9_alison_creampie:
        $ falison = "sad"
        a "By the way, we should go get the pill. Waiting any longer would probably be a bad idea, especially after all the times you came inside..."
        $ fian = "n"
        i "Yeah. The last thing I need in my life right now is to become a dad."
        $ falison = "n"
        a "Same here. I can't think of anything that would ruin my life faster than having a kid at this moment."
        a "I have a career to set up straight!"
        $ fian = "smile"
        i "What are we waiting for, then? Let's go get that miracle pill."
        a "Let me search for the closest pharmacy..."
    else:
        $ falison = "n"
        a "I'm dead tired... We should go to bed."
        $ fian = "smile"
        i "Yeah."
    scene hotelnight_dark with long
    pause 0.5
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH09_S03")
##ALISON SATURDAY #######################################################
    call calendar(_day="Saturday") from _call_calendar_76

    play music "music/normal_day.mp3" loop
    scene village with long
    "We checked out of the hotel in the morning, had breakfast on another nice terrace, and got on the train right after that."
    play sound "sfx/train.mp3"
    scene train_travel with long
    "We arrived at our station just before lunchtime."
    $ ian_look = 1
    scene street with long
    if v9_alison_creampie:
        $ fian = "worried"
        $ falison = "sad"
        "Alison had been uncharacteristically quiet during the ride, and she did look rather unwell."
        show ian at lef
        show alison at rig
        with short
        i "How are you feeling?"
        a "Like someone's hammering at my head from the inside. And like I'm on the brink of puking all the time."
        i "That pill really did a number on you..."
        a "Yeah, I told you I get some nasty side effects..."
        $ falison = "n"
        a "But at least we made the most of it since this moment was unavoidable already."
        $ fian = "sad"
        i "I'm sorry. I should've really pulled out that first time."
        a "Forget about it."
    else:
        $ falison = "n"
        $ fian = "smile"
        show ian at lef
        show alison at rig
        with short
        i "Here we are..."
    $ falison = "sad"
    a "Ugh, the last thing I want right now is to go to that family dinner."
    if ian_alison_love:
        $ falison = "smile"
        a "It's been a wonderful few days with you, Ian. I'm so glad you came."
        $ fian = "confident"
        i "I came a lot of times..."
        a "You idiot!"
        $ fian = "smile"
        i "But you're right, I had a blast with you, Alison. I can't wait to see you again."
        a "Awww, look at you... You can be cute when you want to!"
        i "Does it bother you?"
        $ falison = "n"
        a "Not at all... I love it, in fact."
        "Alison kissed me on the lips before waving her hand goodbye."
        a "I need to go, I'm already late! See you soon!"
        i "I'll text you later."
    else:
        i "There are some obligations you can't get away from."
        $ falison = "smile"
        a "I already have too many of these. Anyway, thanks for coming with me these days. I had fun."
        $ fian = "confident"
        i "I did too. A lot of it, actually."
        $ falison = "flirt"
        a "Yeah. A ton."
        $ falison = "n"
        a "Ugh, I need to go, I'm already late. So, see you around?"
        $ fian = "smile"
        i "Yeah. Good luck with work, family, and all that stuff."
        a "Thanks!"
    hide alison with short
    show ian at truecenter with move
    i "That was an interesting experience... and intense."
    if ian_alison_love:
        i "Alison..."
        "We started hooking up about three months ago, and I could feel something was changing."
        $ fian = "n"
        "I couldn't see Alison just as a friend anymore. Not even like a simple fuck-buddy."
        "There was more to it. But why did it feel so... weird?"
        $ fian = "smile"
        i "It will sort itself out in due time, I guess."
        i "So far, I'm pretty happy with how it's playing out..."
    else:
        if ian_holly_dating:
            $ fian = "sad"
            i "But this has to stop. I can't be doing things like this if I want to go anywhere with Holly..."
            i "Even if we are taking it slow, I doubt she'd like to know what I did with Alison. It would probably hurt her..."
            $ fian = "n"
            i "So I guess this was the last time, huh...?"
            $ fian = "smile"
            i "Well, at least it went out with a bang."
        elif ian_lena_dating and ian_lena_love:
            $ fian = "sad"
            i "But this has to stop. I can't be doing things like this and claim to be serious about Lena..."
            i "We're not officially dating, but that's what I'd like to happen. I doubt she'd be okay with me having Alison as a sex friend."
            $ fian = "n"
            i "So I guess this was the last time, huh...?"
            $ fian = "smile"
            i "Well, at least it went out with a bang."
        else:
            $ fian = "confident"
            i "Who would've thought my friendship with Alison would end up developing into something like... this."
            i "I'm enjoying it quite a lot."

    if ian_lena_dating:
        stop music fadeout 2.0
        i "I should get going. I told Lena I would meet her today for lunch..."
    else:
        i "Now that I think of it, Lena should be already in town. I told her I would call..."
        hide ian
        show ian_phone
        with short
        i "..."
        show phone_lena at lef3 with short
        l "Hi, Ian! Are you back from your trip?"
        i "Yeah. Are you busy? I thought we could meet for lunch."
        l "Alright. In fact, I wanted to drop by the café and see how Molly and Ed are doing."
        i "Okay, see you there."
    if lena_lust > 6 and ian_lena_dating:
        $ lena_look = "sexy"
    else:
        $ lena_look = 4
    $ flena = "smile"
    if ian_lena_dating:
        scene street with long
        $ ian_look = 2
        "After stopping at my place to freshen up and change, I headed off to our date."
        "When I arrived at the café Lena was already waiting for me outside."
        if ian_lena_dating:
            play music "music/date.mp3" loop
        show ian at lef
        show lena at rig
        with short
        l "Hey there! Nice to finally see you!"
        i "I missed you too."
        if lena_look == 4:
            scene v9_lena1a
        else:
            scene v9_lena1b
        with long
        "I leaned in for a kiss."
        "It had been almost two weeks since my lips tasted Lena's for the last time. I had been missing this..."
        if lena_ian_love or lena_lust > 6:
            "And by the looks of it, she was feeling the same."
            if lena_look == 4:
                scene v9_lena2a
            else:
                scene v9_lena2b
            with long
            "Lena didn't seem in a rush for that kiss to end, and neither was I."
            "Our lips spread apart, giving way to our tongues. They came together in a slow, passionate dance."
            "I held her hips and pulled her body toward me, and she slid her hands around my neck, caressing my nape as the kisses rolled one after the other."
            "It felt so incredibly good to kiss Lena again, to taste her, to feel her warmth and her perfume..."
            "When we finally decided to end the kiss, there was a bulge under my pants. I could not resist the effect she had on me!"
            $ fian = "smile"
            $ flena = "shy"
            scene street
            show ian at lef
            show lena2 at rig
            with short
            i "Welcome back."
            l "Now that's how you greet someone..."
        else:
            "It was brief, but enough to put a smile on my face."
            $ fian = "smile"
            $ flena = "smile"
            scene street
            show ian at lef
            show lena at rig
            with short
            i "Welcome back."
            l "Thanks."
        l "Shall we go in?"
        scene cafe
        show ian at lef
        show lena at rig
        with short
        l "Hello!"
    else:
        scene cafe with long
        $ ian_look = 2
        "After stopping at my place to freshen up and change, I headed off to meet Lena."
        "I arrived at the café just a couple of minutes before Lena did."
        show ian at lef
        show lena at rig
        with short
        i "Hey there!"
        l "Hi, Ian! Nice to see you."
    jump v9lenadate1

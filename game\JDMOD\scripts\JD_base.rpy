




define JD_version = "0.13.3.6.a"
define JD_chapter = 13

default persistent.JD_developer = False

image JD_icon = im.Scale("JDMOD/images/gui/JD.png", int(config.screen_height * 0.05), int(config.screen_height * 0.05), yalign = 0.5)
image JD_greyscreen = Solid("#121619E6")

init python:
    def label_callback(name, abnormal):
        ignored_labels = ["_after_load", "_start", "_quit", "_splashscreen", "_before_main_menu", "_main_menu", "_after_warp", "_hide_windows", "_console"]
        
        if not name in ignored_labels:
            store.current_label = name

    config.label_callback = label_callback






define x_pos = int(config.screen_width * 0.19)
define y_pos = int(config.screen_height * 0.77)
define text_size = int(config.screen_height * 0.013)
define gap = int(config.screen_width * 0.01)
define spacing = int(config.screen_width * 0.004)
define button_x_size = int(config.screen_width * 0.10)
define button_y_size = int(config.screen_height * 0.07)
define num_col = 7






style JD_window:
    background None
    xfill False
    yfill False
    xminimum int(button_x_size * 2)
    yminimum int(button_y_size * 1.5)
    xalign 0.5
    yalign 0.5
style JD_frame:
    background Solid("#362f2d80")
    xpadding spacing
    ypadding spacing
    xalign 0.5
    yalign 0.5
style JD_vpgrid:
    xspacing spacing
    yspacing spacing
style JD_hbox:
    yalign 0.5
    xalign 0.5
style JD_vbox:
    yalign 0.5
    xalign 0.5

style JD_button:
    idle_background Solid("#000000")
    insensitive_background Solid("#000000")
    hover_background Solid("#362f2d")
    selected_background Solid("#000000")
    xminimum button_x_size
    yminimum button_y_size
    xpadding 10
    ypadding 0
    xalign 0.5
    yalign 0.5
style JD_image_button:
    xalign 0.5
    yalign 0.5


define JD_text_color = gui.text_color
define JD_text_outlines_color = "#383838"

define JD_text_altcolor = "#c5002e"
define JD_text_outlines_altcolor = "#600000"

define JD_keybind_color = "#c5002e"
define JD_keybind_outlines_color = "#600000"
style JD_text:
    font font_big_noodle
    insensitive_color JD_text_color
    idle_color JD_text_color
    hover_color JD_text_altcolor
    size text_size * 4
    outlines [(0, JD_text_outlines_color, 2, 2)]
    hover_outlines [(0, JD_text_outlines_altcolor, 2, 2)]
    outline_scaling "step"
    xalign 0.5
    yalign 0.5
    yoffset 2
style JD_button_text is JD_text
style JD_input is JD_text:
    color "#000"
    outlines [(0, JD_text_outlines_color, 0, 0)]

style returnbutton_JD:
    xanchor 1.0
    yanchor 1.0
    xpos 0.98
    ypos 0.98
style returnbutton_JD_text:
    size text_size * 2.5







transform menu_top:
    on show:
        yoffset int(-config.screen_height * 0.08) alpha 0.0
        easein 0.50 yoffset 0 alpha 1.0
    on hide:
        easeout 0.30 yoffset int(-config.screen_height * 0.08) alpha 0.0
transform menu_bottom:
    on show:
        yoffset int(config.screen_height * 0.08) alpha 0.0
        easein 0.50 yoffset 0 alpha 1.0
    on hide:
        easeout 0.30 yoffset int(config.screen_height * 0.08) alpha 0.0
transform menu_left:
    on show:
        xoffset int(-config.screen_width * 0.08) alpha 0.0
        easein 0.50 xoffset 0 alpha 1.0
    on hide:
        easeout 0.30 xoffset int(-config.screen_width * 0.08) alpha 0.0
transform menu_right:
    on show:
        xoffset int(config.screen_width * 0.08) alpha 0.0
        easein 0.50 xoffset 0 alpha 1.0
    on hide:
        easeout 0.30 xoffset int(config.screen_width * 0.08) alpha 0.0







screen JD_title(title="JDMOD"):
    style_prefix "title_JD"
    use JD_version

    $ icon_size = int(config.screen_height * 0.06)
    hbox:
        text title:
            size text_size * 7
            color JD_text_altcolor
            outlines [(2, JD_text_outlines_altcolor, 0, 0), (2, JD_text_outlines_altcolor, 2, 2)]
        text "by JohnDupont":
            font font_peach_pen
            outlines [(2, JD_text_outlines_color, 0, 0), (2, JD_text_outlines_color, 2, 2)]
        imagebutton:
            hover im.Scale("JDMOD/images/support/patreon_hover.png", icon_size, icon_size)
            idle im.Scale("JDMOD/images/support/patreon_idle.png", icon_size, icon_size)
            action OpenURL ("https://www.patreon.com/JohnDupont")


style title_JD_hbox:
    spacing spacing * 2
    xalign 0.5
    yanchor 0
    ypos 0.01
style title_JD_text:
    size text_size * 4
    yalign 1.0







screen JD_version():
    style_prefix "version_JD"

    vbox:
        textbutton "Report a bug":
            action OpenURL("https://discord.gg/XMKgefdjqr")
        if config.version <> "":
            text "Game: {}".format(config.version)
        textbutton "JDMOD: v{}".format(JD_version):
            action OpenURL("https://www.patreon.com/posts/37882810")


style version_JD_vbox:
    yanchor 0
    ypos 0.01
    xanchor 1.0
    xpos 0.99
style version_JD_button is empty:
    xalign 0
style version_JD_text:
    size text_size * 2
    xalign 0
style version_JD_button_text is version_JD_text







screen JD_error():
    style_prefix "error_JD"
    modal True
    key "game_menu" action NullAction()

    add ImageReference("JD_bg")
    use JD_title()
    vbox:
        text "The JDMOD can't find the required images.\nIt's usually due to a bad install. You should try re-installing it."
        frame:
            has button:
                text "Click here for detailed instructions"

                action OpenURL("https://www.patreon.com/posts/36691457")

    textbutton "{color=[JD_keybind_color]}{outlinecolor=[JD_keybind_outlines_color]}Esc.{/color}{/outlinecolor}   Play the game anyway":
        style "returnbutton_JD"
        at menu_right
        action Return()
        keysym "K_ESCAPE"

style error_JD_vbox:
    xminimum int(config.screen_width * 0.6)
    xalign 0.5
    yalign 0.5
    spacing 20
style error_JD_text:
    text_align 0.5
    size text_size * 5







init python:
    def JD_thanks():
        patrons = []
        
        with renpy.file("JDMOD/database/patrons.tsv") as sceneslist:
            for line in sceneslist:
                line = line.decode("utf-8")
                data = line.rstrip().split("\t")
                if data == "" or data[0][0] == "#": continue
                patrons.append(data[0])
        
        patrons.sort()
        return ", ".join(patrons)

label splashscreen:
    scene black
    $ JD_thanks()
    call screen JD_gal_loadingscreen()
    pause(0.1)
    return

image JD_bg = im.Grayscale("JDMOD/images/gui/JD_bg.png")
screen JD_gal_loadingscreen():
    style_prefix "loadingscreen_gal_JD"

    key "game_menu" action NullAction()

    add ImageReference("JD_bg")
    use JD_title()

    vbox:
        text "The JDMOD exists thanks to your support on Patreon:":
            color JD_text_altcolor
            outlines [(2, JD_text_outlines_altcolor, 0, 0), (2, JD_text_outlines_altcolor, 2, 2)]
        text "{}".format(JD_thanks()):
            style "loadingscreen_gal_JD_text_patrons"

    if persistent.JD_developer:
        textbutton "{color=[JD_keybind_color]}{outlinecolor=[JD_keybind_outlines_color]}Enter.{/color}{/outlinecolor}   (DEV ONLY)   Hide":
            style "returnbutton_JD"
            at menu_right
            action Return()
            keysym "K_RETURN"
    else:
        $ JD_timer = renpy.random.randint(30, 40)
        timer float(JD_timer/10) action Return()


style loadingscreen_gal_JD_vbox:
    xalign 0.5
    yalign 0.4
style loadingscreen_gal_JD_text is title_JD_text:
    size text_size * 6
style loadingscreen_gal_JD_text_patrons is JD_text:
    xsize int(config.screen_width * 0.8)
    size text_size * 6
    font font_peach_pen
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc

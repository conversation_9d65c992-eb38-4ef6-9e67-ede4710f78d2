



define JD_v1_pool_shot1 = {
    'main': 'v1_poolshot1.webp',
    "goodchoice": 1,
    'choices': [
        
        ['img', 'pool_where_ball.webp', (1256, 109), (0.5,0), None],
        ['img', 'pool_choose_ball_white.webp', (1098, 240), (0,0), None],
        ['img', 'pool_stick.webp', (1230, 598), (0,0), None],
        ['button_auto', 'pool_choose_check', (1169, 403), (0.5,0.5), 1],
        ['button_auto', 'pool_choose_check', (1261, 403), (0.5,0.5), 2],
        ['button_auto', 'pool_choose_check', (1352, 403), (0.5,0.5), 3]
    ],
    'hover': [
        [1, 'pool_check.webp', (689, 385), (0,0)],
        [2, 'pool_check.webp', (707, 370), (0,0)],
        [3, 'pool_check.webp', (713, 348), (0,0)],
        [None, 'pool_check.webp', (689, 385), (0,0)],
        [None, 'pool_check.webp', (707, 370), (0,0)],
        [None, 'pool_check.webp', (713, 348), (0,0)]
    ]
}

define JD_v3_pool_shot1 = {
    'main': 'v3_poolshot1.webp',
    "goodchoice": 3,
    'choices': [
        ['img', 'pool_which_ball.webp', (1256, 109), (0.5,0), None],
        ['img', 'pool_choose_ball_white.webp', (1098, 240), (0,0), None],
        ['img', 'pool_stick.webp', (1230, 598), (0,0), None],
        ['button_auto', 'pool_choose_ball', (633, 292), (0.5,0.5), 1],
        ['button_auto', 'pool_choose_ball', (351, 256), (0.5,0.5), 2],
        ['button_auto', 'pool_choose_ball', (286, 594), (0.5,0.5), 3]
    ],
    'hover': [
        [1, 'pool_choose_ball_red.webp', (1098, 240), (0,0)],
        [2, 'pool_choose_ball_yellow.webp', (1098, 240), (0,0)],
        [3, 'pool_choose_ball_blue.webp', (1098, 240), (0,0)],
    ]
}

define JD_v3_pool_shot2 = {
    'main': 'v3_poolshot2.webp',
    "goodchoice": 2,
    'choices': [
        
        ['img', 'pool_where_ball.webp', (1256, 109), (0.5,0), None],
        ['img', 'pool_choose_ball_white.webp', (1098, 240), (0,0), None],
        ['img', 'pool_stick.webp', (1230, 598), (0,0), None],
        ['button_auto', 'pool_choose_check', (1169, 403), (0.5,0.5), 1],
        ['button_auto', 'pool_choose_check', (1261, 403), (0.5,0.5), 2],
        ['button_auto', 'pool_choose_check', (1352, 403), (0.5,0.5), 3]
    ],
    'hover': [
        [1, 'pool_check.webp', (509, 826), (0,0)],
        [2, 'pool_check.webp', (540, 821), (0,0)],
        [3, 'pool_check.webp', (559, 794), (0,0)],
        [None, 'pool_check.webp', (509, 826), (0,0)],
        [None, 'pool_check.webp', (540, 821), (0,0)],
        [None, 'pool_check.webp', (559, 794), (0,0)]
    ]
}


define JD_v9_pool_shot1 = {
    'main': 'v10_poolshot1.webp',
    "goodchoice": "a",
    'choices': [
        
        ['img', 'pool_where_ball.webp', (1256, 109), (0.5,0), None],
        ['img', 'pool_choose_ball_white.webp', (1098, 240), (0,0), None],
        ['img', 'pool_stick.webp', (1230, 598), (0,0), None],
        ['button_auto', 'pool_choose_check', (1169, 403), (0.5,0.5), 'a'],
        ['button_auto', 'pool_choose_check', (1261, 403), (0.5,0.5), 'b'],
        ['button_auto', 'pool_choose_check', (1352, 403), (0.5,0.5), 'c']
    ],
    'hover': [
        ['a', 'pool_check.webp', (334, 598), (0,0)],
        ['c', 'pool_check.webp', (400, 595), (0,0)],
        ['b', 'pool_check.webp', (371, 606), (0,0)],
        [None, 'pool_check.webp', (334, 598), (0,0)],
        [None, 'pool_check.webp', (400, 595), (0,0)],
        [None, 'pool_check.webp', (371, 606), (0,0)]
    ]
}

define JD_v9_pool_shot2 = {
    'main': 'v10_poolshot2.webp',
    "goodchoice": "b",
    'choices': [
        ['img', 'pool_where_ball.webp', (1256, 109), (0.5,0), None],
        ['img', 'pool_choose_ball_white.webp', (1098, 240), (0,0), None],
        ['img', 'pool_stick.webp', (1230, 598), (0,0), None],
        ['button_auto', 'pool_choose_check', (1169, 403), (0.5,0.5), 'c'],
        ['button_auto', 'pool_choose_check', (1261, 403), (0.5,0.5), 'b'],
        ['button_auto', 'pool_choose_check', (1352, 403), (0.5,0.5), 'a']
    ],
    'hover': [
        ['c', 'pool_check.webp', (161, 354), (0,0)],
        ['b', 'pool_check.webp', (159, 371), (0,0)],
        ['a', 'pool_check.webp', (167, 387), (0,0)],
        [None, 'pool_check.webp', (161, 354), (0,0)],
        [None, 'pool_check.webp', (159, 371), (0,0)],
        [None, 'pool_check.webp', (167, 387), (0,0)]
    ]
}

define JD_v9_pool_shot3 = {
    'main': 'v10_poolshot3.webp',
    "goodchoice": "d",
    'choices': [
        ['img', 'pool_which_ball.webp', (1256, 109), (0.5,0), None],
        ['img', 'pool_choose_ball_white.webp', (1098, 240), (0,0), None],
        ['img', 'pool_stick.webp', (1230, 598), (0,0), None],
        ['button_auto', 'pool_choose_ball', (253, 177), (0.5,0.5), 'a'],
        ['button_auto', 'pool_choose_ball', (593, 778), (0.5,0.5), 'b'],
        ['button_auto', 'pool_choose_ball', (255, 734), (0.5,0.5), 'c'],
        ['button_auto', 'pool_choose_ball', (180, 504), (0.5,0.5), 'd']
    ],
    'hover': [
        ['a', 'pool_choose_ball_yellow.webp', (1098, 240), (0,0)],
        ['b', 'pool_choose_ball_orange.webp', (1098, 240), (0,0)],
        ['c', 'pool_choose_ball_green.webp', (1098, 240), (0,0)],
        ['d', 'pool_choose_ball_purple.webp', (1098, 240), (0,0)],
    ]
}




screen JD_screen_pool_game(game):
    tag pool_game

    default selected = None

    add game['main']

    for _type, _img, _pos, _anchor, _return in game['choices']:
        if _type == 'img':
            add _img pos _pos anchor _anchor
        elif _type == 'button_auto':
            if _return == game["goodchoice"]:
                imagebutton:
                    idle _img + "_idle.webp"
                    hover _img + "_hover.webp"
                    pos _pos
                    anchor _anchor
                    hovered SetScreenVariable('selected', _return)
                    unhovered SetScreenVariable('selected', None)
                    action Play("ch_one", "sfx/cue.mp3"), Return(_return) at fade_in_skill
            else:
                imagebutton:
                    idle im.Grayscale(_img + "_idle.webp")
                    hover im.Grayscale(_img + "_hover.webp")
                    pos _pos
                    anchor _anchor
                    hovered SetScreenVariable('selected', _return)
                    unhovered SetScreenVariable('selected', None)
                    action Play("ch_one", "sfx/cue.mp3"), Return(_return) at fade_in_skill


    if 'hover' in game:
        for _sel, _img, _pos, _anchor in game['hover']:
            if _sel == selected:
                if _sel == game["goodchoice"]:
                    add _img pos _pos anchor _anchor
                else:
                    add im.Grayscale(_img) pos _pos anchor _anchor
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc

##################################################################################################################################################
## DEFAULT VARIABLES #############################################################################################################################
##################################################################################################################################################

default temp = []
default temp_gallery = False

# IAN
default ian_active = True

#stats
default ian_wits = 0
default ian_charisma = 0
default ian_athletics = 0
default ian_lust = 0
default ian_will = 0
default ian_money = 2
default ian_wits_xp = 0
default ian_charisma_xp = 0
default ian_athletics_xp = 0
default ian_lust_xp = 0
default ian_will_xp = 0

# job
default ian_job_magazine = 2
default ian_stipend = 1
default ian_owed_money = 0
default ian_live_perry = True

# relationships
default ian_perry = 5
default ian_lena = 3
default ian_cindy = 3
default ian_wade = 5
default ian_alison = 5
default ian_holly = 5
default ian_jeremy = 6
default ian_emma = 5
default ian_louise = 3
default ian_ivy = 3
default ian_axel = 3
default ian_seymour = 3
default ian_stan = 3
default ian_default = 4
default ian_minerva = 3
default ian_wen = 4
default ian_robert = 3
default ian_mark = 0
default ian_cherry = 3
default ian_lola = 3
default ian_dad = 3
default ian_gillian = 0

# agenda
default ian_agenda = False
default ian_perry_agenda = False
default ian_lena_agenda = False
default ian_cindy_agenda = False
default ian_wade_agenda = False
default ian_alison_agenda = False
default ian_holly_agenda = False
default ian_jeremy_agenda = False
default ian_emma_agenda = False
default ian_louise_agenda = False
default ian_ivy_agenda = False
default ian_axel_agenda = False
default ian_seymour_agenda = False
default ian_stan_agenda = False
default ian_minerva_agenda = False
default ian_wen_agenda = False
default ian_robert_agenda = False
default ian_mark_agenda = False
default ian_ed_agenda = False
default ian_molly_agenda = False
default ian_danny_agenda = False
default ian_lenamom_agenda = False
default ian_lenadad_agenda = False
default ian_cherry_agenda = False
default ian_producer_agenda = False
default ian_lola_agenda = False
default ian_gillian_agenda = False
default ian_marcel_agenda = False

# LENA 
default lena_active = False

#stats
default lena_wits = 1
default lena_charisma = 1
default lena_athletics = 1
default lena_lust = 1
default lena_will = 1
default lena_money = 0
default lena_wits_xp = 0
default lena_charisma_xp = 0
default lena_athletics_xp = 0
default lena_lust_xp = 0
default lena_will_xp = 0

# job
default lena_job_cafe = 2
default lena_job_restaurant = 2           # 2= Full time / 1= Reduced Hours (if lena_robert_sex) / 0= Laid off
default lena_owed_money = 0
default lena_live_stan_louise = True
default lena_money_family = 0             # max 4 (ch10)

# relationships
default lena_perry = 5
default lena_cindy = 3                    # ch 11 -0 -fight/1-warn about axel/2-3-neutral-friendly
default lena_wade = 3
default lena_alison = 3
default lena_holly = 4
default lena_jeremy = 3
default lena_emma = 4
default lena_louise = 5
default lena_ivy = 6
default lena_axel = 0
default lena_seymour = 4
default lena_stan = 3
default lena_default = 4
default lena_ed = 4
default lena_molly = 8
default lena_robert = 4
default lena_mark = 4
default lena_danny = 5
default lena_lenamom = 4
default lena_lenadad = 6
default lena_cherry = 0
default lena_producer = 0
default lena_lola = 10
default lena_gillian = 3
default lena_marcel = 4                   # 2= rejected advances early / 3= flirt but reject advances / 5 = flirt

# agenda
default lena_agenda = False
default lena_perry_agenda = False
default lena_ian_agenda = False
default lena_cindy_agenda = False
default lena_wade_agenda = False
default lena_alison_agenda = False
default lena_holly_agenda = False
default lena_jeremy_agenda = False
default lena_emma_agenda = False
default lena_louise_agenda = False
default lena_ivy_agenda = False
default lena_axel_agenda = False
default lena_seymour_agenda = False
default lena_stan_agenda = False
default lena_minerva_agenda = False
default lena_wen_agenda = False
default lena_robert_agenda = False
default lena_mark_agenda = False
default lena_ed_agenda = False
default lena_molly_agenda = False
default lena_danny_agenda = False
default lena_lenamom_agenda = False
default lena_lenadad_agenda = False
default lena_cherry_agenda = False
default lena_producer_agenda = False
default lena_lola_agenda = False
default lena_gillian_agenda = False
default lena_marcel_agenda = False

# status
default chapter = 0
default day = "Saturday"
default week = 1
default month = "April"
default prev_day = "Saturday"
default prev_week = 1
default prev_month = "April"
default max_len_month = 0
default anim_day_dir = 1
default anim_week_dir = 1
default anim_month_dir = 1

##################################################################################################################################################
## COSMETIC VARIABLES ############################################################################################################################
##################################################################################################################################################

default lena_look = 1
default flena = "n"
#default lena_style = "good"
default lena_necklace = 0
#default lena_ring = 0
#default lena_hair = 0
default lena_makeup = 0
#default lena_hat = 0
#default lena_nails = 0
#default lena_boobs = 0
default lena_piercing1 = False
default lena_piercing2 = False
#default lena_piercing3 = False
#default lena_piercing4 = False
#default lena_piercing5 = False
#default lena_piercing6 = False
#default lena_piercing7 = False
#default lena_piercing8 = False
#default lena_piercing9 = False
#default lena_piercing10 = False
default lena_tattoo1 = False
default lena_tattoo2 = False
default lena_tattoo3 = False
#default lena_tattoo4 = False
#default lena_tattoo5 = False
#default lena_tattoo6 = False
#default lena_tattoo7 = False
#default lena_tattoo8 = False
#default lena_tattoo9 = False
#default lena_tattoo10 = False
default lena_extras = 0

default ian_look = 1
default fian = "n"
#default ian_style = "good"
default ian_headgear = False
default ian_hurt = 0
#default ian_beard = 0
#default ian_tattoo1 = False
#default ian_tattoo2 = False
#default ian_tattoo3 = False
#default ian_tattoo4 = False
#default ian_tattoo5 = False
#default ian_tattoo6 = False
#default ian_tattoo7 = False
#default ian_tattoo8 = False
#default ian_tattoo9 = False
#default ian_tattoo10 = False
#default ian_physique = 0
#default ian_hair = 0
#default ian_scar = 0
#default ian_extras = 0

default alison_look = 1
default falison = "n"
default alison_tattoo = 0
default alison_extras = 0
default alison_makeup = 0
default alison_necklace = 0

default axel_look = 2
default faxel = "n"
default axel_hurt = 0
#default axel_extras = 0

default cherry_look = 1
default fcherry = "n"
default cherry_stockings = False
default cherry_extras = 0

default cindy_look = 1
default fcindy = "n"
default cindy_tattoo = 0
default cindy_extras = 0
default cindy_necklace = 0
default cindy_makeup = 0

default emma_look = 1
default femma = "n"
default emma_tattoo = False
default emma_extras = 0
default emma_hair = False
default emma_pubes = True

default holly_look = 1
default fholly = "n"
default holly_hair = 1
#default holly_style = "good"
default holly_glasses = True
#default holly_tattoo1 = False
#default holly_tattoo2 = False
#default holly_tattoo3 = False
#default holly_tattoo4 = False
#default holly_tattoo5 = False
default holly_extras = 0

default ivy_look = 1
default fivy = "n"
default ivy_navel = False
#default ivy_tattoo1 = False
#default ivy_tattoo2 = False
#default ivy_tattoo3 = False
#default ivy_tattoo4 = False
#default ivy_tattoo5 = False
default ivy_extras = 0

default jeremy_look = 1
default fjeremy = "smile"
#default jeremy_beard = 0
#default jeremy_hurt = 0
#default jeremy_extras = 0

default louise_look = 1
default flouise = "n"
#default louise_tattoo = 0
#default louise_extras = 0

default perry_look = 1
default fperry = "n"
default perry_glasses = True
#default perry_extras = 0

default robert_look = 1
default frobert = "n"
default robert_splash = False
default robert_hurt = 0
#default robert_extras = 0

default seymour_look = 1
default fseymour = "n"
#default seymour_hurt = 0
#default seymour_extras = 0

default stan_look = 1
default fstan = "n"
default stan_camera = False
#default stan_extras = 0

default wade_look = 1
default fwade = "n"
#default wade_hurt = 0
default wade_extras = 0

default fdanny = "n"

default minerva_look = 1
default fminerva  = "n"
default minerva_tattoo = 0
default minerva_extras = 0
default minerva_necklace = 0

default fed = "n"
default ed_look = 1
#default ed_extras = 0

#default molly_look = 1
default fmolly = "n"
#default molly_extras = 0

default fdad = "n"
default fmom = "n"

default fgillian = "n"
default gillian_look = 1
#default gillian_tattoo = 0
default gillian_extras = 0

default fmark = "n"
#default mark_look = 1
#default mark_hurt = 0
#default mark_extras = 0

#default mayor_look = 1
default fmayor = "n"

#default nat_look = 1
default fnat = "n"

default fpeter = "n"
default feli = "n"
default ffinley = "n"
default fsen = "n"
default fjohn = "n"
default frosa = "n"
default fjack = "n"
default fmarcel = "n"
default arthur_look = 1
default farthur = "n"

##################################################################################################################################################
## v0.1 variables ################################################################################################################################
##################################################################################################################################################

default persistent.tutorial = True
default cheat_mode = False
#default easy_mode = False
#default tutorial_menu = False
#default tutorial1 = False
#default tutorial2 = False
#default tutorial3 = False

## IAN VARIABLES ##########################

# Lena
default v1_answer_cafe1 = False
default v1_answer_cafe2 = False
default v1_answer_cafe3 = False
# mma
default ian_lowkick = False
default ian_grappling = False
# Alison
default v1_alisonlunch = False
default v1_encourage_alison = False
default v1_alisonoften = False
# night bar
default v1_teamcindy = False
default v1_poolcindywin = False
default v1_teamwade = False
default v1_poolwadewin = False
default v1_poolpoints = 0
default v1_hitball = 0
default v1_drunk = False
# Robert
default v1_perry_help = False
default v1_fight = False
default v1_fight_kick = False
default v1_fight_grappling = False
default v1_fight_charisma = False
# Holly
default holly_change = 0                  # Holly wants to be like Lena. Max 5 (+1 Ian comment, +1 Ivy talk, +1 cock dildo, +1 give number to guy, +1 marcel, +1 lena shoot)

## LENA VARIABLES #########################

# Louise
default v1_lena_louisecarriedaway = False
# cafe
default v1_ed_truth = False
default v1_ed_flirt = False
# photo shoot
default v1_choosepic = 0
# Stan Louise Ivy
default v1_talk_stan = False
default v1_talk_louise = False
default v1_talk_ivy = False
# peoplegram
default v1_rss_quote = 0

##################################################################################################################################################
## v0.2 variables ################################################################################################################################
##################################################################################################################################################

## IAN VARIABLES ##########################

# status
default ian_chad = 1                      # Ian's mainstream and alpha-ness. 0/1/2 BASE. +3 to 5 max.
# mma status
default jiujitsu = 0                      # Ian's jiu jitsu skill level  +1 Ian tries Jiu Jitsu, 2 Ian sticks with Jiujitsu (max 3 -armbar) /after ch 10, (max 4) --6 is the master level --
default kickboxing = 0                    # Ian's striking skill level  +1 low kick +1 heavy bag, +1/2 pads minigame (max 3)   /ch 10 +1 sparring (max 4)   /after ch 10, +1 if v10_sparring == 2 (max 5)                --6 is the master level --
# drawing
default v2_photo_draw = False             # Lena takes a pic of Ian's good drawing
default v2_addlena = False
# book
default book_scifi = False
default book_fantasy = False
default book_historical = False
default book_card1 = "n"                  # Book Cards for CALL TO ADVENTURE
# Alison
default v2_alisonflirt = 0
default ian_alison_interest = 0
# Jeremy
default v2_showlena_jeremy = False
# minerva
default decide_review_holly = False       # Secondary menu var
default ian_minerva_review = False
default ian_honest_review = False
default ian_switch_review = False
# Lena
default v2_ian_date = False
# night bar Alison Cherry
default v2_alison_clothes = False         # Ian compliments Alison's clothes. Opens up $ alison_sexy
default v2_tell_jeremy_girl = False       # Tell alison jeremy is with a girl
default v2_cocktail = 0
# Alison bar
default v2_bar_alison = False             # Go with her at the bar
default alison_sexy = False               # What alison will be wearing from now on. After chapter 6 turns numerical: 2-will become sexier 1-red top 0-green top
default v2_find_alison = False
default v2_alison_home = False
default ian_alison_sex = False
# Cherry bar
default v2_cherry_interest = 0
default perry_cherry = 0                  # Has to be > 1 for her to give phone number. After Chapter 8, turns to True if Ian helps Perry out.
default v2_cherry_home = False
default ian_cherry_sex = False
# limp status
default v2_ian_limp = False               # Ian goes limp dick

## LENA VARIABLES #########################

# Lena status
default lena_posh = 1                     # Lena becomes spoiled. 0/1/2 BASE. +3 máx 5
# Ivy
default v2_berate_minerva = False         # Lena talks with ivy
# Louise
default v2_sleep_louise = False
# Stan
default v2_stan_museum = False            # Lena invites Stan to the exhibition with Danny
default v2_stan_model = False             # Lena agrees to pose for Stan
# cafe
default ed_callout = False
# Holly
default v2_holly_song = False             # Lena will show holly her song
# Robert
default v2_robert_invite = False          # Lena offers Robert to go get some drinks
default v2_robert_spoiler = False         # Robert baits Lena with secret info to get drinks
default v2_robert_date = False
default v2_robert_kiss = False
default v2_robert_reject = False
default v2_robert_home = False
default v2_robert_bj = False
default v2_robert_swallow = False
default v2_robert_chance = False          # Second chance after rejecting
default v3_robert_date = False            # Date on chapter 3
# Ian date
default v2_ian_like = False               # Lena tells ian she like him
default v2_ian_kiss = False
# status branch
default v2_lena_gogym = False
default v2_lena_gohome = False
# song
default song_1a = "n"
default song_1b = "n"
default song_1c = "n"

##################################################################################################################################################
## v0.3 variables ################################################################################################################################
##################################################################################################################################################

default lena_passion = "n"                # What Lena says she's passionate about, song, model, life, money
default lena_metal = 0                    # Lena likes metal, not really or hates it
default lena_bdick = 0                    # Lena likes big dicks (+1 Jeremy spy, +1 Jeremy masturbate, +1 Axel pics, +1 v6masturbate) max. 4
default lena_bj = 0                       # Lena likes to give blowjobs max.5
default lena_song1 = 0                    # Lena's first song value min 0 max 6
# Robert
default v3_robert_ignore = False          # Lena ignores Robert's call at night
default lena_robert_sex = False
default lena_robert_sex_early = False     # Lena has sex with Robert a second time before going to work (only if v2_robert_home == True)
default lena_robert_sex_late = False      # Lena has sex with Robert for the first time Wednesday at night
default v3_robert_reject = False
default v3_robert_bj = False
default v3_robert_repeat = False          # Lena has sex 2 times with Robert after calling him at night
# Stan
default v3_stan_shoot = 0                 # How Lena poses for Stan (0=awkward/1=cool/2=continue/3=sofa tease)
default v3_defend_stan = False            # Lena defends Stan before Louise
default v3_welcome_stan = False           # Lena lets Stan hear song/conversation with Louise
# peoplegram
default v3_pg_ian = False                 # Lena posts Ian's drawing on Peoplegram
default v3_pg_danny = False               # Lena posts Danny's picture on Peoplegram
# stalkfap
default v3_check_stalkfap = False         # Create account -passive
default stalkfap = False                  # Decide to upload -active -----> Chapter 13: "late" Lena goes all in when in need for money (stalkfap_pro = 2)
# Seymour
default v3_seymour_call = False           # Text about proposal to Seymour
default v3_seymour_date = False           # Accept dinner with Seymour
default v3_seymour_reject = False         # Lena rejects the dinner date
default seymour_restaurant = False        # Seymour knows Lena works at the restaurant
default help_bum = 0                      # Lena helps or runs away from homeless guy 0- run away, 1- don't give, 2- give
# Louise
default v3_spy = False                    # Spy on Louise
default v3_spy_full = False               # Watch full scene
default v3_bbc = False                    # Lena focuses on Jeremy's bbc
default v3_breakfast_jeremy = False       # Lena has breakfast with Jeremy
# cafe
default v3_talk_molly = False             # Lena tells Molly about Axel and Mom
# Ian
default v3_new_date = False               # Lena and Ian will have a second date Sunday morning (always true if v2_iandate == True)
default v3_date_look = False
default v3_ian_date = False
default v3_ian_date2 = False              # Lena goes with Holly and brings Ian (never used)
# Axel
default v3_axel_call = False              # Decide to call Axel
# masturbate
default v3_use_dildo = False              # Use vibrator to masturbate. Also true if axel_pictures_watch
default v3_masturbate = "n"               # What Lena thinks about when masturbating , ian, robert, holly, spy, jeremy
# Holly
default lena_go_holly = 0                 # Lena's interest in Holly-- max. 3 (+1 compliment, +1 kiss, v6masturbate = 3) Chapter 8 if Lena kisses or TRIES to kiss Holly, = 4, CH 10 Lena tries to go for her, = 5 --- open path/ 3--- closed path
# Jeremy
default louise_jeremy = True              # Jeremy dates Louise
default alison_jeremy = True              # Alison and Jeremy fucked
# Cherry
default know_cherry_model = False         # Ian knows Cherry has done modeling gigs
default ian_cherry_free = False           # Ian tells Cherry no strings attached
# Holly
default v3_holly_date = False             # Ian meets Holly on Sunday
default ian_go_holly = 0                  # Ian's interest in Holly -- tells Holly she's cute +1 or very cute +2 -- max. 2 - v0.6 Turns True or False to accept trip with Holly
default encourage_holly = 0               # Ian or Lena encourage Holly to be more confident. 4 max. 3 or 4 will join poledance.
# cindy
default v3_pool_win = False
default ian_go_cindy = 0                  # Ian's interest in Cindy, max 3
default v3_cindy_comment = False          # Ian comments on Cindy's pic on Peoplegram
default v3_cindy_date = False             # Ian goes to the bar with Cindy
default v3_cindy_dance = False            # Dance TOGETHER with Cindy
default v3_cindy_dance_signs = False      # Ian sees signs of Cindy's attraction
default v3_cindy_reject = False           # Cindy rejects Ian's touchiness
default ian_cindy_model = False           # Ian asks Cindy to show her pictures
# Alison
default alison_voyeur = False             # Jeremy will share exploits with Alison
default alison_no_voyeur = False          # Ian tells Jeremy not to tell him about Alison
default v3_alison_date = False            # Ian meet Alison at night instead of Lena and Holly
default v3_alison_sex = False             # Ian has sex with Alison after the date
default v3_alison_boobjob = False         # Ian get a boob job from Alison
default v3_alison_cunnilingus = False     # Ian eats Alison out
default alison_satisfaction = 0           # Alison's sexual satisfaction with Ian, max 2 (cunnilingus + athletics) chapter 5 max 4 (public/fingering). Max 5 = Alison bitch
# gillian
default v3_gillian_stop = False           # Ian forces himself to delete Gillian's pics from his phone

##################################################################################################################################################
## v0.4 variables ################################################################################################################################
##################################################################################################################################################

## LENA VARIABLES #########################

# Seymour photo shoot
default v4_seymour_date = False           # Lena has a photo-shoot with Seymour
default v4_lenapose = 0                   # One of 3 poses Lena chooses to do
default seymour_shoot = 0                 # How satisfied Seymour is with Lena's shoot - max. 3
# Ian
default lena_go_ian = 0                   # Lena's interest in Ian (0-none 1-doubt 2-yes)
default v4_ian_date = False               # Lena meets Ian at night
# Robert
default lena_robert_over = False          # Lena breaks up with Robert after having sex
default v4_robert_sex = False             # (if lena_robert_sex_late == True) Lena fucks Robert at her home for the second time  ---> Lena has 3rd sex scene at the end of chapter
default v4_robert_top = False             # Lena takes top position with Robert
default v4_robert_public = False          # Lena blows Robert at the restaurant (1 interrupted, 2 full scene)
# Axel
default v4_axel_date = False              # Lena agrees to meet Axel
default axel_pictures_watch = False       # Lena watches Axel's pics
default axel_pictures_destroy = False     # Lena destroys Axel pictures
# Stan
default v4_accuse_stan = False            # Lena accuses Stan of stealing underwear
default v4_defend_stan = False            # Lena takes Stan's side against Louise
# photo shoot stan danny
default v4_danny_shoot = False            # Lena sets up a photo-shoot with Danny
default v4_stan_shoot = False             # Lena will ask Stan to take pictures
# status sex
default lena_anal = 0                     # Lena experiments with anal play (0-uninitiated 1-initiated) 2-HAD ANAL SEX
default v4_piercing = 0                   # The piercing Lena gets originally, 1 or 2 (used for retrocompatibility in phone gallery images in case Lena decides to change her piercing later in game)
# cafe
default v4_compliment_ed = False          # Lena tells Ed he looks young
default cafe_help = False                 # Lena will try to help Ed and Molly with the café
default cafe_steal = False                # Lena decides to steal from Molly and Ed. Chapter 6 turns into # 1-stole once 2-stole twice 3-got caught stealing and fired
# Louise
default v4_confront_louise = False        # Lena tells Louise Jeremy is lying when making up, she will get mad
# Holly
default holly_gym = False                 # Holly joins pole dance

## IAN VARIABLES ##########################

# status book
default book_card2 = "n"                  # Book Cards for ENEMY
# Alison and cherry
default v4_alison_sexting = False         # Ian texts Alison and gets a selfie
default v4_cherry_sexting = False         # Ian texts Cherry and gets a selfie
# Jeremy/ alison / louise
default v4_show_louise = False            # jeremy shows the group sewxy pics of Louise
default alison_jeremy_block = False       # Ian tells Jeremy to stay away form Alison                          # These two vars only matter if ian_alison_sex = True
default alison_jeremy_doubt = False       # Ian tells Jeremy he feels uncomfortable with him pursuing Alison   #
# cindy
default wade_cindy = 0                    # Wade's relationship with Cindy. 0 min, 1 Ian encourages Wade, 2 Ian encourages Cindy
default v4_cindy_date = False             # Ian goes with Cindy to the gallery
default v5_cindy_shoot = False            # Ian will go with Cindy to the photo-shoot
# Seymour
default v5_hand_proposal = False          # Ian wants to hand his proposal to Seymour himself
default v5_hand_proposal_lena = False     # Lena agrees to hand Ian's proposal to Seymour
# Lena date
default v4_place = "n"                    # The place where Ian takes Lena to the date, fortress or shine
default v4_ian_kiss = False               # Ian makes out with Lena at the date
default ian_lena_sex = False              # Ian and Lena have sex
default v4_ian_lena_sex = False           # Lena invites Ian over after the date
# status
default tournament = False                # Ian is interested in taking part in MMA tournament
default lena_mayor_agenda = False
default ian_mayor_agenda = False
default lena_mayorswife_agenda = False
default ian_mayorswife_agenda = False
default ian_yuri = 4
default ian_yuri_agenda = False

##################################################################################################################################################
## v0.5 variables ################################################################################################################################
##################################################################################################################################################

## IAN VARIABLES ##########################

# status
default ian_mike = 4                      # Mike friendship
default ian_mike_agenda = False
# status book
default book_card3 = "n"                  # Book Cards for MENTOR sage/talking animal/antihero
# minerva
default v5_ian_showup = False             # Ian goes to the office when Seymour appears (Will or compliant with Minerva)
# cindy
default v5_cindy_nude = 0                 # Cindy gets naked in photo-shoot 1- topless 2- nude
# perry and emma
default v5_perry_feelings = False         # Perry tells Ian about his feelings for Emma
default v5_perry_club = False             # Perry goes to the club
default v5_emma_talk = False              # Ian agreed to talk to Emma on Perry's behalf / if v5_emma_grind = True Ian won't actually talk to her/ v5_emma_convo3 has to be True
default perry_emma = 0                    # Progress on Perry's and Emma's relationship. +1 Perry goes to club. +1 Ian talks to Emma favorably.
# Holly
default v5_holly_date = False             # Ian meets Holly on Sunday at the park
default v5_tell_holly = "n"               # What Ian tells Holly when she asks if he has a girlfriend "wantgf" "nogf" "hategf"
# status
default v5_ian_morning = "n"              # How Ian spends his Saturday morning "gym" "write" or "n" (nothing)
# night out
default v5_ian_cool = False               # Ian wears the red shirt to the club
default v5_bouncer_in = False             # Ian gets past the bouncer
# Emma sex
default v5_ogle = "n"                     # Who Ian looks while dancing, can be "emma" or "alison"
default v5_emma_convo1 = False            # Talk with Emma about her clothes
default v5_emma_convo2 = False            # Talk with Emma about her relationships
default v5_emma_convo3 = False            # Talk with Emma about Perry / $ v5_emma_talk
default v5_dancing_emma = False           # Ask Emma to dance
default v5_emma_grind = False             # Sexy dance with Emma
default ian_emma_sex = False              # Ian kisses Emma and ends up at his place having sex
default v5_emma_finger = False            # Ian tells Emma to finger herself while fucking her ass
default emma_satisfaction = 0             # How much Ian pleasures Emma. 0 base, 1 dirty talk
# Alison sex
default v5_tell_alison = "n"              # What Ian tells Alison about Lena. n - nothing concrete, kiss -hook up at bar, sex- had sex at Lena's place
default v5_alison_public = False          # Ian fingers Alison in the club
default v5_alison_sex = False             # Ian has sex with Alison after the club
default v5_alison_boobjob = False
default v5_alison_boobjob_cum = False
default v5_alison_dirty_talk = False      # Ian talks dirty during sex
default v5_alison_jeremy = False          # Jeremy fucks Alison that night

## LENA VARIABLES #########################

# stalkfap
default stalkfap_pro = 0                  # Lena decides to take stalkfap seriously. 1-Post naughty content / 2-send hot videos --> ch13 "late" (if cafe_help == False)
# night out
default v5_lena_sexy = False              # Lena wears sexy outfit to club
default v5_lena_music = False             # Lena likes the music at the club
default v5_defend_ivy = False             # Lena says Ivy is not so bad to Louise
# Mike
default lena_mike = 4                     # Mike friendship
default lena_mike_agenda = False
default v5_mike_flirt = 0                 # Lena flirts with Mike at the club. 0 - don't like him, 1 - find him interesting, 2 - flirt with him openly.
default v5_mike_convo1 = False
default v5_mike_convo2 = False
default v5_mike_convo3 = False            # Flirting conversation, Mike tells he had GF
default v5_mike_dance = False             # Lena tries to seduce Mike
default lena_mike_sex = False             # Lena takes Mike home and has sex
default v5_mike_bj = False                # Lena takes the lead and sucks Mike's cock
default v5_mike_bareback = False          # Lena fucks Mike without condom
default v5_mike_cum = False               # Mike cums in Lena's pussy
# Louise sex
default v5_louise_sex = False             # Lena has sex with Louise after clubbing
default v5_teach_louise = False           # Lena teaches Louise the sapphic ways
default v5_louise_orgasm = False          # Lena returns the favor to Louise
default lena_louise_sex = False           # Lena has sex with Louise
default lena_reject_louise = False        # Lena rejects Louise's sexual advances, the first or second time (v5/v6)
# photo shoot
default v5_shoot = 0                      # Lena's naughtiness during the shoot 0-artsy/1-show ass/2-show pussy stalkfap/3-show pussy stalkfap_pro
# Robert sexting
default v5_robert_sexting = False         # Lena sends sexy pics to Robert
# status ian
default ian_lena_dating = False           # Ian and Lena are seeing each other
default v5_ian_confide = False            # Lena tells Ian about her relationship with Axel

##################################################################################################################################################
## v0.6 variables ################################################################################################################################
##################################################################################################################################################

default reality = True                    # THE CAKE IS A LIE!

## IAN VARIABLES ##########################

# status book
default book_card4 = "n"                  # Book Cards for CHALLENGE doom/treason/fight
# Ian stats
default ian_weed = 0                      # Ian smokes weed. Max 2 (ch 13)
default ian_lena_dom = 0                  # Ian has a more dominant attitude towards Lena (+1, push head), chapter 8 (=2) Ian fuck hard, chapter 11 (+1, choking) ( max 2)
# perry
default v6_perry_feelings = False         # Perry discusses Emma with Ian and Wade
# Lena
default v6_lena_invite = False            # Ian invites Lena to night out with Alison, she declines but sets a precedent
# cindy and wade
default v6_cindy_pg = 3                   # The picture Cindy post on peoplegram -1- backside 2-shirt up 3- no shirt 4- bare ass // used in ch 12 to track Cindy's daring selfie
default v6_stop_wade = False              # Ian stops Wade from calling Cindy. If v5_cindy_shoot he LIES. If ian_cindy_model he kinda lies. $wade_cindy = 1
default v6_confess_wade = False           # Ian tells Wade he was involved with Cindy. $ian_wade = 2 $wade_cindy = 1 +1WP
# night out
default v6_berate_alison = False          # Minor flag, Ian says Alison's a mess to Cherry. Later she will tell Alison and will lose 1 friend point
default v6_ian_drinks = "n"               # Ian goes to get drinks with "cherry" (tells Ian about Alison's feelings) or stays with "alison"
default v6_rightaway = "n"                # Ian leaves the bar to have sex with "cherry" or "alison" right away
# Alison
default ian_alison_dating = False         # Ian and Alison have an ongoing relationship
default v6_alison_pics = 0                # Alison models for Ian, tied to ian_alison_like (0,1,2)
default ian_alison_like = 0               # Ian tells Alison she's his favorite (2) or that she like her a lot (1) In chapter 7, Ian agrees to go on a trip (2) or refuses (0-1)
default v6_alison_extra_pic = False       # Ian takes two more naughty pics of Alison
default v6_alison_cum = False             # Ian creampies Alison
# Cherry
default ian_cherry_dating = False         # Ian and Cherry have an ongoing relationship
default v6_cherry_selfie = False          # Cherry sends a selfie if Ian asks
default v6_cherry_alright = False         # Ask cherry if she's OK when making out
default cherry_satisfaction = 0           # Cherry's sexual satisfaction with Ian. Max. 4 (fuck two times)
default v6_cherry_wits = False            # Ian guesses Cherry's bothered by a guy
default v6_cherry_anal = 0                # Ian has anal sex with Cherry 1-tease asshole 2-anal sex
# Lena date
default v6_lena_family = False            # Lena tells Ian about her family and brother (never used)
default v6_ian_confess_lena = False       # If ian_alison_dating or ian_cherry_dating are True, Ian can tell Lena that he's sleeping with other people- +1WP
default v6_lena_blowjob = False           # Lena blows Ian at his place
default v6_lena_swallow = False           # Lena swallows Ian's cum
default v6_lena_sex = False               # Ian fuck Lena spending -1WP
# minerva
default ian_minerva_sex = False           # Ian fucks Minerva to keep his job
default ian_defy_minerva = False          # Ian beats Minerva to keep his job +1WP
default v6_minerva_hard = False           # Ian puts Minerva in her place, dom approach. If false, kiss, softer approach. In Ch 7 changes to numerical: +1 put her in her place, +1 not eat her pussy

## LENA VARIABLES #########################

# Lena
default lena_tattoos = False              # Lena is interested in getting a tattoo done
default lena_song2 = 0                    # Value of Lena's second song, min. 0 max. 6
default song_2a = "n"                     # rusty, crack, closed
default song_2b = "n"                     # weeping, sad, gray
default song_2c = "n"                     # lethargy, loneliness, dust
default v6_call_mom = False               # Lena calls her mom, if not, lena_lenamom = 2 (min.)-(max.)= 4 +1 for money
default lena_bbc = False                  # Lena lusts after BBC after unlocking Louise's phone
default v6_masturbate = "n"               # what Lena thinks while masturbating: jeremy, ian, holly, mike, robert, axel, louise
default lena_anal_first = "n"             # Who takes Lena's anal virginity, mike, robert, ian
default outfit_bunny = False              # Lena decides to wear the bunny outfit
# cafe
default cafe_music = False                # Lena plays at the café
default cafe_nude = False                 # Lena poses at the café
#jeremy
default v6_spy = False                    # Lena unlocks Louise's phone and masturbates to Jeremy's pics
#louise
default v6_louise_sex = False             # Lena accepts Louise nightly advances
default v6_louise_pussy = False           # Louise performs cunnilingus on Lena
default v6_louise_dildo = False           # Lena tells Louise to use the dildo
default v6_louise_orgasm = False          # Lena returns the favor eating Louise out
# Ian
default v6_ian_selfie = False             # Lena and Ian exchange sexy selfies
default v6_ian_date = False               # Lena invites Ian over at night
default ian_dirty_talk = False            # Lena shows Ian her more naughty side, max 2
# Mike
default lena_mike_dating = False          # Lena starts hooking up with Mike
default mike_dirty_talk = 0               # Lena uses dirty talk with mike: 1-being used like a slut 2-badmouthing girlfriend max 2
# Robert
default v6_robert_date = False            # Lena meets Robert to fuck after his vacation
default robert_dirty_talk = 0             # Lena tells Robert what he wants to hear (he's the best) max 2
# Stan
default stan_simp = 0                     # Stan is caught masturbating to Lena - 0 not caught 1- scolded 2 -encouraged, 3-caught spying and called out 4-allowed to spy /// --> 5= v10_stalkfap == "stan"
# Holly
default v6_holly_kiss = "n"               # Holy gets a practice kiss with "lena" or "ivy"
default v6_hollyconvo1 = False            # Flag to ask about family
default v6_hollyconvo2 = False            # Flag to ask about books
default v6_hollyconvo3 = False            # Flag to ask about relationships
# sex toys
default lena_dildo1 = True                # Lena buys a cock-shaped dildo -- OBSOLETE
# Ivy
default v6_stalkfap_ivy = False           # Lena checks Ivy's stalkfap for ideas
# Seymour
default lena_job_seymour = False          # Lena is working for Seymour
default seymour_disposition = 0           # Lena's disposition to working with Seymour. 3- obey seymour (-1 will), extra shot in photo shoot 2- obey seymour 1- keep in control 0- dislike. ///---/// After chapter 9 --- (3-devoted to Seymour) (2-on board with Seymour) (1-didn't use vibrator) (0.5-hates seymour but uses vibrator) (0-hates Seymour, rejected vibrator)
default v6_seymour_shoot = "n"            # The pose Lena chooses to do "spicy" or "elegant"
default seymour_necklace = False          # Seymour gives Lena the onyx necklace
# Axel
default v6_axel_work = False              # Lena agrees to work as a model with Axel
default v6_axel_pose = 0                  # Lena agrees to pose with Axel. 0- never offered 1- refuse (fallout with Seymour/Agnes) 2-pose 3- full scene
# Agnes
default lena_agnes = 3
default lena_agnes_agenda = False
default ian_agnes_agenda = False
default v6_agnes_shoot = False            # Lena is offered Agnes shoot if she doesn't work for Seymour
# Jessica
default jess_bad = False
default ian_jess = 3
default lena_jess = 3
default ian_jess_agenda = False
default lena_jess_agenda = False
default jess_look = 1
default fjess = "n"

##################################################################################################################################################
## v0.7 variables ################################################################################################################################
##################################################################################################################################################

## IAN VARIABLES ##########################

# effort
default ian_effort_points = 0
default v7_effort_will = False            # Flag for menu
default v7_effort_book = False
default v7_effort_holly = False
default v7_effort_gym = False
default v7_effort_job = 0                 # 1-part time job, +1 money. 2-full time job, +2 money
default v7_effort_weed = False
# gym
default fight_tutorial = False            # Ian gets told the tournament's rules
# Lena
default ian_lena_love = False             # Ian tells Jeremy about his feelings for Lena: he had a crush on her (True) no strings attached (False)
default ian_stalkfap = False              # Ian subscribes to Lena's Stalkfap - ch10 --> 2 = Ian paid to see Lena's exclusive vids (stalkfap_pro== 2) 1 = only saw public content
default v7_drawing_flirt = False          # Lena flirts with Ian and does a sexy pose in the drawing session. True= Ian "robert"= Lena does the pose for Robert
# jerking off
default v7_jerking = 0                    # Ian's stamina
default v7_jerking_lena = False
default v7_jerking_alison = False
default v7_jerking_cherry = False
default v7_jerking_jeremy = False
default v7_jerking_louise = False         # OBSOLETE (never used)
#cindy
default v7_cindy_pics = 0                 # Cindy show Ian her last pictures. 1- 3 first pictures, 2-naked picture
default v7_follow_cindy = False           # Ian almost kisses cindy at the back alley but backs away
default v7_cindy_kiss = False             # Ian kisses Cindy
default ian_cindy_sex = False             # Ian fucks Cindy
default v7_cindy_athletics = False        # Athletics fuck, Ian picks up Cindy
default v7_cindy_lust = False             # Lust fuck, Ian gives it from behind against the wall
# Holly
default v7_holly_trip = False             # Ian decides to go to the book fair with Holly
# minerva
default v7_minerva_sex = False            # Ian fucks Minerva during lunch break
default v7_minerva_cunnilingus = False    # Ian eats Minerva's pussy
default minerva_dirty_talk = 0            # 1-Ian teases her 2-Ian talks about her husband
# bar night out/jessica
default v7_jeremy_show = False            # Ian shows Alison's sexy pics to Jeremy
default v7_jeremy_jess = False            # Jeremy tries hitting on Jessica if Ian refuses
default ian_jess_number = False           # Ian gets Jessica's number
default axel_knows_dating = False         # Ian tells Axel he's dating Lena
# Robert FIGHT
default v7_ianvsrobert = "n"              # The interaction before the fight "shove" "disappoint" "insult" "defend" "agree" "charisma"
default v7_fight = 1                      # Points to determine if Ian wins the fight/ RD1 start at 1 / RD 2- max 3 / RD 3- max4 - end states: "win_punch" "win_throw" "lose" "drawup" "drawdown" "n" (fight didn't happen)
default v7_round = 0                      # Counter for the round the fight's in
default v7_tapout = 0                     # Number of times Ian tries to stop Robert from fighting
default v7_lowkick = 0                    # Number of times Ian attempted a lowkick
default v7_throw = 0                      # Number of times Ian attempted a throw -1 first successful 2-first successful second failed 3- only failed one
default v7_rd3_passive = False            # Ian tries to stop the fight at round 3
# Emma
default emma_jeremy = False               # Ian tells Jeremy to try his luck with Emma
# Alison
default v7_alison_voyeur = False          # Ian asks Jeremy for more sexy pics
default v7_alison_bj = 0                  # 1- normal bj 2-throat fuck
default v7_alison_problems = False        # Ian listens to Alison's problems
# Emma
default v7_emma_bj = False                # Emma gives Ian a BJ before the party
default v7_bj_call = False                # Ian picks up Perry's call (both Alison and Emma)
# Holly
default v7_holly_kiss = False             # Holly Kisses Ian at the hotel
default ian_holly_sex = False             # Ian and Holly have sex during the trip
default v7_holly_bj = False               # Ian asks for a BJ
default v7_holly_lick = False             # Ian eats Holly's pussy
default v7_holly_rough = False            # Ian fucks Holly on all fours hard

## LENA VARIABLES #########################

# Lena
default lena_wardrobe_wits1 = False
default lena_wardrobe_charisma1 = False
default lena_wardrobe_athletics1 = False
default lena_wardrobe_lust1 = False
default v7_necklace_sell = False          # Lena sells the necklace Seymour gifted her. After ch9 #1=told seymour she forgot #2=told seymour she lost it #3=told seymour she sold it
default lena_drugs = False                # Lena takes Molly with Ivy
default v7_lena_masturbate = False        # Lena masturbates with dildos
default v7_piercing = 0                   # The piercing Lena gets changes to, 1 or 2 (used for retrocompatibility in phone gallery images in case Lena decides to change her piercing later in game)
# sexual fantasies
default lena_fty_show = False             # Exhibitionist fantasy
default lena_fty_slave = False            # Sex slave fantasy
default lena_fty_lesbo = False            # Lesbian fantasy
default lena_fty_3some = 0                # 1 = with two guys 2 = with two girls
default lena_fty_bbc = False              # BBC fantasy
default lena_bbc_masturbate = 0           # tracks Lena masturbating to bbc porn (2= masturbated ch7 AND ch12) (1= only masturbated ch12)---> (3= v13_bbc_dildo == True - used bbc dildo to masturbate )
# tattoo
default v7tattoobuy = False               # Lena visits the tattoo parlor to get an appointment with Jess (used with lena_tattoos, or Lena isn't interested in getting one)
# toys
default toy_dildo = True                  # Normal cock dildo
default toy_collar = False                # BDSM collar
default toy_badboy = False                # Bad boy big cock dildo
default toy_mandingo = False              # Mandingo kin BBC dildo / available from ch11
# Axel
default lena_axel_desire = False          # Lena gives in to her desire for Axel in her dreams ---- CH 11 --determines if Axel route is on(only during CH 11)--- #1=half sex scene #2=full sex scene
# Lena ian
default v7_ian_date = False               # Lena and Ian spend the night and morning at Ian's place
default lena_ian_mad = False              # Lena gets angry at Ian for fighting Robert. After ch 8, lena_ian_mad = 0 means Lena is not mad anymore. If still True, ian_lena_over = True
default lena_ian_love = False             # Lena is falling for Ian. If False, no string attached. -----> chapter 11: # 2 -lena confession during sex / # 1 no second confession ---> (ch12 == 0.5 --Lena turns off love)
default v7_69_cum = False                 # Ian cums in Lena's mouth while 69ing
default lena_satisfaction = 0             # Lena's sexual satisfaction with Ian. After CHAPTER 8, 0- normal sex 1-soft sex 2-hard sex
default ian_lena_sex_late = False         # If Lena and Ian hadn't fucked up til now, they finally do. (if == 2, first time happened on ch 10)
# Robert
default lena_robert_over2 = False         # Lena dumps Robert late (Ian doesn't know it yet)
default lena_robert_dating = False        # Lena and Robert become fuckbuddies, have sex either Thursday or Saturday
# Holly
default holly_marcel = False              # Holly makes conversation with the bouncer at Blazer
default holly_guy = False                 # Ivy tells her friend to hit up Holly ---- chapter 10 if ian_holly_dating == False (0= not interested /1= interested /2= hooking up /3= lena voyeur) ch 11 (4=incite)
# billy
default billy_trust = 0                   # Lena's opinion about Billy's project: 0 distrust 1- give it a try 2- good idea
default billy_model = False               # Lena agrees to take part in Billy's business
# Mike
default v7_mike_bj = False                # Lena suck's Mike's cock in the DJ booth
# Louise
default v7_louise_sex = False             # Louise is waiting for Lena in her room after she goes out with Ivy.
default louise_dominant = False           # Lena acts dominant towards Louise
default v7_louise_feet = False            # Lena orders Louise to lick her feet
default v7_louise_rimmjob = False         # Louise rims Lena's asshole
# Ivy game
default v7_game = False                   # Lena goes to Ivy's place
default v7_lena_bet = False               # Lena bets money to play the game
default v7_game_quit = False              # Lena quits the game and refuses the dare
default v7_ivy_masturbate = False         # Lena dares Ivy to masturbate
default v7_louise_kiss = False            # Lena kisses Louise during Ivy's game
default v7_game_pants = False             # Lena pulls Jeremy's cock out
default v7_louise_game_quit = False       # Lena makes Louise kit the game
default v7_game_grind = False             # Lena makes Louise grind in Jeremy's cock
default v7_ivy_kiss = False               # Lena and Ivy Kiss longer
default v7_ivy_lesbo = False              # Ivy licks Lena's boobs
# Jeremy
default v7_bbc = "ivy"                    # Who jerks Jeremy off. "lena" "ivy". By default that night Ivy gives bj to Jeremy
default v7_bbc_insist = False             # Lena insist to Jeremy to let her jerk him off
default v7_bbc_cum = False                # Lena makes Jeremy cum.
# stalkfap dance
default v7_dance = 0                      # 1-take bra off 2- full naked
default v7_dance_stan = False             # OBSOLETE -- Lena recruits Stan's help, lena_fty_show = True -- OBSOLETE
default v7_dance_provoke = 0              # 1- shake boobs 2-shake ass w panties 3-shake nude ass

##################################################################################################################################################
## v0.8 variables ################################################################################################################################
##################################################################################################################################################

## LENA VARIABLES #########################

# clothes
default lena_wardrobe_lingerie = False    # Lingerie 1 (Black - lena_lingerie1a.webp)
default v8_choker = False                 # Lena wears choker
default v8_sy = False                     # Lena wears Seymour's necklace
default lena_wardrobe_bunny = False       # Bunny outfit
# toys
default toy_double = False                # Double ended dildo
default toy_tail = False                  # Anal plug with tail
default toy_lush = False                  # lush vibrator (available chapter 11)
default toy_wand = False                  # vibrating wand (available ch11 in sex shop or if seymour_desire == True)
# stalkfap
default v8_stalkfap_comments = False      # Lena reads comments and can reply to DMs
default v8_stalkfap_dm1 = 0               # Lena replies to polite DM +1, Lena rewards him with a sexy selfie + 2
default v8_stalkfap_dm2 = 0               # Lena replies to custom request DM, +1 ass video, guy doesn't pay, +2 fingering video
# Robert
default v8_robert_public = False          # Lena fucks Robert at the restaurant
# Ian
default ian_lena_over = False             # Lena break it up with Ian because of Holly (v7_holly_kiss) or Robert fight (lena_ian_mad)
# Louise
default lena_louise_sex_late = False      # Lena hooks up with Louise for the first time after Jeremy and Ivy hooked up in their night out, in front of Louise
default lena_louise_sex_late2 = False     # Equivalent to v6_louise_sex, Lena has her second late sex scene with Louise on Saturday
default v8_louise_sex = False             # Lena decides to use Louise (not True if lena_louise_sex_late)
# ed
default v8_compliment_ed = False          # Lena shares her troubles when Ed asks her
# Emma
default v8_emma_chat = False              # Lena talks with Emma to learn more about her
# Jeremy
default v8_jeremy_flirt = False           # Lena flirts with Jeremy texting
default v8_jeremy_sex = False             # If v8_jeremy_flirt: Lena gives BJ to Jeremy in Louise's room, can also have sex
default lena_jeremy_sex = False           # Lena fucks Jeremy
# sexting
default v8_lena_sexting = "n"             # Lena can sext with Ian, Mike or Robert
default v8_sexting_full = False           # Lena sends pussy and masturbation pics (always true with Robert and Mike)
default v8ianpicnude = False              # Ian sends naked pic to Lena
# Axel
default lena_axel_dating = False          # Lena calls Axel and ask him to hook him up with Wildcats agency, agreeing for a photo shoot
# concert
default v8_song = 0                       # The long Lena plays at the concert. 1= Broken Dream 2= Shine Again
# Holly
default v8_holly_date = False             # Lena invites holly over after shopping
default v8_holly_ivy = False              # Holly stays over and talk about sexual fantasies (if holly_gym == True and ian_holly_dating == False). If not, Lena can invite her over (v8_holly_date = True)
default v8_holly_strip = False            # Lena takes part in Ivy's strategy to undress Holly
default v8_holly_sex = "n"                # Holly has sex with either "lena", or "ivy"/"lenaivy" if Lena let's Ivy play with her
default lena_holly_sex = False            # Lena and Holly have sex
# Mike
default v8_mike_sex = False
default lena_mike_love = False
default v8_mike_extras = "n"

## IAN VARIABLES ##########################

# status book
default book_card5 = "n"                  # Book Cards for LOVE INTEREST romantic/crude/metaphysical
# job victor
default ian_job_victor = False            # Ian starts collaborating with Victor's magazine with his satirical reviews
# Holly
default ian_holly_dating = False          # Ian doesn't reject Holly (if ian_lena_love == False) and she agrees to "see what happens next"
default v8_holly_bj = False               # Holly gives Ian a morning BJ
default v8_holly_cum = False              # Ian cums on Holly
# Alison
default ian_milo_agenda = False           # Alison's ex appears
default v8_alison_sexting = 0             # If Ian is nice, Alison sexts with Ian at night. 1-Alison sends 1 pic 2- Ian convinces her to send a second pic
default v8_alison_sext = 0                # What Ian says while sexting: 1-generic 2-cum inside pussy 3-anal sex rejection
default v8_alison_ex = "ian"              # Who knocks out Milo, "jeremy" or "ian"
# minerva
default v8_minerva_flirt = 0              # What Ian says to Minerva at the office 1-compliment 2-tease 3-touch butt
default v8_minerva_sex = False            # Ian fucks Minerva while she calls her husband
default ian_minerva_dating = False        # Ian asks Minerva to be nicer to him //// Chapter 10 sex scene ---> 3 = love / 2 = nice / 1 = just sex / False = no sex
# Lena
default v8_lena_anal = False              # Ian fucks Lena in the ass
default v8_lena_story = False             # Lena tells the complete story to Ian about her break up with Axel due to Cherry
# drugs
default v8_trip = False                   # Ian has a psychedelic experience
default trip_kill = False                 # Ian cuts the trip short
# Emma
default v8_emma_sex = False

##################################################################################################################################################
## v0.9 variables ################################################################################################################################
##################################################################################################################################################

## IAN VARIABLES ##########################

# status book
default book_card6 = "n"                  # Book Cards for ENDING victory/sacrifice/defeat
default v9bookmarker = 0                  # Tracking var. determines the sequence of book writing scene (depends on v9_alison_trip)
# Lena
default v9_lena_call = 0                  # What Ian tells Lena over the phone call when she's at her parents: 0- passive 1-friendly 2-loving
default v9lenagreetkiss = False           # Tracking var. Ian kisses Lena when greeting her
default v9_ian_family = 0                 # Ian tell sLena about his family 1= dad 2= mom
default ian_lena_makeup = False           # If ian_lena_over because of Holly, Ian apologizes to Lena and makes up with her.
default ian_lena_couple = False           # Ian and Lena are officially dating
default ian_lena_open = False             # Ian proposes a more open relationship to Lena (if ian_lena_couple) // ch.12 "ian" = Ian incites open rel, Lena is reluctant // "lena" = Lena asks for open rel, Ian accepts
default ian_lena_breakup = False          # Lena rejects Ian and he decides to stop hooking up (ian_lena_love == True and lena_ian_love == False)//// CH 10 - Ian breaks up with Lena (v10_ian_left)
default v9_lena_sex = 0                   # Ian and Lena have sex at the park: 1- only masturbate 2- have sex 3-warn about peeping tom and Lena stops 4-Lena lets him watch
# Alison
default v9_alison_trip = False            # Ian goes to the trip with Alison
default alison_jeremy_3some = 0           # Ian agrees to a possible threesome with Alison and Jeremy. 0 -rejection / 1- Ian considers it / 2- Ian agrees with Alison / 3- future 3some repeat
default v9_fireworks = "n"                # How Ian treats Alison during the fireworks. "lust" - go hotel to fuck / "love" - embrace and kiss / "n" - silence
default ian_alison_love = False           # Ian and Alison are serious about each other (ian_lena_love == False and ian_holly_dating == False) ---> ch13 # Ian responds to sharing a flat with Alison 0=reject/1=think about it/2=enthusiastic
# gillian
default ian_alison_dom = False            # Ian sees Alison just as a fuck-buddy and sex object
default v9_alison_creampie = False        # Ian cums inside Alison's pussy
default v9_alison_anal = False            # Ian pushes Alison to try anal sex with disastrous consequences
default v9askanalalison = False           # 
default v9alisonphone = False             # If v9_alison_trip == False, ian listens to Alison over the phone when she calls him to talk about Milo
default v9_alison_voyeur = False          # Ian watches Alison's threesome video (if v7_alison_voyeur = True)
# clothes
default ian_wardrobe_wits1 = False
default ian_wardrobe_charisma1 = False
default ian_wardrobe_athletics1 = False
default ian_wardrobe_lust1 = False
default v9_ianwearing = "n"               # Tracking var. the outfit Ian chooses to currently wear "wits" "charisma" "athletics" "lust" "n" = normal
# perry
default cafe_perry = False                # Ian convinces Perry to help at the café (after Ch 10 needs to be used with cafe_help, otherwise irrelevant)
default v9_perry_axel = False             # Ian discusses Axel with Perry and shows him a picture -- NOT USED / OBSOLETE
# Holly
default v9_holly_elephant = False         # Ian hears about Holly's grandma story and the elephant figurines
default v9_holly_bj = 0                   # Rates Holly's performance 0-bad 2-almost cum 3-cum
# cindy
default v9_cindy_shoot = 0                # Ian agrees to go to the photo shoot : 3-endures entire shoot / 2-gets kicked out of Axel's home / 1-Gets told to leave by Cindy at the start / 0- refused to go to Wade
default v9_axel_warn = 0                  # Ian warns Axel not to get his hands on Cindy during the shoot/ 1- warns when touching face / 2-warns while touching leg
default ian_cindy_over = False            # Ian and Cindy agree to keep what happened between them a secret and not speak or repeat it again (v7_ian_kiss or ian_cindy_sex) // CH 12 ian_cindy_over = 2 (if ian fights with Cindy) // CH 13 ian_cindy_over = 3 (breakup during date)/ = 4 (breakup after sex bc Axel)/ = "lenalate" (breakup bc Ian is dating Lena now)
default ian_cindy_dating = False          # Ian pursues a relationship with Cindy (if no ian_cindy_love, Cindy is cheating on Wade)
default ian_cindy_love = False            # Ian confesses he feels something for Cindy and ask her to break up with Wade
default v9_cindy_sex = False              # Ian has sex with Cindy at her home
default cindy_satisfaction = 0            # MAX 2 (ch9). Ian's performance on the bedroom. MAX 3 (ch10) /// MAX 4 (ch12)/// MAX 6 (ch13)
default v9cindycunnilingus = False        # tracking var. Ian chose to eat Cindy's pussy
default v9_cindy_morning = False          # Ian has sex with Cindy in the morning
default v9_cindy_lie = 0                  # How convincing is the lie Ian tells Perry, min 0 max 3
# Cherry
default v9_cherry_date = False            # If dating, Ian goes to the bar with Cherry. If not, she will be at his place talking to Perry on Sunday.
default v9_cherrytalk = "n"               # What Ian responds to Cherry's story /"naive" he defends her / "neutral" he just listens / "wrong" he accuses her
default ian_cherry_love = False           # Ian pursues his relationship with Cherry and has sex with her (otherwise relationship stagnates for now)
default ian_cherry_over = False           # Ian explicitly refuses Cherry at the date (you fell in love with the wrong guy)
default v9_cherry_sex = 0                 # Ian has sex with Cherry (ian_cherry_love = True) /2 = "making love" /1 = "making you cum" /0 = "normal"
# Emma
default v9_emma_sext = 0                  # Ian texts Emma during the card game (emma_jeremy or v8_emma_sex) 1-text 2-sext

## LENA VARIABLES #########################

# Lena
default v9_luggage = "n"                  # Who helps Lena carry her luggage home when v9_alison_trip == True, "mike" "robert" "stan"
default v9_luggage_sex = "n"              # Lena has sex with "mike" or "robert" after they help her unpack
default v9_tat = 0                        # Lena decides to get a tattoo in this chapter 1, 2, 3
default v9_piercing = 0                   # Lena decided to get a navel piercing in this chapter 1, 2
# Mike
default lena_mike_over = False            # Lena turns down Mike if ian_lena_couple == True
# Robert
default v9_lena_robert_over = False       # Lena breaks up with Robert if ian_lena_couple == True (lena_robert_over becomes True additionally)
# Axel
default axel_disposition = 0              # Lena's starting disposition towards Axel at the photo shoot 0 - cold 1 - neutral 2 - amicable//// CH 10 (0=aggressive, 1=cold, 2=had sex)(3-love reaction ch 11)
default v9_axel_pose = 0                  # Lena's poses in the shoot 0 - normal 1- cheeky attitude 2- bed poses
default v9_axel_kiss = "n"                # How lena reacts to Axel's kiss "push" "wait" "kiss"
default v9_axel_sex = False               # Lena has sex with Axel
# Seymour
default v9_contract = 0                   # Lena asks about the contract during dinner (0, reject right away/ 1, ask clauses / 2, agree right away / 3, add additional clause)
default lena_seymour_dating = False       # Seymour becomes Lena's patron (until chapter 11)
default seymour_desire = False            # Lena becomes Seymour's object of desire (if v9_seymour_orgasm == True) # ch11 ---> use this variable to track Lena+Seymour path (if she refuses lena_seymour_dating remains True to indicate she had been working for him)
default v9_seymour_orgasm = False         # Lena uses the vibrator and ends up cumming (squirted if seymour_desposition > 1)

##################################################################################################################################################
## v0.10 variables ###############################################################################################################################
##################################################################################################################################################

## IAN VARIABLES ##########################

# Ian
default ian_fit = 0                       # tracks Ian's physical transformation (CH10: 1 = if ian_athletics > 5 )
default v10_ianlook = "n"                 # Memory variable to track ian's choice of attire during this chapter
default v10_round = 0                     # Tracking for Jeremy's spar (3 rounds) "tapout" means Ian quit the fight at the last round
default v10_sparring = 0                  # Score of Ian vs Jeremy sparring (max 2)
default v10_iangym = False                # Ian decides to go to the gym before Lena's concert
default v10_pool = 0                      # Ian plays a game of pool (0= never played/ 1= lost/ 2= won)
# social media
default v10_follow_ivy = False            # Ian follows Ivy on Peoplegram
default v10_text_lena = False             # Ian texts Lena before bed
default v10_text_cindy = False            # Ian texts Cindy before bed
default v10_text_holly = False            # Ian texts Holly before bed
default v10_text_cherry = False           # Ian texts Cherry before bed
default v10_text_alison = False           # Ian texts Alison before bed
default v10_text_jess = False             # Ian texts Jess before bed -- 1 = got a date / 2 = insisted too much / 3 = screwed up his chance
default v10_text_selfie = False           # Ian sends a sexy selfie
# work
default ian_nat_agenda = False
default ian_nat = 3
default ian_clark_agenda = False
default ian_clark = 3
# Minerva
default v10natflirt = 0                   # Ian's response to Nat --- 2: get phone number --- 1: neutral --- 0: ask for Seymour's contact
default v10_robert_fight = False          # Ian calls out Robert at the restaurant (v10_minerva_dinner == 2)
default v10_minerva_compliment = 0        # How Ian compliments Minerva's hair 1 = normal compliment 2 = lovely compliment
default v10_minerva_dinner = 0            # Ian has dinner with Minerva (2) or goes straight to the hotel, only inyerested in sex(1)
default ian_minerva_over = False          # Ian breaks it off with Minerva or fails at the dinner (v10_minerva_dinner == 2)
default v10minfamily1 = False             # Ian asks about Minerva's husband
default v10minfamily2 = False             # Ian asks about Minerva's son
default v10_minerva_anal = False          # Ian fucks Minerva's ass (ian_minerva_dating < 3)
# book
default book_points = 0
default v10_book_advice = False           # Ian asks Holly for advice on his book
# Holly
default v10_holly_sex = 0                 # If dating, Ian has sex with Holly. 1 = came outside 2 = came in mouth (needs holly_change> 2)
default ian_holly_love = 0                # How Ian responds to Holly's feelings (0-cold/1-ok/2-correspond) ---> Chapter 11 ---> True = Ian opens up about Gillian and accepts his feelings for Holly
# Lena
default ian_cuck = False                  # Ian enters the cuck route (if v9_axel_sex or lena_seymour_dating and seymour_disposition < 2) ----> prologue: # 2 = sexless/ deny sex in ch 11 /// # 1 = ride face and v10 jerkoff (v10_lena_sex < 2) ----> ch 11: #3 epilogue tease ///# 2= sexless/came from footjob+used dildo /// # 1= talk about guys
default v10_lena_sex = 0                  # Ian has sex with Lena during their date. (1 == handjob no sex / 2 == sex 1st act / 3 == full sex scene / 4 == hard sex)
default v10_lena_mad = "n"                # Lena gets mad at Ian after the concert "stalkfap" (Ian criticizes Lena's stalkfap) / "sex" (sexless Ian pressures Lena) / "bothered" (sexless Ian demands sex)
default ian_stalkfap_on = 0               # Ian participates with Lena's Stalkfap taking pics (1 = insecure / 2 = turned on)
# Jess
default v10_jess_porn = False             # (if jess_bad and ian_jess_number) Ian can check out Jess' porn videos
default v10_jess_video1 = False           # Marker for videos Ian chooses to see
default v10_jess_video2 = False           # Marker for videos Ian chooses to see
default v10_jess_video3 = False           # Marker for videos Ian chooses to see
default v10_jess_date = False             # Ian goes on a date with Jess
default v10_askjess_ivy = False           # Jess tells Ian about Ivy's personality
default v10_askjess_city = False          # Jess tells Ian about why she moved to Baluart
default v10_askjess_scar = False          # Jess get mad at Ian for inquiring about her scar
default v10_askjess_love = False          # Jess tells Ian about her love life
default v10_pay_jess = False              #
# $ ian_jess                              # Tracks Ian messing up ---> ( 0 = pay 4 sex / 1 = talk about porn/hit on her -------- (bigger than)> 4 = Jess shows interest / < 5 = not interested)
# Ivy
default v10_ivy_gym = 0                   # Ian trains with Ivy ( 1 = lost / 2 = let go of choke and lost / 3 = won)
# Wade
default v10_encourage_wade = False        # Ian encourages Wade to become a pro player
# Emma
default v10_joinlate = False              # Tracking var Ian shows up late at Lena's concert bc writing/gym
default v10_emma_sex = False              # Ian fucks Emma at the Fortress
# Alison
default ian_alison_breakup = False        # Ian breaks up with Alison bc he's dating Lena/Holly/Cindy // ch 11 == "cindy" Ian forced to break up bc ian_cindy_love// ch12 == "lena" if ian_lena_couple == "late"//ch13 == "13" Ian cums inside Alison and decides to break up
default v10_wine = False                  # Ian buys wine for Alison's date (+2 charisma)
default v10_alison_creampie = False       # Ian chooses not to wear a condom and creampies Alison
default v10_alison_3some = "n"            # Ending of 3some scenes/ "ian" Ian fucks her last / "jeremy" Jeremy fucks her last / "duo" both come at the same time / "n" Ian fails and goes limp
default v10_alison_3some_wait = False     # Ian needs to go to the bathroom to pump himself up (can later succeed --- or fail ($ v10_alison_3some = "n"))
default v10ianshoppedwine = False         # Track var for ian buying wine for Alison
# Cindy
default v10_cindy_bj = False              # Ian gets a bj from Cindy
default v10_cindy_compliment = False      # Ian compliments Cindy profusely after sex
default v10_perry_lie = 0                 # How successful Ian's lies to Perry to conceal Cindy are (0-3max)

default v10perrychatbook = False

## LENA VARIABLES #########################

#lena
default v10_money_family = 0              # Tracks how much money Lena sends to her family this chapter 0-1-2
default v10_lena_masturbate = 0           # Lena chooses to masturbate before Thursday's concert 1-normal 2-anal
default v10_tat = 0                       # Lena decides to get a tattoo in this chapter 1, 2, 3
default v10_piercing = 0                  # Lena decided to get a navel piercing in this chapter 1, 2
default v10boutique = False               # Track var for boutique scene trigger
default lena_posh_tryon = False           # Lena tries on the black dress
default v10_call_mom = False              # Lena calls her parents
default lena_wardrobe_black_dress = False # Lena buys the expensive black dress
default lena_cheating = False             # If ian_lena_couple, Lena is cheating on Ian (v10_stalkfap== "mike" or v10_mike_sex or v10_wc_bj == "mike"/"mark")--- also take into account (v9_axel_sex / v10_ivy_sex / lena_louise_sex and lena_reject_louise == False) which are not represented
#                                         # Ch 12 ---> (0.5 = Lena decides to stop cheating // 1 = break up with Ian // 2 = Continue cheating)
default v10_shooting = 0                  # The poses Lena does in Seymour/Kent photo shoot (max.4)
# seymour
default v10_defend_seymour = False        # Lena defends Seymour in front of Emma (seymour_disposition > 1)
# robert
default v10_robert_sex = False            # Lena booty calls Robert after the concert, only if v10_lena_mad == "stalkfap" or "sex" (short scene)
# mike
default v10_mike_sex = False              # Lena responds to Mike's booty call during the concert
default mike_collab = False               # Lena agrees to collab with Mike on a song
default v10_mark_flirt = False            # Lena flirts with Mark at the party
# stan
default holly_stan = False                # Lena encourages Holly to hang out with Stan
default v10_stan_concert = False          # Tracking var. Stan shows up at the concert (if lena_stan > 5)
default v10_stan_shoot = 0                # Lena promises Stan another photoshhot 0-no promises 1-maybe and money 2-yes (v4_stan_shoot or v2_stan_model)
default v10_stan_pay = 0                  # Lena pays extra for rent (2=pays/1=can't afford to/0=refuses to pay)
default stan_change = 0                   # (triggers if v10_stan_concert == True) 2 =Lena appeases Stan / 1=Lena sides with Stan against Louise/ 0= Stan acts like an incel
# perry
default v10_tease_perry = False           # Lena teases Perry when he finds her wearing just a towel (stalkfap == True)
# louise
default v10_louise_sex = False            # Lena has sex with Louise this chapter (after concert or later)
default lena_louise_collar = False        # Lena uses the collar with Louise (continuity trouble since "toytrack_collar" has been altered in ch8, should work fine with fresh save)
default v10_louise_orgasm = False         # Lena 69 with Louise
# jeremy
default v10_jeremy_3some = False          # Lena participates in a 3 some with Louise and Jeremy (#1-share/#2-leave louise out)
# holly
default v10_holly_shoot = False           # Lena nudges Holly to show her a sexy pose
default v10_lena_holly_sex = False        # Lena has sex with Holly after the shoot (2=lena fingers Holly/3=Holly eats Lena out)
# ivy/party
default v10_ivy_gift = 0                  # The birthday gift Lena gets for Ivy (2-expensive perfume/1-cheap earrings/0-no gift)
default v10_partytalk = "n"               # Who Lena sits next to at the club, "ivy", "mark", "jess", "alice"True
default v10_ian_left = False              # Ian breaks up with Lena and leaves the party (if v10_lena_mad == "stalkfap" or "sex")
default v10_lena_drug = False             # Lena takes MDMA with Ivy at her party
default v10_lena_dance = "n"              # Lena dances with "ian","mike","mark","ivy"
default v10_wc_bj = "n"                   # Lena gives a blowjob to "ian"/"mike"/"mark" in the bathroom
default v10_wc_swallow = False            # Lena shallows her partner's cum after blowjob
default v10_ivy_sleep = False             # Lena spends the night at Ivy's place
default v10_ivy_sex = 0                   # Lena gets it on with Ivy (0=refused right away/1=stood still/2=Ivy ate her out/3=Lena ate her out)
# axel
default v10_axel_text = 0                 # Lena's response to Axel's text (if v9_axel_sex) 1-ignore 2-neutral 3-friendly
default v10_axel_fight = "n"              # Axel gets into an altercation with "ian", "mark", or "mike" ("mikeian" if Lena dismisses Ian and talks to Axel)
# stalkfap shooting
default v10_stalkfap = 0                  # Lena chooses a partner to take pictures "ian" "mike" "stan" "n"
default v10_shoot_look = 0                # Lena's choice of attire for the shooting ("bunny", "lingerie", 0-->default)
default v10_stalkfap_anal = 0             # Lena uses the plug and dildo and gets fucked in the ass (0=none/1=plug /2=dildo /3=double penetration)
default v10_stalkfap_dildo = 0            # The dildo Lena uses 0=none 1=normal 2=badboy
default v10_stalkfap_jerkoff = 0          # During Stalkfap shoot with Ian, Lena tells him to masturbate (becomes False if they have sex afterwards)/// with Stan is always True
default v10_stalkfap_dildo_action = False # Lena fucks her pussy/ass with the dildos in front of Ian/Mike/Stan
default v10_stalkfap_facial = False       # Lena asks Ian/Mike to cum on her face

##################################################################################################################################################
## v0.11 variables ###############################################################################################################################
##################################################################################################################################################

## LENA VARIABLES #########################

## Prologue ###############################
default v11_louise_finger = False         # Lena fingers Louise, (opens up make her cum)
default v11_louise_spank = 0              # Counter for Lena spanking Louise 1,2,3
default v11_louise_ass = False            # Lena teases her asshole (opens up dildo use)
default v11_louise_dildo = 0              # 1- normal dildo 2- double dildo ($ toy_double) 3- Fuck each other using dildo
default v11_louise_pay = False            # If Lena is paying for rent (v10_stan_pay == 2) she tries to convince Louise to pay up
# stan
default lena_stan_dating = False          # Lena agrees to go on a date with Stan
default lena_reject_stan = 0              # Lena rejects Stan's advances (1=polite/2=rough)
# ian
default v11iandinner = False              # Tracking var Lena goes to dinner w/Ian before sex
default v11_lena_dom = False              # Lena rides Ian's face (prologue) / teases Ian with her feet (Ch 11)
default v11_lena_choke = False            # Lena asks Ian to choke her (ian_lena_dom is 1 or 2, lena_fty_slave = True)
default ian_cheating = False              # Ian cheats on Lena (if ian_cindy_dating or ian_minerva_dating or ian_alison_fuck) ------> CH 12. 2= keep doing it // 1= break up with Lena // 0.5= decides to stop 
default ian_lena_3some = False            # Lena is open to having a 3some with Ian
# mark
default lena_mark_dating = False          # Lena tells Mark she's interested in a date ---> if v11_mark_sex == False this becomes False
default v11_ignore_mark = False           # Lena ignores Marks message/ otherwise she tells him she's not interested
# axel
default lena_axel_over = False            # Lena tells Axel she's not interested in working with him anymore (lena_axel_desire == False)
default v11_axel_cherry = False           # Lena asks Axel for explanations about his meeting with Cherry
# holly
default lena_holly_love = False           # Lena is open to more than friendship with Holly
default v11_holly_change = False          # Tracking var. for Holly's clothes (True if holly_change > 3)

## Chapter start ##########################
#family
default v11_dad_talk = False              # Lena lets her dad in and talks about her mom
# song
default lena_song3 = 0
default song_3a = "n"                     # spark, magic, power
default song_3b = "n"                     # lightup, swimin, destroy
default song_3c = "n"                     # scars, clothes, mask
# stalkfap
default v11_stalkfap = 0                  # (if stalkfap_pro == 2) 1= normal dildo 2= big dildo
# robert
default v11_robert_sex = False
# mike
default v11_mike_sex = False
# stan
default v11_stan_kiss = 0                 # 3 = Lena makes out w Stan (lust) 2 = Lena kisses Stan (romantic) / 1= Lena teases Stan for future kiss (lust)
# holly
default holly_robert = False              # Lena sets up a date between Holly and Robert
default v11_holly_call = 0                # Lena responds to Holly's phone call (1-friend/2-flirt)
default lena_holly_dating = False         # Lena and Holly have exclusive relationship (no Ian/Ivy) (if lena_holly_sex or v10_lena_holly_sex and v8_holly_sex != "ivy" and v8_holly_sex != "lenaivy") ---> ch13 == "late" if entry point from broken trinity (ian_holly_breakup == 2) or v12_ian_lena_breakup and lena_go_holly == 5
default v11_lena_holly_sex = False        # Lena has sex with Holly in her room
default holly_ivy = False                 # Holly is under Ivy's influence (holly_change becomes True)
# emma
default v11_dog = 0                       # 2-pet 1-stay still 0-kick
default v11_emma_date = False             # if lena_emma > 5, Emma offers to hang out with Lena
# ivy
default v11_ivy_parents = False           # Lena tells Ivy about her family situation (lena_ivy > 7)
default v11_shower_sex = False            # "ivy" = Ivy seduces Lena in the shower / "3some" Lena and Holly join Ivy in the shower 
# louise 3 some
default v11_louise_3some = False          # Lena has a threesome with Louise and Ian or Mike ("ian"/"mike")
default v11_louise_3some_sex = False      # Ian or Mike get to stick it inside Louise's pussy
# seymour
default lena_travel = "n"                 # Lena tells Seymour where she would like to travel
# perry
default v11_perry_invite = 3              # How Lena feels about Perrys invitation (3-automatic accept/2-wants to go/1-thinking about it/0-doesn't want)

## Epilogue ###############################
# lena status
default v11_lena_dress = 0                # The dress Lena buys 1=black 2=shiny 3=athletics 4=red
default v11_lena_extras = "n"               # new. tracking var for Lena's "e" earrings and "s" stockings or "es" both
default lena_bikini = 0                   # The bikini Lena buys (1,2,3)
default lena_bikini_pic1 = False          # Emma takes a pic of bikini 1 (for dialogue consistency + optional gallery)
default lena_bikini_pic2 = False          # Emma takes a pic of bikini 2 (for dialogue consistency + optional gallery)
default lena_bikini_pic3 = False          # Emma takes a pic of bikini 3 (for dialogue consistency + optional gallery)
default lena_smoke = False                # Lena accepts Ivy's invitation to smoke
# emma
default v11_emma_pics = 0                 # Lena takes pics with Emma in the dressing room (1-emma pose/2-together/3-tease emma)
default emma_bikini = False               # Emma buys the sexy bikini
# jeremy
default lena_ivy_jeremy = False           # Lena agrees to talk to Ivy on behalf of Jeremy
default v11_bbc = False                   # Lena sucks off either "jeremy" or "marcel"
default v11_bbc_pic = False               # Lena takes a selfie with Jeremy/Marcel's cock
default v11_jeremy_kiss = False           # Lena kisses Jeremy
# bartending mini game
default v11_bar1 = 0                      # 3-pour generously/2-pour normal/1-pour scarceley/0-call marcel
default v11_bar1b = 0                     # 4-john flirts/3-pour good/2-pour bad/1-call Marcel
default v11_john_flirt = 0                # 3-flirt back/2-be polite/1-be mean
default v11_bar2 = 0                      # 3-pour correctly/2-ask Jeremy/1-pour incorrectly/0-ignore 
default v11_bar2b = 0                     # rosa round 2
default v11_rosa_fight = 0                # 4-punch her/3-slap her/2-call marcel/1-ignore her
default v11_bar3 = 0                      # 3-free shots/2-no free shots/1-call marcel
default v11_bar3b = 0                     # finley 2nd round
default v11_bar4 = 0                      # 3-Flirt with Eli/2-talk to Eli/1-serve Eli/0-Let Jeremy handle her
default v11_robert_talk = 3               # if lena_robert_sex == False, how Lena responds to Robert at the club (2-friendly/1-grumpy/0-call Marcel)
default v11_bar4b = 0                     # eli rd 2
default v11barpoints = 0                  # sum total of events
# jack
default lena_jack = 0                     # 4-full sex/3-bj sex/2-flirt/1-ignore/0-fight
default lena_jack_dating = 0              # Lena will maybe meet Jack again. 1-casual 2-hung up
# mark/ian
default v11_ian_sex  = False              # Lena agrees to drop by Ian's place after work (if #2, Ian filmed the sex scene)
default v11_mark_sex = False              # Lena hooks up with Mark after Blazer work (if #2, Mark filmed the sex scene)
default v11_lena_anal = False             # Lena has rough anal sex with Ian/Mark
# axel
# lena_axel_desire                        # CH 11 --determines if Axel route is on--- #1=half sex scene #2=full sex scene

## IAN VARIABLES ##########################

# hierofant work
default v11_book_review1 = 0              # Ian rejects the book (0) or pushes for publication (1)
default v11_book_review2 = 0
default v11_book_review3 = 0
default v11_book_review4 = 0
# cindy
default v11_cindy_bj = False              # Cindy gives Ian a BJ 
# holly
default holly_fit = False                 # if holly_gym and v11_holly_change, Holly becomes fit (she can only be Fit is holly_change is True)
default holly_clark = False               # If Holly is not dating Ian or interested in Lena, she gets friendlier with Clark
default v11_holly_anal = False            # Ian teases Holly's asshole
default v11_holly_sex = False             # Ian has sex with Holly unless he shuts her off
default v11_holly_fuck = False            # Ian encourages Holly to be bolder (more explicit sex scene)
# gillian
default v11_gillian_dream = 0             # Ian's reaction to Gillian's dream (if v3_gillian_stop == False) 0- used will to wake up / 1- resisted / 2- gave in   
default v11_gillian_talk = 0              # Ian's feelings towards Gillian: "you fucked me up" (1), "I'm still hurting" (2), "I still think about you" (3), "I've moved on" (4) ---> 2 becomes obsolete in the gillian_stop == False path (1-block/3-follow)
# alison
default alison_blonde = 0                 # Alison goes for a change of style (if alison_jeremy_3some == 2 or ian_alison_dom or ian_alison_dating == False) ---> 3- ian compliments / 2- ian likes it / 1- ian doesn't like it
default v11_gallery_sex = False           # Ian has sex with Alison in the art gallery
default v11_alison_condom = False         # Ian agrees to wear a condom (if not, only titfuck) 
default v11_alison_bbctalk = False        # Ian wants to know which cock felt better, his or Jeremy's
default v11_alison_voyeur = False         # Tracking var. Ian asks Alison how she knew about the pictures Jeremy was sending him
default ian_alison_fuck = False           # if ian_alison_dating == False (includes ian_alison_sex), Ian fuck Alison and become fuckbuddies, cums inside | Incompatible with ian_cherry_dating
default v11_alison_reject = 0             # if ian_alison_dating == False (includes ian_alison_sex), Ian declines Alison's invitation to have sex (1-don't go to house/2-go to house but don't kiss her)
# cherry
default v11_cherry_art = 0                # Ian's comment on Cherry's art (max 3)
default v11_cherrytalk1 = False           # Tracking var for conversation
default v11_cherrytalk2 = False           # Tracking var for conversation (family)
default v11_cherrytalk3 = False           # Tracking var for conversation (buy a painting)
# jeremy
default v11_ask_jeremy = False            # Ian interrogates Jeremy about the details of his meeting with Gillian
default ivy_jeremy = 0                    # Ian gives his opinion on Jeremy 0= he's an ass / 1= normal / 2= He's cool
default lena_jeremy_dating = False        # tracks ongoing Lena Jeremy relationship (True if v8_jeremy_sex or v10_jeremy_3some)
# ivy
default v11_ivy_openup = False            # Ian tells Ivy about Gillian
default v11_ivy_flirt = False             # Ian acts flirty toward Ivy
# lena
default v11_lena_breakup = False          # Ian stops dating Lena to focus on "cherry" (ian_cherry_dating == True/ ian_lena_dating == False) or "cindy" (ian_cindy_love)
default v11_tell_dream = False            # Cuck Ian tells Lena about his dream with Gillian and another dude
default v11_lena_openup = False           # Ian opens up about Gillian
default v11_lena_bj = False               # Lena blows Ian as part of the sex scene 
default v11_lena_squirt = False           # Ian manages to make Lena squirt
default ian_lena_couple_over = False      # Ian and Lena's serious relationship is broken (v10_ian_left or v11_lena_breakup)

##################################################################################################################################################
## v0.12 variables ###############################################################################################################################
##################################################################################################################################################

## IAN VARIABLES (prologue) ###############

# ian
default ian_summer_look = 0               # Ian's choice of attire for the summer
default v12_gift = "n"                    # Ian buys a necklace for "lena" "holly" "alison" "cindy" "cherry" "minerva"
default v12_fireworks = False             # Ian buys fireworks for the beach trip
# cherry
default v12_cherry_dom = False            # Ian fucks Cherry hard
default v12_cherry_lena = False           # Ian tries to convince Cherry to make amends with Lena
default v12_cherry_painting = False       # Cherry gifts Ian a painting
# cindy
default cindy_ass = False                 # Ian licks Cindy's ass ---> CH13 == 2: Ian fingers Cindy's ass // == 3: Ian tries fucking Cindy's ass
default v12_cindy_cum = False             # Ian covers Cindy with his cum // ch12 == 2 Ian sexts and tells her he wants to cum on her face// CH13== 3: Ian cum on her face
default v12_cindy_rel = 0                 # How Ian handles the conversation w/cindy (ian_cindy_love: 2-set boundaries/1-trust her/0-breakup) /// (ian_cindy_dating: 3-confess love/ 2-play it cool/1-tense/0-breakup)
# alison
default v12_alison_sex = False            # Ian visits Alison to fuck (1-Ian refuses to wear a condom and cums on Alison's tits/2-gives it to Alison) in ian_alison_fuck = 1 came on tits/ 2 came inside
default v12_alison_pics = False           # Ian convinces Alison to take pics during sex /"blonde" indicates Alison was blonde in this scene
default v12_alison_pics_jeremy = False    # Ian shares Alison's pics and vids with Jeremy
default alison_nipple = 0                 # Blonde Alison gets nipples pierced (1-Ian doesn't like it/2-neutral/3-positive)
# minerva
default v12_minerva_sex = False           # Ian fucks minerva in the office
default v12_minerva_help = False          # Ian accepts Minerva's help in the contest
# perry
default v12_bbq = 0                       # Ian agrees to pay for the bbq (2-pays all/1-pays half/0-refuses)
# jessica
default v12_jess_date = False             # Jessica spends the night at Ian's place ---> 1=Ian asked her to leave / 2-slept on the couch/3-slept on the bed/4-had sex --> v12_jess_date == 3 triggers look change
default v12_jess_talk = False             # Ian uses Wits to inquire about Jessica's motives
default ian_jess_love = False             # Ian openly shows interest in Jess
default v12_jess_bj = False               # tracking var for Jess sex scene
default v12_jess_pussy = False            # tracking var for Jess sex scene
default v12_jess_anal = False             # tracking var for Jess sex scene
# lena
default ian_lena_crisis = False           # Jeremy tells Ian about Lena's flings with other guys or with himself ---> CH 12 How Ian adresses the issue: "forgive"/"ignore"/ "open" (resolves in $ ian_lena_open = "ian"/"lena")
# jeremy
default v12_jeremy_crisis = 0             # Ian gets mad at Jeremy  2 == for sleeping with Lena behind his back (ian_jeremy = 0) / 1 == for telling him about ian_lena_crisis (ian_jeremy = 2)
# emma
default v12_emma_dress = 0                # Ian's comment on Emma's summer dress// 0== no comment // 1== ridicule // 2== admire // 3 == hot look

## LENA VARIABLES #########################

#family
default lena_family_help = False          # Lena offers financial help to her family (if seymour_desire == False)
# lena
default v12_guitar_practice = False       # Lena practices with her guitar in her bedroom
# axel
default v12_axel_date = False             # Lena agrees to meet Axel (if lena_axel_desire == 0 Lena can reject the date, in which case lena_axel_desire_over is not activated, but it factors smilarly)
default lena_axel_desire_over = False     # Lena refuses Axel and gets on the train
default lena_axel_fuck = False            # Lena ends up going to Axel's place ---> tracks on-going Axel relationship (not lena_axel_desire)
default v12_car_bj = False                # Lena teases Axel in the car and gives him a bj
default v12_axel_use = False              # Axel uses Lena's throat ("use me" option)

## IAN VARIABLES (summer house) ###########

# ian
default v12_room = 0                      # Who Ian shares room with "lena "holly "emma" "wade"
default v12_ian_weed = False              # Ian smokes weed with Wade and the guys
# gillian
default gillian_stop = False              # Ian gets over Gillian (if v3_gillian_stop == False gets over later in game)
# holly
default holly_slut = False                # Holly gets corrupted (if holly_ivy or holly_guy > 1 or holly_robert)
default v12_holly_weed = False            # Holly tries weed
default v12_holly_sex1 = False            # Ian turns Holly on on arrival and gets a bj
default v12_holly_compliment = False      # Ian comments on Holly's look if holly_fit = True
default v12_holly_sea_sex = False         # Ian fucks Holly in the sea
default v12_confront_holly = False        # if ian_holly_love == False or v12_minerva_sex Ian presses on Holly on what is bothering her
default holly_trinity = 0                 # Holly route (2= Ian wants to be with Holly / 1= Ian is tentative)
default ian_holly_breakup = False         # Ian breaks up with Holly (2 = over lena kiss or 1 = Ian is not ready for relationship)
default v12_holly_sex2 = False            # Ian has sex with Holly during naptime
default v12_holly_sex3 = False            # Final night sex scene
default v12_holly_fuck = 0                # 1 = Ian fucks Holly doggystyle / 2 = naughty Holly also grinds on Ian
default v12_holly_flirt = 0               # Holly Slut makes a move on Ian: 1= reject her / 2= have sex with her
# emma
default v12_emma_sex1 = False             # Ian has morning sex with Emma
default v12_emma_rim = False              # Emma licks Ian's asshole
default v12_emma_creampie = 0             # Ian cums inside Emma's pussy (#of times) max 4?
default v12_emma_anal = 0                 # Ian fucks Emma's ass (#of times)
default v12_emma_topless = 1              # Emma goes topless at the beach (2= Ian encourages her/0= no topless)
default v12_emma_sunscreen = 0            # Ian puts sunscreen on Emma's back (1= quick / 2 = touched butt / 3 = fingered asshole)
default v12_emma_confide = False          # if ian_lena_crisis, Ian tells Emma about his worries
default ian_emma_love = 0                 # Ian corresponds Emma's feelings (2= make love, accept feelings / 1= talk feelings / 0.5= call her out)
default v12_emma_flirt = False            # Ian flirts with Emma if they used to fuck (v12_emma_sex1 == False)
default v12_emma_sex2 = False             # Ian has night sex with Emma (if v12_emma_flirt == True had shorter sex scene)
default v12_emma_sexpics = False          # Ian takes pics of Emma while fucking
default v12_emma_cunni = False            # Ian eats Emma out
default ian_emma_dom = False              # Ian becomes dominant towards Emma in their sexual relationship (face fuck scene) --> (ch13) 3 = spit on hot Emma in bathroom sex scene
default v12_emma_sex3 = False             # Night sex scene, Ian decides to cum in "mouth" "pussy" "ass"
default ian_emma_dating = False           # Tracks relationship path = Ian and Emma have been hooking up for a while (v12_emma_sex3) vs. v12_emma_flirt (hooked up at the beach house)
# cindy
default v12_cindy_text = 0                # 1-Ian texts Cindy when seeing her post / 2- cindy sends pictures / 3- cindy pussy pic / 4- ian dick pic / 5- sexting / 6- pussy pic 
default v12_cindy_text_cum = False        # Ian tells Cindy he wants to cum on her face (if v12_cindy_cum or v12_cindy_text == 6 she sends selfie)
# wade
default v12_wade_trip = 0                 # How Ian reacts to Wade's bad trip (0= stay away / 1= submit him / 2= calm him down)
default v12_wade_talk = False             # Ian looks over Wade and hears his reflection
# perry
default v12_perry_success = 0             # if perry_emma < 1, this score determines Perry's success. max 5-6? required success > 3
default v12_perry_spy = False             # if Perry and Emma hook up, 1= Ian listens/ 2= Ian peeks on them
# lena
default v12_lena_background = False       # Lena talks about her past relationships
default lena_background = "n"             # Background story --- "wits" / "charisma" / "lust"
default v12_lena_sex1 = False             # Ian and Lena have sex on arrival (afternoon) - (only if lena_axel_fuck == False and lena_cheating =! 1)
default v12_lena_facefuck =  False        # ian_lena_dom - Ian fucks Lena's throat
default v12_lena_69 = False               # Ian and Lena get into 69 position
default v12_lena_sex2 = False             # 1st night sex scene (cuck > 1 doesn't acivate the flag), (in lena_axel_fuck 2 == Ian chose to cum)
default v12_lena_squirt = False           # Ian makes Lena squirt (True = made her squirt in second sex scene / 2= made her squirt in final sex scene)
default v12_lena_bikini = 0               # Ian comments on Lena's bikini (3=hot/2=alright/1-too much)
default v12_lena_topless = False          # Lena goes topless at the beach (if lena goes topless, Emma also will)/ 2= ian asked Lena to go topless
default v12_lena_sunscreen = "n"          # "wade" "perry" or "ian" put sunscreen on Lena
default v12_lena_sunscreen_flirt = 0      # Ian fingers Lena (2) or touches her tits (1)
default v12_ian_lena_breakup = False      # Breakup because cheating == 1 or ian_lena_crisis (lena cheating)
default v12_ian_lena_over = False         # Uncommited Ian+Lena stop being friends with benefits
default v12_lena_sex3 = False             # Afternoon sex scene after beach (skippable if ian_lena_crisis)
default v12_lena_smoke = False            # Lena uses the cigarrete during the sex scene
default v12_lena_feet = False             # Lena uses her feet on Ian's cock
default v12_lena_rim = False              # Lena rims Ian's asshole
default v12_ian_rim = False               # Ian rims Lena's asshole
default ian_lena_mmf = 0                  # Ian is on board with a mmf threesome with Lena 1-we could try // 2-I think it's hot
default v12_lena_beach_sex = False        # Lena and Ian have sex at the beach (ian_lena_pure or gillian/crisis)
default v12_lena_rough = 0                # Ian punishes unfaithful Lena with rough sex 1==pussy/2==anal (if ian_lena_crisis == "forgive" or ian_lena_open == "ian"/"lena")
default ian_lena_pure = False             # Ian and Lena have a healthy relationship (ian_lena_couple, lena_crisis == False, gillian_stop, ian_cheating != 2)
# ivy
default v12_ivy_text = 0                  # Ian comments on Ivy's post and texts with her (1-normal convo /2-flirt, teasing pic/3-naked pic)
# louise
default v12_louise_text = 0               # Louise texts Ian (if ian_louise > 4) (tracks second texting flirt event -- 0=Ignored her/1=Apologized/2=Played it cool/3-Told sexy dream --> 4-loved her smile)
default ian_louise_flirt = 0              # Ian flirts with Louise 1-teasing pic / 2- nude pics
# other/never ever game
default v12_moon_text = "n"               # Who Ian texts a picture of the full moon "cindy" "cherry" "alison" "minerva" "louise" "jess"?
default v12_beach_music = 0               # Music Ian plays during have I never ever
default v12_never_ever = False            # The group plays Never HAve I Ever at the beach
default ian_anal = False                  # Ian says he's played with his butt in Never have I ever
default lena_cest = False                 # Lena confesses to having wet dreams about her step-dad
default v12_lena_ons = False              # Lena has had a one night stand in the past (if lena_background == "lust" or lena_jack > 4 or lena_mike_sex and lena_mike_dating == False or v11_mark_sex or v2_robert_home)
default v12_never_ever1 = False
default v12_never_ever2 = False
default v12_never_ever3 = False
default v12_never_ever4 = False
default v12_never_ever5 = False
default v12_never_ever6 = False
default v12_never_ever7 = False
default v12_never_ever8 = False
default v12_never_ever9 = False
#end
default v12_end = "n"                     # /"good" perry+emma-cherry and wade_cindy == 2 /"mid" perry bad, wade_cindy == 2  /"bad" perry good or bad and wade bad /"worst" ian+emma and wade bad

##################################################################################################################################################
## v0.13 variables ###############################################################################################################################
##################################################################################################################################################

## LENA VARIABLES #########################

# seymour
default lena_arthur = 2                   # Lena's friendship stat towards Arthur
default v13_seymour_shoot = 0             # Lena participates in the shoot with Seymour (1-follow his lead//1.5-follow+necktie//2-necktie//3-ask to be touched//4-sit on his lap)
default v13_seymour_show = 0              # Lena masturbates for Seymour (3-caught masturbating//2-caught wearing lingerie//1-asked to wear lingerie)
default v13_seymour_fake = 0              # 2-Lena reaches orgasm//1-Lena fakes it//0-Lena stops (if v13_seymour_show > 0)
default lena_seymour_sex = False          # Lena has sex with Seymour and fully commits to him
# wardrobe
default v13_seymour_lingerie = 0          # The lingerie Lena picks 2=white 1=black
default v13_tat = 0                       # Lena gets a tattoo in chapter 13
default v13_piercing = 0                  # Lena gets a piercing in chapter 13
# bbc
default v13_bbc_dildo = False             # if toy_mandingo == True, Lena uses it
# mark/robert/john (ian)
default v13_guy = 0                       # Lena goes on a date with "mark" "robert" "john"
default v13_toy_dp = False                # Lena DP's herself with a dildo while having anal with Ian/Robert/Mark
default v13_holly_3some = 0               # Lena can have a threesome with "robert" or "mark"
default v13_guybj = False                 # tracking var. for guy sex scene
# ian
default v13_lena_ian_date = False         # Lena goes on a date with Ian (if ian_lena_dating and ian_lena_crisis != "forgive" and holly_trinity !=2 and v13_guy == False and ian_cuck < 2)
default ian_lena_mmf_guy = 0              # if ian_lena_mmf > 0, the guy Lena chooses for the 3some "robert""mike""mark""jeremy""john""wade"
# holly
default v13_glory_holly = False           # Lena watches Holly's video, if holly_slut = True  -- 3== Lena masturbates while watching, 2== keeps watching
default lena_holly_over = False           # if lena_holly_dating, Lena con go back to being friends with Holly (if lena_axel_fuck will make this true automatically)
default v13_lena_holly_sex = 0            # lena_holly_dating sex scene -- 1=normal/2=sixtynine/3=annilingus+sixtynine // trinity route: 1=Lena started without Ian
# trinity
default v13_trinity_naughty = False       # Lena gets naughty and licks Holly's ass
default v13_trinity_bj = False            # Lena sucks Ian's cock during the threesome
# stan
default v13_stalkfap_stan = False         # Stan helps Lena record content for Stalkfap -- if (v10_stalkfap == "stan" and ian_lena_pure == False) or (v4_stan_shoot and v5_shoot > 1 and stan_simp == 4 and ian_lena_pure == False and lena_cheating != 0.5)
default lena_stan_sex = False             # Lena has sex with Stan during their movie date --> romantic path
default v13_stan_cunni = False            # Romantic path, Lena has Stan eat her out
default lena_stan_love = False            # Romantic path, Lena makes love to Stan
default v13_stan_reward = 0               # Stalkfap path, Lena reward Stan for his services as cameraman 1==fondle boobs//2==stroke cock//3==blowjob/cum in mouth
default v13_stan_sex = False              # Stalkfap path ---> Lena fucks Stan
default v13_stan_pay = False              # Stalkfap Lena asks Stan to pay her for sexual services (if v13_stan_reward > 1)
# jeremy
default v13_jeremy_sex = False            # Lena has a date with Jeremy (lena_jeremy_dating) 2==film blowjob//3==rimjob
default v13_jeremy_deep = 0               # How Lena fucks Jeremy 2==deep//1==faster
# emma
default emma_seymour = False              # Lena convices Emma to meet Seymour
default v13_emma_date = False             # if v11_emma_date, Lena has another date with Emma (2== sleepover)
default lena_emma_sex = False             # Lena hooks up with Emma (not possible if ---> holly_trinity == 2 or lena_holly_dating or (ian_lena_pure and ian_lena_3some == False) or (ian_lena_couple and ian_lena_open != "lena" and ian_lena_open != "ian"))
default ian_lena_emma_3some = 0           # Lena sets up a threesome (if ian_lena_pure) --> (can become True in later chapters in non-pure relationship) ---> Ian's reaction: 2=enthusiastic/1=bothered
default v13_emma_talk = 0                 # Lena uses dirty talk (2) or soft talk (1) in her sex scene with Emma
default v13_emma_double = False           # Lena uses the double dildo with Emma
default v13_emma_wand = False             # Lena uses the vibrating wand with Emma
# stalkfap_pro = "late"                   # if cafe_help == False Lena enters stalkfap desperate for money. (prior states can be: stalkfap_pro == 1/ stalkfap== True/False)

## IAN  VARIABLES #########################
# ian
default ian_promotion = 0                 # needs > 2 points to get promoted working at Hierofant
# jeremy
default v13_jeremy_fight = False          # Ian confronts Jeremy at the gym (v12_jeremy_crisis)
# perry/wade
default v13_ask_wade = False              # Ian asks Perry about Wade's status (tracking var. could become useful later)
default perry_enemy = 0                   # Perry becomes hostile towards Ian (if ian_emma_dating or v12_emma_flirt) ---> 1=normal/2=blame perry/3=make fun of perry
# louise
default v13_louise_date = False           # Ian meets Louise at her flat to watch a movie
default v13_louise_cunni = False          # Ian eats Louise out before she sucks him off
default ian_louise_love = 0               # 1=Ian fucks Louise romantically/2= Ian seals the scene with a heartfelt kiss
default ian_louise_use = False            # Ian shelfisly uses Louise for his pleasure (blowjob)
default v13_louise_choke = False          # Ian fucks Louise hard
default v13_louise_sex = False            # tracking var. for the scene (Ian makes love to Louise) "quickshot" == Ian cums fast and ends the scene (+ v13_louise_cum == 2: Ian used Louise's mouth to cum and left)
default v13_louise_athletics = False      # tracking var. for the scene (Ian picks up Louise in the air)
default v13_louise_anal = False           # tracking var. for the scene (Ian Ian does anal with Louise)
default v13_louise_cum = 0                # Ian cums on Louise's face (2) ir inside (1)
default v13_louise_pose = "n"             # tracks phases of sex scene
default v13_louise_pic = 0                # Ian takes a picture of Louise after having sex --- 2= cumshot face closeup / 1= in sofa
default v13_louise_stay = False           # Ian without plans (cindy/minerva) or ian_louise_love can stay the night with Louise after the sex scene
default ian_louise_dating = 0             # Ian agrees to start dating Louise (2=real relationship/1.5=lie to her/1=take it slowly)
default ian_louise_over = 0               # Ian rejects Louise interest in following up their relationship (1=ignored call/2=talked on the phone)
# minerva
default v13_minerva_date = False          # Ian meets Minerva in the hotel ("wednesday" or "thursday" if v13_cindy_date)
default v13_minerva_anal = False          # Ian cums in Minerva's ass
default minerva_petname = "n"             # Ian gives a petname to Minerva "mommy"or "minnie"
# ivy
default ivy_permission = 0                # Ian gives permission to Lena to fool around with Ivy 1=block/2=accept/3=threesome--- if v10_ivy_sex/v11_shower_sex and ian_lena_open != "ian" and ian_lena_open != "lena" and lena_cheating != 2 and holly_trinity < 2 and ian_cuck < 2 and lena_seymour_sex == False
# cindy
default v13_cindy_antagonize = 0          # Ian comes across Axel+Cindy (1=antagonize Axel/2=antagonize Cindy/0.5=made friendly conversation)
default v13_cindy_date = False            # Ian and Cindy meet after her modeling trip
default v13_cindy_polaroids = False       # Ian sorts through Cindy's polaroids
default v13_cindy_bj = False              # Cindy sucks Ian's cock
# cindy_ass                               # 2= finger ass // 3= ask for anal
# v12_cindy_cum                           # 3= cum on face // 4= fuck with cum on face
# ian_cindy_over = 3/4                    # breakup w Cindy during date (3)/ after sex (4)
# emma
default emma_hot = False                  # Emma gets her hottest look (if v12_emma_dress == 3 and ian_emma_dating or v12_emma_flirt) 2 == encourage to be even hotter / 0.5 == discourage, revert to emma_hot = False later
default v13_emma_sex = False              # if emma_hot or ian_emma_dom Emma texts Ian to fuck in the bathroom
# alison
default v13_alison_fuck = False           # Ian accepts Alison's booty call ---> 1=came in mouth/2=came insisde/3=came together
default v13_alison_condom = False         # Ian uses a condom in Alison dating sex scene
default v13_alison_pullout = False        # Ian has sex without a condom but pulls out as promised/otherwise, Alison gets mad
# gillian
default v13_gillian_date = False          # Ian agrees to go on a café date with Gillian (if v11_gillian_talk > 1)
# holly slut
default v13_holly_night = 0               # slut Holly visits Ian at his place at night (1-agree right away/2-ask question)
default v13_holly_slut_sex = False        # Ian accepts Holly's advences and has sex with her (for the second time, v12_holly_flirt == 2)/(for the first time, v12_holly_flirt == 0)
default v13_holly_pic = False             # slut Holly takes a selfie with Ian's cock
default v13_holly_abuse = False           # Ian forced slut Holly to deepthroat him
default v13_holly_anal = 0                # Ian fucks slut Holly's ass (1-tease/2-fuck)
default v13_holly_hard = False            # Ian gives it to Holly hard
# victor
default v13_victor_confide = False        # Ian tells Victor about getting the first prize from Seymour ($ seymour_desire == True)

##################################################################################################################################################
## v0.1 variables TWO SIDES OF THE COIN ##########################################################################################################
##################################################################################################################################################
label master_script:

    if chapter == 0:

        $ chapter = 1

        $ save_name = "Ian: Chapter 1"
        $ prologueover = True

        jump chapter_one

##################################################################################################################################################
## v0.2 variables ################################################################################################################################
##################################################################################################################################################

    if chapter == 1:
        $ chapter = 2
        $ emma_tattoo = True

        jump chapter_two

##################################################################################################################################################
## v0.3 variables ################################################################################################################################
##################################################################################################################################################

    if chapter == 2:

        $ chapter = 3

## LENA VARIABLES #########################

        if v2_robert_bj:
            $ lena_bj += 1
        if v2_robert_swallow:
            $ lena_bj += 1
    # song
        if song_1a == "real":
            $ lena_song1 += 2
        if song_1a == "precise":
            $ lena_song1 += 1
        if song_1b == "tragedy":
            $ lena_song1 += 2
        if song_1b == "story":
            $ lena_song1 += 1
        if song_1c == "abyss":
            $ lena_song1 += 2
        if song_1c == "kingdom":
            $ lena_song1 += 1

    # Jeremy
        if v2_alison_home:
            $ alison_jeremy = False

        jump chapter_three

##################################################################################################################################################
## v0.4 variables ################################################################################################################################
##################################################################################################################################################

    if chapter == 3:

        $ chapter = 4

## LENA VARIABLES #########################

    # Seymour photo shoot
        if v3_seymour_date == False:
            $ v3_seymour_reject = True

        if v3_seymour_date and v3_seymour_reject == False:
            $ v4_seymour_date = True

        jump chapter_four

##################################################################################################################################################
## v0.5 variables ################################################################################################################################
##################################################################################################################################################
    
    if chapter == 4:

        $ chapter = 5


        jump chapter_five


##################################################################################################################################################
## v0.6 variables ################################################################################################################################
##################################################################################################################################################

    if chapter == 5:
        $ chapter = 6

    # Jessica
        if lena_mike_sex or lena_robert_sex:
            $ jess_bad = True

        jump chapter_six

##################################################################################################################################################
## v0.7 Variables ################################################################################################################################
##################################################################################################################################################
    if chapter == 6:
        $ chapter = 7

        if outfit_bunny or v6_robert_date:
            $ lena_wardrobe_bunny = True
        if lena_bbc:
            $ lena_fty_bbc = True

        jump chapter_seven

##################################################################################################################################################
## v0.8 Variables ################################################################################################################################
##################################################################################################################################################
    if chapter == 7:
        $ chapter = 8

## LENA VARIABLES #########################

    # clothes
        if v4_seymour_date:
            $ lena_wardrobe_lingerie = True

        jump chapter_eight

##################################################################################################################################################
## v0.9 Variables ################################################################################################################################
##################################################################################################################################################
    
    if chapter == 8:
        $ chapter = 9

## IAN VARIABLES ##########################

    # Lena
        if ian_lena_over:
            $ ian_lena_dating = False
        if lena_louise_sex:
            $ lena_fty_lesbo = True


        jump chapter_nine

##################################################################################################################################################
## v0.10 Variables ###############################################################################################################################
##################################################################################################################################################

    if chapter == 9:
        $ chapter = 10

        if ian_lena_couple:
            $ ian_lena_love = True

        jump chapter_ten


##################################################################################################################################################
## v0.11 Variables ###############################################################################################################################
##################################################################################################################################################

    if chapter == 10:
        $ chapter = 11

    # REDUNDANCY FOR SAVES
        # lena_cheating:
        if (v10_wc_bj == "mark" and ian_lena_couple and ian_lena_breakup == False) or (v10_wc_bj == "mike" and ian_lena_couple and ian_lena_breakup == False):
            $ lena_cheating = True

    # PROLOGUE - STATUS UPDATE
        # lena lesbian
        if v10_ivy_sex:
            $ lena_fty_lesbo = True

        # ian x lena breakup
        if v10_ian_left or ian_lena_breakup:
            $ ian_lena_dating = False

        # lena_ian_love
        if lena_ian_love:
            $ lena_ian_love = 1

        # ian cheating
        if (ian_lena_couple and ian_lena_breakup == False and ian_cindy_dating) or (ian_lena_couple and ian_lena_breakup == False and ian_minerva_dating):
            $ ian_cheating = True

        # mike
        if (lena_mike_over and v10_stalkfap == "mike") or (lena_mike_over and v10_mike_sex) or (lena_mike_over and v10_wc_bj == "mike"):
            $ lena_mike_over = False
        if lena_mike_over:
            $ lena_mike_dating = False

        # ian cindy perry lie
        if v9_cindy_lie == 3:
            $ v10_perry_lie += 1
        elif v9_cindy_lie == 2 and v10_perry_lie < 2:
            $ v10_perry_lie += 1
        elif v9_cindy_lie == 0 and ian_cindy_dating and v10_perry_lie > 0:
            $ v10_perry_lie -= 1

        # holly's look
        if holly_change > 3:
            $ v11_holly_change = True

    # AFTER PROLOGUE - STATUS UPDATE
        # alison blonde
        if alison_jeremy_3some == 2 or ian_alison_dom or ian_alison_dating == False:
            $ alison_blonde = 1

        # kickboxing (ch 10 fix); BM: moved before Ch 11 so it's easier to master the skill
        if v10_sparring > 1 and kickboxing < 5: #kickboxing = 5 max (ch 10 max 4 + 1)
            $ kickboxing += 1

        jump chapter_eleven

##################################################################################################################################################
## v0.12 Variables ###############################################################################################################################
##################################################################################################################################################

    if chapter == 11:
        $ chapter = 12
        
        if v11_shower_sex:
            $ lena_fty_lesbo = True
        # ian fitness
        if ian_athletics > 5:
            $ ian_fit = 1
        # alison fuck (3some path) fix
        if ian_alison_dating == False and ian_alison_breakup == False and ian_cherry_dating == False and alison_jeremy_3some > 1 and v11_alison_reject == 0:
            $ ian_alison_fuck = True
        # alison anal
        if v9_alison_trip == False:
            $ v9askanalalison = False
        # holly
        if holly_change > 3 or holly_ivy:
            $ v11_holly_change = True
        # axel
        if ian_lena_couple and lena_axel_desire > 0:
            $ lena_cheating = True
        if holly_ivy and lena_go_holly > 3:
            $ lena_go_holly = 3
        
        jump chapter_twelve

##################################################################################################################################################
## v0.13 Variables ###############################################################################################################################
##################################################################################################################################################
    if chapter == 12:
        $ chapter = 13

        # lena crisis open relationship 
        if ian_lena_open == "ian" or ian_lena_open == "lena":
            $ ian_lena_crisis = "open"
        if v12_ian_lena_breakup and lena_cheating == 0.5:
            $ lena_cheating = 0

        jump chapter_thirteen
 
##################################################################################################################################################
## END ###########################################################################################################################################
##################################################################################################################################################

    jump end

##################################################################################################################################################################################################################
## V9 CINDY SHOOT ##################################################################################################################################################################################################################
##################################################################################################################################################################################################################

label v9cindydate:
    stop music fadeout 2.0
# LENA DATE RECAP/ clothes change
    # got back from Alison trip
    if v9_alison_trip:
        # cindy love
        if v7_cindy_kiss:
            scene street with long
            "I headed home to take a quick shower and change before meeting Cindy."
            scene ianroom with long
            if ian_lena_couple:
                $ fian = "happy"
                "I wasn't able to stop smiling as I walked down the street..."
            elif ian_lena_dating:
                if ian_lena_love:
                    $ fian = "n"
                    "When I arrived home I still wasn't sure how to feel."
                else:
                    $ fian = "smile"
                    "That conversation with Lena had lifted a load off my shoulders."
            elif ian_lena_breakup:
                $ fian = "sad"
                "I was feeling so drained when I got there."
            else:
                if ian_lena_makeup:
                    $ fian = "n"
                    "I couldn't get a strange feeling out of my head after speaking with Lena today."
                elif ian_lena_over:
                    $ fian = "sad"
                    "I was trying to keep those thoughts away from my mind, but couldn't stop thinking about Lena."
                else:
                    $ fian = "n"
                    "It was nice hanging out with Lena again. I still wasn't sure how I managed to befriend her, but..."
            show ian with short
        # no cindy love
        else:
            scene street with long
            "I headed straight to the meeting point."
            show ian with short
            if ian_lena_couple:
                $ fian = "happy"
                "I wasn't able to stop smiling as I walked down the street."
            elif ian_lena_dating:
                if ian_lena_love:
                    $ fian = "n"
                    "I wasn't sure how to feel after our conversation. So much to process..."
                else:
                    $ fian = "smile"
                    "That conversation with Lena had lifted a load off my shoulders."
            elif ian_lena_breakup:
                $ fian = "sad"
                "I was feeling so drained while I walked down the street."
            else:
                if ian_lena_makeup:
                    $ fian = "n"
                    "I couldn't get a strange feeling out of my head after speaking with Lena today."
                elif ian_lena_over:
                    $ fian = "sad"
                    "I was trying to keep those thoughts away from my mind, but couldn't stop thinking about Lena."
                else:
                    $ fian = "n"
                    "It was nice hanging out with Lena again. I still wasn't sure how I managed to befriend her, but..."
        #common
        if ian_lena_couple:
            i "She said yes... I guess we are officially dating now..."
            if v9_lena_sex > 2:
                i "And what we did at the park was wild! I can't believe that actually happened..."
            elif v9_lena_sex == 1:
                i "And what we did at the park was wild... I made her cum right there in the open."
            else:
                "Too bad I didn't get the chance to have sex with her today..."
            "It had been a very long year since I had broken up with Gillian, and only recently things had started to get better..."
            if v5_ian_showup:
                "And not only had Lena corresponded to my feelings, but Hierofant was hiring me after all!"
            if v7_cindy_kiss:
                $ fian = "n"
                i "I asked Lena to be with me, but..."
                "What about Cindy? I was hoping to sort things out this afternoon, but I had no idea how I would do that."
                i "I need to stop tangling things up..."
            else:
                i "I can't remember the last time I felt this hyped."
                i "I'm even starting to feel confident about my chances of winning the book contest!"
        elif ian_lena_dating:
            if ian_lena_love:
                "Lena had turned down my feelings... But she also offered us to keep hooking up."
                "I had accepted, of course. I didn't want to lose her, and being her fuckbuddy was better than nothing... Much better."
                i "I wonder if my feelings for her will get in the way, though..."
            else:
                "I was afraid she wouldn't be okay with us openly being just friends with benefits, but she was on board with the idea."
                i "This makes things a lot easier..."
            if v9_lena_sex > 2:
                i "And what we did at the park was wild! I can't believe that actually happened..."
            elif v9_lena_sex == 1:
                i "And what we did at the park was wild... I made her cum right there in the open."
            else:
                "Too bad I didn't get the chance to have sex with her today..."
            "It had been a very long year since I had broken up with Gillian, and only recently things were starting to get better..."
            if v5_ian_showup:
                $ fian = "smile"
                "But Lena wasn't the only good thing. Hierofant was hiring me after all!"
            if v7_cindy_kiss:
                $ fian = "n"
                i "Now I have to sort things out with Cindy..."
            else:
                i "I'd say things are looking good so far..."
                i "I'm even starting to feel confident about my chances of winning the book contest!"
        elif ian_lena_breakup:
            "Lena turned down my feelings. I should've seen that coming."
            i "I probably already knew this would be her answer... But I had to be honest with myself."
            i "This is for the best... I wouldn't have been happy with what she was offering me."
            i "But this means I've lost my chance with her... Fuck..."
            if v5_ian_showup:
                $ fian = "n"
                "At least Hierofant had decided to hire me after all. Those were really good news."
            if v7_cindy_kiss:
                $ fian = "n"
                i "Now I have to deal with Cindy. This is my chance to sort things out..."
            else:
                i "I need to focus on winning that contest now."
        else:
            if ian_lena_makeup:
                i "We talked again about what happened with Holly... But this time I felt something was different."
                i "Maybe I finally regained her trust? I don't know..."
                $ fian = "smile"
                i "But it felt just right."
            elif ian_lena_over:
                "Going back to being just friends was being as hard as I had anticipated. I couldn't help but notice how attractive she was..."
                if ian_lena_love:
                    "And not just physically. I really liked her... A lot."
                else:
                    i "It's so damn hard not to like her."
                "I took a deep breath."
                $ fian = "n"
                i "It is what it is. I need to accept that, hard as it is."
            else:
                $ fian = "smile"
                "I was glad I did. She was a really cool girl."
            "It had been a very long year since I had broken up with Gillian, and only recently things had started to get better."
            "Having Lena in my life was part of that..."
            if v5_ian_showup:
                $ fian = "smile"
                "But also the fact Hierofant was hiring me after all! That had been some great news."
            if v7_cindy_kiss:
                $ fian = "n"
                i "Now I have to deal with Cindy. This is my chance to sort things out..."
            else:
                i "I need to focus on winning that contest now."
        # clothes
        if v7_cindy_kiss:
            "I was more nervous than I was willing to admit, even to myself. I had been obsessing over Cindy for weeks now..."
            "I took a deep breath."
            i "No point in overthinking things now. Let's see what happens."
            hide ian with short
            call screen v9ianclotheschoice2
            stop music fadeout 2.0
            show ian with long
            if ian_look == "wits1":
                $ v9_ianwearing = "wits"
                i "Wearing this seems appropriate."
            elif ian_look == "charisma1":
                $ v9_ianwearing = "charisma"
                i "I'll wear this. I want to make a good impression."
            elif ian_look == "athletics1":
                $ v9_ianwearing = "athletics"
                i "This is comfortable and it makes me feel kind of confident."
            elif ian_look == "lust1":
                $ v9_ianwearing = "lust"
                i "I'll wear this... I want to look good."
            else:
                $ v9_ianwearing = "n"
                i "Like this is okay."
            show ian at truecenter with move
            i "Let's go..."
            scene street with long
            show ian at lef with short
        else:
            $ fian = "n"
            show ian at lef with move
    else:
        scene street with long
        $ fian = "n"
        show ian at lef with short
## DATE START
    play music "music/cindys_theme.mp3" loop
    $ cindy_look = 2
    $ axel_look = 1
    $ faxel = "smile"
    $ fcindy = "n"
    "Cindy was already waiting for me at the meeting point."
    if v5_cindy_shoot:
        i "Weird... This place is quite far away from the studio we went to last time."
    show cindy at rig with short
    # cindy hookup
    if v7_cindy_kiss:
        i "Hey."
        c "Hi..."
        menu:
            "We need to talk":
                $ renpy.block_rollback()
                "I decided to jump straight into the fray."
                i "Cindy, we need to talk. It's long overdue."
                if ian_charisma < 9:
                    call xp_up('charisma') from _call_xp_up_407
                $ fcindy = "serious"
                c "Again with that?"
                $ fian = "serious"
                i "What do you mean, \"again with that\"?"
                if ian_cindy > 0:
                    call friend_xp('cindy', -1) from _call_friend_xp_549
                i "You want us to act like nothing ever happened? If that's it, okay, but we need to discuss it at least once."

            "How are you doing?":
                $ renpy.block_rollback()
                "I decided to take a calculated approach."
                i "How are you doing?"
                c "Fine."
                i "Any news? We didn't get the chance to speak much last Wednesday."
                c "Nothing much. Same old same old."
                i "..."
                c "..."
                "Fuck. She was avoiding the issue altogether."
                if ian_wits < 9:
                    call xp_up('wits') from _call_xp_up_408
                i "Cindy..."
                c "What?"
                i "You know what. We need to talk."

            "...":
                $ renpy.block_rollback()
                i "..."
                c "... ..."
                i "... ... ..."
                c "... ... ... ..."
                $ fian = "worried"
                i "Well, aren't you gonna say something?"
                c "Me? Why don't you say something?"
                $ fian = "n"
                i "Alright. You know we need to talk..."
                $ fcindy = "serious"

        $ fcindy = "serious"
        c "Ugh... You won't give up, will you?"
        $ fian = "n"
        i "I'd like to straighten things out. Know where we stand..."
        $ fcindy = "sad"
        if ian_cindy_sex:
            c "Do we really need to make such a big deal out of this?"
            $ fcindy = "blush"
            c "We made a stupid mistake, just once. That's all..."
        else:
            $ fcindy = "sad"
            c "Why are you making such a big deal out of this? It was just a stupid kiss..."
        show ian at lef3
        show cindy at rig3
        with move
        show axel with short
        x "Hey there, guys."
        $ fian = "n"
        $ fcindy = "n"
        hide cindy
        show cindy2 at rig3
        with short
        c "Hi...!"
        if axel_knows_dating:
            $ faxel = "n"
            "It was barely noticeable, but Axel's smile dimmed a bit when he laid eyes upon me."
            $ faxel = "smile"
            "He quickly recomposed his radiant expression."
        if v3_cindy_date:
            x "What's up, Ian? I didn't know you were coming today too."
            i "Yeah, I'm tagging along again, if you don't mind."
            x "Of course. Glad to have you as an audience."
        else:
            x "Hey there. Ian, if I remember correctly, right?"
            i "Yeah."
            if axel_knows_dating == False:
                "I shook Axel's hand. He had a very strong and confident grip."
                x "Nice to meet you again."
                "His radiant smile had no hints of that tense vibe he projected last time when he learned Jeremy and I were involved with Lena."
            x "So, are you coming with us today?"
            i "Yeah, I'll be tagging along, if you don't mind."
            x "Alright. I wasn't expecting to work with an audience, but it's no bother."
        x "Let's go, shall we?"
        jump v9cindyaxelhome
    # wade mission
    else:
        if v5_cindy_shoot or ian_cindy_model:
            if ian_cindy < 5:
                $ fcindy = "serious"
                c "So here you are..."
                i "Yup."
                c "I can't believe Wade asked you to babysit me... This is embarrassing."
                if v5_cindy_shoot:
                    i "It's not like this is the first time I come with you to a photo shoot. You asked me for support that one time..."
                else:
                    i "Well, you already showed me quite a few pictures from your shoots. It shouldn't be {i}that{/i} embarrassing..."
                c "This is different. I didn't invite you, your presence is Wade's imposition."
            else:
                c "Here you are."
                i "Hey."
                $ fcindy = "serious"
                c "I can't believe Wade asked you to babysit me... This is embarrassing."
                if v5_cindy_shoot:
                    i "It's not like this is the first time I come with you to a photo shoot. You asked me for support that one time..."
                    $ fcindy = "sad"
                    c "Yeah, I was nervous with it being my first time..."
                    $ fcindy = "n"
                    c "But now it's different. I'm not sure I'm comfortable with you being here..."
                else:
                    i "Well, you already showed me quite a few pictures from your shoots. It shouldn't be {i}that{/i} embarrassing..."
                    c "This is completely different! I'm not sure I'm comfortable with you being here..."
                $ fian = "sad"
                i "What's changed?"
                $ fcindy = "serious"
                c "I didn't invite you, your presence is Wade's imposition."
        else:
            $ fcindy = "serious"
            c "So here you are..."
            i "Yup."
            c "I can't believe Wade asked you to babysit me... This is embarrassing."
            i "It kinda is... But he asked me for help, so..."
            c "Help with what? Keeping me under surveillance?"
            c "I don't know who you guys think you are, but...!"
            $ fian = "worried"
            i "Hey, it's not my fault! I'm just trying to help you guys..."
            c "I don't remember asking for help. Your presence is Wade's imposition."
        c "Well, here you are. You did your duty as a friend."
        c "You can leave now."
        $ fian = "worried"
        i "You want me to leave? And what am I supposed to tell Wade?"
        c "Tell him whatever you want. I don't care."
        menu:
            "I'm not leaving":
                $ renpy.block_rollback()
                $ fian = "n"
                i "I can't do that..."
                c "Yes, you can."
                i "Look, I know this is between you and Wade, and me getting involved in this is probably a bad idea..."
                c "It is."
                if v5_cindy_shoot or ian_cindy_model:
                    i "But I've been involved in this from the start. It was me who encouraged you to try this modeling thing."
                    $ fcindy = "sad"
                    i "At first, you asked me to support you and I did. Now it's Wade who's asking me for support..."
                    i "I can't speak for him, but I'm not here to police your behavior. I just want to make things easier for my friend and ease the tension between you guys."
                else:
                    i "Still, it is a worse idea to kick me out. Do you think it will ease the tension between you and Wade?"
                i "But if you're really against me being here, well... I can't impose my presence, can I?"
                if wade_cindy == 2 or ian_cindy > 7:
                    $ fcindy = "sad"
                    c "..."
                    c "This is absurd... I don't like it."
                    i "I don't like it either, but what would you have me do? How do you think Wade would react?"
                    $ fcindy = "serious"
                    c "Fuck it. Tag along if you must."
                    if ian_charisma < 9:
                        call xp_up('charisma') from _call_xp_up_409
                    c "At least this way Wade won't complain..."
                else:
                    $ v9_cindy_shoot = 1
                    $ fcindy = "serious"
                    c "No, you can't. I don't want you here, so please leave."
                    $ fian = "sad"
                    i "Are you sure about...?"
                    $ fcindy = "mad"
                    c "Yes, I am! I've asked you three times now, so will you leave already?"
                    call friend_xp('cindy', -1) from _call_friend_xp_550
                    $ ian_cindy = 0

            "Walk away":
                $ renpy.block_rollback()
                $ v9_cindy_shoot = 1
                $ fian = "n"
                i "Look, I don't know what's going on between you and Wade... I shouldn't have gotten involved in the first place."
                c "That's right."
                $ fian = "serious"
                i "There's no need to be this rude, you know? I'm just trying to help."
                c "I already told you I didn't ask for your help!"
                i "I know, that's why I'm not coming, since you don't want me there. But Wade won't be happy."
                if ian_wits < 9:
                    call xp_up('wits') from _call_xp_up_410
                c "That's something I'll deal with myself."

        show ian at lef3
        show cindy at rig3
        with move
        show axel with short
        x "Hey there guys."
        $ fian = "n"
        $ fcindy = "n"
        hide cindy
        show cindy2 at rig3
        with short
        c "Hi...!"
        if axel_knows_dating:
            $ faxel = "n"
            "It was barely noticeable, but Axel's smile dimmed a bit when he laid eyes upon me."
            $ faxel = "smile"
            "He quickly recomposed his radiant expression."
        if v3_cindy_date:
            x "What's up, Ian? I didn't know you were coming today too."
        else:
            x "Hey there. Ian, if I remember correctly, right?"
            i "Yeah."
            if axel_knows_dating == False:
                "I shook Axel's hand. He had a very strong and confident grip."
                x "Nice to meet you again."
                "His radiant smile had no hints of that tense vibe he projected last time when he learned Jeremy and I were involved with Lena."
            x "So, are you coming with us today?"
        if v9_cindy_shoot == 1:
            $ fcindy = "serious"
            c "He's not. He was just leaving. Right, Ian?"
            i "So it seems, yeah..."
            show ian at left with move
            $ faxel = "n"
            show axel at centerlef with move
            x "Hey, before you go..."
            $ fcindy = "sad"
            # findme apology
            if axel_knows_dating:
                $ faxel = "sad"
                x "I wanted to apologize for being kinda rude that night. I was having an odd day."
                i "Nah... It's okay."
                x "I guess you know about my history with Lena and how, well... How I fucked up."
                i "I've heard something about it, yeah."
                x "Then you can imagine this is not exactly... easy for me."
                x "Lena's an incredible girl and well... Things didn't end up too nicely between us."
                x "Lots of unresolved issues, especially on my part."
                $ fian = "worried"
                "I wasn't expecting Axel to apologize, and even less to open up. I wasn't sure what to say."
                $ fian = "n"
                if ian_chad > 3:
                    i "I don't know what to tell you, man. I just don't want to create trouble."
                else:
                    i "I guess I can see where you're coming from... I just don't want to create trouble."
                $ faxel = "smile"
                x "Me neither."
                call friend_xp('axel', 3) from _call_friend_xp_551
                x "Well then, see you around. And again, sorry if I acted like a jackass."
            else:
                $ faxel = "sad"
                x "Sorry if I acted a bit weird the other night. I was having an odd day and learning you were all Lena's friends, it threw me off..."
                i "I understand."
                x "I'm not sure if you're aware of my history with her, but it's kinda... difficult."
                i "I know just enough. But as I said, we're just friends."
                $ faxel = "smile"
                x "I know, that's why I wanted to apologize for my reaction."
                call friend_xp('axel', 3) from _call_friend_xp_552
                x "Well then, see you some other time."
            $ faxel = "happy"
            x "Let's go, Cindy."
            hide axel
            hide cindy2
            with short
            i "..."
            $ fian = "sad"
            i "I wasted my time coming here... And Wade won't like this."
            scene street with long
            "I headed back home to get some more writing done."
            jump v9iansaturdaywade
        else:
            i "Yeah."
            if v5_cindy_shoot:
                x "Alright. Glad to have you as an audience again."
            else:
                x "Alright. I wasn't expecting to work with an audience today, but it's no bother."
            x "This way!"
            jump v9cindyaxelhome

## ARRIVE TO AXEL'S HOUSE
label gallery_CH09_S06:
    if _in_replay:
        call setup_CH09_S06 from _call_setup_CH09_S06

label v9cindyaxelhome:
    stop music fadeout 2.0
    scene street with long
    if v5_cindy_shoot:
        "We followed Axel into an apartment block. This definitely was a different place than the one we went to last time..."
    else:
        "We followed Axel into an apartment block."
    scene axelhome with long
    $ fian = "n"
    $ fcindy = "n"
    $ faxel = "smile"
    play sound "sfx/door_home.mp3"
    "We took the elevator and entered a luminous and spacious loft."
    show ian at lef3
    show axel
    show cindy2 at rig3
    with short
    x "Welcome to my humble abode."
    $ fcindy = "smile"
    $ fian = "worried"
    c "Wow, this is your place? It's awesome!"
    c "I'd kill to have an apartment like this in this city...!"
    x "It's not so big, but more than enough for a single guy like me."
    $ fian = "n"
    if v5_cindy_shoot:
        i "I thought we were going to the same studio as last time."
        x "I wanted to use natural light for today's shoot, and here's perfect for that."
    else:
        i "I thought we were going to a studio..."
    x "No point in renting a studio when I have a better one at home!"
    if axel_knows_dating:
        "Finding myself at Axel's place had me on edge. I felt like I had been invited to step into enemy territory, under some sort of truce."
        "Or, in the worst-case scenario, I was being lured into an ambush..."
    elif ian_lena_dating or ian_lena_over:
        "Finding myself at Axel's place had me on edge. It was like I had just stepped into enemy territory, and I was undercover."
        "If he knew I had been dating Lena, I doubt he would've welcomed me in."
    else:
        "Finding myself at Axel's place had me on edge. For some reason, I felt like I had just stepped into enemy territory."
    $ fcindy = "n"
    hide cindy2
    show cindy at rig3
    with short
    c "So what will we be doing today? I wasn't sure what clothes to bring..."
    x "Don't worry about that. We won't be using anything today."
    c "Anything?"
    x "I want to shoot you at natural, no atrezzo, no overdone makeup... Just your skin and daylight."
    $ fcindy = "blush"
    $ fian = "worried"
    c "Alright..."
    menu:
        "You want her completely naked?":
            $ renpy.block_rollback()
            $ fian = "serious"
            i "You want her to pose completely naked?"
            $ faxel = "happy"
            x "Is there a problem with that?"
            $ fcindy = "serious"
            c "No, there's not."
            if ian_cindy > 0:
                call friend_xp('cindy', -1) from _call_friend_xp_553
            if v5_cindy_nude == 2:
                x "I mean, it's not like this would be her first time, or have you forgotten?"
            elif v5_cindy_nude == 1:
                x "Well, we already dipped our toes in those waters, didn't we?"

        "You're comfortable with it, Cindy?":
            $ renpy.block_rollback()
            i "Are you comfortable with that, Cindy?"
            $ faxel = "happy"
            x "Why wouldn't she be?"
            if v5_cindy_nude == 2:
                x "It's not like this would be her first time, or have you forgotten?"
                $ fian = "n"
                i "The scenario is quite different, though."
            elif v5_cindy_nude == 1:
                x "We already dipped our toes in those waters, didn't we?"
                $ fian = "n"
                i "Going topless is different than posing fully nude."
            else:
                $ fian = "n"
                i "Not everyone is okay with posing completely naked in front of a camera..."
            $ fcindy = "n"
            c "I have no problem with that."
            $ fian = "worried"
            x "See? You don't need to worry."

        "Keep quiet":
            $ renpy.block_rollback()
            "I decided to keep my mouth shut for the time being."

    $ faxel = "smile"
    x "You've been doing great in our shoots so far, Cindy. I'm impressed at how much promise you're showing, really."
    $ fcindy = "shy"
    $ fian = "n"
    hide cindy
    show cindy2 at rig3
    with short
    c "I'm still a bit clueless..."
    x "That's why I thought the best thing would be for you to work with just your body."
    x "What separates a professional model from an amateur one is her attitude in front of the camera."
    x "If you're able to relax, connect with your emotions and project them, your modeling will improve leaps and bounds."
    x "You need to feel comfortable in your own skin, express yourself effortlessly in front of the camera..."
    x "And the best way to learn those fundamentals is to get everything out of the way: just you, light, and the camera."
    c "Alright. So should I strip...?"
    x "You can do it in the bathroom if you feel more comfortable. I know it can be kind of an intimate moment for some models."
    c "Yeah, I guess I'll do that. Be right back."
    hide cindy2 with short
    show ian at lef
    show axel at rig
    with move
    i "..."
    # findme
    if axel_knows_dating:
        $ faxel = "n"
        x "By the way..."
        $ faxel = "sad"
        x "I wanted to apologize for being kinda rude that night. I was having an odd day."
        $ fian = "sad"
        i "Nah... It's okay."
        x "I guess you know about my history with Lena and how, well... How I fucked up."
        i "I've heard something about it, yeah."
        x "Then you can imagine this is not exactly... easy for me."
        x "Lena's an incredible girl and well... Things didn't end up too nicely between us."
        x "Lots of unresolved issues, especially on my part."
        $ fian = "worried"
        "I wasn't expecting Axel to apologize, and even less to open up. I wasn't sure what to say."
        $ fian = "n"
        if ian_chad > 3:
            i "I don't know what to tell you, man. I just don't want to create trouble."
        else:
            i "I guess I can see where you're coming from... I just don't want to create trouble."
        $ faxel = "smile"
        x "Me neither."
        if v5_cindy_shoot:
            i "It felt wrong not to let you know, especially since we already knew each other."
        else:
            i "I know we don't really know each other, but it felt wrong not letting you know."
        i "I'm not trying to hide."
        $ faxel = "smile"
        x "I can respect that."
        call friend_xp('axel', 3) from _call_friend_xp_554
        x "Anyway, sorry for acting like a jerk. I was having an odd day and it threw me off."
    else:
        x "Do you want something to drink?"
        if ian_chad > 3:
            i "I'm fine."
        else:
            i "No, I'm fine. Thanks."
        $ faxel = "n"
        x "By the way..."
        $ faxel = "sad"
        x "Sorry if I acted a bit weird the other night. I was having an odd day and learning you were all Lena's friends, it threw me off..."
        i "I understand."
        x "I'm not sure if you're aware of my history with her, but it's kinda... difficult."
        i "I know just enough. But as I said, we're just friends."
        $ faxel = "smile"
        x "I know, that's why I wanted to apologize for my reaction. I was a bit of a jerk."
        call friend_xp('axel', 3) from _call_friend_xp_555
    i "No problem."
    x "So..."
    if v5_cindy_shoot:
        x "Cindy must really trust you if she asked you to come with her... again."
    else:
        x "Cindy must really trust you if she asked you to come with her."
    i "I guess so."
    x "Well, she brought you instead of her boyfriend. That's something to think about."
    i "It's... complicated."
    x "Complicated, huh? How?"
    i "I don't think it's my place to talk about it..."
    x "You're right. Gossiping is not nice."

## SHOOT STARTS
    $ fcindy = "blush"
    show ian at lef3
    show axel at rig3
    with move
    show cindynude2 with short
    if v5_cindy_shoot or ian_cindy_model:
        $ fian = "blush"
    else:
        $ fian = "surprise"
    c "Okay, I'm ready..."
    # kiss or sex or saw her nude before
    if v5_cindy_nude == 2 or v7_cindy_pics == 2 or v7_cindy_kiss:
        i "..."
        c "..."
        "Cindy's eyes and mine met for a second, and we both looked away, blushing."
        $ faxel = "n"
        x "..."
        x "Are you sure you're comfortable, Cindy?"
        c "Um, it's kind of awkward..."
        $ fian = "worried"
        "Axel looked at me with a serious expression."
        x "Ian, I can't have you make the model uncomfortable, otherwise, she won't be able to work right..."
        if v5_cindy_shoot:
            x "I thought you knew from last time."
        else:
            x "I suppose you understand."
        x "Don't get this the wrong way, but maybe it would be better if you left."
        $ fian = "disgusted"
        i "What...?"
        $ timeout = 4
        $ timeout_label = "v9cantleave"
        $ renpy.block_rollback()
        menu:
            "{image=icon_wits.webp}/{image=icon_charisma.webp}She has to {i}learn{/i} to be comfortable" if ian_wits > 5 or ian_charisma > 5:
                $ renpy.block_rollback()
                jump v9convinceaxel

            "I can't leave":
                $ renpy.block_rollback()
                label v9cantleave:
                    "I had to come up with an excuse why I couldn't leave...!"
                if v7_cindy_kiss:
                    "The real reason was that I was waiting for my chance to talk to Cindy... But I couldn't say that."
                    "Instead, I blurted out something I shouldn't."
                $ fian = "serious"
                i "I'm sorry, but I can't leave. I promised Wade I would keep an eye on..."
                $ fcindy = "mad"
                c "Oh, screw Wade, and screw you! I'm not Wade's property and you're not my babysitter!"
                call friend_xp('cindy', -1) from _call_friend_xp_556
                $ ian_cindy = 0
                jump v9axelkickout

            "I won't disturb you":
                $ renpy.block_rollback()
                $ fian = "n"
                "I tried to keep a straight face, difficult as it was."
                i "I'm sorry. I'm not used to this kind of environment and I made it awkward."
                i "I'll sit quietly in a corner and you'll barely notice me. I won't disturb you."
                if v7_cindy_kiss or ian_cindy > 7:
                    c "Alright."
                else:
                    c "I don't know about that..."
                    x "Don't feel forced to say yes, Cindy. Choose what would make you feel more comfortable, that's the only important thing right now."
                    c "That's true..."
                    c "I think it'd be better if you left, Ian."
                    jump v9axelkickout

    # no dating
    else:
        i "...!" with vpunch
        "I didn't know where to look. Cindy was standing in front of me, completely naked...!"
        if v5_cindy_nude == 1:
            "I had seen her topless last time, but this..."
        elif ian_cindy_model:
            "I had seen her sexy pictures, but this..."
        else:
            "I had never even seen Cindy in her underwear, much less looking like that..."
        "It felt so damn awkward, wrong, and tempting at the same time..."
        $ fcindy = "serious"
        c "Stop looking at me like that!"
        $ faxel = "n"
        i "Wha--{w=0.3}{nw}"
        $ fian = "blush"
        i "No, I..."
        $ fcindy = "blush"
        x "Ian, I can't have you make the model uncomfortable, otherwise, she won't be able to work right..."
        if v5_cindy_shoot:
            x "I thought you knew from last time."
        else:
            x "I suppose you understand."
        i "I wasn't...!"
        x "Don't get this the wrong way, but maybe it would be better if you left."
        $ timeout = 4
        $ timeout_label = "v9wasntme2"
        $ renpy.block_rollback()
        menu:
            "I can't leave":
                $ renpy.block_rollback()
                if ian_chad < 5:
                    $ ian_chad += 1
                $ fian = "n"
                i "I'm sorry, but I can't leave."
                x "Why is that?"
                i "I promised Wade I would keep an eye on..."
                $ fcindy = "mad"
                c "Get the hell out of here. I'm not Wade's property and you're not my babysitter!"
                call friend_xp('cindy', -1) from _call_friend_xp_557
                $ ian_cindy = 0
                label v9axelkickout:
                    $ timeout_label = None
                $ v9_cindy_shoot = 2
                if wade_cindy > 0:
                    $ wade_cindy -= 1
                x "I'm sorry, but you've heard the lady."
                $ fian = "worried"
                i "..."
                x "Don't make me ask twice."
                call friend_xp('axel', -1) from _call_friend_xp_558
                $ ian_axel = 2
                "Fuck, what was I going to do? Fight him?"
                "That wouldn't solve shit. And I was already deep enough in it."
                $ fian = "serious"
                i "Alright, as you wish. It's your home, after all."
                i "Very conveniently I might add."
                c "Will you leave already?"
                i "Sure."
                hide ian with short
                "Fuck, that had been bad... Had I made things worse by talking too much?"
                scene street with long
                "I headed back home, trying to get that bitter sensation out of my mouth."
                $ renpy.end_replay()
                jump v9iansaturdaywade

            "It surprised me":
                $ renpy.block_rollback()
                $ fian = "n"
                "I tried to keep a straight face, difficult as it was."
                i "I'm sorry, it just... surprised me. I'm not used to this kind of environment."
                c "Me neither... Maybe it's just me, but it felt kinda awkward."
                x "Well, the idea for today's shoot is for you to be relaxed..."

            "I wasn't looking!":
                $ renpy.block_rollback()
                label v9wasntme2:
                    i "I--{w=0.3}I wasn't looking...!"
                $ fcindy = "serious"
                c "Yes, you were!"
                x "If you think you'll feel more comfortable without him..."
                $ fcindy = "blush"
                c "Yeah... I think that'd be best."
                $ fian = "worried"
                i "But I promised Wade I would keep an eye on...!"
                $ fcindy = "mad"
                c "Oh, screw Wade, and screw you! I'm not Wade's property and you're not my babysitter!"
                call friend_xp('cindy', -1) from _call_friend_xp_559
                $ ian_cindy = 0
                jump v9axelkickout

            "Leave":
                $ renpy.block_rollback()
                if ian_chad > 0:
                    $ ian_chad -= 1
                i "Yes, you're right. This was a bad idea."
                if ian_cindy > 7:
                    $ fcindy = "blush"
                    c "Wait... Maybe it's just me. It just felt so awkward."
                    x "Are you sure? The idea for today's shoot is for you to be relaxed..."
                else:
                    $ v9_cindy_shoot = 2
                    $ fcindy = "serious"
                    c "You don't say?"
                    x "I'm sorry, but you've heard the lady."
                    i "No, sorry for bothering you. I'm leaving."
                    hide ian with short
                    "Fuck, that had been incredibly embarrassing and awkward!"
                    scene street with long
                    "I headed back home, trying to get that bitter sensation out of my mouth."
                    $ renpy.end_replay()
                    jump v9iansaturdaywade

        x "Are you sure it is a good idea for him to stay?"
        $ timeout_label = "v9cantleave2"
        $ renpy.block_rollback()
        menu:
            "{image=icon_wits.webp}/{image=icon_charisma.webp}She has to {i}learn{/i} to be comfortable" if ian_wits > 5 or ian_charisma > 5:
                $ renpy.block_rollback()
                label v9convinceaxel:
                    $ fian = "n"
                i "Well, you said it yourself: she has to {i}learn{/i} to be comfortable. Me being here might help."
                c "That makes sense. I want to try it."
                x "You can work on it step by step, I'm not expecting you to act like a pro from the get-go..."
                $ fcindy = "serious"
                c "No, I'm serious about this. I need to push myself and get out of my comfort zone."
                hide cindynude2
                show cindynude
                with short
                x "Alright..."
                $ fian = "smile"
                $ fcindy = "n"

            "I can't leave":
                $ renpy.block_rollback()
                label v9cantleave2:
                    if ian_chad < 5:
                        $ ian_chad += 1
                $ fian = "n"
                i "I'm sorry, but I can't leave."
                x "Why is that?"
                i "Wade asked me to keep an eye on..."
                $ fcindy = "mad"
                c "Oh, screw Wade, and screw you! I'm not Wade's property and you're not my babysitter!"
                call friend_xp('cindy', -1) from _call_friend_xp_560
                $ ian_cindy = 0
                $ fcindy = "serious"
                c "Axel's right, it'll be better if you leave."
                jump v9axelkickout

            "I won't disturb you":
                $ renpy.block_rollback()
                $ fian = "sad"
                i "I will sit quietly in a corner and you'll barely notice me. I won't disturb you."
                if v5_cindy_shoot and ian_cindy > 5:
                    c "Alright."
                else:
                    c "I don't know about that..."
                    x "Don't feel forced to say yes, Cindy. Choose what would make you feel more comfortable, that's the only important thing right now."
                    c "That's true..."
                    c "I think it'd be better if you left, Ian."
                    jump v9axelkickout

    $ timeout_label = "v9axelsilence"
    $ faxel = "smile"
    x "Good. Now, if that's solved, let's get to it."
    x "Let's move to the couch, Cindy."
## POSING STARTS
    stop music fadeout 2.0
    play music "music/flirty2.mp3" loop
    scene v9_cindy_shoot1c
    show v9_cindy_shoot1a
    with long
    c "Here?"
    "Cindy improvised a pose, standing with one knee on the couch, and Axel knelt in front of her, looking for a good angle."
    x "Yeah, that's good."
    play sound "sfx/camera.mp3"
    with flash
    if v7_cindy_kiss:
        hide v9_cindy_shoot1a
        show v9_cindy_shoot1b
        with short
        "Cindy looked up in my direction."
        "I was staring from one of the corners of the room, sitting on a stool."
        "Her emerald eyes met mine for a couple of quick seconds, and then she lowered her gaze again."
        hide v9_cindy_shoot1b
        show v9_cindy_shoot1a
        with short
    x "Relax, Cindy. I want you to look up at the camera."
    hide v9_cindy_shoot1a with short
    x "There, just like that."
    x "Forget about Ian being here. It's just you and me."
    x "You and the camera."
    play sound "sfx/camera.mp3"
    scene v9_cindy_shoot1d with flash
    "Axel's words seemed to encourage Cindy."
    "Her cheeks were tinted with red, but her eyes stared down the barrel of the camera without wavering."
    x "Perfect... You look so beautiful under this light."
    "She did, indeed."
    if v5_cindy_nude == 2 or v7_cindy_pics == 2:
        if ian_cindy_sex:
            "It was not my first time seeing Wade's girlfriend naked. I had done much more than that, in fact."
            "She was unbearably gorgeous..."
        elif v5_cindy_nude == 2:
            "It was not my first time seeing Wade's girlfriend naked. And what a spectacle that was..."
        else:
            "It was not my first time seeing Wade's girlfriend naked. But not like this..."
    elif v5_cindy_nude == 1:
        if ian_cindy_sex:
            "I had seen Wade's girlfriend topless before. I had done much more than that, in fact."
            "But this was my first time seeing her fully naked like that."
            "She was unbearably gorgeous..."
        else:
            "I had seen Wade's girlfriend topless before. But this was an even bigger spectacle..."
    else:
        "I felt so weird staring at Cindy's completely naked body. She was Wade's girl and he was not even here..."
        "It was impossible not to appreciate how damn gorgeous Cindy was. Even more than I had imagined."
        "And that made me feel guilty for not wanting to avert my eyes..."
    scene v9_cindy_shoot2a with long
    x "Your hair looks incredible. Let me take some close-up shots."
    play sound "sfx/camera.mp3"
    with flash
    x "Keep looking at the camera. Lower your chin a bit..."
    scene v9_cindy_shoot2b with long
    x "Here, tilt your head to the right, like this..."
    $ renpy.block_rollback()
    menu:
        "No touching!":
            $ renpy.block_rollback()
            $ v9_axel_warn = 1
            $ fian = "serious"
            $ faxel = "smile"
            $ fcindy = "blush"
            scene axelhome
            show ian at lef3
            show cindynude
            show axel at rig3
            with short
            i "Hey! No need to touch her, right?"
            if ian_charisma < 9:
                call xp_up('charisma') from _call_xp_up_411
            "Axel turned his head and looked at me with a casual smile."
            x "Wow, you're even more protective than her boyfriend, aren't you?"
            $ fcindy = "serious"
            c "Stay quiet, Ian! If you're not gonna help, at least don't disturb us!"
            $ fian = "worried"

            if ian_axel > 1:
                call friend_xp('axel', -1) from _call_friend_xp_561
                $ ian_axel = 1
                pause 1

            if ian_cindy > 1:
                call friend_xp('cindy', -2) from _call_friend_xp_562
            elif ian_cindy > 0:
                call friend_xp('cindy', -1) from _call_friend_xp_563

            x "Let's get back to it."
            $ fcindy = "n"
            c "Yes."

        "Keep quiet":
            $ renpy.block_rollback()
            label v9axelsilence:
                i "..."
            "Axel slid his hand over Cindy's cheek, brushing aside her golden hair."
            c "Like this...?"
            x "That's perfect."
            "Axel's thumb briefly brushed Cindy's lips when he pulled his hand back."
            scene v9_cindy_shoot2a with long
            play sound "sfx/camera.mp3"
            with flash
            x "These are looking amazing..."

    x "I want you lying down on the sofa now."
    scene v9_cindy_shoot3 with long
    x "Great! Try raising that knee up a bit more... And twist your back slightly."
    x "Perfect! You're a natural..."
    play sound "sfx/camera.mp3"
    with flash
    x "It keeps surprising me how talented you are!"
    "Axel's compliments seemed to be easing Cindy's initial awkwardness pretty effectively."
    "She looked more comfortable posing in front of the camera and her eyes expressed confidence."
    "I had no idea if Cindy was talented or not, but her beauty was as bright as the rays of light that entered through the windows."
    if ian_cindy_sex:
        "To think I had had sex with her..."
        "But now... I had no idea what was going to happen."
    elif v7_cindy_kiss:
        "To think I had kissed her..."
        "But now... I had no idea what was going to happen."
    else:
        "Trying to remain blind to it was impossible."
    scene v9_cindy_shoot4 with long
    play sound "sfx/camera.mp3"
    with flash
    "The photo shoot went on for almost one hour."
    "Cindy tried a variety of poses on Axel's couch, following his indications and requests."
    "She really seemed to have forgotten I was in the room... She looked totally focused on the shoot."
    "Axel continued to shower her with encouraging and flattering words. He knew what buttons to push, like a true professional..."
    "To think Lena had been dating this guy..."
    if ian_lena_couple:
        "I couldn't help but dislike the way he conducted himself. He was... too charming."
        "Did he talk to Lena like he was talking to Cindy now? What kind of relationship did they really have?"
        "I felt I had very little in common with Axel, yet Lena had chosen to date me now."
        "Was Lena mentally comparing me to him when we were together...?"
    elif ian_lena_dating:
        "I couldn't help but dislike the way he conducted himself. He was... too charming."
        "Did he talk to Lena like he was talking to Cindy now?"
        "Was Lena mentally comparing me to him when we were together...?"
    elif ian_lena_breakup:
        "I couldn't help but dislike the way he conducted himself. Not to speak about how he had hurt her, cheating with Cherry..."
        "Yet this guy had earned Lena's love at some point, while I hadn't been able to."
    else:
        "This guy was Lena's ex... The one who had cheated on her with Cherry."
        "I couldn't help but dislike the way he conducted himself."
        "He was... too charming."
    if v7_cindy_kiss:
        "And now he was getting close to Cindy. I couldn't help but feel a big, looming threat..."
    else:
        "And now he was getting close to Cindy. I couldn't help but feel Wade's relationship was in real danger..."
    "After a while, I started to get lost in my thoughts. So many things crossing my mind...!"
    play sound "sfx/camera.mp3"
    scene v9_cindy_shoot5
    show v9_cindy_shoot5_axel1
    with flash
    "When I looked up again I was met by a striking view."
    "Cindy was leaning on the sofa, her back turned to me, and that pose...!"
    "Axel's back was blocking the view. I could only imagine what he could be seeing at that moment!"
    x "Great pose. Now look back at me."
    scene v9_cindy_shoot5b
    show v9_cindy_shoot5c
    show v9_cindy_shoot5_axel1
    with long
    x "Good."
    play sound "sfx/camera.mp3"
    with flash
    if v7_cindy_kiss:
        hide v9_cindy_shoot5c with short
        "Cindy's eyes escaped the pull of the lens and wandered to the corner I was sitting in... looking for me."
        "Our gazes met again, and I saw Cindy's cheeks flush with intense red... But she didn't look away."
        "Not immediately."
        x "Focus on the camera, Cindy."
        scene v9_cindy_shoot5c
        show v9_cindy_shoot5_axel1
        with short
        c "Yes, sorry."
        play sound "sfx/camera.mp3"
        with flash
    if v9_axel_warn == False:
        $ timeout_label = "v9axelsilence2"
        x "Wait, don't move."
        if v7_cindy_kiss:
            scene v9_cindy_shoot5b
        else:
            scene v9_cindy_shoot5
        with long
        i "...!" with vpunch
        "Axel stood up, clearing my view completely...!"
        "My eyes were drawn to Cindy's crotch like metal coins to a magnet. There was no way of escaping its pull."
        if v7_cindy_kiss:
            "Then I realized Cindy was looking at me again."
            if ian_cindy_sex:
                "Flashbacks of that night rushed through my head. The night I had been inside her..."
            else:
                "I had kissed Cindy, once... But now I wanted to do so much more...!"
        else:
            "Thank God she was not paying any attention to me, or else I would've been caught on the spot..."
            "Staring at Wade's girlfriend's privates."
        show v9_cindy_shoot5_axel2 with long
        x "I need you to move this leg a bit further..."
        $ renpy.block_rollback()
        menu:
            "No touching!":
                $ renpy.block_rollback()
                $ v9_axel_warn = 2
                $ fian = "serious"
                $ faxel = "n"
                $ fcindy = "blush"
                stop music fadeout 2.0
                i "Hey, hey!"
                scene axelhome
                show ian at lef3
                show cindynude2
                show axel at rig3
                with short
                i "No need to touch her, right?"
                if ian_charisma < 9:
                    call xp_up('charisma') from _call_xp_up_412
                $ faxel = "smile"
                "Axel turned his head and looked at me with a casual smile."
                x "Wow, you're even more protective than her boyfriend, aren't you?"
                i "No. It's called simple professionalism."
                $ faxel = "n"
                call friend_xp('axel', -1) from _call_friend_xp_564
                $ ian_axel = 0
                pause 0.5
                $ faxel = "smile"
                x "Don't worry, I wasn't trying to do anything {i}dangerous{/i}."
                $ faxel = "happy"
                x "And even if I had those kinds of intentions I wouldn't dare to, having you here to protect her!"
                $ fian = "mad"
                "Was this guy shitting on me?"
                $ faxel = "smile"
                if lena_axel_dating:
                    x "We're already done, anyway. I have another shoot lined up just now."
                else:
                    x "We're already done, anyway. We have plenty of good shots already."

            "Keep quiet":
                $ renpy.block_rollback()
                label v9axelsilence2:
                    if wade_cindy > 0:
                        $ wade_cindy -= 1
                if v7_cindy_kiss:
                    scene v9_cindy_shoot5
                    show v9_cindy_shoot5_axel2
                    with long
                "Axel ran his hand up Cindy's thigh, getting dangerously close to her pussy."
                x "Just slightly. There..."
                if ian_lust < 9:
                    call xp_up('lust') from _call_xp_up_413
                "How the hell did he have the nerve to...?"
                hide v9_cindy_shoot5_axel2 with long
                "But Cindy didn't even utter a word of complaint."
                "Was she okay with Axel touching her like that...?"
                show v9_cindy_shoot5_axel1 with long
                play sound "sfx/camera.mp3"
                with flash
                "Axel took a few more pictures and finally decided to call an end to the photo shoot."
                jump v9cindyshootend
    else:
        if v7_cindy_kiss:
            if ian_cindy_sex:
                "Flashbacks of that night rushed through my head. The night I had been inside her..."
            else:
                "I had just kissed Cindy, but it was nearly impossible not wanting to do so much more...!"
        else:
            "Thank God she was not paying any attention to me, or else I would've been caught on the spot..."
            "Trying to get a glimpse of Wade's girlfriend's privates."

        "Axel took a couple more pictures. Could he really see Cindy's pussy from that angle?"
        "Before I could decide if I should call him out on that, he decided to call an end to the photo shoot."
        label v9cindyshootend:
            $ fian = "blush"
            $ faxel = "smile"
            if v7_cindy_kiss:
                $ fcindy = "blush"
            else:
                $ fcindy = "shy"
            stop music fadeout 2.0
            scene axelhome
            show ian at lef3
            show cindynude2
            show axel at rig3
            with short
            x "Alright, I think we're done here!"
            if lena_axel_dating:
                x "I'm sorry it was so short, but I have another shoot lined up just now."
##END SHOOTING
    x "How did you feel today, Cindy?"
    $ fian = "worried"
    $ fcindy = "shy"
    hide cindynude2
    show cindynude
    with short
    c "I think I did okay... Better than the other times, at least."
    x "Yeah, you did. You're improving so fast..."
    x "You're really talented!"
    c "So you've been saying."
    $ faxel = "happy"
    x "So maybe it's time you started to believe me."
    x "I'm sure one of the big agencies I work with would hire you, no doubt."
    x "You just need to build a slightly bigger portfolio and they will fight to sign you."
    c "You really think so!?" with vpunch
    $ fcindy = "blush"
    c "..."
    if v7_cindy_kiss:
        "Cindy looked at me and it seemed like she suddenly became self-conscious again."
    else:
        "Cindy's eyes met mine and she seemed to finally remember I was in the room, too."
        i "..."
    c "I should go get dressed."
    $ faxel = "smile"
    hide cindynude with short
    show axel at right with move
    $ fian = "serious"
    "I looked at Axel while he checked the pictures he had taken on his camera."
    if v5_cindy_shoot:
        "His vibe had been different during the first photo shoot. I had already had a bad feeling about him, but now I was pretty sure..."
    else:
        "I had already had a bad feeling about him, but now I was pretty sure..."
    if v7_cindy_kiss:
        "He was a real threat when it came to Cindy."
    else:
        "He was a real threat to Wade and Cindy's relationship."
    "It was obvious he was hitting on her. It was only a matter of time for him to make a move..."
    "And considering how things were between Wade and Cindy, well..."
    "He had a good chance at getting what he was after."
    if v7_cindy_kiss:
        $ fian = "worried"
        "Then again... Wasn't I after the same thing?"
        "I couldn't help but feel both Axel and I were just a pair of wolves circling around Cindy..."
    else:
        $ fian = "sad"
        "That would ultimately depend on Cindy, though..."
    $ fcindy = "n"
    show cindy2 with short
    c "Ready."
    $ fian = "n"
    x "Come look at some of these pictures."
    $ fcindy = "smile"
    show cindy2 at rig3 with move
    c "Sure, show me!"
    x "Look at this one. It's amazing."
    $ fcindy = "shy"
    c "Oh, wow! This might be my favorite so far!"
    $ faxel = "happy"
    x "That's because you haven't seen this one yet..."
    "I stood there while they browsed the pictures on the camera, shoulder to shoulder."
    if v7_cindy_kiss:
        "All I wanted was for us to get out of here so I could finally fucking speak to Cindy...!"
    else:
        "I doubted Wade would've liked to see that. Or any part of the shoot itself."
        "I had to get Cindy out of here..."
    # lena appears
    if lena_axel_dating:
        $ flena = "worried"
        if v6_axel_pose == 3:
            $ lena_look = "sexy"
        elif v6_axel_pose == 2:
            $ lena_look = 4
        else:
            $ lena_look = 1
        play sound "sfx/doorbell.mp3"
        $ fcindy = "n"
        x "Oh, she's early..."
        c "Who's that?"
        $ faxel = "smile"
        x "The other model I'm working with today."
        show cindy2 at lef
        show ian at left
        with move
        "Axel went to get the door."
        play sound "sfx/door.mp3"
        hide cindy2
        show cindy at lef
        show lena2 at rig
        with long
        l "Hi..."
        $ fian = "worried"
        $ fcindy = "surprise"
        i "Lena."
        $ flena = "surprise"
        l "Wha--?"
        l "What are you guys doing here?"
        x "I just finished shooting with Cindy. Do you know each other?"
        $ fcindy = "serious"
        $ flena = "worried"
        c "We met. Once."
        if axel_knows_dating or v4_ian_date == False:
            $ faxel = "n"
            x "And of course, you have Ian as a mutual friend. Small world."
            $ faxel = "smile"
            x "Anyway, they were just leaving. Right, guys?"
        else:
            x "Really? How come?"
            $ fcindy = "n"
            c "Ian and Lena are pretty well {i}acquainted{/i}."
            $ faxel = "sad"
            "The way Cindy said that last word made Axel's face change for a second."
            $ faxel = "n"
            "He looked at me."
            x "I had no idea. How {i}acquainted{/i} are you, exactly?"
            call friend_xp('axel', -1) from _call_friend_xp_565
            $ ian_axel = 0
            $ flena = "serious"
            "Lena made as if to turn around and leave."
            l "I think it'll be better if I come back another time."
            $ faxel = "n"
            x "No, wait..."
            $ faxel = "smile"
            x "They were just leaving. Right, guys?"
            $ flena = "sad"
        $ fian = "n"
        $ fcindy = "sad"
        i "Yeah."
        c "Send me the pictures when you have them, okay?"
        x "Sure, I will."
        hide cindy with short
        show ian at lef with move
        $ fian = "sad"
        i "Is everything okay...?"
        $ flena = "sad"
        l "Yeah... I needed some help finding work as a model and Ivy insisted I contacted Axel."
        if ian_lena_couple:
            i "Are you sure this is a good idea?"
            $ flena = "n"
            l "It'll be fine, don't worry."
            i "Alright. Call me if you need anything, okay?"
            $ flena = "smile"
            l "I will. Thanks, Ian."
        elif ian_lena_dating:
            i "Are you sure this is a good idea?"
            $ flena = "n"
            l "It'll be fine, don't worry..."
            i "Alright. Let's talk soon, okay?"
            l "I will. Thanks, Ian."
        else:
            i "I see. Will you be fine?"
            $ flena = "n"
            l "Yes, you don't have to worry."
            i "Alright. Take care."
            l "Thanks, Ian."
        $ fian = "worried"
        $ fcindy = "serious"
    # no lena
    else:
        i "Shall we get going, or...?"
        $ fcindy = "serious"
        $ faxel = "smile"
        c "What's the rush?"
        x "I guess it must've been boring waiting while we were shooting. Of course, you want to leave, ha ha."
        x "Go ahead, I'll sit down to edit the pictures so I can send you the best one as soon as possible."
        $ fcindy = "smile"
        c "Great! Thank you so much, Axel!"
        x "It's my pleasure. I'll text you soon."
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH09_S06")
# CINDY CONVERSATION
    $ timeout_label = None
    scene street_afternoon with long
    show ian at lef
    show cindy at rig
    with short
    play music "music/calm.mp3" loop
    if lena_axel_dating:
        "Cindy was waiting for me down in the street."
        c "Are you done?"
        i "Yeah."
        "I looked back at the building. Lena was alone with Axel in his apartment..."
        "It was surprising, especially after what I had learned about their relationship."
        if ian_lena_couple:
            "Should I be worried? She just agreed to be my girlfriend, yet she was posing for her ex again..."
            "I didn't like that. I didn't like it one bit."
            call friend_xp('lena', -2) from _call_friend_xp_567
        elif ian_lena_dating:
            "It didn't make me feel exactly comfortable. Lena wasn't my girlfriend or anything, but still..."
            "I didn't trust Axel one bit."
        else:
            if ian_lena_breakup or ian_lena_over:
                "It made me kinda worried. Could this be the reason Lena turned me down?"
                "She hadn't mentioned anything... and that was what made me iffy."
            else:
                "I wondered if Lena would really be okay..."
        c "Ian, are you listening to me?"
        $ fian = "n"
        i "Uh, yeah..."
        if v7_cindy_kiss:
            "I had some business to attend to myself."
        $ fcindy = "n"
        c "Come on, let's go."
    else:
        "I followed Cindy outside."
        if v7_cindy_kiss:
            "Would I finally be able to have a proper talk with her?"
        c "Let's go."
    "I walked alongside her since we were going in the same direction."
    c "Maybe it's not for me to say, but I think the pictures we took today looked fantastic."
    c "I can't wait to see the edited versions... I never knew someone could make me look like that!"
    i "..."
    c "Axel says I could make it as a model. Can you imagine?"
    c "It sure beats working nine-to-five at my Dad's business..."
    c "I've never been talented at anything, not really. What if this is it?"
    c "What if this is what I'm supposed to do?"
    i "Is it what you want?"
    # talk wade, end date
    if v7_cindy_kiss == False:
        c "Yeah, I think it is. What is there to lose?"
        i "Wade."
        $ fcindy = "sad"
        c "..."
        $ fcindy = "serious"
        c "What are you insinuating?"
        i "Look, I know I've gotten way too involved as it is, and your relationship is not really my business..."
        c "It is not."
        menu:
            "{image=icon_wits.webp}Do you really want to be with Wade?" if ian_wits > 5:
                $ renpy.block_rollback()
                if wade_cindy < 2:
                    $ wade_cindy += 1
                $ fian = "sad"
                i "Cindy... Do you really want to be with Wade?"
                $ fcindy = "sad"
                c "I do..."
                if wade_cindy == 2:
                    c "I'm trying to, but it's not easy."
                else:
                    c "At least that's what I think..."
                i "What do you mean?"
                c "It's like... I don't know, my life isn't going the way I expected it to go."
                c "I feel trapped... And Wade, well, he seems to feel way too comfortable in this trap."
                c "I feel I can't count on him to move forward. All he wants to do is stay where he is, but I need to grow..."
                $ fian = "n"
                i "Then why don't you tell him? The same exact way you just told me."
                c "..."
                c "I had never been able to verbalize it this way. Maybe he will get the message if I explain it like this..."
                i "You have to at least try. You owe it to him and to yourself."
                if ian_cindy < 12:
                    call friend_xp('cindy', 1) from _call_friend_xp_568
                c "You're right..."

            "Defend Wade":
                $ renpy.block_rollback()
                $ fian = "serious"
                i "Still, Wade's my friend and I'm worried for him. I know he's having a hard time..."
                c "And what about me? Who worries about me having a hard time?"
                $ fian = "n"
                i "Are you?"
                c "Ugh, you never listen, do you? None of you guys do."
                if ian_cindy > 0:
                    call friend_xp('cindy', -1) from _call_friend_xp_569
                i "I know Wade cares."
                c "Yeah? And where's he? If he really cared it'd be nice for him to show it!"
                i "He does, in his own way. I know he tries to..."
                $ fcindy = "sad"
                c "Well, he's not doing such a good job."

            "Stay out of it":
                $ renpy.block_rollback()
                if wade_cindy > 1:
                    call friend_xp('cindy', -1) from _call_friend_xp_570
                $ fian = "serious"
                i "Fine, I've already talked too much."
                $ fian = "n"
                i "Deal with this as you see fit... But you can't blame me for being worried about Wade."
                c "And who worries about me, huh? Nobody."
                c "I have to take care of myself."
                i "Whatever you say..."

        stop music fadeout 2.0
        scene street_afternoon with long
        "Cindy and I went our separate ways as we continued heading home."
        jump v9iansaturdaywade
    # cindy ian talk
    label gallery_CH09_S07:
        if _in_replay:
            call setup_CH09_S07 from _call_setup_CH09_S07

    $ fcindy = "blush"
    "Cindy stopped walking."
    hide cindy
    show cindy2 at rig
    with short
    c "..."
    $ fian = "sad"
    i "Cindy?"
    c "I don't know. I don't know what I want."
    c "Why is everything so complicated?"
    $ fian = "n"
    i "Usually, it's because we make it complicated. I would know..."
    i "That's why I think we need to untangle this mess and talk about what happened."
    stop music fadeout 2.0
    "For the first time, Cindy didn't shut me off right away. This was my chance."
    i "I would like to know how you feel about... that night."
    if ian_cindy_sex:
        c "Guilty. Embarrassed... and also dirty."
        $ fian = "worried"
        c "I feel awful."
    else:
        c "I feel... stupid and embarrassed. And also guilty."
        $ fian = "worried"
    "Not a single one of those words was positive."
    menu:
        "{image=icon_love.webp}It meant something to me" if ian_lena_couple == False:
            $ renpy.block_rollback()
            $ ian_cindy_love = True
            $ fian = "sad"
            i "I understand why you'd feel that way. I'm not exactly proud of myself, either..."
            i "But I need to say this: it meant something to me."
            if ian_cindy_sex:
                i "What happened that night was crazy, I know. Not even in my wildest dreams would I have thought that I..."
                i "But that's the thing. I had been dreaming about it. About you."
            else:
                i "When I kissed you... I had been thinking about it for so long..."
                i "I know I shouldn't have. But it was all I wanted to do."
            i "I know it's wrong. And I feel guilty about it too."
            i "A part of me thinks it'd be better if we just pretended this never happened. Try to forget about it."
            c "Yeah..."
            i "But I can't. I don't want to forget about it."
            c "Ian..."
            if ian_cindy < 11:
                call friend_xp('cindy', 2) from _call_friend_xp_571
            elif ian_cindy < 12:
                call friend_xp('cindy', 1) from _call_friend_xp_572
            jump v9cindykeeptalking

        "I feel guilty too":
            $ renpy.block_rollback()
            $ fian = "sad"
            i "I feel guilty, too."
            i "I'm not exactly proud of myself... and I don't know what to do now."
            c "What is there to do? I think the best thing we can do is just forget about it ever happening."
            i "Is that what you really want?"
            c "It's what I've been trying to do..."
            if ian_cindy_sex:
                c "We went too far. Way too far."
                c "I want to blame the alcohol, but... It was so wrong."
            else:
                c "It was just a stupid kiss. We were just too drunk..."
            if ian_lust < 5 and ian_will > 0:
                menu:
                    "{image=icon_will.webp}I don't want to forget about it":
                        $ renpy.block_rollback()
                        i "I'm sorry, but I can't. I don't want to forget about it."
                        call willdown from _call_willdown_12
                        jump v9cindykeeptalking

                    "You're right":
                        $ renpy.block_rollback()
                        jump v9cindystoptalking
            else:
                menu:
                    "{image=icon_lust.webp}I don't want to forget about it" if ian_lust > 4:
                        $ renpy.block_rollback()
                        i "I'm sorry, but I can't. I don't want to forget about it."
                        label v9cindykeeptalking:
                            c "I, uh..."
                        c "..."
                        c "I don't feel comfortable talking about this in the middle of the street."
                        $ fian = "sad"
                        i "Do you want to go somewhere else? Maybe the park...?"
                        c "My place is around the corner... Maybe we can talk there."
                        $ fian = "n"
                        "I gulped."
                        i "Sounds good to me."

                    "You're right":
                        $ renpy.block_rollback()
                        label v9cindystoptalking:
                            $ fian = "worried"
                        i "..."
                        i "I guess you're right."
                        if ian_cindy_sex:
                            i "What happened was... wrong. We went too far, for whatever reason."
                            i "The only reasonable way of patching things up would be to... forget it ever happened."
                        else:
                            i "As you said, it was just a dumb kiss. It didn't mean or change anything."
                            i "We can just forget about it ever happening."
                        jump v9cindystoptalking2

        "It was a mistake":
            $ renpy.block_rollback()
            "And she was right."
            $ fian = "sad"
            if ian_cindy_sex:
                i "It was a mistake. A big one."
                c "I can't still believe we really... did it."
            else:
                i "That kiss was a mistake. That much is true."
                c "I still wonder if I'm making too much of a big deal out of it. After all, it was just a stupid kiss..."
            c "But every time I think about it, I... I just don't want to."
            i "It's haunting me, too. That's why I wanted us to talk it over, so we can finally bury it."
            if ian_cindy_sex:
                i "What happened was crazy. I still can't really explain it, I guess we were both drunk and emotions were running high..."
                i "But that's not an excuse. There's no excuse; what we did was wrong."
                c "Keeping this secret is really taking a toll on me..."
                i "Then maybe we should just forget it ever happened."
            else:
                i "As you said, it was just a stupid kiss. We were both drunk, emotions were running high..."
                i "And we made a mistake. Just that."
                c "So... Are you suggesting we forget about it?"
                i "Yeah."
            label v9cindystoptalking2:
                $ ian_cindy_over = True
            "Part of me was against it. I wouldn't be able to forget it even if I wanted to."
            "But I had to act like that was the case. Otherwise, there was no telling what would happen between me, Wade, and Cindy."
            c "Alright... I guess I can do that."
            c "So this is the last time we're gonna speak about it?"
            i "Yeah..."
            if ian_cindy_sex:
                c "Okay... The fastest way to forget it will be to act like it never happened."
                c "But Jeremy..."
                $ fian = "n"
                i "I've talked to him. He won't tell a soul."
                $ fcindy = "n"
                c "Good. I'm glad we talked it over."
            else:
                $ fcindy = "n"
                c "Okay. You're right, it was just a dumb kiss. This changes nothing."
                c "I'm glad we talked it over."
            $ fian = "n"
            i "You didn't make it easy..."
            c "Me? I don't know what you're talking about..."
            c "In fact, I don't even know what we were talking about just now!"
            c "Goodnight, Ian."
            hide cindy2 with short
            i "..."
            i "There she goes."
            i "I guess I did the right thing... A bit late, but better late than never."
            if ian_wits < 9:
                call xp_up('wits') from _call_xp_up_414
            hide ian with short
            $ renpy.end_replay()
            jump v9iansaturdaywade
## CINDY HOME #########################################################################################################################################################################################
    $ v9cindychat = False  ## tracking var
    scene streetnight with long
    "None of us spoke as we walked to Cindy's apartment. The situation was tense, awkward, and exciting all at the same time..."
    play sound "sfx/door_home.mp3"
    $ fian = "n"
    $ fcindy = "sad"
    scene cindyroomnight with long
    "I had only been here once, several months ago, when Wade moved in with Cindy."
    show ian at lef
    show cindy at rig
    with long
    "It felt weird coming back in these circumstances, but I couldn't think of a better place to settle things with Cindy."
    "I knew Wade would be staying the night at my place, so it was safe for me to be here..."
    c "Do you want something to drink?"
    i "A glass of water, please."
    "I followed her to the kitchen, where she poured two glasses."
    c "So... You say you don't want to forget about it."
    c "What do you want us to do, then?"
    i "Well..."
    menu v9cindyfinalconvo:
        "{image=icon_ring.webp}I want to be with you" if ian_cindy_love and ian_cindy > 9:
            $ renpy.block_rollback()
            if ian_look == "wits1":
                scene v9_cindy1a
            elif ian_look == "charisma1":
                scene v9_cindy1b
            elif ian_look == "athletics1":
                scene v9_cindy1c
            elif ian_look == "lust1":
                scene v9_cindy1d
            else:
                scene v9_cindy1e
            show v9_cindy1_0
            with long
            "I got really close to Cindy and placed my hands on her hips."
            i "I want to be with you, Cindy. I can't get that feeling out of my chest."
            c "You want to be with me...? Like how?"
            i "Like that night... I want to kiss you, to embrace you..."
            if ian_cindy_sex:
                i "I want to make love to you."
            c "But Wade..."
            i "Break up with him."
            c "What...? You want me to break up with Wade? Really?"
            i "Yes."
            label v9cindylove:
                stop music fadeout 4.0
            c "But..."
            i "But what? You can't do it? Why?"
            i "I want you, Cindy."
            i "All of you."
            c "..."
            "Her response was much more than what I was expecting."
            play music "music/sex_bright.mp3" loop
            hide v9_cindy1_0 with long
            "She kissed me."
            "Feeling Cindy's sweet lips for the first time after that night sent a shiver down my spine."
            "I responded in kind, and our mouths melted together in long, deep kisses."

        "{image=icon_lust.webp}Let's keep seeing each other" if ian_lust > 4 or ian_cindy_love == False:
            $ renpy.block_rollback()
            $ ian_cindy_love = False
            i "I really like you, Cindy. And I have the feeling it goes both ways, judging by what happened..."
            $ fcindy = "blush"
            c "..."
            if ian_cindy_sex:
                i "That night... It wasn't ideal, but it was amazing. I had been dreaming of being with you..."
            else:
                i "It was just a kiss, but I know both of us wanted more."
            i "I still do."
            play music "music/sex_deep2.mp3" loop
            if ian_look == "wits1":
                scene v9_cindy1a
            elif ian_look == "charisma1":
                scene v9_cindy1b
            elif ian_look == "athletics1":
                scene v9_cindy1c
            elif ian_look == "lust1":
                scene v9_cindy1d
            else:
                scene v9_cindy1e
            show v9_cindy1_0
            with long
            "I got really close to Cindy and placed my hands on her hips."
            c "So what does that mean?"
            i "I want to keep seeing you. I want to..."
            c "You want to be with me? You want me to break up with Wade?"
            c "You want me to cheat on him behind his back?"
            menu:
                "{image=icon_charisma.webp}Yes, cheat on him" if ian_charisma > 5 or ian_chad == 5:
                    $ renpy.block_rollback()
                    i "Yes. I want you to do that."
                    c "That's crazy. I..."
                    i "You also want that. You're just too afraid to accept it."
                    if ian_cindy_sex:
                        i "We already did it once... And it was incredible."
                        i "And I know I'm not wrong when I say you felt the same."
                    else:
                        i "That night we would've shared more than just a kiss if I hadn't kept a cool head..."
                        i "Well, tonight I'm not gonna pull myself back."
                    label v9cindycheat:
                        "I went in for the kiss, decidedly."
                    hide v9_cindy1_0 with long
                    "My lips found Cindy's, and she tensed up for a moment."
                    "But that tension melted away quickly and as softly as the kiss Cindy gave me in return."
                    "Soft kisses that quickly turned deep and steamy..."

                "Break up with Wade":
                    $ renpy.block_rollback()
                    i "You should break up with Wade."
                    $ fian = "worried"
                    $ fcindy = "serious"
                    scene cindyroomnight
                    show cindy at rig
                    show ian at lef
                    with long
                    "Cindy took a step back."
                    c "And then, what?"
                    $ fian = "n"
                    i "Then we can see each other without it being wrong..."
                    c "Isn't it convenient? I sacrifice my relationship, and... what are you giving up, exactly?"
                    $ fian = "worried"
                    if ian_lena_dating:
                        c "I doubt you'd stop seeing Lena, right? Why would you?"
                        if ian_alison_dating:
                            c "Or Alison..."
                        if ian_cherry_dating:
                            c "Or that girl, Alison's model friend..."
                    elif ian_alison_dating:
                        c "I doubt you'd stop seeing Alison, right? Why would you?"
                    else:
                        c "I doubt you'd stop chasing other girls, right? Why would you?"
                        if ian_cherry_dating:
                            c "Like that girl, Alison's model friend..."
                    menu:
                        "{image=icon_ring.webp}I want to be with you" if ian_cindy > 9 and ian_lena_couple == False:
                            $ renpy.block_rollback()
                            $ ian_cindy_love = True
                            stop music fadeout 2.0
                            if ian_look == "wits1":
                                scene v9_cindy1a
                            elif ian_look == "charisma1":
                                scene v9_cindy1b
                            elif ian_look == "athletics1":
                                scene v9_cindy1c
                            elif ian_look == "lust1":
                                scene v9_cindy1d
                            else:
                                scene v9_cindy1e
                            show v9_cindy1_0
                            with long
                            i "I don't care about other girls. Not if you give me a chance to be with you."
                            c "But Wade..."
                            i "I already told you. Break up with him."
                            jump v9cindylove

                        "I'm not ready for a relationship":
                            $ renpy.block_rollback()
                            $ fian = "n"
                            i "I'm not ready for a relationship..."
                            c "Who said anything about relationships?"
                            i "You did."
                            c "All I'm saying is I won't break up with Wade just to hook up with you!"
                            menu:
                                "{image=icon_lust.webp}Then cheat on Wade" if ian_chad > 3 or ian_lust > 5:
                                    $ renpy.block_rollback()
                                    if ian_look == "wits1":
                                        scene v9_cindy1a
                                    elif ian_look == "charisma1":
                                        scene v9_cindy1b
                                    elif ian_look == "athletics1":
                                        scene v9_cindy1c
                                    elif ian_look == "lust1":
                                        scene v9_cindy1d
                                    else:
                                        scene v9_cindy1e
                                    show v9_cindy1_0
                                    with long
                                    i "Then cheat on him. Don't leave him if you don't want to."
                                    i "As long as I can kiss you and make love to you I don't care."
                                    jump v9cindycheat

                                "I can't do it":
                                    $ renpy.block_rollback()
                                    $ fian = "sad"
                                    stop music fadeout 4.0
                                    i "I can't do it."
                                    $ fcindy = "blush"
                                    i "If you don't break up with Wade... it just feels wrong."
                                    if ian_cindy_sex:
                                        i "I feel guilty enough as it is... I can't justify keeping on doing it."
                                    else:
                                        i "Kissing you already makes me feel guilty enough."
                                    $ fcindy = "serious"
                                    c "Coward..."
                                    call friend_xp('cindy', -1) from _call_friend_xp_573
                                    $ ian_cindy = 3
                                    c "I guess we have nothing else to talk about."
                                    c "The smart thing to do will be to forget any of this ever happened. Just like I have been trying to do."
                                    jump v9cindybadidea

        "What do {i}you{/i} want?" if v9cindychat == False:
            $ renpy.block_rollback()
            $ v9cindychat = True
            i "What do {i}you{/i} want, Cindy?"
            $ fcindy = "serious"
            c "I don't know...!"
            c "I've been trying to forget it, but you say you don't want to, so..."
            c "I'm waiting to hear whatever it is you have to say!"
            call friend_xp('cindy', -1) from _call_friend_xp_574
            jump v9cindyfinalconvo

        "This was a bad idea" if v9cindychat:
            $ renpy.block_rollback()
            $ fian = "sad"
            i "This... Maybe this was a bad idea."
            c "Yes, it was."
            call friend_xp('cindy', -1) from _call_friend_xp_575
            $ ian_cindy = 4
            if ian_cindy_sex:
                i "What happened was... wrong. We went too far, for whatever reason."
                i "The only reasonable way of patching things up would be to... forget it ever happened."
            else:
                i "What happened was... wrong. But as you said, it was just a dumb kiss. It didn't mean or change anything."
                i "We can just forget about it ever happening."
            c "That's what I've been trying to do all along."
            label v9cindybadidea:
                $ fian = "n"
            $ ian_cindy_over = True
            $ ian_cindy_love = False
            i "Still, I think it was necessary for us to talk about it. So we don't have to mention it ever again."
            $ fcindy = "sad"
            c "Yeah..."
            if ian_cindy_sex:
                c "About Jeremy..."
                $ fian = "n"
                i "I've talked to him. He won't tell a soul."
            $ fcindy = "n"
            c "So this settles that, doesn't it?"
            i "It does... I'll get going."
            i "Good night, Cindy."
            scene streetnight with long
            "In the end, I wasn't able to do it. A part of me still wanted to, but..."
            "This was the right thing to do. Kind of."
            $ renpy.end_replay()
            jump v9iansaturdaywade

##CINDY SEX #############################################################################################################################################################################################
    $ ian_cindy_dating = True
    $ v9_cindy_sex = True
    "Flashbacks of that night flooded my mind. It was happening again."
    if ian_cindy_sex:
        "The moment I had been dreaming of... The moment to make Cindy mine once again."
        "And, unlike last time, we had the whole night to ourselves."
    else:
        "The moment I had been dreaming of... The moment I could finally make Cindy mine."
        "And, unlike last time, we were going to see it through."
    "Cindy's kisses carried the same ferocious energy that I remembered."
    "She pressed her body against mine, exploring it with her hands, reaching under my shirt..."
    if ian_cindy < 10:
        call friend_xp('cindy', 1) from _call_friend_xp_576
        $ ian_cindy = 10
    scene v9_cindy2
    if ian_look == 2:
        show v9_cindy2b
    with long
    "Clothes were in the way of our needs, so we began stripping them away."
    if ian_cindy_sex:
        "My hands ran over Cindy's torso, sliding down her top, caressing her wonderful breasts..."
    else:
        "My hands ran over Cindy's torso, sliding down her top, and cupped her breasts. They were soft and surprisingly hefty..."
    "She shivered when my palms brushed over her hard nipples."
    play sound "sfx/mh1.mp3"
    c "Nhh..."
    scene v9_cindy3
    if ian_look == 2:
        show v9_cindy3b
    with long
    i "Cindy...!"
    "I turned her around and pressed her against my body, getting drunk on her aphrodisiac aroma."
    "She continued to shiver under my lips, now rolling down her neck. And my hands weren't idle, either."
    if ian_cindy_sex:
        "My palm slid down her tight abdomen, venturing underneath her panties..."
        play sound "sfx/ah2.mp3"
        c "Ian...!"
        "Cindy gasped as my fingers explored her slit. I remembered it well..."
        "It became increasingly moist under my touch and my kisses. Cindy started to tremble between my arms..."
    else:
        "I slid my fingers under the fabric of her panties, feeling her shaven pubis."
        "I was really going to touch Cindy's..."
        play sound "sfx/ah2.mp3"
        c "Ahnnn...!"
        "I was pleased to find her slit moist and welcoming to my touch."
        "I found her clit right away. It was a small, hard button but easily accessible..."
        "I began rubbing it ever so slightly while my hungry kisses continued to devour Cindy's neck, making it slippery with my saliva."
    "All the while I pushed my hips forward, rubbing my raging hard-on on Cindy's buttcheeks..."
    "She reached back with her hand and tried patting my cock over the pants."
    c "Ian... You're so hard...!"
    scene v9_cindy4 with long
    "She was driving me insane. It was time to move it to the bed."
    "We went into the bedroom without getting our hands off each other, compelled by pure lust."
    "I invited her to lay down on the bed and I bent down on top of her, continuing to kiss her lips, her neck, her collarbone..."
    "I finally made it to her breasts, feeling the hard bump of her nipple on my tongue."
    play sound "sfx/ah3.mp3"
    "Cindy moaned as I traced warm paths of saliva over one, then the other..."
    "I looked up, searching for her eyes, but she had them firmly closed."
    "I wanted to stare directly once more into those terrific green jewels."
    "Those wild pupils that had mesmerized me that night in the alley..."
    menu:
        "Ask for a blowjob":
            $ renpy.block_rollback()
            i "Cindy, I want to feel your mouth..."
            c "Wait, do you want me to blow you?"
            "I had already started stripping my pants."
            i "Yes."
            c "Eat me out first, would you...? And I'll consider it."
            call friend_xp('cindy', -1) from _call_friend_xp_577
            i "..."
            i "Sure."

        "Eat Cindy out":
            $ renpy.block_rollback()
            $ v9cindycunnilingus = True
            if ian_cindy_sex:
                "Last time I couldn't eat Cindy out, but now the setting was perfect for it."
            i "Cindy, let me make you feel good... I'm dying to eat you out."
            c "Oh yes Ian, please...!"
            if ian_wits < 9:
                call xp_up('wits') from _call_xp_up_415

        "Have sex with Cindy":
            $ renpy.block_rollback()
            i "I can't take it anymore, Cindy! I want to be inside of you!"
            c "Wha-- Wait, won't you eat me out first?"
            i "Wha--?{w=0.3}{nw}"
            i "Sure..."

    scene v9_cindy5 with long
    "I removed Cindy's panties as I made my way down to her crotch."
    "She spread her legs for me, invitingly. Cindy's pussy was inches away from me, dainty and pretty, just like her."
    if v9cindycunnilingus:
        "I kissed it, softly. I wanted to take my time, savor this moment..."
        "After several kisses, I rolled out my tongue, lubricated with warm saliva, and slid it slowly from bottom to top."
    else:
        "I kissed it, softly at first, and then I got my tongue involved too, sliding it slowly from bottom to top."
    play sound "sfx/ah1.mp3"
    c "Ahh, yes... Just like that, gently..."
    "My tongue found her clit with the same ease as my fingers, and I concentrated my smooth, wet caresses there."
    "I felt her hand on my head, her fingers curling up around my hair as her hips trembled."
    if v9cindycunnilingus:
        "My singular purpose was to make Cindy feel good, and hearing her moans of pleasure was turning me on like crazy...!"
    c "Mhhh... Oh, yes... This is just what I needed..."
    if (ian_wits > 5 and v9cindycunnilingus) or (ian_lust > 5 and v9cindycunnilingus) or (ian_wits > 6 and ian_lust > 6):
        $ cindy_satisfaction += 1
        "I was taking pride in pleasuring Cindy. I wanted to show her how much I relished and adored her."
        "Lucky for me, I knew just how to do that."
        play sound "sfx/orgasm1.mp3"
        scene v9_cindy6 with vpunch
        c "Ahhhhnnn...!! Ohhh...!"
        "Cindy held my head with both hands as her whole body tensed up like the string of a bow."
        "Her hips convulsed on my mouth as I made sure not to lose track of her clit, still flicking it with my tongue."
        "Seeing her cum was so damn hot...! My cock was hard and steaming, raring to go. But ladies come first."
        "The tension slowly dispersed, leaving Cindy lax and panting on the bed."
        c "Oh, fuck, Ian... That was..."
        if ian_cindy < 12:
            call friend_xp('cindy', 1) from _call_friend_xp_578
        c "Mhhh..."
    if cindy_satisfaction > 0 and v9cindycunnilingus:
        "Cindy recovered from her orgasm faster than I was expecting. She suddenly turned me over, taking the lead."
        scene v9_cindy7 with long
        "She held my cock and bent over it. I shivered, knowing what was coming..."
        i "Oh, fuck yes..."
        c "It's only fair..."
        play sound "sfx/bj1.mp3"
        "The touch of Cindy's warm lips on my manhood was electrifying. She was really gonna suck my cock...!"
        "I had been dreaming of this moment, and I was finally living it."
        "I felt the smooth and slippery caress of Cindy's tongue sliding over the underside of my penis..."
        "The grip of her delicate hand rubbing the shaft, her warm breath around my cock, the slight pressure of her lips on the glans..."
        "If only she looked at me in the eyes...!"
        "Before I could decide to ask her to do it, she decided she had sucked me off long enough."
    elif cindy_satisfaction > 0:
        "Cindy recovered from her orgasm faster than I was expecting. She suddenly turned me over, taking the lead."
    else:
        "After a couple of minutes of that, she suddenly turned me over, taking the lead."
        c "I can't take it anymore..."
    scene v9_cindy8 with long
    c "I need you inside of me...!"
    "Cindy hopped on top and held my cock with her hand, lining it up with her slit."
    if ian_cindy_sex:
        "The anticipation had my blood boiling. I was about to fuck Cindy a second time..."
        "Only now I could enjoy her to my heart's content."
    else:
        "I felt it on the tip of my dick."
        "That suffocating heat."
        "That slimy and delicious wetness."
    if ian_lena_couple:
        "I didn't care about Wade. Not even Lena was on my mind at that moment."
        "All my mind and body desired was in front of me. Or rather, on top..."
    else:
        "I didn't care about Wade. All my mind and body desired was in front of me. Or rather, on top..."
    "Cindy let her weight fall down, and I felt my cock pushing through her tight pussy, its inner walls pulsating around it."
    play sound "sfx/ah6.mp3"
    scene v9_cindy9 with long
    "A wave of delight spread through my whole body as it became connected with Cindy's."
    "She gasped and trembled, staying still for a moment, taking in the sensation of having me inside her."
    c "Oh God, Ian...! You're so big...!"
    "I felt my cock swelling even more, and then she started moving her hips."
    if ian_cindy_sex:
        "I got caught in a whirlwind of pleasure and emotions, the same as the first time. The effect Cindy had on me had not weakened..."
        "Quite the opposite, in fact."
        "This moment was even more intense, intimate, and arousing. I had never felt like this before."
    else:
        "I got caught in a whirlwind of pleasure and emotions. I had never been closer to Cindy..."
        "This was it. What I had been dreaming of. What I thought I would never achieve."
        "My tool inside Cindy's pussy. The weight of her body melting over mine..."
    "I made sure to put everything I had into satisfying Cindy. I wanted to sear this night in her memory, same as it was being seared in mine."
    "I wanted to make her mine, and the way to achieve that was drive her crazy with pleasure."
    "I gritted my teeth and pushed my cock even deeper with an undulating hip movement."
    play sound "sfx/oh1.mp3"
    c "Ian, Ian...! You're killing me...!"
    c "You're..."
    play sound "sfx/ah4.mp3"
    scene v9_cindy9b with flash
    c "Aaahhhhnnn!!!" with vpunch
    "Cindy let out a wild, loud, and raspy scream of ecstasy."
    with vpunch
    pause 0.6
    with vpunch
    pause 0.6
    with vpunch
    pause 0.6
    "Her legs shook uncontrollably for several seconds as we held each other tightly."
    "I tried to find her eyes again, but they were still closed. Cindy was completely immersed in waves of pleasure..."
    "And seeing that only made my cock even harder inside of her."
    scene v9_cindy9
    show v9_cindy9_eyes
    with long
    menu:
        "{image=icon_ring.webp}You're so beautiful" if ian_cindy_love:
            $ renpy.block_rollback()
            i "God, you're so beautiful, Cindy..."
            "She trembled again and hid her face in the curve of my neck, breathing heavily."
            i "I love seeing you like this. I love making you cum."
            if ian_charisma < 9:
                call xp_up('charisma') from _call_xp_up_416
            c "Oh fuck, Ian..."

        "You're so sexy":
            $ renpy.block_rollback()
            i "You're so sexy, Cindy... And right now you're sexier than ever."
            "She hid her face in the curve of my neck, breathing heavily."
            i "I love making you cum."
            if ian_lust < 9:
                call xp_up('lust') from _call_xp_up_417
            c "Mhhh... Ian..."

        "Was it good?":
            $ renpy.block_rollback()
            i "How are you feeling? Was it good...?"
            c "It was... ahh..."
            "Cindy hid her face on the pillow, breathing heavily."
            c "So good."

    "I let Cindy recover from her orgasm."
    "She stayed on top of me for a while, with my throbbing cock still inside."
    "I had waited far too long for this to be over so soon."
    i "Do you want to keep going...?"
    scene v9_cindy10 with long
    "Cindy responded not with words, but by rolling to the side and getting on all fours."
    "I accepted her silent invitation in a heartbeat, penetrating her luscious pussy once more."
    c "Uhhnn..."
    "I started slow, testing her reactions. But giving it to Cindy doggy style had me raring to go..."
    "Thankfully there was no rush. I could take all the time I wanted to delight myself with her body."
    "I held her slim waist while I thrust my hips, a bit harder each time, getting in response gasps, then whimpers, and finally moans..."
    play sound "sfx/oh1.mp3"
    c "Uuuhhh...! Ahhh, Ian..."
    "I loved hearing my name coming out of her lips, especially when paired with such erotic groans!"
    menu:
        "{image=icon_athletics.webp}Keep going" if ian_athletics > 4:
            $ renpy.block_rollback()
            $ cindy_satisfaction += 1
            scene v9_cindy11 with long
            "I was at my limit, but I tensed my muscles and kept pounding at her. I didn't want this to end so soon."
            "Cindy gripped the bedsheets, eyes closed and biting her lip."
            "She looked both defenseless and ecstatic under my strong thrusts."
            "She had surrendered completely to the throes of pleasure."
            play sound "sfx/ah5.mp3"
            c "Nhaaaahh...!!!" with vpunch
            "Cindy clasped her fingers even tighter and hid her face on the sheets, suffocating a long moan."
            "I was pretty sure she just came again."
            if v7_cindy_lust == False:
                "Her body was a marvel, her golden hair dazzling, and her sexy moans so fucking hot!"
            else:
                "What a sight... I couldn't believe how beautifully arousing she was!"
            "And at that moment she was all mine...!"

        "Cum!":
            $ renpy.block_rollback()

    i "I'm at my limit... I'm gonna cum, Cindy...!"
    "She answered faintly, almost gasping."
    c "Do it outside...!"
    scene v9_cindy11b with flash
    "I almost didn't make it, but I managed to pull out just in time, heeding her request."
    i "Agghhhh!!!{w=0.6}{nw}" with vpunch
    pause 0.6
    with vpunch
    pause 0.6
    with vpunch
    pause 0.6
    "For a moment I lost my sense of space and time, sucked into a parallel world of pure ecstasy."
    "The only thing that anchored me to the real world were my hands holding Cindy's hips tightly. They were the first thing I felt when I came back from my personal nirvana."
    "It is true what they say: the only way to rid yourself of desire is to fulfill it..."
    stop music fadeout 2.0
    if cindy_satisfaction == 2:
        if ian_lust < 9:
            call xp_up('lust') from _call_xp_up_418
        if ian_will < 2:
            call will_up() from _call_will_up_56
    scene cindyroomnight with long
    "I laid down on the bed, feeling exhausted and light-headed."
    "Cindy snuggled on my side, her face hidden on my armpit. She didn't utter a single word, but feeling her warm body next to me was all I needed."
    "I didn't feel like speaking, either. Doing so would've dispelled the enchanting mist of the moment, revealing the gravity behind our actions."
    "I would face it under the light of day, but it was still dark, and we both found comfort in the night."

## MORNING
    call calendar(_day="Sunday") from _call_calendar_58

    scene cindyroom with long
    "When I opened my eyes the next morning I felt disoriented for a few seconds, waking up in an unfamiliar room."
    $ fian = "worried"
    show iannude2 with short
    "Right... I was in Cindy's bed. Where both she and Wade slept every night..."
    "Looking around one would never be able to tell this was Wade's bedroom, too. It looked way too girly for him..."
    "It was obvious Cindy had imposed her taste when it came to decorating it."
    "Cindy...! My heart skipped a beat, waking me up for good."
    "I turned around on the bed, looking for her."
    play music "music/sex_good.mp3" loop
    scene v9_cindy12
    show v9_cindy12b
    with long
    "She was still sleeping next to me."
    "I was mesmerized by her once again... Was this the sight Wade woke up to every morning?"
    "I took in Cindy's beauty in no hurry, taking my time to watch her body under the first light of the morning."
    "Her toned legs, her wasp waist, her perfect breasts, her sensual lips..."
    hide v9_cindy12b with long
    "And her striking eyes, intensely green like grass under the summer sun."
    "She looked at me peacefully, slowly awakening from her sleep. That gaze I had been yearning for..."
    menu:
        "{image=icon_lust.webp}Turn her on" if ian_lust > 5 or cindy_satisfaction > 0:
            $ renpy.block_rollback()
            $ v9_cindy_morning = True
            "Watching Cindy had my cock rock hard in seconds, and staring into her eyes made my desire for her overflow again."
            "Last night hadn't been enough... Not by a long shot."
            scene v9_cindy13 with long
            "My hands took hold of her body and my lips dove into her neck with slow, steamy kisses."
            play sound "sfx/mh1.mp3"
            "Cindy was lax, still half-asleep, but her body reacted to my touch. I was steadily turning her engine on..."
            "I began moving my hips, rubbing my erection between her thighs. I was excited beyond reason, sinking in my lust for her."
            "She held one of her legs up, allowing my shaft to slide directly over her pussy. She was already wet...!"
            i "Look at me, Cindy. I want to see your beautiful eyes..."
            scene v9_cindy13b with long
            play sound "sfx/ah6.mp3"
            "I thought she would ignore my request, but when my cock started penetrating her she opened her eyelids..."
            "I finally found that connection I was looking for. Staring directly into her pupils while I was inside her..."
            "For some reason, it amplified all my sensations. I was drowning in the moment, and I knew she was drowning with me."
            if ian_cindy_love:
                i "I love you, Cindy..."
                c "No... If you say something like that to me..."
            else:
                i "You drive me insane, Cindy... I want you...!"
                c "Yes... I want you too...! I..."
            play sound "sfx/moan1.mp3"
            c "Annhhh!!" with vpunch
            "Cindy let out the cutest moan when I thrust my cock even deeper."
            "I felt the inner walls of her pussy gripping me tight. Her lips were so close I could feel her quivering breath on mine."
            "I was about to match my thrusts with a profound, ardent kiss, when..."
            stop music
            play sound "sfx/door_home.mp3"
            "We heard the unmistakable sound of someone sliding a key into the front door."
            play music "music/tension.mp3" loop
            $ fian = "surprise"
            $ fcindy = "surprise"
            scene cindyroom
            show iannude at lef
            show cindynude2 at rig
            with short
            i "What the...!?"
            c "Oh, no! No, no!"
            w "I'm home..."
            i "FUCK!" with vpunch
            i "What do we do...!?"
            $ fcindy = "blush"
            play sound "sfx/punchgym.mp3"
            hide iannude with vpunch
            "Cindy pushed me off the bed, to the side opposite the door."
            "I heard her get up quickly and leave the room, closing the door behind her."
            play sound "sfx/door.mp3"
            hide cindynude2 with short
            pause 1
            $ fian = "worried"
            show iannude2 with short

        "Get up":
            $ renpy.block_rollback()
            stop music fadeout 2.0
            "Watching her naked body had been turning me on, but staring into her eyes made me hesitate."
            "The night had passed, and now the consequences of our actions became visible under the light of day."
            $ fian = "blush"
            $ fcindy = "blush"
            scene cindyroom
            show iannude at lef
            show cindynude2 at rig
            with long
            i "Good morning..."
            c "Um... Good morning."
            "I could see the worry on Cindy's face. She was thinking the same thing as me, no doubt."
            "I wanted to say something, but I wasn't sure what."
            if ian_cindy_love:
                "Last night had been a turning point for us, or at least that was what it felt to me."
                "I told Cindy I wanted to be with her, and she chose me instead of Wade... Right?"
            else:
                "Should I play it cool, or try to address the situation right away?"
                if ian_cindy_sex:
                    "It wasn't the first time she cheated on Wade with me, which probably made things even more grievous."
                else:
                    "It had finally happened: she cheated on Wade... with me."
            c "..."
            $ fian = "worried"
            i "So, uh..."
            $ fcindy = "n"
            c "I'm gonna make some coffee. Want some?"
            i "Sure."
            hide cindynude2 with short
            show iannude at truecenter with move
            "Cindy went to the kitchen, leaving me alone in the bedroom."
            "It was always so hard telling what she was really thinking..."
            hide iannude
            show ian
            with short
            "I got dressed and was about to go to the kitchen when I heard the dangling of keys on the front door."
            play sound "sfx/door_home.mp3"
            $ fian = "surprise"
            i "What the...!?"
            w "I'm home..."
            play music "music/tension.mp3" loop
            i "FUCK!" with vpunch
            $ fian = "worried"
            "I froze for a second. What should I do?"
            "My first instinct was to hide under the bed. Or maybe I should just confront Wade?"
            "Maybe I could make up some kind of excuse. No, I--"

    c "{i}What are you doing here so early? What about the tournament?{/i}"
    w "{i}I got eliminated yesterday...{/i}"
    "I stayed put, quiet like a statue, listening to their conversation."
    c "{i}Really? How come you didn't tell me?{/i}"
    w "{i}Since when do you care? I thought you said it was a stupid tournament.{/i}"
    w "{i}Besides, you haven't told me about the photo shoot either.{/i}"
    c "{i}Do you want to... go out for breakfast so we can talk about both?{/i}"
    w "{i}I already had a bite at Perry's... {/i}"
    c "{i}Well, I haven't. I just woke up, as you can see.{/i}"
    c "{i}Let me get dressed real quick and we can go. No, in fact, you can wait for me outside.{/i}"
    $ fian = "disgusted"
    w "{i}What? Why--{/i}"
    c "{i}I want to go to that brunch place, the one that's always packed. I'm starving, so I don't want to have to wait in line!{/i}"
    w "{i}So I have to do the waiting for you?{/i}"
    c "{i}It's the least you can do after dumping me yesterday!{/i}"
    w "{i}I didn't dump you...{/i}"
    c "{i}Please, Wade! Besides, I will get ready in a second!{/i}"
    "There was silence for a second, and then Wade sighed."
    w "{i}Whatever you say...{/i}"
    $ fian = "sad"
    stop music fadeout 2.0
    $ fcindy = "blush"
    "I heard Wade leave, and seconds later Cindy came back into the bedroom."
    if v9_cindy_morning:
        show iannude2 at lef with move
    else:
        show ian at lef with move
    play sound "sfx/door.mp3"
    show cindynude at rig with short
    if v9_cindy_morning:
        c "You need to get dressed and leave ASAP."
    else:
        c "You need to leave ASAP."
    i "Yeah, that was fucking close... I almost had a heart attack."
    c "You? What about me!?"
    c "I have no idea how I got him to leave. I'm...!"
    c "I need to take a shower."
    hide cindynude with short
    pause 1
    hide iannude2 with short
    if v9_cindy_morning:
        "I picked up my clothes, got dressed, and left while Cindy was in the bathroom."
    else:
        "Cindy got in the bathroom and closed the door. It was time for me to leave..."
    scene street2 with long
    "My heart continued beating like mad while I made my way home. That had been fucking dangerous."
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH09_S07")
    jump v9iansunday2

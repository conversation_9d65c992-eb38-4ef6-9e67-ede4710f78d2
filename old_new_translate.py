# -*- coding: utf-8 -*-
import os
import re
import sys

# 在import区域下方添加新函数


def is_english_or_numeric(text):
    """检查字符串是否只包含英文字符、数字和常见标点符号"""
    # 如果字符串为空直接返回False
    if not text or text.strip() == '':
        return False

    # 检查每个字符是否是英文、数字或标点
    for char in text:
        # 英文字母、数字、常见标点符号和空格
        if not (ord('a') <= ord(char) <= ord('z') or
                ord('A') <= ord(char) <= ord('Z') or
                ord('0') <= ord(char) <= ord('9') or
                char in " .,!?;:'\"()-_[]{}@#$%^&*+=\\|/<>~`"):
            return False
    return True


# 显示脚本依赖提示
print("该脚本是用于辅助ren'py游戏的翻译，可提取text、character、renpy.input、textbutton、tooltip以及变量中的字符串并生成old new语句。")
print("需要安装pandas和openpyxl库，没有的话打开cmd运行pip install pandas openpyxl先")
print("全部需翻译的表格在translate/translate_excel文件夹下，老版本翻译表格在translate/translated_excel文件夹下，最终的old new语句在translate/translate_result文件夹下")

try:
    import pandas as pd
    from openpyxl import load_workbook
    from openpyxl.utils import get_column_letter
except ImportError as e:
    print(f"\n错误：{e}")
    print("请安装所需库，在cmd中运行命令：pip install pandas openpyxl")
    input("按任意键退出...")
    sys.exit(1)


def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    try:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"已创建目录: {directory}")
    except Exception as e:
        print(f"创建目录 {directory} 时出错: {e}")
        sys.exit(1)


def extract_strings_from_rpy(file_path, filter_non_english=False):
    """从rpy文件中提取字符串
    
    Args:
        file_path: rpy文件路径
        filter_non_english: 是否过滤非英文字符串
    """
    name_strings = []
    text_strings = []
    variable_strings = []
    replace_strings = []

    try:
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()

            # 1. 提取character的字符串
            # 排除_()包裹的Character调用
            char_patterns = [
                r'Character\s*\(\s*(["\'])(.*?)\1',  # Character("name")
                # define x = Character("name")
                r'define\s+\w+\s*=\s*Character\s*\(\s*(["\'])(.*?)\1'
            ]

            for pattern in char_patterns:
                # 先查找所有匹配
                all_matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in all_matches:
                    # 获取整个匹配和匹配的字符串
                    full_match = match.group(0)
                    string = match.group(2)

                    # 确保这个匹配不是在_()中
                    start_pos = match.start()
                    line_start = content.rfind('\n', 0, start_pos)
                    if line_start == -1:
                        line_start = 0
                    line = content[line_start:start_pos]

                    # 修正逻辑：如果行不包含_( 或者不是_()函数调用的一部分，则添加字符串
                    if ('_(' not in line or not re.search(r'_\s*\(\s*' + re.escape(full_match), content)) and \
                       (not filter_non_english or is_english_or_numeric(string)):
                        name_strings.append(string)

            # 2. 提取text, textbutton, tooltip, renpy.input的字符串
            # 要排除_()包裹的内容
            text_keywords = [
                r'text\s+',
                r'textbutton\s+',
                r'tooltip\s+',
                r'renpy\.input\s*\(\s*',
                r'show\s+text\s+'
            ]

            for line in content.split('\n'):
                for keyword in text_keywords:
                    # 如果行中包含关键字
                    if re.search(keyword, line, re.IGNORECASE):
                        # 先检查是否有_()包裹
                        if not re.search(keyword + r'\s*_\s*\(', line, re.IGNORECASE):
                            # 提取双引号或单引号中的内容
                            str_matches = re.finditer(r'(["\'])(.*?)\1', line)
                            for match in str_matches:
                                # 确保这个引号内容是紧跟在关键字后面的
                                quote_start = match.start()
                                if re.search(keyword + r'[^"\']*$', line[:quote_start], re.IGNORECASE):
                                    string = match.group(2)
                                    if not filter_non_english or is_english_or_numeric(string):
                                        text_strings.append(string)

            # 3. 提取变量定义中的字符串
            # 排除_()包裹的内容
            variable_keywords = [
                r'default\s+\w+\s*=\s*',
                r'define\s+\w+\s*=\s*',  # 非Character定义
                r'\$\s*\w+\s*=\s*'
            ]

            for line in content.split('\n'):
                for keyword in variable_keywords:
                    # 如果行中包含关键字但不包含Character
                    if re.search(keyword, line) and "Character" not in line:
                        # 检查是否有_()包裹
                        if not re.search(keyword + r'\s*_\s*\(', line):
                            # 提取双引号或单引号中的内容
                            str_matches = re.finditer(r'(["\'])(.*?)\1', line)
                            for match in str_matches:
                                string = match.group(2)
                                if not filter_non_english or is_english_or_numeric(string):
                                    variable_strings.append(string)

            # 4. 提取所有f-string文本，无论在哪里出现
            # 排除_()包裹的内容
            for line in content.split('\n'):
                if 'f"' in line or "f'" in line:
                    # 确保不是在_()中
                    if not re.search(r'_\s*\(\s*f', line):
                        f_matches = re.finditer(r'f(["\'])(.*?)\1', line)
                        for match in f_matches:
                            string = match.group(2)
                            if not filter_non_english or is_english_or_numeric(string):
                                replace_strings.append(string)
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")

    return name_strings, text_strings, variable_strings, replace_strings


def extract_old_new_translations(file_path):
    """从已有的翻译文件中提取old和new字符串"""
    old_strings = []
    new_strings = []

    try:
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()

            # 提取old和new语句
            old_matches = re.findall(r'old\s+(["\'])(.*?)\1', content)
            new_matches = re.findall(r'new\s+(["\'])(.*?)\1', content)

            for _, old_string in old_matches:
                old_strings.append(old_string)

            for _, new_string in new_matches:
                new_strings.append(new_string)
    except Exception as e:
        print(f"处理翻译文件 {file_path} 时出错: {e}")

    # 确保old和new数量匹配
    min_length = min(len(old_strings), len(new_strings))
    return old_strings[:min_length], new_strings[:min_length]


def extract_replace_text(file_path):
    """从汉化文件中提取replace_text函数的替换项"""
    replace_old = []
    replace_new = []

    try:
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()

            # 查找replace_text函数
            replace_function = re.search(
                r'def\s+replace_text\s*\(.*?\):(.*?)return\s+s', content, re.DOTALL)

            if replace_function:
                function_body = replace_function.group(1)

                # 提取s.replace('old', 'new')模式
                replace_pairs = re.findall(
                    r's\s*=\s*s\.replace\s*\(\s*([\'"])(.*?)\1\s*,\s*([\'"])(.*?)\3\s*\)', function_body)

                for _, old, _, new in replace_pairs:
                    replace_old.append(old)
                    replace_new.append(new)
    except Exception as e:
        print(f"处理替换文本函数 {file_path} 时出错: {e}")

    return replace_old, replace_new


def scan_directory_for_rpy(directory, exclude_dirs=None):
    """扫描目录获取所有rpy文件路径"""
    if exclude_dirs is None:
        exclude_dirs = []

    rpy_files = []

    try:
        for root, dirs, files in os.walk(directory):
            # 跳过排除的目录
            dirs[:] = [d for d in dirs if os.path.join(
                root, d) not in exclude_dirs]

            for file in files:
                if file.endswith('.rpy'):
                    rpy_files.append(os.path.join(root, file))
    except Exception as e:
        print(f"扫描目录 {directory} 时出错: {e}")

    return rpy_files


def adjust_column_width(file_path):
    """调整Excel文件的列宽以适应内容"""
    try:
        workbook = load_workbook(file_path)
        worksheet = workbook.active

        # 计算每列的最大宽度
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)

            for cell in column:
                try:
                    if cell.value:
                        cell_length = len(str(cell.value))
                        if cell_length > max_length:
                            max_length = cell_length
                except:
                    pass

            # 设置列宽度 (加一些额外宽度以确保完整显示)
            adjusted_width = (max_length + 2) * 1.2
            worksheet.column_dimensions[column_letter].width = adjusted_width

        workbook.save(file_path)
    except Exception as e:
        print(f"调整Excel列宽时出错: {e}")


def save_to_excel(strings, file_path):
    """将字符串保存到Excel的A列，并自动调整列宽"""
    try:
        unique_strings = list(set(strings))
        df = pd.DataFrame(
            {'A': unique_strings, 'B': [''] * len(unique_strings)})
        df.to_excel(file_path, index=False, header=False)

        # 调整列宽
        adjust_column_width(file_path)

        print(f"已保存 {len(unique_strings)} 条字符串到 {file_path}")
    except Exception as e:
        print(f"保存Excel文件 {file_path} 时出错: {e}")


def save_to_excel_with_translations(old_strings, new_strings, file_path):
    """将old和new字符串保存到Excel的A列和B列，并自动调整列宽"""
    try:
        data = {'A': old_strings, 'B': new_strings}
        df = pd.DataFrame(data)
        df.to_excel(file_path, index=False, header=False)

        # 调整列宽
        adjust_column_width(file_path)

        print(f"已保存 {len(old_strings)} 条翻译到 {file_path}")
    except Exception as e:
        print(f"保存翻译Excel文件 {file_path} 时出错: {e}")


def generate_old_new_file(strings, output_file):
    """生成old new格式的文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for string in strings:
                f.write("    old \"%s\"\n" % string.replace('"', '\\"'))
                f.write("    new \"\"\n\n")

        print(f"已生成 {len(strings)} 条翻译语句到 {output_file}")
    except Exception as e:
        print(f"生成翻译文件 {output_file} 时出错: {e}")


def generate_replace_file(replace_strings, replace_compared_df, language_folder, output_file):
    """生成replace_text函数文件"""
    try:
        # 获取已翻译过的替换项集合
        if replace_compared_df is not None and not replace_compared_df.empty:
            already_replaced = set(replace_compared_df[0].tolist())
        else:
            already_replaced = set()

        # 找出未翻译的替换项
        unique_replace_strings = list(set(replace_strings) - already_replaced)

        # 生成replace_text函数文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f'init python:\n')
            f.write(f'    if preferences.language == "{language_folder}" :\n')
            f.write(f'        def replace_text(s):\n')

            # 先写入未翻译的替换项
            for string in unique_replace_strings:
                f.write(f'            s = s.replace(\'{string}\' , \'\')\n')

            # 再写入已翻译的替换项（如果有）
            if replace_compared_df is not None and not replace_compared_df.empty:
                for _, row in replace_compared_df.iterrows():
                    f.write(
                        f'            s = s.replace(\'{row[0]}\' , \'{row[1]}\')\n')

            f.write(f'            return s\n')
            f.write(f'        config.replace_text = replace_text\n')

        print(f"已生成replace_text函数到 {output_file}")
    except Exception as e:
        print(f"生成replace_text函数文件时出错: {e}")


def main():
    # 获取脚本所在目录（而不是当前工作目录）
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # 创建必要的目录，都基于脚本所在目录
    game_dir = os.path.join(script_dir, 'game')
    translate_dir = os.path.join(script_dir, 'translate')
    translate_excel_dir = os.path.join(translate_dir, 'translate_excel')
    translated_excel_dir = os.path.join(translate_dir, 'translated_excel')
    translate_result_dir = os.path.join(translate_dir, 'translate_result')

    # 确保所有目录存在
    ensure_dir_exists(translate_dir)
    ensure_dir_exists(translate_excel_dir)
    ensure_dir_exists(translated_excel_dir)
    ensure_dir_exists(translate_result_dir)

    if not os.path.exists(game_dir):
        print(f"错误：游戏目录 {game_dir} 不存在，请确保脚本位于游戏根目录")
        input("按任意键退出...")
        return

    # 询问汉化文件夹名称
    language_folder = input("请输入汉化文件夹名(默认为chinese)：") or "chinese"

    # 询问是否生成全部语句的表格
    generate_all = input("是否生成全部语句的表格(y/n)：").lower()
    if generate_all != 'y':
        print("已终止脚本")
        input("按任意键退出...")
        return

    # 扫描game文件夹下的rpy文件，排除tl目录
    tl_dir = os.path.join(game_dir, 'tl')
    exclude_dirs = [tl_dir]
    rpy_files = scan_directory_for_rpy(game_dir, exclude_dirs)

    all_names = []
    all_texts = []
    all_variables = []
    all_replace_strings = []

    # 处理每个文件
    for file_path in rpy_files:
        print(f"正在处理文件: {file_path}")
        # 启用英文过滤器，因为这是处理game目录中的文件
        names, texts, variables, replace_strings = extract_strings_from_rpy(
            file_path, filter_non_english=True)
        all_names.extend(names)
        all_texts.extend(texts)
        all_variables.extend(variables)
        all_replace_strings.extend(replace_strings)

    # 保存到Excel文件
    name_excel = os.path.join(translate_excel_dir, 'name.xlsx')
    text_excel = os.path.join(translate_excel_dir, 'text.xlsx')
    variable_excel = os.path.join(translate_excel_dir, 'variable.xlsx')
    replace_excel = os.path.join(translate_excel_dir, 'replace.xlsx')

    save_to_excel(all_names, name_excel)
    save_to_excel(all_texts, text_excel)
    save_to_excel(all_variables, variable_excel)
    save_to_excel(all_replace_strings, replace_excel)

    print("已生成完毕")

    # 询问是否生成老版本翻译表格
    generate_old = input("是否生成老版本翻译表格(y/n)：").lower()
    if generate_old != 'y':
        print("已终止脚本")
        input("按任意键退出...")
        return

    # 扫描tl目录下的指定语言文件夹
    tl_language_dir = os.path.join(tl_dir, language_folder)
    if not os.path.exists(tl_language_dir):
        print(f"错误：{tl_language_dir} 目录不存在")
        input("按任意键退出...")
        return

    tl_rpy_files = scan_directory_for_rpy(tl_language_dir)
    all_old_strings = []
    all_new_strings = []
    all_replace_old = []
    all_replace_new = []

    # 处理每个翻译文件时不过滤非英文内容
    for file_path in tl_rpy_files:
        print(f"正在处理翻译文件: {file_path}")
        old_strings, new_strings = extract_old_new_translations(file_path)
        replace_old, replace_new = extract_replace_text(file_path)

        all_old_strings.extend(old_strings)
        all_new_strings.extend(new_strings)
        all_replace_old.extend(replace_old)
        all_replace_new.extend(replace_new)

    # 保存到Excel文件
    compared_excel = os.path.join(translated_excel_dir, 'compared.xlsx')
    replace_compared_excel = os.path.join(
        translated_excel_dir, 'replace_compared.xlsx')

    save_to_excel_with_translations(
        all_old_strings, all_new_strings, compared_excel)
    save_to_excel_with_translations(
        all_replace_old, all_replace_new, replace_compared_excel)

    print("已生成完毕")

    # 询问是否生成最终语句
    generate_final = input("是否生成最终语句(y/n)：").lower()
    if generate_final != 'y':
        print("已终止脚本")
        input("按任意键退出...")
        return

    try:
        # 读取之前生成的Excel文件
        name_df = pd.read_excel(name_excel, header=None)
        text_df = pd.read_excel(text_excel, header=None)
        variable_df = pd.read_excel(variable_excel, header=None)
        compared_df = pd.read_excel(compared_excel, header=None)

        # 读取replace相关的Excel文件
        replace_df = pd.read_excel(replace_excel, header=None)
        try:
            replace_compared_df = pd.read_excel(
                replace_compared_excel, header=None)
        except:
            replace_compared_df = None

        # 修改：按照优先级处理重复内容（优先保留name，然后是text，最后是variable）
        print("按优先级处理重复内容（优先保留name，然后text，最后variable）...")

        # 将DataFrame列表转换为列表
        name_strings = name_df[0].tolist()
        text_strings = text_df[0].tolist()
        variable_strings = variable_df[0].tolist()

        # 创建一个字典来存储最终要翻译的字符串及其来源
        final_strings = {}

        # 按照优先级添加字符串
        # 1. 先添加name文件的内容（最高优先级）
        for string in name_strings:
            final_strings[string] = "name"

        # 2. 添加text文件的内容（中等优先级）- 如果已存在则跳过
        for string in text_strings:
            if string not in final_strings:
                final_strings[string] = "text"

        # 3. 添加variable文件的内容（最低优先级）- 如果已存在则跳过
        for string in variable_strings:
            if string not in final_strings:
                final_strings[string] = "variable"

        # 创建最终的不重复字符串集合
        all_strings_to_translate = set(final_strings.keys())

        # 移除已翻译的字符串
        old_strings_set = set(compared_df[0].tolist())
        strings_to_translate = all_strings_to_translate - old_strings_set

        # 生成最终的翻译文件
        translate_txt = os.path.join(translate_result_dir, 'translate.txt')
        replace_txt = os.path.join(translate_result_dir, 'replace.txt')

        generate_old_new_file(strings_to_translate, translate_txt)
        generate_replace_file(replace_df[0].tolist(
        ), replace_compared_df, language_folder, replace_txt)

        print("已生成完毕")
    except Exception as e:
        print(f"生成最终翻译文件时出错: {e}")

    input("按任意键退出...")


if __name__ == "__main__":
    try:
        main()
    except ImportError as e:
        print(f"\n错误：{e}")
        print("请安装所需库，在cmd中运行命令：pip install pandas openpyxl")
        input("按任意键退出...")
    except Exception as e:
        print(f"错误：{e}")
        input("按任意键退出...")

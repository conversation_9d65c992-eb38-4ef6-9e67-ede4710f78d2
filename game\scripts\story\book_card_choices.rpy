label book_card_choice_2:
    if book_card1 == "vengeance":
        if book_scifi:
            i "Yes, let's go with this."
            i "It will give my sci-fi book a dark and gruesome tone that will work really well."
        elif book_fantasy:
            i "Yes, let's go with this."
            i "It will give my fantasy book a dark and gruesome tone that will be really cool."
        elif book_historical:
            i "Yes, let's go with this."
            i "I will need to find some interesting rivalry or grudge in History, though."
    elif book_card1 == "call_of_duty":
        if book_scifi:
            i "Making the hero fight for something bigger than himself will make my sci-fi story more relatable and epic."
            i "And maybe the cause that sets him on his adventure isn't as righteous as it seems at first glance..."
        elif book_fantasy:
            i "The call of duty has kick-started so many classic adventure stories. The hero has to fight for something bigger than himself..."
            i "It'll work like a charm."
        elif book_historical:
            i "The call of duty has kick-started so many adventures in History. Wars, conquests, sacrifices..."
            i "People have been very patriotic throughout History, dying to protect something they believed bigger than themselves."
    elif book_card1 == "chosen_one":
        if book_scifi:
            i "A Chosen One in a sci-fi novel? I'm not sure I have seen that before..."
            i "Maybe I can give reason to that pre-destined thing using some deterministic physics or causal loops in space-time!"
            i "Yeah, that sounds pretty awesome..."
        elif book_fantasy:
            i "A fantasy story needs a Chosen One, right?"
            i "That should be easy to write..."
        elif book_historical:
            i "A Chosen One in a historical setting? I wonder how I will justify that..."
            i "History is already written, though, so one can argue it was bound to happen that way."
    return

label book_card_choice_3:
    if book_card2 == "dark_lord":
        if book_scifi:
            i "Mhh... A Dark Lord in a sci-fi book? Sure, why not? It could work."
            i "It can be a space conqueror or some sort of evil cosmic entity..."
        elif book_fantasy:
            i "What's a fantasy book without a Dark Lord? It's true and tested."
            i "All great fantasy stories have one... I should have one, too."
        elif book_historical:
            i "You could easily say someone like Hitler or Stalin were real-life Dark Lords..."
            i "I'm sure I can find other examples in History."
    elif book_card2 == "villain":
        if book_scifi:
            i "A treacherous and violent antagonist can fit in my sci-fi book."
            i "Someone evil who's hellbent on destroying anyone who gets between him and his selfish goals..."
        elif book_fantasy:
            i "I think using this kind of antagonist will work best in my fantasy book."
            i "Writing an enemy who's grounded and gritty, far from fantasy's tropes."
        elif book_historical:
            i "History is full of villains, people with selfish and seedy interests that stepped on everyone else..."
            i "Treachery, tyranny, murder... All that sounds pretty villainous."
    elif book_card2 == "relativistic":
        if book_scifi:
            i "Sci-fi is the perfect space for grays: no absolute goodness or evilness."
            i "Questioning morality is questioning culture and society itself, so it will work perfectly."
        elif book_fantasy:
            i "Someone who's neither good nor bad, someone chaotic with his own interests..."
            i "It could work in a fantasy setting."
        elif book_historical:
            i "Nothing's black or white in History. It's all a conflict of interests."
            i "Everyone's the hero in their own eyes, making the other the bad guy... It's an interesting insight."
    return

label book_card_choice_4:
    if book_card3 == "trickster":
        if book_scifi:
            i "I guess this kind of mentor will add to the complexity of the story, playing off of it."
        elif book_fantasy:
            i "This kind of mentor could make the story more fun and original."
        elif book_historical:
            i "I'm sure I can find such a character in History, someone like Maquiavelo, or one of those crazy Zen masters..."
    elif book_card3 == "sage":
        if book_scifi:
            i "I could make him a scientific genius who's repentant from working for the bad guys..."
        elif book_fantasy:
            i "This will be easy. This archetype fits right in."
        elif book_historical:
            i "This could be a very interesting experience to portray a great man from History in the book."
            i "Make him explain his views, which ended up shaping the world we live in..."
    elif book_card3 == "anti_hero":
        if book_scifi:
            i "Writing an anti-hero is always fun. He could be a galactic bounty hunter or a thug from the underworld..."
        elif book_fantasy:
            i "Writing an anti-hero is always fun. He could be a wandering monster hunter or a mercenary, a sword for hire..."
        elif book_historical:
            i "Writing an anti-hero is always fun, and I'm sure I can find a lot of characters like those in History."
    return

label book_card_choice_5:
    if book_card4 == "fight":
        if book_scifi:
            i "I have to think of a way to make the fight clever and interesting, having the characters use some tricks and technology to their advantage..."
        elif book_fantasy:
            i "Nothing's better than a good, gritty duel to the death, sword versus sword."
        elif book_historical:
            i "The main character and the villain can have their final duel in the middle of the battlefield... That sounds super epic."
    elif book_card4 == "treason":
        if book_scifi:
            i "Plenty of opportunities to plot a good treason. Galactic politics can be really complex... and backstabbing."
        elif book_fantasy:
            i "A close companion could betray the hero. Maybe he's been trying to sabotage him all along, or maybe he has his own interests..."
        elif book_historical:
            i "If the reader knows about History, the treason won't come as a surprise, though. But it's interesting nonetheless."
    elif book_card4 == "doom":
        if book_scifi:
            i "It's a good way to delve into the grim darkness of the future... Sounds fitting."
        elif book_fantasy:
            i "Fantasy heroes have it too easy normally. I want to challenge him, put him against really dire odds."
            i "It will make for a very epic narration."
        elif book_historical:
            i "There have been a lot of moments of crisis for kingdoms and countries during History. Many ceased to exist or transformed completely."
    return

label book_card_choice_6:
    if book_card5 == "romantic":
        if book_scifi:
            i "I need to think about how to add a romantic love interest to a sci-fi book without making it feel too cliché or out of place..."
        elif book_fantasy:
            i "A romantic love interest should be easy to fit into my fantasy story."
        elif book_historical:
            i "A romantic love interest can balance out nicely the historical element of the book, adding some human drama."
    elif book_card5 == "crude":
        if book_scifi:
            i "I'm tired of those romantic fairy tales about love. It's far more complex and dramatic, and I will write about that in my sci-fi book."
        elif book_fantasy:
            i "Fantasy is not only about fairy tales and their one-dimensional depiction of love. And I will show that in my book."
        elif book_historical:
            i "Love is often harsh and shitty in real life. I will stay true to that in my Historical fiction."
    elif book_card5 == "metaphysical":
        if book_scifi:
            i "A scientific examination of what love really is could lead to very interesting philosophical interpretations. I'll go with that!"
        elif book_fantasy:
            i "A fantasy scenario has some very interesting possibilities to explore that, with magic and gods and that kind of stuff..."
        elif book_historical:
            i "Religion has always been in the heart of human societies, and they always talked about Gods and their love. I could explore that in my book."
    return

label book_card_choice_7:
    if book_card6 == "victory":
        if book_scifi:
            i "I have hope for a better future. That's the final idea I want my book to convey."
        elif book_fantasy:
            i "After all those trials and adventures, it would be a downer if the hero didn't succeed!"
        elif book_historical:
            i "A good ending always works, and it justifies all the struggles."
    elif book_card6 == "sacrifice":
        if book_scifi:
            i "This is a good final message for my sci-fi book. Real and bittersweet."
        elif book_fantasy:
            i "Fantasy stories can become way too whimsical with a happy ending. This will make it more nuanced and interesting."
        elif book_historical:
            i "The sacrifices of the past were always made to gain something, to build a more prosperous society. It didn't always work, but..."
    elif book_card6 == "defeat":
        if book_scifi:
            i "This will cap off my sci-fi exploration with a dark, somber note. A grim message."
        elif book_fantasy:
            i "This will surely subvert the expectations of most fantasy readers."
        elif book_historical:
            i "History is tragic itself. At least we can learn something from the mistakes of the past."
    return
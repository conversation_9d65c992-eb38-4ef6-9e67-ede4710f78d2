init offset = 1














image side jd = "JDMOD/images/gui/JD big.png"
define jd = Character (image="jd", what_color="#e1bed6", what_xalign=0.5, what_text_align=0.5)
default persistent.textbox_opacity = 0.8

screen say(who, what):
    style_prefix "say"

    window:
        id "window"
        ysize gui.textbox_height
        xfill True
        background Transform( Image("gui/textbox.webp", xalign=0.5, yalign=1.0) , alpha=persistent.textbox_opacity)

        if who is not None:

            window:
                id "namebox"
                style "namebox"
                text who id "who"

        text what id "what"




    if not renpy.variant("small"):
        add SideImage() xpos 335 xanchor 0.5 ypos 898 yanchor 0.5
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc

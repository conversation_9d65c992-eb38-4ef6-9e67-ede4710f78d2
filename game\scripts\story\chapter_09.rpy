##################################################################################################################################################################################################################
########################################################### CHAPTER 9 LUST AND POWER #################################################################################################################################################################################
##################################################################################################################################################################################################################

label chapter_nine:

    $ save_name = "Ian: Chapter 9"
    $ holly_glasses = True
    $ wade_look = 1
    $ cindy_look = 1
    $ lena_active = False
    $ ian_active = True

    call label_chapter_title from _call_label_chapter_title_8
    show active_ian
    with long
    pause 1.0
    call calendar(_day="Tuesday", _week=3) from _call_calendar_8

    $ fian = "n"
    $ ian_look = 1
    $ holly_look = 1
## IAN WORKING - RECAP ################################################################################################################################################################################################################
    play music "music/normal_day4.mp3"
    if ian_holly_dating:
        $ holly_look = "1skirt"
# Magazine
    if ian_job_magazine > 0:
        scene magazine with long
        i "Another day at the office. And it's just Tuesday..."
        show ian with short
        if ian_defy_minerva or ian_minerva_sex:
            "Having stabilized my situation with Minerva was good for my finances, but it still sucked to spend five days a week in here."
            i "At least Minerva has been leaving me alone after our confrontation."
            if ian_minerva_sex:
                if v8_minerva_sex:
                    $ fian = "confident"
                    "And I had gotten some additional perks, too: a cock-hungry MILF to play with..."
                    "It was a pretty wicked situation all things considered, but as long as nobody knew... No reason not to indulge in it."
                else:
                    $ fian = "shy"
                    "We had had another kind of \"confrontation\" too, a really wicked one..."
                    if v7_minerva_sex:
                        "It had also happened a second time, but all things considered, it seemed a good idea to try and keep my distance from Minerva. This situation was too weird, and dangerous."
                    else:
                        "It had only been once, though. And I wasn't looking forward to it happening a second time. That situation was too weird, and dangerous."
        else:
            "Things were the same as always, boring and pretty frustrating. At least Minerva had been very busy lately, which meant she had been leaving me alone."
            if ian_job_magazine == 1:
                "At times I wondered if this internship was even worth my time considering the meager pay."
                "I couldn't wait for the book contest to hopefully get noticed by a publisher."
            else:
                "I wanted to keep things like that."
        $ fian = "n"
        if ian_holly_dating:
            show ian at lef with move
            $ fian = "smile"
            $ fholly = "happyshy"
            show holly2 at rig with short
            h "Lunch break! Do you want to eat together?"
            i "Of course."
            h "I thought we could drop by the café. Lena asked me to check out how they're doing..."
            i "Sounds good. Let's go."
        else:
            i "Lunch break... I think I'll visit the café today, even though Lena's away."
# Rider
    else:
        scene v8_rider1 with long
        "One more day pedaling around the city, making food deliveries."
        "I had been doing it for two weeks now. It was honest work, but a pretty shitty one."
        "It would help me pay the bills for now, but I knew I couldn't stake my future on it. Not much else I could do for now, though..."
        scene street with long
        show ian with short
        i "I'm beat... Time to stop and get me something to eat."
        "I was close to the café, so I decided to drop by, even though Lena was away."
# Cafe
    scene cafe with long
    $ fed = "n"
    $ ian_ed_agenda = True
    if ian_job_magazine > 0 and ian_holly_dating:
        $ fian = "smile"
        $ fholly = "smile"
        show ian at lef
        show holly at rig
        with short
        h "Good afternoon!"
        show ian at lef3
        show holly at rig3
        with move
        show ed with short
        ed "Hello there, Holly! And Ian, too."
        i "How's it going, Mr. Van Dyke? Any news regarding the café?"
    else:
        show ian at lef with short
        i "Good afternoon."
        show ed at rig with short
        ed "Hello there, Ian. Here for some grub?"
        $ fian = "smile"
        i "Yeah, I could use something tasty to refill my energy."
        ed "Of course! And you don't have to sit alone today."
        "Ed pointed to a table in one of the corners of the café."
        if ian_holly_dating:
            $ fholly = "happyshy"
            show ian at lef3
            show ed at truecenter
            with move
            show holly at rig3 with short
            h "Ian!"
            $ fian = "happy"
            i "I had the feeling I'd find you here today."
            ed "Holly is one of our most loyal customers!"
            $ fian = "smile"
            i "Speaking about that, how's everything going, Mr. Van Dyke? Any news regarding the café?"
        elif v7_holly_kiss:
            $ fholly = "blush"
            show ian at lef3
            show ed at truecenter
            with move
            show holly3 at rig3 with short
            $ fian = "n"
            h "Hi..."
            i "Oh, hey Holly."
            "Things had been rather awkward with Holly since the book fair, and no wonder. I was trying to play it cool, but it was easy to see she felt uncomfortable still."
            $ fian = "smile"
            i "So... How's it going, Mr. Van Dyke? Any news regarding the café?"
        else:
            $ fholly = "smile"
            show ian at lef3
            show ed at truecenter
            with move
            show holly2 at rig3 with short
            h "Hi."
            i "Oh, hey Holly. I can't say I'm surprised to find you here."
            ed "Holly is one of our most loyal customers!"
            i "Speaking about that, how's everything going, Mr. Van Dyke? Any news regarding the café?"
    $ fed = "sad"
    $ fholly = "n"
    ed "Actually, yes..."
    ed "I've finally found someone interested in taking it from us."
    menu:
        "That's good news!":
            $ renpy.block_rollback()
            i "Hey, that's good news, isn't it?"
            if ian_charisma < 8:
                call xp_up ('charisma') from _call_xp_up_65
            ed "In a way, yeah, but... You know my wife's not thrilled about the idea of leaving the business in someone else's hands and, honestly, I feel the same way."

        "That took a while":
            $ renpy.block_rollback()
            $ fian = "n"
            i "That took a while, didn't it?"
            ed "Yeah, surprisingly long... And that's the only offer we have so far."
            ed "Not to mention my wife is not thrilled about the idea of leaving the business in someone else's hands and, honestly, I feel the same way."

        "You don't look too enthusiastic":
            $ renpy.block_rollback()
            $ fian = "n"
            i "You don't look too enthusiastic about it, even though it sounds like good news..."
            if ian_wits < 8:
                call xp_up ('wits') from _call_xp_up_66
            ed "It's not so simple. My wife's not thrilled about the idea of leaving the business in someone else's hands and, honestly, I feel the same way."

    $ fian = "n"
    hide holly
    hide holly2
    hide holly3
    show holly2 at rig3
    with short
    h "You've worked hard to build this place up. Giving it up must be like sacrificing a part of who you are..."
    if cafe_help:
        ed "It is. The offer is not that bad, though it's not great."
        $ fed = "n"
        ed "I feel bad accepting it, after everything Lena has tried in order to help us..."
        ed "I wonder if we really stand a chance to save the café ourselves. Molly says we do, but that might be just wishful thinking."
    else:
        ed "It is. But we're short on options, and we already decided this was our best one..."
        ed "It's been really hard to find someone interested, and now that we finally got an offer, well..."
        ed "It's nothing to write home about, honestly. But it's better than nothing, so..."
        ed "We'll probably take it, unless a better one comes our way soon, which is unlikely. Molly's not too happy..."
    h "Where's Mrs. Van Dyke, by the way?"
    ed "She was feeling unwell today. Yesterday too."
    if cafe_help:
        ed "It's been tough not having Lena around these days. We had come to rely on her help more than we realized, and thanks to her we've been getting more customers lately, so..."
        ed "We've been kinda busy. Molly's not young anymore, and the stress of this situation has been taking a toll on her health, too."
    else:
        ed "It's been tough not having Lena around these days. We had come to rely on her help more than we realized. It's not like we get that many customers these days anyway, but..."
        ed "This whole situation, and her age, have been taking a toll on Molly."
    "Lena had been away for a week now. She had to go back to her parents after she learned her Mom had been put in the hospital after an accident."
    if ian_lena_dating:
        "Seems I wasn't the only one who had been missing her..."
    else:
        "Her absence was really noticeable..."
    i "I'm sorry to hear about that. But at least now you've got options... even if they're not ideal."
    ed "Yeah, it beats what we had before. Still, we would really like to keep running the café."
    if cafe_help:
        $ fed = "n"
        ed "We'll see how things go these next weeks..."
    else:
        ed "Sadly that doesn't seem possible, but we need to be realistic about our financial situation."
        $ fed = "n"
    $ fed = "smile"
    ed "Anyway! I don't want to bother you younglings with our troubles! You're here to have a good time."
    ed "I'll be right back with your orders."
    hide ed with short
    show ian at lef
    show holly2 at rig
    with move
    if cafe_help:
        i "So they're gonna try and keep the café open after all. I hope they succeed."
        h "I hope so too."
    else:
        i "So they're selling the café after all. Good for them, I guess."
        h "Ed didn't look too happy about it... It's a pity, honestly."
    if ian_holly_dating:
        $ fian = "smile"
        i "On another topic... I like your skirt. It suits you."
        $ fholly = "shy"
        h "You think so? I bought it when I went shopping with Lena and Ivy..."
    elif v7_holly_kiss:
            i "..."
            $ fholly = "blush"
            hide holly2
            show holly3 at rig
            with short
            h "..."
    if ian_lena_dating:
        h "So... How's Lena? Have you talked with her recently?"
        $ fian = "smile"
        i "Yeah. She's..."
        $ fian = "n"
        i "Well, it's complicated. Turns out her Mom fell from a ladder while working at the market, and she fractured her hip."
        $ fholly = "sad"
        h "I know. She needed surgery, right?"
        i "Yeah... Thankfully the intervention went smoothly, or that's what Lena told me."
        h "Do you know when she's coming back?"
        i "Not yet... Hopefully this week, maybe next. I'll talk to her again tonight, let's see what she says."
        h "I will text her too. I'm worried about her."
        $ fian = "smile"
        i "She'll like that."
    else:
        i "How's Lena, by the way? Have you talked with her recently?"
        $ fholly = "n"
        h "Yeah. She's... Well, it's complicated."
        h "Turns out her Mom fell from a ladder while working at the market, and she fractured her hip."
        $ fian = "sad"
        i "I know about that. The doctors said she'd need surgery, right?"
        h "Yeah, they did that already and it seems everything went okay."
        $ fian = "smile"
        i "Glad to hear... I guess I'll text her later tonight, see how she's holding up."
    if v7_holly_kiss and ian_holly_dating == False:
        $ fholly = "blush"
        if ian_lena_over:
            h "Um... I'm sorry about you and Lena..."
            $ fian = "sad"
            i "Don't be... We've already been over this. I messed up, you don't have to be sorry."
            h "Still, I feel I got between you two..."
            if ian_holly_sex:
                i "Stop it, Holly. It's me who needs to apologize."
                $ fian = "blush"
                i "That night at the hotel, I shouldn't have..."
                h "It's not like you forced the situation. I wanted it too, so..."
                i "..."
                h "..."
            else:
                i "It was just a kiss, but it seems that's important to Lena. I can't blame her, though."
                i "If only I wasn't so clueless at the time... But there's no point to dwell on it. It's just the way it is."
            $ fian = "smile"
            i "Anyway, don't worry about Lena and me. We're still friends, even if things will inevitably be a bit awkward between us for a while."
            h "..."
            h "I don't know if it's okay for me to even say this, but... If you need someone to talk to or..."
            h "I mean... I don't want things to get awkward between us despite what happened..."
            i "Thanks, Holly. I appreciate that, really."
        elif ian_lena_dating:
            h "I don't know if it's okay for me to ask about this, but... Is everything alright between Lena and you after, you know...?"
            $ fian = "smile"
            i "Yeah, everything's fine. It was a bit awkward at first, but we talked it over and it seems everything's sorted out."
            h "I'm glad. I'm sorry for coming onto you that night, I..."
            i "It's okay, Holly. We've already talked about this."
            $ fian = "n"
            i "I should've been more upfront about what was going on. It was my responsibility, too."
            $ fian = "smile"
            i "And nothing really happened. It was just a harmless kiss."
            $ fholly = "sad"
            h "Yeah... Just a kiss."
        else:
            h "..."
            $ fian = "worried"
            i "..."
            "The vibe was still pretty awkward between Holly and me after what happened during the book fair."
            if ian_holly_sex:
                "We had already gone over what happened: we had sex, but it looked like Holly was expecting something more from me."
                "Something I couldn't give her in my current situation. So that episode ended up becoming a one-night stand."
            else:
                "We had already gone over what happened, and the reasons why I rejected her kisses."
                "I couldn't blame her for feeling ashamed, though..."
        "We were trying to play it cool, but it was difficult to act as if nothing happened."
        hide holly3
        show holly2 at rig
        with short
    elif ian_holly_dating and ian_lena_over:
        $ fholly = "blush"
        h "How are things between you and Lena after... you know..."
        $ fian = "n"
        i "Everything's cool. I guess it will feel a bit awkward for a while, but... You know we talked it over and sorted things out."
        $ fian = "smile"
        i "I'm just happy I didn't cause a falling out between you two. Again, sorry for putting you in such an uncomfortable position."
        $ fholly = "happyshy"
        h "Don't be. It was worth it, and Lena and I are still friends."
    elif ian_holly_dating and ian_lena_over == False and ian_lena_dating == False:
        $ fholly = "shy"
        h "I... I told her about what happened during the weekend..."
        h "Between us, I mean, at the Hotel, and..."
        $ fholly = "worried"
        $ fian = "sad"
        h "Not the details, of course! Just what we talked about and well..."
        h "A bit of what happened..."
        $ fian = "smile"
        h "I just thought I should let you know."
        i "Hey, she's your friend. I already assumed you'd talk to her about that."
        i "What did she say, by the way?"
        $ fholly = "shy"
        h "She congratulated me..."
        $ fholly = "worried"
        h "I mean, she said she was really happy for me...!"
        $ fian = "happy"
        "I couldn't help but laugh. It was adorable how flustered Holly got."
        $ fian = "smile"
        i "She's a good friend. You should keep her close."
        $ fholly = "shy"
        h "I will."
    elif ian_holly_dating == False:
        $ fian = "n"
        "I had the feeling Holly had been acting a bit distant around me lately. Like she had suddenly felt compelled to close herself off, just like when I first met her."
        if ian_go_holly and v7_holly_trip == False:
            $ fian = "sad"
            "I guess she was disappointed at me flaking on her at the book fair. It really sucked, I had wanted to go..."
            "And not only because of the fair, but to spend some time with Holly, too."
        $ fian = "smile"
        "I was glad to see her warming up to me again... Even though something still felt a bit off."
    $ v9hollytalkbook = False
    $ v9hollytalklena = False
    menu v9hollychatmenu:
        "{image=icon_friend.webp}Talk about the book contest" if ian_holly > 6 and v9hollytalkbook == False:
            $ renpy.block_rollback()
            $ v9hollytalkbook = True
            $ fian = "n"
            i "I wanted to ask you for some advice regarding the book contest..."
            $ fholly = "smile"
            h "Of course. The deadline is next month, right?"
            i "Yeah. I thought I was making good progress, but now I feel I have to rush things. I'm almost finished with it, but..."
            i "I'm not sure how to wrap it up."
            $ fholly = "n"
            h "Finishing a book often is the most difficult part... I haven't read the manuscript, and I'm afraid I don't have much time, but if you want..."
            $ fian = "smile"
            if book_scifi:
                i "No, don't worry. I just want your expert advice on how to finish my science fiction book."
                h "That one is tricky... If I were you I'd probably avoid one of those dark and hopeless endings."
                h "But I guess a whimsical, happy ending is not the most exciting one for that kind of book either."
            if book_fantasy:
                i "No, don't worry. I just want your expert advice on how to finish my fantasy book."
                h "I don't think you can go wrong with any ending you choose... Of course, having a happy ending to a fantasy story is always rewarding for the reader."
                h "But you could also try and deviate from a more whimsical ending... Do something bittersweet, something that carries heavy emotional weight."
            if book_historical:
                i "No, don't worry. I just want your expert advice on how to finish my historical book."
                h "I'm not an expert on historical fiction, but... You don't have much of a choice, right? History dictates the ending for you."
                $ fian = "n"
                i "That's right... But I wonder what angle to take."
                h "History teaches us a lot about our world and ourselves. And sometimes the most important lessons are the most painful..."
                h "You could try to extract some value from a bad thing that happened."
            $ fian = "smile"
            i "That makes sense. Thanks, Holly."
            if ian_holly_dating:
                $ fholly = "happy"
                h "It's a pleasure to help you. Don't hesitate to ask again if you need to."
            else:
                $ fholly = "smile"
                h "Don't mention it."
            jump v9hollychatmenu

        "Ask about Holly's friendship with Lena" if v9hollytalklena == False:
            $ renpy.block_rollback()
            $ v9hollytalklena = True
            i "You must miss Lena these days. It seems you two are becoming pretty close."
            if v8_holly_sex == "lena" or v8_holly_sex == "lenaivy":
                $ fholly = "worried"
                hide holly2
                show holly3 at rig
                with short
                h "Uh, yeah..."
                $ fian = "n"
                h "I mean... I guess you could certainly use those words..."
                "Holly looked really flustered all of a sudden for some reason."
                $ fian = "smile"
                i "It's clear to see she really appreciates you."
            else:
                $ fholly = "shy"
                h "I'd like to think so... She's really nice."
                i "She is. And it's clear to see she really appreciates you."
            if holly_gym:
                i "Are you still taking pole dancing classes with her?"
                $ fholly = "happyshy"
                h "Yeah... I'm awful at it, but it's kinda fun..."
                $ fian = "smile"
                i "I would've never pictured you picking up such a hobby."
                $ fholly = "shy"
                h "Me neither. I would've never even tried it if Lena hadn't invited me..."
                i "And are you getting along with Ivy, too?"
                if v8_holly_sex == "lenaivy" or v8_holly_sex == "ivy":
                    $ fholly = "blush"
                    h "Y-{w=0.3}yeah, Ivy, she's... She's really trying to teach me, um..."
                    $ fian = "n"
                    h "To teach me... Stuff."
                    h "So yeah, we get along. We're so different, but she's been very... welcoming so far."
                    "Was I missing something?"
                else:
                    h "I'd say so... She and I are very different, but she's been welcoming so far..."
                $ fian = "smile"
                i "I'm glad. I barely know her, but she looks rather intimidating."
                $ fholly = "shy"
                h "She is... And Lena would be too if she wasn't so kind."
                h "They're both incredible, so strong and sexy and confident..."
                h "I still don't understand why they even want to be friends with someone like me."
            else:
                if v8_holly_sex == "lena":
                    h "Y-{w=0.3}yeah... It seems that way..."
                    $ fian = "n"
                    i "You don't seem too thrilled about it."
                    $ fholly = "surprise"
                    h "No, no! Of course, I am! Super thrilled...!"
                else:
                    h "I appreciate her too."
                $ fholly = "blush"
                h "She's incredible. So smart, confident, and beautiful..."
                $ fian = "smile"
                h "I'm still amazed that she wants to be friends with someone like me."
            i "Maybe it's because you're a lot cooler than you think."
            $ fholly = "happyshy"
            h "Maybe I'll start believing that..."
            jump v9hollychatmenu

        "Get going":
            $ renpy.block_rollback()
            scene cafe with long
            $ fian = "smile"
            $ fholly = "smile"
            "We finished our lunch and it was time to get going."

    scene street
    show ian at lef
    show holly2 at rig
    with long
    if ian_holly_dating:
        $ fholly = "happyshy"
        h "I really enjoyed having lunch with you today..."
        $ fholly = "blush"
        hide holly2
        show holly3 at rig
        with short
        h "Um... I haven't forgotten what you told me about your... stance on relationships, and I don't want to pester you..."
        $ fian = "n"
        h "I mean, I know we agreed to take it slow and all that, so..."
        h "I was just wondering when... If you'd like us to hang out. It doesn't have to be a date or anything..."
        $ fian = "smile"
        i "Relax, Holly. Of course, I'd like to hang out with you."
        i "In fact, I was thinking about inviting you tomorrow to my place. We can watch that movie we talked about if you want."
        $ fholly = "shy"
        h "I'd like that."
        i "Tomorrow afternoon it is, then."
    if ian_job_magazine > 0:
        i "Now, let's go back to work. I can't wait to be done with it."
    else:
        i "Well, I need to get back to work. Pizzas don't deliver themselves, you know."
        h "Good luck with that... And with the book. I'm sure you'll make an impression at the contest."
        if ian_holly_dating:
            i "I hope you're right. See you tomorrow!"
        else:
            i "I hope you're right. See you around, Holly!"
    stop music fadeout 2.0
    scene street with long
    pause 0.5

## GYM AND JEREMY TALK ################################################################################################################################################################################################################
    scene gym with long
    play music "music/jeremys_theme.mp3" loop
    pause 0.5
    $ fian = "worried"
    $ jeremy_look = 2
    if jiujitsu > 1:
        $ ian_look = "gi"
        $ fjeremy = "happy"
        show wen at rig
        show ian at lef
        with short
        if ian_job_magazine > 0:
            wen "Good! Today was a good session, well done."
            "I was lying on the mat, with no strength left to even stand up, fighting to catch my breath back."
            i "Are you kidding? I almost died back there... again."
        else:
            wen "What's the matter? You're in bad shape today!"
            "I was lying on the mat, with no strength left to even stand up, fighting to catch my breath back."
            i "I've spent the whole day on a bicycle. I don't have much energy left...!"
            i "I almost died back there... again."
        hide wen
        show wensmile at rig
        if tournament:
            wen "This is how real warriors are made! You need to constantly test your limits, especially if you're serious about the tournament!"
        else:
            wen "This is how real warriors are made! You need to constantly test your limits!"
        if v7_effort_gym:
            if ian_athletics < 10:
                call xp_up('athletics') from _call_xp_up_67
            wen "And you've been putting in the hours, lately!"
        show wensmile at rig3
        show ian at lef3
        with move
        show jeremy with short
        j "I see you're enjoying your jiu-jitsu classes, Ian!"
        wen "You will enjoy them too from now on!"
        $ fjeremy = "n"
        $ fian = "n"
        j "What do you mean?"
        show ian at left
        show jeremy at lef
        show wensmile at right
        with move
        hide wensmile
        show wen at right
        show yuri at rig
        with short
    else:
        $ ian_look = 7
        $ fjeremy = "smile"
        show ian at lef3
        show jeremy
        show yuri at rig3
        with short
        if ian_job_magazine > 0:
            yuri "Good work today, guys. Let's wrap it up."
            "I was lying on the mat, dizzy and trying to catch my breath back."
            i "This keeps getting more and more intense..."
        else:
            yuri "What's the matter, Ian? You're in bad shape today!"
            "I was lying on the mat, dizzy and trying to catch my breath back."
            i "I've spent the whole day on a bicycle. I don't have much energy left...!"
            i "And this keeps getting more and more intense..."
        yuri "I need to make sure you guys are ready for the tournament, so we need to go at it hard."
        if tournament == False:
            i "And I need to remind you I'm not participating. I'm just doing this as a hobby!"
        if v7_effort_gym:
            if ian_athletics < 10:
                call xp_up('athletics') from _call_xp_up_68
            yuri "You're certainly putting in the hours, there's no denying that!"
        $ fjeremy = "flirt"
        j "I can't wait to get in there and slay the competition! I feel so sharp lately!"
        $ fian = "n"
        show ian at left
        show jeremy at lef
        show yuri at rig
        with move
        show wen at right with short
        wen "Your striking is not bad, yeah, and those long limbs surely help..."
        wen "But that won't be much use to you when training with me!"
        $ fjeremy = "n"
        j "With you? What do you mean?"
        yuri "You'll train with Wen from now on, too."
        $ fjeremy = "sad"
        j "What?"
    yuri "You need to train some grappling if you want to have any chance at the tournament."
    $ fjeremy = "sad"
    j "What, really? My striking is good enough, I don't need grappling!"
    yuri "You need to learn how to defend against it, at least."
    $ fjeremy = "flirt"
    j "Well, I'm not easy to take down..."
    hide wen
    show wensmile at right
    with short
    show wensmile at rig
    show yuri at right
    with move
    $ fjeremy = "surprise"
    j "Whoa!{w=0.3}{nw}"
    play sound "sfx/throw.mp3"
    hide jeremy with vpunch
    $ fian = "happy"
    hide yuri
    show yurismile at right
    with short
    "Wen snuck behind Jeremy, picked him up, and dropped him on the mat."
    wen "Are you sure about that?"
    $ fjeremy = "n"
    show jeremy at lef with short
    j "Not fair, that was a surprise attack..."
    hide yurismile
    show yuri at right
    yuri "Anyway, both Wen and I will train you from now on. You too, Ian."
    if tournament == False:
        $ fian = "n"
        i "Sure, but remember Jeremy's the one entering the tournament, not me."
        wen "Yeah, yeah, don't worry. We won't be as hard on you."
        $ fian = "smile"
        i "Great. I can't wait to see Jeremy suffer."
        j "Dude..."
    yuri "We'll start next Thursday. Rest up and eat well!"
# jeremy talk
    stop music fadeout 2.0
    scene streetnight with long
    $ ian_look = 1
    $ jeremy_look = 3
    $ fian = "n"
    $ fjeremy = "n"
    show jeremy at rig
    show ian at lef
    with short
    play music "music/normal_day.mp3" loop
    j "I have to train with Wen now? That sucks..."
    if jiujitsu > 1:
        i "It's tough, but I'm really enjoying learning jiu-jitsu."
    else:
        i "Why are you so against it? I mean, I prefer kickboxing too, but..."
    j "It's just boring! I hate watching wrestlers fight in MMA matches."
    $ fjeremy = "flirt"
    j "What's cool is the {i}ba-bap-boom!/{/i} and {i}k-pow!k-pow!{/i} that puts guys to sleep, you know."
    "He accompanied his vocal effects with some shadow boxing."
    i "Whatever. Doesn't look like you have too much of a choice in this."
    $ fjeremy = "n"
    j "Yeah..."
# cindy
    if ian_cindy_sex:
        j "So, about that issue that I don't like to ask about... You know..."
        $ fian = "sad"
        i "Cindy."
        j "Yeah, well... No news on that front, right?"
        menu:
            "{image=icon_friend.webp}Confide in Jeremy" if ian_jeremy > 5:
                $ renpy.block_rollback()
                $ fian = "worried"
                i "No. It's obvious she hasn't told Wade, but she never responded to my texts."
                i "I stopped trying to talk to her a couple of weeks ago, but I guess we'll have to at some point..."
                j "She's not giving you any chance at sorting things out, huh?"
                $ fian = "n"
                i "No... I'd say she's running away from what happened, but just ignoring it is making my anxiety levels pretty damn high."
                j "It seems you'll have to force the issue or she'll keep avoiding it."
                i "We'll see what happens. We're bound to meet at some point."
                j "Good luck with that, you're gonna need it."

            "No news":
                $ renpy.block_rollback()
                $ fian = "n"
                i "No. No news."
                j "Okay. No news is good news. That's all I wanted to know."

# alison call
    if ian_alison_dating:
        play sound "sfx/ring.mp3"
        i "Wait, I'm getting a call. It's Alison..."
        $ fjeremy = "smile"
        hide ian
        show ian_phone at lef
        show phone_alison at left
        with short
        a "Hey! Can you talk?"
        i "Sure, what's up?"
        a "It's about that escapade we talked about. I have a few days off this week, and I have the perfect plan..."
        if ian_alison_like == 2:
            $ fian = "smile"
            i "Sure! Tell me about it."
        else:
            i "Oh, that's right..."
            "Alison wanted to spend a weekend with me someplace, just the two of us..."
        a "The idea is to take the train to this city nearby. I've already looked up the hotel and it's pretty cheap."
        a "The stay plus the train tickets will cost you {image=icon_money.webp}{image=icon_money.webp}."
        a "We would leave Thursday morning and come back Saturday around lunchtime..."
        hide phone_alison
        show phone_alison_sad at left
        a "I know it's not ideal, but this Saturday is my grandmother's birthday, and my family won't let me skip it."
        a "As if I didn't spend enough time with them already..."
        # if ian_alison_like == 2:
        #     $ v9_alison_trip = True
        #     if ian_job_magazine == 2:
        #         if ian_defy_minerva or ian_minerva_sex:
        #             i "I have to be at the office on Thursdays or Fridays... But I doubt my boss will object to me taking a couple of days off."
        #         else:
        #             $ fian = "n"
        #             i "I have to be at the office on Thursdays or Fridays... But I guess I can ask for a couple of days off."
        #     elif ian_job_magazine == 1:
        #         i "It's no problem for me. I don't have to go to the office on Thursdays or Fridays."
        #     else:
        #         i "It's no problem for me. It's not like I have a nine-to-five job nowadays."
        #     hide phone_alison_sad
        #     show phone_alison_smile at left
        #     a "Great! Let's meet at the train station, Thursday morning."
        #     $ fian = "smile"
        #     i "I'll be there."
        # else:
        menu:
            "{image=icon_pay.webp}{image=icon_pay.webp}Go on a trip with Alison" if ian_money > 1:
                $ renpy.block_rollback()
                $ v9_alison_trip = True
                $ fian = "smile"
                i "Sounds like a good plan. I'm in."
                call money(-2) from _call_money_2
                hide phone_alison_sad
                show phone_alison_smile at left
                if ian_job_magazine == 2:
                    if ian_defy_minerva or ian_minerva_sex:
                        i "I have to be at the office on Thursdays or Fridays, but I doubt my boss will object to me taking a couple of days off."
                    else:
                        $ fian = "n"
                        i "I have to be at the office on Thursdays or Fridays... But I guess I can ask for a couple of days off."
                        $ fian = "smile"
                elif ian_job_magazine == 1:
                    i "And the timing works for me since I don't have to go to the office on Thursdays or Fridays."
                else:
                    i "And the timing works for me since I don't have a nine-to-five job nowadays..."
                a "Great! Let's meet at the train station, Thursday morning."
                i "Sure. I'll be there."

            "Decline the offer":
                $ renpy.block_rollback()
                $ fian = "n"
                if ian_alison_like > 1:
                    $ ian_alison_like = 1
                i "I'm sorry, Alison, but right now it's not good timing for me."
                i "You know I'm a bit short on money, and the literary contest is around the corner. I need to focus on my book..."
                if ian_alison > 6:
                    call friend_xp('alison', -1) from _call_friend_xp_79
                    $ ian_alison = 6
                a "I see... Yeah, of course."
                hide phone_alison_sad
                show phone_alison at left
                a "Too bad! It would've been cool."
                i "Yeah, some other time, maybe."
                a "I'll guess I'll try to find another unsuspecting victim who wants to tag along then!"
                $ fian = "smile"
                i "Good luck with that. Bye, Alison."

        hide phone_alison
        hide phone_alison_sad
        hide phone_alison_smile
        hide ian_phone
        show ian at lef
        with short
        if v9_alison_trip:
            $ fjeremy = "flirt"
            j "So, you're gonna spend a few days locked in a hotel room with Alison, fucking like rabbits."
            $ fian = "happy"
            i "We'll probably do other things too, but yeah, that sounds about right."
            $ fjeremy = "happy"
            if alison_jeremy:
                j "Lucky you... I only fucked her twice, but it was pretty great. She has some major boobage."
            else:
                j "Lucky you, you get to enjoy her major boobage."
            if alison_jeremy_block:
                i "And it's all mine."
            else:
                j "Say, wouldn't it be awesome if we double-teamed her?"
                $ fian = "worried"
                i "What?"
                j "Don't tell me you've never wanted to spit-roast a chick and high-five your bro while giving it to her!"
                "Jeremy illustrated the scene with some explicit mimic, laughing."
                menu:
                    "{image=icon_friend.webp}You want to have a threesome?" if ian_jeremy > 5:
                        $ renpy.block_rollback()
                        $ alison_jeremy_3some = 1
                        $ fian = "n"
                        i "You want us to have a threesome with Alison?"
                        $ fjeremy = "smile"
                        j "Well, it's one of those things I'd like to do at least once in a lifetime! And I have the impression Alison would be up for it."
                        $ fjeremy = "flirt"
                        if alison_jeremy:
                            j "She's fucked us both already, after all!"
                        else:
                            j "I think I would've probably boned her if it weren't for you!"
                        i "You have the craziest ideas."
                        $ fjeremy = "smile"
                        j "It's not so crazy if it's doable. And as I said, I think she'd be up for it."
                        i "She's rather naughty, yeah..."
                        j "It's just an idea, but don't you think it could be fun? And there's no better guy to share that experience with than you, bro."
                        j "No homo."
                        if ian_chad > 3:
                            $ fian = "smile"
                            i "Yeah, no homo."

                    "Get out of here!":
                        $ renpy.block_rollback()
                        $ fian = "serious"
                        i "Get the hell out of here!"
                        j "Come on! It's one of those things you have to do at least once in a lifetime!"
                        if ian_jeremy > 5:
                            if ian_charisma > 3:
                                $ fian = "happy"
                                if alison_jeremy:
                                    i "Are you jealous that she prefers hooking up with me?"
                                else:
                                    i "Are you that desperate to have a piece of Alison?"
                                    $ fjeremy = "smile"
                                    j "Ha ha, of course not. But I'm sure she'd be into it."
                                    i "Didn't she reject your advances?"
                                $ fjeremy = "n"
                                j "It's not always about competition, man."
                            $ fian = "smile"
                            if ian_lust > 4:
                                i "Swap yourself with another girl and then I'm in."
                            else:
                                i "I enjoy having Alison's full attention, thank you."
                            $ fjeremy = "happy"
                            j "What, you're not confident in your own sexuality?"
                            i "I am, it's just that I don't find it appealing to have to see you naked while I fuck a girl!"
                            j "It's not about the appeal, it's about the brotherhood!"
                            i "Whatever bro, it's just not for me."
                        else:
                            i "Don't be ridiculous..."
                            $ fian = "n"
                            i "Sorry to disappoint you, but I have no intention of inviting you to bed with me and Alison."
                            $ fjeremy = "smile"
                            j "Jeez, it was just a joke. Don't be so grumpy, you remind me of Perry."

        else:
            j "You passed on her invitation? Why, dude?"
            $ fian = "n"
            i "I'm pretty busy, and I just don't feel like it, to be honest."
            $ fjeremy = "happy"
            j "You don't feel like spending a few days locked in a hotel room with Alison, fucking like rabbits?"
            $ fian = "smile"
            i "Yeah. Too much of one thing and you'll get sick of it."
            $ fjeremy = "flirt"
            j "Alright, but then don't act surprised when someone else takes up the chance you're passing up."
            if alison_jeremy_block:
                $ fian = "n"
                i "You're still going on about that? There are other women aside from Alison, you know?"
                $ fjeremy = "smile"
                j "There are also other guys aside from me. She'll find someone if you don't show interest."
            i "Well, she's not my girlfriend. It's not like I have any obligation toward her, and she can do whatever she pleases."
            $ fjeremy = "happy"
            j "Remember you said that!"
# end talk
    $ fjeremy = "smile"
    if ian_jeremy > 7:
        j "We need to hang out soon, by the way! It's been far too long since we went out partying."
    elif ian_jeremy > 3:
        j "We should hang out soon, by the way! It's been far too long since we went out partying."
    else:
        j "Anyway, we could hang out one of these days. It's been far too long since we went out partying."
    $ fian = "smile"
    i "Sure, but you're always busy on weekends."
    if alison_jeremy and ian_alison_dating == False:
        j "You can party on weekdays too, you know that?"
        i "Yeah, when you work during the weekends."
        j "It's not so bad. And I can take a day off or two from time to time."
        $ fjeremy = "happy"
        j "For example, I'm going on a trip with Alison from Thursday to Saturday."
        $ fian = "n"
        i "That's right... She talked about wanting to do something like that."
        $ fian = "smile"
        i "I didn't think you were the kind of guy to go on a romantic weekend trip with a girl."
        $ fjeremy = "smile"
        j "My plan was to spend the whole day fucking without even leaving the hotel, but I guess I will have to behave..."
        j "My friend Billy wanted to join us too. He even offered to pay for everything, that dude is loaded!"
    else:
        j "I know, but I'll find the time. You need to meet my dude Billy, too!"
    i "Billy? That's your childhood friend you told me about, right? The one who owned a theme park?"
    j "His father did, or something like that. We would always get in for free."
    j "But yeah, he's the one. Really cool guy. I'll introduce you next time."
    if v9_alison_trip == False and alison_jeremy:
        if louise_jeremy and v7_bbc == "lena":
            j "Anyway, I need to get going. I'm meeting Louise..."
        else:
            $ fjeremy = "flirt"
            j "Anyway, I need to get going. I'm meeting with this girl..."
            i "Ivy?"
            $ fjeremy = "n"
            j "No... She's playing hard to get again."
            i "Didn't she suck your dick already?"
            j "Yeah, and I thought everything would play smoothly after that, but I haven't managed to get a proper date with her yet..."
            $ fjeremy = "smile"
            j "But I can see she likes teasing me, so the game isn't over. I know I'll get the prize if I keep playing!"
            j "Anyway, gotta go!"
        i "Sure. See you."
    else:
        i "Well, time to get going. Later, Jeremy."
        j "Good going, bro!"

## HOME PERRY ##############################################################################################################################################################################
    play sound "sfx/door_home.mp3"
    scene ianhomenight with long
    $ fperry = "n"
    $ fian = "n"
    show ian at lef with short
    i "Hey."
    show perry at rig with short
    p "{i}W--{w=0.5}wassup{/i}."
    "Perry was on the couch playing a video game, unsurprisingly."
    i "Hard day at the office?"
    p "Yup."
    $ fian = "serious"
    i "Watch out, don't accidentally work yourself to death."
    $ fperry = "meh"
    p "Someone's in a bad m--{w=0.5}mood today."
    i "All I'm saying is..."
    $ fian = "n"
    i "Wait, now that I think of it..."
    i "You could help at the café. They're in need of someone."
    $ fperry = "meh"
    p "What? Why?"
    i "Lena's away, so they need someone to fill in for her until she comes back."
    p "Ugh, waitressing. Not my thing."
    p "I'll p--{w=0.5}p--{w=0.3}pass."
    if ian_charisma > 4 and ian_perry > 5:
        menu:
            "{image=icon_charisma.webp}{image=icon_friend.webp}Convince Perry":
                $ renpy.block_rollback()
                $ cafe_perry = True
                $ fian = "worried"
                i "Dude, really? It's only for a few days. You could do some volunteering from time to time."
                hide perry
                show perry2 at rig
                with short
                p "Volunteering? I won't even get paid?"
                $ fian = "disgusted"
                i "Dude, really?"
                i "They're an elderly couple, working hard to keep afloat the business they built together so lovingly to the point of Molly putting her health in jeopardy."
                $ fperry = "surprise"
                i "And on top of that, you deny them a bit of help unless they pay you? That's really low."
                $ fperry = "mad"
                p "Don't t--{w=0.5}try to guilt-trip me, you fucker! Why don't {i}YOU{/i} help them?"
                $ fian = "serious"
                i "Because I'm busy? I need to work to pay {i}YOU{/i} rent and I'm trying to write a goddamn book."
                $ fian = "n"
                i "On the other hand, you have time to spare."
                $ fperry = "serious"
                p "I do s--{w=0.5}stuff too, you know..."
                i "Too bad. I told Emma about the idea and she thought it was really cool of you."
                $ fperry = "sad"
                p "She did?"
                $ fian = "smile"
                i "Of course. You know how much she loves selfless behavior. She's an activist herself, after all."
                p "..."
                i "..."
                i "So, will you {i}at least{/i} drop by and see if you can help them serve tables for a couple of hours?"
                hide perry2
                show perry at rig
                with short
                p "Alright."
                if perry_emma < 2:
                    i "Awesome. Good night, buddy."

            "Leave it be":
                $ renpy.block_rollback()
                $ fian = "serious"
                i "Yeah, what a surprise. Never mind I said anything."
                $ fperry = "n"
                p "Okay."
                i "I'll be in my room."
                if ian_perry > 4:
                    call friend_xp('perry', -1) from _call_friend_xp_80
                    $ ian_perry = 4
                    pause 0.5
    else:
        menu:
            "Convince Perry":
                $ renpy.block_rollback()
                $ fian = "serious"
                i "Dude, really? It's only for a few days. You could do some volunteering from time to time."
                hide perry
                show perry2 at rig
                with short
                p "Volunteering? I won't even get paid?"
                i "That's what volunteering means. They can't afford to pay you in their situation. They can barely keep employing Lena."
                $ fperry = "sad"
                p "Hard p--{w=0.5}pass, dude..."
                i "That's a new low for you..."
                $ fperry = "serious"
                p "And why don't {i}YOU{/i} do it since you're so h--{w=0.5}high and mighty?"
                i "Because I'm busy? I need to work to pay {i}YOU{/i} rent and I'm trying to write a goddamn book."
                p "And I'm doing stuff too! I'm helping Wade t--{w=0.5}train for the Visual Fighter tournament..."
                i "That's bullshit and you know it."
                if ian_perry > 4:
                    call friend_xp('perry', -1) from _call_friend_xp_81
                    $ ian_perry = 4
                hide perry2
                show perry at rig
                with short
                p "Whatever, dude. Just s--{w=0.5}stop trying to convince me."
                $ fian = "n"
                i "I don't get what's with you, man. It has to be beyond simple laziness..."
                p "I said stop it!"
                i "Okay, okay, I'm going to my room."

            "Leave it be":
                $ renpy.block_rollback()
                $ fian = "serious"
                i "Yeah, what a surprise. Never mind I said anything."
                $ fperry = "n"
                p "Okay."
                i "I'll be in my room."
                if ian_perry > 4:
                    call friend_xp('perry', -1) from _call_friend_xp_82
                    $ ian_perry = 4
                    pause 0.5

    if perry_emma > 1:
        if cafe_perry:
            $ fian = "evil"
            i "And speaking of Emma..."
            $ fperry = "meh"
        else:
            show ian at left with move
            $ fian = "n"
            i "..."
            show ian at lef3 with move
            $ fperry = "meh"
            p "What?"
            i "So..."
            show ian at lef with move
            $ fian = "evil"
            i "How's it going with Emma?"
        hide perry
        show perry2 at rig
        with short
        p "Again? I already t--{w=0.5}told you everything you needed to know about that!"
        $ fian = "smile"
        i "Are you kidding? All you said was that you two \"hooked up\" and that's it!"
        i "Have you talked to her since?"
        p "Yeah."
        i "So?"
        p "So nothing, it's like usual."
        $ fian = "worried"
        i "\"Like usual\"?"
        $ fian = "happy"
        i "What about the fact you boned her?"
        $ fperry = "serious"
        p "I didn't b--{w=0.5}b--{w=0.5}b--{w=0.5}bone her!"
        i "Alright, alright. You made sweet love to her."
        p "Shut your trap!"
        $ fperry = "meh"
        p "I didn't {i}bone{/i} her nor made {i}s--{w=0.5}sweet love{/i} t--{w=0.5}to her."
        $ fian = "sad"
        i "You didn't? But what I saw..."
        p "You're a c--{w=0.5}creep, you know that?"
        $ fian = "disgusted"
        i "Hey, it's not like I walked in on you on purpose!"
        $ fian = "n"
        i "You're lucky I didn't disturb you two, so you could keep doing... whatever you two were doing."
        hide perry2
        show perry at rig
        with short
        p "We just... made out, and I f--{w=0.5}fingered her."
        $ fian = "n"
        i "Oh, okay. Now that I've heard it I'm not sure I wanted the details."
        $ fian = "worried"
        i "But you didn't have sex with her?"
        hide perry
        show perry2 at rig
        with short
        p "Such a phallocentric way to look at s--{w=0.5}sex. I expected more from you."
        p "Sex can be sex even if a d--{w=0.5}dick's not involved, you know?"
        $ fian = "serious"
        i "You know what I mean. You didn't get to stick the sword in the stone."
        if ian_wits > 6:
            $ fian = "n"
            i "Wait, shouldn't this metaphor be the other way around...?"
        $ fperry = "meh"
        hide perry2
        show perry at rig
        with short
        p "I didn't {i}f--{w=0.5}fuck her{/i}, is what I'm trying to get at."
        $ fian = "n"
        i "Alright... And have you two talked about meeting again to maybe, you know... Seal the deal?"
        p "No... We haven't really mentioned what happened. Everything's b--{w=0.5}business as usual."
        i "And is there a particular reason you're actively avoiding the subject?"
        $ fperry = "sad"
        p "I guess if she's not bringing it up it's because she doesn't w--{w=0.5}want to."
        p "Maybe that only happened because she was drunk, or s--{w=0.5}stoned..."
        p "Maybe she's even forgotten it... Or regrets it!"
        menu:
            "{image=icon_wits.webp}/{image=icon_charisma.webp}You're exaggerating" if ian_wits > 5 or ian_charisma > 5:
                $ renpy.block_rollback()
                $ perry_emma = 3
                $ fian = "smile"
                i "Do you even listen to yourself? You're exaggerating."
                i "If she made out with you it's because she was feeling like it, same as you."
                $ fperry = "serious"
                hide perry
                show perry2 at rig
                with short
                p "But what am I supposed to t--{w=0.5}tell her?"
                p "\"Hey, when are we gonna fuck?\"."
                $ fian = "n"
                i "Dude, it's not so hard. Just say something like..."
                i "\"Hey, I've been thinking about last night. I hope it was as fun for you as it was for me.\""
                $ fperry = "meh"
                hide perry2
                show perry at rig
                with short
                p "Are you sure?"
                $ fian = "happy"
                i "Trust me, I'm a writer!"
                p "..."
                p "Alright."
                i "Good luck!"

            "You'll have to find out":
                $ renpy.block_rollback()
                $ fian = "smile"
                i "Well, the only way to find out is if you bring up the subject."
                $ fperry = "meh"
                p "Let me h--{w=0.5}handle this my own way... And don't tell Emma about this conversation!"
                i "Alright, alright. I won't get involved."

            "I can't help you":
                $ renpy.block_rollback()
                i "Look, dude, I can't help you with everything. You need to be the one to talk to her about this."
                $ fperry = "serious"
                p "I didn't ask for your help and I don't need you to t--{w=0.5}talk to Emma about this."
                p "I'll handle things my own way."
                i "Alright."

    stop music fadeout 2.0
    scene ianroomnight with long
    play sound "sfx/door.mp3"
    $ fian = "n"
    $ ian_look = 2
    show ian with short
    if perry_emma == 3:
        i "He'd be lost without me..."
    elif perry_emma == 2:
        i "I'm afraid his chances are slim without my help..."
        if cafe_perry:
            i "I can't believe I convinced him to help at the café. Maybe he'll even do it."
        else:
            i "I can't believe that lazy fuck refused to help at the café."
    else:
        if cafe_perry:
            i "I can't believe I convinced him... Maybe he'll even do it."
        else:
            i "I can't believe that guy and his lazy ass..."
    play music "music/normal_day2.mp3" loop
    "I sat in front of my computer but picked up my phone."
# cindy
    if v7_cindy_kiss:
        show ian at left with move
        show v9_cindy_txt with short
        i "..."
        $ fian = "worried"
        i "I wonder what the hell's up with this whole situation..."
        "I hadn't heard from Cindy in weeks. And she never answered my messages."
        i "Should I try again...?"
        $ fian = "n"
        i "No, not a good idea. But she must know we'll need to discuss what happened at some point, right?"
        $ fian = "sad"
        hide v9_cindy_txt
        show v8_cindy
        with short
        i "..."
        "It was clear she hadn't told Wade. If that happened, I would know within hours..."
        "And I would find myself in massive trouble with Wade and Perry..."
        if ian_cindy_sex:
            "Jeremy had busted us, and so far was guarding my back, even if I could tell he wasn't too pleased with the situation."
        i "And what would everyone else think? Nothing good, that was for sure..."
        i "Why did I get myself in this mess?"
        if v7_cindy_pics > 0:
            hide v8_cindy
            show v7_cindy_pic1
            with short
            i "..."
            hide v7_cindy_pic1
            show v7_cindy_pic2
            with short
            i "..."
            hide v7_cindy_pic2
            show v7_cindy_pic3
            with short
            i "..."
            $ fian = "blush"
            if v7_cindy_pics == 2:
                hide v7_cindy_pic3
                show v7_cindy_pic4
                with short
            i "This is why, of course."
        elif v5_cindy_shoot or ian_cindy_model:
            hide v8_cindy
            show v6_cindy_pic1
            with short
            i "..."
            hide v6_cindy_pic1
            if v5_cindy_nude > 0 or v5_cindy_shoot == False:
                show v6_cindy_pic2b
            else:
                show v6_cindy_pic2a
            with short
            i "..."
            $ fian = "blush"
            if v6_cindy_pg == 4:
                hide v6_cindy_pic2a
                hide v6_cindy_pic2b
                show v6_cindy_pic3
                with short
            i "This is why, of course."
        $ fian = "sad"
        i "Damn you, Cindy..."
        scene ianroomnight
        show ian at left
        with short
        $ fian = "n"
        show ian at truecenter with move
        i "I can't get this out of my head, but it's no use looming over it right now."
        i "Let's tend to other stuff..."
# call lena (dating)
    if ian_lena_dating:
        $ fian = "smile"
        i "I told Lena I'd call her tonight."
        if v7_cindy_kiss:
            $ fian = "sad"
            i "I don't know if it will distract me or make me feel guiltier..."
            $ fian = "serious"
            i "Get it together, Ian."
        hide ian
        show ian_phone
        with short
        i "..."
        show phone_lena_smile at left with short
        l "Hello, Ian!"
        $ fian = "smile"
        i "How are you doing today?"
        hide phone_lena_smile
        show phone_lena_sad at left
        l "Ugh, don't get me started... My mom's been in the shittiest of moods..."
        $ fian = "sad"
        i "She's still grumpy?"
        l "Yeah. I thought when we got home she might cheer up, but it's been the opposite."
        l "She's so used to doing everything herself, not being able to now makes her incredibly frustrated."
        hide phone_lena_sad
        show phone_lena_serious at left
        l "This means we have to deal with her awful temper..."
        i "I imagine it must not be a fun time breaking your hip."
        l "Yeah, no, it isn't. Turns out that's not the real problem, though."
        hide phone_lena_serious
        show phone_lena_sad at left
        i "What do you mean?"
        l "They found out she's anemic. That's the reason she fainted and fell from the ladder."
        i "Is it a serious issue?"
        l "It doesn't seem to be... Still, she won't be able to work for a long, long time..."
        i "And what about your Dad? You told me he was also feeling a bit under the weather..."
        hide phone_lena_sad
        show phone_lena at left
        l "Oh, no, he's fine. It was just a cold, but his immune system is still a bit weak, so it was a bit worse than normal."
        l "He's making a full recovery, though."
        $ fian = "smile"
        i "Some good news, at least!"
        l "Anyway, I've bored you long enough with my family drama. How are you?"
        menu:
            "{image=icon_love.webp}I miss you" if ian_lena_love:
                $ renpy.block_rollback()
                $ v9_lena_call = 2
                i "You know... I miss you."
                if lena_ian_love:
                    hide phone_lena
                    show phone_lena_shy at left
                    if ian_lena < 12:
                        call friend_xp('lena', 1) from _call_friend_xp_83
                    l "Really?"
                    i "You have no idea."
                    l "I miss you too... I can't wait to get back."
                    if ian_will == 0:
                        call will_up() from _call_will_up_50
                    i "And when will that be?"
                else:
                    hide phone_lena
                    show phone_lena_blush at left
                    l "Uh, oh... I see."
                    $ fian = "n"
                    i "Did I say something weird?"
                    l "No, no... I miss you too."
                    $ fian = "smile"
                    i "So, when are you coming back?"
                hide phone_lena_blush
                hide phone_lena_shy
                show phone_lena_smile at left
                l "This Friday, if everything goes as planned."

            "I can't wait to see you":
                $ renpy.block_rollback()
                $ v9_lena_call = 1
                i "Well, I can't wait to see you. When are you coming back?"
                if ian_lust < 5:
                    call xp_up('lust') from _call_xp_up_69
                hide phone_lena
                show phone_lena_shy at left
                l "This Friday, in theory. I can't wait to be back, either."

            "I'm fine":
                $ renpy.block_rollback()
                i "I'm fine. Wondering when you'll come back."
                hide phone_lena
                show phone_lena_smile at left
                l "This Friday, if everything goes as planned."

        l "Do you want to meet?"
        if v9_alison_trip:
            $ fian = "sad"
            i "Oh, this Friday... I'll be away until Saturday."
            hide phone_lena_smile
            hide phone_lena_shy
            show phone_lena at left
            l "How come?"
            $ fian = "n"
            i "I'm taking a couple of days off and visiting a nearby city with Alison..."
            l "Oh, that sounds cool."
            i "We can meet Saturday for lunch if you want."
            l "Sure..."
        else:
            i "Of course."
            l "Awesome..."
        lm "Lena! Come here, please!" with vpunch
        hide phone_lena
        hide phone_lena_smile
        hide phone_lena_shy
        show phone_lena_worried at left
        $ fian = "worried"
        lm "Help me get up from the couch! Your dad can't on his own!"
        hide phone_lena_worried
        show phone_lena_serious at left
        l "Wait a minute, Mom! I'm on the phone!"
        lm "Dinner's ready! It'll get cold, come on, darling!"
        l "Sheesh. I need to go."
        hide phone_lena_serious
        show phone_lena at left
        if ian_lena_sex:
            if v9_alison_trip:
                l "Enjoy your trip! Oh, and by the way..."
            else:
                l "See you on Friday! Oh, and by the way..."
            hide phone_lena
            show phone_lena_flirt_shy at left
            l "I still have your blue shirt, and I'm making good use of it."
            play sound "sfx/sms.mp3"
            hide ian_phone
            show ian
            with short
            show ian at right with move
            $ fian = "shy"
            show v9_lena_selfie1_comp with short
            $ ian_lena_pics.append("v9_lena_selfie1_comp")
            $ lena_lena_pics.append("v9_lena_selfie1_comp")
            "I took a look at the message Lena just sent. A picture."
            hide ian
            show ian_phone at right
            with short
            i "Now it's yours, forever. It looks so much better on you."
            if lena_ian_love:
                l "I like wearing it when I go to sleep. It makes me feel not so alone in bed..."
                i "I guess it's not a bad placeholder, but a hug would be better."
                l "I can't wait to get one of those. See you, Ian."
            else:
                l "See you."
        else:
            if v9_alison_trip:
                l "Enjoy your trip!"
            else:
                l "See you on Friday!"
            $ fian = "smile"
        i "Bye..."
        scene ianroomnight with long
        show ian with short
        if ian_lena_love:
            i "She's lovely..."
            $ fian = "sad"
            i "She seems to be going through a really hard time, though."
        else:
            $ fian = "sad"
            i "Lena seems to be going through a really hard time..."
        i "I wonder if I could help her somehow."
# text lena (over/no dating)
    else:
        i "I told Holly I would text Lena..."
        if ian_lena_over:
            i "And I really want to know how she's doing. I've been keeping some distance, but I still care about her..."
        nvl clear
        i_p "{i}Hey Lena, how are you doing? Is your mom feeling better?{/i}"
        i "Sent."
        play sound "sfx/sms.mp3"
        l_p "{i}Hi, Ian! {image=emoji_smile.webp} Things are a bit complicated over here, but I'm trying to take it one step at a time {image=emoji_disgust.webp}{/i}"
        $ fian = "sad"
        i_p "{i} Damn... I'm sorry to hear that{image=emoji_sad.webp} Holly told me she had the surgery done already, and that everything went okay.{/i}"
        l_p "{i}Yeah. In fact, today she was discharged and she's back home.{/i}"
        $ fian = "n"
        i_p "{i}She must be happy about that.{/i}"
        l_p "{i}If only... She's so used to doing everything herself, not being able to now makes her incredibly frustrated.{/i}"
        l_p "{i}Which means we have to deal with her awful temper {image=emoji_mad.webp}{/i}"
        i_p "{i}Damn, sorry to hear that.{/i}"
        l_p "{i}I don't want to bother you with my family drama {image=emoji_ups.webp}{/i}"
        menu:
            "{image=icon_love.webp}I'm here to help" if ian_lena_over:
                $ renpy.block_rollback()
                $ v9_lena_call = 2
                $ fian = "blush"
                i_p "{i}I'm not sure if I'm in any position to say this, but I'm here to help if you need me.{/i}"
                l_p "{i}Thanks, Ian {image=emoji_smile.webp} So far I'm managing, so don't worry!{/i}"
                i_p "{i}I know, but I just wanted you to know that I'm willing to lend you a hand.{/i}"
                l_p "{i}I'll take you up on that if I find myself in need!{/i}"
                $ fian = "n"
                i_p "{i}When are you planning to come back, by the way?{/i}"
                l_p "{i}This Friday, hopefully! See you then if you want {image=emoji_smile.webp}{/i}"
                i_p "{i}Sure, call me when you get back.{/i}"
                l_p "{i}I need to go, my mom is screaming for help downstairs {image=emoji_ups.webp}{/i}"
                i_p "{i}Take care.{/i}"

            "It's no bother":
                $ renpy.block_rollback()
                $ v9_lena_call = 1
                $ fian = "smile"
                i_p "{i}Hey, it's no bother. I'm sorry you're going through difficult times.{/i}"
                l_p "{i}I don't mind a bit of adversity but lately it's been too much {image=emoji_sad.webp}{/i}"
                i_p "{i}I know you're strong. And you're not alone, friends come in handy in these situations {image=emoji_wink.webp}{/i}"
                if ian_charisma < 5:
                    call xp_up('charisma') from _call_xp_up_70
                l_p "{i}Thanks, Ian {image=emoji_smile.webp} I need to go, my mom is screaming for help downstairs {image=emoji_ups.webp}{/i}"
                l_p "{i}I'll be back this Friday, hopefully! See you then if you want {image=emoji_smile.webp}{/i}"
                i_p "{i}Sure, call me when you get back.{/i}"
                i_p "{i}Take care.{/i}"

            "When are you coming back?":
                $ renpy.block_rollback()
                i_p "{i}So, when are you coming back?{/i}"
                l_p "{i}This Friday, hopefully! See you then if you want {image=emoji_smile.webp}{/i}"
                i_p "{i}Sure, call me when you get back.{/i}"
                l_p "{i}I need to go, my mom is screaming for help downstairs {image=emoji_ups.webp}{/i}"
                i_p "{i}Take care.{/i}"

        $ fian = "sad"
        i "Lena seems to be going through a really hard time..."
        if ian_lena_over and v9_lena_call == 2:
            $ fian = "worried"
            i "I don't know if it was a good idea to tell her what I just did... She seemed rather cold."
            i "Ahhh, what am I even trying to accomplish here?"
        elif ian_lena_over:
            i "Was she a bit colder than usual toward me? It would make sense, all things considered... But it's hard to tell from just texts."
        else:
            i "I hope she manages to make it work somehow."
# end day
    $ fian = "n"
    i "Alright, I should try to do some writing before going to bed."
    i "I really need to finish this..."

## IAN WEDNESDAY ##############################################################################################################################################################################################################################################################################
##############################################################################################################################################################################################################################################################################################
    stop music fadeout 2.0

    call calendar(_day="Wednesday") from _call_calendar_9

    play music "music/normal_day.mp3" loop
## work office
    if ian_job_magazine > 0:
        if v8_minerva_sex:
            $ minerva_look = 3
        else:
            $ minerva_look = 1
        $ fminerva = "n"
        $ ian_look = 1
        scene magazine with long
        show ian with short
        "That morning I couldn't focus on work. My mind was hung up on the novel, and how to tie everything up."
        show ian at lef3 with move
        show minerva with short
        mi "Ian, to my office. I need to talk to you."
        show minerva at rig3 with move
        play sound "sfx/door.mp3"
        hide minerva with short
        "She left before I even had the chance to reply."
        i "What does she want now?"
        #sex
        if ian_minerva_sex:
            if v8_minerva_sex:
                $ fian = "confident"
                i "Maybe she's feeling horny and wants me to take care of her urges..."
                $ fian = "worried"
                "I looked around the office, full of people."
                if ian_holly_dating:
                    "And Holly was amongst them..."
                    i "No way it's safe to do \"that\" now. And Holly, if she found out..."
                    $ fian = "sad"
                    i "I can't keep doing that with Minerva."
                else:
                    i "No way it's safe to do \"that\" now."
            else:
                i "I hope she doesn't want me to take care of her urges again..."
                if v7_minerva_sex:
                    i "I took the bait twice, but..."
                else:
                    i "She tricked me the first time, but I'm not falling for it again."
                "I looked around the office, full of people."
                if ian_holly_dating:
                    "And Holly was amongst them..."
                    i "It's too risky, and if Holly found out..."
                    $ fian = "serious"
                    i "No, I won't make the same mistake again."
                else:
                    i "No way it's safe to do \"that\" now. And I don't want to make the same mistake twice."
            $ fian = "n"
            "I stood up and followed Minerva to her office, not knowing exactly what to expect."
            show ian at lef with move
            play sound "sfx/door.mp3"
            show minerva at rig with short
            i "What is it? If it's about performing any more sexual favors for you, then..."
            $ fminerva = "mad"
            mi "Shut up! We don't discuss that here in the office!"
            if v8_minerva_sex:
                mi "And this is not what this is about!"
                $ fminerva = "n"
            else:
                i "I just wanted to make clear that it's not gonna happen again..."
                mi "I said shut up! Not here!"
            i "Alright. So?"
        # defy
        elif ian_defy_minerva:
            $ fian = "serious"
            i "I hope she hasn't come up with a plan to get back at me after I put her in her place..."
            $ fian = "n"
            i "No, I still have the upper hand in this situation."
            "I stood up and followed Minerva to her office, not knowing exactly what to expect."
            show ian at lef with move
            play sound "sfx/door.mp3"
            show minerva at rig with short
            $ fian = "serious"
            i "So, what is it? Have you decided to apologize?"
            $ fminerva = "mad"
            mi "Shut up. Don't make things more complicated than they need to be."
            i "Sounds like you could use some of your own advice."
            mi "Enough. Let's keep things strictly professional, okay?"
        # defiant
        elif ian_job_magazine == 1:
            $ fian = "worried"
            i "I have a bad feeling about this..."
            $ fian = "n"
            i "But I don't think I have given her any new reasons to get pissed at me."
            "I stood up and followed Minerva to her office, not knowing exactly what to expect."
            show ian at lef with move
            play sound "sfx/door.mp3"
            show minerva at rig with short
            i "Yes?"
            mi "We need to talk about your current situation in this magazine."
            i "Is there something wrong?"
            $ fminerva = "mad"
            mi "I could give so many answers to that question... But no, that's not the reason I called you in."
            mi "I haven't been pleased with your performance and behavior, and I've been very clear about that."
            $ fian = "serious"
            i "Yeah."
            $ fminerva = "n"
            mi "That's why I reduced your working hours and have been keeping an eye on you. And I think you've learned your lesson."
            mi "So I'm reinstating your full internship, starting next week."
            $ ian_job_magazine = 2 # increases Ian's monthly income. Reference this branch in the future with [if v5_ian_showup == False]
            $ fian = "worried"
            "I wasn't expecting that."
            i "So I'll be working five days a week again?"
            mi "Yes. And I have some specific tasks for you."
            $ fian = "n"
            i "What tasks?"
        # compliant
        else:
            $ fian = "sad"
            i "I hope I didn't get into trouble..."
            "I stood up and followed Minerva to her office, not knowing exactly what to expect."
            show ian at lef with move
            play sound "sfx/door.mp3"
            show minerva at rig with short
            i "Yes?"
            mi "Have you finished writing the press releases I asked for?"
            i "I'm on it."
            mi "Well, finish that today."
        if v5_ian_showup:
            mi "I need to allocate some extra tasks to you."
            $ fian = "n"
            i "Extra tasks?"
        mi "In your resume it says you have basic notions of editing software, right? I need you to do some stuff for the magazine."
        i "I thought you took care of that."
        $ fminerva = "mad"
        mi "Well, I'm a very busy woman these days, and I'll be even busier with the upcoming literary contest."
        i "With the contest? Why?"
        if v8_minerva_sex == False:
            mi "You really like to ask questions, don't you?"
        $ fminerva = "n"
        if v7_holly_trip:
            mi "You already know Hierofant is co-hosting the contest for upcoming authors this year."
            mi "And they've asked me to be a member of the jury."
        else:
            mi "I don't know if you've heard, but Hierofant is co-hosting the contest for upcoming authors this year."
            mi "And they've asked me to be a member of the jury."
        $ fian = "surprise"
        i "You're a member of the jury!?"
        $ fminerva = "mad"
        mi "Yes, why are you...?"
        #sex
        if ian_minerva_sex:
            $ fminerva = "n"
            mi "Oh, of course. You want to enter, right?"
            $ fian  ="n"
            i "Yeah."
            mi "Well, don't ask me for any favors regarding that. All submissions are entered anonymously under a pseudonym."
            if ian_minerva_dating:
                mi "I can't help you, even if you ask me to."
                i "I know. I don't need you to cheat for me."
            else:
                mi "I couldn't help you even if I wanted, which I don't."
                $ fian = "serious"
                i "I don't need you to cheat for me."
            mi "Good."
            mi "That's it. You can go, I'll send you the work shortly."
        # defy
        elif ian_defy_minerva:
            mi "Of course. You want to enter, is that right?"
            $ fian = "serious"
            i "What do you care?"
            mi "All submissions are entered anonymously under a pseudonym, so I don't."
            i "Good."
            mi "That's it. You can go, I'll send you the work shortly."
        # defiant/compliant
        else:
            $ fminerva = "evil"
            $ fian = "worried"
            mi "Of course... You want to enter, I presume?"
            $ fian = "n"
            i "What's it to you?"
            mi "Oh, nothing. All submissions are entered anonymously under a pseudonym, after all."
            mi "I won't know which entry is yours... will I?"
            $ fian = "worried"
            "The way in which she said it gave me the creeps."
            $ fian = "serious"
            i "I hope you're not planning to violate the rules of the contest."
            $ fminerva = "n"
            mi "What? Why should I?"
            mi "I'm afraid you're not as important as you think you are..."
            if v5_ian_showup == False:
                $ fminerva = "mad"
                mi "Now go, and don't make me regret reinstating your full internship."
            else:
                mi "You can go now. I'll send you the work shortly."
        # alison
        if v9_alison_trip and ian_job_magazine == 2 and v5_ian_showup:
            show ian at lef3 with move
            $ fian = "n"
            i "..."
            if ian_defy_minerva or ian_minerva_sex:
                i "Oh, by the way. I'm taking this Thursday and Friday off."
                $ fminerva = "mad"
                mi "What? Why?"
                i "Because it says in my contract that I can. I still have a few personal days I can take if I want."
                mi "Haven't you heard how much work we have recently?"
                if ian_minerva > 0:
                    call friend_xp('minerva', -1) from _call_friend_xp_84
                i "I'm sure it can wait till Monday. Or have someone else do it, I'm surely not the only one who knows how to use that software."
                play sound "sfx/door_slam.mp3"
                hide minerva with vpunch
            else:
                i "..."
                i "Oh, by the way... I need to take this Thursday and Friday off."
                $ fminerva = "mad"
                mi "What? Didn't you hear a word I was saying? We have a ton of work!"
                i "Well, my contract says I can take a few personal days if I need to, so..."
                mi "I'm sure those personal issues can wait until the weekend."
                $ fian = "serious"
                i "No, they can't. That's why I'm taking these two days off."
                mi "What's so important?"
                i "I don't think I need to tell you, do I?"
                if ian_minerva > 0:
                    call friend_xp('minerva', -1) from _call_friend_xp_85
                mi "Alright, do what you will, but I don't foresee a bright future for you in this magazine. Nor a long one."
                i "I'm counting on that, too. If you'll excuse me..."
                play sound "sfx/door.mp3"
                hide minerva with short
        else:
            play sound "sfx/door.mp3"
            hide minerva with short
        show ian at truecenter with move
        "I shut the door and returned to my desk."
        if ian_minerva_sex or ian_defy_minerva:
            $ fian = "n"
            "So Minerva was going to be a judge in the contest... Could that prove useful?"
            $ fian = "worried"
            "Or maybe it was actually dangerous..."
        else:
            $ fian = "worried"
            "So Minerva was going to be a judge on the contest...? That was bad news..."
            "But she couldn't do anything to harm me, could she? Unless she decided to go against the rules..."
        $ fian = "n"
        "I continued with my assigned tasks. I loathed having to spend time at the office instead of working on my book."
        if v9_alison_trip:
            i "Well, at least I get paid for this shit. I'll need some cash for the trip with Alison."
        else:
            i "Well, at least I get paid for this shit."

        # if ian_job_magazine == 2 and v5_ian_showup:
        #     call money(2) from _call_money_3
        # else:
        #     call money(1) from _call_money_4
        pause 1
## work rider
    else:
        $ ian_look = 1
        scene v8_rider1 with long
        "The next day I got on the bike and made deliveries from noon till dusk."
        if v7_effort_job == 2:
            if ian_holly_dating:
                "I would've continued working during the night too, like I had been doing last week, but I was meeting with Holly today."
            else:
                "I would've continued working during the night too, like I had been doing last week, but I had to devote more hours to finishing my book."
        scene street
        show ian at lef
        with long
        "I was waiting in front of one of the restaurants to collect the order and jump on the bike again when a familiar face crossed paths with me."
        $ minerva_look = 1
        $ fminerva = "mad"
        show minerva at rig with short
        mi "Ugh, I thought I wouldn't have to see your face anymore."
        menu:
            "{image=icon_charisma.webp}Get back at her" if ian_charisma > 4:
                $ renpy.block_rollback()
                if ian_chad < 5:
                    $ ian_chad += 1
                $ fian = "serious"
                i "Yeah, same here."
                $ fian = "evil"
                i "I thought witches didn't leave their lairs until midnight."
                $ fminerva = "furious"
                mi "You think you're so smart, don't you?"
                $ fian = "serious"
                i "I don't work for you anymore, so why don't you just leave me alone?"
                i "Take your bullshit somewhere else, you old hag."
                mi "You're right about one thing: you don't work for me anymore."
                "Minerva took a look at the logo of the delivery company on my thermal backpack."
                $ fminerva = "evil"
                mi "I see you've found a job better suited to your talents. Maybe I could've used you to deliver coffees to the office."
                i "Is this how you pass the time? Are you so sexually frustrated that you need to harass old employees?"
                $ fminerva = "furious"
                mi "Watch it!"
                i "Or what?"

            "Show disgust":
                $ renpy.block_rollback()
                $ fian = "serious"
                i "Well, it's not a pleasure for me either. Guess I'm just unlucky today."
                "Minerva took a look at the logo of the delivery company on my thermal backpack."
                $ fminerva = "evil"
                mi "I see you've found a job better suited to your talents. Maybe I could've used you to deliver coffees to the office."
                i "This beats working for you, be sure of that."
                mi "I'm glad for you, then..."

            "Stay civil":
                $ renpy.block_rollback()
                if ian_chad > 0:
                    $ ian_chad -= 1
                i "Yeah, good day to you too."
                call friend_xp('minerva', 1) from _call_friend_xp_86
                "Minerva took a look at the logo of the delivery company on my thermal backpack."
                $ fminerva = "evil"
                mi "I see you've found a job better suited to your talents. Maybe I could've used you to deliver coffees to the office."
                $ fian = "serious"
                i "Is there something that you want? Otherwise, please leave me alone."

        $ fminerva = "evil"
        mi "I heard a birdie say you're going to enter this year's contest for upcoming authors..."
        i "What of it?"
        if v7_holly_trip:
            mi "You already know Hierofant is co-hosting the contest for upcoming authors this year..."
            mi "Well, turns out they've asked me to be a member of the jury!"
        else:
            mi "I don't know if you've heard, but Hierofant is co-hosting the contest for upcoming authors this year."
            mi "And they've asked me to be a member of the jury."
        $ fian = "worried"
        i "What...?"
        mi "I can't wait to read the submissions... I'm sure I'll be able to pick the {i}right{/i} one."
        mi "Enjoy your new vocation delivering food, because it looks like you'll be doing it for a long time..."
        mi "Bye!"
        hide minerva with short
        show ian at truecenter with move
        $ fian = "mad"
        i "That bitch...!" with vpunch
        $ fian = "worried"
        i "Is she really gonna be one of the judges?"
        i "All submissions are entered anonymously under a pseudonym... but I can perfectly imagine her breaking the rules and trying to look up which one's mine."
        i "Fuck..."
        man "Order number twenty-two! Who's got it?"
        $ fian = "n"
        i "That one's mine..."
        scene v8_rider1 with long
        "I continued working, trying not to worry too much about what Minerva just told me."
        "It was hard not to, though..."
        "Being a delivery boy wasn't the most glamorous job, and the pay was meager, but it was something."
        # if v7_effort_job == 2:
        #     "And the extra hours I had been putting in these past two weeks amounted to some extra cash, too."
        #     call money(2) from _call_money_5
        # else:
        #     call money(1) from _call_money_6
        pause 1
    if ian_holly_dating:
        jump v9hollydatescene
    else:
        jump v9homefriends
## DATE WITH HOLLY ##############################################################################################################################################################################################################################################################################
##############################################################################################################################################################################################################################################################################################
label gallery_CH09_S01:
    if _in_replay:
        call setup_CH09_S01 from _call_setup_CH09_S01

label v9hollydatescene:
    scene street with long
    $ fian = "smile"
    $ fholly = "happyshy"
    play music "music/date.mp3" loop
    "Thankfully I had another reward for finishing my work today."
    show ian at lef
    show holly2 at rig
    with short
    "A date with Holly."
    if ian_job_magazine:
        i "Ugh, today's shift couldn't end fast enough."
    else:
        i "Here you are."
    i "Let's head to my place, shall we?"
    h "Yeah."
    play sound "sfx/door_home.mp3"
    scene ianhome with long
    $ fholly = "shy"
    show ian at lef with short
    i "Hello...?"
    show holly3 at rig with short
    i "Seems Perry's not home. Better this way."
    h "So this is your place..."
    i "Yeah. It's not much to look at, but... Do you want me to show you around?"
    $ fholly = "smile"
    h "Sure."
    "I gave Holly a quick tour of the house, ending up in my room."
    play sound "sfx/door.mp3"
    scene ianroom
    show ian at lef
    show holly2 at rig
    with long
    i "And this is my lair."
    h "It looks cozy..."
    i "It's not a bad room at all. There's plenty of space and good sunlight..."
    i "But it's a bit bare-bones. Most of my non-essential stuff is still at my parents' house."
    menu:
        "{image=icon_lust.webp}Make out with Holly" if ian_lust > 5:
            $ renpy.block_rollback()
            $ fian = "confident"
            $ v9_holly_elephant = "kiss"
            "I got closer to Holly and suggestively wrapped my hand around her waist."
            $ fholly = "blush"
            hide holly2
            show holly3 at rig
            with short
            i "But enough speaking... That's not why we're here, is it?"
            h "Uh... I guess not..."
            "I leaned in, slowly."
            i "And what would that reason actually be...?"
            h "To, uh, watch a movie. That's what we said."
            $ fian = "worried"
            i "..."
            show holly3 at rig3 with move
            h "Oh, look, you have a guitar! I didn't know you played, just like Lena."
            "She turned away from me, undoing my embrace. Her voice was a bit shaky and she looked clearly startled."
            if holly_change == 0:
                call friend_xp('holly', -1) from _call_friend_xp_87
            "I was too direct... She probably wasn't expecting that."
            if ian_lena_over:
                "And why was she mentioning Lena all of a sudden?"
            $ fian = "smile"
            i "{i}I guess I'll need to wait until she feels comfortable.{/i}"
            show ian at truecenter with move
            jump v9hollyguitar

        "Show the room to Holly":
            $ renpy.block_rollback()
            $ v9_holly_elephant = True
            i "Do you want to take a look around? I have quite a lot of geeky stuff in here..."
            $ fholly = "happy"
            h "Sure!"
            "I showed Holly a copy of a very old edition I had from a classic fantasy novel and a couple of niche comic books."
            "She also loved the poster on the wall and was familiar with the characters from almost all the figurines I had."
            if ian_chad > 3:
                $ fian = "n"
                i "I need to get rid of these at some point... I don't know why I brought them with me."
                $ fholly = "smile"
                h "Don't do it, it's cool to collect stuff. I like collecting little elephant figurines."
            else:
                i "I don't know why I brought those with me. I guess I didn't know what else to put on the shelves."
                $ fholly = "smile"
                h "It's cool to collect stuff. I like collecting little elephant figurines."
            $ fian = "happy"
            i "Elephants?"
            $ fholly = "shy"
            hide holly2
            show holly3 at rig
            with short
            h "Yeah, I know it's silly, but...  My grandma had a small collection herself, and she gave me one on each birthday since I was a child."
            h "She said elephants live really long lives, and that I would grow up strong and wise if I kept those little figurines, a new one each year to give me luck."
            if ian_wits < 9:
                call xp_up('wits') from _call_xp_up_71
            i "Sounds like she loves you very much."
            $ fholly = "sad"
            h "She did... And I her."
            $ fian = "worried"
            i "..."
            $ fholly = "smile"
            hide holly3
            show holly2 at rig
            with short
            h "Oh, look, you have a guitar! I didn't know you played, just like Lena."
            label v9hollyguitar:
                $ fian = "smile"
            i "It's not a guitar, it's a bass. And yeah, I used to play, back in high school."
            $ fholly = "blush"
            h "And you don't play anymore?"
            i "It's been a very long time... Turns out playing the bass is not so fun if you don't have a band to do it with."
            if v9_holly_elephant != True:
                show ian at lef with move
            i "Anyway, let's go watch that movie, shall we?"
            $ fholly = "happyshy"
            if v9_holly_elephant != True:
                show holly3 at rig with move
            h "Yes."

        "Go watch the movie":
            $ renpy.block_rollback()
            i "Anyway, let's go watch that movie, shall we?"
            $ fholly = "happyshy"
            h "Yes."

    scene ianhome with long
    $ ian_look = 2
    $ fholly = "happyshy"
    "We went back to the living room and I played the movie on the TV."
    show ian at lef
    show holly at rig
    with short
    h "I've been wanting to see this one for quite some time. A lot of people recommended it to me."
    i "I can't believe you've never seen it before... It's a classic!"
    stop music fadeout 2.0
    $ fholly = "shy"
    "We sat on the sofa one next to the other. As the movie progressed I felt Holly getting more relaxed and comfortable."
    "We were slowly leaning closer together. Our legs touched, then our shoulders."
    $ fholly = "flirt"
    "Our bodies were resting on each other... I turned my eyes away from the screen and looked at Holly."
    "She was looking at me too."
    play music "music/sex_romantic.mp3" loop
    scene v9_holly1 with long
    "I kissed her, of course."
    "Softly, at first. Barely a touch of my lips on hers."
    "When I pulled away slightly her chin followed mine, searching for the next kiss. I gave it to her."
    "Kiss after kiss I felt Holly's shyness starting to melt. She pressed her body against mine, her hands touched my arms, my chest..."
    "Once again mixed feelings spiraled inside of me: sweetness and desire, care and lust."
    "They felt opposite, but mixed so well together...!"
    "That was Holly's flavor, and I couldn't get enough of it."
    scene v9_holly2 with long
    "I held Holly by the hips and pulled her on top of me, welcoming her with a deep kiss."
    "The bridge of her glasses was knocking against my nose, so she took them off. Much better."
    "I continued kissing Holly slowly, passionately, inviting her tongue to dance."
    "Meanwhile, my hands caressed her skin, sneaking under her clothes."
    show v9_holly3 with long
    "As the minutes went on, my kisses started rolling down Holly's neck, and even lower..."
    play sound "sfx/ah3.mp3"
    "I could feel how excited I was making her. The way she sighed and moaned, so softly, so wantingly..."
    "The way she started grinding her hips ever so slightly when I started teasing her nipples..."
    "How she gradually increased her movements, dragging her crotch over my erection as I continued to lick her deliciously hard nipples..."
    if ian_chad > 3:
        "She writhed with pleasure and desire between my arms, and she was making me so fucking horny."
    else:
        "She writhed with pleasure and desire between my arms, and she was making me so damn excited."
    stop music fadeout 3.0
    "To my surprise, Holly posted up and reached down with her hand, cupping my hard-on over the pants."
    if holly_change > 2:
        h "You're so hard down here..."
    scene v9_holly4 with long
    "Before I could say anything, she began unzipping them, digging for my cock."
    if holly_change < 3:
        "It was a bit awkward how quiet she was..."
    i "Holly, don't feel forced to..."
    "She looked at me. Her cheeks were red, but her eyes showed determination."
    h "I want to do it. I want to make you feel good..."
    "I helped her remove my pants and I laid back on the sofa. I had no intention of going against Holly's wishes..."
    "She wrapped her delicate fingers around the shaft and began stroking it softly."
    if ian_chad > 3:
        "Her gaze was fixated on my dick. I wasn't sure if she was fascinated by it or was trying to study it."
    else:
        "Her gaze was fixated on my manhood. I wasn't sure if she was fascinated by it or was trying to study it."
    "Either way, the way she used her hand felt good..."
    if holly_change > 2:
        h "It's so big..."
    else:
        h "..."
    scene v9_holly5 with long
    if holly_change > 2:
        "After saying those surprising words, Holly leaned forward and wrapped her lips around my glans."
        i "Mhhh!"
    else:
        "Holly leaned forward and wrapped her lips around my glans without warning."
        i "Holly...!"
    if ian_lust < 9:
        call xp_up ('lust') from _call_xp_up_72
    play sound "sfx/mh1.mp3"
    if v8_holly_bj:
        "She began sucking weakly like she was scared she could hurt me."
        "She surely didn't want to scrape me with her teeth like last time..."
        i "That's it... Use your lips more, don't be afraid... I like it."
        "Encouraged, she did as I asked. Her lips felt so soft and nice."
        i "It's so good when you do it slowly like that... I want to feel your tongue, too..."
        scene v9_holly6 with long
        "I already knew Holly wasn't particularly skilled when it came to this..."
    elif v7_holly_bj:
        h "Nhh..."
        "She wrapped her lips around the glans and tried to suck on it. Her teeth lightly scraped, making me twitch."
        i "Ouch!"
        h "I'm sorry...!"
        i "Don't worry. Try to use just your lips, without the teeth..."
        "Holly puckered her lips and slid them around the glans."
        i "Yes, like that... Now try using only your tongue..."
        scene v9_holly6 with long
        "I already knew Holly wasn't particularly skilled when it came to this..."
    else:
        "Her hair prevented me from seeing her face as she bobbed her head, sucking on the tip."
        "Her movements were timid, clumsy, even. I had the impression she didn't really know what to do with a dick in her mouth..."
        "At some points, I even felt her teeth scraping against my sensitive glans."
        i "Ouch..."
        h "D-{w=0.3}did I hurt you...?!"
        i "No, it's okay... Keep going."
        i "Try using just the tongue now..."
        scene v9_holly6 with long
        "I wasn't surprised to discover Holly wasn't particularly skilled when it came to this."
    "But I didn't mind. She still turned me on so much..."
    "And she could surely learn, as I was trying to learn what made her tick, too."
    if holly_change > 1:
        show v9_holly6b with long
        "Holly began stroking my cock without me telling her to. She seemed into it..."
        i "That's good... Yeah, do it a bit faster..."
    else:
        i "Try using the hand at the same time..."
        show v9_holly6b with long
        i "Just like that. A bit faster... Yeah..."
    "Holly listened to every indication and adjusted accordingly."
    "Her fingers slid across my shaft while her silky tongue continued to stimulate the glans. This was getting good...!"
    "I closed my eyes and tried to relax, letting the pleasure build up..."
    $ holly_look = "sexytopless"
    $ holly_glasses = False
    if v8_holly_bj or v7_holly_bj or holly_change > 1:
        scene v9_holly5
        show v9_holly5b
        with long
        "Holly went back to using her lips, but this time she added her tongue into the motion, running it softly across the underside."
        "And her hand kept stroking the shaft, too..."
        # cum
        if holly_change > 2:
            $ v9_holly_bj = 2
            scene v9_holly7 with long
            "I had to open my eyes again when I felt Holly sliding her tongue all across my hard shaft."
            "She was trapping it between her lips, licking it from top to bottom, almost like she was playing the harmonica."
            "Where did she learn to do such a thing?"
            i "Damn, Holly...! If you keep that up..."
            play sound "sfx/bj3.mp3"
            h "{i}\*Mhaa\*... \*Mmbuh\*...{/i}"
            "She didn't show any intention of stopping. I didn't ask her to."
            "Hearing her panting and feeling her warm saliva soaking my dick was driving me to the edge."
            show v9_holly7b with flash
            i "Ohhh!!" with vpunch
            "I barely had time to brace myself for the surge of pleasure."
            with vpunch
            pause 0.5
            with vpunch
            pause 0.5
            i "Oh, wow, Holly..."
            $ fian = "shy"
            $ fholly = "shy"
            scene ianhome
            show iannude at lef
            show ian2_pantless at lef
            show hollybra3 at rig
            with long
            h "Did you like it?"
            i "Is there any doubt?"
            if ian_lust < 9:
                call xp_up ('lust') from _call_xp_up_73
            $ fholly = "happyshy"
            h "I'm glad...!"
            "Just then I heard the characteristic sound of dangling keys behind the main door."
        # almost
        else:
            $ v9_holly_bj = 1
            "I was starting to really enjoy it. If she kept this up I would probably cum..."
            i "This is good... You're making me feel really good, Holly..."
            "She sucked more eagerly, taking what I had just said as a compliment."
            "She really did want to make me feel good..."
            "I was getting dangerously close to the edge when I heard the characteristic sound of dangling keys behind the main door."
    # no cum
    else:
        "Holly continued using her tongue and hand as I instructed her, but she lacked a bit of creativity."
        "I felt pleasure starting to slightly deflate. Damn, I had been getting close...!"
        "I was about to ask her to mix it up a bit when I heard the characteristic sound of dangling keys behind the main door."
    stop music fadeout 1.0
    $ fian = "surprise"
    $ fholly = "worried"
    $ fperry = "meh"
    scene ianhome
    show iannude at lef
    show ian2_pantless at lef
    show hollybra at rig
    with short
    i "Shit! Perry's back!"
    h "...!"
    i "Hurry, this way!"
    play sound "sfx/door_home.mp3"
    scene ianhome with short
    show perry at lef3 with short
    "We made it to my room just in time, before Perry could see us."
    show perry at truecenter with move
    p "..."
    p "Ian, are you home? You left the T--{w=0.5}TV on."
    i "Yeah, just a minute...!"
    p "..."
    p "And these glasses...?"
    $ fian = "n"
    show perry at rig with move
    show ian at lef with short
    i "I'll take those from you, thanks."
    p "Who do they belong to?"
    $ fian = "smile"
    i "To Holly. You've met her before, at Lena's concert..."
    show perry at rig3
    show ian at lef3
    with move
    $ holly_look = "1skirt"
    $ fholly = "shy"
    $ holly_glasses = True
    show holly3 with short
    h "Hello..."
    $ fperry = "smile"
    p "Oh, yeah, of c--{w=0.5}course! Nice to see you, Holly."
    p "Are you joining us this afternoon, too?"
    $ fholly = "n"
    $ fian = "worried"
    i "Joining us?"
    $ fperry = "meh"
    p "Oh, yeah, guess I f--{w=0.5}forgot to tell you."
    $ fperry = "n"
    p "Wade and Emma will be dropping by in a bit to play cards and have some b--{w=0.5}beers."
    $ fperry = "smile"
    p "So, do you wanna stay, Holly?"
    $ fholly = "shy"
    h "Well, if it's no bother..."
    p "Of course it isn't, r--{w=0.5}right Ian?"
    $ fian = "smile"
    i "Not at all. I'd like you to stay."
    h "Okay, in that case, I will!"
    scene ianhome with long
    if v9_holly_bj == 2:
        "It sucked that Perry interrupted us, but I had had enough time to fully enjoy Holly's blowjob."
        "She had surprised me..."
    elif v9_holly_bj == 1:
        "It sucked so much that Perry had interrupted us... I had really been enjoying Holly's blowjob."
        "She was a fast learner, it seemed..."
    else:
        "It sucked so much that Perry had interrupted us... I had been starting to enjoy Holly's blowjob."
        "And with practice, she would get good at it, I knew it."
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH09_S01")
    jump v9homefriendsstart

## NIGHT WITH FRIENDS ##############################################################################################################################################################################################################################################################################
##############################################################################################################################################################################################################################################################################################
label v9homefriends:
    play sound "sfx/door_home.mp3"
    $ fperry = "n"
    scene ianhome with long
    $ fian = "n"
    show ian with short
    i "Hello..."
    "Hm. Perry's not home. Weird."
    play sound "sfx/door.mp3"
    scene ianroom with short
    $ ian_look = 2
    show ian with short
    "I went to my room and decided to relax for a bit before getting to work on my book. I was tired..."
    play sound "sfx/door_home.mp3"
    "A few minutes later I heard Perry arriving. He was talking to someone on the phone."
    p "Yeah, I bought a six-pack for myself. Bring your own b--{w=0.5}beer!"
    p "Your whiskey? Yeah, we have it... But I t--{w=0.5}think there's not much left. Bring ice in that case."
    p "Okay, okay. S--{w=0.5}see you in a bit."
    play sound "sfx/door.mp3"
    scene ianhome with short
    show ian at lef
    show perry at rig
    with short
    i "Hey. Who were you talking to?"
    p "Wade."
    i "He's coming?"
    p "Oh, yeah, guess I f--{w=0.5}forgot to tell you."
    p "Wade and Emma will be dropping by in a bit to play cards and have some b--{w=0.5}beers."
    if v7_cindy_kiss:
        $ fian = "worried"
        i "Wait, is... Is Cindy coming, too?"
        $ fperry = "meh"
        p "I suppose. It's not like s--{w=0.5}she'll let Wade enjoy himself on his own, so..."
        i "But you're not sure?"
        p "I don't know, he didn't specify. Why are you asking so many questions about C--{w=0.5}Cindy?"
        $ fian = "n"
        i "I just want to know who's coming to my house..."
        p "Our house."
    else:
        i "Today? It's Wednesday."
        p "So? They won't stay until late, I g--{w=0.5}guess."
    p "You don't need to join us if you're busy. You can s--{w=0.5}stay in your room."
    $ fian = "smile"
    i "Nah... I guess I'll join, but not until late."
    scene ianhome with short
    "I tried to do some work while I waited for my friends to arrive, but couldn't get much done."
    "Anyway, I could use some distraction after that last boring week."
##PARTY START ################################################################
label v9homefriendsstart:
    stop music fadeout 2.0
    scene ianhomenight with long
    pause 0.5
    $ fian = "smile"
    $ fholly = "smile"
    $ fperry = "n"
    $ fwade = "smile"
    $ fcindy = "n"
    play sound "sfx/doorbell.mp3"
    if ian_holly_dating:
        show ian at lef3
        show holly2
        show perry at rig3
    else:
        show ian at lef
        show perry at rig
    with short
    p "Here they are."
    i "I'll get the door."
    hide perry
    hide holly2
    with short
    if ian_holly_dating == False:
        show ian at lef3 with move
    if v7_cindy_kiss:
        $ fian = "n"
        "I felt my pulse getting faster as I walked to the door. Would I finally see Cindy?"
        "Today seemed like the worst possible moment. I hadn't been able to talk to her, and Wade..."
        play sound "sfx/door.mp3"
        show wade2 at rig3 with long
        w "Hey."
        "I almost let out a sigh of relief."
        i "What's up? Come on in..."
        show wade2 at truecenter with move
        play music "music/cindys_theme.mp3" loop
        show cindy at rig3 with short
        c "Hello!"
        $ fian = "worried"
        "My heart skipped a beat."
        $ fian = "sad"
        i "Hey... Cindy."
    else:
        play sound "sfx/door.mp3"
        play music "music/cindys_theme.mp3" loop
        show wade2
        show cindy at rig3
        with long
        c "Hello!"
        i "Hey, guys. Come on in."
    if ian_holly_dating:
        show cindy at rig2
        show wade2
        show ian at lef3
        with move
        show perry at right5 with short
        p "Hey guys."
        show holly2 at left5 behind ian with short
        h "Hi..."
        c "Oh, who are you? I don't believe we know each other..."
        if holly_change > 2:
            if ian_job_magazine > 0:
                h "I'm Holly, nice to meet you. I work with Ian at the magazine..."
            else:
                h "I'm Holly, nice to meet you. I used to work with Ian at the magazine..."
            i "And you're also a best-selling writer..."
            $ fholly = "blush"
            hide holly2
            show holly3 at left5 behind ian
            with short
        else:
            $ fholly = "blush"
            hide holly2
            show holly3 at left5 behind ian
            with short
            h "Um, I'm..."
            if ian_job_magazine > 0:
                i "Her name is Holly. She works with me at the magazine, and she's also a best-selling writer..."
            else:
                i "Her name is Holly. I used to work with her at the magazine. And she's also a best-selling writer..."
        c "Really? That sounds cool."
        h "I'm far from a best-selling author...! I just started my career, so not that many people know about my books yet..."
        $ fwade = "happy"
        w "Nice to meet you, Holly. Do you want a beer?"
        "Wade handed her a can, but she hesitated."
        hide holly3
        show holly2 at left5 behind ian
        with short
        h "I'm not really used to drinking beer. I don't want it to go to waste..."
        c "Don't worry, it's just beer."
        $ fperry = "meh"
        p "Beer is never \"just\" b--{w=0.5}beer."
        i "We can share one, or I can get you something sweeter."
        hide holly2
        show holly3 at left5 behind ian
        with short
        $ fholly = "shy"
        h "Sharing one with you sounds fine."
    else:
        show cindy at rig
        show wade2 at centerlef
        show ian at left
        with move
        show perry at right with short
        p "Hey, guys."
        if v7_cindy_kiss:
            $ fian = "n"
            "I watched Cindy as she casually walked to the couch and took a seat. She looked completely careless."
            "She didn't even look at me twice, acting like nothing ever happened..."
            "That was probably for the better, but I couldn't help but feel a bit stingy."
            if ian_cindy_sex:
                "She was doing an amazing job, considering what actually happened in that dark alley..."
            "I sat on the couch with them as Perry and Wade cracked a couple of beers open."
        else:
            "We sat on the couch with Perry as he and Wade cracked a couple of beers open."
    play sound "sfx/beer.mp3"
    $ fperry = "n"
    $ fwade = "n"
    $ fcindy = "n"
    i "So, what are we playing today?"
    w "\"Cards to pain humanity\"."
    if ian_holly_dating:
        $ fholly = "smile"
        h "Oh, it's that game about combining cards to make politically incorrect jokes?"
    else:
        i "Ah, that game about combining cards to make politically incorrect jokes?"
    p "Exactly. They don't even need to be p--{w=0.5}politically incorrect, just funny."
    $ v9cardscore = 0
    $ v9cardgame1 = "n"
    $ v9cardgame2 = "n"
    $ v9cardgame3 = "n"
    $ v9cardgame4 = "n"
    $ v9cardtalkperry = False
    $ v9cardtalkwade = False
    $ v9cardtalkholly = False
    $ fperry = "n"
    menu:
        "Let's play":
            $ renpy.block_rollback()
            $ fian = "smile"
            i "Sure, let's play."
            w "Do I need to explain the rules?"
            menu:
                "Yes":
                    $ renpy.block_rollback()
                    i "Yes."
                    w "It's very simple: we take turns being the {i}judge{/i}."
                    w "The judge draws a card with a prompt on it, and the rest have to pick one of their cards as an answer."
                    w "The judge picks the funniest one of the bunch and awards a point to that person."
                    i "Got it."
                "No":
                    $ renpy.block_rollback()
                    i "No, I already know how to play."

            w "Cool, let's play then."
            jump v9cardgame

        "Skip":
            $ renpy.block_rollback()
            $ fian = "n"
            i "I don't really feel like playing tonight."
            $ fperry = "meh"
            p "Really?"
            i "You guys can play if you want. I'll drink my beer."
            scene ianhomenight with long
            "I watched them play a few rounds. Cindy got pissed every time Perry chose Wade's answers, and Wade also tended to pick Perry's."
            if ian_holly_dating:
                "Holly surprised us with her rather dark sense of humor, making us laugh and earning herself some points."
                "Cindy looked happy when we put the cards away and decided to put a couple of pizzas in the oven."
            else:
                "She looked happy when we put the cards away and decided to put a couple of pizzas in the oven."
            jump v9aftercardgame

## CARD GAME ##################################################################################################################################################################
label v9cardgame:
    p "We should wait for Emma."
    w "We can start playing without her, she'll join when she gets here."
    p "Alright..."
    # turn 1 Perry - win wade
    p "Okay, I'll start. Here's the first prompt:"
    play sound "sfx/paper_click.mp3"
    show v9_card1 with short
    "Perry placed the card on top of the table and read it aloud to us."
    p "\"I drink to forget \*blank\*\" Ch--{w=0.5}choose wisely!"
    "I looked at the cards I had drawn. Which one would be funnier?"
    hide v9_card1 with short
    menu:
        "\"I drink to forget...\""
        "A hairy asshole":
            $ renpy.block_rollback()
            $ v9cardgame1 = "asshole"
            i "This one, for sure."

        "Not being able to find the clit":
            $ renpy.block_rollback()
            $ v9cardgame1 = "clit"
            i "This one's hilarious."

        "Cheating":
            $ renpy.block_rollback()
            $ v9cardgame1 = "cheating"
            i "Let's go with this one..."

        "Poor life choices":
            $ renpy.block_rollback()
            $ v9cardgame1 = "life"
            i "This one, obviously."

        "An unwanted pregnancy":
            $ renpy.block_rollback()
            $ v9cardgame1 = "pregnancy"
            i "This one's good."

    "I placed my card face-down on the table, like the others."
    "Perry picked them up and shuffled them before reading them out loud."
    p "\"I drink to forget... not wearing pants\". Ha ha, n--{w=0.5}not bad."
    if ian_holly_dating:
        p "\"I drink to forget... my parents' divorce\". Oof, ha ha ha."
    $ fperry = "meh"
    p "\"I drink to forget... drinking alone\". This one's pretty b--{w=0.5}bad."
    $ fcindy = "serious"
    c "No, it's not."
    $ fwade = "happy"
    w "Guess we all know who that card belongs to."
    c "Shut up."
    $ fcindy = "n"
    $ fwade = "n"
    if v9cardgame1 == "asshole":
        $ fperry = "smile"
        p "\"I drink to forget... a hairy asshole\". Ha ha ha, yeah, I would surely drink to f--{w=0.5}forget that!"
    if v9cardgame1 == "clit":
        $ fperry = "smile"
        p "\"I drink to forget... not being able to find the clit\". Ha ha, this one's g--{w=0.5}good."
        c "Not that it ever happened to you, right?"
        $ fperry = "n"
        p "No, it hasn't."
    if v9cardgame1 == "cheating":
        $ fperry = "n"
        p "\"I drink to forget... cheating\". Sad."
        $ fian ="n"
        if v7_cindy_kiss:
            $ fcindy = "blush"
            "Cindy couldn't avoid looking at me briefly."
            "I finally got a reaction out of her..."
            $ fcindy = "serious"
            c "This is not even funny."
            if ian_cindy > 0:
                call friend_xp('cindy', -1) from _call_friend_xp_88
    if v9cardgame1 == "life":
        $ fperry = "meh"
        p "\"I drink to forget... poor life choices\"."
        c "That sounds exactly like the reason you'd be drinking, ha ha!"
        p "I d--{w=0.5}drink because I like it."
    if v9cardgame1 == "pregnancy":
        $ fperry = "smile"
        p "\"I drink to forget... an unwanted p--{w=0.5}pregnancy\". LOL."
    $ fperry = "n"
    if v9cardgame1 == "asshole":
        $ v9cardscore += 1
        p "My favorite is... \"a h--{w=0.5}hairy asshole!\""
        $ fian = "happy"
        i "Yes! First point goes to me!"
        c "Lame."
        $ fian = "smile"
        i "It was a good one... You just don't understand our sense of humor."
    elif v9cardgame1 == "pregnancy":
        $ v9cardscore += 1
        p "My favorite is... \"an unwanted p--{w=0.5}pregnancy!\""
        $ fian = "happy"
        i "Yes! First point goes to me!"
        c "Lame."
        $ fian = "smile"
        i "It was a good one... You just don't understand our sense of humor."
    else:
        p "My favorite is... \"not wearing p--{w=0.5}pants!\""
        $ fwade = "happy"
        w "Yes! First point goes to me, baby!"
        $ fcindy = "mad"
        c "It was the lamest one of the bunch. The only reason Perry picked it is because you two have the same stupid sense of humor."
        $ fwade = "smile"
        w "You're just mad because he didn't pick yours."
    ## turn 2 cindy - win wade/holly
    $ fcindy = "n"
    c "Whatever. It's my turn now."
    "Cindy drew a card and placed it on top of the table."
    play sound "sfx/paper_click.mp3"
    show v9_card2 with short
    c "Give me your best answers."
    hide v9_card2 with short
    menu:
        "\"What ended my last relationship?\""
        "My dick":
            $ renpy.block_rollback()
            $ v9cardgame2 = "dick"

        "A hairy asshole" if v9cardgame1 != "asshole":
            $ renpy.block_rollback()
            $ v9cardgame2 = "asshole"

        "Not being able to find the clit" if v9cardgame1 != "clit":
            $ renpy.block_rollback()
            $ v9cardgame2 = "clit"

        "Cheating" if v9cardgame1 != "cheating":
            $ renpy.block_rollback()
            $ v9cardgame2 = "cheating"

        "Poor life choices" if v9cardgame1 != "life":
            $ renpy.block_rollback()
            $ v9cardgame2 = "life"

        "An unwanted pregnancy" if v9cardgame1 != "pregnancy":
            $ renpy.block_rollback()
            $ v9cardgame2 = "pregnancy"

    "We all handed our cards to Cindy for her to read and pick one."
    c "Let's see... \"What ended my last relationship?\""
    c "\"Cocaine overdose...\""
    c "\"Extremely tight pants...\""
    if v9cardgame2 == "dick":
        $ fcindy = "serious"
        c "\"My dick\". Gosh, they're all so stupid..."
    if v9cardgame2 == "asshole":
        $ fcindy = "serious"
        c "\"A hairy asshole\". Gosh, they're all so stupid..."
    if v9cardgame2 == "clit":
        $ fcindy = "smile"
        c "\"Not being able to find the clit\"! Ha ha ha, this one's just too accurate..."
    if v9cardgame2 == "cheating":
        $ fcindy = "blush"
        c "\"Cheating\"."
        $ fian ="n"
        if v7_cindy_kiss:
            $ fcindy = "blush"
            "Cindy couldn't avoid looking at me briefly."
            "I finally got a reaction out of her..."
            $ fcindy = "serious"
            c "This is not even funny."
            if ian_cindy > 0:
                call friend_xp('cindy', -1) from _call_friend_xp_89
    if v9cardgame2 == "life":
        c "\"Poor life choices\". Sure..."
    if v9cardgame2 == "pregnancy":
        c "\"An unwanted pregnancy\". Too dark!"
    if ian_holly_dating:
        c "And the last one... \"the sinking of the Titanic\"!"
        if v9cardgame2 == "clit":
            $ v9cardscore += 1
            $ fcindy = "smile"
            c "This one's good, but the best one by far is \"not being able to find the clit\"!"
            $ fian = "happy"
            if v9cardscore == 2:
                i "Awesome! Another point for me, I'm killing it."
            else:
                i "Yes! That's my card. A point for me!"
            p "Yeah, I must admit it was p--{w=0.5}pretty funny."
        else:
            $ fcindy = "smile"
            c "This is the best one, by far!"
            $ fperry = "meh"
            $ fholly = "happy"
            h "Yay! It's mine!"
            p "I guess girls have a s--{w=0.5}similar sense of humor."
    elif v9cardgame2 == "clit":
        $ fcindy = "smile"
        c "\"Not being able to find the clit\" is definitely the best one, no doubt about it."
        $ v9cardscore += 1
        $ fian = "happy"
        if v9cardscore == 2:
            i "Awesome! Another point for me, I'm killing it."
        else:
            i "Yes! That's my card. A point for me!"
        p "Yeah, I must admit it was p--{w=0.5}pretty funny."
    else:
        $ fcindy = "n"
        c "Ugh, they're all so bad, but if I need to pick one..."
        c "\"Cocaine overdose\" it is."
        $ fwade = "happy"
        w "Oh yes! You picked the right one, ha ha."
        $ fperry = "serious"
        p "\"Extremely tight p--{w=0.5}p--{w=0.5}pants\" was hilarious."
    # turn 3 Wade - win perry
    $ fperry = "n"
    $ fcindy = "n"
    $ fian = "smile"
    $ fholly = "smile"
    $ fwade = "smile"
    w "My turn now. And the prompt is..."
    play sound "sfx/paper_click.mp3"
    show v9_card3 with short
    w "Let's see what you have."
    hide v9_card3 with short
    menu:
        "\"What are my parents hiding from me?\""
        "A dead mime":
            $ renpy.block_rollback()
            $ v9cardgame3 = "mime"

        "My dick" if v9cardgame2 != "dick":
            $ renpy.block_rollback()
            $ v9cardgame3 = "dick"

        "A hairy asshole" if v9cardgame1 != "asshole" and v9cardgame2 != "asshole":
            $ renpy.block_rollback()
            $ v9cardgame3 = "asshole"

        "Not being able to find the clit" if v9cardgame1 != "clit" and v9cardgame2 != "clit":
            $ renpy.block_rollback()
            $ v9cardgame3 = "clit"

        "Cheating" if v9cardgame1 != "cheating" and v9cardgame2 != "cheating":
            $ renpy.block_rollback()
            $ v9cardgame3 = "cheating"

        "Poor life choices" if v9cardgame1 != "life" and v9cardgame2 != "life":
            $ renpy.block_rollback()
            $ v9cardgame3 = "life"

        "An unwanted pregnancy" if v9cardgame1 != "pregnancy" and v9cardgame2 != "pregnancy":
            $ renpy.block_rollback()
            $ v9cardgame3 = "pregnancy"

    "After picking a card carefully I handed it to Wade."
    w "So, what are my parents hiding from me?"
    if v9cardgame3 == "mime":
        $ fwade = "happy"
        w "\"A dead mime\"! Ha ha ha ha."
        $ fcindy = "serious"
        $ fperry = "meh"
        "Wade burst out laughing for some reason."
        w "Ha ha ha, I think we already have a winner. No need to read the other ones."
        $ fcindy = "mad"
        c "What are you even talking about? A dead mime? It doesn't make any sense!"
        w "It's hilarious! You just don't get it... and it's obvious this wasn't your card."
        i "It was mine."
        $ v9cardscore += 1
        if v9cardscore == 3:
            i "Three points! I'm crushing it."
        if v9cardscore == 2:
            i "Another point for me. I'm in the lead!"
        if v9cardscore == 1:
            i "Finally, a point for me."
        c "You got that point for the stupidest reason I've ever seen."
        $ fwade = "n"
        w "You're calling me stupid?"
        c "It's hard not to. A dead mime, seriously? My answer was much better!"
        w "Well, I liked this one."
        if ian_holly_dating:
            h "Everyone has a different sense of humor..."
            c "Which makes this game completely pointless!"
        else:
            c "And there's no logical reason behind it."
    else:
        if v9cardgame3 == "dick":
            w "\"My dick\". Okay."
        if v9cardgame3 == "asshole":
            w "\"A hairy asshole\". Okay."
        if v9cardgame3 == "clit":
            w "\"Not being able to find the clit\"! This one doesn't make much sense."
        if v9cardgame3 == "cheating":
            w "\"Cheating\". Ha ha."
            if v7_cindy_kiss:
                $ fcindy = "blush"
                "Cindy couldn't avoid looking at me briefly."
                "I finally got a reaction out of her..."
                $ fcindy = "serious"
                c "This is not even funny."
                if ian_cindy > 0:
                    call friend_xp('cindy', -1) from _call_friend_xp_90
        if v9cardgame3 == "life":
            w "\"Poor life choices\"."
        if v9cardgame3 == "pregnancy":
            w "\"An unwanted pregnancy\". Ha ha ha!"
        w "\"A brain tumor\"..."
        w "\"The true meaning of Christmas\"! I like this one, ha ha!"
        if ian_holly_dating:
            w "\"My gay uncle\". Ha ha, this one's up there, too..."
            w "But no, the Christmas one is getting the point."
        else:
            w "Yeah, this one's getting the point."
        $ fperry = "happy"
        $ fcindy = "serious"
        p "Hell yeah! That's mine."
        if ian_holly_dating:
            h "Jeez, I almost got the point!"
        c "The true meaning of Christmas? It doesn't make any sense! How's that even funny?"
        w "It is to me."
        c "That's stupid."
        p "You're just mad he didn't p--{w=0.5}pick your card. Which one was it?"
        c "The brain tumor!"
        p "Mine was much better, obviously."
        c "You're both equally stupid."
    $ fian = "n"
    i "Guys... It's just a stupid game."
    $ fcindy = "serious"
    c "It's your turn now, Ian. I hope you have better criteria than these two!"
    i "Alright, let's see..."
    play sound "sfx/paper_click.mp3"
    show v9_card4 with short
    "I revealed the next prompt and waited for them to hand me their cards."
    i "Let's see... Which one should I award the point to?"
    hide v9_card4 with short
    $ config.menu_include_disabled = False
    $ greyed_out_disabled = True
    menu:
        "\"While having sex I like to think about...\""
        "Worshiping that pussy":
            $ renpy.block_rollback()
            $ fian = "happy"
            i "It has to be this one: \"While having sex I like to think about worshiping that pussy\"!"
            $ fperry = "happy"
            $ fcindy = "serious"
            p "Right on! T--{w=0.5}that was my answer!"
            c "This game is stupid."
            if ian_cindy > 3:
                call friend_xp('cindy', -1) from _call_friend_xp_91
            p "You didn't get a single p--{w=0.5}point so far, huh? Maybe you're the one who's not funny!"
            c "Shut up! What would you know?"

        "Explaining how vaginas work":
            $ renpy.block_rollback()
            $ fian = "happy"
            i "It has to be this one: \"While having sex I like to think about explaining how vaginas work\"!"
            $ fcindy = "smile"
            c "Yes, finally! You picked the right one!"
            if ian_cindy < 12:
                call friend_xp('cindy', 1) from _call_friend_xp_92
            $ fperry = "meh"
            p "I don't see how that's f--{w=0.5}funny."
            $ fcindy = "n"
            c "That's because you're not a woman, and you have no idea how clumsy guys actually are when it comes to vaginas."
            c "You're probably guilty of that, too..."
            p "What would you know?"
            $ fcindy = "smile"
            c "Anyway, this point's mine!"

        "A really cool hat":
            $ renpy.block_rollback()
            $ fian = "happy"
            i "It has to be this one: \"While having sex I like to think about a really cool hat\"!"
            $ fwade = "happy"
            $ fcindy = "mad"
            w "Wohoo! That's my point!"
            c "What the hell? It doesn't even make any sense!"
            if ian_cindy > 3:
                call friend_xp('cindy', -1) from _call_friend_xp_93
            i "Humor doesn't have to make sense."
            $ fcindy = "serious"
            c "Yes, it does! This game is bullshit."

        "Invading Poland" if ian_holly_dating:
            $ renpy.block_rollback()
            $ fian = "happy"
            i "It has to be this one: \"While having sex I like to think about invading Poland\"!"
            $ fcindy = "serious"
            $ fholly = "happyshy"
            $ fwade = "happy"
            $ fperry = "smile"
            w "Ha ha ha, that one's fucking hilarious! Who does it belong to?"
            $ fholly = "shy"
            h "It's mine."
            i "Wow, really?"
            c "You have a pretty fucked up sense of humor, don't you?"
            $ fholly = "blush"
            h "I don't know... Was it too dark?"
            $ fian = "smile"
            i "Don't listen to Cindy. I thought it was hilarious, that's why you get the point."
            $ fholly = "shy"
            hide holly3
            show holly2 at left5 behind ian
            with short
            h "Thanks!"

    $ config.menu_include_disabled = True
    $ greyed_out_disabled = False
    scene ianhomenight with long
    "We played a few more rounds until we got tired and decided to call it quits."
    if v9cardscore == 3:
        "In the end, I was the one with the most points, which seemingly pissed Cindy off."
        if ian_wits < 9:
            call xp_up('wits') from _call_xp_up_74
    elif v9cardscore == 2:
        "In the end, Wade and I were tied for the most points, which seemingly pissed Cindy off."
        if ian_wits < 9:
            call xp_up('wits') from _call_xp_up_75
    else:
        "Perry and Wade played well off each other, so in the end, they were tied for the most points, which pissed Cindy off."
    "She was happy when we put the cards away and decided to put a couple of pizzas in the oven."
    if v7_cindy_kiss:
        "Despite her complaining about the game, I could notice Cindy acted mellower around Wade."
        "I couldn't help but feel a sting of jealousy every time she leaned on him or laughed at his jokes..."
    jump v9aftercardgame

## AFTER CARD GAME ###################################################################################################################################################################################################
label v9aftercardgame:
    $ fperry = "meh"
    $ fwade = "n"
    $ fcindy = "n"
    $ fian = "n"
    $ fholly = "smile"
    if ian_holly_dating:
        show cindy at rig2
        show wade2
        show ian at lef3
        show perry at right5
        show holly2 at left5 behind ian
    else:
        show cindy at rig
        show wade2 at centerlef
        show ian at left
        show perry at right
    with short
    play sound "sfx/beer.mp3"
    "I cracked another beer open while we waited for dinner to be ready."
    if v7_cindy_kiss:
        "I was trying to play it cool, but I was so tense around Cindy... Unlike her, who seemed unfazed by the situation."
        c "I'm going to the bathroom, be right back."
        hide cindy with short
        $ fian = "worried"
        "Maybe this was the moment I had been waiting for. If I followed her I could talk to her in the hallway..."
        $ fian = "n"
        "No, it was a bad idea. This was not the time."
        show ian at lef3
        show wade2 at truecenter
        show perry at rig3
        with move
        $ fperry = "meh"
        p "So, Wade... How's it g--{w=0.5}going with Cindy? You went on that weekend trip and all..."
        p "It seems you two solved your p--{w=0.5}problems, huh? How did you do that?"
        w "I'm not sure... I don't recall doing anything specific."
        w "Things have been rather calm after that last fight on my birthday."
        $ fian = "disgusted"
        p "Maybe the fight solved it? You know, like when the TV isn't w--{w=0.5}working so you just give it a couple of whacks, and then it w--{w=0.5}works again?"
        $ fian = "n"
        i "I don't think relationships work like that."
        $ fian = "worried"
        "Shit, I should better keep quiet right now."
        w "I don't know..."
        $ fwade = "serious"
        w "Things have been calm, but I feel something's off. Like..."
        w "I don't know, like she's too nice. She's never that nice."
        p "Maybe her love for you got even deeper?"
        $ fwade = "sad"
        w "You think?"
        p "You two looked really happy together on that last p--{w=0.5}pic Cindy uploaded if you ask me."
        $ fwade = "n"
        w "Mhhh..."
        show wade2 at centerlef
        show ian at left
        show perry at right
        with move
        show cindy2 at rig with short
        c "What were you guys talking about?"
        $ fperry = "n"
        p "I was telling Wade you two look real g--{w=0.5}good together in that pic you posted on Peoplegram."
        $ fcindy = "smile"
        c "Oh yeah, that picture turned up great. We look perfect."
        $ fwade = "happy"
        w "See? I'm not so bad at taking pictures myself, after all!"
        c "You took a good one, I'll give you that."
        $ fian = "n"
        $ fcindy = "n"
        $ fwade = "n"
    $ config.menu_include_disabled = False
    $ greyed_out_disabled = True
    menu v9cardtalk:
        "Talk to Perry" if v9cardtalkperry == False:
            $ renpy.block_rollback()
            label v9ctperry:
                $ v9cardtalkperry = True
            i "What's up with Emma? She's really late."
            p "I texted her before, but she..."
            play sound "sfx/sms.mp3"
            p "Oh, s--{w=0.5}speak of the devil. She texted me back."
            p "..."
            $ fperry = "sad"
            p "She says she c--{w=0.5}can't make it."
            $ fian = "sad"
            i "Why?"
            $ fperry = "serious"
            hide perry
            if ian_holly_dating:
                show perry2 at right5
            else:
                show perry2 at right
            with short
            p "She had to attend one of t--{w=0.5}those assembly meetings."
            c "She's taking her activism very seriously, isn't she?"
            $ fian = "smile"
            i "Looks like it. She's always been kind of interested in social and environmental issues..."
            c "Good for her. Must be gratifying feeling if you can actually make some sort of impact on the world..."
            if cafe_perry:
                $ fperry = "meh"
                p "I wanted to tell her I've started helping out at the café today..."
                $ fian = "happy"
                i "Oh, you did?"
                p "Yeah, where did you think I was today?"
                $ fian = "smile"
                i "I don't know... But I'm glad you've decided to lend them a hand."
                i "I'm sure Emma would've appreciated it."
            else:
                p "She can make an impact by b--{w=0.5}being here, too."
            $ fwade = "happy"
            w "Seems like someone was really looking forward to seeing Emma today..."
            if perry_emma > 1:
                $ fian = "evil"
                i "Oh, you have no idea... Especially after what happened last time."
                $ fperry = "mad"
                hide perry2
                if ian_holly_dating:
                    show perry at right5
                else:
                    show perry at right
                with short
                p "Shut up."
                $ fcindy = "smile"
                c "What? What happened?"
                p "Nothing."
                i "Sure... Nothing."
                w "Dude! Is there something you haven't told me?"
                $ fperry = "serious"
                hide perry
                if ian_holly_dating:
                    show perry2 at right5
                else:
                    show perry2 at right
                with short
                p "No, not really. It's j--{w=0.5}just..."
                "Perry looked at me."
                p "I hate you."
                if ian_lena_dating or ian_lena_over:
                    if ian_alison_dating or ian_cherry_dating:
                        i "Now you know how that feels!"
                $ fian = "smile"
                i "It's not fair I'm the only one you guys gossip about all the time, ha ha."
                $ fperry = "meh"
                "Cindy and Wade questioned Perry about his relationship with Emma, to which he answered very succinctly."
                "It was fun seeing them react and trying to encourage him. He surely needed all the morale he could get."
                p "Enough of that, can we t--{w=0.5}talk about something else, please?"
            else:
                p "Shut up."
                if v8_emma_sex or emma_jeremy:
                    menu:
                        "{image=icon_lust.webp}Text Emma":
                            $ renpy.block_rollback()
                            label gallery_CH09_S13:
                                if _in_replay:
                                    call setup_CH09_S13 from _call_setup_CH09_S13
                            $ v9_emma_sext = 1
                            $ fian = "n"
                            i "I would've liked to see her today, too..."
                            $ fian = "smile"
                            i "Let me see if I can pressure her to come after all."
                            "I pulled up my phone and texted Emma."
                            
                            nvl clear
                            if v7_cindy_kiss:
                                hide cindy2
                                show cindy at rig
                                with short
                            show cindy at centerlef2
                            show wade2 at centerlef
                            show perry2 at centerrig2
                            with move
                            
                            i_p "{i}Hey, so you're flaking on us tonight? That's low!{/i}"
                            play sound "sfx/sms.mp3"
                            e_p "{i}Hey! I'm sorry, there was this important meeting today and I couldn't miss it {image=emoji_sad.webp}{/i}"
                            i_p "{i}Is it still going?{/i}"
                            if emma_jeremy:
                                e_p "{i}No, it just finished. But I already had plans for tonight, anyway {image=emoji_tongue.webp}{/i}"
                                i_p "{i}What plans?{/i}"
                                "Emma's response came in the shape of a picture."
                                play sound "sfx/sms.mp3"
                                $ fian = "surprise"
                                show v9_emma1b with short
                                $ ian_emma_pics.append("v9_emma1b.webp")
                                "I was surprised to see Jeremy in it."
                                $ fian = "worried"
                                i_p "{i}So you're ditching us to do dirty things with Jeremy? {image=emoji_ups.webp}{/i}"
                                e_p "{i}What can I say? I'm a horny girl! {image=emoji_crazy.webp}{/i}"
                                $ fian = "smile"
                                i_p "{i}Yeah, you are. I know it well!{/i}"
                                i_p "{i}Have fun you guys, and save some for me next time!{/i}"
                                e_p "{i}Oh yes, for sure!{image=emoji_lips.webp}{image=emoji_cum.webp}{/i}"
                                hide v9_emma1b with short
                                p "Are you texting with Emma? Did she say a--{w=0.5}anything?"
                                $ fian = "worried"
                                if ian_holly_dating:
                                    show cindy at rig2
                                    show perry2 at right5
                                    show wade2 at truecenter
                                    with move
                                else:
                                    show cindy at rig
                                    show perry2 at right
                                    with move
                                i "Uh, I... Yeah, she says she's tired tonight and will stay at home."
                                p "Really? That's so unlike her, I d--{w=0.5}don't understand it."
                                c "Maybe when you mature a bit you will."
                                p "You're one to talk."
                                $ fcindy = "serious"
                                c "What did you just say?"
                                $ fian = "smile"
                                i "We'll miss Emma tonight, but we'll see her next time."
                                $ fcindy = "n"
                            else:
                                e_p "{i}I just got back home. I'm really sorry, I wanted to see you guys but I'm so tired today...{/i}"
                                e_p "{i}I must be getting old {image=emoji_disgust.webp}{/i}"
                                i_p "{i}It's okay. I was just trying to pressure you a bit into coming. I really wanted to see you too.{/i}"
                                play sound "sfx/sms.mp3"
                                show v9_emma1 with short
                                $ ian_emma_pics.append("v9_emma1.webp")
                                $ fian = "happy"
                                e_p "{i}There you go!{image=emoji_crazy.webp}{/i}"
                                i_p "{i}Where are your clothes? {image=emoji_laugh.webp}{/i}"
                                e_p "{i}I was about to take a shower. Besides, it's nothing you haven't seen before {image=emoji_wink.webp}{/i}"
                                $ fperry = "meh"
                                hide v9_emma1 with short
                                p "Are you texting with Emma? Did she say a--{w=0.5}anything?"
                                $ fian = "worried"
                                if ian_holly_dating:
                                    show cindy at rig2
                                    show perry2 at right5
                                    show wade2 at truecenter
                                    with move
                                else:
                                    show cindy at rig
                                    show perry2 at right
                                    with move
                                i "Uh, I... Yeah, she says she's tired tonight and will stay at home."
                                p "Really? That's so unlike her, I d--{w=0.5}don't understand it."
                                c "Maybe when you mature a bit you will."
                                p "You're one to talk."
                                $ fcindy = "serious"
                                c "What did you just say?"
                                menu:
                                    "Keep texting with Emma":
                                        $ renpy.block_rollback()
                                        $ v9_emma_sext = 2
                                        $ fian = "smile"
                                        i "Um, guys, I need to go to the bathroom..."
                                        if ian_holly_dating:
                                            i "I'll be right back, Holly."
                                            h "Sure."
                                        stop music fadeout 2.0
                                        play sound "sfx/door.mp3"
                                        scene ianhomenight with long
                                        $ fian = "n"
                                        show ian with short
                                        "I used that excuse to get myself some intimacy. Texting with Emma was risky with Perry and the others around..."
                                        if ian_holly_dating:
                                            $ fian = "sad"
                                            "I felt kind of bad playing with Emma while Holly was there, but..."
                                        $ fian = "confident"
                                        "Emma had gotten me in a raunchy mood and I wanted to enjoy this chance. I texted her again."
                                        play music "music/sex_bingo.mp3" loop
                                        i_p "{i}I've seen it before, but I want to see more {image=emoji_flirt.webp}{/i}"
                                        if ian_lust < 9:
                                            call xp_up('lust') from _call_xp_up_76
                                        play sound "sfx/sms.mp3"
                                        e_p "{i}Oh yeah? And what do you want to see?{/i}"
                                        i_p "{i}For starters, that epic ass of yours {image=emoji_ass.webp}{image=emoji_glasses.webp}{/i}"
                                        e_p "{i}Hahaha I knew it {image=emoji_crazy.webp}{/i}"
                                        show ian at left with move
                                        play sound "sfx/sms.mp3"
                                        show v9_emma2 with short
                                        $ ian_emma_pics.append("v9_emma2.webp")
                                        i "Fuck, what a great picture..."
                                        i_p "{i}Damn, you really know how to get me hard, don't you?{/i}"
                                        e_p "{i}Not my fault you are weak when it comes to my ass!{/i}"
                                        i_p "{i}Yes, it is. You enjoyed teasing me that night at the club with those slutty shorts...{/i}"
                                        e_p "{i}You really loved them, huh?{/i}"
                                        i_p "{i}I loved more what was underneath. Care to give me a glimpse again?{/i}"
                                        e_p "{i}I'll show you mine if you show me yours!{/i}"
                                        i_p "{i}You want a pic of my dick?{/i}"
                                        e_p "{i}Yeah {image=emoji_devil.webp}{/i}"
                                        hide v9_emma2
                                        hide ian
                                        with short
                                        i "Alright..."
                                        show iannude at left
                                        show ian2_pantless at left
                                        with short
                                        play sound "sfx/camera.mp3"
                                        show v8_selfie_dick with short
                                        "I pulled out my cock, snapped a pic, and sent it to Emma."
                                        e_p "{i}Oh wow, you really are hard!{/i}"
                                        i_p "{i}Now you see I'm not lying... You really make me horny as fuck.{/i}"
                                        play sound "sfx/sms.mp3"
                                        hide v8_selfie_dick
                                        show v9_emma3
                                        with short
                                        $ ian_emma_pics.append("v9_emma3.webp")
                                        e_p "{i}So is this what you want to see? {image=emoji_crazy.webp}{/i}"
                                        i "Holy fuck."
                                        menu:
                                            "{image=icon_lust.webp}I'd fuck you right now" if ian_lust > 4:
                                                $ renpy.block_rollback()
                                                i_p "{i}I wish I was there right now. I'd fuck you senseless...{/i}"
                                                i_p "{i}I'd get behind you, just like you are in that pic, and shove my hard cock in both holes.{/i}"
                                                i_p "{i}First in that dripping pussy... And once my cock was all lubed up with your juices, I'd drill your ass hard.{/i}"

                                            "{image=icon_charisma.webp}I had so much fun with those" if ian_charisma > 4:
                                                $ renpy.block_rollback()
                                                i_p "{i}I had so much fun with those... I'm not sure which one I prefer, your pussy or your ass.{/i}"
                                                e_p "{i}Why pick just one? {image=emoji_flirt.webp}{/i}"
                                                i_p "{i}That's right. If I was there I'd fuck both of them...{/i}"
                                                i_p "{i}First your dripping pussy... And once my cock was all lubed up with your juices, I'd drill your ass hard.{/i}"

                                            "That's so hot":
                                                $ renpy.block_rollback()
                                                i_p "{i}That's so fucking hot... I love the view, Emma.{/i}"
                                                e_p "{i}You're such a voyeur {image=emoji_laugh.webp}{/i}"
                                                i_p "{i}I'd love to do much more than just watch. If I was there I'd fuck you just as you are in that pic.{/i}"
                                                i_p "{i}First your dripping pussy... And once my cock was all lubed up with your juices, I'd drill your ass hard.{/i}"
                                                if ian_lust < 5:
                                                    call xp_up('lust') from _call_xp_up_77

                                            "Are those for me?":
                                                $ renpy.block_rollback()
                                                i_p "{i}Nice... Are you offering those to me?{/i}"
                                                e_p "{i}What do you think? {image=emoji_tongue.webp} {/i}"
                                                i_p "{i}I think you'd love for me to get behind you, just like you are in this picture, and shove my cock inside you.{/i}"
                                                i_p "{i}First into your dripping pussy... And once my cock was all lubed up with your juices, I'd drill your ass hard.{/i}"
                                                if ian_charisma < 5:
                                                    call xp_up('charisma') from _call_xp_up_78

                                        e_p "{i}Ian, you're turning me on so much right now...{/i}"
                                        i_p "{i}Show it to me. Show me how turned on you really are.{/i}"
                                        "I waited for Emma's next message with my cock in my hand. She sent a short video..."
                                        hide v9_emma3
                                        show v9_emma4a
                                        show v7_alisonbbc1_vid
                                        with short
                                        i "This looks promising!"
                                        $ fian = "shy"
                                        hide v9_emma4a
                                        hide v7_alisonbbc1_vid
                                        show v9_emma4_animation
                                        with short
                                        $ ian_emma_pics.append("v9_emma4_animation")
                                        play sound "sfx/ah2.mp3"
                                        e "{i}Mhhh... Ohh...{/i}"
                                        "Emma shamelessly showed me how she pleased herself."
                                        "Her fingers rubbed her clit for a few seconds before disappearing inside her drenched pussy."
                                        "She shoved them as deep as they'd go, roughly and wantonly."
                                        e "{i}Oh, fuck... Nhhh...!{/i}"
                                        "I could hear her moans and the squishy sounds her fingers made through my phone speakers."
                                        hide v9_emma4_animation
                                        show v9_emma4c
                                        with short
                                        $ ian_emma_pics.append("v9_emma4c.webp")
                                        play sound "sfx/mh1.mp3"
                                        e "{i}Mmmh... I wish this was your cock instead of my fingers...{/i}"
                                        e "{i}You fuck me so good!{/i}"
                                        "Sitting on the toilet lid, I jerked off while watching Emma masturbate. She was so damn hot!"
                                        i "Damn... My cock is about to explode...!"
                                        play sound "sfx/sms.mp3"
                                        hide v9_emma4c
                                        show v9_emma5
                                        with short
                                        $ ian_emma_pics.append("v9_emma5.webp")
                                        "She sent yet another short video."
                                        "This time, she was on all fours, fingering her asshole."
                                        "I had a perfect, close-up view of Emma's privates, of how her fingers stretched her elastic anus, going in and out, rubbing the insides frantically..."
                                        i "Fuck, Emma...! I'm getting close..."
                                        stop music
                                        hide v9_emma5 with vpunch
                                        $ fian = "worried"
                                        p "Hey, are you s--{w=0.5}still there? I hope you're not taking a dump!"
                                        i "Fuck..."
                                        hide iannude
                                        hide ian2_pantless
                                        show ian  at left
                                        with long
                                        show ian at truecenter with move
                                        $ fian = "serious"
                                        i "I'm almost done! Get off my back!"
                                        p "Hurry it up, I need to use the b--{w=0.5}bathroom too!"
                                        play sound "sfx/door.mp3"
                                        if ian_holly_dating:
                                            show ian at lef3
                                        else:
                                            show ian at left
                                        with move
                                        "I put away my phone and rejoined the group in the living room."
                                        play music "music/cindys_theme.mp3" loop
                                        if ian_holly_dating:
                                            show cindy at rig2
                                            show wade2
                                            show perry2 at right5
                                            show holly2 at left5 behind ian
                                        else:
                                            show cindy at rig
                                            show wade2 at centerlef
                                            show perry2 at right
                                        with short
                                        $ fian = "smile"
                                        "I would enjoy Emma's gifts later..."

                                    "Put the phone down":
                                        $ renpy.block_rollback()
                                        $ fian = "n"
                                        "I decided to put the phone down. It was risky keeping this conversation going..."
                                        if ian_holly_dating:
                                            $ fian = "sad"
                                            "Especially with Holly by my side. I should be paying attention to her, not to Emma."
                                        $ fian = "smile"
                                        i "We'll miss Emma tonight, but we'll see her next time."
                                        $ fcindy = "n"
                            $ renpy.end_replay()
                            $ gallery_unlock_scene("CH09_S13")

                        "Too bad":
                            $ renpy.block_rollback()
                            i "She'll be missed."
                else:
                    i "She'll be missed."
                $ fperry = "meh"
            hide perry2
            if ian_holly_dating:
                show perry at right5
            else:
                show perry at right
            with short
            $ fwade = "smile"
            $ fcindy = "n"
            if v9cardtalkwade:
                jump v9pizza2
            else:
                jump v9cardtalk

        "Talk to Wade" if v9cardtalkwade == False:
            $ renpy.block_rollback()
            $ v9cardtalkwade = True
            i "So, Wade, how's your job hunting going? Found anything yet?"
            $ fwade = "n"
            hide wade2
            if ian_holly_dating:
                show wade
            else:
                show wade at centerlef
            with short
            w "I've had a couple of interviews, but they haven't gotten in touch so far..."
            $ fcindy = "serious"
            c "If you only devoted half of the time you spend playing video games on getting a job, you'd have already found one!"
            $ fwade = "serious"
            $ fperry = "meh"
            $ fian = "n"
            $ fholly = "sad"
            w "It's not so easy, okay?"
            c "How would you know?"
            w "Because I'm trying to find one?"
            c "Well, your unemployment benefits are up next month, so try harder."
            w "Easy for you to say when your dad has a business and gives you employment."
            jump v9pizza

        "Talk to Cindy" if v9cardtalkwade == False:
            $ renpy.block_rollback()
            $ v9cardtalkwade = True
            if v7_cindy_kiss:
                $ fian = "n"
                "I was trying to find a way to break the ice with Cindy. Get some kind of nod, or reaction... Anything."
            i "So, Cindy... How's everything going?"
            c "Same old, same old. Working my nine-to-five, trying to enjoy my free time, you know."
            i "How was your small vacation with Wade?"
            c "Oh, it was fine. We went to this coastal city, not far from here. It's not like we can afford much else with our budget, but it's something."
            w "We were saving up for August, but you suddenly wanted us to take that trip."
            $ fcindy = "serious"
            c "At least I have some initiative."
            $ fwade = "serious"
            $ fperry = "meh"
            $ fian = "n"
            $ fholly = "sad"
            w "I have initiative too. It was me who came up with the idea of going someplace during summer..."
            c "And it's me who's planning everything, looking up hotels, plane tickets..."
            w "Nobody asked you to! You did it because you like to control everything!"
            c "You know that if I don't take care of it nobody will!"
            jump v9pizza

        "Talk to Holly" if ian_holly_dating and v9cardtalkholly == False:
            $ renpy.block_rollback()
            label v9ctholly:
                $ v9cardtalkholly = True
            $ fian = "smile"
            i "How are you, Holly? Having fun?"
            $ fholly = "happyshy"
            h "Yes. Thank you for inviting me. I almost forgot how fun it is to play board games."
            $ fperry = "n"
            p "We do that from time to t--{w=0.5}time. You can join us if you want."
            $ fholly = "shy"
            h "If it's no bother..."
            i "Don't be silly, of course it isn't."
            c "How come you didn't introduce her to us earlier, Ian?"
            $ fian = "n"
            i "Oh, well, I guess it's only recently that Holly and I have started to... get acquainted."
            $ fcindy = "flirt"
            hide cindy
            show cindy2 at rig2
            with short
            c "Are you sure \"acquainted\" is the word you're looking for?"
            $ fholly = "blush"
            hide holly2
            show holly3 at left5 behind ian
            with short
            $ fian = "blush"
            i "Um, yeah. I mean..."
            "I looked at Holly, worried that we were making her feel uncomfortable."
            $ fcindy = "smile"
            c "Oh, come on, don't be shy! It's obvious there's something between you two!"
            "Damn Cindy, she could be even less subtle than Alison."
            h "It is?"
            c "I'm not mistaken, am I?"
            $ fian = "n"
            i "No, you're not. Holly and I are... starting to get to know each other."
            i "That's all you need to know for now."
            c "I see, I see!"
            $ fcindy = "n"
            hide cindy2
            show cindy at rig2
            with short
            c "You look cute together. I hope we get to see you again, Holly."
            $ fholly = "shy"
            h "Thanks. I hope so too."
            $ fian = "smile"
            i "I'm sure you will."
            $ fholly = "smile"
            hide holly3
            show holly2 at left5 behind ian
            with short
            if v9cardtalkwade:
                jump v9pizza2
            else:
                jump v9cardtalk

label v9pizza:
    $ config.menu_include_disabled = True
    $ greyed_out_disabled = False
    stop music fadeout 2.0
    i "Guys..."
    if v7_cindy_kiss:
        "That mellow attitude Cindy had been showing toward Wade suddenly went up in flames."
        "Something had sparked conflict once more, seemingly out of nowhere."
        "Thankfully we were saved by the bell, literally. By the oven's bell."
    else:
        "Things were starting to heat up when we were saved by the bell, literally. By the oven's bell."
    play sound "sfx/ding.mp3"
    $ fperry = "n"
    p "Pizzas are ready!"
    c "I'll go get them."
    hide cindy2
    hide cindy
    with short
    if v7_cindy_kiss:
        "This was my chance to talk to her in private...!"
    i "I'll help you."
    scene ianhomenight
    if ian_holly_dating:
        show ian at lef3
    else:
        show ian at left
    with short
    show ian at lef with move
    "I followed Cindy to the kitchen."
    $ fcindy = "serious"
    show cindy at rig with short
    # cindy talk
    if v7_cindy_kiss:
        $ fian = "sad"
        i "Hey, Cindy... What's going on?"
        c "What do you mean, what's going on?"
        i "You know what I mean. We need to talk."
        i "You never responded to my texts..."
        $ fcindy = "mad"
        c "{i}Shhht{/i}! Lower your voice!"
        c "This is not the place nor the moment to talk about {i}that{/i}."
        menu:
            "When will that be?":
                $ renpy.block_rollback()
                $ fian = "n"
                i "And when will that be, exactly?"
                c "Not now."

            "You're being childish":
                $ renpy.block_rollback()
                $ fian = "serious"
                i "You're acting childish, you know that?"
                c "Will you just leave me alone, for fucks sake?"
                if ian_cindy > 0:
                    call friend_xp('cindy', -1) from _call_friend_xp_94

            "As you wish":
                $ renpy.block_rollback()
                if ian_chad > 0:
                    $ ian_chad -= 1
                i "As you wish..."

        hide cindy with short
        "Cindy turned around and left the kitchen without even looking at me twice."
        $ fian = "worried"
        i "Fuck, she isn't giving me a chance..."
        "Was pretending nothing ever happened the only option, every day from now on?"
        if ian_lena_dating:
            "Maybe it was for the best, all things considered, but..."
        $ fian = "n"
        "No, I couldn't leave things like this."
        "I picked up the pizza and followed Cindy back to the living room."
        $ fcindy = "n"
    # cindy wade relationship
    else:
        i "Hey... Everything alright?"
        c "Yeah."
        if wade_cindy == 2:
            $ fian = "sad"
            i "I thought things had been getting better between you two."
            $ fcindy = "sad"
            c "They have... I know there are some things that won't change overnight, but..."
            c "I'm trying to be patient, but some stuff really gets on my nerves."
            if ian_cindy > 5:
                i "Do you want to talk about it, or...?"
                c "Maybe some other time."
            else:
                i "I see... Can we do something to help, or...?"
                c "No, it's not your problem. Sorry to cause trouble."
        else:
            i "I don't mean to pry, it's just... I don't want you two getting into another fight in front of everyone."
            $ fcindy = "sad"
            c "I know. I'm sorry. Things are... complicated."
            if wade_cindy == 0:
                c "They have been for quite some time now."
            i "Yeah... Can we do something to help, or...?"
            c "No, it's not your problem. Sorry to cause trouble."
        $ fcindy  ="n"
        c "Let's just try to have fun, okay?"
        i "Sure..."
        hide cindy with short
        $ fian = "n"
        "We picked up the pizzas and went back to the living room."
    play music "music/normal_day2.mp3" loop
    $ fwade = "n"
    $ fholly = "n"
    if ian_holly_dating:
        show ian at lef3 with move
        show cindy at rig2
        show wade2
        show perry at right5
        show holly2 at left5 behind ian
        with short
    else:
        show ian at left with move
        show cindy at rig
        show wade2 at centerlef
        show perry at right
        with short
label v9pizza2:
    if v9cardtalkperry == False:
        jump v9ctperry
    if ian_holly_dating and v9cardtalkholly == False:
        jump v9ctholly
    "We continued to talk and drink as we ate the pizzas."
    # holly leaves
    if ian_holly_dating:
        "When we finished, Holly said it was late and she had to go home."
        c "Good night! Nice to meet you, Holly!"
        $ fholly = "happyshy"
        h "Thanks!"
        $ fperry = "n"
        p "See you next t--{w=0.5}time!"
        i "I'll walk you to the door."
        hide wade2
        hide cindy
        hide perry
        with short
        show ian at lef
        show holly2 at rig
        with move
        $ fian = "smile"
        i "So, did you have fun?"
        h "Yes, I did! They are very nice."
        h "Thanks for inviting me."
        i "Not at all. It was all kind of improvised, but I enjoyed having you here."
        $ fholly = "shy"
        hide holly2
        show holly3 at rig
        with short
        h "Me too... And I also enjoyed the movie, even though we didn't finish it..."
        $ fian = "happy"
        i "I guess we'll need to meet again to finish it!"
        h "I'd love that. Good night, Ian."
        "She kissed me briefly on the lips before leaving."
        hide holly3 with short
        $ fian = "smile"
        $ fwade = "happy"
        $ fcindy = "flirt"
        show ian at left with move
        show cindy at rig
        show wade2 at centerlef
        show perry at right
        with short
        pause 0.5
        w "She's cute."
        c "That's the exact word, cute. And it's obvious she's so into you."
        $ fian = "shy"
        i "Well, yeah... We're just taking it one step at a time, see where this goes."
        $ fwade = "smile"
        if ian_lena_over:
            c "I thought you were with that other girl, Lena... What happened with that?"
            $ fian = "sad"
            i "That... We hooked up a few times, but in the end, it seems it wasn't meant to be."
            $ fian = "n"
            i "To tell the truth, I had been interested in Holly for some time now... I wasn't sure anything would come out of it, but it has."
            $ fian = "smile"
            i "So everything's cool."
            w "Good for you."
        if ian_alison_sex:
            c "And what about Alison? Weren't you two dating or something...?"
            $ fian = "sad"
            if ian_alison_dating:
                i "What? No, that's not what it is..."
                c "Are you sure? She might think something different."
                $ fian = "worried"
                i "Why? Have you talked to her about it? Has she said something?"
                c "No, nothing like that. But you two have been going on dates for quite some time now..."
                $ fian = "n"
                i "They're not \"dates\"..."
            else:
                i "What? Not at all..."
                i "We just, you know... A couple of times. That's it."
                c "I see..."
        elif ian_cherry_sex:
            c "And what about that girl, the model...? Alison's friend."
            $ fian = "n"
            i "Cherry?"
            c "Yeah. Weren't you dating her too or something?"
            if ian_cherry_dating:
                i "No, not at all... We just hooked up a couple of times."
            else:
                i "No, not at all... That was just a one-night stand. We're cool now."
        $ fperry = "n"
        p "Anyway, I like H--{w=0.5}Holly. I think she suits you."
        p "She even reminds me a bit of G--{w=0.5}Gillian."
        $ fian = "sad"
        $ fwade = "n"
        $ fcindy = "sad"
        i "..."
        $ fperry = "meh"
        p "..."
        c "Not the most appropriate comment."
        $ fperry = "serious"
        hide perry
        show perry2 at right
        with short
        p "What? It's the t--{w=0.5}truth!"
        $ fian = "n"
        i "No, you're right. She has some similarities with her..."
        $ fian = "smile"
        $ fperry = "n"
        hide perry2
        show perry at right
        with short
        i "But she's also very different. And I also like that about her."
        i "She's a writer, like me. And a great one."
        $ fcindy = "n"
        c "It really sounds like she's the perfect girlfriend for you."
        $ fian = "n"
        i "It's too soon to use that word. Let's see how things play out."
    # gossip lena cherry drug
    $ fperry = "meh"
    p "It s--{w=0.5}sucks Emma couldn't come. We had a blast last time."
    $ fian = "n"
    i "I wouldn't say that exactly. It was a weird night."
    if v8_trip:
        p "Well, you literally b--{w=0.5}blasted off with that crazy drug Emma gave you."
        $ fwade = "happy"
        $ fian = "sad"
        i "I was referring to the debacle we had before that..."
        c "Wait, debacle? Crazy drug? What the hell happened that night?"
        w "I wanna hear about that drug first, ha ha."
        $ fian = "n"
        menu:
            "Tell them about your trip":
                $ renpy.block_rollback()
                i "It was the weirdest thing ever. I don't know exactly what it was or where Emma got it, but man..."
                p "He took a puff and he was gone."
                $ fcindy = "surprise"
                c "Unconscious?"
                p "Pretty much, yeah."
                w "Holy shit, ha ha ha."
                if ian_wade < 12:
                    call friend_xp('wade', 1) from _call_friend_xp_95
                $ fcindy = "sad"
                c "I didn't know you were a junkie, Ian..."
                $ fian = "worried"
                i "What are you talking about? It's nothing like that!"
                $ fian = "n"
                i "Emma insisted that it was an experience worth having, and I decided to give it a go."
                $ fwade = "smile"
                w "Brave man."
                $ fcindy = "n"
                c "And? Was it worth it?"
                if trip_kill:
                    i "Not really... It was so confusing, and I felt trapped in some kind of parallel dimension..."
                    $ fcindy = "sad"
                    i "I was so glad when I woke up in my bed, but I had trouble discerning reality from fantasy for a while. It wasn't fun."
                    c "Damn... That sounds really dangerous. Emma can be a bit crazy... And so can you, it seems."
                    i "I think it was a one-and-done."
                else:
                    i "I'd say so. It got really scary at first, and then very confusing. But at the same time..."
                    $ fian = "smile"
                    $ fcindy = "sad"
                    i "I don't know, I can't explain it with words. My memory is all foggy, but I had the weirdest feeling."
                    i "Like I was experiencing true reality for the first time or something like that."
                    w "Sounds mind-blowing."
                    c "And dangerous. Emma can be a bit crazy... And so can you, it seems."
                    i "It was fine... But yeah, crazy."
                $ fcindy = "n"
                c "And what about that debacle you mentioned? It has to do with this?"
                $ fian = "n"
                i "No... It was before that."
                p "It had n--{w=0.5}nothing to do with us, really. It was Lena and Cherry..."
                jump v9gossipmenu

            "Talk about what happened between Lena and Cherry":
                $ renpy.block_rollback()
                i "Forget about it... It was really weird. But what happened between Lena and Cherry was even weirder."
                c "Lena and Cherry? What's up with them?"
                p "Turns out they have some b--{w=0.5}bad blood."
                jump v9gossipian

            "I shouldn't talk about it":
                $ renpy.block_rollback()
                i "Forget about it... And I don't think we should discuss that other thing. It's not our place to get into other people's lives."
                $ fwade = "n"
                $ fcindy = "serious"
                c "Whose lives? Stop being a tease and tell us!"
                jump v9gossipperry

    else:
        p "Other than that d--{w=0.5}debacle between Lena and Cherry everything was n--{w=0.5}nice."
        c "Wait, debacle? What happened?"
        menu v9gossipmenu:
            "Talk about what happened between Lena and Cherry":
                $ renpy.block_rollback()
                i "Well, it turns out they have some history... and some bad blood."
                label v9gossipian:
                    $ fcindy = "flirt"
                c "Oh, really? Tell me more!"
                if ian_cindy < 12:
                    call friend_xp('cindy', 1) from _call_friend_xp_96
                i "Seems like Cherry was the reason Lena broke up with her ex."
                c "No way! He was cheating on her?"
                i "Something like that..."

            "I shouldn't talk about it":
                $ renpy.block_rollback()
                i "I don't think we should discuss that. It's not our place to get into other people's lives."
                $ fwade = "n"
                $ fcindy = "serious"
                c "Oh, come on! Stop being a tease and tell us!"
                label v9gossipperry:
                    if ian_cindy > 3:
                        call friend_xp('cindy', -1) from _call_friend_xp_97
                $ fperry = "meh"
                p "Turns out Lena and Cherry have some b--{w=0.5}bad blood. From the past."
                $ fcindy = "flirt"
                c "Oh, really? Tell me more!"
                "Perry didn't seem to have a problem gossiping about it. Not surprising."
                p "Seems like Cherry was the reason Lena b--{w=0.5}broke up with her ex."
                c "No way! He was cheating on her?"
                p "Something like that..."
    # cindy axel photo shoot
    hide cindy
    show cindy2 at rig
    with short
    c "Interesting... Give me more details!"
    if v3_cindy_date:
        i "You can ask Axel directly."
        $ fcindy = "sad"
        c "Axel?"
        i "He's Lena's ex."
        $ fcindy = "surprise"
        c "What? No way!"
        $ fwade = "serious"
    else:
        p "What was the story? Both Lena and Cherry p--{w=0.5}posed for him and he fucked them both even though he was dating Lena..."
        c "They posed for him?"
        i "Yeah. Axel is a photographer."
        $ fcindy = "surprise"
        $ fwade = "sad"
        c "Wait, did you say... Axel?"
        $ fwade = "serious"
        w "Are we talking about the same guy you've been posing for, Cindy?"
        $ fcindy = "sad"
        $ fian = "worried"
        c "That would be too much of a coincidence..."
        if v7_holly_trip == False:
            $ fian = "sad"
            i "I'm afraid it's not."
        else:
            i "It would indeed, but... How many photographers named Axel can you find in this city?"
            "It was quite a shock. That guy's shadow seemed to be everywhere..."
        w "Makes perfect sense."
    hide wade2
    show wade at centerlef
    with short
    w "So that guy cheated on his girlfriend? Seems he's not very trustworthy..."
    w "What a shock."
    $ fian = "n"
    $ fcindy = "serious"
    hide cindy2
    show cindy at rig
    with short
    c "Don't start with that again. We already went over it, didn't we?"
    $ fperry = "meh"
    hide perry
    show perry2 at right
    with short
    p "So you're gonna keep taking p--{w=0.5}pictures with this Axel guy? You're trying to become a model now?"
    $ fcindy = "serious"
    c "I don't need to explain myself to you. I'm not trying to become anything..."
    $ fcindy = "n"
    c "I do it because I'm enjoying it, that's it."
    $ fwade = "n"
    w "Sure..."
    c "Which reminds me... The next photo shoot will be this Saturday."
    $ fwade = "sad"
    $ fperry = "sad"
    hide perry2
    show perry at right
    with short
    w "This Saturday?"
    c "Yeah."
    p "But this weekend we have the Visual Fighter t--{w=0.5}tournament we've been preparing for!"
    w "That's true. You know how important it is to me..."
    $ fcindy = "serious"
    c "Yeah, so? I already knew I couldn't make plans with you this weekend."
    $ fwade = "serious"
    w "But we agreed you wouldn't pose for that guy unless I was there with you."
    $ fcindy = "mad"
    c "Ugh, you know what you sound like right now? I don't like that attitude at all."
    w "I don't care, it's what we agreed on."
    $ fian = "sad"
    "Another fight was breaking out between them... This was becoming way too common."
    menu:
        "Wade's right":
            $ renpy.block_rollback()
            $ fian = "n"
            i "Wade has a point. Can't you re-schedule the photo shoot so he can come along?"
            w "Exactly. I don't see why that would be a problem."
            if ian_wade < 11:
                call friend_xp('wade', 2) from _call_friend_xp_98
            elif ian_wade < 12:
                call friend_xp('wade', 1) from _call_friend_xp_99
            c "Well, it is a problem since Axel will be out of town for the next two weeks because of work."
            "Cindy glared at me."
            c "You should think twice before assuming things you don't know."
            if ian_cindy > 1:
                call friend_xp('cindy', -2) from _call_friend_xp_100
            elif ian_cindy > 0:
                call friend_xp('cindy', -1) from _call_friend_xp_101
            w "So? It's just two weeks. You can surely wait."
            $ fcindy = "serious"
            c "Look, it's simple: you have a plan for this weekend, and I have another one."
            c "You're welcome to join me, but I guess you don't want to give up on that stupid tournament. And I don't want to give up on the photo shoot."

        "Cindy's right":
            $ renpy.block_rollback()
            $ fian = "n"
            i "I don't think Cindy said you can't go... You could if you didn't prioritize the tournament."
            $ fcindy = "smile"
            c "Exactly! Thanks for pointing it out, Ian."
            if ian_cindy < 11:
                call friend_xp('cindy', 2) from _call_friend_xp_102
            elif ian_cindy < 12:
                call friend_xp('cindy', 1) from _call_friend_xp_103
            w "Yeah, thanks, bro."
            if ian_wade > 1:
                call friend_xp('wade', -2) from _call_friend_xp_104
            elif ian_wade > 0:
                call friend_xp('wade', -1) from _call_friend_xp_105
            w "Can't you ask him to do it another day?"
            $ fcindy = "serious"
            "Cindy turned to her boyfriend."
            c "Axel will be out of town for the next two weeks because of work. Besides, he's doing this as a favor and not charging me anything."
            c "Look, it's simple: you have a plan for this weekend, and I have another one."
            c "As Ian said, you're welcome to join me, but I guess you don't want to give up on that stupid tournament. And I don't want to give up on the photo shoot."

        "Stay out of it":
            $ renpy.block_rollback()
            "Staying out of it was the smartest thing to do. For better or worse, Perry wasn't as wise as me."
            $ fperry = "mad"
            hide perry
            show perry2 at right
            with short
            p "That's enough! Can you s--{w=0.5}stop fighting when we get together?"
            p "It's supposed to be fun!"
            $ fcindy = "serious"
            c "We're not fighting."
            $ fperry = "serious"
            p "Yes, you are."
            c "In any case, it's simple: you and Wade have a plan for this weekend, and I have another one."
            "Cindy turned to her boyfriend."
            c "You're welcome to join me, but I guess you don't want to give up on that stupid tournament. And I don't want to give up on the photo shoot."
            w "So why don't you ask him to do it another day?"
            c "Axel will be out of town for the next two weeks because of work. Besides, he's doing this as a favor and not charging me anything."
            c "You can come with me or not, it's your choice."

    stop music fadeout 2.0
    w "..."
    $ fian = "sad"
    $ fperry = "sad"
    hide perry
    hide perry2
    show perry2 at right
    with short
    p "Dude, you can't miss the t--{w=0.5}tournament. Pros from all over the country are coming, it's your moment to test your might!"
    $ fwade = "n"
    w "I know."
    if v6_confess_wade:
        w "Maybe you could fill in for me, Perry..."
        $ fperry = "serious"
        c "What?"
        if cafe_perry:
            $ fperry = "sad"
            p "I can't. I told Mrs. Van Dyke I would h--{w=0.5}help out at the café this weekend..."
        else:
            p "No way, dude. I don't want to miss the tournament either, even if I'm not c--{w=0.5}competing."
        c "And I don't want to have him there either!"
        $ fperry = "meh"
        w "In that case..."
        $ fwade = "serious"
        "Wade directed a significant look at me. It wasn't a friendly one."
        $ fian = "worried"
        w "Ian already went with you once. He can surely tag along again."
        $ fcindy = "blush"
        c "Well, about that..."
    else:
        "Wade looked at me."
        w "Could you fill in for me, Ian?"
        $ fian = "worried"
        if v7_cindy_kiss:
            $ fcindy = "blush"
            i "Who, me?"
        elif v5_cindy_shoot or ian_cindy_model:
            i "Who, me?"
        else:
            $ fcindy = "surprise"
            c "What?"
            i "Who, me?"
            $ fcindy = "serious"
        w "Yeah. I can't ask anyone else."
    menu:
        "I'll go":
            $ renpy.block_rollback()
            $ v9_cindy_shoot = 3
            $ fian = "n"
            i "Okay, I'll go."
            $ fperry = "meh"
            if v6_confess_wade:
                w "Good."
            else:
                $ fwade = "smile"
                w "Thanks, man."
            if ian_charisma < 9:
                call xp_up('charisma') from _call_xp_up_79
            if v7_cindy_kiss or v5_cindy_shoot or ian_cindy_model:
                if v7_cindy_kiss:
                    "This would be the perfect opportunity to finally talk to Cindy, just the two of us."
                    "And she didn't seem to be against it."
                    $ fcindy = "n"
                    c "I find this pretty absurd, but if that's what you need to put your mind at ease, so be it."
                elif v5_cindy_shoot or ian_cindy_model:
                    if ian_cindy < 5:
                        $ fcindy = "serious"
                        "Cindy gave me a sour look. She didn't seem too happy about me tagging along."
                    else:
                        $ fcindy = "n"
                    c "This is absurd, but if that's what you need to put your mind at ease so be it."
                w "Problem solved, then."
                w "It's late, I'mma call it a night."
                c "Yeah. Good night, guys."
            else:
                $ fcindy = "serious"
                c "What's this? Do you trust me so little?"
                $ fwade = "serious"
                w "You're not giving me too many options."
                $ fcindy = "mad"
                c "So you're sending Ian to watch over me? That's fucking creepy!"
                $ fian = "worried"
                w "Either that, or we can go together in two weeks or whenever that guy comes back!"
                "Cindy stood up with clear intentions of leaving."
                $ fcindy = "serious"
                c "Whatever. Have it your way."
                c "It's late, I'mma call it a night."
                w "Sure."
                "Wade followed her and both left after saying goodbye."
                $ fian = "n"
            play sound "sfx/door.mp3"
            hide wade
            hide cindy
            with short
            show ian at lef
            show perry2 at rig
            with move
            p "Are you sure that was a good idea?"
            i "I'm just trying to help Wade."
            p "Yeah, well... You're g--{w=0.5}getting involved in something you shouldn't, but that's just my opinion."
            if v7_cindy_kiss:
                "Perry had a point... But I had my own reasons to agree to Wade's request."
                "After all, I was already much more involved than I should be in this mess."
            else:
                "Perry had a point... But I had already made my choice."
            p "Anyway, it f--{w=0.5}fucking sucks for Wade. Seems like Cindy isn't giving him a break."
            i "Things have been rather tense between them, yeah."
            p "Seeing that shit-show makes me glad about being s--{w=0.5}single."
            p "Well, I'm going to bed. Goodnight."

        "It's not my place" if v7_cindy_kiss == False:
            $ renpy.block_rollback()
            $ fian = "sad"
            i "I'm sorry, but I don't think it's my place to be there..."
            $ fwade = "n"
            w "Come on, man. I'm asking you a favor."
            i "It's not like I don't want to help you, but... I don't feel comfortable with the idea."
            if v6_confess_wade:
                $ fwade = "serious"
                w "You didn't seem to have any problem the first time."
                if ian_wade > 0:
                    call friend_xp('wade', -1) from _call_friend_xp_106
                $ fian = "worried"
                $ fcindy = "serious"
                i "And that was a mistake. I shouldn't have gotten involved..."
            $ fcindy = "serious"
            c "And I'm also not comfortable with having someone there monitoring me on your behalf. I'm not cattle, you know?"
            $ fwade = "serious"
            w "Okay, whatever. Go alone to that fucking photo shoot, if you are so eager to do so. I don't care."
            "Wade got up with clear intentions of leaving, and Cindy followed suit."
            c "Fine. Good night, guys."
            hide wade
            hide cindy
            with short
            $ fian = "worried"
            $ fperry = "meh"
            show ian at lef
            show perry2 at rig
            with move
            p "So I guess this is it for tonight. What a sh--{w=0.5}shit show."
            i "Maybe I should have agreed to go..."
            p "No, I think you did the right th--{w=0.5}thing. It's something they have to figure out themselves, we sh--{w=0.5}shouldn't get involved."
            $ fian = "n"
            i "That's something surprisingly wise coming from you."
            p "Whatever. I'm going to bed."

    if v9_alison_trip:
        i "Me too. I have to get up early tomorrow..."
    else:
        i "Yeah, me too."
    scene ianhomenight_dark with long
    pause 0.5
    if ian_holly_dating and v9_alison_trip == False:
        play sound "sfx/door.mp3"
        scene ianroomnight
        show ian
        with long
        "I was getting ready to go to bed when my phone buzzed with an incoming text."
        $ fian = "smile"
        i "Oh, it's Holly's."
        nvl clear
        h_p "{i}Thanks for today, it was really fun {image=emoji_smile.webp}{/i}"
        i_p "{i}It was! I hope you join us next time too.{/i}"
        jump v9hollynudes
        label v9hollynudes2:
            menu:
                "{image=icon_lust.webp}You're so damn sexy" if ian_lust > 4:
                    $ renpy.block_rollback()
                    if holly_change < 4:
                        $ holly_change += 1
                    $ fian = "confident"
                    i_p "{i}Damn Holly, you're so damn sexy {image=emoji_fire.webp}{image=emoji_fire.webp}{/i}"
                    h_p "{i}Nobody has ever called me \"sexy\" before {image=emoji_shy.webp}{/i}"
                    i_p "{i}Well, get used to it. I loved these pics, and I hope to get more!{/i}"
                    h_p "{i}Noted{image=emoji_crazy.webp}{/i}"
                    h_p "{i}Good night, Ian {image=emoji_lips.webp}{/i}"
                    hide v9_holly_selfie2 with short
                    show ian at truecenter with move
                    $ fian = "smile"
                    i "What a surprise... I love seeing how Holly is loosening up with me."

                "I love these":
                    $ renpy.block_rollback()
                    i_p "{i}Wow Holly, I love these! You're so cute and sexy {image=emoji_flirt.webp}{/i}"
                    if ian_lust < 5:
                        call xp_up('lust') from _call_xp_up_80
                    h_p "{i}For real?{/i}"
                    i_p "{i}Absolutely. You're lovely.{/i}"
                    h_p "{i}Thank you {image=emoji_shy.webp}{/i}"
                    i_p "{i}No, thank you for sending these. I'll treasure them.{/i}"
                    h_p "{i}I'm glad. Good night, Ian {image=emoji_lips.webp}{/i}"
                    hide v9_holly_selfie2 with short
                    show ian at truecenter with move
                    i "What a surprise... I love seeing how Holly is loosening up with me."

                "Thank you for these":
                    $ renpy.block_rollback()
                    i_p "{i}Thank you for the pics. I appreciate them.{/i}"
                    h_p "{i}I know they're nothing spectacular, but... I hope you liked them {image=emoji_ups.webp}{/i}"
                    i_p "{i}I did. You're so cute, Holly {image=emoji_smile.webp}{/i}"
                    i_p "{i}Good night {image=emoji_moon.webp}{/i}"
                    hide v9_holly_selfie2 with short
                    show ian at truecenter with move
                    i "This was surprising... Holly seems to be loosening up a bit, that's good."

        i "I wonder how things will evolve from now on..."
# Jeremy Emma
    if emma_jeremy and v9_emma_sext > 0:
        if ian_holly_dating == False and v9_alison_trip == False:
            $ fian = "smile"
            i "And now that I think about it, I wonder how Emma and Jeremy's date went, too."
            i "This is the second time they hook up as far as I know..."
        else:
            play sound "sfx/door.mp3"
            scene ianroomnight
            show ian
            with long
            i "Time for bed..."
            i "I wonder how Emma and Jeremy's date went. This is the second time they hook up as far as I know..."
        menu:
            "Text Jeremy":
                $ renpy.block_rollback()
                label gallery_CH09_S14:
                    if _in_replay:
                        call setup_CH09_S14 from _call_setup_CH09_S14
                $ fian = "smile"
                "I pulled up my phone and texted my friend."
                nvl clear
                i_p "{i}What's up, bro? You're taking a liking to Emma, it seems!{/i}"
                play sound "sfx/sms.mp3"
                j_p "{i}Hey bro! You bet!{/i}"
                j_p "{i}At first, I thought she wasn't my type, but she's a freak in bed!{/i}"
                i_p "{i}I know. And that ass, right?{/i}"
                show ian at left with move
                play sound "sfx/sms.mp3"
                show v9_emma2 with short
                $ ian_emma_pics.append("v9_emma2.webp")
                j_p "{i}That ass indeed {image=emoji_ass.webp}{image=emoji_ass.webp}{image=emoji_ass.webp}{/i}"
                i_p "{i}Nice pic! Did she send it to you?{/i}"
                j_p "{i}Yeah. Wanna see more? {image=emoji_crazy.webp}{/i}"
                menu:
                    "Yes":
                        $ renpy.block_rollback()
                        $ v9_emma_sext = 2
                        $ fian = "confident"
                        i_p "{i}Sure, show me {image=emoji_glasses.webp}{/i}"
                        hide v9_emma2
                        show v9_emma3
                        with short
                        $ ian_emma_pics.append("v9_emma3.webp")
                        i "Damn, what a view. Emma really is a dirty girl..."
                        if ian_lust < 9:
                            call xp_up('lust') from _call_xp_up_81
                        hide v9_emma3
                        show v9_emma5
                        with short
                        $ ian_emma_pics.append("v9_emma5.webp")
                        i "I guess one finger is not enough for her! Her ass is made to be fucked..."
                        hide v9_emma5
                        show v9_emma7
                        with short
                        $ ian_emma_pics.append("v9_emma7.webp")
                        i "Damn, she really is a freak... One hole is not enough for her."
                        i "I remember her doing something like that the first time we had sex, but I was balls-deep in her ass at that time."
                        $ fian = "happy"
                        i "Too bad Jeremy can't enjoy it as I do!"
                        hide v9_emma7
                        show v9_emma6
                        with short
                        $ ian_emma_pics.append("v9_emma6.webp")
                        $ fian = "smile"
                        i "Emma seems to enjoy sucking his cock, though..."
                        $ fian = "confident"
                        i "She's surprisingly good at it. I want to try her lips again..."
                        hide v9_emma6
                        show v9_emma8
                        with short
                        $ ian_emma_pics.append("v9_emma8.webp")
                        i "Damn, she totally looks like a pornstar with her face covered in jizz."
                        $ fian = "happy"
                        i "Perry would have a heart attack if he ever saw these pictures!"
                        i "He already would have a hard time accepting I've fucked Emma, but Jeremy..."
                        i "He would go nuts."
                        hide v9_emma8 with short
                        show ian at truecenter with move
                        i "Seems that was the last photo."
                        i_p "{i}Great pics, bro. Emma's a great fuck {/i}"
                        j_p "{i}Hell yeah! I owe you one, man! You got me laid {image=emoji_laugh.webp}{/i}"
                        if not _in_replay:
                            $ gallery_unlock_scene("CH09_S14")


                    "No":
                        $ renpy.block_rollback()
                        i_p "{i}No need.{/i}"
                        hide v9_emma2 with short
                        show ian at truecenter with move
                        j_p "{i}Anyway, I owe you one! You got me laid {image=emoji_laugh.webp}{/i}"

                if ian_jeremy < 12:
                    call friend_xp('jeremy', 1) from _call_friend_xp_107
                i_p "{i}I'm a great friend, aren't I?{/i}"
                if alison_jeremy_3some > 0:
                    j_p "{i}No doubt! Maybe we can double team Emma too, I'm sure she'd be up for it!{/i}"
                    $ fian = "happy"
                    i_p "{i}Let's not get too crazy just now, okay? {image=emoji_laugh.webp}{/i}"
                else:
                    j_p "{i}No doubt! I'll try to return the favor {image=emoji_glasses.webp} {/i}"
                j_p "{i}See you at the gym, bro!{/i}"
                i "Time to call it a day..."
                scene ianroomnight
                $ renpy.end_replay()

            "Go to bed":
                $ renpy.block_rollback()
                $ fian = "smile"
                i "I'm sure they had fun. Jeremy can't complain, I'm a really good friend!"
                if ian_jeremy < 8:
                    call friend_xp('jeremy', 1) from _call_friend_xp_108

## IAN THURSDAY ###################################################################################################################################################################################################

    if v9_alison_trip:
        jump v9tripalison_start
    else:
        jump v9ianthursday
##################################################################################################################################################################################################################
## IAN ALONE ##################################################################################################################################################################################################################
##################################################################################################################################################################################################################
label v9ianthursday:
    $ ian_look = 1
    $ fian = "n"

    call calendar(_day="Thursday") from _call_calendar_10
    if ian_job_magazine == 2 and v5_ian_showup:
        scene magazine with long
        "The next morning was rather boring and unremarkable, as expected."
        scene street with long
        show ian with short
        "After I was done at the office I headed to the gym, like every Thursday. I had some spare time before the class started, though..."
    else:
        if v7_effort_job:
            scene v8_rider1 with long
            if ian_job_magazine == 0:
                "The next morning was unremarkable, as expected. After doing some chores, I jumped on my bike and continued to work making deliveries."
            else:
                "The next day I was busy with my second job, making deliveries on my bike."
                "At least this way I could make up for the pay cut Minerva had punished me with."
                # call money(1) from _call_money_7
            "I worked from noon until it was time to go to the gym, like every Thursday."
            scene street with long
            show ian with short
            "I had some spare time before the class started, though..."
        else:
            scene ianroom with long
            "The next morning was pretty unremarkable. I used it to keep working on my book, getting closer to the finish line."
            show ian with short
            "I decided to finally take a break during the afternoon. I had planned to go to the gym, like every Thursday."
            "A couple of hours remained until the class started, though..."
    menu:
        "{image=icon_pay.webp}Shop for clothes" if ian_money > 0:
            $ renpy.block_rollback()
            "I decided to drop by the mall and check a few shops. I hadn't bought new clothes in ages."
            i "This hoodie has served me well, but it's getting a bit ragged. Same as most of my shirts..."
            jump v9ianthursdayshop

        "Go to the gym":
            $ renpy.block_rollback()
            "I decided to go straight to the gym. No time to waste."
            play music "music/normal_day3.mp3" loop
            jump v9ianthursdaynight
##SHOPPING
label v9ianthursdayshop:
    play music "music/normal_day3.mp3" loop
    scene mall with long
    show ian with short
    i "Let's be efficient about this... I don't have much time."
    "I stepped into the clothing store and started browsing for something that I might like."
    show ian at left with move
    hide ian
    $ fian = "n"
    show ianunder at left
    with short
    i "Let's see..."
    jump v9clothingian
label v9ianthrusdaypregym:
    if ian_wardrobe_wits1 or ian_wardrobe_charisma1 or ian_wardrobe_athletics1 or ian_wardrobe_lust1:
        hide ianunder with short
        if v9_ianwearing == "wits":
            $ ian_look = "wits1"
        elif v9_ianwearing == "charisma":
            $ ian_look = "charisma1"
        elif v9_ianwearing == "athletics":
            $ ian_look = "athletics1"
        elif v9_ianwearing == "lust":
            $ ian_look = "lust1"
        else:
            $ ian_look = 2
        show ian with short
        $ fian = "smile"
        i "Alright, I like this one!"
        hide ian with short
        "I left the mall wearing my new clothes and headed to the gym."
    else:
        $ fian = "n"
        i "I can't seem to find anything I like... I guess my old wardrobe will have to do."
        hide ian with short
        "I left the mall without buying anything and went straight to the gym."
## GYM
label v9ianthursdaynight:
    scene gym with long
    if jiujitsu > 1:
        $ ian_look = "gi"
        "The class with Wen was as intense and tiresome as usual, and this time Yuri chimed in to add to the training."
    else:
        $ ian_look = 7
        "The class with Yuri was as intense and tiresome as usual, and this time Wen chimed in to add to the training."
    # no jeremy
    if alison_jeremy:
        show ian at lef3
        show wen
        show yuri at rig3
        with short
        if ian_athletics > 4:
            $ fian = "smile"
            i "That was a good one! I'm beat."
            wen "You're doing fine. Keep working hard and maybe we'll even make a fighter out of you!"
        else:
            $ fian = "worried"
            i "I'm about to throw up."
            yuri "You should train more consistently, otherwise, you won't improve!"
        if ian_alison_dating:
            i "Where's Jeremy, by the way? It's weird he didn't show up."
            yuri "He's been training hard for the tournament, but he said something about going on a trip with a girl or something."
            i "Of course... The only thing that's more important than this to him is girls."
            hide wen
            show wensmile
        else:
            yuri "By the way, Ian, do you know where Jeremy is?"
            yuri "It's weird for him not to show up today. The tournament is next month."
            i "Yeah, he went on a trip with a friend... With a girl."
            hide wen
            show wensmile
            wen "A girlfriend?"
            i "A friend with benefits. But yeah, the only thing that could keep him away from the gym is a girl."
        wen "We've all had a phase like that at some point."
        yuri "We all have our weaknesses! But I hope he doesn't get too complacent, the tournament is next month."
        if tournament:
            hide wensmile
            show wen
            $ fian = "n"
            i "I hope I'm ready."
            if ian_athletics > 5:
                wen "You have the potential to do good."
                $ fian = "happy"
                i "Are you praising me perhaps?"
                wen "Maybe just a bit."
            elif ian_athletics > 3:
                wen "You'll only know when you try."
                i "I guess so."
            else:
                yuri "Maybe you should think it through. It's not mandatory."
                i "Uh... Okay."
# with jeremy
    else:
        $ jeremy_look = 2
        show ian at left
        show wen at lef
        show jeremy at rig
        show yuri at right
        with short
        if ian_athletics > 4:
            $ fian = "smile"
            i "That was a good one! I'm beat."
            wen "You're doing fine. Keep working hard and maybe we'll even make a fighter out of you!"
            j "I feel great, too! My striking is on a whole new level!"
        else:
            $ fian = "worried"
            i "I'm about to throw up."
            yuri "You should train more consistently, otherwise, you won't improve!"
            j "I feel I've improved loads recently. My striking is on a whole new level!"
        yuri "Hold your horses. You've improved, that's true, but you still have holes in your game."
        hide wen
        show wensmile at lef
        wen "Shall I take you down again?"
        $ fjeremy = "n"
        j "No, you already proved your point."
        $ fjeremy = "flirt"
        j "But that's only because it's you, Wen. A normal human being won't take me down so easily!"
        yuri "That's true. You have long limbs and good balance, but don't get too overconfident."
        yuri "The tournament is next month."
        hide wensmile
        show wen at lef
        if tournament:
            $ fian = "n"
            i "I hope I'm ready."
            if ian_athletics > 5:
                wen "You have the potential to do good."
                $ fian = "happy"
                i "Are you praising me perhaps?"
                wen "Maybe just a bit."
            elif ian_athletics > 3:
                wen "You'll only know when you try."
                i "I guess so."
            else:
                yuri "Maybe you should think it through. It's not mandatory."
                i "Uh... Okay."
        else:
            i "Yeah, good luck with that."
            $ fjeremy = "happy"
            j "I hope you come to cheer for me!"
    stop music fadeout 2.0
    scene streetnight with long
    $ jeremy_look = 1
    $ fjeremy = "smile"
    $ fian = "n"
    if v9_ianwearing == "wits":
        $ ian_look = "wits1"
    elif v9_ianwearing == "charisma":
        $ ian_look = "charisma1"
    elif v9_ianwearing == "athletics":
        $ ian_look = "athletics1"
    elif v9_ianwearing == "lust":
        $ ian_look = "lust1"
    else:
        $ ian_look = 2
    if alison_jeremy == False:
        show ian at lef
        show jeremy at rig
    else:
        show ian
    with short
    if alison_jeremy == False:
        if ian_look == "charisma1":
            j "You look slick, bro! Since when do you dress so elegantly?"
            $ fian = "smile"
            i "It's never too late to start, right?"
        elif ian_look == "athletics1":
            j "Cool new shirt, bro! You need to show those gains!"
            $ fian = "smile"
            i "Thanks."
        elif ian_look == "lust1":
            j "Damn, you're looking fly my dude! Where did you get that jacket?"
            $ fian = "smile"
            i "You like it, huh?"
            j "It's really cool."
        if louise_jeremy and v7_bbc == "lena":
            j "I need to get going. I'm meeting Louise..."
            i "Sure. See you."
        else:
            $ fjeremy = "flirt"
            j "I need to get going. I'm meeting with this girl..."
            i "Ivy?"
            $ fjeremy = "n"
            j "No... She's playing hard to get again."
            i "Didn't she suck your dick already?"
            j "Yeah, and I thought everything would play smoothly after that, but I haven't managed to get a proper date with her yet..."
            $ fjeremy = "smile"
            j "But I can see she likes teasing me, so the game is not over. I know I will get the prize if I keep playing!"
            j "Anyway, gotta go!"
        hide jeremy with short
        show ian at truecenter with move
    else:
        i "Time to get going."
    if ian_cherry_dating:
        "I checked my phone as I started walking."
        "To my surprise, I had a missed call from Cherry and a text message."
        nvl clear
        ch_p "{i}Hey Ian, are you free?{/i}"
        menu:
            "{image=icon_friend.webp}Meet Cherry":
                $ renpy.block_rollback()
                $ v9_cherry_date = True
                jump v9cherrydate

            "I'm busy":
                $ renpy.block_rollback()
                $ fian = "sad"
                i "I'm not sure it's a good idea to meet with her after what happened last time."
                if ian_lena_dating:
                    i "Especially considering my relationship with Lena... I want to stay away from drama."
                else:
                    i "It's not like it was Cherry's fault, but... I want to stay away from drama."
                i "I should put some distance between us."
                $ fian = "n"
                i_p "{i}I am really busy at the moment.{/i}"
                # "I texted her back letting her know I was busy at the moment."
                play sound "sfx/sms.mp3"
                ch_p "{i}It's okay. We'll talk some other time!{/i}"
                i "Alright..."
                jump v9ianthursdayhome
    else:
        jump v9ianthursdayhome
################################################################################################################################################################################
## DATE WITH CHERRY ################################################################################################################################################################################
################################################################################################################################################################################
label v9cherrydate:
    i "Something's up... I'm calling her."
    hide ian
    show ian_phone
    with short
    i "..."
    show phone_cherry_sad at lef3
    ch "Hey, Ian."
    i "What's up? I was at the gym and just saw your text."
    ch "Are you free to talk?"
    i "Yeah. Do you want to meet?"
    ch "Yes... I'm close to that bar where we met, Shine."
    i "Alright. On my way. See you in a bit."
    hide phone_cherry_sad
    hide ian_phone
    show ian
    with short
    i "She sounded kind of down... I hope everything's okay."
    hide ian with short
    label gallery_CH09_S02:
        if _in_replay:
            call setup_CH09_S02 from _call_setup_CH09_S02
    play music "music/normal_day2.mp3" loop
    scene cocktailbar with long
    show ian at lef3 with short
    "When I arrived I saw Cherry sitting alone at the bar."
    $ fcherry = "sad"
    show cherry at rig with short
    "The place was rather quiet at that hour. Not many customers and the music wasn't loud, so we could talk comfortably."
    show ian at lef with move
    i "Hey."
    ch "Hi, Ian."
    if ian_look == "wits1" or ian_look == "charisma1" or ian_look == "athletics1" or ian_look == "lust1":
        $ fcherry = "smile"
    if ian_look == "wits1":
        ch "Nice sweater. I like how it looks on you."
        $ fian = "smile"
        i "Thanks."
    elif ian_look == "charisma1":
        ch "You're looking really handsome today."
        $ fian = "smile"
        i "Thanks. New shirt."
    elif ian_look == "athletics1":
        ch "You've really been hitting the gym lately, haven't you?"
        $ fian = "smile"
        i "I try to."
    elif ian_look == "lust1":
        ch "Oh, change of look?"
        $ fian = "smile"
        i "I'm trying a new style. Do you like it?"
        ch "It suits you."
    $ fian = "n"
    "I ordered a drink and sat next to Cherry, ready to listen to what she had to say."
    i "So... Is everything alright?"
    $ fcherry = "sad"
    ch "Yeah... I just wanted to apologize for what happened last night."
    i "Oh. You don't need to... It's not like it was your fault or anything."
    ch "I feel it somehow was. And I didn't get the chance to explain myself to you..."
    i "You know you don't owe me an explanation, right? You don't have to tell me about it if you don't want to..."
    ch "But I want to. You guys have been cool to me and I have the feeling I ruined your night."
    ch "Suddenly finding Lena there, though... It caught me completely off guard."
    ch "I guess she told you about our history, right?"
    i "I know a bit... She learned Axel was cheating on her, and the girl he was sleeping with was you."
    "Cherry took a deep breath before answering."
    ch "Yeah. That's right."
    ch "I'm not proud of what I did. Not at all. Lena has every right to hate me."
    menu:
        "I'm sure you didn't do it on purpose":
            $ renpy.block_rollback()
            $ v9_cherrytalk = "naive"
            i "I'm sure you didn't do it on purpose. Axel probably kept that he was dating Lena from you, or..."
            ch "No. I knew they were together from the beginning."
            $ fian = "sad"
            ch "I knew what I was doing... And did it anyway."
            i "It takes some bravery to admit one's mistakes..."
            if ian_charisma < 9:
                call xp_up ('charisma') from _call_xp_up_82
            i "And... why did you do it?"
            ch "I just..."

        "Why did you do it?":
            $ renpy.block_rollback()
            $ v9_cherrytalk = "neutral"
            i "Why did you do it? You knew she was in a relationship with Axel, right?"
            ch "Yeah, I did..."
            i "I'm not trying to judge you here. I can see you feel what you did was wrong, but I guess you had your reasons..."
            if ian_wits < 9:
                call xp_up ('wits') from _call_xp_up_83
            ch "I did, but that doesn't justify it. I just..."

        "What you did was wrong":
            $ renpy.block_rollback()
            $ v9_cherrytalk = "wrong"
            $ fian = "serious"
            i "What you did was wrong. Cheating on someone is one of the worst things one can do..."
            "Cherry's story was reminding me too much of what happened to me with Gillian. I hated it..."
            call friend_xp('cherry', -2) from _call_friend_xp_109
            ch "I know. It's not like I was cheating on anybody, though."
            ch "But I got involved in their relationship, and that's wrong. I knew that all along, but..."
            $ fian = "n"
            i "But?"
            ch "But I just couldn't help myself. I just..."

    ch "I guess I was in love."
    $ fian = "sad"
    i "So that guy you told me about, the one you were still hung up on, is Axel."
    ch "Yeah. After his breakup with Lena, well..."
    ch "He was in a very bad place emotionally. I tried talking with him, but he blocked me out."
    ch "It was clear he didn't want me there, so I left. Tried to move on."
    $ fian = "n"
    i "But you saw him again a while back, right? Talked to him."
    ch "Yeah. It was... weird."
    ch "He looked okay, like he was doing fine again. And he apologized."
    i "Did he try to make a move on you?"
    ch "No, not at all. In fact, I could feel a wall between us, despite him acting politely."
    ch "And I... I felt ashamed of myself."
    $ fian = "worried"
    i "Why?"
    ch "Because a part of me wanted to jump over that wall. To break it down."
    ch "To ask him to be with me again."
    ch "After everything that happened... After all the mayhem I caused, after all the pain..."
    ch "I thought I had been moving on with my life, but those old feelings were still there."
    ch "I thought that maybe now that Lena wasn't in the picture he'd take me, but nothing further from the truth..."
    "Cherry paused, visibly distraught like she was trying hard to contain her emotions."
    ch "Sorry... I don't want to burden you with my stupid drama, it's just..."
    menu:
        "{image=icon_love.webp}Comfort Cherry" if v9_cherrytalk != "wrong":
            $ renpy.block_rollback()
            $ ian_cherry_love = True
            if ian_look == "wits1":
                scene v9_cherry1a
            elif ian_look == "charisma1":
                scene v9_cherry1b
            elif ian_look == "athletics1":
                scene v9_cherry1c
            elif ian_look == "lust1":
                scene v9_cherry1d
            else:
                scene v9_cherry1e
            with long
            "Cherry had been looking down as she spoke."
            "I held her chin softly with my fingers, inviting her to raise her head."
            i "Cherry, it's okay... We all make mistakes."
            ch "This one was a fucking big one."
            i "Yeah. And incredibly painful, I'm sure."
            i "You messed up big time. But you're not gloating or dismissing it."
            i "I can see how much you care."
            show v9_cherry1_eyes with short
            "She finally looked up, and her eyes met mine."
            if ian_charisma < 9:
                call xp_up ('charisma') from _call_xp_up_84
            ch "I wish that was enough."
            i "It is to me. I think you're a decent person, really."
            i "One I care about."
            if ian_cherry < 11:
                call friend_xp('cherry', 2) from _call_friend_xp_110
            elif ian_cherry < 12:
                call friend_xp('cherry', 1) from _call_friend_xp_111
            ch "..."
            "She stared at me, and for a moment I felt like she was about to kiss me..."
            $ fian = "n"
            scene cocktailbar
            show ian at lef
            show cherry at rig
            with short
            "But she leaned back on the stool and looked at me with a conflicted expression."
            ch "I'm sorry, but I need to ask..."

        "It's okay":
            $ renpy.block_rollback()
            $ fian = "sad"
            if v9_cherrytalk == "neutral":
                i "It's okay... As I said, I'm not judging you."
                ch "Thanks..."
            if v9_cherrytalk == "naive":
                i "It's okay... I'm here to listen."
                ch "Thanks..."
            if v9_cherrytalk == "wrong":
                i "As I said, I think what you did was wrong, but we all make mistakes."
            if ian_cherry < 12:
                call friend_xp('cherry', 1) from _call_friend_xp_112
            ch "Anyway, I did what I did... And that's why Lena reacted the way she did."
            ch "I'm sorry that my wrongdoings caused trouble for you."
            i "It's not just you who was in the wrong. Axel is to blame too, even more so than you."
            ch "And that's why Lena hates him too..."
            ch "I need to ask, though..."

        "You fell in love with the wrong guy":
            $ renpy.block_rollback()
            $ ian_cherry_over = True
            $ fian = "serious"
            if v9_cherrytalk != "wrong":
                "Cherry's story was reminding me too much of what happened to me with Gillian. I hated it..."
            i "Seems you fell in love with the wrong guy."
            call friend_xp('cherry', -1) from _call_friend_xp_113
            $ ian_cherry = 4
            ch "I can't argue with you on that. But the heart wants what the heart wants."
            $ fian = "n"
            i "I guess I will never understand."
            $ fcherry = "n"
            ch "I'm not asking you to. I just wanted to give you my apologies for the trouble I caused you and your friends."
            i "It is what it is. The real trouble is between you and Lena..."
            ch "About that..."

    ch "Why was Lena at your place that night? Is she your friend, or...?"
    if ian_lena_dating:
        menu:
            "{image=icon_love.webp}I'm serious about her" if ian_lena_love:
                $ renpy.block_rollback()
                $ fian = "n"
                i "Well, we've been... seeing each other for a while. And the truth is..."
                i "I think I'm serious about her."
                if ian_cherry_love:
                    $ ian_cherry_love = False
                    show cherry at rig3 with move
                    ch "Oh."
                ch "I see. Just as I imagined."
                ch "In that case, it'd be better if I keep my distance."
                ch "The last thing I want is to create more drama with her. Get between her and the guy she likes..."
                $ fian = "worried"
                i "Of course... I understand."
                i "Though I don't know if she likes me back the same way I do."
                $ fcherry = "smile"
                ch "Well, you seem to be clear about your feelings, and that's the only thing that matters."
                $ fian = "n"
                i "Yeah. You're right."
                $ fcherry = "n"
                ch "I wish you luck."

            "She's just a friend" if ian_lena_love == False:
                $ renpy.block_rollback()
                $ fian = "n"
                i "She's just a friend..."
                ch "The way you said {i}friend{/i} makes me think it's more than just that..."
                i "Well, yeah, we've hooked up a few times. But we're keeping things casual. Neither of us is looking for a committed relationship."
                ch "Is that so? I see."
                if ian_cherry_love:
                    show cherry at rig3 with move
                ch "In any case, I think it'd be better if I keep my distance."
                ch "The last thing I want is to create more drama with her. Get between her and the guy she likes..."
                if ian_cherry_love:
                    $ config.menu_include_disabled = False
                    $ greyed_out_disabled = True
                    menu:
                        "{image=icon_will.webp}Continue to date Cherry" if ian_charisma < 6 and ian_will > 0:
                            $ renpy.block_rollback()
                            call willdown from _call_willdown_9
                            jump v9cherrycontinue

                        "{image=icon_charisma.webp}Continue to date Cherry" if ian_charisma > 5:
                            $ renpy.block_rollback()
                            label v9cherrycontinue:
                                i "It won't be like that. She has no claim over me, nor I over her, I assure you."
                            i "She has no reason to get mad about it, and if she did, well... I'm free to see whoever I want."
                            i "Same as you."
                            ch "If it's like that, then..."
                            show cherry at rig with move
                            $ fcherry = "blush"
                            ch "Oh, look. Seems we've finished our drinks..."
                            i "Yeah. Want another one or do you have to get home early?"
                            ch "I don't... It's not like somebody's waiting for me there, so..."
                            $ config.menu_include_disabled = True
                            $ greyed_out_disabled = False
                            jump v9cherrysex

                        "Agree with Cherry's decision":
                            $ renpy.block_rollback()
                            $ ian_cherry_love = False
                            $ fian = "sad"
                            i "It's not like that, but I understand why you feel that way."
                            ch "I'm sorry, it's just... I'm still not comfortable with what happened between Lena and me, so..."
                            $ fian = "smile"
                            i "I said I understand, really. Don't worry about it."
                            $ fcherry = "n"
                            ch "Thanks, Ian. You really are a good guy."
                            $ config.menu_include_disabled = True
                            $ greyed_out_disabled = False
                else:
                    $ fian = "sad"
                    i "Yeah, you're probably right. I can imagine how uncomfortable this situation is for you."
                    ch "And I kinda dragged you into it. I'm sorry."
                    $ fian = "smile"
                    i "It's okay. I'm glad I got to know you, you're a cool girl."
                    $ fcherry = "n"
                    ch "Thanks, Ian. You really are a good guy."

            "I don't know yet":
                $ renpy.block_rollback()
                $ fian = "sad"
                i "The truth is I don't know yet."
                i "We've been seeing each other for a while, but we haven't... defined our status."
                if ian_cherry_love:
                    $ ian_cherry_love = False
                    ch "Oh. In that case..."
                    show cherry at rig3 with move
                    ch "I think it's better if I stay away."
                    ch "The last thing I want is to create more drama with her. Get between her and the guy she likes..."
                    $ fian = "worried"
                    i "Of course... I understand."
                else:
                    ch "I see. I won't ask further then, I just wanted to be sure."
                    i "That's how it is..."
                $ fcherry = "n"
                ch "I'm sorry for making things uncomfortable with the personal question."
                i "No, it's okay. You just opened up to me and you deserve an answer."
    else:
        i "She's just a friend. I met her a while ago at her workplace, we started chatting up and..."
        if ian_lena_over:
            i "Well, we had a fling, something casual, and things didn't really click."
            i "You could say we've agreed to... friendzone each other after that."
        else:
            i "Well, she seems like a really cool girl, so we started hanging out and I introduced her to most of my friends."
        $ fcherry = "n"
        ch "I see. Sorry about the personal question..."
        $ fian = "smile"
        i "No worries. I understand why you were curious."

    ch "Anyway, I feel better now that I got that off my chest."
    ch "I didn't want you guys to think ill of me, and after what happened..."
    ch "I should've gotten in touch sooner to apologize."
    menu:
        "{image=icon_lust.webp}Invite Cherry home" if ian_lena_dating == False:
            $ renpy.block_rollback()
            if ian_cherry_love:
                $ fian = "smile"
                i "Looks like we finished our drinks..."
                ch "Yeah..."
                i "So, do you have any plans for tonight, or...?"
                $ fcherry = "blush"
                ch "Not really. I mean, I have to get home, but it's not like anyone's waiting for me there..."
                jump v9cherrysex
            else:
                $ fian = "confident"
                i "So... Since we're here together and we've just finished our drinks..."
                i "I was wondering if you'd like to come hang out at my place tonight."
                stop music fadeout 2.0
                $ fcherry = "sad"
                show cherry at rig3 with move
                ch "Uh, I... I'm not really in the mood for that right now."
                call friend_xp('cherry', -2) from _call_friend_xp_114
                $ fian = "worried"
                i "Oh."
                if ian_lena_dating:
                    ch "I mean, we just agreed we should keep our distance all things considered..."
                    i "Of course."
                else:
                    i "Yeah, of course."
                i "After what you told me tonight, yeah... I understand."
                $ fcherry = "smile"
                ch "Anyway... Thank you for listening. Give my apologies to Perry and Emma, too."
                $ fian = "n"
                i "I will."
                ch "Goodnight, Ian."
                $ renpy.end_replay()
                jump v9ianthursdayhome2

        "{image=icon_friend.webp}Will you be okay?" if ian_cherry > 6:
            $ renpy.block_rollback()
            $ fian = "n"
            i "Will you be okay?"
            $ fcherry = "smile"
            ch "Yeah, don't worry. I know how to take care of myself."
            i "In any case, if you need to talk, or whatever... We won't stop hanging out with you despite what happened between you and Lena."
            i "Maybe just... Schedule it for different days."
            ch "Yeah, that'd certainly be the best."
            ch "It's getting a bit late..."
            i "Yeah, and we've already finished our drinks. Time to get going, I guess."
            $ fian = "smile"
            i "Take care, Cherry."
            if ian_cherry_love:
                $ ian_cherry_love = False
                $ fcherry = "blush"
                ch "Oh, you're leaving already?"
                $ fian = "n"
                i "Yeah, I thought you wanted to call it a night."
                $ fcherry = "sad"
                ch "No, yeah, of course."
                $ fcherry = "n"
            ch "Goodnight, Ian. See you around."
            $ renpy.end_replay()
            jump v9ianthursdayhome2

        "Bye":
            $ renpy.block_rollback()
            $ fian = "n"
            i "It's okay. We've finished our drinks, so unless you want to talk about anything else..."
            if ian_cherry_love:
                $ ian_cherry_love = False
                $ fcherry = "blush"
                ch "Oh, I was expecting..."
                i "Huh?"
                $ fcherry = "sad"
                ch "Forget it."
            else:
                ch "Yeah, let's get going."
            $ fcherry = "n"
            ch "Goodnight, Ian."
            i "Bye."
            $ renpy.end_replay()
            jump v9ianthursdayhome2
##CHERRY SEX SCENE ########################################################################################################################################################################################
label v9cherrysex:
    i "No one's waiting for me either, so maybe we could go together."
    $ fcherry = "smile"
    ch "Sounds good to me."
    i "Let's go, then."
    stop music fadeout 2.0
    scene cocktailbar with long
    play sound "sfx/door_home.mp3"
    scene ianhomenight_dark with long
    show ian at lef
    show cherry at rig
    with short
    "When we arrived at the apartment Perry had already retired to his room."
    "Good, I wasn't looking forward to explaining myself before taking Cherry to my bedroom."
    i "This way."
    play music "music/sex_bright.mp3" loop
    play sound "sfx/door.mp3"
    if ian_look == "wits1":
        scene v9_cherry2a
    elif ian_look == "charisma1":
        scene v9_cherry2b
    elif ian_look == "athletics1":
        scene v9_cherry2c
    elif ian_look == "lust1":
        scene v9_cherry2d
    else:
        scene v9_cherry2e
    with long
    "I embraced Cherry as soon as I closed the door behind us."
    "I had been wanting to do so this whole time... And it looked like Cherry had been feeling the same."
    "She welcomed my kisses as I tasted her amazing, fleshy lips, and pressed her body against mine when I wrapped my arms around her."
    "I felt a delicate, warm passion emanating from her. My kisses were trying both to excite and comfort her."
    "After the way she had opened up to me, I wanted to show her some tenderness."
    play sound "sfx/ah1.mp3"
    show v9_cherry3
    if ian_look == 2:
        show v9_cherry3b
    with long
    "I took my time tracing paths with my kisses down her chin, neck, and collarbone, making Cherry shiver."
    "I continued stimulating her, feeling her body tremble, her breathing becoming heavier, her hands grasping at my body with a growing desire."
    "Clothes started coming off, and my mouth found her stiff, erect nipples."
    "She moaned delightfully when I licked them, making them even harder."
    if ian_holly_dating:
        "They felt so similar to Holly's..."
        "I felt a remorseful sting when thinking about her, but I pushed those thoughts aside."
    "I was getting lost in the growing intensity of this moment I was sharing with Cherry."
    scene v9_cherry4 with long
    "My desire to make her feel good led me to make her lay down on the bed, gently spread her legs, and dive between them."
    "I felt the hot moisture of her pussy on my lips as I started kissing and licking it, softly at first, making her shiver again."
    play sound "sfx/ah2.mp3"
    ch "Ian..."
    "She closed her eyes and loosened her body, accepting my invitation to enjoy herself."
    "My tongue caressed her excited clit, hard and prominent, causing Cherry to moan with delight."
    "Her legs twitched and her hips contorted as she writhed in pleasure. I could feel it growing minute after minute."
    "Cherry twisted her hips, matching the increasing movements of my tongue. I felt she was getting close..."
    if ian_lust < 8:
        call xp_up('lust') from _call_xp_up_85
    ch "Stop, Ian, stop...!"
    ch "I need you inside of me, please..."
    scene v9_cherry5 with long
    "I granted her wish gladly."
    "My cock slid into Cherry's pussy smoothly, with a single thrust."
    play sound "sfx/ah6.mp3"
    ch "Ohh, yes... This is..."
    ch "Mhhh!"
    "I began pumping my hips slowly, driving my cock as deep as I could, retracting it almost completely, and pushing it in again."
    "Cherry moaned and trembled underneath me, her hands caressing my body matching the rhythm of my thrusts."
    ch "Oh, Ian...! What are you doing to me?"
    menu:
        "{image=icon_love.webp}I'm making love to you":
            $ renpy.block_rollback()
            $ v9_cherry_sex = 2
            i "I'm making love to you."
            scene v9_cherry6 with long
            "I looked directly into her eyes as I said those words and then kissed her deeply."
            "Cherry wrapped her arms around me and dug her fingers in my flesh, pulling me toward her, making the kiss even deeper."
            "A sudden surge of intensity engulfed her, and I let myself get wrapped in it too."
            play sound "sfx/oh1.mp3"
            ch "Mhhh, Ian...!"
            ch "Ian!" with vpunch
            "I felt her pussy pulsating and contracting around my hard cock as I pushed it in all the way."
            "Her kisses were wild and passionate, her hands held me so tight it almost hurt..."
            play sound "sfx/orgasm1.mp3"
            ch "Ahhhh!!! {w=0.5}{nw}" with flash
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            if ian_lust < 8:
                call xp_up('lust') from _call_xp_up_86
            "Her whole body trembled and convulsed when the orgasm finally struck."
            "I had never seen Cherry cum this hard..."
            ch "Oh, Ian..."

        "{image=icon_lust.webp}I'm making you cum" if ian_lust > 4:
            $ renpy.block_rollback()
            $ v9_cherry_sex = 1
            i "Easy... I'm making you cum."
            "I was laser-focused on that goal. And I knew I was getting close to reaching it."
            "I noticed Cherry was getting there while I was eating her out. Now I just had to finish the job."
            play sound "sfx/oh1.mp3"
            ch "Mhhh, Ian...!"
            "I felt her pussy pulsating and contracting around my hard cock as I pushed it in all the way."
            i "That's it. This is how you like it, right?"
            "I whispered in her ear as I continued to thrust deep and slow."
            i "Go ahead. Cum for me, Cherry."
            play sound "sfx/orgasm1.mp3"
            ch "Ahhhh!!! {w=0.5}{nw}" with flash
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            "Her whole body trembled and convulsed when the orgasm finally struck."

        "Making you feel good":
            $ renpy.block_rollback()
            i "I'm making you feel good... It's all I want right now."
            ch "Yes... It's so good... Keep doing it just like that...!"
            "I continued to thrust deep and slow, feeling her body tremble."
            i "Like this? This is how you like it?"
            if ian_lust < 5:
                call xp_up('lust') from _call_xp_up_87
            play sound "sfx/oh1.mp3"
            ch "Yes... Ahhh...!"
            jump v9cherrysexorg

        "...":
            $ renpy.block_rollback()
            i "..."
            "I was too concentrated on the task at hand to say anything."
            "I didn't want to interrupt Cherry's beautiful moans..."
            play sound "sfx/oh1.mp3"
            label v9cherrysexorg:
                "I felt her pussy pulsating and contracting around my hard cock."
            play sound "sfx/ah5.mp3"
            ch "Ahhhh!!! {w=0.5}{nw}" with flash
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            "Her whole body trembled and convulsed when the orgasm finally struck."

    # second round
    if ian_athletics < 5:
        menu:
            "{image=icon_will.webp}Go for a second round" if ian_will > 0:
                $ renpy.block_rollback()
                call willdown from _call_willdown_10
                jump v9cherrysexsecondr

            "Go to sleep":
                $ renpy.block_rollback()
                jump v9cherrysexsleep
    else:
        menu:
            "{image=icon_athletics.webp}Go for a second round" if ian_athletics > 4:
                $ renpy.block_rollback()
                label v9cherrysexsecondr:
                    if v9_cherry_sex == 2:
                        "It took her a few long seconds to recover and open her eyes again."
                        scene v9_cherry5 with long
                    elif v9_cherry_sex == 1:
                        "It took her a few long seconds to recover and look at me again."
                        ch "Oh, God..."
                    else:
                        "It took her a few long seconds to recover and look at me again."
                        ch "Ohhh..."
                "My cock was still hard and throbbing inside her."
                ch "You're not done, are you?"
                ch "I want you to cum, too..."
                scene v9_cherry7 with long
                "Now that I had accomplished my goal of making Cherry enjoy herself, it was time for me to do just that."
                i "I want you on all fours..."
                if ian_lust < 9:
                    call xp_up('lust') from _call_xp_up_88
                "Cherry turned around gladly, and I penetrated her once more."
                "The view from that position was incredible..."
                "Cherry's beautiful dark skin glistening with sweat, revealing the sexy volumes of her toned back as her muscles tensed with every thrust..."
                "My hands over her perfectly tight, round ass. Guiding her back and forth, seeing my manhood disappear into her hungry pussy again and again..."
                "The way she moved, slowly and seductively, trying to pleasure me with her body..."
                i "You're so beautiful, Cherry...!"
                menu:
                    "{image=icon_lust.webp}Anal cowgirl" if v6_cherry_anal > 0 or ian_lust > 5:
                        $ renpy.block_rollback()
                        "Her asshole was wet and shiny with her pussy juices, and it appeared loose and inviting..."
                        if v6_cherry_anal == 2:
                            i "Hey... Can I use your ass?"
                            ch "You know you can. I like it quite a lot..."
                        else:
                            $ v6_cherry_anal = 2
                            i "Hey... Would you like to try anal? Do you like it?"
                            ch "I do... In fact, I like it quite a lot."
                        scene v9_cherry8b with long
                        "To my surprise, Cherry took the lead. She made me lay down and got on top of me, guiding my cock into her anus..."
                        play sound "sfx/oh1.mp3"
                        ch "Mhhh!"
                        i "Oh, fuck."
                        "It was a tight fit, but my tool slid into Cherry's asshole with ease."
                        "From that position, it was up to her to do most of the work.  She drove her hips upwards and let them fall rhythmically, faster and faster."
                        "She moaned and panted, tightening her pelvic muscles and squeezing my cock even more with her ass."
                        "I closed my eyes and focused on enjoying myself. This was getting intense...!"
                        "It wasn't long until I felt I was about to reach my limit. I had been holding it in long enough."
                        i "I'm gonna cum, Cherry!"
                        ch "Do it! Now's your turn to cum for me!"
                        show v9_cherry8b_cum with flash
                        i "Ahhhh!!{w=0.6}{nw}" with vpunch
                        with vpunch
                        pause 0.6
                        with vpunch
                        pause 0.6
                        "She kept bouncing on my cock as convulsions overtook my body, making me shoot my seed into her rectum."

                    "Reverse cowgirl":
                        $ renpy.block_rollback()
                        scene v9_cherry8 with long
                        "I held Cherry firmly and sat down without pulling out, taking her on top of me."
                        ch "Oh, fuck yes... I love this position..."
                        i "I know. I remember the first time we had sex very well..."
                        "This time I let Cherry do most of the work, focusing on enjoying myself."
                        "Cherry drove her hips upwards and let them fall rhythmically, faster and faster."
                        "Her soaked pussy engulfed my cock entirely, again and again, and I knew I was about to reach my limit."
                        i "I'm gonna cum, Cherry!"
                        ch "Do it! Now's your turn to cum for me!"
                        show v9_cherry8_cum with flash
                        i "Ahhhh!!{w=0.6}{nw}" with vpunch
                        with vpunch
                        pause 0.6
                        with vpunch
                        pause 0.6
                        "She kept bouncing on my cock as convulsions overtook my body, making me shoot my seed into her pussy."

                    "Cum":
                        $ renpy.block_rollback()
                        "I had been holding it long enough. It was time to let my pleasure loose."
                        i "I'm gonna cum, Cherry!"
                        ch "Do it! Now's your turn to cum for me!"
                        i "Ahhhh!!{w=0.6}{nw}" with flash
                        with vpunch
                        pause 0.6
                        with vpunch
                        pause 0.6
                        with vpunch
                        pause 0.6
                        "My entire body tensed up as I shot my seed into her pussy."

                "I closed my eyes and enjoyed the sweet release. Having sex with Cherry was amazing..."
                stop music fadeout 2.0
                scene ianroomnight_dark with long
                "Next thing I knew, Cherry was laying in bed next to me, her warm body resting tightly against mine."
                "There was no need for any more words. We stayed just like that until sleep settled."

            "Go to sleep":
                $ renpy.block_rollback()
                label v9cherrysexsleep:
                    stop music fadeout 2.0
                "Cherry's breathing calmed down as her orgasm slowly subsided."
                scene ianroomnight_dark with long
                "I didn't get to cum, but Cherry seemed tired and ready to fall asleep. I didn't want to disturb her..."
                "And to be honest, I had no energy left for a second round after such a long day."
                "Still, I had enjoyed this so much. And so did Cherry, which was all I needed to be satisfied."
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH09_S02")

    ## morning after
    call calendar(_day="Friday") from _call_calendar_11
    $ ian_look = 2
    $ fian = "n"
    $ fcherry = "n"
    scene ianroom with long
    i "Mhhh..."
    "Some sounds and a hint of movement woke me up the next morning."
    show iannude2 at lef with short
    "I opened my foggy eyes and saw Cherry getting dressed under the first grey lights of the day."
    show cherrybra at rig with short
    i "Good morning..."
    $ fcherry = "blush"
    ch "Oh!"
    i "Are you leaving?"
    ch "Yeah... Sorry, I didn't want to wake you up."
    $ fian = "smile"
    i "It's okay. Don't you want to take a shower? I can prepare some coffee, too."
    ch "No, it's fine... I need to get going or I'll be... late for work."
    $ fian = "n"
    i "Oh. Okay."
    hide cherrybra
    show cherry at rig
    with short
    "Cherry finished getting dressed and picked her thing up."
    ch "So... See you around. And, uh, thanks for last night."
    i "Bye..."
    play sound "sfx/door.mp3"
    hide cherry with short
    i "..."
    i "She left without as much as a kiss on the cheek."
    i "I wonder if something's wrong... I'd say last night went perfectly."
    play sound "sfx/shower.mp3"
    scene ianhome with long
    "Since I was already awake I decided to take a shower and get the day started."
    "I was a bit thrown off by Cherry's attitude, but I tried not to overthink things."
    if ian_wits > 6:
        "Listening to her last night had made me aware she was in a rather frail emotional state. I could relate."
        "In that case, giving her some space would be the best thing to do."
    play music "music/emmas_theme.mp3" loop
    $ fperry = "n"
    show ian at lef
    show perry at rig
    with long
    p "Finally. I h--{w=0.5}hope you left some warm water for me too."
    i "Good morning to you too."
    $ fperry = "meh"
    p "So, you brought Cherry home last night, right?"
    $ fian = "serious"
    i "So you're spying on me now?"
    $ fperry = "serious"
    hide perry
    show perry2 at rig
    with short
    p "No, I'm not. I'm simply not d--{w=0.5}deaf, and I live here too."
    i "You need to start using earplugs."
    p "Why do you think I b--{w=0.5}bought them? Last thing I want is to hear the d--{w=0.5}dirty things that go on in your room..."
    $ fperry = "meh"
    p "I just heard you when you two got home. I was still awake and I thought I heard Ch--{w=0.5}Cherry's voice."
    $ fian = "n"
    i "Yeah, I was with her."
    hide perry2
    show perry at rig
    with short
    p "And... How's she doing? I wasn't sure we'd s--{w=0.5}see her again after what happened last time..."
    $ fian = "sad"
    i "She wanted to meet to apologize and tell me her side of the story."
    p "Does it line up with what Lena t--{w=0.5}told you?"
    i "Yeah, pretty much. As she said, Lena has plenty of reasons to be mad at her..."
    i "I just hope she's not mad at us for still being friends with Cherry."
    p "I'd say you're more than just her f--{w=0.5}friend, but yeah..."
    p "Cherry seems like a really cool girl. I'd still like to h--{w=0.5}hang out with her, even if it's troublesome."
    i "Me too."
    p "If anything, it's this Axel dude who sounds like t--{w=0.5}trouble..."
    jump v9perryaxel

##HOME DIRECTLY TO PERRY ############################################################################################################################################################################################
label v9ianthursdayhome:
    $ fian = "n"
    $ fperry = "n"
    $ fcherry = "sad"
    play sound "sfx/door_home.mp3"
    scene ianhomenight with long
    show ian with short
    i "I'm back."
    # Cherry is there
    if ian_cherry_dating == False:
        show ian at lef3 with move
        show cherry
        show perry at rig3
        with short
        p "Hey."
        $ fian = "worried"
        ch "Hello..."
        play music "music/broken_dreams2.mp3" loop
        i "Hey, Cherry. I wasn't expecting to see you here today."
        ch "Yeah, sorry for dropping by so suddenly. I was in the area and..."
        p "She wanted to talk about what h--{w=0.5}happened that night with Lena and all."
        $ fian = "n"
        i "Oh, that. It's okay, you don't have to explain yourself to us or anything if you don't want to..."
        ch "But I do. I wanted to apologize, first of all."
        p "It was just bad luck. Nobody could've p--{w=0.5}predicted that would happen."
        ch "Still, it's because of my wrongdoings that it happened, and I ruined your night."
        ch "So I'm sorry."
        i "We said it's okay..."
        ch "I know you've heard from Lena about what happened. She was dating Axel and I was... sleeping with him behind her back."
        i "Something like that, yeah."
        if ian_lust > 4 and v8_lena_story:
            i "She also mentioned the threesomes."
            $ fcherry = "blush"
            ch "Yeah..."
        ch "I'm not proud of what I did. Not at all. Lena has every right to hate me."
        menu:
            "I'm sure you didn't do it on purpose":
                $ renpy.block_rollback()
                $ v9_cherrytalk = "naive"
                i "I'm sure you didn't do it on purpose. Axel probably kept that he was dating Lena from you, or..."
                ch "No. I knew they were together from the beginning."
                $ fian = "sad"
                ch "I knew what I was doing... And did it anyway."
                i "It takes some bravery to admit one's mistakes..."
                if ian_charisma < 9:
                    call xp_up ('charisma') from _call_xp_up_89
                p "And... w--{w=0.5}why did you do it?"
                $ fcherry = "blush"
                ch "I just..."

            "Why did you do it?":
                $ renpy.block_rollback()
                $ v9_cherrytalk = "neutral"
                i "Why did you do it? You knew she was in a relationship with Axel, right?"
                ch "Yeah, I did..."
                i "I'm not trying to judge you here. I can see you feel what you did was wrong, but I guess you had your reasons..."
                if ian_wits < 9:
                    call xp_up ('wits') from _call_xp_up_90
                $ fcherry = "blush"
                ch "I did, but that doesn't justify it. I just..."

            "What you did was wrong":
                $ renpy.block_rollback()
                $ v9_cherrytalk = "wrong"
                $ fian = "serious"
                i "What you did was wrong. Cheating on someone is one of the worst things one can do..."
                "Cherry's story was reminding me too much of what happened to me with Gillian. I hated it..."
                call friend_xp('cherry', -2) from _call_friend_xp_115
                $ fperry = "serious"
                p "Cut her some s--{w=0.5}slack. It's not like she was cheating on anybody herself. Not exactly..."
                ch "I got involved in their relationship, and that's wrong. I knew that all along, but..."
                $ fian = "n"
                i "But?"
                $ fcherry = "blush"
                ch "But I just couldn't help myself. I just..."

        ch " I guess I was in love."
        $ fperry = "meh"
        hide perry
        show perry2 at rig3
        with short
        p "The guy is also to blame. He was the one in a relationship, after all!"
        $ fcherry = "n"
        ch "And that's why Lena hates him too..."
        i "What did you find so appealing about Axel to justify acting as you did?"
        ch "I've wondered that myself a lot. I still do."
        ch "But aside from his obvious charms, I liked him for what most people don't see. What he only showed behind closed doors..."
        ch "Nevermind. The heart wants what the heart wants, even if it doesn't make any sense."
        p "So... Are you still seeing him? Lena's ex?"
        $ fcherry = "sad"
        ch "No... After his breakup with Lena, well..."
        ch "He was in a very bad place emotionally. I tried talking with him, but he blocked me out."
        menu:
            "Listen":
                $ renpy.block_rollback()
                ch "It was clear he didn't want me there, so I left. Tried to move on."
                ch "But I saw him again about a month ago, we stumbled upon each other at a bar."
                ch "We talked. He looked okay, like he was doing fine again. And he apologized."
                p "Did he t--{w=0.5}try to make a move on you?"
                ch "No, not at all. In fact, I could feel a wall between us, despite him acting politely."
                ch "And I... I felt ashamed of myself."
                $ fian = "worried"
                i "Why?"
                ch "Because a part of me wanted to jump over that wall. To break it down."
                ch "To ask him to be with me again."
                ch "After all that happened... After all the mayhem I caused, after all the pain..."
                ch "I thought I had been moving on with my life, but those old feelings were still there."
                ch "I thought that maybe now that Lena wasn't in the picture he'd take me, but nothing further from the truth..."
                "Cherry paused, visibly distraught like she was trying hard to contain her emotions."
                ch "Sorry... I don't want to burden you with my stupid drama, it's just..."
                if v9_cherrytalk == "neutral":
                    i "It's okay... As I said, we're not judging you."
                    p "No, we are not."
                    ch "Thanks..."
                    call friend_xp('cherry', 1) from _call_friend_xp_116
                if v9_cherrytalk == "naive":
                    i "It's okay... we're here to listen."
                    p "Yeah."
                    ch "Thanks..."
                    call friend_xp('cherry', 1) from _call_friend_xp_117
                if v9_cherrytalk == "wrong":
                    i "As I said, I think what you did was wrong, but we all make mistakes."
                    $ fperry = "serious"
                    p "Dude, don't j--{w=0.5}judge her. She just opened up to us."

            "Pretend to listen":
                $ renpy.block_rollback()
                if ian_chad < 5:
                    $ ian_chad += 1
                "I was not in the mood to listen to someone's drama that night... I just wanted to get some rest."
                "I dozed off for a while, thinking about my own stuff until Cherry was done."

        ch "Anyway, I did what I did... And that's why Lena reacted the way she did."
        ch "I'm sorry that my wrongdoings caused trouble for you."
        $ fperry = "n"
        $ fian = "n"
        hide perry2
        show perry at rig3
        with short
        p "You've already apologized too much, stop it."
        $ fcherry = "smile"
        ch "Okay."
        $ fcherry = "n"
        stop music fadeout 2.0
        ch "Well then, I think that's all I wanted to talk about... Sorry for imposing."
        $ fcherry = "smile"
        $ fperry = "serious"
        ch "I mean, sorry not sorry. I needed to talk to you guys."
        $ fperry = "n"
        $ fian = "smile"
        p "That's better."
        ch "I'll leave you be. See you some other time."
        if perry_cherry:
            "She kissed Perry on the cheek."
            ch "Let me know when you want to go get that beer at the Fortress."
            $ fperry = "smile"
            $ fian = "worried"
            p "Sure."
            ch "Bye bye."
        else:
            $ fian = "n"
            i "Bye."
    # Ian rejected Cherry
    else:
        show ian at lef with move
        show perry at rig with short
        play music "music/emmas_theme.mp3" loop
        p "Hey."
        p "I just got a call from Cherry."
        $ fian = "sad"
        i "You too? What did she want?"
        p "She t--{w=0.5}told me about what happened between her and Lena and that dude."
        i "What was her story? Does it line up with Lena's?"
        p "Yeah, pretty much. She h--{w=0.5}hooked up with that guy even though she was well aware he and Lena were dating."
        i "Jeez..."
        p "She sounded like she was really s--{w=0.5}sorry, though. And she wasn't trying to make any excuses."
        p "I believe she honestly feels really bad about it."
        i "Yeah, sadly that doesn't change what happened."
        p "Anyway, she asked me to a--{w=0.5}apologize to you too for what happened that night."
        $ fian = "sad"
        i "Maybe I should've called her..."
        i "But it's better this way. I don't blame Cherry for what happened, but I can see why Lena would be uncomfortable having her around."
        $ fperry = "meh"
        hide perry
        show perry2 at rig
        with short
        p "Cherry's a really c--{w=0.5}cool girl. It would be such a shame to stop hanging out with her!"
        $ fian = "n"
        i "I don't want something like that to happen again... And I don't want Lena to think I'm taking Cherry's side or something."
        p "I'd still like to h--{w=0.5}hang out with her, even if it's troublesome."
        i "Sure. You're free to do what you want."
        hide perry
        show perry at rig
        with short

label v9ianthursdayhome2:
    # ian met cherry
    if v9_cherry_date:
        scene cocktailbar with long
        $ fian = "n"
        $ fperry = "n"
        play sound "sfx/door_home.mp3"
        scene ianhomenight with long
        show ian at lef with short
        i "I'm back."
        show perry at rig with short
        play music "music/emmas_theme.mp3" loop
        p "It's p--{w=0.5}pretty late. Were you hanging out with J--{w=0.5}Jeremy after the workout?"
        i "With Cherry."
        p "Oh."
        "I told Perry about my conversation with her."
        p "That's b--{w=0.5}basically what we already knew, but with more detail."
        p "She doesn't n--{w=0.5}need to apologize, though."
        if v9_cherrytalk == "wrong":
            i "That's what I told her, even if I still think what she did was wrong."
        else:
            i "That's what I told her. It's not her fault, but it is unfortunate indeed..."
        i "I think she just wanted to get it off her chest. She felt guilty we got tangled up in her trouble."
    else:
        if ian_cherry_dating == False:
            stop music fadeout 2.0
            hide cherry with short
            $ fperry = "meh"
            show ian at lef
            show perry at rig
            with move
            p "Damn... What a d--{w=0.5}drama these girls have on their hands."
            play music "music/emmas_theme.mp3" loop
            if perry_cherry:
                i "What was that about? You asked her to go on a date to the Fortress?"
                $ fperry = "serious"
                p "It's not a date. I said we c--{w=0.5}could hang out sometime since she likes the bar."
                $ fian = "smile"
                i "Hey, good going... Just be sure not to cause any more trouble!"
            else:
                i "Seems like the modeling scene is indeed a troublesome one."
    $ fperry = "meh"
    p "If anything, it's this Axel dude who sounds like t--{w=0.5}trouble..."
## PERRY and IAN discuss AXEL ###############################
label v9perryaxel:
    p "Lena, Cherry, Ivy, and even C--{w=0.5}Cindy..."
    p "What's up with this guy? How come he's i--{w=0.5}involved with all the hot girls?"
    # its friday
    if ian_cherry_love:
        $ fian = "n"
        if ian_job_magazine == 2 and v5_ian_showup:
            i "Anyway, I need to get to work."
        else:
            i "Anyway, I'm going to my room to write. I need all the time I can get."
        p "Sure."
        play sound "sfx/door.mp3"
        stop music fadeout 2.0
        scene ianhome with long
        jump v9ianfriday2
    # its thursday
    else:
        $ fian = "n"
        i "Anyway, time to call it a day. Goodnight."
        stop music fadeout 2.0
        play sound "sfx/door.mp3"
        scene ianhomenight_dark with long
        jump v9ianfriday

##########################################################################################################################################################################################################################################################################
## IAN FRIDAY ##### book writing #####################################################################################################################################################################################################################################################################
##########################################################################################################################################################################################################################################################################
label v9ianfriday:
    call calendar(_day="Friday") from _call_calendar_12

    $ ian_look = 2
    $ fian = "n"
    play music "music/normal_day5.mp3" loop
    if ian_job_magazine == 2 and v5_ian_showup:
        scene magazine with long
        "The next morning I reluctantly dragged my ass to the office."
        show ian with short
        "All I wanted was to stay home and finish writing the goddamn book..."
    else:
        scene ianroom with long
        "The next morning I sat in front of the computer, determined to finally finish the goddamn book."
        show v2_ianwrite
    jump v9ianfriday3
label v9ianfriday2:
    $ fian = "n"
    play music "music/normal_day5.mp3" loop
    if ian_job_magazine == 2 and v5_ian_showup:
        scene magazine with long
        "I reluctantly dragged my ass to the office."
        show ian with short
        "All I wanted was to stay home and finish writing the goddamn book..."
    else:
        scene ianroom with long
        "It was time to finish the goddamn book."
        show v2_ianwrite with long
        "I sat down in front of my computer, determined to get it done."
label v9ianfriday3:
    if ian_job_magazine == 2 and v5_ian_showup:
        i "This is boring... I can't get the novel out of my mind."
        i "Fuck it. I'm gonna use this time to write."
        hide ian
        show v2_ianwrite
        with long
        "I opened the files I had in my pendrive and continued where I left off."
    else:
        i "Let's pick up where I left off..."
    if book_scifi:
        i "My {color=#3FB305}Science Fiction{/color} novel is almost complete. All I need is a proper ending."
    if book_fantasy:
        i "My {color=#B30505}Fantasy{/color}  novel is almost complete. All I need is a proper ending."
    if book_historical:
        i "My {color=#D1B402}Historical{/color}  novel is almost complete. All I need is a proper ending."
    if v9hollytalkbook:
        i "What was Holly's advice...? I should keep that in mind."
    label v9_writebookchoice:
        call show_book_screen(7) from _call_show_book_screen_6

        if book_card6 == "victory":
            i "A good book needs a good ending. One that leaves the reader happy and satisfied after reading all those pages."
        elif book_card6 == "sacrifice":
            i "Triumph often comes at a cost. You can't win unless you're willing to sacrifice something in return..."
        elif book_card6 == "defeat":
            i "Not all endings are happy. Tragedy is a very powerful narrative."

        menu:
            "Choose this card":
                $ renpy.block_rollback()
                call book_card_choice_7 from _call_book_card_choice_7

            "Try something else":
                $ renpy.block_rollback()
                jump v9_writebookchoice

    play sound "sfx/keyboard.mp3"
    if v9bookmarker == 1:
        jump v9iansaturdaywrite2
    if v9bookmarker == 2:
        jump v9iansaturdaywrite3
    "I was so focused on writing the last chapters of the book I didn't even stop for lunch."
    if ian_job_magazine == 2 and v5_ian_showup:
        "Time flew by and before I knew it it was time to leave."
        scene street with long
        $ fian = "smile"
        show ian with short
        "I walked out of the office in a much better mood than usual."
        i "That was a good writing session..."
        play sound "sfx/ring.mp3"
        hide ian
        show ian_phone
        with short
    else:
        "Time flew by and I only stopped when my phone rang."
        play sound "sfx/ring.mp3"
        scene ianroom
        show ian_phone
        with short
    # alison call
    i "Yes?"
    if v9_alison_trip == False:
        show phone_alison_sad at lef3 with short
        a "Ian! You never told me about Milo!"
        $ fian = "worried"
        i "Wha--"
        i "Hold on a second, what are you...?"
        if alison_jeremy:
            a "Jeremy just told me about what happened a couple of weeks ago, when Milo came looking for you guys at the gym."
        else:
            a "I talked to Jeremy and he told me about what happened a couple of weeks ago, when Milo came looking for you guys at the gym."
        $ fian = "sad"
        i "Oh, that..."
        if v8_alison_ex == "ian":
            "Alison's ex had gotten violent with Jeremy and me and I ended up knocking him out..."
        else:
            "Alison's ex had gotten violent with us and Jeremy ended up knocking him out..."
        i "I thought you'd learn directly from him..."
        if v7_alison_problems:
            a "No, I followed your advice and blocked him. I haven't heard from him in weeks."
        else:
            a "Nope... I feel kinda guilty about it, but I ended up blocking him."
            i "No wonder..."
        $ fian = "n"
        i "I'm sorry. I have so much going on it completely slipped my mind..."
        if alison_jeremy:
            if ian_alison_dating:
                i "So you spoke with Jeremy?"
                hide phone_alison_sad
                show phone_alison at lef3
                a "Yes. I managed to trick him to come with me on this trip I wanted to make."
                i "Oh."
                i "You never told me."
                a "It was a last-minute thing. I was starting to think I would have to go alone since Cherry was busy too, but Jeremy decided to jump in."
                i "I see..."
                if alison_jeremy_block:
                    $ fian = "disgusted"
                    "So Jeremy had tagged along with Alison in my place?"
                    "I didn't like hearing that news, but I decided to keep it to myself..."
                elif alison_jeremy_doubt:
                    $ fian = "worried"
                    "I wasn't sure how to feel about that..."
                else:
                    $ fian = "sad"
                    "Something like that wasn't unexpected, but I wasn't sure how to feel about it..."
                a "Jeremy also invited this friend of his, and it seems he's loaded."
            else:
                i "Well, Jeremy told you in the end. How's the trip going, by the way?"
                hide phone_alison_sad
                show phone_alison at lef3
                a "So far so good. It's just what I had been needing..."
                a "And I'm not spending a cent! Jeremy invited this friend of his over, and it seems he's loaded."
            a "He insists on paying for everything... Well, I'm not one to complain!"
            i "Oh, so he brought a friend over? I thought it was just the two of you..."
            a "I thought so too, but the more, the merrier. Too bad you didn't want to come..."
            hide phone_alison
            show phone_alison_sad at lef3
            a "Anyway, I'm sorry you got involved in my mess. I had no idea Milo could do such a thing..."
            menu:
                "Talk with Alison":
                    $ renpy.block_rollback()
                    $ v9alisonphone = True
                    $ fian = "sad"
                    i "It's okay. If anything, I feel sorry for him."
                    i "I don't understand how he knew to look for us. Or where."
                    a "Something must've slipped last time I was talking with him before I blocked him. I got a bit too emotional..."
                    hide phone_alison_sad
                    show phone_alison_mad at lef3
                    a "I can't believe that idiot did that! What's wrong with him?"
                    i "He didn't look like he had all his wits with him, if I may say so. He was... erratic, and quite aggressive."
                    hide phone_alison_mad
                    show phone_alison_sad at lef3
                    a "I'm worried about him. Maybe I shouldn't have blocked him... I'm sure he wouldn't have lashed out at you guys if I didn't."
                    $ fian = "n"
                    i "You did the right thing if you ask me. He's clearly proven he's not someone who can be reasoned with right now."
                    if ian_chad > 3:
                        i "He needs to sort his shit out before trying to talk to you again, or to anyone."
                    else:
                        i "He needs to do some soul-searching and put his emotions in order before trying to talk to you again."
                    a "That's--{w=0.5}{nw}"
                    hide phone_alison_sad
                    show phone_alison_blush at lef3
                    a "Hey, wait!"
                    $ fian = "worried"
                    i "Huh?"
                    a "No, it's nothing..."
                    a "Anyway, I'm sorry that my ex caused you trouble."
                    $ fian = "n"
                    i "I said it's okay."
                    a "Sure, well... Talk to you soon, okay?"
                    i "Yeah."
                    a "{i}Stop it...!{/i}"
                    hide phone_alison_blush with short
                    i "She hung up."
                    hide ian_phone
                    show ian
                    with short

                "Hang up":
                    $ renpy.block_rollback()
                    $ fian = "n"
                    i "Hey, Alison, I'm kinda busy right now."
                    i "Have fun with your trip and don't worry about Milo. It's okay."
                    a "Oh. Okay."
                    i "Bye, Alison."
                    hide phone_alison_sad
                    hide ian_phone
                    show ian
                    with short
        else:
            $ fian = "smile"
            i "Anyway, how's the trip going? Having fun?"
            hide phone_alison_sad
            show phone_alison at lef3
            with short
            a "Yeah, but it would’ve been cooler if you joined..."
            i "Maybe next time."
            a "Sure, next time."
            a "Let's hang out soon. Bye!"
            i "Bye, Alison."
            hide phone_alison
            hide ian_phone
            show ian
            with short

        if v5_ian_showup:
            i "Huh? Seems I got another call while I was on the phone with Alison..."
            i "Whose number is this? I don't have it in my contacts."
            play sound "sfx/ring.mp3"
            i "Oh, they're calling again."
            hide ian
            show ian_phone
            with short
            i "Yes?"
        elif ian_lena_dating:
            "Whatever... I should get ready to meet Lena. She's coming back today..."
            play sound "sfx/ring.mp3"
            "As if I had just summoned her with my thoughts, I got a call from her at that precise moment."
            hide ian
            show ian_phone
            with short
            $ fian = "smile"
        else:
            play sound "sfx/ring.mp3"
            i "Another call?"
            hide ian
            show ian_phone
            with short
    # hierofant call
    if v5_ian_showup:
        woman "Hello, is this Ian Watts?"
        if ian_charisma > 6 or ian_chad > 4:
            i "Yeah, the one and only."
        else:
            $ fian = "n"
            i "That's me, yeah."
        woman "I'm calling from human resources at Hierofant publishing."
        $ fian = "worried"
        i "...!" with vpunch
        "My heart skipped a beat when hearing that name. This was the call I had been waiting for."
        woman "We received your resume and application for the intern beta-reader position..."
        woman "And we'd like to offer you a job at the company."
        woman "Would you be interested?"
        $ fian = "happy"
        i "Y-{w=0.3}yeah, of course! That's why I contacted you guys..."
        woman "Excellent. Would you be able to fill in the position at the start of July? In two weeks, let's say."
        i "Yeah, no problem."
        woman "Alright! We'll contact you at a later date and send you all the additional information and the contract."
        woman "Have a nice end of the week!"
        i "Bye!"
        hide ian_phone
        show ian
        with short
        "I jumped as soon as the call ended."
        i "Hell yeah!" with vpunch
        i "I've gotten that position I was aiming for!"
        if ian_will < 2:
            call will_up() from _call_will_up_51
        $ fian = "smile"
        "It had taken a while, and I had been afraid they had passed on my application, but... I had gotten it!"
        if ian_job_magazine < 2:
            "I felt a huge load lift off my shoulders. All the risks I had taken were finally bearing fruit."
            if ian_job_magazine == 0:
                "No more food deliveries for me... I would finally get a job I was excited about."
            else:
                "I would finally escape Minerva's clutches. No more putting up with her bullshit..."
        else:
            if ian_defy_minerva or ian_minerva_sex:
                "I had managed to beat Minerva at her own game, but I still wasn't happy working at that office."
                "I was finally getting a chance to do something I felt excited about."
            else:
                "I had been hating my job for far too long. I was, at last, getting a chance to do something I felt excited about."
        i "This news really boosted my morale. I was really needing this..."
        i "And I'm practically done with my book, too! Today has been an awesome day..."
        if ian_lena_dating:
            "And I still had to meet Lena. She was coming back today..."
            play sound "sfx/ring.mp3"
            "As if I had just summoned her with my thoughts, I got a call from her at that precise moment."
            hide ian
            show ian_phone
            with short
            $ fian = "smile"
            i "Hey."
            show phone_lena at lef3 with short
            l "Hi!"
        else:
            play sound "sfx/ring.mp3"
            i "Another call?"
            hide ian
            show ian_phone
            with short
            show phone_lena at lef3 with short
            l "Hi!"
            "I almost forgot! Lena was coming back today and I asked her to call me to hang out when that happened."
            $ fian = "smile"
    else:
        show phone_lena at lef3 with short
        l "Hi!"
        if ian_lena_dating:
            $ fian = "smile"
            "This was the call I had been waiting for."
        else:
            "I almost forgot! Lena was coming back today and I asked her to call me to hang out when that happened."
            $ fian = "smile"
    # lena call
    i "Hey, Lena! Are you in town already?"
    l "Almost! I'm on the train, about to arrive."
    if ian_lena_dating:
        i "Do you want me to greet you at the station?"
        hide phone_lena
        show phone_lena_smile at lef3
        l "I'd like that."
        i "I'm heading over there."
        l "Alright! See you in a few minutes!"
    else:
        l "Say, would you mind coming to greet me at the station?"
        l "My parents wouldn't let me leave without taking some old stuff with me and I'm carrying a bit too much luggage..."
        i "No problem, I can help you out."
        hide phone_lena
        show phone_lena_smile at lef3
        l "Awesome! Thank you so much. I'll see you in a bit!"
##LENA DATE #################################################################################################################################################################################################################################
    $ flena = "n"
    if lena_lust > 6 and ian_lena_dating:
        $ lena_look = "sexy"
    else:
        $ lena_look = 4
    if ian_lena_dating:
        stop music fadeout 2.0
    scene street with long
    "I headed to the train station and waited for Lena."
    show ian at lef with short
    "I didn't have to wait long. I spotted her carrying two suitcases and a backpack."
    if ian_lena_dating:
        play music "music/shine_again1.mp3" loop
    show lena at rig with short
    i "Hey! What are you hauling there?"
    $ flena = "worried"
    if ian_lena_dating:
        l "Ugh, my  parents wouldn't let me leave without taking some old and useless stuff with me."
        l "Old clothes, new bed sheets, and even a damn duvet!"
    else:
        l "My parents insisted on me taking with me a lot of useless stuff: old clothes, new bed sheets, and even a damn duvet!"
    $ fian = "happy"
    i "A duvet? It's almost July!"
    l "I know! They even wanted me to pack a dinnerware set, but I refused. A small victory."
    menu:
        "{image=icon_love.webp}Kiss Lena" if ian_lena_dating:
            $ renpy.block_rollback()
            $ v9lenagreetkiss = True
            $ fian = "smile"
            i "I'm so glad to see you."
            if lena_look == 4:
                scene v9_lena1a
            else:
                scene v9_lena1b
            if lena_tattoo2:
                show v9_lena1_t2
            with long
            "I leaned in for a kiss."
            "It had been almost two weeks since my lips had tasted Lena's for the last time. I had been missing this..."
            if lena_ian_love or lena_lust > 6:
                "And by the looks of it, she was feeling the same."
                if lena_look == 4:
                    scene v9_lena2a
                else:
                    scene v9_lena2b
                if lena_tattoo2:
                    show v9_lena1_t2
                with long
                "Lena didn't seem in a rush for that kiss to end, and neither was I."
                "Our lips spread apart, giving way to our tongues. They came together in a slow, passionate dance."
                "I held her hips and pulled her body toward me, and she slid her hands around my neck, caressing my nape as the kisses rolled one after the other."
                if ian_lust < 6:
                    call xp_up('lust') from _call_xp_up_91
                "It felt so incredibly good to kiss Lena again, to taste her, to feel her warmth and her perfume..."
                "When we finally decided to end the kiss, there was a bulge under my pants. I could not resist the effect she had on me!"
                $ fian = "smile"
                $ flena = "shy"
                scene street
                show ian at lef
                show lena2 at rig
                with short
                i "Welcome back."
                l "Now that's how you greet someone..."
            else:
                "It was brief, but enough to put a smile on my face."
                $ fian = "smile"
                $ flena = "smile"
                scene street
                show ian at lef
                show lena at rig
                with short
                i "Welcome back."
                l "Thanks."
            if ian_lena < 12:
                call friend_xp('lena', 1) from _call_friend_xp_118

        "That's so typical":
            $ renpy.block_rollback()
            $ fian = "smile"
            i "That's so typical... Didn't they make you pack some tuppers full of frozen food, too?"
            if ian_wits < 6:
                call xp_up('wits') from _call_xp_up_92
            $ flena = "happy"
            l "They would've, if only my mother had been fit to cook!"
            l "I have no idea what to do with half these things. Or where to store them, my room is tiny!"

        "Parents can be so annoying":
            $ renpy.block_rollback()
            $ fian = "n"
            i "Damn, you have to put up with a lot... Parents can be so annoying and overbearing!"
            $ flena = "sad"
            l "I know it's their way of showing they worry about me, but sometimes I'd appreciate it if they were a bit more restrained."
            l "I have no idea what to do with half these things. Or where to store them, my room is tiny!"

        "They worry about you":
            $ renpy.block_rollback()
            $ fian = "smile"
            i "I'm sure that's their way of showing they care and are worried about you."
            if ian_charisma < 6:
                call xp_up('charisma') from _call_xp_up_93
            $ flena = "n"
            l "I know, I know. But I wish they would listen to me instead of being so overbearing!"
            l "I have no idea what to do with half these things. Or where to store them, my room is tiny!"

    $ fian = "smile"
    i "Here, let me help you with your luggage."
    if v9lenagreetkiss == False and lena_ian_love and ian_lena_dating:
        $ flena = "shy"
        l "Wait... Where's my welcome kiss?"
        $ fian = "shy"
        i "Oh...! Of course..."
        if lena_look == 4:
            scene v9_lena1a
        else:
            scene v9_lena1b
        if lena_tattoo2:
            show v9_lena1_t2
        with long
        "I leaned in for a kiss."
        "It had been almost two weeks since my lips had tasted Lena's for the last time. I had been missing this..."
        scene street
        show ian at lef
        show lena2 at rig
        with short
        l "Okay... Now we can go."
    if cafe_help:
        l "Do you mind if we stop by the café while we're on our way? I'm starving and I want to check up on Molly and Ed."
        i "Sure. Let's go."
    else:
        l "Do you mind if we stop for a bite while we're on our way? I'm starving!"
        i "Sure, we can drop by the café if you want."
        l "Sure, let's go."
    scene cafe with long
    $ fian = "smile"
    $ flena = "smile"
    show ian at lef
    show lena at rig
    with short
    l "Hello!"
## cafe conversation
label v9lenadate1:
    $ fed = "smile"
    $ flena = "n"
    $ fian = "smile"
    $ v9_ianwearing = "n"
    show ian at lef3
    show lena at rig3
    with move
    show ed with short
    ed "Lena! So nice to see you!"
    if lena_look == "sexy":
        $ fed = "sad"
        ed "Wow, you're looking... I mean, you look so different in those clothes!"
        l "Uh... Thanks?"
        ed "Sorry. How was your trip back home?"
    else:
        ed "How was your trip back home?"
    l "It wasn't the best, but I try not to complain too much. I'm glad to be back, though."
    l "Where's Molly?"
    $ fed = "n"
    ed "She was feeling a bit under the weather and I told her to take the day off."
    $ flena = "sad"
    if cafe_perry:
        $ fperry = "smile"
        l "Oh..."
        show ian at left
        show ed at lef
        show lena at right
        with move
        show perry at rig with short
        p "H--{w=0.5}hey, Lena! Welcome back!"
        $ flena = "surprise"
        $ fian = "happy"
        l "Perry! What are you doing here?"
        i "I convinced him to lend a hand at the café while you were away."
        $ flena = "smile"
        l "Really? That's so nice of you, guys!"
        ed "Yeah, Perry has been really helpful these past couple of days."
        i "He's not complaining all the time?"
        $ fperry = "n"
        p "I'm not. It's been p--{w=0.5}pretty entertaining, actually."
        $ fian = "smile"
        if v9_alison_trip:
            i "I thought you'd be at the Visual Fighter tournament today with Wade."
            p "Yeah, I came during lunch b--{w=0.5}break. Ed makes the best eggs I've ever tried..."
            $ fperry = "smile"
            p "And I can eat for free as long as I help."
            ed "It's the least I can do since we're not paying you!"
        i "I'm glad you're liking it here."
        ed "Okay, enough chit-chat!"
    else:
        l "Oh. I hope she gets well soon."
        $ flena = "n"
        l "How have things been during my absence? Any news?"
        ed "Yeah, but we'll talk about that on Monday. You're here to enjoy yourselves today, am I right?"
        $ flena = "smile"
        l "Yeah."
    ed "Take a seat and I'll be with you in a minute."
    hide ed
    hide perry
    with short
    show ian at lef
    show lena at rig
    with move
    if cafe_perry and cafe_help:
        l "It was so nice of you to think about sending Perry. Thanks."
        if cafe_help:
            if ian_lena < 5:
                call friend_xp('lena', 2) from _call_friend_xp_119
            elif ian_lena < 12:
                call friend_xp('lena', 1) from _call_friend_xp_120
        i "I wasn't sure it was such a good idea, but it seems he's being somewhat helpful."
        l "They need all the help they can get right now..."
    $ flena = "n"
    l "So, how have you been doing?"
    $ fian = "n"
    i "I should be the one asking that question..."
    if ian_lena_dating:
        l "I don't have much to tell aside from what we talked about over the phone the other day."
    else:
        l "Well, I don't have much to tell aside from what you already know."
    l  "I feel like we've only been talking about me lately... What about you?"
    l "Any news?"
    if v5_ian_showup:
        $ fian = "smile"
        if v9_alison_trip:
            i "Yes, in fact... The other day I finally got the call!"
        else:
            i "Yes, in fact... I just got the call!"
        l "What call?"
        $ fian = "happy"
        i "From Hierofant. They're hiring me as an intern."
        $ flena = "happy"
        l "Really? Congratulations!"
        l "You really wanted to get that job. Now you just have to finish your book..."
        if v9_alison_trip:
            i "I'm almost done. Just need to write the end..."
        else:
            i "I also did that. Well, almost."
    else:
        $ fian = "n"
        i "Not much, really..."
        $ fian = "smile"
        i "Well, I've practically finished my book."
        if v9_alison_trip:
            i "I still need to write the ending, and thoroughly revise it before submitting it, but aside from that..."
        else:
            i "All that's left is to revise it thoroughly before submitting it to the contest."
    l "That's great! You've been working so hard at it."
    $ fian = "smile"
    i "I don't know if it will bear any fruit at all, but yeah... At least I can say I did my best."
    l "It's nice to hear you've been making progress."
    $ flena = "sad"
    l "Meanwhile, I've been stuck at my parents' house..."
    $ fian = "n"
    l "I feel kinda guilty, though. I know they need me, but I can't help but feel bothered by the situation."
    menu:
        "I'd feel like that too":
            $ renpy.block_rollback()
            i "I'd feel the same way, too... As you say, parents can be really overbearing."
            i "Especially when they keep treating you as a kid."
            $ flena = "serious"
            l "I know, right?"
            if ian_charisma < 6:
                call xp_up('charisma') from _call_xp_up_94
            l "I don't want to feel bad, but my mom makes it kinda hard!"

        "The situation sucks":
            $ renpy.block_rollback()
            i "The situation sucks... But stuff happens and all we can do is try to cope."
            l "Sounds good, doesn't work. It's hard to be stoic when life sucks."

        "You need to help them":
            $ renpy.block_rollback()
            $ fian = "sad"
            i "But you have a duty to help them. They're your parents after all."
            l "I know, and that's what I'm doing."
            l "It doesn't make it any easier."
            if ian_lena > 3:
                call friend_xp ('lena',-1) from _call_friend_xp_121

    $ flena = "sad"
    l "At least my dad is feeling better. I just wish the timing had been a bit different, I could really use a break!"
    menu:
        "{image=icon_friend.webp}Are you close with your dad?" if ian_lena > 3:
            $ renpy.block_rollback()
            stop music fadeout 2.0
            $ v9_ian_family = 1
            "Lena paused for a second, and for a moment I thought she wouldn't answer."
            play music "music/broken_dreams2.mp3" loop
            l "I am, I guess... At least as close as daughters tend to be with their dads."
            $ flena = "n"
            hide lena
            show lena2 at rig
            with short
            l "Well, technically he's not my {i}real{/i} dad, but it never made any difference to me."
            $ fian = "n"
            i "I didn't know you had a stepfather."
            l "I've never used that term. As I said, to me he's always been just \"Dad\"."
            l "My real father left when I was two. I have no real memories of him aside from what I've seen in pictures and what my mother told me."
            l "But Dad has always been there since I can remember. He got together with my mom shortly after, helped her take care of me and my brother."
            i "He sounds like a really good guy."
            l "He is... And he did the best he could given the circumstances. We've had ups and downs, especially when I was a teenager..."
            l " But I know he's always seen me as his own daughter and really loves me."
            $ flena = "serious"
            l "Not like that other guy... He left us and never looked back."
            $ fian = "sad"
            l "I haven't seen or heard from him since he left. Not a call, not even a birthday postcard."
            l "For some reason, when I was a kid there was a period when I hoped I would get one."
            l "That day never came, of course. I stopped deluding myself after my twelfth birthday or so."
            $ flena = "worried"
            l "Oh, sorry...! I startled rambling and..."
            l "It has been a while since I talked about this with someone!"
            $ fian = "smile"
            i "No, it's okay. It's me who asked, after all..."
            i "I guess we both have daddy issues, ha ha."
            l "What makes you say that?"
            menu:
                "Tell Lena about your family":
                    $ renpy.block_rollback()
                    $ v9_ian_family = 2
                    $ fian = "n"
                    i "Oh, you know, the typical story. Son is a disappointment to his father. A bit like Perry."
                    $ flena = "sad"
                    l "Why would he be disappointed in you? You're nothing like Perry."
                    if ian_charisma > 6 or ian_chad > 3:
                        l "Like, you're the complete opposite."
                        if ian_charisma > 6:
                            $ fian = "happy"
                            i "I'm glad you think that way."
                        else:
                            $ fian = "smile"
                            i "Yeah, I know."
                    if ian_lena < 12:
                        call friend_xp('lena', 1) from _call_friend_xp_122
                    $ fian = "n"
                    i "Still, my dad has his reasons. He works in finances, and he's a very career-driven man. Very methodical."
                    i "He's not thrilled about the career path I've chosen. I mean, it's not like he's disgusted by me wanting to be a writer..."
                    i "He's just disappointed that I'm not succeeding. I think I could've become a plumber as long as I made it to the top of the hierarchy."
                    $ flena = "worried"
                    l "Plumbers have a hierarchy?"
                    i "I guess they do."
                    if ian_wits > 10:
                        i "So, he's always going on about \"climbing the ladder\", \"becoming competent\", and \"are you winning, son?\"."
                    else:
                        i "So, he's always going on about \"climbing the ladder\", \"becoming competent\", and so on."
                    $ flena = "sad"
                    l "And what about your mother?"
                    $ fian = "n"
                    i "She's never been a big part of my life."
                    $ flena = "n"
                    l "What do you mean?"
                    menu:
                        "{image=icon_friend.webp}Tell Lena about your mother" if ian_lena > 5:
                            $ renpy.block_rollback()
                            $ v9_ian_family = 3
                            i "Let's say she's not the most emotional woman out there. As a kid, I mostly remember her as an authority figure, not so much as a... {i}mom{/i}."
                            i "Not that she doesn't love me, of course. I know she does, but I guess she never knew how to show it. Or she was too busy with her own job, like my father."
                            i "And they are like that with each other, too. I think their relationship only works because they don't share anything but the house. And a son."
                            l "So they don't do anything together?"
                            i "I guess they used to, but not anymore. I'm pretty sure my mother's been seeing another man."
                            $ flena = "surprise"
                            l "What? How do you know?"
                            i "I've always been quite perceptive, so I picked up on things. We were living in the same house, after all."
                            $ flena = "worried"
                            l "Wait, how long have you suspected this?"
                            i "For a few years now."
                            l "And does your dad know...?"
                            i "I think he probably does. In fact, I'd be surprised if he wasn't seeing someone, too. I mean, they're human, and they're not that old."
                            $ flena = "sad"
                            l "So do they have an arrangement between them or...?"
                            i "I don't know, they probably do, or they would've been divorced by now. They just never shared what that arrangement was with me."
                            i "It's their business, I suppose."
                            $ flena = "worried"
                            l "I'd say it's more like a {i}family business{/i} when you're married and have a son..."
                            stop music fadeout 2.0
                            $ fian = "smile"
                            i "Anyway, I'm an adult now, so they're free to live their own life however they please, and so am I."
                            if ian_wits < 9:
                                call xp_up('wits') from _call_xp_up_95
                            i "No need to keep boring you with my family's soap opera."

                        "No need to get into that":
                            $ renpy.block_rollback()
                            i "No need to get into that, really... I don't want to keep boring you with my family's soap opera."


                "Change the subject":
                    $ renpy.block_rollback()
                    $ fian = "disgusted"
                    i "Uh, never mind... That came off kinda weird, didn't it?"
                    $ flena = "happy"
                    l "Now that you mention it... Ha ha ha."
                    $ fian = "n"
                    i "Anyway, families are complicated."
                    l "They are, indeed..."

            stop music fadeout 2.0
            $ flena = "smile"
            l "Every family is like a soap opera... Each one fucked up in their own way."

        "Hang in there":
            $ renpy.block_rollback()
            $ fian = "n"
            i "Well, hang in there. At least not everything is going badly..."
            $ fian = "smile"
            i "And you have friends to count on."
            $ flena = "smile"
            l "Hopefully, I won't burden you with my family drama."
## PARK DATE ########################################################################################################################################################################################
    stop music fadeout 2.0
    scene cafe with long
    if v9_alison_trip:
        "We continued to chat while we had lunch. Once finished, we said goodbye to Ed and got on our way."
    else:
        "We continued to chat while we had a snack, and then we got back on our way to Lena's place."
    $ fian = "smile"
    $ flena = "n"
    scene street with long
    show ian at lef
    show lena at rig
    with short
## IAN LENA DATING - define relationship #############################################
    if ian_lena_dating:
        if v9_alison_trip:
            l "It's a nice afternoon. Do you want to take a walk in the park?"
            i "Sure. I'm enjoying the company."
        else:
            l "This way, if we cross through the park we'll get there quicker."
            i "Yes ma'am."
        play music "music/calm.mp3" loop
        scene park with long
        if v9_alison_trip:
            "We ended up buying some popcorn at the kiosk and taking a seat on a bench in front of the river."
            "For a while we just watched the water flow, the ducks swim and fly and an occasional boat going upstream."
        else:
            "We dragged Lena's luggage across the park."
            "The afternoon was nearing its end, and since we were already there, we decided to sit on a bench in front of the river to watch the sunset."
            scene parknight with long
            "We sat in silence as the last lights of the day shimmered on the surface of the water, being replaced by the reflections of lamps and city lights."
        show ian at lef
        show lena at rig
        with short
        l "It's nice to be back. I like it here."
        i "Baluart's a very nice city. Not the busiest, and marginally provincial, but yeah..."
        i "A great place to live."
        l "It's strange, but after spending this week at my parents' home..."
        l "Well, I feel more at home here. Even if I'm still far from getting my own place, or a stable job..."
        l "Here I feel free to be myself. And I want to build something up with that, whatever it is."
        if ian_lena_love:
            i "And I would like to be part of that."
            $ flena = "blush"
            l "..."
            $ fian = "n"
            i "..."
            $ fian = "sad"
            i "I said something out of line, didn't I?"
            l "No, in fact... I've been meaning to talk with you about this."
            if lena_ian_love:
                l "About... us."
                l "I mean, we've been seeing each other for a while now, and..."
            else:
                l "I mean... What's the deal between you and me, exactly?"
                l "I'm sorry, I know I'm being rather blunt. But we've been seeing each other for a while now, and..."
            $ fian = "n"
            i "Yeah, you're right. I've been thinking about this, too."
        else:
            i "You have lots of opportunities to do that here. Lots of people."
            if lena_ian_love:
                $ flena = "blush"
                l "Yeah... I've been wondering about that..."
                l "Who to share all of this with..."
                $ fian = "n"
                i "..."
                i "I guess... you're asking what is exactly the deal between us, right?"
                l "Yeah..."
            else:
                l "..."
                l "Hey, can I ask you something?"
                i "Sure."
                l "Maybe this is coming a bit out of the blue, but..."
                l "What's the deal between us, exactly?"
                $ fian = "n"
                i "Oh."
                i "Damn, that really caught me off guard."
            l "I'm sorry, I know I'm being rather blunt. But we've been seeing each other for a while now, and..."
            i "No, you're right. I've been meaning to talk to you about this too."

        $ flena = "n"
        l "So it seems we're about to have \"the talk\", right?"
        i "Yeah... It's about time, I guess."
        l "Okay, let me start since I brought it up."
        l "Seems like we're both in a, let's say... complex moment, emotionally speaking."
        l "So I think it'd be better if we put our cards on the table and discuss things like, you know... adults."
        $ flena = "blush"
        l "I'm not too good at this myself, but I know my last relationship ended horribly because of the lack of communication and honesty."
        l "I'm trying to change that, so..."
        $ flena = "n"
        l "I'd appreciate it if you honestly told me how you feel about this."
        menu:
            "{image=icon_ring.webp}I want a relationship with you" if (ian_alison_love == False and ian_lena_love) or (ian_alison_love == False and ian_lena > 8):
                $ renpy.block_rollback()
                stop music fadeout 2.0
                "This was it. This was the moment."
                "I had to be frank with Lena. Expose my feelings."
                "That moment every guy fears... Especially when they've been hurt before."
                play music "music/ourredstring.mp3" loop
                $ fian = "blush"
                if ian_lena_love:
                    i "I've been thinking this over for some time, and while it's true I still have some conflicted feelings about it..."
                    i "The truth is I'd like to be in a relationship with you."
                    i "That's how I feel. How I've been feeling for a while now..."
                else:
                    $ ian_lena_love = 2
                    i "The truth is, up until now, I've been trying to see this as something casual."
                    i "I told myself we were just experimenting together, and that might be true..."
                    i "But if I listen to what I really feel..."
                    i "I think I'd like to be in a relationship with you."
                # 1 - AGREE
                if lena_ian_love and v8_jeremy_sex == False:
                    $ ian_lena_couple = True
                    $ flena = "blush"
                    l "Oh..."
                    $ flena = "shy"
                    l "Actually, I've been feeling the same..."
                    $ fian = "shy"
                    i "Really?"
                    l "Yeah, I mean..."
                    $ flena = "blush"
                    l "I really like you. You're the first person I've liked in this way for a long time."
                    l "I'm just not sure if I'm ready to get in another relationship just yet."
                    $ fian = "worried"
                    i "I understand... I share the same fear."
                    l "Still..."
                    $ flena = "shy"
                    l "I'd like to give it a try. I won't know otherwise."
                    if ian_will < 2:
                        call will_up() from _call_will_up_52
                    # A - lena open
                    if lena_robert_dating or lena_mike_dating or v8_jeremy_sex or (lena_louise_sex and lena_reject_louise == False):
                        $ fian = "shy"
                        i "So is that a yes?"
                        $ flena = "blush"
                        l "It is... Kind of."
                        $ fian = "worried"
                        i "Kind of?"
                        l "You've been honest with me, so I will do the same."
                        l "I'm going through some kind of... \"experimenting\" phase right now."
                        # aa - ian open
                        if ian_alison_dating or ian_cherry_dating or v7_cindy_kiss or ian_minerva_sex or ian_emma_sex:
                            i "Actually, me too. I've also been... experimenting a bit."
                            $ flena = "n"
                            if ian_alison_dating or ian_cherry_dating:
                                l "Yeah, I know. Still, I wanted to put my cards on the table too."
                            else:
                                l "Oh, I see."
                                l "Knowing that makes me feel a bit better. It's good we're putting our cards on the table."
                            if lena_robert_sex:
                                $ fian = "sad"
                                i "So, that guy, Robert..."
                                if lena_robert_over2:
                                    l "Actually, I'm done with him. After what happened at the drawing event, well..."
                                    l "I never liked him that much, to begin with."
                                    $ fian = "smile"
                                    "That news made me feel much better."
                                    $ fian = "disgusted"
                                    "I still had no idea how that douchebag got to hook up with Lena in the first place... But at least she kicked him to the curb."
                                elif lena_robert_over:
                                    l "Oh, no, that was just a stupid fling..."
                                    l "I already told you I was done with him."
                                    "I still had no idea how that douchebag got to hook up with Lena in the first place... But at least she kicked him to the curb."
                                else:
                                    l "Well, yeah... We've been seeing each other from time to time..."
                                    $ fian = "disgusted"
                                    "She was still hanging out with that douchebag? I couldn't help but feel a knot in my stomach."
                                    call friend_xp ('lena', -1) from _call_friend_xp_123
                                    i "Do you plan to keep that going?"
                                    l "I was, but this changes things. And I never liked him that much anyway..."
                            $ fian = "n"
                            l "So I guess you're not interested in dating around anymore, right? Since you want us to be together..."
                            i "That's right. You're the only girl I'm really interested in."
                            if v7_cindy_kiss:
                                $ fian = "insecure"
                                "As soon as I said that I wondered if I was speaking the truth. I hadn't been able to get Cindy off my mind yet..."
                            if v2_cherry_home:
                                $ flena = "worried"
                                l "So I take it whatever it was you had with Cherry is over, right?"
                                if ian_cherry_love:
                                    $ fian = "insecure"
                                    i "Uh, that..."
                                    "Last night with Cherry wasn't just some ordinary hookup... It didn't feel like it."
                                    $ fian = "worried"
                                    "Besides, I told her Lena and I were only friends... But that was about to change now."
                                    "Unless I messed it up..."
                                    $ fian = "sad"
                                    i "Yes. It's done, it wasn't even a thing to begin with. Just a casual fling..."
                                    $ flena = "serious"
                                    l "But then again, it {i}was{/i} a thing."
                                    $ fian = "n"
                                    i "You said it yourself. {i}Was{/i}. You're the girl I want to be with..."
                                elif ian_cherry_sex:
                                    $ fian = "sad"
                                    i "Yes, it is... It wasn't even a thing to begin with. Just a casual fling..."
                                    $ flena = "serious"
                                    l "But then again, it {i}was{/i} a thing."
                                    $ fian = "n"
                                    i "You said it yourself. {i}Was{/i}. You're the girl I want to be with..."
                                else:
                                    $ fian = "worried"
                                    i "Wha--? Yeah, nothing really happened between us, I already told you..."
                                    $ flena = "serious"
                                    l "It wasn't nothing."
                                    i "Yeah, I mean, we just... We never even went all the way."
                                    $ flena = "sad"
                                    l "Alright... That's a weight off my back."
                                $ flena = "sad"
                                l "I hope you understand why I'm rather touchy about this subject..."
                                $ fian = "n"
                                i "Of course."
                            $ fian = "worried"
                            i "What about you? Are you still interested in... {i}experimenting{/i}?"
                        # ab - ian monogamous
                        else:
                            if lena_robert_sex:
                                $ fian = "sad"
                                i "I figured as much. That guy, Robert..."
                                $ flena = "n"
                                if lena_robert_over2:
                                    l "Actually, I'm done with him. After what happened at the life drawing, well..."
                                    l "I never liked him that much, to begin with."
                                    $ fian = "n"
                                    "That news made me feel better for some reason."
                                    $ fian = "disgusted"
                                    "I still had no idea how that douchebag got to hook up with Lena in the first place... But at least she kicked him to the curb."
                                elif lena_robert_over:
                                    l "Oh, no, that was just a stupid fling..."
                                    l "I already told you I was done with him."
                                    $ fian = "disgusted"
                                    "I still had no idea how that douchebag got to hook up with Lena in the first place... But at least she kicked him to the curb."
                                else:
                                    l "Well, yeah... We've been seeing each other from time to time..."
                                    $ fian = "disgusted"
                                    "She was still hanging out with that douchebag? I couldn't help but feel a knot in my stomach."
                                    call friend_xp ('lena', -1) from _call_friend_xp_124
                                    i "Do you plan to keep that going?"
                                    l "I was, but this changes things. And I never liked him that much anyway..."
                                $ flena = "sad"
                                l "I don't know if you've also been seeing other people during this time..."
                            else:
                                $ fian = "n"
                                i "Oh."
                                $ fian = "sad"
                                i "Ohhh..."
                                i "I see."
                                l "I don't know if that's been the case for you, too..."
                            i "No, not really. You're the only girl I'm really interested in."
                            $ fian = "worried"
                            i "So... Are you still interested in... {i}experimenting{/i}?"
                        $ flena = "blush"
                        l "I have... mixed feelings about it."
                        l "I have to admit it felt nice being able to do whatever without worrying about hurting anyone..."
                        l "But that won't be the case anymore."
                        $ fian = "n"
                        menu:
                            "{image=icon_lust.webp}We can experiment together" if ian_alison_dating or ian_cherry_dating or v7_cindy_kiss or ian_minerva_sex or ian_emma_sex or ian_lust > 4:
                                $ renpy.block_rollback()
                                $ ian_lena_open = True
                                if ian_alison_dating or ian_cherry_dating or v7_cindy_kiss or ian_minerva_sex or ian_emma_sex:
                                    i "We're both in a similar situation, so I get where you're coming from. I feel a bit like that too..."
                                    i "So maybe we could keep experimenting... together."
                                else:
                                    i "As I said, you're the only girl I'm really interested in..."
                                    i "But I wouldn't mind us experimenting together."
                                $ flena = "flirtshy"
                                l "Experimenting together? And... how would that be like?"
                                $ fian = "blush"
                                i "I'm not sure. I've never tried something like that in a relationship, but maybe we can talk it over."
                                i "If the two of us are okay with it, I guess we could try a more... {i}open{/i} approach to things."
                                l "That could be... interesting."

                            "{image=icon_love.webp}No, it won't":
                                $ renpy.block_rollback()
                                i "No, I guess it won't."
                                $ flena = "shy"
                                l "Yeah. If we're really gonna give this... us, a try, we should commit to it."
                                $ fian = "smile"
                                i "I feel the same."

                    # B - lena monogamous
                    else:
                        $ flena = "shy"
                        $ fian = "shy"
                        l "And I can't think of anyone who I'd like to try that better than you. So..."
                        i "So is that a yes?"
                        l "Yes. I want us to give it a try."
                        $ fian = "happy"
                        i "That's...!"
                        i "... Awesome."
                        if ian_alison_dating or ian_cherry_dating:
                            $ flena = "blush"
                            l "I need to ask, though... I know you've been seeing other people..."
                            $ fian = "n"
                            i "Well, yeah..."
                            i "But it wasn't anything serious. I'm not interested in pursuing anyone else..."
                            if v7_cindy_kiss:
                                $ fian = "disgusted"
                                "As soon as I said that I wondered if I was speaking the truth. I hadn't been able to get Cindy off my mind yet..."
                            if v2_cherry_home:
                                $ flena = "worried"
                                l "So I take it whatever it was you had with Cherry is over, right?"
                                if ian_cherry_love:
                                    $ fian = "insecure"
                                    i "Uh, that..."
                                    "Last night with Cherry wasn't just some ordinary hookup... It didn't feel like it."
                                    $ fian = "worried"
                                    "Besides, I told her Lena and I were only friends... But that was about to change now."
                                    "Unless I messed it up..."
                                    $ fian = "sad"
                                    i "Yes. It's done, it wasn't even a thing to begin with. Just a casual fling..."
                                    $ flena = "serious"
                                    l "But then again, it {i}was{/i} a thing."
                                    $ fian = "n"
                                    i "You said it yourself. {i}Was{/i}. You're the girl I want to be with..."
                                elif ian_cherry_sex:
                                    $ fian = "sad"
                                    i "Yes, it is... It wasn't even a thing to begin with. Just a casual fling..."
                                    $ flena = "serious"
                                    l "But then again, it {i}was{/i} a thing."
                                    $ fian = "n"
                                    i "You said it yourself. {i}Was{/i}. You're the girl I want to be with..."
                                else:
                                    $ fian = "worried"
                                    i "Wha--? Yeah, nothing really happened between us, I already told you..."
                                    $ flena = "serious"
                                    l "It wasn't nothing."
                                    i "Yeah, I mean, we just... We never even went all the way."
                                    $ flena = "sad"
                                    l "Alright... That's a weight off my back."
                                $ flena = "sad"
                                l "I hope you understand why I'm rather touchy about this subject..."
                                $ fian = "n"
                                i "Of course."
                            $ flena = "n"
                            l "Alright, thanks for clarifying. I needed to know."
                        if lena_robert_sex:
                            $ fian = "n"
                            i "I need to ask, though... About that dude, Robert..."
                            $ flena = "blush"
                            if lena_robert_over2:
                                l "Actually, I'm done with him. After what happened at the life drawing, well..."
                                l "I never liked him that much, to begin with."
                                $ fian = "smile"
                                "Hearing that news lifted a heavy load off my shoulders."
                            elif lena_robert_over:
                                l "Oh, no, that was just a stupid fling..."
                                l "I already told you I was done with him."
                            $ fian = "disgusted"
                            "I still had no idea how that douchebag got to hook up with Lena in the first place... But it didn't matter now."
                            $ fian = "smile"
                            "She wanted to be with me."
                        $ flena = "shy"

                # 2 - DECLINE
                else:
                    if lena_jeremy_sex:
                        $ flena = "blush"
                    else:
                        $ flena = "worried"
                    l "Oh."
                    l "About that..."
                    stop music fadeout 2.0
                    if lena_jeremy_sex:
                        l "I... Right now I don't think it's a good moment..."
                        l "There are things that... I mean, I don't feel ready for that kind of commitment."
                        if lena_ian_love:
                            l "The truth is I really like you, and you're great, it's just..."
                        else:
                            l "I actually like you quite a lot, but it's just..."
                    else:
                        l "Right now I don't feel ready for that kind of commitment... Not by a long shot."
                        l "I mean, you're great and I actually like you quite a lot, but it's just..."
                    $ fian = "n"
                    i "..."
                    i "I understand."
                    if lena_ian_love:
                        i "I knew I was risking getting this response by telling you, but you asked me to be honest, so..."
                    else:
                        i "I had the impression you'd give me this answer, but you asked me to be honest, so..."
                    $ flena = "sad"
                    l "I'm sorry... But I just can't get into a serious relationship at this moment."
                    l "It wouldn't be fair to you."
                    if lena_robert_dating or lena_mike_dating or v8_jeremy_sex or (lena_louise_sex and lena_reject_louise == False):
                        l "Besides, I'm going through some kind of... \"experimenting\" phase right now."
                        if lena_robert_sex:
                            $ fian = "sad"
                            i "I know. That guy, Robert..."
                            if lena_robert_over2:
                                l "Actually, I'm done with him. After what happened at the life drawing, well..."
                                l "I never liked him that much, to begin with."
                                "That news made me feel better for some reason."
                                $ fian = "disgusted"
                                "I still had no idea how that douchebag got to hook up with Lena in the first place... But at least she kicked him to the curb."
                            elif lena_robert_over:
                                l "Oh, no, that was just a stupid fling..."
                                l "I already told you I was done with him."
                                $ fian = "disgusted"
                                "I still had no idea how that douchebag got to hook up with Lena in the first place... But at least she kicked him to the curb."
                            else:
                                l "Well, yeah... We've been seeing each other from time to time..."
                                $ fian = "disgusted"
                                "She was still hanging out with that douchebag? I couldn't help but feel a knot in my stomach."
                                call friend_xp ('lena', -1) from _call_friend_xp_125
                        else:
                            $ fian = "worried"
                            i "Oh. I see."
                            "I had no idea Lena was seeing other people..."
                            "It wasn't surprising. I was sure she had a lot of guys trying to get with her..."
                            $ fian = "disgusted"
                            "Still, knowing she had been seeing other people stung more than I would've liked."
                        if ian_alison_dating or ian_cherry_dating:
                            $ flena = "n"
                            l "Besides, you've also been dating around, right?"
                            $ fian = "n"
                            i "Yeah, a bit... I might not be ready for a stable relationship either."
                        else:
                            $ flena = "sad"
                            l "I'm sorry to have to turn you down, but..."
                            $ fian = "n"
                            i "Don't apologize. I appreciate your honesty."
                    else:
                        i "Hey, I said I understand, really. You don't need to explain yourself more than you already did."
                    $ fian = "sad"
                    i "I just hope I didn't make things uncomfortable between us by telling you how I feel."
                    $ flena = "n"
                    l "I don't think so, not really..."
                    l "But I need to ask: since I can't offer you what you want..."
                    l "Are you still okay with the way things currently are between us?"
                    menu:
                        "I still want us to hook up":
                            $ renpy.block_rollback()
                            $ fian = "smile"
                            i "Yeah. I'd still like to keep seeing you... like we have been doing up until now."
                            l "Even though you'd like to be in a serious relationship..."
                            $ fian = "n"
                            i "A part of me wants that. An irrational, childish part."
                            i "But as I said, I'm probably not ready for a relationship either, and it would only be trouble."
                            i "I really like you, and what we have right now... It doesn't need to become anything more."
                            i "In fact, it's probably best it stays like this. Much easier for both of us."
                            $ flena = "smile"
                            l "Alright, I just wanted to be sure."

                        "I need some distance":
                            $ renpy.block_rollback()
                            $ ian_lena_dating = False
                            $ ian_lena_breakup = True
                            $ fian = "sad"
                            i "Honestly... I think I'm not."
                            $ flena = "sad"
                            l "I see."
                            i "I'm not mad or disappointed or anything. It's just..."
                            i "I really like you. And I'm afraid that feeling will only increase if we keep seeing each other."
                            i "I know you don't feel the same way, and I'd hate to make things uncomfortable and end up hurting you, or myself..."
                            i "I don't know, maybe I'm being stupid, but..."
                            $ flena = "n"
                            l "No. I think you're being really mature."
                            if ian_will < 2:
                                call will_up() from _call_will_up_53
                            l "I think you're probably right, and I'd hate things to end up badly between us. So if that's what you feel..."
                            l "I respect your decision."
                            $ fian = "n"
                            i "I hope we can still hang out together and all that..."
                            l "Yeah, me too. First and foremost I consider you my friend."
                            $ fian = "smile"
                            i "Me too."
                            "I had no idea if this arrangement would work, or if it was a good idea at all..."
                            $ fian = "n"
                            "I had been rejected, and I was giving up on Lena, which was not easy to do. Not at all."
                            "But at least I had been true to my feelings..."

            "I'd like to keep it casual" if ian_lena_love == False or ian_alison_love:
                $ renpy.block_rollback()
                i "Well, as you said, we're both in a complex moment emotionally."
                i "You've become someone quite special to me, but..."
                if ian_alison_love:
                    "But I had feelings for someone else. My long-time high school friend, Alison..."
                    if ian_lena_love:
                        "I wanted to be with Lena, too, both of them were dear to me..."
                        "If I got into a serious relationship with Lena at the moment, I would only end up disappointing and hurting her."
                i "The truth is I'm just not ready for another relationship at this point."
                if lena_ian_love:
                    $ flena = "blush"
                    l "Oh."
                    $ flena = "sad"
                    l "Yeah, of course. I thought as much."
                    $ fian = "sad"
                    i "I hope I didn't disappoint you or anything..."
                    $ flena = "n"
                    l "No, no. I already suspected this was the case. I mean, I know your reasons."
                    $ fian = "smile"
                    i "What about you? How do you see things?"
                    l "Um, well... I'm not ready for another relationship either, not by a long shot..."
                else:
                    $ flena = "sad"
                    l "I see...!"
                    $ flena = "n"
                    l "Well, I'm glad we see things the same way."
                    $ fian = "smile"
                    i "We do?"
                    l "Yeah. I'm not ready for another relationship, not by a long shot..."
                $ flena = "blush"
                l "It is true that you're more than a simple hookup to me. And I feel it goes both ways..."
                l "But pressuring each other to commit to anything so soon would be a bad idea."
                i "So that means we can keep things as they are now, right?"
                if lena_ian_love:
                    l "Yeah... No pressure."
                    i "Alright."
                else:
                    $ flena = "shy"
                    l "Yeah. Let's keep things more... casual. Nice and easy."
                    i "Good."

        # end states
        if v9_alison_trip:
            if v9_cindy_shoot or lena_axel_dating:
                if v9_cindy_shoot:
                    i "Well..."
                    $ fian = "surprise"
                    i "Oh, damn! Is it this late already?"
                    $ flena = "worried"
                    l "Is there a problem?"
                    $ fian = "worried"
                    i "I promised Wade I would do a favor for him. It's kind of important..."
                    $ flena = "n"
                    l "Of course."
                elif lena_axel_dating:
                    $ flena = "surprise"
                    l "Oh, damn! Is it this late already?"
                    $ fian = "worried"
                    i "Is there a problem?"
                    $ flena = "sad"
                    l "I have a photo shoot lined up. I should get going to get there in time..."
                    $ fian = "smile"
                    i "Of course. Work is work."
                if ian_lena_breakup:
                    $ fian = "n"
                    i "Well... See you around, okay?"
                    l "Sure... And Ian..."
                    l "Despite us not wanting the same thing, I still enjoy hanging out with you."
                    l "I hope we can keep doing that, even if we put some distance between us."
                    $ fian = "smile"
                    i "We will. Bye, Lena."
                elif ian_lena_couple:
                    $ fian = "smile"
                    i "So, thanks for not shutting me down and giving this a chance."
                    i "I'm looking forward to it."
                    $ flena = "shy"
                    l "Same here. See you soon, okay?"
                    i "No doubt about that."
                elif ian_lena_dating:
                    $ fian = "smile"
                    i "I'm glad we finally had this conversation."
                    $ flena = "smile"
                    l "Yeah, me too."
                    l "See you soon, alright?"
                    i "Sure."
                if v9_cindy_shoot:
                    jump v9cindydate
                else:
                    jump v9ianfridayalone
            else:
                scene parknight
                $ flena = "smile"
                $ fian = "smile"
                show ian at lef
                show lena at rig
                with long
                "The sun had been setting during our conversation, and now the city lights were the only ones reflecting on the dark surface of the river."
                jump v9lenadateend
        else:
            jump v9lenadateend

## JUST FRIENDS  #############################################
    else:
        scene street2 with long
        $ fian = "smile"
        $ flena = "n"
        show ian at lef
        show lena at rig
        with long
        if v9_alison_trip == False:
            l "Here we are. Thank you for helping me with the luggage!"
            i "Don't mention it."
        # talk about holly
        if ian_holly_dating:
            $ flena = "happy"
            l "By the way, how are things with Holly?"
            $ fian = "shy"
            i "I'd say they are quite nice... Haven't you talked to her about it?"
            l "I did, in fact."
            $ fian = "smile"
            i "I suspected as much. And what did she tell you?"
            $ flena = "smile"
            l "She's a bit reserved, and I didn't want to poke her too much about it..."
            l "But she sounds really enthusiastic about it. She told me you introduced her to your friends the other night!"
            i "Yeah, it was kind of unexpected, but I think she had fun. And I'd say they loved her."
            if lena_go_holly == 4:
                $ flena = "blush"
                l "Yeah... It's impossible not to love Holly..."
                $ fian = "n"
                i "Huh?"
                $ flena = "shy"
                l "I mean, she's a really sweet girl!"
                $ fian = "smile"
                i "Yeah, she is."
            else:
                l "It's impossible not to love Holly!"
                i "That's true."
            $ flena = "happy"
            l "Anyway, I'm so happy to see things are going well for you two."
            i "It seems they are, yeah. But I still want us to take it slow..."
            $ flena = "smile"
            l "Of course, of course."
        elif v7_holly_kiss:
            $ flena = "sad"
            l "By the way... How are things with Holly?"
            $ fian = "sad"
            if ian_holly_sex:
                i "It's been... Okay. She's a bit awkward around me, but it's what I would expect..."
                l "She's closed off to protect herself. It's a shame this had to happen."
                if ian_lena_over:
                    i "I know. And I know I caused you to do the same..."
                    $ flena = "worried"
                    l "Well... Yeah."
                    i "I wish things had been different... But I made a choice that night, one I wasn't prepared to follow through."
                else:
                    i "I know. I wish things had been different... But I made a choice that night, one I wasn't prepared to follow through."
                i "I cannot make excuses. We all know I fucked up."
                $ flena = "n"
                l "At least you're not shying away from it. It doesn't make what happened go away, but it helps moving forward."
                $ fian = "n"
                i "Good to know..."
                if ian_lena_over:
                    $ flena = "sad"
                    l "I know you were not happy about my decision to stop seeing each other like... more than just friends."
                    jump v9lenaianholly
            else:
                i "It's been... Okay. But I can clearly see how much she's closed off around me."
                if ian_lena_over:
                    i "And I think she still feels guilty about, you know... what happened between you and me."
                    $ flena = "worried"
                    l "Still? I already talked it over with her, it's not her fault at all!"
                    i "She can be really stubborn..."
                    if lena_ian_love:
                        l "..."
                        $ flena = "blush"
                        l "And so can I."
                    else:
                        l "I'll talk to her again. And you... How's it going for you, Ian?"
                    $ fian = "n"
                    l "I know you were not happy about my decision to stop seeing each other like... more than just friends."
                    menu v9lenaianholly:
                        "{image=icon_charisma.webp}I understand" if ian_charisma > 4:
                            $ renpy.block_rollback()
                            $ ian_lena_makeup = True
                            if ian_holly_sex:
                                i "I understand your reasons. And I know what I did, well... It wasn't exactly fair."
                                i "I should've acted more responsibly, especially knowing the implications, but I acted selfishly."
                            else:
                                i "I understand your reasons. Even if nothing really happened between me and Holly, I should've prevented things from getting to that point."
                            i "I guess it would've been different if it wasn't Holly. But she's not just any girl, she's your friend. A close one."
                            if ian_lena < 12:
                                call friend_xp('lena', 1) from _call_friend_xp_126
                            $ flena = "smile"
                            l "Yeah, that's exactly it... I wouldn't have reacted this way if it was just some random girl. We were not dating, after all..."
                            $ flena = "sad"
                            l "But Holly... She was the main reason I made that choice."
                            if ian_holly_sex:
                                $ flena = "serious"
                                l "I still think what you did was not nice..."
                                $ flena = "sad"
                                l "But I'm guilty of not being honest with Holly, too."
                            $ fian = "sad"
                            i "I'm sorry if my actions damaged your relationship with her. And it sucks they damaged my relationship with you."
                            $ flena = "blush"
                            l "Yeah, it does."
                            i "..."
                            l "..."

                        "It seemed unfair to me":
                            $ renpy.block_rollback()
                            i "Well, it seemed unfair to me."
                            if lena_robert_sex:
                                if ian_holly_sex:
                                    i "I mean, you were sleeping around with that guy, Robert. How's that different from what I did?"
                                else:
                                    i "I mean, you were sleeping around with that guy, Robert. Meanwhile, all I did was kiss Holly..."
                                $ flena = "serious"
                                l "Do we really need to go over this again?"
                                if ian_lena > 0:
                                    call friend_xp('lena', -1) from _call_friend_xp_127
                                l "Things would be different if it was just some random girl. But she's not."
                                $ flena = "sad"
                                l "Holly's become a close friend of mine... I made that decision mainly because of her, not because of you."
                                $ fian = "n"
                                if lena_robert_over2:
                                    l "Besides, I'm done with Robert, right after what happened at the life drawing."
                                    "Well, at least that news made me feel a bit better."
                                elif lena_robert_over:
                                    l "Besides, that was just a stupid fling, done and over. You know that."
                                i "Hum... Okay."
                            else:
                                if ian_holly_sex:
                                    $ flena = "serious"
                                    l "Unfair? How so?"
                                    i "I mean, I..."
                                    l "You slept with Holly knowing she had feelings for you, without telling her beforehand you didn't want the same."
                                    $ fian = "serious"
                                    i "Hey, I couldn't be sure about what she felt. She didn't tell me either!"
                                    l "Even if that's the case, you knew she and I had become close friends, and what you sleeping with her would entail, considering you were doing the same with me..."
                                    $ fian = "n"
                                    i "..."
                                    "I couldn't think of a way to refute that. I should've kept my mouth shut."
                                    call friend_xp('lena', -2) from _call_friend_xp_128
                                    $ flena = "n"
                                    l "Let's better drop the subject. Nothing's gonna change at this point."
                                    i "Yeah."
                                else:
                                    i "I mean, nothing really happened between us. It was just a kiss, and I stopped before it got anywhere..."
                                    $ flena = "serious"
                                    l "It's not about that. While I think this whole situation could've been prevented if you had been more open with Holly..."
                                    call friend_xp('lena', -1) from _call_friend_xp_129
                                    $ flena = "sad"
                                    l "I'm at fault too. And this isn't about you kissing another girl. It's about Holly."
                                    l "She's become a close friend of mine... I made that decision mainly because of her, not because of you."
                                    $ fian = "n"
                                    i "Hum... Okay."

                        "I don't wanna talk about it":
                            $ renpy.block_rollback()
                            i "I'd prefer to drop the subject and move on. It's been uncomfortable enough already."
                            i "Shit happened, let's just leave it at that."
                            $ flena = "n"
                            l "Alright."

                else:
                    i "I think she feels ashamed about what happened... And about me rejecting her."
                    l "Yeah... Nobody likes that."
                    $ flena = "n"
                    l "But she'll be alright. I'll make sure of that."
                    $ fian = "smile"
                    i "Good to know."
        # end
        if v9_alison_trip:
            $ flena = "n"
            if v9_cindy_shoot:
                i "It's getting late... I told Wade I'd do a favor for him, so I need to get going."
                l "Of course."
                i "It was nice seeing you again, Lena."
            elif lena_axel_dating:
                l "It's getting a bit late... I have a photo shoot lined up for today, so I should start preparing."
                i "Of course. It was nice seeing you again, Lena."
            else:
                l "I think I'll get going. I need to put many things in order still."
                i "Of course. It was nice seeing you again, Lena."
            $ flena = "n"
            l "I'm glad to be back. See you soon!"
            if v9_cindy_shoot:
                jump v9cindydate
            else:
                jump v9ianfridayalone
        else:
            i "Well, I'll let you go. I'm sure you'll be busy after being away for so many days."
            l "Yeah. I need to set some stuff in order..."
            $ fian = "smile"
            i "It was nice seeing you again, Lena."
            $ flena = "n"
            l "I'm glad to be back. See you soon!"
            jump v9ianfridayalone
## dating end
label v9lenadateend:
    if ian_lena_breakup:
        $ fian = "n"
        i "Well... I think we said everything that needed to be said."
        i "I'll get going if you're okay with that."
        l "Sure... And Ian..."
        l "Despite us not wanting the same thing, I still enjoy hanging out with you."
        l "I hope we can keep doing that, even if we put some distance between us."
        $ fian = "smile"
        i "We will. Bye, Lena."
        jump v9ianfridayalone
    elif ian_lena_couple:
        l "So... What now?"
        $ fian = "smile"
        i "I don't know... What about we just keep doing what we have been doing thus far?"
        i "Seems to be working out pretty well..."
        $ flena = "happy"
        l "Agree to that."
    label gallery_CH09_S04:
        if _in_replay:
            call setup_CH09_S04 from _call_setup_CH09_S04
    i "So..."
    if lena_look == 4:
        scene v9_lena1aa
    else:
        scene v9_lena1bb
    if lena_tattoo2:
        show v9_lena1_t2
    with long
    "We had exchanged enough words."
    if ian_lena_couple:
        "Now was the time for another kind of communication."
        "Kisses could tell as much as words, sometimes even more."
    else:
        "Any doubts were sorted out, and we both knew where we stood regarding each other."
        "We could keep this relationship going without the pressure of expectations, sharing this casual intimacy."
    if lena_look == 4:
        scene v9_lena2aa
    else:
        scene v9_lena2bb
    if lena_tattoo2:
        show v9_lena1_t2
    with long
    "Our tongues and arms intertwined while we kissed."
    "We were sitting so close together that Lena was practically on top of me."
    if ian_lena_sex:
        "I had missed this so much... Her soft and warm skin, the comforting weight of her body on mine, her aroma, her taste..."
    else:
        "I was getting so incredibly horny... To think I hadn't been able to have sex with Lena yet..."
    "I could barely contain my desire."
    if ian_lust < 6 and ian_will > 0 and ian_lena_sex:
        menu:
            "{image=icon_will.webp}Escalate the situation" if ian_will > 0:
                $ renpy.block_rollback()
                call willdown from _call_willdown_11
                jump v9parksex

            "Stop it now":
                $ renpy.block_rollback()
                jump v9lenastopit
    else:
        menu:
            "{image=icon_lust.webp}Escalate the situation" if ian_lust > 5:
                $ renpy.block_rollback()
                jump v9parksex

            "Stop it now":
                $ renpy.block_rollback()
                label v9lenastopit:
                    "Sadly, this was not the place to give in to lust."
                "I decided to leave it there, enjoying Lena's sweet kisses."
                "That would need to be enough for now..."
                stop music fadeout 2.0
                $ fian = "shy"
                $ flena = "flirt"
                scene parknight
                show ian at lef
                show lena at rig
                with long
                l "It's getting late... I have a very busy day tomorrow, so much I need to put back in order."
                if v9_alison_trip:
                    i "I understand... I guess we both could use some rest."
                    $ flena = "n"
                    l "Right... You must be also tired after your trip."
                    $ fian = "smile"
                    i "Yeah, it's been a long day. Let's go."
                else:
                    i "Of course... You must be also tired after your trip."
                    $ flena = "n"
                    l "Yeah... Thank you for helping me with the luggage, by the way."
                    $ fian = "smile"
                    i "Don't mention it. Let's go."
                scene parknight with long
                "I walked Lena home and said goodbye after making out again in front of her door."
                "I had the feeling she would've liked to take things upstairs, just like me, but we would need to settle with just kisses for tonight."
                $ renpy.end_replay()
                jump v9ianfridayalone
## LENA IAN SEX #########################################################################################################################################################################################################

label v9parksex:
    stop music fadeout 2.0
    play music "music/sex.mp3" loop
    if lena_look == 4:
        scene v9_lena3a
    else:
        scene v9_lena3b
    if lena_piercing1:
        show v9_lena3_p1
    elif lena_piercing2:
        show v9_lena3_p2
    if lena_tattoo1:
        show v9_lena3_t1
    if lena_tattoo2:
        show v9_lena3_t2
    if lena_tattoo3:
        if lena_look == 4:
            show v9_lena3_t3a
        else:
            show v9_lena3_t3b
    with long
    "I held Lena's hips and dragged her toward me, making her sit on my lap."
    "Her weight pressed my raging hard-on almost painfully, making it twitch. There was no way Lena wasn't feeling it..."
    "As our kisses continued, my hands got braver, caressing her waist, her exposed thighs, her breasts..."
    if (ian_lena_sex and lena_lust > 3) or lena_fty_show:
        "Lena's breathing was getting noticeably heavier as she kissed me back. I felt her arousal growing..."
    else:
        "Lena wasn't trying to stop me. She reciprocated my kisses and I could feel her breathing getting heavier..."
    if ian_lena_couple:
        i "I missed you so much..."
    else:
        i "I missed this so much..."
    l "Me too..."
    "I didn't care we were in a public place. All I wanted was to keep pushing things further."
    "I was so damn horny...!"
    if lena_look == 4:
        scene v9_lena4a
    else:
        scene v9_lena4b
    if lena_piercing1:
        show v9_lena3_p1
    elif lena_piercing2:
        show v9_lena3_p2
    if lena_tattoo1:
        show v9_lena4_t1
    if lena_tattoo2:
        show v9_lena3_t2
    if lena_tattoo3:
        if lena_look == 4:
            show v9_lena4_t3a
        else:
            show v9_lena4_t3b
    with long
    "My fingers found the button on Lena's shorts and unfastened it."
    "That created enough space for me to slide my hand into Lena's panties comfortably..."
    if ian_lena_sex == False:
        l "Wait, Ian...!"
        $ fian = "blush"
        $ flena = "blush"
        stop music fadeout 2.0
        scene parknight
        show ian at lef
        show lena2 at rig
        with long
        i "I'm sorry, I thought... I got carried away."
        l "I understand, I'm horny too... But we're in the middle of the park!"
        l "Somebody could see us... And this isn't the most appropriate scenario for our first time, don't you think?"
        i "I wasn't trying to push things all the way, but now that you mention it..."
        $ fian = "worried"
        if ian_lena_couple:
            i "We haven't slept together yet. I know you want to take it slow, but now we're officially dating..."
        else:
            i "We haven't slept together yet. I know you want to take it slow, but we've been seeing each other for a few months now..."
        $ flena = "sad"
        l "Yeah, I've been... kinda postponing it. I'm still nervous about it, that's why this is not the right place for it..."
        $ fian = "n"
        i "You're right."
        $ fian = "smile"
        i "I hope we can find the right place soon."
        $ flena = "shy"
        l "We will."
        scene parknight with long
        "I walked Lena home and said goodbye after making out again in front of her door."
        "I really wanted to have sex with her, but I would need to settle with just kisses for tonight..."
        jump v9ianfridayalone
    play sound "sfx/mh1.mp3"
    l "Ian...! What are you doing...?"
    "Despite her words, I didn't feel any desire to move away from her. Lena stayed on my lap as my fingers reached her slit."
    l "Someone could see us..."
    i "There's nobody around. And those bushes block most of the view..."
    "One fingertip snuck between Lena's lower lips, and I found a warm, moist welcome."
    i "You can tell me to stop at any moment. Just say so."
    if lena_look == 4:
        scene v9_lena5a
    else:
        scene v9_lena5b
    if lena_piercing1:
        show v9_lena3_p1
    elif lena_piercing2:
        show v9_lena3_p2
    if lena_tattoo1:
        show v9_lena5_t1
    if lena_tattoo2:
        show v9_lena3_t2
    if lena_tattoo3:
        if lena_look == 4:
            show v9_lena4_t3a
        else:
            show v9_lena4_t3b
    with long
    "She didn't."
    "Instead, she allowed my bold caresses, panting softly in my ear as I tried to pleasure her."
    "My fingers rubbed her clit softly, lubricated by the juices of her arousal."
    "It wasn't the most comfortable position, but it allowed me access to both her pussy and breasts."
    "I lifted up her top to stimulate them too, delighting myself with the touch of their soft skin, broken up only by Lena's hard and pointy nipples."
    "I caressed, pinched, and licked them, and Lena trembled."
    l "I can't believe we're doing this in the middle of the park..."
    if lena_fty_show or lena_lust > 5:
        l "It's turning me on so much..."
        menu:
            "Have sex with Lena":
                $ renpy.block_rollback()
                $ v9_lena_sex = 2
                i "I want to go all the way, Lena..."
                i "I want you, right now."

            "Make her cum":
                $ renpy.block_rollback()
                label v9parkfingers:
                    $ v9_lena_sex = 1
                    "Trying to escalate things even further was out of the question, being in the open like we were..."
                    "But I didn't want to stop either. And Lena seemed to share my sentiment."
                    l "Mhhh, Ian... I love the way you're touching me..."
                    "I kept doing what I was doing. Bit by bit I felt Lena's trembling increasing, her breathing getting heavier..."
                    l "Ian...!"
                    play sound "sfx/oh1.mp3"
                    l "Ohhhmmm!!{w=0.5}{nw}" with vpunch
                    pause 0.5
                    with vpunch
                    pause 0.5
                    with vpunch
                    pause 0.5
                    "Lena shivered between my arms, gasping and digging her fingers in my hair when orgasm struck."
                    "I loved making her cum like that!"
                    "After recovering, Lena recomposed her clothing quickly and discreetly."
                    stop music fadeout 2.0
                    $ fian = "shy"
                    $ flena = "shy"
                    jump v9parkend
    else:
        jump v9parkfingers
    "Lena looked left and right to make sure nobody was in sight."
    if lena_bj > 3:
        l "Keep an eye out for me..."
        if lena_look == 4:
            scene v9_lena6a
        else:
            scene v9_lena6b
        if lena_piercing1:
            show v9_lena6_p1
        elif lena_piercing2:
            show v9_lena6_p2
        if lena_tattoo1:
            show v9_lena6_t1
        if lena_tattoo2:
            show v9_lena6_t2
        if lena_tattoo3:
            show v9_lena6_t3
        with long
        play sound "sfx/bj2.mp3"
        "After saying that, Lena hunched over and undid my trousers' fly with nimble moves."
        "As soon as my hard cock sprung out, Lena wrapped her lips around it and started sucking."
        i "Oof...!"
        "That sudden burst of passion caught me off guard, and so did the amazing sensation of her hot and wet mouth sliding up and down my manhood."
        play sound "sfx/bj6.mp3"
        l "Mhhh...! It's so hard...!"
        l "You really did miss me..."
        i "You have no id--{w=0.5}{nw}"
        i "Wait, someone's coming...!" with vpunch
        show v9_lena6_guy with short
        "Lena lowered her head and took cover while some guy walked by behind us."
        i "..."
        "We stayed completely still and he didn't seem to notice us. Lena still had my throbbing cock in her mouth..."
        if ian_lust > 5:
            "This was so fucking hot...!"
        "After a few seconds, he disappeared, and shortly after the sound of his footsteps."
        hide v9_lena6_guy with short
        l "That was close..."
    "She got up and I thought she would ask for us to get going, but that wasn't what she was thinking at all."
    if lena_look == 4:
        scene v9_lena7a
    else:
        scene v9_lena7b
    with long
    l "Help me with these..."
    "I pulled Lena's shorts down, and her thong with them. Was she really willing to go all the way?"
    if lena_bj > 3:
        l "My heart's racing like crazy... Do you think that guy saw us?"
        i "I don't think so. And if he did, he didn't care one bit."
    else:
        l "My heart's racing like crazy..."
        i "Mine too."
    "The risk of getting caught was real, but I was confident about us being in a good spot."
    "Not that reason was playing a big part at that moment: all I could think about was being deep inside Lena."
    if ian_lust < 9:
        call xp_up('lust') from _call_xp_up_96
    "Luckily for me, that desire was mutual."
    if lena_look == 4:
        scene v9_lena8a
    else:
        scene v9_lena8b
    if lena_tattoo2:
        show v9_lena8_t2
    if lena_tattoo3:
        show v9_lena8_t3
    with long
    play sound "sfx/ah2.mp3"
    l "Ohhh...!"
    i "Mhhh! Fuck, Lena...!"
    "Finally...! This was it... What I had been needing..."
    if ian_lena_couple:
        "Being connected with Lena like this felt like nothing else."
        "Even if we were having sex in the open, everything else around me disappeared."
    else:
        "Being in the open had me on edge, but my excitement was easily overpowering that feeling."
        "All I cared about was what was right in front of me, on top of me, around me..."
    "I was getting lost in this incredible sexual connection I had with Lena."
    if lena_look == 4:
        scene v9_lena9a_animation
    else:
        scene v9_lena9b_animation
    with short
    play sound "sfx/ah6.mp3"
    l "Oh, yes, Ian... Fuck me..."
    if ian_lena_couple:
        l "I needed this so much... To be with you again...!"
    else:
        l "I needed this so much...!"
    "The way she was moving her hips felt amazing."
    "The way her pussy contracted around my cock, the way Lena moaned in my ear..."
    if lena_look == 4:
        scene v9_lena8a
    else:
        scene v9_lena8b
    if lena_tattoo2:
        show v9_lena8_t2
    if lena_tattoo3:
        show v9_lena8_t3
    with long
    "Then I caught a movement out of the corner of my eye. Was someone watching us...?"
    show v9_lena8_peep with long
    "Yes! I saw a silhouette behind the bushes. He was standing still, looking in our direction as Lena continued to ride me."
    "Her face was buried on the opposite side of my neck, so she couldn't see we had a peeping tom..."
    menu:
        "Warn Lena":
            $ renpy.block_rollback()
            $ v9_lena_sex = 3
            "I needed to warn Lena."
            i "Wait...! I think someone's watching...!"
            "Lena stopped bouncing on my cock for a second and checked our surroundings."
            if lena_fty_show or lena_lust > 7:
                $ v9_lena_sex = 4
                $ lena_fty_show = True
                "The next words she whispered in my ear almost made me cum."
                l "I don't care. Let him watch..."
            else:
                l "Oh, fuck...!"
                stop music fadeout 1.0
                scene parknight with short
                "Lena got off me and hid behind the bench's back until the onlooker left, continuing his way."
                $ fian = "blush"
                $ flena = "blush"
                jump v9parkend
            if lena_look == 4:
                scene v9_lena9a_animation
            else:
                scene v9_lena9b_animation
            with short
            "Lena continued to ride me, even harder than before."
            i "Oh, fuck...!"
            "I dug my fingers into Lena's hips, groaning with pleasure. This was wild."
            l "I'm not stopping... I love this so much...!"
            l "Your cock is driving me crazy, Ian!"

        "Don't say anything":
            $ renpy.block_rollback()
            "The last thing I wanted was to spoil this awesome moment I was living with Lena... So I decided to keep quiet and keep fucking her."
            if lena_look == 4:
                scene v9_lena9a_animation
            else:
                scene v9_lena9b_animation
            with short
            "If he wanted to watch, so be it. As long as he didn't interrupt us I didn't care."
            "All that mattered was Lena's ass bouncing on my cock."

    "We were both trapped in this whirlwind of mutual lust and desire, the physical manifestation of the strong attraction we felt for each other."
    if ian_lena_couple:
        "Of the bond we shared."
    l "Ohhh...! Mhhoooh!!"
    "Lena's moans became louder, and her hip movements more intense."
    "She was on fire...!"
    if lena_look == 4:
        scene v9_lena8a
    else:
        scene v9_lena8b
    if lena_tattoo2:
        show v9_lena8_t2
    if lena_tattoo3:
        show v9_lena8_t3
    with flash
    l "Ohmmmhhh!!!" with vpunch
    "She could barely contain a long howl when climax jolted her entire body."
    with vpunch
    pause 0.6
    with vpunch
    pause 0.6
    "I felt her pussy contracting around my cock and her fingers digging into my hair and skin as she squeezed me tight."
    l "God..."
    "We stayed like that for a few long moments, my hard cock still throbbing inside her melting pussy, as Lena recovered from her blissful daze."
    stop music fadeout 4.0
    "I didn't notice the moment our spectator left, but when I looked for him he was already gone."
    "At least he had the decency to not interrupt us..."
    "I would've liked to keep going, but sadly we couldn't afford to."
    "We heard voices and footsteps. Several people were coming our way."
    "We had tested our luck too much already."
    $ fian = "shy"
    $ flena = "shy"

# after sex
label v9parkend:
    scene parknight
    show ian at lef
    show lena at rig
    with long
    if v9_lena_sex > 1:
        "We recomposed our clothing quickly and discreetly."
    if v9_lena_sex == 4:
        play sound "sfx/punchgym.mp3"
        with vpunch
        "Lena punched me in the arm."
        l "You're crazy! Doing these kinds of things here... That guy totally saw us!"
        i "You didn't seem to mind when I told you!"
        $ flena = "flirt"
        l "Well, what can I say? I couldn't bring myself to stop fucking you... It felt so damn good..."
        if ian_chad > 3:
            $ fian = "confident"
            i "Fuck yeah, it did..."
            i "It would've taken a lot more than a passing peeping tom to stop me from fucking you, too."
        else:
            $ fian = "smile"
            i "Indeed, it did..."
            i "It would've taken a lot more than a passing peeping tom to stop me from what I was doing, too."
        l "I like this adventurous side of you..."
        i "Same here. You surprised me..."
    elif v9_lena_sex == 3:
        l "That guy totally saw us!"
        i "Maybe... He was far away, we can't be really sure..."
        $ flena = "serious"
        play sound "sfx/punchgym.mp3"
        with vpunch
        "Lena punched me in the arm."
        l "You're crazy! Doing these kinds of things here...!"
        $ fian = "worried"
        i "Hey, I'd say you were into it too..."
        $ flena = "shy"
        l "I was... But this was way too risky!"
        $ fian = "smile"
        i "I'm sorry if I was being a bit too adventurous..."
    else:
        play sound "sfx/punchgym.mp3"
        with vpunch
        "Lena punched me in the arm."
        if lena_bj > 3:
            l "That was crazy! That guy almost saw us while I was sucking you off!"
            $ fian = "smile"
            i "Nah, I don't think he did."
            if v9_lena_sex > 1:
                "I was pretty sure that second dude actually caught wind of what we were doing... But there was no point in telling Lena about it now."
        else:
            l "That was crazy! What if someone saw us?"
            if v9_lena_sex > 1:
                "They did... But there was no point in telling Lena about it now."
            $ fian = "smile"
            i "Um... I don't think they did..."
        $ flena = "flirt"
        l "Are you sure? I lost sight of everything near the end!"
        l "There was no way I could've noticed if we had any audience, ha ha."
        i "Well, that's what makes it fun, isn't it?"
        l "I don't know, but... This was a lot of fun, that's for sure."
    if v9_lena_sex > 1:
        $ flena = "sad"
        if v9_lena_sex == 3:
            l "It sucks we didn't get to finish..."
            i "I know, but it was still amazing."
            i "Having sex with you is always mind-blowing, it doesn't mind if I get to cum or not."
        else:
            l "I'm sorry you didn't get to cum, though..."
            $ fian = "smile"
            i "Don't worry... I'm satisfied knowing you did."
            i "And it's not like I didn't get to enjoy it... Having sex with you is always mind-blowing, it doesn't matter if I get to cum or not."
        $ flena = "flirt"
        l "In any case, I owe you one..."
    else:
        $ flena = "flirt"
        l "I guess I owe you one..."
    $ fian = "happy"
    i "I'll be more than happy to collect that debt."
    i "Come, I'll walk you home..."
    scene parknight with long
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH09_S04")
    jump v9ianfridayalone

# IAN FRIDAY END #########################################################################################################################################################################################################
label v9ianfridayalone:
    stop music fadeout 2.0
    scene fade with long
    pause 1
    play sound "sfx/door_home.mp3"
    scene ianhomenight
    show ian
    with long
    if ian_lena_couple:
        $ fian = "happy"
        "I wasn't able to stop smiling as I made my way back home."
        i "She said yes... I guess we are officially dating now..."
        if v9_lena_sex > 1:
            i "And what we did at the park was wild! I can't believe that actually happened..."
        elif v9_lena_sex == 1:
            i "And what we did at the park was wild... I made her cum right there in the open."
        else:
            "Too bad I didn't get the chance to have sex with her today..."
        "It had been a very long year since I broke up with Gillian, and only recently things had started to get better..."
        if v5_ian_showup:
            "And not only had Lena corresponded to my feelings, but Hierofant was hiring me after all!"
        i "I can't remember the last time I felt this hyped."
        i "I'm even starting to feel confident about my chances of winning the book contest!"
    elif ian_lena_dating:
        if ian_lena_love:
            $ fian = "n"
            "When I arrived home I still wasn't sure how to feel."
            $ fian = "sad"
            "Lena had turned down my feelings... But she also offered us to keep seeing each other casually."
            # fuckbuddy
            if ian_lena_sex:
                "I had accepted, of course. I didn't want to lose her, and being her fuckbuddy was better than nothing..."
                $ fian = "n"
                i "I wonder if \"fuckbuddy\" is an accurate term. It's clear there's more than just sex between us..."
            # sexless
            else:
                # third base
                if v7_ian_date:
                    "I had accepted, of course. I didn't want to lose her or miss the chance to go all the way with her..."
                # second base
                else:
                    "I had accepted, of course. I didn't want to lose her or miss the chance to get more intimate with her..."
                $ fian = "n"
            i "But I need to accept this is as far as she's willing to go with me right now. Maybe it will change in time..."
        else:
            $ fian = "smile"
            "That conversation with Lena had lifted a load off my shoulders."
            "I was afraid she wouldn't be okay with us openly being just friends with benefits, but she was on board with the idea."
            $ fian = "n"
            # fuckbuddy
            if ian_lena_sex:
                i "I wonder if \"friends with benefits\" is an accurate term. It's clear there's more than just sex between us..."
            # sexless
            else:
                # third base
                if v7_ian_date:
                    i "I wonder if \"friends with benefits\" is an accurate term. It's clear there's more than just physical attraction between us..."
                # second base
                else:
                    i "I wonder if \"friends with benefits\" is an accurate term. We didn't have many opportunities to be intimate but it's clear there's more than just basic attraction between us..."
            $ fian = "smile"
            i "Still, this makes things a lot easier..."
        if v9_lena_sex > 1:
            i "And what we did at the park was wild! I can't believe that actually happened..."
        elif v9_lena_sex == 1:
            i "And what we did at the park was wild... I made her cum right there in the open."
        else:
            "Too bad I didn't get the chance to have sex with her today..."
        "It had been a very long year since I broke up with Gillian, and only recently things were starting to get better..."
        if v5_ian_showup:
            $ fian = "smile"
            "But Lena wasn't the only good thing. Hierofant was hiring me after all!"
        i "I'd say things are looking good so far..."
        i "I'm even starting to feel confident about my chances of winning the book contest!"
    elif ian_lena_breakup:
        $ fian = "sad"
        "I was feeling so drained when I got home."
        "Lena had turned down my feelings. I should've seen that coming."
        i "I probably already knew this would be her answer... But I had to be honest with myself."
        i "This is for the best... I wouldn't have been happy with what she was offering me."
        i "But this means I've lost my chance with her... Fuck..."
        if v5_ian_showup:
            $ fian = "n"
            "At least Hierofant had decided to hire me after all. Those were really good news."
        i "I need to focus on my book now. I won't give up on that contest."
    else:
        if ian_lena_makeup:
            $ fian = "n"
            "I couldn't get a strange feeling out of my head after speaking with Lena today."
            i "We talked again about what happened with Holly... But this time I felt something was different."
            i "Maybe I finally regained her trust? I don't know..."
            $ fian = "smile"
            i "But it felt just right."
        elif ian_lena_over:
            $ fian = "sad"
            "When I got back home I was still thinking about Lena. Of course I was."
            "Going back to being just friends was being as hard as I had anticipated. I couldn't help but notice how attractive she was..."
            if ian_lena_love:
                "And not just physically. I really liked her... A lot."
            else:
                i "It's so damn hard not to like her."
            "I took a deep breath."
            $ fian = "n"
            i "It is what it is. I need to accept that, hard as it may be."
        else:
            $ fian = "n"
            "It was nice hanging out with Lena again. I still wasn't sure how I managed to befriend her, but..."
            $ fian = "smile"
            "I was glad I did. She was a really cool girl."
        "It had been a very long year since I broke up with Gillian, and only recently things had started to get better."
        "Having Lena in my life was part of that..."
        if v5_ian_showup:
            $ fian = "smile"
            "But also the fact Hierofant was hiring me after all! Those had been some great news."
        i "I need to focus on my book right now. I'm not giving up on that contest."
    if v9_alison_trip:
        jump v9iansaturdaywrite
## ALISON BBC ##################################################################
    if v7_alison_voyeur:
        label gallery_CH09_S05:
            if _in_replay:
                call setup_CH09_S05 from _call_setup_CH09_S05
        stop music fadeout 2.0
        scene ianroomnight with long
        play sound "sfx/keyboard.mp3"
        show v2_ianwrite with short
        "It was late and I was a bit tired, but I wanted to write a bit more before going to bed..."
        play sound "sfx/sms.mp3"
        $ fian = "n"
        scene ianroomnight
        show ian
        with short
        i "A text message... It's Jeremy's."
        nvl clear
        j_p "{i}\"Hey bro, how's it going? Here it's being kinda crazy {image=emoji_crazy.webp}{image=emoji_laugh.webp}\"{/i}"
        show ian at left with move
        play sound "sfx/sms.mp3"
        show v9_bbc0
        show v7_alisonbbc1_vid
        with short
        $ fian = "worried"
        "He sent a video. There was a caption written under the preview."
        j_p "{i}\"Threesome: check {image=emoji_check.webp}\"{/i}"
        menu:
            "Play the video":
                $ renpy.block_rollback()
                $ v9_alison_voyeur = True
                $ fian = "blush"
                "I couldn't resist the temptation and pressed the play button..."
                play music "music/sex.mp3" loop
                hide v7_alisonbbc1_vid with short

            "Delete it":
                $ renpy.block_rollback()
                i "I don't want to see this, whatever it is..."
                i "I've already seen too much. I don't like feeling like a creep."
                i "Maybe if the girl in the video wasn't Alison..."
                scene ianroomnight
                show ian at left
                with short
                "I deleted the video and put the phone away."
                show ian at truecenter with move
                i "I need to stay focused on my book..."
                $ renpy.end_replay()
                jump v9iansaturdaymorning

        if v9alisonphone:
            a "Something must've slipped last time I was talking with him before I blocked him. I got a bit too emotional..."
            a "{i}I can't believe that idiot did that! What's wrong with him?{/i}"
            a "{i}...{/i}"
            a "{i}I'm worried about him. Maybe I shouldn't have blocked him... I'm sure he wouldn't have lashed out at you guys if I didn't.{/i}"
            a "{i}...{/i}"
            $ fian = "disgusted"
            i "Wait, this is when she called me earlier today..."
            a "{i}Thats--{/i}{w=0.5}{nw}"
            hide v9_bbc0
            show v9_bbc1
            # if alison_sexy == 2:
            #     show v9_bbc1_p
            with short
            guy "{i}Come on baby, hang up already.{/i}"
            a "{i}Hey, wait!{/i}"
            $ fian = "surprise"
            "I was not familiar with the guy that suddenly entered the frame and started groping Alison."
            a "{i}No, it's nothing...{/i}"
            a "{i}Anyway, I'm sorry that my ex caused you trouble.{/i}"
            guy "Hurry it up."
            "He began kneading Alison's breasts lusciously."
            $ fian = "worried"
            i "This guy... It has to be Jeremy's friend, Billy."
            a "{i}Sure, well... Talk to you soon, okay?{/i}"
            a "{i}Stop it...!{/i}"
        else:
            a "{i}He insists on paying for everything... Well, I'm not one to complain!{/i}"
            a "{i}...{/i}"
            a "{i}Anyway, I'm sorry you got involved in my mess. I had no idea Milo could do such a thing...{/i}"
            $ fian = "disgusted"
            i "Wait, this is when she called me earlier today..."
            a "{i}...{/i}"
            a "{i}Oh. Okay.{/i}"
            hide v9_bbc0
            show v9_bbc1
            # if alison_sexy == 2:
            #     show v9_bbc1_p
            $ fian = "surprise"
            guy "{i}What, he hung up?{/i}"
            a "{i}Wait...{/i}"
            "I was not familiar with the guy that suddenly entered the frame and started groping Alison."
            guy "{i}You're done, aren't you? Come on.{/i}"
            "He began kneading Alison's breasts lusciously."
            $ fian = "worried"
            i "This guy... It has to be Jeremy's friend, Billy."
        i "So the one filming must be..."
        hide v9_bbc1
        show v9_bbc2
        # if alison_sexy == 2:
        #     hide v9_bbc1_p
        #     show v9_bbc2_p
        with short
        j "{i}I'm horny again, too.{/i}"
        "Indeed. I saw him when he pointed the phone camera at a mirror."
        "He joined his friend and started groping Alison intently."
        "She didn't try to stop them as they sandwiched her, rubbing their dicks against her ass and thighs."
        b "{i}Let's go for another round!{/i}"
        $ fian = "blush"
        i "What the fuck...?"
        a "{i}Jeez... This is all you guys can think about, isn't it?{/i}"
        hide v9_bbc2
        show v9_bbc3
        # if alison_sexy == 2:
        #     hide v9_bbc2_p
        #     show v9_bbc3_p
        with short
        "Despite her words, Alison got on her knees and held both their half-erect dicks in her hands, stroking them."
        "Jeremy followed her movements with the camera, filming her like she was some kind of pornstar."
        b "{i}You're just so sexy, babe.{/i}"
        j "{i}Hell yeah. You're one of the hottest girls I've ever met.{/i}"
        a "{i}Stop trying to flatter me. You don't need to sugar the pill.{/i}"
        play sound "sfx/bj2.mp3"
        "As she said that, Alison began sucking one of the cocks she was handling."
        "Watching the pics and videos Jeremy had been sharing with me had been a shocking experience already, but this..."
        i "Is this even real...?"
        hide v9_bbc3
        show v9_bbc4
        # if alison_sexy == 2:
        #     hide v9_bbc3_p
        with short
        b "{i}Here, suck mine now!{/i}"
        b "{i}Get a good angle on this!{/i}"
        "Billy sat on the edge of the bed and dragged Alison toward him."
        "She began blowing him eagerly as Jeremy knelt next to them, filming the action up close."
        b "{i}That's it, babe. You know how to suck dick...{/i}"
        b "{i}But I know you can take it deeper!{/i}"
        $ fian = "surprise"
        hide v9_bbc4
        show v9_bbc4b
        with vpunch
        play sound "sfx/gag1.mp3"
        a "{i}Mghh!!{/i}"
        "Billy's cock disappeared into Alison's mouth when he pushed her head down."
        j "{i}Damn!{/i}"
        $ fian = "blush"
        "It was nothing like Jeremy's monster cock, but the guy was well endowed..."
        "He held Alison's head in place for a few long seconds, until she needed to catch some air again."
        hide v9_bbc4b
        show v9_bbc4c
        with short
        play sound "sfx/bj4.mp3"
        a "{i}Aghff!{/i}"
        a "{i}Don't be so rough!{/i}"
        b "{i}Sorry, babe. Come on, keep sucking.{/i}"
        play sound "sfx/dp2.mp3"
        "Alison's complaints didn't seem to match her actions. She obeyed and continued using her mouth on Billy's cock devotedly."
        "Jeremy continued to film her sloppy blowjob for a while. Alison made the shaft shiny with her saliva, and spit was dripping down her chin."
        j "{i}Okay, I'm fucking hard now. I want in the action too!{/i}"
        "Watching the video had gotten my cock rock hard, too..."
        scene v7_jerkoff_animation1b
        show v9_bbc5 at ianjerk
        show v7_jerkoff_hand
        with short
        "I unzipped my pants and started masturbating as I continued to bear witness to that baffling scene."
        j "{i}Here, let me put it in...{/i}"
        a "{i}Nhh... If you're not gonna wear a condom, don't--{/i}"
        j "{i}I know, I know. I won't cum inside, don't worry.{/i}"
        a "{i}That's what you said last time...{/i}"
        b "{i}Hey, keep sucking my cock.{/i}"
        hide v7_jerkoff_hand
        hide v9_bbc5
        show v9_bbc5b at ianjerk
        show v7_jerkoff_hand
        with short
        play sound "sfx/bj6.mp3"
        a "{i}Mhhhnn...{/i}"
        "Alison got busy again as Jeremy stuffed his massive tool into her pussy."
        "The way he was holding the phone made it look like I was in Jeremy's shoes."
        "Bit by bit he shoved his member into Alison's pussy. It looked like a cock that big had trouble holding a full erection, but he managed to get it done."
        hide v7_jerkoff_hand
        hide v9_bbc5b
        show v9_bbc5c at ianjerk
        show v7_jerkoff_hand
        with short
        b "{i}Come on, suck it properly like before.{/i}"
        play sound "sfx/dp2.mp3"
        "Billy pushed Alison's head down again, and she complied."
        "Her body was being rocked back and forth by Jeremy's thrusts, and she bobbed her head to the same rhythm, gobbling up Billy's cock."
        b "{i}Hell yeah, that's it!{/i}"
        i "Fuck, Alison..."
        b "{i}Let's change it up. I want that pussy too!{/i}"
        j "{i}Sure thing!{/i}"
        hide v7_jerkoff_hand
        hide v9_bbc5c
        show v9_bbc6 at ianjerk
        # if alison_sexy == 2:
        #     show v9_bbc6_p at ianjerk
        show v7_jerkoff_hand
        with short
        "The unreal spectacle continued."
        "Alison let them handle her how they pleased, seemingly quite into it..."
        play sound "sfx/dp1.mp3"
        "She sucked and fucked with dedication, looking to satisfy them and getting quite a lot of enjoyment out of it herself."
        "I jerked off to her shameless display, fascinated by a side of her that had been hidden until Jeremy revealed it to me."
        "To think Alison could be slutty to the point of having a threesome with two guys and letting them film her...!"
        "My cock was hard as iron!"
        hide v7_jerkoff_hand
        hide v9_bbc6
        show v9_bbc7 at ianjerk
        # if alison_sexy == 2:
        #     hide v9_bbc6_p
        #     show v9_bbc7_p at ianjerk
        show v7_jerkoff_hand
        with short
        "They changed it up again. Jeremy's log was spreading Alison's pussy while Billy rubbed his dick all over her face."
        b "{i}That's it, give it to her J!{/i}"
        j "{i}Shit, I'm about to burst!{/i}"
        a "{i}Do it outside...!{/i}"
        hide v7_jerkoff_hand
        hide v9_bbc7
        show v9_bbc7b at ianjerk
        # if alison_sexy == 2:
        #     hide v9_bbc7_p
        #     show v9_bbc7_p at ianjerk
        show v7_jerkoff_hand
        with flash
        "Jeremy kept his word and pulled out just in time."
        "A spray of cum splashed Alison's tits and a second one on her belly."
        a "{i}Oh, fuck...! There's so much...!{/i}"
        b "{i}My man Jeremy cums buckets!{/i}"
        play sound "sfx/high5.mp3"
        "I heard the sound of a high five between him and Billy."
        b "{i}Now's my turn... Here, give me the phone! {/i}"
        hide v7_jerkoff_hand
        hide v9_bbc7b
        show v9_bbc8 at ianjerk
        # if alison_sexy == 2:
        #     hide v9_bbc7_p
        show v7_jerkoff_hand
        with short
        "Billy sat on top of Alison, jerking himself off rapidly."
        b "{i}Squeeze those tits together!{/i}"
        "Alison did just that and opened her mouth, ready to receive Billy's jizz."
        "It looked like she was having a great time..."
        b "{i}Uhhh!!{/i}"
        scene v7_jerkoff1b
        show v7_jerkoff_cum1
        show v9_bbc8 at ianjerk
        show v7_jerkoff_hand
        with flash
        i "Ahhh!!"
        "I came at the same time Billy sprayed Alison's face with another load."
        if ian_lust < 9:
            call xp_up ('lust') from _call_xp_up_97
        "The video ended at that moment, leaving a frozen image of Alison covered in cum on my screen."
        stop music fadeout 2.0
        $ fian = "blush"
        scene ianroomnight
        show iannude
        show ian2_pantless
        with short
        i "Fuck..."
        "Once my excitement had dissipated, shameful guilt and regret became apparent."
        i "What the hell's wrong with me?"
        "It wasn't the first time I felt like that, of course. I had masturbated with Alison's videos before, but this one was wild beyond reason."
        "I couldn't deny it: seeing Alison fucked like a slut turned me on so much."
        if ian_lena_couple:
            "What would Lena think about me if she knew?"
            i "I really shouldn't be doing this. Damn you, Jeremy..."
        elif ian_holly_dating:
            "What would Holly think about me if she knew?"
            i "I really shouldn't be doing this. Damn you, Jeremy..."
        elif ian_alison_sex:
            "It also made me feel a bit envious. I had a go at Alison myself, but I never imagined she could be this freaky in bed..."
        else:
            "It also made me feel a bit envious. If I had known Alison was that freaky in bed..."
            if v2_alison_home and v2_ian_limp:
                $ fian = "depress"
                "Maybe then I wouldn't have gotten soft with her that night."
        $ renpy.end_replay()
        $ gallery_unlock_scene("CH09_S05")
    jump v9iansaturdaymorning

## IAN SATURDAY #########################################################################################################################################################################################################
# if alison trip == True and cindy shoot == False
label v9iansaturdaywrite:
    $ v9bookmarker = 1
    play sound "sfx/door.mp3"
    $ fian = "n"
    scene ianroom
    show ian
    with long
    i "Alright... Let's get to it."
    hide ian
    show v2_ianwrite
    with long
    i "I'm feeling inspired."
    if book_scifi:
        i "My {color=#3FB305}Science Fiction{/color} novel is almost complete. All I need is a proper ending."
    if book_fantasy:
        i "My {color=#B30505}Fantasy{/color}  novel is almost complete. All I need is a proper ending."
    if book_historical:
        i "My {color=#D1B402}Historical{/color}  novel is almost complete. All I need is a proper ending."
    if v9hollytalkbook:
        i "What was Holly's advice...? I should take that in mind."
    jump v9_writebookchoice
label v9iansaturdaywrite2:
    "I continued to write for most of the night, and I only went to sleep when my eyelids felt heavy as lead."
    "It was a very good writing session..."
    jump v9iansunday
# SATURDAY MORNING #################################################################################################################################################
label v9iansaturdaymorning:
    call calendar(_day="Saturday") from _call_calendar_13

    $ ian_look = 2
    $ fian = "n"
    play music "music/normal_day.mp3" loop
    scene ianroom with long
    "I woke up with the singular purpose in mind to get everything ready for the contest."
    show ian with short
    i "I've finished writing the story. Now it's time for the boring part: editing and polishing."
    $ fperry = "smile"
    $ fian = "n"
    play sound "sfx/door.mp3"
    scene ianhome with long
    "I went to the kitchen to get a strong cup of coffee. I would need it."
    show ian at lef
    show perry at rig
    with short
    p "Hey! I'm heading to the Visual Fighter t--{w=0.5}tournament with Wade. Don't you wanna come?"
    if v9_cindy_shoot:
        i "I need to go to Cindy's shoot in his stead, don't you remember."
        $ fperry = "meh"
        p "Oh, that's true. You're gonna miss Wade playing against some of the b--{w=0.5}best players in the country..."
    else:
        i "Nah, I'm busy... And I'm not into that game nearly as much as you guys."
        $ fperry = "meh"
        p "As you wish. Some of the b--{w=0.5}best players in the country are coming... And Wade is gonna face them!"
    $ fian = "smile"
    i "Wish him luck on my behalf."
    p "It's not about luck, it's all about skill!"
    $ fperry = "n"
    p "By the way, he'll be s--{w=0.5}spending the night here today."
    $ fian = "n"
    i "How come?"
    p "The tournament lasts two days, and our p--{w=0.5}place is closer to the venue..."
    $ fperry = "meh"
    p "And Cindy's been rather s--{w=0.5}stingy about it, so we think it'll be best for him to stay here while it lasts."
    p "She would probably break his f--{w=0.5}focus."
    i "I see. You guys are taking this very seriously."
    $ fperry = "n"
    p "Anyway, see you tonight!"
    hide perry with short
    i "Let's get to it..."
    play sound "sfx/keyboard.mp3"
    scene ianroom
    show v2_ianwrite
    with long
    "I sat in my room and began revising my novel from the start, changing what I didn't like and ironing out any mistakes."
    if v5_ian_showup:
        "I felt motivated, especially after knowing Hierofant was hiring me. I was finally making progress..."
    else:
        "If I wanted to get out of that fucking magazine I needed to kickstart my career as an author..."
    if v7_cindy_kiss:
        "I had trouble focusing, though."
        "I would be seeing Cindy later today. I would finally get a chance to talk to her, or so I hoped..."
        if ian_lena_couple:
            "And what happened yesterday with Lena only made things more complicated."
        "I finished my coffee and tried forcing my mind back into the task at hand."
    i "I've been working on this novel for so long. I can't believe I'm about to finish it."
    if ian_holly_dating:
        "Knowing I had Holly by my side made me feel even more hopeful. We followed a very similar path."
        "She was also busy finishing the new book of her trilogy, but she texted me to ask how was I doing and to give me advice and encouragement."
        "She was lovely..."
    elif ian_lena_couple:
        "Knowing Lena wanted to be with me, that I would have her on my side from now on, made me even more hopeful."
        "She was going through hard times, but she found time to text me to ask how was I doing with the book and give me encouragement."
        "She was the most amazing girl I had ever met..."
    # go cindy date
    if v9_cindy_shoot:
        scene ianroom
        show ian
        with long
        if v7_cindy_kiss:
            i "It's about time... I need to get ready."
            "I was more nervous than I was willing to admit to even myself. I had been obsessing over Cindy for weeks now..."
            "I took a deep breath."
            i "No point in overthinking things now. Let's see what happens."
            hide ian with short
            call screen v9ianclotheschoice2
            stop music fadeout 2.0
            show ian with long
            if ian_look == "wits1":
                $ v9_ianwearing = "wits"
                i "Wearing this seems appropriate."
            elif ian_look == "charisma1":
                $ v9_ianwearing = "charisma"
                i "I'll wear this. I want to cause a good impression."
            elif ian_look == "athletics1":
                $ v9_ianwearing = "athletics"
                i "This is comfortable and it makes me feel kind of confident."
            elif ian_look == "lust1":
                $ v9_ianwearing = "lust"
                i "I'll wear this... I want to look good."
            else:
                $ v9_ianwearing = "n"
                i "Like this is okay."
            show ian at truecenter with move
            i "Let's go..."
        else:
            $ fian = "surprise"
            stop music fadeout 2.0
            i "Fuck, I almost forgot!"
            i "It's almost time for Cindy's photo shoot!"
            "I stopped what I was doing and left in a hurry."
        stop music fadeout 2.0
        jump v9cindydate
    # stay/ night
    else:
        play sound "sfx/keyboard.mp3"
        scene ianroomnight
        show v2_ianwrite
        with long
        "I continued writing until nighttime, making progress."
        "It was a Saturday well spent."
# wade and perry come back from the tournament
label v9iansaturdaywade:
    stop music fadeout 2.0
    # cindy shot reactions
    if v7_cindy_kiss and v9_cindy_shoot == 2:
        $ fian = "disgusted"
        show ian with short
        i "Fuck... There goes my chance to talk to her...!"
        i "What do I do now? Shall I wait here for her to leave, or...?"
        $ fian = "worried"
        i "No, that would be way too creepy and desperate."
        i "All I can do is go home... Fuck!"
    elif v9_cindy_shoot == 3 and lena_axel_dating and ian_lena_love:
        play sound "sfx/door_home.mp3"
        $ fian = "disgusted"
        scene ianhomenight
        show ian
        with short
        "I couldn't stop thinking about Lena. And Axel."
        "She said she was there for strictly professional reasons but was she telling the truth?"
        "Couldn't she have found another photographer?"
        $ fian = "sad"
        if ian_lena_couple:
            i "I can't be doubting her like that... We're supposed to trust each other."
            "Still, I couldn't help but wonder what was going on right now at Axel's place."
            "I wanted to take my phone and call her, but I knew I shouldn't."
        elif ian_lena_dating:
            i "I mean, she's free to do whatever she wants. She's not my girlfriend, after all..."
            i "But with her ex..."
            "I couldn't help but wonder what was going on right now at Axel's place."
        elif ian_lena_breakup:
            i "This makes her rejection sting ever harder. Maybe I should've accepted remaining as friends with benefits..."
            "I couldn't help but wonder what was going on right now at Axel's place."
        else:
            i "I mean, I have no right to complain about her life choices. Whatever we had is over..."
            "Still, I couldn't help but wonder what was going on right now at Axel's place."
    elif ian_cindy_over:
        $ fian = "sad"
        scene ianhomenight
        show ian
        with short
        i "It's over... This is the end of my affair with Cindy. It wasn't sustainable..."
        i "We can pretend all we want, but I doubt we'll manage to just forget what really happened..."
        i "I suppose I'll need to steer clear of her for a while."
        if ian_lena_couple:
            i "I have Lena now, after all. This thing with Cindy wasn't going anywhere, to begin with."
        else:
            i "This thing with Cindy wasn't going anywhere, to begin with. She's Wade's girlfriend... for now."
        i "Ah, fuck. I decided to put an end to this, but I still like her a lot...!"
        if ian_cindy_sex:
            i "Having sex with her was... amazing. If only I could do it again..."
        else:
            i "Now I kinda regret just kissing her that night..."
        $ fian = "n"
        i "But no, this is how it has to be. It's the closest I get to erasing my mistake."
    # wade and perry
    scene ianhomenight_dark with long
    "It was late when I heard the sound of keys at the front door."
    play sound "sfx/door_home.mp3"
    $ fian = "n"
    $ fperry = "smile"
    $ fwade = "smile"
    scene ianhomenight with long
    show perry at rig3
    show wade2
    with short
    p "We're here!"
    show ian at lef3 with short
    i "What's up? How was the first day of the tournament?"
    w "I got wiped out."
    i "Dang. So no matches for you tomorrow?"
    w "Nope."
    p "But it doesn't matter! Wade was a--{w=0.5}amazing!"
    menu:
        "What happened?":
            $ renpy.block_rollback()
            play music "music/the_fortress.mp3" loop
            $ fian = "smile"
            i "Yeah? Tell me what happened!"
            w "I had very bad luck in the pairings. I had to fight this dude in the second round..."
            w "He's considered one of the best Visual Fighter players in the world. Best in the country for sure."
            p "Everyone expected him to defeat Wade easily. He's a r--{w=0.5}rockstar when it comes to fighting games..."
            p "But Wade beat him!"
            i "No way!"
            w "He mopped the floor with me in the first round, but I think he underestimated me and got sloppy in the second, so I caught him with a combo and won..."
            p "And the third round was n--{w=0.5}nuts! Trading blow for blow..."
            w "He had me against the ropes. At one point I thought I was done."
            p "But then Wade awakened his ultra-instincts and t--{w=0.5}turned it around with one of the hardest moves to do in the game!"
            p "The crowd went wild, everyone was jumping, and s--{w=0.5}screaming... You should've been there!"
            $ fian = "happy"
            i "That sounds epic! Congrats, man."
            $ fwade = "happy"
            w "Thanks. It was really cool."
            if ian_wade < 12:
                call friend_xp('wade', 1) from _call_friend_xp_130
            $ fian = "n"
            i "Too bad you lost to the next guy... Was he even better than this one?"
            $ fwade = "smile"
            $ fperry = "n"
            hide wade2
            show wade
            with short
            w "No, he wasn't, but my mind was fried after that match... I had no energy left to focus."
            i "That sucks."
            $ fperry = "happy"
            p "Never mind that! Wade was the m--{w=0.5}man! Everyone wanted to talk to him..."
            p "And an e-sports promoter had offered him to try out for a t--{w=0.5}team!"
            $ fwade = "happy"
            $ fian = "smile"
            w "Yeah, we were just having some beers with him and some other people."
            i "Sounds like this tournament was really fruitful for you."
            w "Seems like it."
            p "Imagine if you get on the team! You could make a living as a pro p--{w=0.5}player!"
            w "We'll see what happens... But that'd be cool."
            menu:
                "{image=icon_friend.webp}I'm impressed!" if ian_wade > 5:
                    $ renpy.block_rollback()
                    $ fian = "happy"
                    i "I'm impressed! For a guy like that to take notice of your skills..."
                    i "Even if you didn't win the tournament, this has been a great victory!"
                    if ian_charisma < 9:
                        call xp_up('charisma') from _call_xp_up_98
                    $ fwade = "happy"
                    w "Yeah, I'm pretty stoked. It was totally worth it."
                    if v9_cindy_shoot > 0:
                        $ fperry = "meh"
                        p "By the way, how did the p--{w=0.5}photo shoot go?"
                        jump v9wadecindyshoot
                    else:
                        i "Good, man. Now, I'll get back to writing if you don't mind."
                        i "And congratulations!"

                "Good luck":
                    $ renpy.block_rollback()
                    i "Good luck with that. It would be cool indeed."
                    if v9_cindy_shoot > 0:
                        $ fperry = "meh"
                        p "By the way, how did the p--{w=0.5}photo shoot go?"
                        jump v9wadecindyshoot
                    else:
                        i "Well, I'll get back to writing if you don't mind."
                        p "Goodnight."

                "Cool story bro":
                    $ renpy.block_rollback()
                    $ fian = "n"
                    if v9_cindy_shoot > 0:
                        i "Cool story, bro. I'll go back to doing my stuff if you don't mind..."
                        $ fperry = "meh"
                        p "Wait, wait! How did the p--{w=0.5}photo shoot go?"
                        jump v9wadecindyshoot
                    else:
                        i "Cool story, bro. I'll go back to doing my stuff if you don't mind."
                        i "Goodnight."

        "Not interested":
            $ renpy.block_rollback()
            i "Yeah, I'm sure he was."
            p "He beat the best player in the c--{w=0.5}country! People lost their minds at the end of that match!"
            w "But I got beat in the following round..."
            i "So it was a lucky break, huh? Look guys, I'll go back to doing my stuff if you don't mind."
            $ fperry = "meh"
            $ fwade = "n"
            if ian_wade > 3:
                call friend_xp('wade', -1) from _call_friend_xp_131
            if v9_cindy_shoot > 0:
                p "Wait, wait! How did the p--{w=0.5}photo shoot go?"
                jump v9wadecindyshoot
            else:
                i "Goodnight."
    stop music fadeout 2.0
    if v9_alison_trip:
        jump v9iansunday
    else:
        play sound "sfx/door.mp3"
        scene ianroomnight with long
        "I went back to my room to keep working."
        "The deadline was almost upon me, and in a couple of months, the winners of the contest would be announced."
        "The long road I had been traveling was almost over. The moment of truth would come soon..."
        "The moment to see if all that I did was enough to make my dream come true."
        jump v9_lenastart
##tell wade about cindy shoot
label v9wadecindyshoot:
    $ fwade = "n"
    $ fian = "n"
    w "Oh yeah, that's right."
    i "Well, about that..."
    if v9_cindy_shoot == 3 and lena_axel_dating:
        "I was still wondering how Lena's shoot would've played out... But Wade wanted info about Cindy."
    if v9_cindy_shoot == 3:
        $ fian = "serious"
        i "Honestly... I don't trust this guy. He was low-key hitting on Cindy throughout the shoot."
        $ fwade = "serious"
        $ fperry = "sad"
        w "So it's not just me, you think so too!"
        if v9_axel_warn > 0:
            i "Yeah. I had to call him out at one point when he got too touchy."
            w "What the fuck...?"
        else:
            i "Yeah..."
        $ fian = "sad"
        i "And the shoot was at his place, not in a studio."
        $ fwade = "sad"
        w "You went to his home? But you left together with Cindy, right?"
        $ fian = "n"
        i "Yeah."
        w "..."
        $ fwade = "n"
        w "I'll talk to her tomorrow."
        w "Thanks for helping out, Ian."
        i "That's what friends are for."
    if v9_cindy_shoot == 2:
        if wade_cindy > 0:
            $ wade_cindy -= 1
        i "Well... I got kicked out."
        $ fperry = "sad"
        $ fwade = "sad"
        p "What?"
        i "Before the shoot started, when... When Cindy got naked, she was feeling uncomfortable, so..."
        w "Wait, she got naked? Completely?"
        i "Yeah. It was pretty awkward, as you can imagine... For both of us."
        i "So she asked me to leave."
        p "And you obeyed her?"
        $ fian = "serious"
        i "What was I supposed to do? Refuse to leave and fight with Axel? We were at his place."
        $ fwade = "surprise"
        w "You went to his home?"
        $ fian = "worried"
        i "Yeah..."
        $ fwade = "serious"
        w "I'm gonna call her."
        hide wade with short
        p "Dude, what a m--{w=0.5}mess..."
        i "Yeah. And I've had enough for today."
    if v9_cindy_shoot == 1:
        if wade_cindy > 0:
            $ wade_cindy -= 1
        $ fian = "sad"
        i "I didn't go."
        $ fperry = "sad"
        $ fwade = "sad"
        w "But you said you would!"
        i "I mean, I met Cindy, but she didn't allow me to go to the shoot."
        $ fwade = "serious"
        w "What!?"
        i "Yeah... She wasn't thrilled about the idea. I can see why."
        p "You failed us!"
        $ fian = "serious"
        i "And what was I supposed to do? Get in forcefully and refuse to leave?"
        $ fwade = "n"
        w "No, you did all you could... Thanks for trying to help."
        $ fian = "n"
        i "I'm sorry, man."
        $ fwade = "serious"
        w "I'm gonna call her."
        hide wade with short
        p "Dude, what a m--{w=0.5}mess..."
        i "Yeah. And I've had enough for today."
    stop music fadeout 2.0
    if v9_alison_trip:
        jump v9iansunday
    else:
        play sound "sfx/door.mp3"
        scene ianroomnight with long
        "I went back to my room and tried to focus on my book again."
        "The long road I had been traveling was almost over. The moment of truth would come soon..."
        "The moment to see if all that I did was enough to make my dream come true."
        jump v9_lenastart

##IAN SUNDAY (if v9_cindy_sex or v9_alison_trip ########################################################################################################################################################################
label v9iansunday:
    call calendar(_day="Sunday") from _call_calendar_14
label v9iansunday2:
    # ian comes back from cindys place
    if v9_cindy_sex:
        $ fperry = "n"
        $ fian = "n"
        play sound "sfx/door_home.mp3"
        scene ianhome with long
        show ian at lef
        show perry2 at rig
        with short
        p "Hey. Where were you?"
        $ fian = "worried"
        i "Uh..."
        p "We thought you'd come straight home after the photo shoot with Cindy. We wanted you to tell us how it went."
        p "Wade even texted you, but you didn't answer."
        "Fuck. I had just crossed the door and Perry was putting me under scrutiny."
        i "I spent the night out..."
        p "Obviously. But where?"
        $ timeout = 3
        $ timeout_label = "v9perrylie"
        menu:
            # "With Holly" if ian_holly_dating: Can't sleep with both Holly and Cindy in the same playthrough
                # $ renpy.block_rollback()
                # $ v9_cindy_lie = 3
                # $ fian = "n"
                # i "I was with Holly."
                # p "With Holly? Where?"
                # i "At her place."
                # p "Doesn't she live with her parents?"
                # $ fian = "serious"
                # i "They were away during the weekend. What's up with the interrogation?"
                # p "Nothing, just curious. You could've said something anyway."

            "With Lena" if ian_lena_dating or ian_lena_over:
                $ renpy.block_rollback()
                $ fian = "n"
                i "I was with Lena."
                if ian_lena_over:
                    $ v9_cindy_lie = 1
                    p "With Lena? Didn't you say things were over between you two?"
                    i "Yeah, but still... You know."
                    p "Mhhh..."
                else:
                    $ v9_cindy_lie = 3
                    p "Ah, of course."
                p "You could've said something anyway."

            "With Alison" if ian_alison_sex:
                $ renpy.block_rollback()
                $ fian = "n"
                i "I was with Alison."
                p "With Alison? Where?"
                i "At her place."
                p "Doesn't she live with her parents?"
                $ fian = "serious"
                i "They were away during the weekend. What's up with the interrogation?"
                if v9_alison_trip:
                    $ v9_cindy_lie = 1
                    p "Just curious. You already spent a few days with Alison on that trip..."
                    p "You must be getting hung up on her."
                    p "You could've said something anyway."
                else:
                    $ v9_cindy_lie = 2
                    p "Just curious. You could've said something anyway."

            "With Cherry" if ian_cherry_dating:
                $ renpy.block_rollback()
                $ fian = "n"
                i "I was with Cherry."
                p "With Cherry, really?"
                if ian_cherry_love:
                    $ v9_cindy_lie = 2
                    p "You brought her home once this week already..."
                    i "Well, she wanted to see me again. I must be doing something right."
                else:
                    $ v9_cindy_lie = 1
                    p "After what happened last time I thought you wanted to keep your distance..."
                    i "Well, she called me and I didn't know how to turn her down."
                    p "Mhhh..."
                p "You could've said something anyway."

            "With a girl":
                $ renpy.block_rollback()
                $ v9_cindy_lie = 1
                $ fian = "n"
                i "I was with a girl."
                p "With a girl? Who?"
                i "You don't know her."
                # if ian_holly_dating: Cindy v Holly route clash
                    # p "Really? I thought you were dating Holly, dude."
                    # i "I'm not... {i}dating{/i} her. I mean, I am, but we're not there yet."
                    # p "Sounds like a scumbag move."
                if ian_lena_dating:
                    p "Really? And what about Lena?"
                    i "What about her?"
                    p "I thought you were dating her."
                    i "I am, but that doesn't mean I can't meet other girls..."
                p "Where did you meet her?"
                i "Through the internet."
                p "I didn't know you were using dating apps."
                $ fian = "n"
                i "I didn't say it was through a dating app. What's up with the interrogation?"
                p "Nothing, just curious... You could've said something anyway."

            "I don't owe you an explanation":
                $ renpy.block_rollback()
                label v9perrylie:
                    $ v9_cindy_lie = 0
                $ fian = "serious"
                i "I don't owe you an explanation, dude. I'm not living at my parents' place anymore."
                $ fperry = "serious"
                call friend_xp('perry', -1) from _call_friend_xp_132
                p "I was just asking, I don't know why you're getting on the defensive all of a sudden."
                i "It's just none of your business, that's it."
                p "Yeah, alright. I don't see why you couldn't say something, but have it your way."

        $ timeout_label = None
        $ fperry = "meh"
        p "So, how was the shoot with Cindy and that guy?"
        $ fian = "sad"
        i "It was alright..."
        $ fian = "n"
        i "By the way, where's Wade? And why aren't you with him?"
        $ fperry = "n"
        p "He got eliminated from the tournament yesterday..."
        i "That sucks."
        $ fperry = "smile"
        hide perry2
        show perry at rig
        with short
        p "Not really. Before that, he beat the best player in the whole country! It was epic!"
        i "He did, huh?"
        p "Yeah, people went crazy. And even though he lost his next match, a headhunter offered him to try out for a pro e-sports team!"
        i "That sounds cool... Good for him."
        $ fperry = "meh"
        p "That's all you're gonna say? It's a big deal!"
        i "Yeah, yeah. Now, if you don't mind, I need to take a shower."

    # talk cherry /write if cindy shoot
    if v9_alison_trip:
        if v9_cindy_shoot:
            $ v9bookmarker = 2
            $ fian = "n"
            if v9_cindy_sex:
                play sound "sfx/shower.mp3"
                scene ianroom with long
                $ ian_look = 2
                "After taking a long shower I went straight to my room, still processing recent events."
                "I wasn't sure if things were going my way or spiraling out of control..."
                show ian with short
            else:
                play sound "sfx/door.mp3"
                scene ianroom
                show ian
                with long
            "With everything that had been going on, I hadn't had time to sit down and finish my book."
            i "Alright... Let's get to it."
            hide ian
            show v2_ianwrite
            with long
            i "I'm feeling inspired."
            if book_scifi:
                i "My {color=#3FB305}Science Fiction{/color} novel is almost complete. All I need is a proper ending."
            if book_fantasy:
                i "My {color=#B30505}Fantasy{/color}  novel is almost complete. All I need is a proper ending."
            if book_historical:
                i "My {color=#D1B402}Historical{/color}  novel is almost complete. All I need is a proper ending."
            if v9hollytalkbook:
                i "What was Holly's advice...? I should take that in mind."
            jump v9_writebookchoice
            label v9iansaturdaywrite3:
                $ fian = "n"
            "I was getting in the groove of things when someone rang the doorbell."
            play sound "sfx/doorbell.mp3"
            $ fperry = "n"
            scene ianhome
            show ian at lef
            show perry at rig
            with long
        else:
            if v9_cindy_sex:
                play sound "sfx/shower.mp3"
                scene ianhome with long
                "I took a long shower, still processing everything that had been going on."
                "I wasn't sure if things were going my way or spiraling out of control..."
                "I had just finished getting dressed when someone rang the doorbell."
            else:
                scene ianhome with long
                "It was a lazy Sunday morning."
                "Both Perry and I were doing our thing when someone unexpectedly rang the doorbell."
            $ ian_look = 2
            $ fian = "n"
            play sound "sfx/doorbell.mp3"
            $ fperry = "n"
            show ian at lef
            show perry at rig
            with short
        i "Are you expecting someone?"
        p "Nope."
        "Perry went to get the door. We were both surprised to see who was on the other side."
        show ian at lef3
        show perry at rig3
        with move
        $ fcherry = "sad"
        play music "music/broken_dreams2.mp3" loop
        show cherry with short
        ch "Hi, guys..."
        $ fian = "sad"
        $ fperry = "sad"
        i "Cherry, what are you doing here?"
        ch "I'm sorry, is it a bad time?"
        p "No, of course not! Come on in."
        ch "Sorry for dropping by so suddenly. I was in the area and..."
        ch "I wanted to talk about what happened last night with Lena and all."
        $ fian = "n"
        $ fperry = "meh"
        i "Oh, that."
        p "It's okay, you don't have to e--{w=0.5}explain yourself to us or anything if you don't want to..."
        ch "But I do. I wanted to apologize, first of all."
        p "It was just bad luck. Nobody could've predicted that would happen."
        ch "Still, it's because of my wrongdoings that it happened, and I ruined your night."
        ch "So I'm sorry."
        i "We said it's okay..."
        ch "I know you've heard from Lena about what happened. She was dating Axel and I was... sleeping with him behind her back."
        i "Something like that, yeah."
        if ian_lust > 4 and v8_lena_story:
            i "She also mentioned the threesomes."
            $ fcherry = "blush"
            ch "Yeah..."
        ch "I'm not proud of what I did. Not at all. Lena has every right to hate me."
        menu:
            "I'm sure you didn't do it on purpose":
                $ renpy.block_rollback()
                $ v9_cherrytalk = "naive"
                i "I'm sure you didn't do it on purpose. Axel probably kept that he was dating Lena from you, or..."
                ch "No. I knew they were together from the beginning."
                $ fian = "sad"
                ch "I knew what I was doing... And did it anyway."
                p "And... w--{w=0.5}why did you do it?"
                $ fcherry = "blush"
                ch "I just..."

            "Why did you do it?":
                $ renpy.block_rollback()
                $ v9_cherrytalk = "neutral"
                i "Why did you do it? You knew she was in a relationship with Axel, right?"
                ch "Yeah, I did..."
                i "I'm not trying to judge you here. I can see you feel what you did was wrong, but I guess you had your reasons..."
                $ fcherry = "blush"
                ch "I did, but that doesn't justify it. I just..."

            "What you did was wrong":
                $ renpy.block_rollback()
                $ v9_cherrytalk = "wrong"
                $ fian = "serious"
                i "What you did was wrong. Cheating on someone is one of the worst things one can do..."
                "Cherry's story was reminding me too much of what happened to me with Gillian. I hated it..."
                call friend_xp('cherry', -1) from _call_friend_xp_133
                $ fperry = "serious"
                p "Cut her some s--{w=0.5}slack. It's not like she was cheating on anybody herself. Not exactly..."
                ch "I got involved in their relationship, and that's wrong. I knew that all along, but..."
                $ fian = "n"
                i "But?"
                $ fcherry = "blush"
                ch "But I just couldn't help myself. I just..."

        ch " I guess I was in love."
        $ fperry = "meh"
        hide perry
        show perry2 at rig3
        with short
        p "The guy is also to blame. He was the one in a relationship, after all!"
        $ fcherry = "n"
        ch "And that's why Lena hates him too..."
        i "What did you find so appealing about Axel to justify acting as you did?"
        ch "I've wondered that myself a lot. I still do."
        ch "But aside from his obvious charms, I liked him for what most people don't see. What he only showed behind closed doors..."
        ch "Nevermind. The heart wants what the heart wants, even if it doesn't make any sense."
        p "So... Are you still seeing him? Lena's ex?"
        $ fcherry = "sad"
        ch "No... After his breakup with Lena, well..."
        ch "He was in a very bad place emotionally. I tried talking with him, but he blocked me out."
        menu:
            "Listen":
                $ renpy.block_rollback()
                ch "It was clear he didn't want me there, so I left. Tried to move on."
                ch "But I saw him again about a month ago, we stumbled upon each other at a bar."
                ch "We talked. He looked okay, like he was doing fine again. And he apologized."
                p "Did he t--{w=0.5}try to make a move on you?"
                ch "No, not at all. In fact, I could feel a wall between us, despite him acting politely."
                ch "And I... I felt ashamed of myself."
                $ fian = "worried"
                i "Why?"
                ch "Because a part of me wanted to jump over that wall. To break it down."
                ch "To ask him to be with me again."
                ch "After all that happened... After all the mayhem I caused, after all the pain..."
                ch "I thought I had been moving on with my life, but those old feelings were still there."
                ch "I thought that maybe now that Lena wasn't in the picture he'd take me, but nothing further from the truth..."
                "Cherry paused, visibly distraught like she was trying hard to contain her emotions."
                ch "Sorry... I don't want to burden you with my stupid drama, it's just..."
                if v9_cherrytalk == "neutral":
                    i "Don't worry... As I said, we're not judging you."
                    p "No, we are not."
                    ch "Thanks..."
                    call friend_xp('cherry', 1) from _call_friend_xp_134
                if v9_cherrytalk == "naive":
                    i "Don't worry... we're here to listen."
                    p "Yeah."
                    ch "Thanks..."
                    call friend_xp('cherry', 1) from _call_friend_xp_135
                if v9_cherrytalk == "wrong":
                    i "As I said, I think what you did was wrong, but we all make mistakes."
                    $ fperry = "serious"
                    p "Dude, don't j--{w=0.5}judge her. She just opened up to us."

            "Pretend to listen":
                $ renpy.block_rollback()
                if ian_chad < 5:
                    $ ian_chad += 1
                "I was not in the mood to listen to someone's drama that night... I just wanted to get some rest."
                "I dozed off for a while, thinking about my own stuff until Cherry was done."

        ch "Anyway, I did what I did... And that's why Lena reacted the way she did."
        ch "I'm sorry that my wrongdoings caused trouble for you."
        $ fperry = "n"
        $ fian = "n"
        hide perry2
        show perry at rig3
        with short
        p "You've already apologized too much, stop it."
        $ fcherry = "smile"
        ch "Okay."
        $ fcherry = "n"
        ch "Well then, I think that's all I wanted to talk about... Sorry for imposing."
        $ fcherry = "smile"
        $ fperry = "serious"
        ch "I mean, sorry not sorry. I needed to talk to you guys."
        $ fperry = "n"
        $ fian = "smile"
        p "That's better."
        stop music fadeout 2.0
        ch "So, that was everything... I guess I'll get going. See you some other time."
        if perry_cherry:
            "She kissed Perry on the cheek."
            ch "Let me know when you want to go get that beer at the Fortress."
            $ fperry = "smile"
            $ fian = "worried"
            p "Sure."
            ch "Bye bye."
        else:
            $ fian = "n"
            i "Bye."
        hide cherry with short
        show ian at lef
        show perry at rig
        with move
        if perry_cherry:
            i "What was that about? You asked her to go on a date to the Fortress?"
            $ fperry = "serious"
            p "It's not a date. I said we c--{w=0.5}could hang out sometime since she likes the bar."
            $ fian = "smile"
            i "Hey, good going..."
            p "Don't start with it!"
            i "Alright, alright."
        else:
            i "Seems like the modeling scene is indeed a troublesome one."
            p "That's why I like to live a s--{w=0.5}simple life..."
    # end
    scene ianroom with long
    $ fian = "n"
    if v9_cindy_sex:
        if v9_alison_trip == False:
            play sound "sfx/shower.mp3"
            scene ianroom with long
            $ ian_look = 2
            "After taking a long shower I went straight to my room, still processing recent events."
            "I wasn't sure if things were going my way or spiraling out of control..."
        else:
            play sound "sfx/door.mp3"
            "I went back to my room after Cherry left."
        show ian with short
        if ian_cindy_love:
            $ fian = "blush"
            "I couldn't get Cindy out of my head. The feeling of her body, her smell, her moans..."
            "All that growing tension between us had been resolved in the most incredible way possible."
            if ian_cindy_sex:
                "What happened that night in the alley was not a fluke: Cindy also harbored feelings for me."
            else:
                "Everything up to this point hadn't been a mirage: Cindy also harbored feelings for me."
            $ fian = "shy"
            "I always thought she was out of my reach, but now... I had made her mine."
            $ fian = "worried"
            "She was still dating Wade, though..."
            if ian_holly_dating:
                "And I had started seeing Holly as more than just friends. What about her...?"
            "I knew what I was doing was fucked up. I felt like a piece of shit... and still, I wanted to keep doing it."
            $ fian = "sad"
            i "I wonder what will happen now. I guess she'll break up with Wade, but then what?"
            i "Can we really be together...?"
        else:
            $ fian = "blush"
            "All the tension between me and Cindy had finally been resolved, and now I couldn't get what happened out of my head."
            if ian_cindy_sex:
                "What happened that night in the alley was not a fluke: I had secured what I thought was beyond my reach."
            else:
                "I had gotten what I was after, what I thought was beyond my reach."
            "I had made Cindy mine... even if she was still dating Wade."
            if ian_lena_couple:
                $ fian = "disgusted"
                "What I had done was fucked up, and not only because of Wade. I asked Lena to start dating me seriously, and she agreed..."
                "And the first thing I did was cheat on her with Cindy. What the hell was wrong with me?"
                "I really liked Lena, more than I had liked anyone in a very long time. But Cindy..."
                $ fian = "sad"
                "My feelings for her were kind of confusing, but very strong. I just couldn't resist her, especially now that I knew I could have her."
            else:
                $ fian = "sad"
                "I knew what I was doing was fucked up. I felt like a piece of shit... and still, I wanted to keep doing it."
                "I felt sorry for Wade, but he had dug his grave himself."
                $ fian = "n"
                "Cindy was too much for him, and if it wasn't me, some other guy, most probably Axel, would end up snatching her."
        "My mind was racing, and I needed to focus on my book. This was the last push."
    else:
        play sound "sfx/door.mp3"
        "I went back to my room to keep working."
    "The deadline was almost upon me, and in a couple of months, the winners of the contest would be announced."
    "The long road I had been traveling was almost over. The moment of truth would come soon..."
    "The moment to see if all that I did was enough to make my dream come true."
    jump v9_lenastart

# SCREENS #########################################################################################################################################################################################################

screen book_screen_7():
    tag book
    imagebutton auto "card7_victory_%s.webp" pos (83, 79) action SetVariable("book_card6", "victory") , [ Play ("ch_one", "sfx/book_card.mp3") ] , Return()
    imagebutton auto "card7_sacrifice_%s.webp" pos (677, 79) action SetVariable("book_card6", "sacrifice")  , [ Play ("ch_one", "sfx/book_card.mp3") ] , Return()
    imagebutton auto "card7_defeat_%s.webp" pos (1270, 79) action SetVariable("book_card6", "defeat") , [ Play ("ch_one", "sfx/book_card.mp3") ] , Return()

screen v9clothingshop():
    if ian_wits > 4 and ian_wardrobe_wits1 == False:
        imagebutton idle "wardrobe_ian_wits1.webp" hover "wardrobe_ian_wits1_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v9shopwits')
    elif ian_wardrobe_wits1:
        imagebutton idle "wardrobe_ian_wits1.webp"
        imagebutton idle "wardrobe_lena_wits1_owned.webp"
    else:
        imagebutton idle "wardrobe_ian_wits1_block.webp"
    if ian_charisma > 4 and ian_wardrobe_charisma1 == False:
        imagebutton idle "wardrobe_ian_charisma1.webp" hover "wardrobe_ian_charisma1_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v9shopcharisma')
    elif ian_wardrobe_charisma1:
        imagebutton idle "wardrobe_ian_charisma1.webp"
        imagebutton idle "wardrobe_lena_charisma1_owned.webp"
    else:
        imagebutton idle "wardrobe_ian_charisma1_block.webp"
    if ian_athletics > 4 and ian_wardrobe_athletics1 == False:
        imagebutton idle "wardrobe_ian_athletics1.webp" hover "wardrobe_ian_athletics1_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v9shopathletics')
    elif ian_wardrobe_athletics1:
        imagebutton idle "wardrobe_ian_athletics1.webp"
        imagebutton idle "wardrobe_lena_athletics1_owned.webp"
    else:
        imagebutton idle "wardrobe_ian_athletics1_block.webp"
    if ian_lust > 4 and ian_wardrobe_lust1 == False:
        imagebutton idle "wardrobe_ian_lust1.webp" hover "wardrobe_ian_lust1_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v9shoplust')
    elif ian_wardrobe_lust1:
        imagebutton idle "wardrobe_ian_lust1.webp"
        imagebutton idle "wardrobe_lena_lust1_owned.webp"
    else:
        imagebutton idle "wardrobe_ian_lust1_block.webp"
    imagebutton idle "v7shopback.webp" hover "v7shopback_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v9leaveshop')
    add "sexshop_money.webp" pos (1799, 29)
    text "{font=[font_big_noodle_oblique]}{color=#000000}[ian_money]{/color}":
        size 30
        xpos 1815 ypos 56

screen v9ianclotheschoice():

    imagebutton idle "wardrobe_ian_red.webp" hover "wardrobe_ian_red_hover.webp" focus_mask True action SetVariable("ian_look", "cool") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    if ian_wardrobe_wits1:
        imagebutton idle "wardrobe_ian_wits1.webp" hover "wardrobe_ian_wits1_hover.webp" focus_mask True action SetVariable("ian_look", "wits1") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "wardrobe_ian_wits1_block.webp"
    if ian_wardrobe_charisma1:
        imagebutton idle "wardrobe_ian_charisma1.webp" hover "wardrobe_ian_charisma1_hover.webp" focus_mask True action SetVariable("ian_look", "charisma1") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "wardrobe_ian_charisma1_block.webp"
    if ian_wardrobe_athletics1:
        imagebutton idle "wardrobe_ian_athletics1.webp" hover "wardrobe_ian_athletics1_hover.webp" focus_mask True action SetVariable("ian_look", "athletics1") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "wardrobe_ian_athletics1_block.webp"
    if ian_wardrobe_lust1:
        imagebutton idle "wardrobe_ian_lust1.webp" hover "wardrobe_ian_lust1_hover.webp" focus_mask True action SetVariable("ian_look", "lust1") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "wardrobe_ian_lust1_block.webp"

screen v9ianclotheschoice2():

    imagebutton idle "wardrobe_ian_base.webp" hover "wardrobe_ian_base_hover.webp" focus_mask True action SetVariable("ian_look", 2) , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    if ian_wardrobe_wits1:
        imagebutton idle "wardrobe_ian_wits1.webp" hover "wardrobe_ian_wits1_hover.webp" focus_mask True action SetVariable("ian_look", "wits1") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "wardrobe_ian_wits1_block.webp"
    if ian_wardrobe_charisma1:
        imagebutton idle "wardrobe_ian_charisma1.webp" hover "wardrobe_ian_charisma1_hover.webp" focus_mask True action SetVariable("ian_look", "charisma1") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "wardrobe_ian_charisma1_block.webp"
    if ian_wardrobe_athletics1:
        imagebutton idle "wardrobe_ian_athletics1.webp" hover "wardrobe_ian_athletics1_hover.webp" focus_mask True action SetVariable("ian_look", "athletics1") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "wardrobe_ian_athletics1_block.webp"
    if ian_wardrobe_lust1:
        imagebutton idle "wardrobe_ian_lust1.webp" hover "wardrobe_ian_lust1_hover.webp" focus_mask True action SetVariable("ian_look", "lust1") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "wardrobe_ian_lust1_block.webp"

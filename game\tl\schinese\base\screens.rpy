﻿
translate schinese strings:
    old "Back"
    new "返回"
    old "Skip"
    new "跳过"
    old "Auto"
    new "自动"
    old "Hide"
    new "隐藏"
    old "Q.Save"
    new "快速保存"
    old "Q.Load"
    new "快速加载"
    old "Options"
    new "设置"
    old "Return"
    new "返回"
    old "New Game"
    new "新游戏"
    old "Save"
    new "保存"
    old "Load"
    new "加载"
    old "End Replay"
    new "结束回放"
    old "Main Menu"
    new "主菜单"
    old "Extras"
    new "附加内容"
    old "Quit"
    new "退出"
    old "About"
    new "关于"
    old "Version [config.version!t]\n"
    new "版本 [config.version!t]\n"
    old "Made with {a=https://www.renpy.org/}Ren'Py{/a} [renpy.version_only].\n\n[renpy.license!t]"
    new "游戏引擎：{a=https://www.renpy.org/}Ren'Py{/a} [renpy.version_only]。\n\n[renpy.license!t]"
    old "Page {}"
    new "第 {} 页"
    old "Automatic saves"
    new "自动保存"
    old "Quick saves"
    new "快速保存"
    old "empty slot"
    new "空白页"
    old "<"
    new "<"    
    old ">"
    new ">"
    old "Display"
    new "显示"
    old "Window"
    new "窗口化"
    old "Fullscreen"
    new "全屏"
    old "Unseen Text"
    new "未读文本"
    old "After Choices"
    new "完成选项后继续"
    old "Transitions"
    new "过渡"
    old "Text Speed"
    new "文本速度"
    old "Auto-Forward Time"
    new "自动加载时间"
    old "Music Volume"
    new "音乐音量"
    old "Sound Volume"
    new "音效音量"
    old "Test"
    new "测试"
    old "Voice Volume"
    new "语音音量"
    old "Mute All"
    new "全部静音"
    old "History"
    new "历史"
    old "The dialogue history is empty."
    new "对话历史为空。"
    old "Controls"
    new "控制"
    old "Keyboard"
    new "键盘"
    old "Mouse"
    new "鼠标"
    old "Gamepad"
    new "游戏手柄"
    old "Enter"
    new "回车/进入"
    old "Advances dialogue and activates the interface."
    new "推进对话并激活界面。"
    old "Space"
    new "空格"
    old "Advances dialogue without selecting choices."
    new "推进对话但不选择选项。"
    old "Arrow Keys"
    new "方向键"
    old "Navigate the interface."
    new "导航界面。"
    old "Escape"
    new "Esc"
    old "Accesses the game menu."
    new "访问游戏菜单。"
    old "Ctrl"
    new "Ctrl"
    old "Skips dialogue while held down."
    new "按住时跳过对话。"
    old "Tab"
    new "Tab"
    old "Toggles dialogue skipping."
    new "开启或关闭快进功能。"
    old "Page Up"
    new "Page Up"
    old "Rolls back to earlier dialogue."
    new "回滚到之前的对话。"
    old "Page Down"
    new "Page Down"
    old "Rolls forward to later dialogue."
    new "继续对话。"
    old "Hides the user interface."
    new "隐藏用户界面。"
    old "Takes a screenshot."
    new "截屏。"
    old "Toggles assistive {a=https://www.renpy.org/l/voicing}self-voicing{/a}."
    new "切换辅助功能 {a=https://www.renpy.org/l/voicing}自动语音{/a}。"
    old "Opens the accessibility menu."
    new "打开辅助功能菜单。"
    old "Left Click"
    new "左键点击"
    old "Middle Click"
    new "中键点击"
    old "Right Click"
    new "右键点击"
    old "Mouse Wheel Up\nClick Rollback Side"
    new "鼠标滚轮上滑\n点击回滚区域"
    old "Mouse Wheel Down"
    new "鼠标滚轮下滑"
    old "Right Trigger\nA/Bottom Button"
    new "右扳机\nA/底部按钮"
    old "Left Trigger\nLeft Shoulder"
    new "左扳机\n左肩"
    old "Right Shoulder"
    new "右肩"
    old "D-Pad, Sticks"
    new "D-Pad, 摇杆"
    old "Start, Guide"
    new "开始, 指南"
    old "Y/Top Button"
    new "Y/顶部按钮"
    old "Calibrate"
    new "校准"
    old "Yes"
    new "是"
    old "No"
    new "否"
    old "Skipping"
    new "跳过"
    old "Menu"
    new "主页"
    old "Prefs"
    new "设置"
    old "Start"
    new "开始"
    old "Preferences"
    new "设置"
    old "Help"
    new "帮助"
    old "Upload Sync"
    new "自动上传"
    old "Download Sync"
    new "自动下载"
    old "Language"
    new "语言"
    # game/screens.rpy:380
    old "[config.name!t]"
    new "[config.name!t]"

    # game/screens.rpy:383
    old "[config.version]"
    new "[config.version]"

    # game/screens.rpy:569
    old "[gui.about!t]\n"
    new "[gui.about!t]\n"

    # game/screens.rpy:649
    old "{#file_time}%A, %B %d %Y, %H:%M"
    new "{#file_time}%A, %B %d %Y, %H:%M"

    # game/screens.rpy:669
    old "{#auto_page}A"
    new "{#auto_page}A"

    # game/screens.rpy:672
    old "{#quick_page}Q"
    new "{#quick_page}Q"

    # game/screens.rpy:676
    old "[page]"
    new "[page]"

    # game/screens.rpy:1212
    old "▸"
    new "▸"

    # game/screens.rpy:1262
    old "[message!tq]"
    new "[message!tq]"

##################################################################################################################################################################################################################
########################################################### CHAPTER 9 LENA #################################################################################################################################################################################
##################################################################################################################################################################################################################

label v9_lenastart:

    $ save_name = "Lena: Chapter 9"
    if v8_stalkfap_dm2:
        $ stalkfap_pro = 2
    $ stan_look = 1
    $ robert_look = 1
    $ seymour_look = 1
    $ lena_active = True
    $ ian_active = False
    scene blackbg with long
    show active_lena with long
    pause 1.0
    call calendar(_day="Friday") from _call_calendar_15

    $ v9miketalka = False # tracking var
    $ v9miketalkb = False
## IAN IS ABSENT ############################################################################################################################################################################################################################## ####################################################################################################################################################
    if v9_alison_trip:
        $ v9luggageholly = False    # tracking var
        $ v9luggagelouise = False
        $ v9luggageivy = False
        $ flena = "worried"
        $ lena_look = 4
        play sound "sfx/train.mp3"
        scene street with long
        "The train finally arrived at its destination."
        play music "music/normal_day2.mp3" loop
        show lena with short
        "I got off dragging my luggage behind. My parents wouldn't let me leave without taking some old and useless stuff with me."
        "Old clothes, new bed sheets, and even a damn duvet!"
        l "I don't know why I let them convince me..."
        l "My plan was to ask Ian to help me with these, but he's away on a trip..."
        if ian_lena_dating:
            if lena_ian_love:
                $ flena = "sad"
                l "I was looking forward to seeing him... It's been almost two weeks since I left."
                l "It's pretty obvious there's something between him and Alison. I have met most of his friends but her..."
                "It made sense Ian was seeing other girls: he was a single, attractive guy."
                if lena_robert_dating or lena_mike_dating or v8_jeremy_sex or (lena_louise_sex and lena_reject_louise == False):
                    "And I had also been dating around myself..."
                $ flena = "blush"
                "I was worried I was getting too hung up on Ian for my own good."
                l "Maybe I should have {i}the talk{/i} with him so I know where we stand..."
            else:
                $ flena = "n"
                l "I guess I'm not the only girl he's dating these days... It's not surprising though, he's a single, attractive guy."
                if lena_robert_dating or lena_mike_dating or v8_jeremy_sex or (lena_louise_sex and lena_reject_louise == False):
                    l "Well, it's not like I'm idle, either. I also have my hookups..."
                else:
                    l "I'll see him tomorrow anyway."
            $ flena = "n"
            "I dragged my luggage out of the station."
            l "Ugh, I don't want to carry this home on my own."
        else:
            $ flena = "n"
            l "I don't want to carry this luggage home on my own."
        l "Who could I ask for help?"
        menu v9luggage:
            "Holly" if v9luggageholly == False:
                $ renpy.block_rollback()
                $ v9luggageholly = True
                $ flena = "sad"
                l "I already talked with Holly and she told me she had to attend another fair this weekend."
                l "Her next book is about to get published and she's very busy..."
                $ flena = "n"
                l "I'll see her soon, though."
                jump v9luggage

            "Louise" if v9luggagelouise == False:
                $ renpy.block_rollback()
                $ v9luggagelouise = True
                l "Maybe Louise would be willing to help me."
                hide lena
                show lena_phone
                with short
                l "..."
                if louise_jeremy:
                    show phone_louise at lef3 with short
                    lo "Hey, Lena... Have you arrived already?"
                else:
                    show phone_louise_smile at lef3 with short
                    lo "Hi, Lena! Are you home already?"
                l "Just got off the train. I'm carrying a lot of luggage, thanks to my parents..."
                l "What are you doing? Can you come and get me at the station and give me a hand?"
                hide phone_louise
                hide phone_louise_smile
                show phone_louise_sad at lef3
                lo "I'm on campus right now... I'll be done in about one hour or so..."
                $ flena = "sad"
                l "I see. Don't worry, I'll manage!"
                if louise_jeremy:
                    l "Okay... See you later."
                else:
                    hide phone_louise_sad
                    show phone_louise_happy at lef3
                    lo "Okay! See you later at home!"
                hide phone_louise_sad
                hide phone_louise_happy
                hide lena_phone
                show lena
                with short
                jump v9luggage

            "Ivy" if v9luggageivy == False:
                $ renpy.block_rollback()
                $ v9luggageivy = True
                l "I'll give Ivy a call."
                hide lena
                show lena_phone
                with short
                l "..."
                l "... ..."
                l "... ... ..."
                $ flena = "sad"
                l "She's not picking up."
                hide lena_phone
                show lena
                with short
                $ flena = "n"
                jump v9luggage

            "{image=icon_friend.webp}Stan" if lena_stan > 5:
                $ renpy.block_rollback()
                $ v9_luggage = "stan"
                l "Let's see if Stan is willing to lend me a hand."
                hide lena
                show lena_phone
                with short
                l "..."
                show phone_stan at lef3 with short
                st "Yes? Lena?"
                l "Hey, Stan! Just got off the train, and I need some help to carry my luggage home."
                st "Say no more. I'll be there in a second...!"
                hide phone_stan with short
                $ flena = "smile"
                l "He hung up already!"
                hide lena_phone
                show lena
                with short
                l "That took much less convincing than I thought it would."
                if lena_charisma < 9:
                    call xp_up('charisma') from _call_xp_up_99
                "I waited for Stan in front of the station."

            "{image=icon_love.webp}Robert" if lena_robert_dating:
                $ renpy.block_rollback()
                $ v9_luggage = "robert"
                l "Robert will be perfect for this task."
                hide lena
                show lena_phone
                with short
                l "..."
                show phone_robert_smile at lef3 with short
                r "Lena! Are you back?"
                l "Yeah, just got off the train and I need someone to help me with my luggage."
                r "I'll be there in a moment!"
                l "Cool, I'll wait for you in front of the station."
                hide lena_phone
                show lena
                hide phone_robert_smile
                with short
                l "That was easy."
                if lena_charisma < 9:
                    call xp_up('charisma') from _call_xp_up_100
                "Robert didn't keep me waiting for long."

            "{image=icon_love.webp}Mike" if lena_mike_love:
                $ renpy.block_rollback()
                $ v9_luggage = "mike"
                $ flena = "flirtshy"
                l "Maybe I could ask Mike..."
                "I was about to call him but decided to text him instead."
                l "Maybe he's with his girlfriend right now, who knows."
                nvl clear
                l_p "{i}Hey Mike, are you free right now? Just got off the train and I need someone to help me with my luggage... {image=emoji_roll.webp} {image=emoji_tongue.webp}{/i}"
                "I was hoping he'd answer, and he did."
                play sound "sfx/sms.mp3"
                mk_p "{i}What's up, babe? I can lend a hand, where are you?{/i}"
                $ flena = "happy"
                l "Awesome."
                if lena_lust < 9:
                    call xp_up('lust') from _call_xp_up_101
                l_p "{i}I'm in front of the station. I'll wait for you here {image=emoji_kiss.webp} {/i}"
                mk_p "{i}On my way {image=emoji_glasses.webp}{/i}"
                $ flena = "smile"
                "I didn't have to wait for long."

            "Do it on your own":
                $ renpy.block_rollback()
                if v9luggageivy and v9luggageholly and v9luggagelouise:
                    $ flena = "worried"
                    l "Seems like nobody's available..."
                    $ flena = "sad"
                    l "Jeez, I guess I'll have to do it on my own."
                else:
                    $ flena = "sad"
                    l "Whatever... I'll just do it on my own."

    ## STAN ################################################################################################
        if v9_luggage == "stan":
            $ fstan = "smile"
            show lena at rig with move
            show stan at lef with short
            st "Hey, Lena!"
            $ flena = "smile"
            l "Thank you for coming!"
            st "Don't mention it... That's what roommates are for."
            scene street2
            show lena at rig
            show stan at lef
            with long
            "Stan helped me with the baggage and we started walking."
            st "How was your trip home?"
            $ flena = "n"
            l "Not the best, given the circumstances..."
            $ fstan = "sad"
            st "I can imagine... How's your Mom?"
            st "Louise only told me she was in the hospital, and I didn't want to be bothersome by texting or calling you to ask..."
            menu:
                "You don't bother me":
                    $ renpy.block_rollback()
                    $ flena = "smile"
                    l "You wouldn't have bothered me, Stan."
                    $ fstan = "blush"
                    st "Really? I've never texted you or anything just in case..."
                    l "You can text me whenever you want, but we live together, so there's no real need for that!"
                    st "That's right..."
                    l "What I mean is that you can talk to me whenever you want, on the phone or at home."
                    $ fstan = "smile"
                    st "Cool, got it."
                    if lena_stan < 12:
                        call friend_xp('stan', 1) from _call_friend_xp_136
                    $ fstan = "n"
                    st "So... How's she?"
                    l "My Mom? She's alright... Well, as alright as someone with anemia and a broken hip can be, I guess."
                    $ fstan = "sad"
                    st "I didn't know it was that serious..."
                    l "She'll survive. It's just gonna be tough for a few months..."

                "She's alright":
                    $ renpy.block_rollback()
                    l "She's alright... Well, as alright as someone with anemia and a broken hip can be, I guess."
                    $ fstan = "sad"
                    st "I didn't know it was that serious..."
                    l "She'll survive. It's just gonna be tough for a few months..."

                "I'm tired of talking about it":
                    $ renpy.block_rollback()
                    $ flena = "sad"
                    l "I don't really want to get into it if you don't mind. I've been going over the same stuff every day this week..."
                    $ fstan = "sad"
                    st "Yes, of course...! Sorry to have bothered you."
                    call friend_xp('stan', -1) from _call_friend_xp_137

            play sound "sfx/door_home.mp3"
            scene lenahome with long
            $ flena = "smile"
            $ fstan = "n"
            "We finally arrived at the apartment."
            show lena at rig
            show stan at lef
            with short
            l "Feels good to be back home... I've missed it!"
            $ fstan = "smile"
            st "We've missed you too..."
            show lena at rig3
            show stan at lef3
            with move
            play sound "sfx/meow.mp3"
            show lola_b with short
            $ flena = "happy"
            "Lola jumped from the couch to greet me, rubbing her head on my legs, tail up."
            l "Hey there, baby girl!"
            $ fstan = "happy"
            st "And she missed you the most!"
            l "Really? How has she been in my absence?"
            st "I've been taking care of her, making sure her bowl was full and all that..."
            $ flena = "sad"
            l "And... How have things with Louise been in my absence?"
            $ fstan = "n"
            st "Calm... I try to steer clear of her and she does the same."
            l "I'm sorry she's giving you such a hard time..."
            st "Hey, it's not your fault... It sucks, but I'm used to it."
            l "You're used to it?"
            $ fstan = "sad"
            st "To not being liked, I mean. Most people I've met in my life disliked me for some reason..."
            st "I guess I'm just weird like that."
            $ flena = "smile"
            if lena_stan > 7:
                l "Screw them. I like you."
                $ fstan = "blush"
                st "I, um, thanks..."
            else:
                l "Screw them. You're a nice guy, Stan."
                st "Um, thanks..."
            l "Alright. I'll go unpack and get settled. Thanks again for your help."
            $ fstan = "smile"
            st "It was my pleasure."
    ## ROBERT ################################################################################################
        elif v9_luggage == "robert":
            $ frobert = "flirt"
            $ fstan = "worried"
            show lena at rig with move
            show robert at lef with short
            "He parked his car in front of the station and got out to greet me and help me with the bags."
            r "Hey there, baby! I missed you!"
            $ flena = "smile"
            l "I didn't know you had a car."
            r "Yeah, I bought it a year ago, brand new!"
            l "Cool."
            r "Hop on in!"
            play sound "sfx/car.mp3"
            scene street with long
            "Robert loaded my luggage in the car and drove me to my place. How convenient!"
            "Deciding to keep him around really had its perks."
            scene lenahome with long
            $ flena = "n"
            $ frobert = "smile"
            play sound "sfx/door_home.mp3"
            show lena at rig
            show robert at lef
            with short
            l "Feels good to be back home..."
            show lena at rig3
            show robert at lef3
            with move
            play sound "sfx/meow.mp3"
            show lola_b at lef with short
            $ flena = "happy"
            "Lola jumped from the couch to greet me, her tail up."
            l "Hey there, baby girl. Did you miss me?"
            "I was about to pet her, but Lola stopped just out of hand's reach, sniffing Robert."
            play sound "sfx/cat_angry.mp3"
            hide lola_b
            show lolamad_b
            with vpunch
            $ flena = "worried"
            $ frobert = "sad"
            "She hissed at him and left, jumping out of sight."
            call friend_xp('lola', -1) from _call_friend_xp_138
            hide lolamad_b with short
            r "Damn, your cat is really gruff!"
            $ flena = "sad"
            l "She has a problem with guys... Maybe she's jealous."
            $ frobert = "flirt"
            r "She should be..."
            show robert at truecenter with move
            $ flena = "n"
            if lena_mike_love:
                play sound "sfx/sms.mp3"
                show lena at rig4 with move
                l "Oh, wait a second. I got a text..."
                $ frobert = "sad"
                $ flena = "flirtshy"
                "It was from Mike..."
                $ frobert = "n"
                nvl clear
                mk_p "{i}Hey babe, how are you doing? Are you back in town already?{/i}"
                l "Wait, let me answer this..."
                l_p "{i}Yeah, just got back! Why, did you miss me? {image=emoji_crazy.webp}{/i}"
                mk_p "{i}You have no idea. Are you busy right now? {image=emoji_flirt.webp}{/i}"
                l_p "{i}Not really. Do you wanna come over?{/i}"
                mk_p "{i}I'll be there in twenty minutes {image=emoji_glasses.webp}{/i}"
                show robert at lef
                show lena at rig
                with move
                $ flena = "n"
                l "Sorry, you need to go. Something came up."
                $ frobert = "sad"
                r "But I was hoping that..."
                l "Thanks for helping me today! Bye!"
                play sound "sfx/door_slam.mp3"
                hide robert with vpunch
                $ flena = "flirt"
                l "Mike wants to see me...!"
                play sound "sfx/door.mp3"
                show stan at lef with short
                $ flena = "n"
            else:
                "Robert moved closer to me, wrapping his arms around my waist, pulling me toward him..."
                r "You have no idea how much I've missed you..."
                play sound "sfx/door.mp3"
                show stan at lef3 with short
                $ frobert = "serious"
            st "Uh, hello, Lena..."
            st "I didn't know you were coming back today..."
            if lena_stan < 4:
                $ flena = "serious"
                l "Hi, Stan."
                st "..."
                st "I'll let you get settled."
                hide stan with short
                if lena_mike_love:
                    l "I almost forgot about him..."
                    hide stan with short
                else:
                    $ frobert = "n"
                    r "Your roommate?"
                    l "Yeah. I almost forgot about him..."
            else:
                $ flena = "n"
                l "Hi, Stan. How has Lola been in my absence?"
                st "I've been taking care of her, making sure her bowl was full and all that..."
                if lena_mike_love:
                    l "Thank you. Any news I should know?"
                    st "No, everything's been mostly the same..."
                    l "Alright. I'll go unpack and get settled."
                    hide stan with short
                else:
                    l "Thank you! Any other news?"
                    st "No, everything's been mostly the same... Anyway, don't let me bother you..."
                    hide stan with short
                    $ frobert = "n"
                    r "Your roommate?"
                    l "Yeah. He's shy and kind of awkward, but he's alright."
            if lena_mike_love:
                $ fmike = "smile"
                "I waited for Mike to arrive."
                play sound "sfx/doorbell.mp3"
                $ flena = "flirtshy"
                l "Here he is!"
                show lena at rig with move
                show mike at lef with short
                l "Hi...!"
                mk "Hey, babe. It's good to see you... How have you been?"
                $ flena = "n"
                l "Oh, well... I just came back from my parent's home. I had to go back for a couple of weeks..."
                mk "Oh, so that's where you've been. Family business?"
                $ flena = "sad"
                l "Well, yeah... Let's talk in my room."
                play sound "sfx/door.mp3"
                scene lenaroom with long
                $ fmike = "sad"
                "I told Mike about my mom's accident and the current situation."
                show lena at rig
                show mike at lef
                with short
                mk "Damn, that sucks. You didn't tell me anything."
                l "I didn't want to bother you..."
                $ fmike = "n"
                mk "Hey, it's no bother. There's not much I can do, but still..."
                $ flena = "smile"
                l "Well, you can help me with my luggage! I need to unpack all this useless stuff my mom forced me to bring."
                mk "Sure."
                jump v9mikeunpack
            else:
                $ frobert = "flirt"
                $ flena = "n"
                r "So, where were we...?"
                menu:
                    "Have sex with Robert":
                        label gallery_CH09_S08:
                            if _in_replay:
                                call setup_CH09_S08 from _call_setup_CH09_S08
                        $ renpy.block_rollback()
                        $ v9_luggage_sex = "robert"
                        $ flena = "slut"
                        l "You were about to fuck my brains out if I'm not mistaken."
                        stop music fadeout 2.0
                        r "That's exactly what I'm gonna do."
                        play music "music/sex.mp3" loop
                        scene v9_robert1
                        if lena_tattoo2:
                            show v9_mike1_t2
                        with long
                        "Robert couldn't wait to stick his cock inside me, and that suited me just fine."
                        play sound "sfx/ah5.mp3"
                        l "Oh, fuck yeah...!"
                        "These two awful long weeks away had been putting so much pressure on me. I had been feeling stressed and isolated."
                        "I was in so much need to release that tension... And nothing better for that than a good, rough fuck!"
                        "Robert, as always, was more than happy to help me scratch that itch."
                        scene v9_robert2
                        with long
                        "I helped and encouraged his efforts by playing with my clit while he penetrated me tirelessly."
                        r "You're so damn hot, Lena...! You turn me on like fucking crazy!"
                        l "Keep fucking me... Harder! Yes, just like that!"
                        "I was glad to see we hadn't lost our compatibility despite not seeing each other for some time."
                        "It had taken a few tries, but Robert had finally found the right way to please me... And I was contributing to that with my own fingers, too."
                        if lena_axel_desire:
                            "Being on all fours for him turned me on so much. Robert had me at his entire disposition, using my body as he pleased."
                            "Just like Axel used to do..."
                        else:
                            "He had me on all fours, enjoying myself and at his entire disposition."
                        l "Oh, fuck, I'm gonna cum!"
                        r "Yes, baby! Cum for me!"
                        play sound "sfx/ah6.mp3"
                        l "Mhhhhaaaahh!!" with vpunch
                        pause 0.6
                        with vpunch
                        pause 0.6
                        with vpunch
                        pause 0.6
                        "We kept going at it for a bit more until it became dark outside."
                        "We certainly made up for the lost time."
                        stop music fadeout 2.0
                        scene lenaroomnight with long
                        $ flena = "smile"
                        $ frobert = "flirt"
                        show lenanude2 at rig
                        show robertnude at lef
                        with short
                        l "Mmmhh... That was good."
                        r "It was incredible, as it always is with you..."
                        r "Damn, I can't get enough of you!"
                        l "Let's save it for another day, okay? It's getting late and I still need to unpack and get settled."
                        $ frobert = "sad"
                        r "Oh, yeah..."
                        $ frobert = "n"
                        r "Alright. Let's meet again soon, okay?"
                        l "I'll give you a call."
                        $ renpy.end_replay()
                        $ gallery_unlock_scene("CH09_S08")

                    "Kick him out":
                        $ renpy.block_rollback()
                        l "We're about done! Thanks for your help with the bags, Robert."
                        $ frobert = "sad"
                        l "Now, if you don't mind, I have to unpack everything and get settled."
                        r "Yeah, but..."
                        l "Let's catch up another time, alright? Bye!"
                        play sound "sfx/door_slam.mp3"
                        hide robert with vpunch

    ## MIKE ################################################################################################
        elif v9_luggage == "mike":
            $ fmike = "n"
            show lena at rig with move
            show mike at lef with short
            mk "Hey."
            $ flena = "flirtshy"
            l "Hi..."
            mk "Long time no see. How have you been?"
            $ flena = "n"
            l "Oh, well... I just came back from my parents' home. I had to go back for a couple of weeks..."
            mk "Oh, so that's where you've been. Family business?"
            $ flena = "sad"
            l "Well, yeah..."
            scene street2 with long
            "I told Mike about my mom's accident and the current situation as we made our way to the apartment."
            show lena at rig
            show mike at lef
            with short
            $ fmike = "sad"
            mk "Damn, that sucks. You didn't tell me anything."
            l "I didn't want to bother you..."
            $ fmike = "n"
            mk "Hey, it's no bother. There's not much I can do, but still..."
            $ flena = "smile"
            l "Well, you can help me with my luggage!"
            mk "Right, that's something, isn't it?"
            $ flena = "shy"
            l "Yeah... Thanks a bunch."
            mk "Don't mention it. I was wondering when I would hear from you again..."
            l "You were?"
            mk "Is that surprising?"
            $ flena = "flirtshy"
            l "A bit, yeah... But I'm glad."
            l "Wanna come up?"
            mk "Sure."
            play sound "sfx/door_home.mp3"
            scene lenahome with long
            show lena at rig
            show mike at lef
            with short
            l "Feels good to be back home..."
            show lena at rig3
            show mike at lef3
            with move
            play sound "sfx/meow.mp3"
            show lola_b at lef with short
            $ flena = "happy"
            "Lola jumped from the couch to greet me, her tail up."
            l "Hey there, baby girl. Did you miss me?"
            "I was about to pet her, but Lola stopped just out of hand's reach, sniffing the air."
            "She looked in Mike's direction, doubted for a second, and turned tail"
            $ flena = "sad"
            hide lola_b with short
            $ fmike = "smile"
            mk "Seems she didn't miss you that much!"
            l "That hurt..."
            mk "Cats are like that, aren't they? That's why I always liked dogs more."
            $ fstan = "worried"
            play sound "sfx/door.mp3"
            show stan with short
            st "Uh, hello, Lena..."
            st "I didn't know you were coming back today..."
            if lena_stan < 4:
                $ flena = "serious"
                l "Hi, Stan."
                mk "What's up?"
                st "..."
                st "I'll let you get settled."
                hide stan with short
                $ flena = "n"
                mk "Your roommate?"
                l "Yeah. I almost forgot about him..."
            else:
                $ flena = "n"
                l "Hi, Stan. How has Lola been in my absence?"
                st "I've been taking care of her, making sure her bowl was full and all that..."
                l "Thank you! Any other news?"
                st "No, everything's been mostly the same... Anyway, don't let me bother you..."
                hide stan with short
                $ flena = "n"
                mk "Your roommate?"
                l "Yeah. He's shy and kind of awkward, but he's alright."
            $ flena = "smile"
            l "Come, let's go to my room."
            play sound "sfx/door.mp3"
            scene lenaroom
            show lena at rig
            show mike at lef
            with long
            "Mike helped me unpack my luggage."
            label v9mikeunpack:
                $ v9_luggage_sex = "mike"
                $ flena = "worried"
                $ fmike = "n"
            l "Where I'm supposed to store this? I should've told my mom to throw this away, instead, I let her convince me to take it home."
            mk "Your room is kinda small, yeah..."
            $ flena = "n"
            l "Where do you live, Mike? Is it nice?"
            mk "My girl and I rented a place on the other side of town. It's a bit far away from everything, but it's alright..."
            mk "Small, too, but affordable."
            menu v9miketalk:
                "Ask about Mike's job"  if v9miketalka == False:
                    $ renpy.block_rollback()
                    $ v9miketalka = True
                    $ flena = "n"
                    l "How's work, by the way? Does DJing pay the bills?"
                    $ fmike = "n"
                    mk "Not really... I hope it will at some point. I'm working on some tracks..."
                    $ flena = "smile"
                    l "I'd love to listen to them!"
                    mk "Sure, I'll show you one day."
                    $ flena = "n"
                    l "Making it in the music industry is really hard... I would know."
                    mk "Yeah, but someone has to make it, so why not be me? This gig at Blazer is the first step to making a name for myself..."
                    mk "I don't get paid much right now, but I will at some point. At least my girl has a stable job, so we can pay the bills!"
                    if v9miketalka and v9miketalkb:
                        call friend_xp('mike', 1) from _call_friend_xp_139
                    jump v9miketalk

                "Ask about Mike's girlfriend" if v9miketalkb == False:
                    $ renpy.block_rollback()
                    $ v9miketalkb = True
                    $ flena = "n"
                    l "So... How's it going with your girl?"
                    $ fmike = "n"
                    mk "It's okay..."
                    l "Just okay?"
                    mk "It's fine. It's my first time living together with my girlfriend, so I'm still getting used to it."
                    mk "She's pretty busy with her new job though, so she gives me plenty of space, which is nice. So far everything's been going alright."
                    l "How long have you been with her?"
                    mk "About a year... And we moved here like three months ago or so."
                    l "You look like a pretty independent guy. I still find it hard to believe you'd settle for just one girl."
                    $ fmike = "smile"
                    mk "Well, you're making that hard, as I'm sure you're aware!"
                    $ flena = "flirt"
                    l "What can I say? It's not fair for her to have you all for herself!"
                    $ fmike = "flirt"
                    mk "You little vixen..."
                    if v9miketalka and v9miketalkb:
                        call friend_xp('mike', 1) from _call_friend_xp_140
                    jump v9miketalk

                "Continue unpacking":
                    $ renpy.block_rollback()

            label gallery_CH09_S09:
                if _in_replay:
                    call setup_CH09_S09 from _call_setup_CH09_S09
            "We finished unpacking and storing everything. Thanks to Mike's help it didn't take too long."
            if lena_wardrobe_bunny:
                "Something inside my closet caught Mike's eye."
                mk "What's this? A sexy bunny costume?"
                $ flena = "shy"
                if v6_robert_date:
                    l "Oh, yeah... It was a gift."
                    mk "A gift, huh...? Interesting."
                    $ fmike = "flirt"
                    mk "I would love to see how you look in it."
                else:
                    l "Oh, yeah... I liked it, so I decided to buy it."
                    mk "I see. Have you used it yet?"
                    l "No, I haven't found the opportunity yet..."
                    $ fmike = "flirt"
                    mk "What about now?"
                $ flena = "flirtshy"
                l "What, right now?"
                mk "Yeah. Don't I deserve payment for my invaluable help today?"
                l "You know I can't say no to you..."
                stop music fadeout 2.0
                play music "music/dumb.mp3" loop
                hide lena
                show lenanude at rig
                with short
                "I stripped down under Mike's watchful eyes and put on the bunny costume."
                show lena_bunny at rig with long
                l "What do you think...?"
                mk "Hot damn."
                hide mike
                show miketopless at lef
                with short
                $ flena = "slut"
                "Mike took off his shirt and threw it aside. I could tell how fired up he was getting..."
                l "Does it suit me?"
                mk "You can't imagine the dirty things I'm thinking about doing to you right now, babe."
                l "Show me..."
            else:
                l "Whew, thanks a lot..."
                $ fmike = "flirt"
                mk "So, do I get some kind of payment for my invaluable help today?"
                $ flena = "flirtshy"
                l "What kind of payment are you willing to accept?"
                mk "You know what I want..."
                "I did. And I wanted it too."
                stop music fadeout 2.0
                play music "music/sex_raunchy.mp3" loop
                hide lena
                show lenanude2 at rig
                with short
                "I stripped down under Mike's watchful eyes, and he followed suit."
                hide mike
                show miketopless at lef
                with short
                $ flena = "slut"
            "I licked my lips lusciously as I ran my hands over Mike's ripped torso."
            l "You're so damn hot..."
            mk "Look who's talking..."
            hide miketopless
            show mikenude at lef
            with short
            mk "My cock's been like this for a while now."
            "I held his rock-hard shaft in my palm. Such a mouth-watering sensation..."
            "And that wasn't the only thing that was getting wet. I felt the warm moisture dripping between my thighs..."
            l "Fuck me, Mike...! I need you to fuck me so bad!"
            scene v9_mike1
            if lena_wardrobe_bunny:
                show v9_mike1_bunny
            if lena_tattoo2:
                show v9_mike1_t2
            with long
            play sound "sfx/ah5.mp3"
            l "Oh, fuck yeah...!"
            "This was just what I had been needing!"
            "These two awful long weeks away had been putting so much pressure on me. I had been feeling stressed and isolated."
            "I was in so much need to release that tension... And nothing better for that than a good, rough fuck!"
            "And no better partner for that than Mike... His way of banging me was like his personality:"
            "Direct and unapologetic, intense to the point of being overwhelming, but also playful and charming."
            scene v9_mike2
            if lena_wardrobe_bunny:
                show v9_mike2_bunny
            with long
            "The rhythm of his hips, fast and ruthless, was broken up by slick, slow pumps, only to suddenly return to loud, flesh-slapping pounding."
            "Those were the moves of a pro... I loved how he fucked me!"
            "And he was hot as hell, to boot. I couldn't help but start masturbating while he penetrated me tirelessly."
            if lena_axel_desire:
                "He had me on all fours, at his entire disposition, using my body as he pleased."
                "Just like Axel used to do..."
            else:
                "He had me on all fours, enjoying myself and at his entire disposition."
            l "Oh, fuck, I'm gonna cum!"
            if lena_anal > 1:
                mk "I want your ass, Lena!"
                if lena_wardrobe_bunny:
                    scene v9_mike3_bunny
                else:
                    scene v9_mike3
                with long
                "He shoved it in my rectum right away, without even taking the time to apply some lube to it."
                "He didn't need it."
                "My ass gulped down his entire shaft smoothly like it was a perfect sleeve for Mike's cock."
                mk "Fuck, your ass feels just like a pussy... It's awesome, Lena!"
                "It felt like that for me too. What intense pleasure I was feeling being ravaged by his hard, throbbing manhood!"
                if lena_lust < 9:
                    call xp_up('lust') from _call_xp_up_102
                l "Oh, fuck! I'm cumming from my ass!"
                play sound "sfx/ah4.mp3"
                l "Oh fuck, Mikeeeaahhh!!!{w=0.3}{nw}" with vpunch
                pause 0.6
                with vpunch
                pause 0.6
                with vpunch
                pause 0.6
            else:
                play sound "sfx/ah4.mp3"
                l "Aaahhhh!!!{w=0.3}{nw}" with vpunch
                pause 0.6
                with vpunch
                pause 0.6
                with vpunch
                pause 0.6
            "We kept going at it until it became dark and we were both sweaty, exhausted, and satisfied."
            "What a sex binge!"
            stop music fadeout 2.0
            scene lenaroomnight with long
            $ flena = "flirt"
            $ fmike = "smile"
            show lenanude2 at rig
            if lena_wardrobe_bunny:
                show lena_bunny at rig
            show mikenude at lef
            with short
            l "Mmmhh... That was amazing."
            mk "It really was. Having sex with you is the bomb."
            if lena_mike < 12:
                call friend_xp('mike', 1) from _call_friend_xp_141
            $ flena = "flirt"
            l "Right? We click together so nicely..."
            $ fmike = "n"
            mk "Wow, look at the time... I should get going."
            $ flena  ="sad"
            l "Sure..."
            mk "Do you mind if I take a quick shower before leaving?"
            $ flena = "n"
            l "Go ahead."
            $ renpy.end_replay()
            $ gallery_unlock_scene("CH09_S09")

    ## ALONE ################################################################################################
        else:
            scene street2 with long
            "The walk home was slow and burdensome. An awfully perfect ending to these two dreadful weeks..."
            "When I finally got home all I wanted was to take a shower and lay on my bed."
            $ flena = "drama"
            play sound "sfx/door_home.mp3"
            scene lenahome with long
            pause 0.5
            show lena with short
            if lena_athletics < 10:
                call xp_up('athletics') from _call_xp_up_103
            l "Whew... I'm home..."
            show lena at rig with move
            play sound "sfx/meow.mp3"
            show lola_b at lef with short
            $ flena = "smile"
            "Lola jumped from the couch to greet me, rubbing her head on my legs, tail up."
            l "Hey there, baby girl. Did you miss me?"
            play sound "sfx/purr.mp3"
            show lolahappy_b at lef with short
            hide lola_b
            l "At least someone's here to greet me..."
            $ flena = "sad"
            l "I didn't think my return would feel so lonely."
            "I hadn't been away that long, but I had left so many things hanging."
            "I had a lot to set up straight, starting with my financial situation. Now more than ever."
            show lena at rig3
            show lolahappy_b at truecenter
            with move
            $ fstan = "worried"
            play sound "sfx/door.mp3"
            show stan at lef3 with short
            st "Uh, hello, Lena..."
            st "I didn't know you were coming back today..."
            if lena_stan < 4:
                $ flena = "serious"
                l "Hi, Stan."
                st "..."
                st "I'll let you get settled."
                hide stan with short
                $ flena = "n"
                l "I almost forgot about him..."
            else:
                $ flena = "n"
                l "Hi, Stan. How has Lola been in my absence?"
                st "I've been taking care of her, making sure her bowl was full and all that..."
                l "Thank you. Any news I should know?"
                st "No, everything's been mostly the same..."
                l "Alright. I'll go unpack and get settled."
    ## ALL
        scene lenaroomnight with long
        pause 0.5
        play sound "sfx/shower.mp3"
        scene v1_lena_shower
        if lena_piercing1:
            show v1_lena_shower_p1
        if lena_piercing2:
            show v1_lena_shower_p2
        if lena_tattoo2:
            show v1_lena_shower_t2
        if lena_tattoo3:
            show v1_lena_shower_t3
        with long
        pause 1
        if v9_luggage_sex == "mike":
            "After Mike left I took a long, relaxing shower myself. That amazing sex had me feeling light-headed and happy..."
            "I was so glad Mike had shown up today. He was just what I had been needing."
            "But now I had to sit down and plan the coming days..."
            play music "music/normal_day2.mp3" loop
        elif v9_luggage_sex == "robert":
            "Robert left and I took a long, relaxing shower. That, coupled with the sex, was just what I had been needing."
            "But now I had to sit down and plan the coming days..."
            play music "music/normal_day2.mp3" loop
        else:
            "After unpacking all my luggage I took a long shower and went to my room to plan the coming days."
        $ flena = "n"
        $ lena_look = "towel"
        scene lenaroomnight with long
        pause 0.5
        show lena with short
        if ian_lena_dating:
            l "Tomorrow I'll be meeting Ian for lunch, and hopefully something more..."
        else:
            l "I guess tomorrow I'm meeting Ian for lunch..."

## IAN GREETS LENA #################################################################################################################################################### ####################################################################################################################################################
    else:
        $ flena = "n"
        $ fian = "smile"
        $ ian_look = 2
        if lena_lust > 6 and ian_lena_dating:
            $ lena_look = "sexy"
        else:
            $ lena_look = 4
        play sound "sfx/train.mp3"
        scene street with long
        "The train finally arrived at its destination."
        if ian_lena_dating == False:
            play music "music/normal_day2.mp3" loop
        show lena with short
        "I got off dragging my luggage behind. Ian was already waiting for me there."
        if ian_lena_dating:
            play music "music/shine_again1.mp3" loop
        show lena at rig with move
        show ian at lef with short
        if lena_ian_love:
            $ flena = "happy"
            "Seeing him immediately put a smile on my face."
            "I had been looking forward to seeing him again..."
        elif ian_lena_dating:
            $ flena = "smile"
            "I had been looking forward to seeing him again..."
        i "Hey! What are you hauling there?"
        $ flena = "worried"
        if ian_lena_dating:
            l "Ugh, my  parents wouldn't let me leave without taking some old and useless stuff with me."
            l "Old clothes, new bed sheets, and even a damn duvet!"
        else:
            l "My parents insisted on taking with me a lot of useless stuff: old clothes, new bed sheets, and even a damn duvet!"
        $ fian = "happy"
        i "A duvet? It's almost July!"
        l "I know! They even wanted me to pack a dinnerware set, but I refused. A small victory."
        if v9lenagreetkiss:
            i "I'm so glad to see you."
            if lena_look == 4:
                scene v9_lena1a
            else:
                scene v9_lena1b
            if lena_tattoo2:
                show v9_lena1_t2
            with long
            "I was wondering how long it would take for him to kiss me. Thankfully, not long at all."
            if lena_ian_love:
                "I realized how much I had been wanting to kiss him during these two lonely weeks..."
            "This was exactly the greeting I was expecting..."
            if lena_ian_love or lena_lust > 6:
                if lena_look == 4:
                    scene v9_lena2a
                else:
                    scene v9_lena2b
                if lena_tattoo2:
                    show v9_lena1_t2
                with long
                "With my eyes closed, I let myself get caught up in the warm, fuzzy sensation of being reunited with Ian."
                "I responded to his kiss with a deeper one, taking my time to enjoy the sensation of his tongue dancing with mine."
                "I pressed my body against his chest, taking in his aroma, his taste, his embrace..."
                if lena_ian_love:
                    "Kissing him really felt special..."
                "I decided to stop before getting hornier than I already was!"
                $ fian = "smile"
                $ flena = "shy"
                scene street
                show ian at lef
                show lena2 at rig
                with short
                i "Welcome back."
                l "Now that's how you greet someone..."
                "I couldn't help but notice the bulge under Ian's pants. I wasn't the only one aroused by that kiss!"
            else:
                "I was about to get my tongue involved, but he pulled away."
                "Maybe he was a bit shy..."
                $ fian = "smile"
                $ flena = "smile"
                scene street
                show ian at lef
                show lena at rig
                with short
                i "Welcome back."
                l "Thanks."
            i "Here, let me help you with your luggage."
        else:
            "We made a bit of small talk before Ian offered to help me with my luggage and start heading home."
        if v9lenagreetkiss == False and lena_ian_love and ian_lena_dating:
            "It felt too cold. I wanted at least to taste his lips!"
            $ flena = "shy"
            l "Wait... Where's my welcome kiss?"
            $ fian = "shy"
            i "Oh...! Of course..."
            if lena_look == 4:
                scene v9_lena1a
            else:
                scene v9_lena1b
            if lena_tattoo2:
                show v9_lena1_t2
            with long
            "This was exactly the greeting I was expecting..."
            "After these two lonely weeks, finally feeling Ian's body so close was even better than usual..."
            "I was about to get my tongue involved, but he pulled away."
            "Was he really this shy, or...?"
            scene street
            show ian at lef
            show lena2 at rig
            with short
            l "Okay... Now we can go."
        if cafe_help:
            l "Do you mind if we stop by the café while we're on our way? I'm starving and I want to check up on Molly and Ed."
            i "Sure. Let's go."
        else:
            l "Do you mind if we stop for a bite while we're on our way? I'm starving!"
            i "Sure, we can drop by the café if you want."
            l "Sure, let's go."
        scene cafe with long
        $ flena = "n"
        $ fian = "smile"
        $ fed = "n"
        show lena at rig3
        show ed
        show ian at lef3
        with short
        "We made a stop at the café and greeted Ed. Molly was absent, again..."
        label v9iandate2:
            $ fperry = "smile"
        if cafe_perry:
            show lena at right
            show ed at rig
            show ian at left
            with move
            $ flena = "happy"
            show perry at lef with short
            "Surprisingly, though, Perry was there! Looks like Ian convinced him to lend a hand..."
            "They were both so nice...! That really put a smile on my face."
        hide ed
        hide perry
        with short
        show ian at lef
        show lena at rig
        with move
        if v5_ian_showup:
            $ flena = "smile"
            "There, Ian told me about the good news about his new job, and about how his book was coming along."
        else:
            $ flena = "n"
            $ fian = "n"
            "There, we exchanged news and Ian told me about how his book was coming along."
        if v9_ian_family > 0:
            $ flena = "sad"
            $ fian = "sad"
            "I talked to him for the first time about my family and my relationship with my dad..."
            if v9_ian_family > 1:
                $ fian = "n"
                "He also opened up about his family, sharing some things he had never shared with me before."
            if v9_ian_family > 2:
                "His relationship with both his mom and dad seemed to be complicated... Just like mine."
        else:
            $ flena = "n"
            "We didn't stay long, just enough to grab a bite."
        ## dating ###########################################################################################################################
        if ian_lena_dating or ian_lena_breakup:
            stop music fadeout 2.0
            scene park with long
            $ flena = "n"
            $ fian = "smile"
            "Then, we decided to cross through the park and ended up sitting down in front of the river..."
            show lena at rig
            show ian at lef
            with short
            "It was nice being back, spending time with Ian again... Feeling like I was getting back in the flow of my own life."
            $ flena = "blush"
            "I wasn't planning on it, but one thing led to another, and we ended up having {i}the talk{/i}."
            $ fian = "n"
            "Truth be told, I had been thinking about it for quite some time now, and I felt this was the right time to bring it up."
            l "Seems like we're both in a, let's say... complex moment, emotionally speaking."
            l "So I think it'd be better if we put our cards on the table and discuss things like, you know... adults."
            l "I'm not too good at this myself, but I know my last relationship ended horribly because of the lack of communication and honesty."
            l "I'm trying to change that, so..."
            $ flena = "n"
            l "I'd appreciate it if you honestly told me how you feel about this."
            # couple
            if ian_lena_love:
                if ian_lena_love == 2:
                    $ ian_lena_love = True
                    i "The truth is, up until now, I've been trying to see this as something casual."
                    i "I told myself we were just experimenting together, and that might be true..."
                    i "But if I listen to what I really feel..."
                    i "I think I'd like to be in a relationship with you."
                else:
                    i "I've been thinking this over for some time, and while it's true I still have some conflicted feelings about it..."
                    i "The truth is I'd like to be in a relationship with you."
                    i "That's how I feel. How I've been feeling for a while now..."
                # couple
                if ian_lena_couple:
                    play music "music/ourredstring.mp3" loop
                    "I felt my heart swell in my chest. Hearing Ian's words made me feel reassured, excited, scared, and happy all at the same time."
                    $ flena = "shy"
                    "He felt the same way I did."
                    l "Actually, I've been feeling the same..."
                    $ fian = "shy"
                    i "Really?"
                    l "Yeah, I mean..."
                    $ flena = "blush"
                    l "I really like you. You're the first person I've liked in this way for a long time."
                    "I could feel we both were a bit afraid of jumping into a relationship once again... But Ian was worth the try."
                    "I was really taking this step with him...!"
                # reject
                else:
                    $ flena = "worried"
                    "I couldn't accept his feelings."
                    $ fian = "sad"
                    if lena_ian_love:
                        "A part of me wanted to. I was scared of jumping into a relationship again, but Ian was worth the try..."
                        $ flena = "blush"
                        "However, I couldn't do it. Not after what had been going on between me and Jeremy."
                        "He was Ian's friend, and Louise's boyfriend. What would happen if they learned about my misdeeds?"
                        "I just couldn't...!"
                    else:
                        "I liked Ian, but it was too soon for me to even think about getting into a serious relationship."
                        "I wasn't sure that was what I wanted, and I had to be honest with myself."
                    $ flena = "sad"
                    l "I'm sorry... But I just can't get into a serious relationship at this moment. It wouldn't be fair to you."
                    $ fian = "n"
                    i "..."
                    i "I understand."
                    if ian_lena_breakup:
                        "This changed things. Ian told me he couldn't keep seeing me like he had been doing thus far."
                        "He needed some distance."
                        i "I hope we can still hang out together and all that..."
                        l "Yeah, me too. First and foremost I consider you my friend."
                        $ fian = "smile"
                        i "Me too."
                        $ flena = "sad"
                        "This wasn't what I was expecting, and I wasn't exactly happy about it... But I had to admire Ian's honesty with his feelings."
                        $ flena = "n"
                        "He was choosing the mature thing to do, and I respected that."
                    else:
                        "I didn't want to lose him, though. I really enjoyed having Ian in my life, even if it was just as a friend with benefits."
                        "He agreed to keep things like they were, even though I couldn't accept his feelings."
                        "Was that the right thing to do, I wondered...?"
                    if v9_lena_sex > 0:
                        if lena_look == 4:
                            scene v9_lena2aa
                        else:
                            scene v9_lena2bb
                        if lena_tattoo2:
                            show v9_lena1_t2
                        with long
                        "Still, he said he wanted us to keep seeing each other."
                        "I wanted it too."
                        jump v9sexretrospective
                    elif v9_alison_trip:
                        jump v9iandate3
                    else:
                        jump v9arrivehome
            # dating
            else:
                i "Well, as you said, we're both in a complex moment emotionally."
                i "You've become someone quite special to me, but..."
                i "I'm just not ready for another relationship at this point."
                if lena_ian_love:
                    $ flena = "blush"
                    l "Oh."
                    $ flena = "sad"
                    l "Yeah, of course. I thought as much."
                    "I tried not to show my disappointment."
                    "A part of me thought this was for the best. I had even been expecting it."
                    "But another part had been hopeful."
                    "I was scared about getting into another relationship, but I felt Ian was worth the try..."
                    "Sadly, he didn't seem to think the same."
                    "I forced myself to block out my feelings of disillusionment. I would process them later, on my own."
                else:
                    $ flena = "sad"
                    l "I see...!"
                    $ flena = "smile"
                    l "Well, I'm glad we see things the same way."
                    $ fian = "smile"
                    "I felt a load lift off my shoulders. This made everything easier, didn't it...?"
                    "I really liked Ian, and I enjoyed having him in my life..."
                    "He seemed like boyfriend material, in all honesty. But I wasn't ready to jump into another relationship."
                    "Not just yet, at least."
                    "This way we could keep moving forward, no strings attached, and see what the future had in store for us, whatever it was."
            if (v9_alison_trip and lena_axel_dating) or (v9_alison_trip and v9_cindy_shoot):
                jump v9iandate3
            if ian_lena_couple == False:
                play music "music/sensual.mp3" loop
            if lena_look == 4:
                scene v9_lena2aa
            else:
                scene v9_lena2bb
            if lena_tattoo2:
                show v9_lena1_t2
            with long
            if ian_lena_couple:
                "We sealed the deal with kisses and caresses. They already felt different than before: more tender and intimate..."
            else:
                if lena_ian_love:
                    "I didn't want my negative feelings to get in the way of this moment. I wanted to enjoy Ian's company..."
                elif ian_lena_love:
                    "It didn't matter. Even if there were things we couldn't sort out, we still enjoyed each other's company."
                else:
                    "And I could enjoy Ian's company with a light heart, setting my passion free."
            if v9_lena_sex > 0:
                label v9sexretrospective:
                    if lena_look == 4:
                        scene v9_lena5a
                    else:
                        scene v9_lena5b
                    if lena_piercing1:
                        show v9_lena3_p1
                    elif lena_piercing2:
                        show v9_lena3_p2
                    if lena_tattoo1:
                        show v9_lena5_t1
                    if lena_tattoo2:
                        show v9_lena3_t2
                    if lena_tattoo3:
                        if lena_look == 4:
                            show v9_lena4_t3a
                        else:
                            show v9_lena4_t3b
                    with long
                "Our kisses quickly escalated to something else."
                "Ian couldn't get his hands off me, and I was more than happy to let him explore my body... even if we were in the middle of the park."
                if lena_fty_show:
                    "It was so exciting...! The danger of being caught had my adrenaline rushing, and I gave in to lust and pleasure."
                else:
                    "Despite the fear that somebody might see us, we gave in to lust, and I to pleasure."
            if v9_lena_sex > 1:
                if lena_look == 4:
                    scene v9_lena8a
                else:
                    scene v9_lena8b
                if lena_tattoo2:
                    show v9_lena8_t2
                if lena_tattoo3:
                    show v9_lena8_t3
                with long
                "We ended up going all the way."
                "I couldn't refrain from my desire for Ian. I didn't want to."
            if v9_lena_sex == 4:
                if lena_look == 4:
                    scene v9_lena9a_animation
                else:
                    scene v9_lena9b_animation
                with short
                play sound "sfx/ah6.mp3"
                "I had been longing to feel Ian inside of me. To be penetrated by his stiff, thick manhood."
                "It filled me up so well...! Something about it made it the perfect match for me."
                "I couldn't stop bouncing on top of it, even when he warned me that someone had spotted us."
                if lena_fty_show:
                    "I didn't care. It turned me on even more, in fact."
                else:
                    "Later I would be embarrassed about it, but at that moment I didn't even care."
                if lena_look == 4:
                    scene v9_lena8a
                else:
                    scene v9_lena8b
                if lena_tattoo2:
                    show v9_lena8_t2
                if lena_tattoo3:
                    show v9_lena8_t3
                show v9_lena8_peep
                with long
                "Ian gifted me such a wonderful orgasm...!"
                "It was such an exciting and naughty experience!"
                if lena_lust < 9:
                    call xp_up('lust') from _call_xp_up_104
            elif v9_lena_sex == 3:
                "I had been longing to feel Ian inside of me. To be penetrated by his stiff, thick manhood."
                show v9_lena8_peep with long
                "Too bad we were interrupted by a peeping tom...!"
                $ flena  = "blush"
                $ fian = "worried"
                scene parknight
                show lena at rig
                show ian at lef
                with long
                "I felt so ashamed... and frustrated!"
                "But these were the risks of getting it on in a public place like that..."
                "Still, it was quite an exciting experience..."
                if lena_lust < 5:
                    call xp_up('lust') from _call_xp_up_105
            elif v9_lena_sex == 2:
                "I had been longing to feel Ian inside of me. To be penetrated by his stiff, thick manhood."
                "It filled me up so well...! Something about it made it the perfect match for me."
                if lena_fty_show:
                    "And having sex in a public place turned me even more...! I ended up enjoying such a wonderful orgasm..."
                else:
                    "Despite my initial discomfort with the setting, I ended up enjoying such a wonderful orgasm..."
                "It was such an exciting and naughty experience..."
                if lena_lust < 6:
                    call xp_up('lust') from _call_xp_up_106
            elif v9_lena_sex == 1:
                "Ian's fingers gifted me a nice orgasm and I wish we could've done more, but that was as far as we dared to go being in the open like that."
                "Still, it was such an exciting and naughty experience..."
                if lena_lust < 5:
                    call xp_up('lust') from _call_xp_up_107
            else:
                if ian_lena_sex:
                    "I was getting really horny, but this wasn't the time or place to escalate things further."
                    "No matter. I knew we would share warm and passionate moments again soon."
                    "For tonight, kisses would need to suffice."
                else:
                    "I was getting so horny... To think Ian and I hadn't had sex yet...!"
                    "Maybe it was time for that to happen. It was weird we hadn't done it already."
                    "But this was neither the time nor place... So our arousal remained unsatisfied."
            stop music fadeout 2.0
            scene parknight with long
            if ian_lena_love and ian_lena_couple == False:
                "I wasn't gonna be his girlfriend, but at least we could still share these moments together..."
            else:
                "After that Ian walked me home and we said goodbye with a long, deep kiss."
            if v9_alison_trip:
                jump v9saturdayend
            play music "music/normal_day2.mp3" loop
            jump v9arrivehome

        ##friends ###########################################################################################################################
        else:
            $ flena = "n"
            $ fian = "smile"
            if ian_holly_dating:
                "I asked him about Holly. I was happy they had finally connected and I hoped their relationship flourished."
                "The only thing that worried me was the possibility of Ian being into Holly a lot less than she was into him, and the consequences that could cause..."
            elif v7_holly_kiss:
                "We also talked about the {i}situation{/i}. That weird triangle between Ian, Holly, and I."
                if ian_lena_makeup:
                    "This time, however, something felt different. Ian's words seemed really honest and forthcoming."
                    "We were starting to be at peace with it."
                elif ian_lena_over:
                    "It had been an uncomfortable affair, and thankfully we would put it behind us rather soon and keep being good friends..."
                    "So far it seemed it was working out."
                elif ian_holly_sex:
                    "It had been an uncomfortable affair, but maybe it would serve Holly to move on from her crush and start living life as she should."
                else:
                    "I felt like we were putting that behind us rather quickly, thankfully."
            if v9_alison_trip:
                jump v9iandate3
            scene street2 with long
            "We said goodbye at the door and I went inside."
            # get home scene
            label v9arrivehome:
                play sound "sfx/door_home.mp3"
            scene lenahomenight with long
            if ian_lena_couple:
                $ flena = "happy"
                show lena with short
                "I was still buzzing when I got home."
                l "Ian..."
            elif ian_lena_breakup:
                $ flena = "sad"
                show lena with short
                "I was feeling a bit down when I got home. Today didn't go as I was expecting..."
            else:
                $ flena = "n"
                show lena with short
                l "Home sweet home, finally..."
            if v9_lena_sex > 2:
                $ flena = "flirtshy"
                l "I still can't believe we ended up having sex in a public place..."
            show lena at rig with move
            play sound "sfx/meow.mp3"
            show lola_b at lef with short
            $ flena = "smile"
            "Lola jumped from the couch to greet me, rubbing her head on my legs, tail up."
            l "Hey there, baby girl. Did you miss me?"
            play sound "sfx/purr.mp3"
            show lolahappy_b at lef with short
            l "Feels good to be back home..."
            $ flena = "n"
            l "I have a lot to do, though."
            "I hadn't been away that long, but I had left so many things hanging."
            "I had a lot to set up straight, starting with my financial situation. Now more than ever."
            show lena at rig3
            show lolahappy_b at truecenter
            hide lola_b
            with move
            if lena_stan < 4:
                $ fstan = "worried"
            else:
                $ fstan = "n"
            play sound "sfx/door.mp3"
            show stan at lef3 with short
            st "Uh, hello, Lena..."
            st "I didn't know you were coming back today..."
            if lena_stan < 4:
                $ flena = "serious"
                l "Hi, Stan."
                st "..."
                st "I'll let you get settled."
                hide stan with short
                $ flena = "n"
                l "I almost forgot about him..."
            else:
                $ flena = "n"
                l "Hi, Stan. How has Lola been in my absence?"
                st "I've been taking care of her, making sure her bowl was full and all that..."
                st "How was your trip home?"
                l "Not the best, given the circumstances..."
                $ fstan = "sad"
                st "I can imagine... How's your Mom?"
                st "Louise only told me she was in the hospital, and I didn't want to be bothersome by texting or calling you to ask..."
                menu:
                    "You don't bother me":
                        $ renpy.block_rollback()
                        $ flena = "smile"
                        l "You wouldn't have bothered me, Stan."
                        $ fstan = "blush"
                        st "Really? I've never texted you or anything just in case..."
                        l "You can text me whenever you want, but we live together, so there's no real need for that!"
                        st "That's right..."
                        l "What I mean is that you can talk to me whenever you want, on the phone or at home."
                        $ fstan = "smile"
                        st "Cool, got it."
                        if lena_stan < 12:
                            call friend_xp('stan', 1) from _call_friend_xp_142
                        $ fstan = "n"
                        st "So... How's she?"
                        l "My Mom? She's alright... Well, as alright as someone with anemia and a broken hip can be, I guess."
                        $ fstan = "sad"
                        st "I didn't know it was that serious..."
                        l "She'll survive. It's just gonna be tough for a few months..."

                    "She's alright":
                        $ renpy.block_rollback()
                        l "She's alright... Well, as alright as someone with anemia and a broken hip can be, I guess."
                        $ fstan = "sad"
                        st "I didn't know it was that serious..."
                        l "She'll survive. It's just gonna be tough for a few months..."

                    "I'm tired of talking about it":
                        $ renpy.block_rollback()
                        $ flena = "sad"
                        l "I don't really want to get into it if you don't mind. I've been going over the same stuff every day this week..."
                        $ fstan = "sad"
                        st "Yes, of course...! Sorry to have bothered you."
                        call friend_xp('stan', -1) from _call_friend_xp_143
                $ flena = "sad"
                l "And... How have things with Louise been in my absence?"
                $ fstan = "n"
                st "Calm... I try to steer clear of her and she does the same."
                st "I think we've only talked once or twice since you've been gone."
                st "Hey, it's not your fault... It sucks, but I'm used to it."
                l "You're used to it?"
                $ fstan = "sad"
                st "To not being liked, I mean. Most people I've met in my life disliked me for some reason..."
                st "I guess I'm just weird like that."
                $ flena = "smile"
                if lena_stan > 7:
                    l "Screw them. I like you."
                    $ fstan = "blush"
                    st "I, um, thanks..."
                else:
                    l "Screw them. You're a nice guy, Stan."
                    st "Um, thanks..."
                l "Alright. I'll go unpack and get settled."
            scene lenaroomnight with long
            pause 0.5
            play sound "sfx/shower.mp3"
            scene v1_lena_shower
            if lena_piercing1:
                show v1_lena_shower_p1
            if lena_piercing2:
                show v1_lena_shower_p2
            if lena_tattoo2:
                show v1_lena_shower_t2
            if lena_tattoo3:
                show v1_lena_shower_t3
            with long
            pause 1
            "After unpacking all my luggage I took a long shower and tried to relax in my room."
            $ flena = "n"
            $ lena_look = "towel"
            scene lenaroomnight with long
            pause 0.5
            show lena with short
            l "Let's see... I need to get things in order and plan the upcoming days..."

## FIRDAY END  #################################################################################################################################################### ####################################################################################################################################################
    #louise
    play sound "sfx/knock.mp3"
    "Someone knocked on my door and entered the bedroom."
    if v9_luggage_sex == "mike" or v9_luggage_sex == "robert":
        $ flouise = "sad"
        show lena at rig with move
        play sound "sfx/door.mp3"
        show louise at lef with short
        lo "Hi..."
        l "Hello, Louise."
        lo "I got home a while ago, but Stan told me you were... busy, so..."
        $ flena = "shy"
        l "Oh, yeah... Sorry about that, but I needed to blow off some steam!"
        if lena_louise_sex or lena_reject_louise:
            lo "You know I can help with that, too..."
            $ flena = "n"
            l "Huh?"
            $ flouise = "n"
            lo "Nothing."
        else:
            $ flena = "n"
    else:
        show lena at rig with move
        if louise_jeremy and v7_bbc == "lena":
            $ flouise = "n"
            play sound "sfx/door.mp3"
            show louise at lef with short
            lo "Hi..."
            l "Hello, Louise."
        else:
            $ flouise = "happy"
            play sound "sfx/door.mp3"
            show louise at lef with short
            lo "Lena! Welcome back!"
            $ flena = "smile"
            "Louise barged in and gave me a hug."
            lo "I'm so glad to see you!"
            l "I'm glad to be back, too."
    # lena bbc
    if louise_jeremy and v7_bbc == "lena":
        $ flouise = "n"
        lo "So, uh, how was your trip back home?"
        $ flena = "n"
        l "It was okay..."
        lo "I see. I just wanted to say hello, so..."
        $ flena = "sad"
        "The awkward vibe that had settled between Louise and me after that night at Ivy's place was still there..."
        if v8_jeremy_sex:
            "I could tell she felt uncomfortable around me, and she had reasons to. If she only knew what I did with Jeremy in her room..."
        else:
            "I could tell she felt uncomfortable around me."
        "My first instinct was to ask her about her relationship, to test the waters..."
        l "How are things going with Jeremy?"
        $ flouise = "blush"
        lo "They're okay... Same as ever... Why?"
        lo "Has he said something to you?"
        $ flena = "sad"
        l "Something about what?"
        lo "No, nothing..."
        lo "Glad to have you back. Good night..."
        play sound "sfx/door.mp3"
        hide louise with short
        show lena at truecenter with move
        l "She's acting really weird..."
        if v8_jeremy_sex:
            $ flena = "worried"
            l "Does she know what I did? No, she would've said something, I'm sure..."
            l "But maybe she suspects it... It's possible Jeremy talked too much and something slipped..."
            l "I'll need to talk to him. I haven't contacted him since that day..."
            $ flena = "sad"
            l "What's wrong with me? I'm doing some really crazy, stupid things lately..."
        else:
            l "I wonder what's up."
        l "Now, where was I...?"
    # normal
    else:
        if lena_louise_sex:
            lo "God, I missed you so much!"
        elif lena_reject_louise:
            lo "Good thing you are back..."
        else:
            lo "I missed having you around!"
        $ flouise = "serious"
        lo "I didn't feel safe sharing the same roof with that creep all by myself... Thank God you're back."
        $ flouise = "smile"
        lo "How are you? Was it tough spending all these days back at your parents' home?"
        $ flena = "n"
        l "Yeah..."
        "We talked for a while and I told Louise about my family's current situation."
        $ flouise = "sad"
        lo "That's so unlucky... So many things piling up!"
        l "Yeah, well... I'm kinda getting used to it at this point."
        l "What about you? How have you been?"
        if louise_jeremy == False:
            $ flena = "smile"
            l "Have you met someone new yet?"
            lo "Someone new?"
            l "You know, a guy..."
            $ flouise = "serious"
            lo "No, no, I'm fed up with guys. They're all immature scumbags!"
            $ flena = "sad"
        lo "Right now I want to focus on finding a job, now that I've finally finished my degree."
        $ flena = "n"
        l "That sounds great."
        # sexual frustration
        if lena_louise_sex and lena_reject_louise == False:
            $ flouise = "flirt"
            if louise_jeremy == False:
                if louise_dominant:
                    lo "Besides, I already have you... Mistress."
                else:
                    lo "Besides, I already have you..."
            "Louise got closer and touched my thigh intently."
            $ flena = "worried"
            lo "I've missed you so much... Felt so alone at night..."
            lo "I..."
            show lena at rig3 with move
            $ flouise = "sad"
            "I moved away from her. Her attitude was making me feel kind of uncomfortable, for some reason..."
            lo "Um, I was hoping we could, you know..."
            show louise at truecenter with move
            lo "Comfort each other after so many days apart..."
            l "Louise... Honestly, I'm not in the mood at the moment."
            l "I have so much stuff on my mind right now."
            lo "Me too, that's why..."
            $ flena = "serious"
            l "I said no, Louise. Please, don't smother me."
            lo "But I thought we could..."
            l "Are you even listening to me? I'm really tired today, Louise!"
            lo "...!" with vpunch
            show louise at lef with move
            lo  "Why are you being so mean to me...?"
            $ flouise = "serious"
            lo "I don't deserve to be treated like this!"
            l "I'm just not in the mood today, is that so hard to understand?"
            $ flouise = "mad"
            if v9_luggage_sex == "mike" or v9_luggage_sex == "robert":
                lo "But you were in the mood to fuck that guy just a few minutes ago!"
                l "Yeah, and that's why I'm tired and not in the mood anymore!"
                lo "So you prefer a random guy over your friend? I'm sure you'd even pick that girl over me, the writer!"
            elif ian_lena_dating:
                lo "I'm sure you aren't like this with Ian! Why do only I get this treatment?"
                lo "I'm sure you'd even pick that girl over me, the writer!"
            else:
                lo "What's the matter? I'm sure you don't treat that writer girl like this!"
                lo "Do you prefer her over me, too?"
            l "Why are you bringing Holly into this now? She has nothing to do with..."
            $ flouise = "cry"
            "Louise started crying."
            $ flena = "worried"
            lo "I see how it is! You prefer her over me!"
            lo "You prefer anybody over me, even that creep Stan!"
            l "What are you even talking about now...?"
            lo "I'm sorry I ever bothered you!"
            play sound "sfx/door_slam.mp3"
            hide louise with vpunch
            show lena at truecenter with move
            l "What the hell...?"
            call friend_xp('louise', -1) from _call_friend_xp_144
            $ lena_louise = 3
            $ flena = "serious"
            l "I don't have the time or energy to deal with this right now..."
            $ flena = "sad"
            "I had the feeling hooking up with Louise could bring trouble, and turns out I was right."
            l "Jeez... I shouldn't have crossed that line."
            l "I will talk to her some other time, but right now I need to focus on other stuff."
            l "What was I thinking about...?"
        #normal
        else:
            if louise_jeremy == False:
                $ flouise = "n"
                lo "Yeah, fuck men."
                $ flena = "sad"
            $ flouise = "happy"
            lo "Anyway, I'm glad you're finally back. I couldn't stand living alone with that pig anymore!"
            lo "Good night, Lena."
            $ flena = "n"
            l "Good night..."
            play sound "sfx/door.mp3"
            hide louise with short
            show lena at truecenter with move
            l "Now, where was I...?"
    # recap
    if lena_axel_dating:
        if v9_alison_trip:
            $ flena = "n"
            l "Oh, yeah, tomorrow I'm having lunch with Ian."
            $ flena = "worried"
            l "I can't spend all afternoon with him, though. I'm meeting Axel for that photo shoot..."
        else:
            $ flena = "worried"
            l "Oh, yeah, my photo shoot with Axel..."
        "We had agreed on meeting this Saturday, at his place. I hadn't been there since we had broken up..."
        l "I'm still on the fence about this. The only reason I've accepted is that Ivy told me she'd go with me..."
        if v6_axel_work:
            l "Being alone with Axel in his place sounds... problematic."
            "Things seemed to have calmed down a bit though, and we even shared the studio again..."
            if v6_axel_pose > 1:
                "But that situation got weird and uncomfortable... To his credit, Axel didn't try to hound me or even contact me afterward."
                "He acted completely different from that time at the restaurant, calm and collected."
            elif v6_axel_pose == 1:
                "It was not something I was expecting, or was thrilled about, and I reacted accordingly."
                "I supposed he'd get mad and frustrated when I refused to work with him, but he didn't."
                "And he was still willing to help me when I asked, with no conditions. He probably saw it as a way to redeem himself, even if it was just a bit."
            if v4_axel_date:
                "Maybe that conversation at the café was what he really needed to move on. What both of us needed."
            else:
                "Maybe things had changed and we were finally ready to move on..."
            if lena_axel_desire:
                $ flena = "blush"
                "I had my doubts, though. That damned dream..."
                "I had been trying not to think about it. What it made me feel, what it meant..."
                "All I was willing to admit to myself was that it made me feel ashamed."
                "Once again I pushed those thoughts away from my mind."
                "All that mattered was that he was giving me a chance to get noticed by a big modeling agency."
            else:
                if axel_pictures_watch:
                    $ flena = "blush"
                    "I had my doubts, though. I couldn't help thinking about those Polaroids I had found inside my notebook..."
                    "I still had them, and I had looked at them more than once. I did more than just look at them, in fact."
                    $ flena = "serious"
                    l "They're just images from the past. Everything's changed now, including me, and Axel."
                if axel_pictures_destroy:
                    $ flena = "serious"
                    "I knew I was, at least. I had put my past behind me."
                if lena_axel == 3:
                    $ flena = "n"
                    l "I have to trust Axel feels the same way, and that doing this favor for me is a way for him to redeem himself."
                elif lena_axel == 2:
                    $ flena = "sad"
                    l "I'd like to think Axel feels the same way, and that doing this favor for me is a way for him to redeem himself, even if it's just a bit."
                else:
                    l "I still hate to have to rely on him, but doing this favor for me will be a way for Axel to redeem himself, even if it's just a bit."
                l "All that matters is that he's giving me a chance to get noticed by a big modeling agency."
        elif v4_axel_date:
            "Things had calmed down a bit after we met at the café, or so it felt like, but I wasn't ready to be alone with him..."
            "He had stopped hounding me and even trying to contact me, at least."
            if lena_job_seymour or v6_agnes_shoot:
                "I supposed he'd get mad and frustrated when I refused to work with him, but he didn't."
            "Maybe we were finally ready to move on..."
            if axel_pictures_watch:
                $ flena = "blush"
                "I had my doubts, though. I couldn't help thinking about those Polaroids I had found inside my notebook..."
                "I still had them, and I had looked at them more than once. I did more than just look at them, in fact."
                $ flena = "serious"
                l "They're just images from the past. Everything's changed now, including me, and Axel."
            if axel_pictures_destroy:
                $ flena = "serious"
                "I knew I was, at least. I had put my past behind me."
            if lena_axel == 3:
                $ flena = "n"
                l "I have to trust Axel feels the same way, and that doing this favor for me is a way for him to redeem himself."
            elif lena_axel == 2:
                $ flena = "sad"
                l "I'd like to think Axel feels the same way, and that doing this favor for me is a way for him to redeem himself, even if it's just a bit."
            else:
                l "I still hate to have to rely on him, but doing this favor for me will be a way for Axel to redeem himself, even if it's just a bit."
            l "All that matters is that he's giving me a chance to get noticed by a big modeling agency."
        $ flena = "sad"
        l "That would surely solve many of my problems... I can't rely on that life drawing gig to pay the bills."
    else:
        $ flena = "sad"
        l "I have so much stuff on my mind..."
        l "I wonder if Axel will really help Ivy get into Wildcats. Being signed on by such a big modeling agency would surely solve many of my problems..."
        $ flena = "serious"
        l "But I don't want to deal with Axel ever again, even if it means losing such an opportunity."
        $ flena = "sad"
        l "I can't pay the bills with my life drawing gig, though..."
# STALKFAP RECAP
    if stalkfap:
        $ flena = "n"
        l "At least I'm getting some money with Stalkfap. It was a good idea to get on it."
        show lena at right with move
        show v9_stalkfap1 #findme - missing from Lena's phone gallery
        if lena_tattoo1:
            show v9_stalkfap1_t1
        if lena_tattoo2:
            show v9_stalkfap1_t2
        if lena_tattoo3:
            show v9_stalkfap1_t3
        if lena_piercing1:
            show v9_stalkfap1_p1
        elif lena_piercing2:
            show v9_stalkfap1_p2
        with short
        if stalkfap_pro == 2:
            l "Since I was at my parents' place, all I could post were a couple selfies, but that seems enough to keep people happy."
            $ flena = "shy"
            l "Of course, that's only half of the story..."
            "I had been sending out custom pictures and videos to the guys who asked for them, just like Ivy encouraged me to do."
            hide v9_stalkfap1
            hide v9_stalkfap1_t1
            hide v9_stalkfap1_t2
            hide v9_stalkfap1_t3
            hide v9_stalkfap1_p1
            hide v9_stalkfap1_p2
            show v9_stalkfap2
            if lena_tattoo2:
                show v9_stalkfap2_t2
            with short
            $ lena_lena_pics.append("v9_stalkfap2_comp")
            l "This guy really asked me to spread my pussy for him..."
            l "I would've found something like this way too shameful in the past. I still do, in fact, but..."
            hide v9_stalkfap2
            hide v9_stalkfap2_t2
            show v9_stalkfap3
            if lena_tattoo2:
                show v9_stalkfap2_t2
            with short
            $ lena_lena_pics.append("v9_stalkfap3_comp")
            l "I guess I can get some fun out of it, too."
            l "The fact that these guys remain anonymous makes it easier to just let go and act naughty."
            l "And they really want me acting naughty..."
            $ flena = "flirtshy"
            hide v9_stalkfap3
            hide v9_stalkfap2_t2
            show v9_stalkfap4
            with short
            $ lena_lena_pics.append("v9_stalkfap4.webp")
            l "They really love asking me to show them how I play with my ass."
            l "I've been doing that quite a lot lately. I never knew fingering my ass could feel so good..."
            l "Ivy was right in making me buy that anal plug. Maybe I should upgrade to a bigger one now that I've gotten used to it..."
            hide v9_stalkfap4
            show v9_stalkfap5
            $ lena_lena_pics.append("v9_stalkfap5.webp")
            with short
            l "This video was so naughty..."
            l "I recorded it just in case, but I haven't shared it with anyone... yet."
            "I was pretty bored at home, so I ended up masturbating quite a lot, even if it wasn't to make a profit."
            if lena_fty_bbc:
                "I had been watching some interracial porn... That never failed to get me going..."
            $ flena = "blush"
            "Maybe I was letting myself get too caught up in lust?"
            "This Stalkfap thing was just something to help me make ends meet, after all..."
            hide v9_stalkfap5 with short
        elif stalkfap_pro:
            l "Since I was at my parents' place, all I could post were a couple selfies, but that seems enough to keep people happy."
            l "It would be cool to do another shoot, but in the meanwhile, sexy selfies it is."
            hide v9_stalkfap1
            hide v9_stalkfap1_t1
            hide v9_stalkfap1_t2
            hide v9_stalkfap1_t3
            hide v9_stalkfap1_p1
            hide v9_stalkfap1_p2
            with short
        else:
            $ flena = "sad"
            l "Sadly, it's not the kind of content I can be proud of..."
            l "Since I was at my parents' place, all I could post were a couple selfies."
            $ flena = "n"
            l "They're far from quality pictures, but people seem to like them, so... They serve their purpose, I guess."
            hide v9_stalkfap1
            hide v9_stalkfap1_t1
            hide v9_stalkfap1_t2
            hide v9_stalkfap1_t3
            hide v9_stalkfap1_p1
            hide v9_stalkfap1_p2
            with short
        show lena at truecenter with move
        if cafe_help:
            l "In any case, I need to keep helping at the café. So far it seems my ideas haven't been too bad..."
            l "I really hope we can turn things around, otherwise, I will need to find myself another job..."
        else:
            l "In any case, I need to find myself another job... I don't think the café will remain open for much longer."
    else:
        if cafe_help:
            l "I also need to keep helping at the café. So far it seems my ideas haven't been too bad..."
            l "I really hope we can turn things around, otherwise, I will need to find myself another job..."
        else:
            l "I also need to find myself another job... I don't think the café will remain open for much longer."
    l "And I also need to work on my music! I should try and play again. It was a good experience..."
    $ flena = "worried"
    l "Ugh, so much to do... I'm getting stressed just thinking about it...!"
    if v9_alison_trip == False:
        if ian_lena_couple:
            $ flena = "shy"
            "At least things with Ian were shaping up great."
            "To think we were starting a relationship... I was excited and jolly, but also a bit afraid."
        elif ian_lena_dating:
            if ian_lena_love:
                $ flena = "sad"
                "I was still going over my conversation with Ian."
                "He had confessed his feelings for me and asked me to be his girlfriend, but I had to decline..."
                if lena_ian_love:
                    $ flena = "blush"
                    "I had been catching feelings for Ian too, I really liked him..."
                    "But after what happened with Jeremy, I just couldn't accept his request. It felt wrong."
                    l "I'm so stupid..."
                else:
                    "I really liked him, but I wasn't prepared to get into a serious relationship. Not so soon."
                    "At least we agreed to keep seeing each other. Who knew what the future could bring..."
            elif lena_ian_love:
                $ flena = "sad"
                "I felt kinda stupid... I had been catching feelings for Ian, I even started to think I could give love a try once again..."
                "But it looked like he wasn't on that stage yet. Or maybe I wasn't even the right girl for that..."
                "In any case, we would continue to see each other. I was a bit on the fence about it, but who knew what the future could bring..."
            else:
                $ flena = "smile"
                "I was glad we finally put our cards on the table. Things should go smoothly now that we knew where we stood."
        elif ian_lena_breakup:
            $ flena = "sad"
            "I was still going over my conversation with Ian."
            "He had confessed his feelings for me and asked me to be his girlfriend, but I had to decline..."
            "I wasn't ready to get into another relationship, not yet. I wanted things to be simple, easier, at least for a time."
            "But that wasn't what Ian wanted, so he decided it was best for us to stop hooking up. It wasn't what I would've chosen, but I had to respect his boundaries."
            "At least he was honest with himself."
        elif ian_lena_makeup:
            $ flena = "n"
            "At least things with Ian seemed to be going back to normal. I really enjoyed seeing him today..."
            "And for some reason, I didn't feel so awkward about what happened with Holly anymore."
            "He wasn't a bad guy..."
    scene lenaroomnight with long
    "I tried to sleep, but I wasn't able to switch my mind off."
    "I had been sleeping poorly lately, and that night wouldn't be an exception..."
    stop music fadeout 2.0
# SATURDAY  #################################################################################################################################################### ####################################################################################################################################################
    call calendar(_day="Saturday") from _call_calendar_16

    $ v9lensatmusic = False # tracking var
    $ v9lensatart = False # tracking var
    $ v9lensatdanny = False # tracking var
    $ v9lensatshop = False # tracking var
    scene lenahome with long
    "As expected, I woke up feeling tired."
    play music "music/normal_day4.mp3" loop
    $ flena = "n"
    $ lena_look = 4
    show lena with short
    if v9_alison_trip:
        "It was Saturday, but I had things to do before meeting Ian."
        if lena_axel_dating:
            $ flena = "worried"
            "And then I had the shoot with Axel... Thinking about it was making me nervous already."
    elif lena_axel_dating:
        "It was Saturday, but I had things to do before the shoot with Axel..."
        $ flena = "worried"
        "Thinking about it was making me nervous already."
    else:
        "It was Saturday, but I had things to do."
    $ flena = "n"
    if cafe_help:
        l "I wonder if I should go help at the café today..."
        l "But I have other stuff to take care of."
    else:
        l "I should start looking for a new job..."
    l "I had to cancel my last life drawing session at the gallery. I should call them."
    l "And I haven't heard from Danny in some time. Maybe he has modeling work for me..."
    if cafe_music:
        l "And I should drop by the record store, also. Maybe they will let me play there next time..."
    else:
        l "And I should drop by the record store, also. I wonder if they'll let me play there again."
    menu v9lenasat:
        "Go to the record store" if v9lensatmusic == False:
            $ renpy.block_rollback()
            $ v9lensatmusic = True
            scene street2 with long
            "I walked to the record store."
            scene recordstore with long
            show lena at rig with short
            l "Hello!"
            $ femma = "smile"
            show emma at lef with short
            e "Hey, Lena! Nice to have you back!"
            $ femma = "sad"
            e "Ian told me about your Mom. How is she?"
            "I talked to Emma briefly about my family situation and she showed me her sympathy."
            e "That's tough. If there's something I can do to help don't hesitate to let me know."
            $ flena = "smile"
            l "Thanks, Emma. I know that's a sincere offer. But right now all I want to ask you is if we could keep rehearsing together."
            $ femma = "n"
            l "Our first show turned out pretty good, but I still feel I need to improve so much!"
            e "Sure! Let's find a day and get together again!"
            show lena at rig3
            show emma at lef3
            with move
            show record_owner with short
            if cafe_music:
                martin "Hello, Lena. I've heard your performance at the café was really good!"
                $ flena = "shy"
                l "Really?"
                martin "Yeah. Last time I told you you could play here too if you wanted..."
                martin "Now it's me who wants you to! Would you like for us to host you one of these nights?"
            else:
                martin "Hey there, Lena! I know for a fact people really liked your performance!"
                $ flena = "shy"
                l "Really?"
                martin "Yeah, I talk to my customers. I'd like to host you another night, what do you think?"
            l "Wow, that'd be... I'd like that, yeah."
            martin "I talked to the owner of the Fortress, too. You know that bar?"
            $ flena = "n"
            if v4_place == "fortress":
                l "Yeah, I went there once..."
            else:
                l "I've heard about it. I have friends who like to go there."
            martin "Well, he told me he'd be interested in having you play there too. Can I give him your contact info?"
            $ flena = "shy"
            $ femma = "smile"
            l "Yeah, sure...!"
            e "Congratulations! Seems like you're starting to make an impact!"
            l "I don't know about that..."
            if cafe_music:
                martin "You gotta start somewhere. There's not a lot of money in it, but if you start playing at some spots maybe you'll earn just enough to pay the bills!"
            else:
                martin "You gotta start somewhere. Oh, and this is your share of the profits from last time."
                call money(1) from _call_money_8
                $ flena = "happy"
                l "Oh, I had totally forgotten...!"
                martin "There's not a lot of money in it, but if you start playing at some spots maybe you'll earn just enough to pay the bills!"
            hide record_owner with short
            $ flena = "smile"
            $ femma = "n"
            show lena at rig
            show emma at lef
            with move
            e "See? You just had to give it a try..."
            l "I'm getting a lot of encouragement... I want to keep trying."
            e "That's the spirit!"
            if v9lensatart or v9lensatdanny:
                $ flena = "sad"
                l "Hopefully, it'll end up working out somehow... I'm having trouble getting jobs recently!"
                $ femma = "sad"
                e "Really?"
                if v9lensatart and v9lensatdanny:
                    l "Yeah... It's really weird, it feels like people suddenly are trying to avoid me or something, I don't know..."
                    e "Getting a job it's becoming harder and harder, and rents are starting to go up quite a lot."
                else:
                    l "Yeah... It's always been hard, but for some reason, it seems to be getting harder."
                    e "You're not the only one who's noticed that. And rents are starting to go up quite a lot."
            else:
                l "How about you, Emma? Are you still involved in that neighborhood assembly?"
                e "Yeah, we've been pretty active lately... Seems things keep getting worse and nobody's doing anything to stop it."
                $ femma = "sad"
                e "Finding a decent job keeps getting harder and harder and rents are starting to go up quite a lot."
            $ flena = "sad"
            l "I know. Something's wrong in this country..."
            $ femma = "serious"
            e "And in this city, specifically. And I know just the guy..."
            "She was talking about Seymour, of course."
            $ femma = "smile"
            e "Splashing his fancy clothes with my beer was one of the most satisfying moments of my life!"
            if seymour_disposition > 1:
                $ flena = "sad"
                "My opinion about Seymour wasn't as bad as Emma's, but I didn't want to antagonize her."
                l "Did he really deserve it?"
            elif seymour_disposition == 1:
                $ flena = "n"
                l "He's a sketchy individual for sure... I'm sure he had it coming."
            else:
                $ flena = "happy"
                l "Yeah, the look on his face was enough to justify it!"
            $ femma = "serious"
            e "He deserves that and much more... I hope his karma catches up to him soon before he does more damage."
            $ femma = "n"
            e "Oops, seems like a customer needs me. Let's meet up soon, alright?"
            $ flena = "smile"
            l "Sure!"
            hide emma with short
            show lena at truecenter with move
            jump v9lenasat

        "Call the art gallery" if v9lensatart == False:
            $ renpy.block_rollback()
            $ v9lensatart = True
            l "I'll make a new appointment with the gallery..."
            $ flena = "n"
            hide lena
            show lena_phone
            with short
            show phone_curator at lef3 with short
            cur "Yes?"
            l "Good morning, it's Lena."
            cur "Oh, hey..."
            l "I'm back in town and ready to work again! Are you still interested in having me on Fridays?"
            cur "Actually, we have that spot filled in already..."
            $ flena = "sad"
            l "Oh, really? What about Tuesdays...?"
            cur "Right now we have every position filled in."
            $ flena = "worried"
            l "But I thought you said you wanted to have me pose regularly..."
            cur "We did, but... As I said, we've filled in the spot with someone else."
            menu:
                "Who's the new model?":
                    $ renpy.block_rollback()
                    $ flena = "sad"
                    l "Who's the new model?"
                    cur "We can't give you that information."
                    $ flena = "serious"
                    l "Why not? It's not like it's confidential or anything."
                    l "All I need to do is go there next Friday and see."
                    cur "We'd rather you not do that. I'm sorry we're forced to cancel our partnership."

                "This doesn't add up":
                    $ renpy.block_rollback()
                    $ flena = "serious"
                    l "This doesn't add up. I thought you were having trouble finding models..."
                    l "And you said you were really happy with my work. Why do away with me all of a sudden?"
                    l "Just because I've been unavailable for two weeks?"
                    if lena_wits < 9:
                        call xp_up('wits') from _call_xp_up_108
                    cur "What you're saying is right, it's just..."
                    cur "Current circumstances are forcing us to do without you, sorry."

                "Why are you doing this to me?":
                    $ renpy.block_rollback()
                    l "Why are you doing this to me? I thought we had an agreement!"
                    cur "Well, we didn't sign any contract..."
                    $ flena = "serious"
                    l "So that makes it okay? I thought you were happy to have me!"
                    cur "We were, it's just..."
                    cur "Circumstances are forcing us to do without you, sorry."

            cur "Good luck."
            hide phone_curator with short
            $ flena = "worried"
            l "He hung up on me!"
            hide lena_phone
            show lena
            with short
            if v9lensatdanny:
                l "Not just Danny, but they're turning me down too? What's going on?"
                "I had come to rely on these drawing events as a source of income, even if it was something on the side."
            else:
                l "What the hell...?"
                $ flena = "sad"
                "This wasn't what I was expecting... I had come to rely on these drawing events as a source of income, even if it was something on the side."
            if cafe_nude == False and v7_fight != "n":
                l "They're {i}forced{/i} to do without me? Has it something to do with Ian and Robert getting into a fight that day?"
            else:
                l "They're {i}forced{/i} to do without me?"
            if v9lensatdanny:
                l "Something's definitely off..."
            jump v9lenasat

        "Call Danny" if v9lensatdanny == False:
            $ renpy.block_rollback()
            $ v9lensatdanny = True
            $ flena = "n"
            if v9lensatart:
                l "Let's try Danny..."
            else:
                l "Let's see what Danny's up to."
            hide lena
            show lena_phone
            with short
            l "..."
            show phone_danny_sad at lef3 with short
            dan "Hello, Lena."
            l "Hey, Danny, how are you doing? It's been a while."
            dan "Yeah, I'm doing fine..."
            l "I was wondering if you have some work for me. I'm looking to do some shoots..."
            dan "I'm sorry, but right now I can't offer you anything..."
            if v6_agnes_shoot:
                if v6_axel_pose == 1:
                    l "Is it because I made Agnes Addingworth angry last time? I'm sorry about that..."
                    dan "No, it's not that, it's..."
                elif v6_axel_pose == 2:
                    l "Did I cause any trouble for you with Agnes Addingworth last time? I thought the shoot went okay..."
                    dan "No, it's not that, it's..."
                elif v6_axel_pose == 3:
                    l "What about Agnes Addingworth? I had the feeling she was happy with me last time..."
                    dan "I don't know about that. I mean, she probably was, but it hasn't to do with her..."
            $ flena = "sad"
            l "I don't mind if you can't hire or pay me right now. We could do a collaboration, I pose for you and you take some pictures for my portfolio and social media accounts..."
            dan "I'm really sorry, but I'm... really busy these days, so I'm afraid I can't."
            menu:
                "All I need is one hour!":
                    $ renpy.block_rollback()
                    l "Come on, all I need is one hour! I'm sure you can spare that much, can't you?"
                    dan "I'll have to check my schedule, but..."

                "I thought you liked working with me":
                    $ renpy.block_rollback()
                    $ flena = "worried"
                    l "Is there a problem? I thought you enjoyed working with me."
                    if lena_charisma < 9:
                        call xp_up('charisma') from _call_xp_up_109
                    dan "I did. I mean, I still do, it's just..."

                "There's something you're not telling me":
                    $ renpy.block_rollback()
                    $ flena = "serious"
                    l "What's the matter, Danny? I have the feeling there's something you're not telling me..."
                    dan "What? No, I mean..."

            dan "As I said, it's not a good time right now."
            dan "Maybe in the future we can work together once more. Take care."
            hide phone_danny_sad
            hide lena_phone
            show lena
            with short
            $ flena = "worried"
            if v9lensatart:
                l "What is going on? Both Danny and the guy from the art gallery turned me down..."
                l "He sounded really uncomfortable talking to me."
                l "Something's definitely off..."
            else:
                l "What the hell...?"
                l "This is weird... Danny sounded really uncomfortable talking to me."
                l "I hope there's nothing wrong with him..."
            jump v9lenasat

        "{image=icon_pay.webp}Go shopping" if v9lensatshop == False:
            $ renpy.block_rollback()
            $ v9lensatshop = True
            l "I have some spare time this morning. I could go downtown and check out some shops..."
            if lena_money < 2:
                $ flena = "sad"
                l "Ahhh... I wish I could, but I can't afford to."
                l "I'm struggling to pay the rent already..."
            else:
                l "Buying some clothes might cheer me up!"
            scene mall with long
            "I picked up my purse and headed down to the mall."
            show lena with short
            menu v9lenashopping:
                "Shop for clothes":
                    $ renpy.block_rollback()
                    l "Let's see if I can find something I like..."
                    show lena at left with move
                    call screen v9clothingshoplena
                    label v9shopwits_lena:
                        hide lena with short
                        l "Let me try this on..."
                        show lenanude
                        show lena_wits1
                        with long
                        $ flena = "smile"
                        l "Oh, I think it's lovely!"
                        if lena_money > 0:
                            menu:
                                "{image=icon_pay.webp}Buy this outfit":
                                    $ renpy.block_rollback()
                                    $ lena_wardrobe_wits1 = True
                                    l "I'm in love with this outfit. I'll buy it!"
                                    call money(-1) from _call_money_9
                                    l "What now?"
                                "Put it back":
                                    $ renpy.block_rollback()
                                    $ flena = "n"
                                    l "I really like it, but I'm not sure I should buy it."
                            jump v9clothesshopcontinue_lena
                        else:
                            $ flena = "sad"
                            "But I don't have enough money to buy it..."
                            jump v9clothesshopcontinue_lena
                        label v9clothesshopcontinue_lena:
                            $ renpy.block_rollback()
                            hide money_down
                            hide lenanude
                            hide lena_wits1
                            hide lena_athletics1
                            hide lena_charisma1
                            hide lena_lust1
                            with short
                            $ flena = "n"
                            show lena with short
                            show lena at left with move
                            call screen v9clothingshoplena
                        label v9leaveshop_lena:
                            $ renpy.block_rollback()
                            hide money_down
                            hide lenanude
                            hide lena_wits1
                            hide lena_athletics1
                            hide lena_charisma1
                            hide lena_lust1
                            with short
                            l "Okay, that's enough."
                            show lena at truecenter with move
                            $ config.menu_include_disabled = False
                            $ greyed_out_disabled = True
                            jump v9lenashopping

                    label v9shopcharisma_lena:
                        hide lena with short
                        l "Let's see how I look in this..."
                        show lenanude
                        show lena_charisma1
                        with long
                        $ flena = "happy"
                        l "Wow, it's perfect!"
                        if lena_money > 0:
                            menu:
                                "{image=icon_pay.webp}Buy this outfit":
                                    $ renpy.block_rollback()
                                    $ lena_wardrobe_charisma1 = True
                                    l "I need to buy this!"
                                    call money(-1) from _call_money_10
                                    $ flena = "smile"
                                    l "What's next?"
                                "Put it back":
                                    $ renpy.block_rollback()
                                    $ flena = "n"
                                    l "It's great, but I'm not sure I should buy it..."
                            jump v9clothesshopcontinue_lena
                        else:
                            $ flena = "sad"
                            "But I don't have enough money to buy it..."
                            jump v9clothesshopcontinue_lena

                    label v9shopathletics_lena:
                        hide lena with short
                        l "Let's see how this fits me..."
                        show lenanude
                        show lena_athletics1
                        with long
                        $ flena = "smile"
                        l "Nice!"
                        if lena_money > 0:
                            menu:
                                "{image=icon_pay.webp}Buy this outfit":
                                    $ renpy.block_rollback()
                                    $ lena_wardrobe_athletics1 = True
                                    l "I really like it, I'll get it!"
                                    call money(-1) from _call_money_11
                                    l "And now...?"
                                "Put it back":
                                    $ renpy.block_rollback()
                                    $ flena = "n"
                                    l "It's really cool, but I'm not sure I should buy it..."
                            jump v9clothesshopcontinue_lena
                        else:
                            $ flena = "sad"
                            "But I don't have enough money to buy it..."
                            jump v9clothesshopcontinue_lena
                    label v9shoplust_lena:
                        hide lena with short
                        l "This one is so daring..."
                        show lenanude
                        show lena_lust1
                        with long
                        $ flena = "shy"
                        l "Wow! I look hot!"
                        if lena_money > 0:
                            menu:
                                "{image=icon_pay.webp}Buy this outfit":
                                    $ renpy.block_rollback()
                                    $ lena_wardrobe_lust1 = True
                                    l "I'm definitely getting this!"
                                    call money(-1) from _call_money_124
                                    l "And now...?"
                                "Put it back":
                                    $ renpy.block_rollback()
                                    $ flena = "n"
                                    l "I'm not sure it suits me..."
                            jump v9clothesshopcontinue_lena
                        else:
                            $ flena = "sad"
                            "But I don't have enough money to buy it..."
                            jump v9clothesshopcontinue_lena

                "Go to the sex shop":
                    $ renpy.block_rollback()
                    l "I feel like browsing some items at the sex shop..."
                    scene sexshop with long
                    show lena at left with short

                    call open_sexshop from _call_open_sexshop

                    show lena at truecenter with move
                    $ flena = "n"
                    l "Alright, now..."
                    menu:
                        "Visit the tattoo parlor" if v9_tat == 0 or v9_piercing == 0:
                            $ renpy.block_rollback()
                            jump v9sexshoptattoo

                        "Leave":
                            $ renpy.block_rollback()
                            scene mall with long
                            show lena with short
                            jump v9lenashopping

                "Go to the tattoo parlor" if v9_tat == 0 or v9_piercing == 0:
                    $ renpy.block_rollback()
                    label v9sexshoptattoo:
                        l "Since I'm here, I'll pay Jess a visit."
                    scene sexshop with long
                    show lena at rig
                    $ fjess = "n"
                    if jess_bad:
                        show jessb at lef
                    else:
                        show jessg at lef
                    with short
                    if jess_bad:
                        if v9_tat > 0 or v9_piercing > 0:
                            js "What now?"
                        else:
                            js "What's up? Are you here for a tattoo?"
                    else:
                        if v9_tat > 0 or v9_piercing > 0:
                            js "Hey, forgot something?"
                        else:
                            js "Hey, Lena! What brings you here today?"
                    menu:
                        "Get a tattoo" if v9_tat == 0:
                            $ renpy.block_rollback()
                            if jess_bad:
                                if v9_piercing > 0:
                                    l "Let me check those designs..."
                                else:
                                    l "Maybe. Let me check those designs..."
                            else:
                                l "I wanted to take a look at those designs again!"
                            js "Sure, take a look."
                            label v9tattoopromt:
                                scene sexshop with long
                            call screen v9tattooshopscreen
                            label v9tat1:
                                $ flena = "smile"
                                scene sexshop
                                show lena at rig
                                if jess_bad:
                                    show jessb at lef
                                else:
                                    show jessg at lef
                                with short
                                l "I like the flower design!"
                                js "That one goes on your hip. It's pretty discreet and girly."
                                l "That's why I like it. The drawing is beautiful."
                                js "Take off your pants and let's see how the stencil looks on you."
                                $ flena = "n"
                                hide lena
                                show lenanude at rig
                                show lena_top4 at rig
                                show stencil1 at rig
                                with short
                                js "What do you think?"
                                menu:
                                    "I want it!"  if lena_money > 1:
                                        $ renpy.block_rollback()
                                        $ v9_tat = 1
                                        $ flena = "happy"
                                        l "I love it! I really want to get it!"
                                        call money(-2) from _call_money_12
                                        jump v9tatend

                                    "Let's try something else":
                                        $ renpy.block_rollback()
                                        $ flena = "sad"
                                        l "I have my doubts... Let's try something else."
                                        hide lenanude
                                        hide lena_top4
                                        hide stencil1
                                        show lena at rig
                                        with short
                                        jump v9tattoopromt

                            label v9tat2:
                                $ flena = "smile"
                                scene sexshop
                                show lena at rig
                                if jess_bad:
                                    show jessb at lef
                                else:
                                    show jessg at lef
                                with short
                                l "I like the one with the birdcage and the roses! It's beautiful!"
                                js "Let me put the stencil on your arm, see how it looks."
                                $ flena = "n"
                                show stencil2 at rig with short
                                js "What do you think?"
                                menu:
                                    "I want it!" if lena_money > 2:
                                        $ renpy.block_rollback()
                                        $ v9_tat = 2
                                        $ flena = "happy"
                                        l "It's perfect! I really want to get this one!"
                                        call money(-3) from _call_money_13
                                        js "I thought it would suit you."
                                        jump v9tatend

                                    "Let's try something else":
                                        $ renpy.block_rollback()
                                        $ flena = "sad"
                                        l "I'm not sure about this one. Let's try something else."
                                        hide stencil2 with short
                                        jump v9tattoopromt

                            label v9tat3:
                                $ flena = "smile"
                                scene sexshop
                                show lena at rig
                                if jess_bad:
                                    show jessb at lef
                                else:
                                    show jessg at lef
                                with short
                                l "The one with the crane and the sakura flowers looks incredible! I want this one!"
                                $ flena = "sad"
                                l "It's expensive, though..."
                                js "This one covers your thigh completely, so it's pretty big. But I'm quite proud of how this one turned out."
                                js "Take off your pants and let's see how the stencil looks on that leg."
                                $ flena = "n"
                                hide lena
                                show lenanude at rig
                                show lena_top4 at rig
                                show stencil3 at rig
                                with short
                                js "What do you think?"
                                menu:
                                    "I want it!" if lena_money > 3:
                                        $ renpy.block_rollback()
                                        $ v9_tat = 3
                                        $ flena = "happy"
                                        l "It's perfect! I really want to get it!"
                                        call money(-4) from _call_money_14
                                        jump v9tatend

                                    "Let's try something else":
                                        $ renpy.block_rollback()
                                        $ flena = "sad"
                                        l "Hum... I don't know. Let's try something else."
                                        hide lenanude
                                        hide lena_top4
                                        hide stencil3
                                        show lena at rig
                                        with short
                                        jump v9tattoopromt

                            label v9notat:
                                $ flena = "sad"
                                scene sexshop
                                show lena at rig
                                if jess_bad:
                                    show jessb at lef
                                else:
                                    show jessg at lef
                                with short
                                l "Um... I'm afraid I won't be getting a tattoo, after all."
                                if jess_bad:
                                    js "Then stop wasting my time. Come back when you change your mind."
                                else:
                                    js "Alright, come back if you change your mind."
                                scene mall with long
                                show lena with short
                                jump v9lenashopping
                            label v9tatend:
                                if v9_piercing > 0:
                                    js "Alright. As I said, come back tomorrow and we'll get you inked as well."
                                    $ flena = "smile"
                                    l "Sure! Thanks!"
                                else:
                                    js "Alright. Right now I have an appointment, but I have a free spot tomorrow."
                                    $ flena = "smile"
                                    l "Sure! I'll come back tomorrow, then! Thanks!"
                                scene mall with long
                                show lena with short
                                jump v9lenashopping

                        "{image=icon_pay.webp}Get a navel piercing" if v9_piercing == 0 and lena_piercing1 == False and lena_piercing2 == False and lena_money > 0:
                            $ renpy.block_rollback()

                            l "Actually, I've been wanting to get a navel piercing."
                            js "Which one?"
                            scene sexshop with long
                            call screen v4navelpiercing
                            if lena_piercing1:
                                $ v9_piercing = 1
                                $ lena_piercing1 = False
                                show v4_navel1
                                l "I'll get this one."
                            elif lena_piercing2:
                                show v4_navel2
                                $ v9_piercing = 2
                                $ lena_piercing2 = False
                                l "I like this one."
                            call money(-1) from _call_money_15
                            if v9_tat > 0:
                                js "Okay. Come back tomorrow and we can get it done."
                                l "Sure!"
                            else:
                                js "Okay. Right now I have an appointment, though, but if you come back tomorrow we can get it done."
                                l "Sure! See you tomorrow, then."
                            scene mall with long
                            show lena with short
                            jump v9lenashopping

                        "Leave":
                            $ renpy.block_rollback()
                            l "Just wanted to say hi. Bye!"
                            hide jessb
                            hide jessg
                            with short
                            show lena at truecenter with move
                            scene mall
                            show lena
                            with long
                            jump v9lenashopping

                "Leave":
                    $ renpy.block_rollback()
                    l "Enough shopping for today."
                    jump v9lenasat

        "Continue with your day" if v9lensatmusic and v9lensatart and v9lensatdanny:
            $ renpy.block_rollback()
            l "Alright, I think I've taken care of everything I needed to do..."
            $ config.menu_include_disabled = True
            $ greyed_out_disabled = False
            # IAN DATE
            if v9_alison_trip:
                l "I'll get ready to meet Ian."
                scene street with long
                $ flena = "smile"
                $ fian = "smile"
                if cafe_help:
                    "We had agreed to meet at the café. I also wanted to check up on Ed and Molly."
                else:
                    "For convenience, we had agreed to meet at the café."
                if ian_lena_dating:
                    stop music fadeout 2.0
                    if lena_lust > 6:
                        $ lena_look = "sexy"
                    play music "music/date.mp3" loop
                    show ian at lef
                    show lena at rig
                    with short
                    l "Hey there! Nice to finally see you!"
                    i "I missed you too."
                    if lena_look == 4:
                        scene v9_lena1a
                    else:
                        scene v9_lena1b
                    if lena_tattoo2:
                        show v9_lena1_t2
                    with long
                    "This was exactly the greeting I was expecting..."
                    if lena_ian_love:
                        "I realized how much I had been wanting to kiss him during these two lonely weeks..."
                    if lena_ian_love or lena_lust > 6:
                        if lena_look == 4:
                            scene v9_lena2a
                        else:
                            scene v9_lena2b
                        if lena_tattoo2:
                            show v9_lena1_t2
                        with long
                        "With my eyes closed, I let myself get caught up in the warm, fuzzy sensation of being reunited with Ian."
                        "I responded to his kiss with a deeper one, taking my time to enjoy the sensation of his tongue dancing with mine."
                        "I pressed my body against his chest, taking in his aroma, his taste, his embrace..."
                        if lena_ian_love:
                            "Kissing him really felt special..."
                        "I decided to stop before getting hornier than I already was!"
                        $ fian = "smile"
                        $ flena = "shy"
                        scene street
                        show ian at lef
                        show lena2 at rig
                        with short
                        i "Welcome back."
                        l "Now that's how you greet someone..."
                        "I couldn't help but notice the bulge under Ian's pants. I wasn't the only one aroused by that kiss!"
                    else:
                        "I was about to get my tongue involved, but he pulled away."
                        "Maybe he was a bit shy..."
                        $ fian = "smile"
                        $ flena = "smile"
                        scene street
                        show ian at lef
                        show lena at rig
                        with short
                        i "Welcome back."
                        l "Thanks."
                    l "Shall we go in?"
                    scene cafe with long
                    $ flena = "n"
                    $ fian = "smile"
                    $ fed = "n"
                    show lena at rig3
                    show ed
                    show ian at lef3
                    with short
                else:
                    scene cafe with long
                    "When I got there he was already waiting for me."
                    show ian at lef
                    show lena at rig
                    with short
                    i "Hey there!"
                    l "Hi, Ian! Nice to see you."
                    $ flena = "n"
                    $ fian = "smile"
                    $ fed = "n"
                    show lena at rig3
                    show ian at lef3
                    with move
                    show ed with short
                jump v9iandate2
                # date end
                label v9iandate3:
                    stop music fadeout 2.0
                    if v9_cindy_shoot:
                        $ fian = "surprise"
                        i "Oh, damn! Is it this late already?"
                        $ flena = "worried"
                        l "Is there a problem?"
                        $ fian = "worried"
                        i "I promised Wade I would do a favor for him. It's kind of important..."
                        $ flena = "n"
                        l "Of course."
                        if lena_axel_dating:
                            "I had something to take care of this afternoon, too. My shoot with Axel was in a couple of hours..."
                            jump v9axelshoot
                        jump v9saturdayend
                    elif lena_axel_dating:
                        $ flena = "surprise"
                        l "Oh, damn! Is it this late already?"
                        $ fian = "worried"
                        i "Is there a problem?"
                        $ flena = "sad"
                        l "I have a photo shoot lined up. I should get going to get there in time..."
                        $ fian = "smile"
                        i "Of course. Work is work."
                        jump v9axelshoot
                    else:
                        jump v9saturdayend

            elif lena_axel_dating:
                $ flena = "sad"
                l "I should get ready for the photo shoot..."
                jump v9axelshoot
            else:
                l "I guess I'll use this time to work on my songs..."
                jump v9saturdayend

## AXEL ####################################################################################################################################################################################################################################
label v9axelshoot:
    jump v9lenaaxel_1

# SATURDAY END ##################################################################################################################
label v9saturdayend:
    $ timeout_label = None
    # think about shoot
    if lena_axel_dating:
        play sound "sfx/door_home.mp3"
        show lenaroomnight with long
        pause 0.5
        if v9_axel_sex:
            $ flena = "drama"
            show lena with short
            "When I got home I was in full panic mode."
            "I had been able to keep it from overwhelming me when I said goodbye to Axel and during the taxi trip, but now that I was alone in my room..."
            l "I just had sex with Axel. It really happened."
            if v9_axel_kiss == "kiss":
                l "What's wrong with me? I gave in so easily... I had no will to resist him..."
            else:
                l "What's wrong with me? I tried to resist, to fight it... But I lost."
            $ flena = "blush"
            l "I can't believe I squirted all over his bed... God..."
            "I had only experienced something like that a couple of times before, both with Axel."
            "The first time it happened I was so surprised and embarrassed. I never knew my body was capable of such a thing."
            "And tonight Axel had caused it to explode a third time. I couldn't hold anything back."
            if ian_lena_couple:
                l "And the worst part is... Ian..."
                "I had just agreed to date him, and the first thing I did was cheat on him with my ex-boyfriend."
            $ flena = "worried"
            "I looked in the mirror and I barely recognized the girl I saw staring back at me."
            "Whoever this person was, she had betrayed me."
            l "No... I betrayed myself."
            if ian_lena_couple:
                $ flena = "cry"
                l "I'm the worst..."
            else:
                l "I'm so stupid..."
            "I felt so confused. The bridge I thought I had been building had burst into flames."
            "Instead of moving forward, I was back at square one. No, even worse than that..."
            "And Axel... What was going through his mind?"
            "The way he had acted after we were done... Where was all that emotion and need to hold onto me that he had shown in the past?"
            "Today it seemed like he had really moved on. Like he didn't need me as he used to..."
            "Had he found someone else? Could it be that he and Cindy...?"
            $ flena = "worried"
            l "What the hell am I even thinking...? I'm out of my mind right now."
            scene lenaroomnight with long
            "I got into bed, wishing to fall asleep so I didn't have to think about this anymore."
            "Of course, that didn't happen, and I spent the night rolling around in bed and torturing myself."
            jump v9lenasunday
        elif axel_disposition == 0:
            $ flena = "serious"
            show lena with short
            "I kept scolding Ivy on the way home, and even though she apologized several times my indignation didn't subside."
            "We spent the last half of the journey in silence and when I got back to my room I was still fuming."
            $ flena = "sad"
            l "I don't mind Ivy being a bit harebrained, but this was important... And she knew it."
            l "At least nothing bad happened..."
            $ flena = "n"
            "I felt a bit lighter after having gone through the shoot. I had been worrying about it for almost two weeks."
            l "I knew I would be uncomfortable, but it wasn't as bad as I thought it could be. Hopefully, this really helps my modeling career..."
            $ flena = "sad"
            l "Though I hope I won't have to work with Axel again if that's the case. Not frequently, at least..."
            $ flena = "n"
            l "Anyway, we'll see how that goes."
        elif v9_axel_kiss != "kiss":
            $ flena = "worried"
            show lena with short
            "When I got home my heart was still racing. I was feeling anger, shame, and many other confusing things..."
            if v9_axel_kiss == "wait":
                "Axel had kissed me..."
            "How did I let things get to that point?"
            $ flena = "serious"
            l "I tried making it very clear to Axel that we are done, that I've decided to move on."
            l "But it seems he doesn't want to get it..."
            $ flena = "sad"
            "I hoped that this wouldn't nullify his promise to send my pictures to Wildcats, but..."
            l "Maybe that'd be for the best, actually. There's no way I can keep dealing with him, and I don't see that changing anytime soon..."
            if lena_will < 2 and v9_axel_kiss == "push":
                call will_up() from _call_will_up_57
            $ flena = "serious"
            l "This is all Ivy's fault, if she had been there... She'll hear me."
            $ flena = "sad"
            if v9_axel_kiss == "wait":
                "I tried calming myself down before going to bed, but it was hard keeping what just happened out of my mind."
                "Thankfully, now back home, I felt safe."
            else:
                "I tried calming myself down before going to bed."
        if v9_alison_trip:
            if ian_lena_couple and axel_disposition == 0:
                $ flena = "happy"
            elif ian_lena_couple:
                $ flena = "smile"
            elif ian_lena_breakup:
                $ flena = "sad"
            "Now I had some space to think about my conversation with Ian earlier today."
            jump v9rethinkian
        else:
            if ian_lena_couple:
                $ flena = "shy"
                "Axel was a thing from the past. I had Ian, now..."
                "A new love, new friends, and new horizons."
            elif ian_lena_dating:
                "Axel was a thing from the past. Now I had other people in my life, like Ian..."
                "New friends, new adventures, and new horizons."
            elif ian_lena_breakup:
                $ flena = "sad"
                "Axel was a thing from the past. Now I had other people in my life..."
                "Things with Ian had come to an abrupt halt, though. I hoped we could really be friends..."
                $ flena = "n"
                "Still, I was looking forward to new horizons."
            else:
                "Axel was a thing from the past. Now I had other people in my life. New friends, and new horizons."
        if lena_robert_dating:
            jump v9roberttxt
        elif lena_mike_dating:
            play sound "sfx/sms.mp3"
            "Just then I got a text from Mike. I wasn't expecting one..."
            jump v9miketxt
        else:
            jump v9lenasunday
    # came back from date with Ian
    elif v9_alison_trip:
        play sound "sfx/door_home.mp3"
        scene lenahomenight with long
        if ian_lena_couple:
            $ flena = "happy"
            show lena with short
            "I was still buzzing when I got home."
            l "Ian..."
        elif ian_lena_breakup:
            $ flena = "sad"
            show lena with short
            "I was feeling a bit down when I got home. Today didn't go as I had been expecting..."
        else:
            $ flena = "n"
            show lena with short
        label v9rethinkian:
            if v9_lena_sex > 2:
                $ flena = "flirtshy"
                l "I still can't believe we ended up having sex in a public place..."
        if ian_lena_couple:
            $ flena = "shy"
            "To think we were starting a relationship... I was excited and jolly, but also a bit afraid."
            "I was hopeful, though... I really liked Ian, more than I had liked anybody in a long, long time."
        elif ian_lena_dating:
            if ian_lena_love:
                $ flena = "sad"
                "I was still going over my conversation with Ian."
                "He had confessed his feelings for me and asked me to be his girlfriend, but I had to decline..."
                if lena_ian_love:
                    $ flena = "blush"
                    "I had been catching feelings for Ian too, I really liked him..."
                    "But after what happened with Jeremy, I just couldn't accept his request. It felt wrong."
                    l "I'm so stupid..."
                else:
                    "I really liked him, but I wasn't prepared to get into a serious relationship. Not so soon."
                    "At least we agreed to keep seeing each other. Who knew what the future could bring..."
            elif lena_ian_love:
                $ flena = "sad"
                "I felt kinda stupid... I had been catching feelings for Ian, I even started to think I could give love a try once again..."
                "But it looked like he wasn't on that stage yet. Or maybe I wasn't even the right girl for that..."
                "In any case, we would continue to see each other. I was a bit on the fence about it, but who knew what the future could bring..."
            else:
                $ flena = "smile"
                "I was glad we finally put our cards on the table. Things should go smoothly now that we knew where we stood."
        elif ian_lena_breakup:
            $ flena = "sad"
            "I was still going over my conversation with Ian."
            "He had confessed his feelings for me and asked me to be his girlfriend, but I had to decline..."
            "I wasn't ready to get into another relationship, not yet. I wanted things to be simple, easier, at least for a time."
            "But that wasn't what Ian wanted, so he decided it was best for us to stop hooking up. It wasn't what I would've chosen, but I had to respect his boundaries."
            "At least he was honest with himself."
        elif ian_lena_makeup:
            $ flena = "n"
            "At least things with Ian seemed to be going back to normal. I really enjoyed seeing him today..."
            "And for some reason, I didn't feel so awkward about what happened with Holly anymore."
            "He wasn't a bad guy..."
        if lena_axel_dating == False:
            play sound "sfx/door.mp3"
            scene lenaroomnight
            show lena
            with long
        if v9_luggage != "robert" and lena_robert_dating:
            jump v9roberttxt
        elif v9_luggage_sex != "mike" and lena_mike_dating:
            play sound "sfx/sms.mp3"
            "Just then I got a text from Mike. I wasn't expecting one..."
            jump v9miketxt
        elif lena_mike_dating and ian_lena_couple or lena_robert_dating and ian_lena_couple:
            jump v9mikeroberttxt
        elif lena_axel_dating:
            stop music fadeout 2.0
            $ flena = "n"
            "I felt drained after the experience, so I went straight to bed. Hopefully, I could get some sleep tonight..."
            jump v9lenasunday
        else:
            stop music fadeout 2.0
            "After that, I went to my room and tried to work on some of my songs."
            play sound "sfx/guitar.mp3"
            scene lena_guitar2 with long
            "I hadn't brought my guitar to my parents' place, and I was itching to feel the strings on my fingers."
            "I played for a bit and checked my notebooks until I felt tired. I needed a good night's sleep..."
            jump v9lenasunday
    # didn't go to Ian or Axel findme
    else:
        stop music fadeout 2.0
        scene lenaroom with long
        $ lena_look = 1
        "After that, I went back to the apartment and tried to work on some of my songs."
        play sound "sfx/guitar.mp3"
        scene lena_guitar1 with long
        "I hadn't brought my guitar to my parents' place, and I was itching to feel the strings on my fingers..."
        scene lena_guitar2 with long
        "Before I knew it the afternoon was over and this Saturday was getting close to its end."
        scene lenaroomnight with long
        $ flena = "n"
        show lena with short
        # robert text
        if lena_robert_dating: # removed v9_luggage because Ian helped (no Alison trip)
            label v9roberttxt: # label is for any Robert branch except Axel sex
                play sound "sfx/sms.mp3"
                "Just then I got a text from Robert."
                nvl clear
                r_p "{i}Hey, baby! I haven't heard from you! You're back in town already, right?{/i}"
                l_p "{i}Yeah, just got back yesterday.{/i}"
                r_p "{i}So what are we waiting for? I really wanna see you {image=emoji_glasses.webp} {image=emoji_fire.webp}{/i}"
                if ian_lena_couple:
                    $ flena = "n"
                    l "I'm afraid it's time to kick Robert to the curb. I'm dating Ian now..."
                    if v9_lena_sex > 1 and v9_alison_trip:
                        l "Besides, I just had sex with Ian... I'm not in the mood to indulge Robert tonight."
                elif v9_lena_sex > 1 and v9_alison_trip:
                    l "I just had sex with Ian... I'm not in the mood to indulge Robert tonight."
                else:
                    if v9_alison_trip == False:
                        if lena_axel_dating:
                            l "..."
                            l "Maybe I could use a distraction tonight... To wash away this sour feeling I have right now..."
                        else:
                            l "I could use some fun tonight..."
                    else:
                        if v9_lena_sex == 0:
                            l "Maybe I could use some fun tonight..."
                        elif v9_lena_sex == 1:
                            l "I had fun in the park with Ian, but we couldn't go all the way. This could be a solution..."
                if v9_luggage_sex != "mike" and lena_mike_dating:
                    play sound "sfx/sms.mp3"
                    "I was about to give my answer to Robert, but suddenly I got another text."
                    "From Mike."
                    jump v9miketxt
                if ian_lena_couple:
                    $ v9_lena_robert_over = True
                    $ lena_robert_over = True
                    l_p "{i}Sorry Robert, but I'm dating someone now. It was fun but it's time to move on to other things. Good luck!{/i}"
                    "Robert texted something back, but I had already put my phone down and I wasn't feeling like explaining myself to him further."
                    "I was feeling tired, so I decided to go to bed. I needed a good night's sleep..."
                    jump v9lenasunday
                elif v9_lena_sex > 1 and v9_alison_trip:
                    l_p "{i}Sorry, I'm really tired today. Some other time!{/i}"
                    "Robert texted something back, but I had already put my phone down and I wasn't feeling like explaining myself to him further."
                    "I was feeling tired, so I decided to go to bed. I needed a good night's sleep..."
                    jump v9lenasunday
                else:
                    menu:
                        "Invite Robert over":
                            $ renpy.block_rollback()
                            $ v9_luggage_sex = "robert"
                            $ flena = "flirt"
                            l "Alright, I'll invite him over."
                            "I texted him back and told him to come to my place."
                            "Twenty minutes later, he was at the door..."
                            jump v9robertsx
                        "Ignore him":
                            $ renpy.block_rollback()
                            l "I'm not in the mood to indulge Robert tonight..."
                            l_p "{i}Sorry, I'm really tired today. Some other time!{/i}"
                            "Robert texted something back, but I had already put my phone down and I wasn't feeling like explaining myself to him further."
                            "I was feeling tired, so I decided to go to bed. I needed a good night's sleep..."
                            jump v9lenasunday
        # mike text
        elif lena_mike_dating: # removed v9_luggage because Ian helped (no Alison trip)
            play sound "sfx/sms.mp3"
            "Just then I got a text from Mike. I wasn't expecting one..."
            label v9miketxt: # label is for any Mike branch except Axel sex
                nvl clear
                if ian_lena_couple:
                    $ lena_mike_over = True
                    $ flena = "blush"
                    mk_p "{i}Hey babe, how are you doing? Are you back in town already?{/i}"
                    l_p "{i}Yeah, got back just yesterday! {image=emoji_smile.webp}{/i}"
                    mk_p "{i}Cool. The truth is I've been missing you... Are you busy right now? {image=emoji_flirt.webp}{/i}"
                    l "..."
                    l "I can't keep doing this now that I've decided to be with Ian. It was wrong to begin with, but now..."
                    l_p "{i}I'm sorry, Mike. I don't think we can keep seeing each other like that.{/i}"
                    mk_p "{i}Well, that was sudden.{/i}"
                    l_p "{i}I know, but things have changed recently...{/i}"
                    mk_p "{i}I get it, no worries. See you around! {image=emoji_wink.webp}{/i}"
                    l "..."
                    l "Well, that was easy... Disappointingly easy, even..."
                    $ flena = "n"
                    l "But it's for the best."
                    if lena_robert_dating:
                        $ v9_lena_robert_over = True
                        $ lena_robert_over = True
                        l "Now that I'm done with Mike, it's time to deal with Robert..."
                        nvl clear
                        l_p "{i}Sorry Robert, but I'm dating someone now. It was fun but it's time to move on to other things. Good luck!{/i}"
                        "Robert texted something back, but I had already put my phone down and I wasn't feeling like explaining myself to him further."
                    "I was feeling tired, so I decided to go to bed. I needed a good night's sleep..."
                    jump v9lenasunday
                else:
                    $ flena = "flirtshy"
                    mk_p "{i}Hey babe, how are you doing? Are you back in town already?{/i}"
                    l_p "{i}Yeah, got back just yesterday! Why, did you miss me? {image=emoji_crazy.webp}{/i}"
                    mk_p "{i}You have no idea. Are you busy right now? {image=emoji_flirt.webp}{/i}"
                    l_p "{i}Not really... Do you wanna come over?{/i}"
                    mk_p "{i}I'll be there in twenty minutes {image=emoji_glasses.webp}{/i}"
                    if v9_alison_trip == False:
                        if lena_axel_dating:
                            $ flena = "sad"
                            l "..."
                            $ flena = "flirtshy"
                            if v9_luggage != "robert" and lena_robert_dating:
                                l "If I'm gonna use a distraction tonight, I'd rather have Mike than Robert..."
                            else:
                                l "Maybe I could use a distraction tonight... To wash away this sour feeling I have right now..."
                        else:
                            l "I can't say no to Mike. He just fucks me so well..."
                    else:
                        if v9_lena_sex == 0:
                            l "This is exactly what I need right now..."
                        elif v9_lena_sex == 1:
                            l "I had fun in the park with Ian, but we couldn't go all the way. This is the perfect solution..."
                        else:
                            l "I just had sex with Ian... But I can't say no to Mike. He just fucks me so well..."
                    "I started getting horny while waiting for him, preparing myself to greet him..."
                    jump v9mikesx
        elif lena_mike_dating and ian_lena_couple or lena_robert_dating and ian_lena_couple:
            label v9mikeroberttxt:
                # if lena_mike_dating: - now only needed for v9_luggage == "robert" and v9_alison_trip; for Mike path v9miketxt covers everything
                    # $ lena_mike_over = True
                    # play sound "sfx/sms.mp3"
                    # $ flena = "worried"
                    # "Just then I got a text from Mike. I wasn't expecting one..."
                    # nvl clear
                    # mk_p "{i}Hey babe, are you busy right now? {image=emoji_flirt.webp}{/i}"
                    # l "..."
                    # l "I can't keep meeting with Mike now that I've decided to be with Ian. It was wrong to begin with, but now..."
                    # l_p "{i}I'm sorry, Mike. I don't think we can keep seeing each other like that.{/i}"
                    # mk_p "{i}Well, that was sudden.{/i}"
                    # l_p "{i}I know, but things have changed recently...{/i}"
                    # mk_p "{i}I get it, no worries. See you around! {image=emoji_wink.webp}{/i}"
                    # l "..."
                    # l "Well, that was easy... Disappointingly easy, even..."
                    # $ flena = "n"
                    # l "But it's for the best."
                    # if lena_robert_dating:
                        # $ v9_lena_robert_over = True
                        # $ lena_robert_over = True
                        # l "Now that I'm done with Mike, it's time to deal with Robert..."
                        # nvl clear
                        # l_p "{i}Sorry Robert, but I'm dating someone now. It was fun but it's time to move on to other things. Good luck!{/i}"
                        # "Robert texted something back, but I had already put my phone down and I wasn't feeling like explaining myself to him further."
                if lena_robert_dating:
                    play sound "sfx/sms.mp3"
                    $ flena = "worried"
                    "Just then I got a text from Robert."
                    nvl clear
                    r_p "{i}Hey baby, what are you doing right now? Can I drop by real quick? I really wanna see you {image=emoji_glasses.webp} {image=emoji_fire.webp}{/i}"
                    $ flena = "n"
                    l "I'm afraid it's time to kick Robert to the curb. I'm dating Ian now..."
                    $ v9_lena_robert_over = True
                    $ lena_robert_over = True
                    l_p "{i}Sorry Robert, but I'm dating someone now. It was fun but it's time to move on to other things. Good luck!{/i}"
                    "Robert texted something back, but I had already put my phone down and I wasn't feeling like explaining myself to him further."
                "I was feeling tired, so I decided to go to bed. I needed a good night's sleep..."
            jump v9lenasunday
        else:
            "I was feeling tired, so I decided to go to bed. I needed a good night's sleep..."
            jump v9lenasunday

## ROBERT SEX 2
label v9robertsx:
    scene lenahomenight_dark with long
    $ lena_look = 2
    $ frobert = "flirt"
    "I opened the door for him and he got his hands on my body right away."
    r "Hey, baby... I've missed you so much..."
    l "I see you're ready to party..."
    r "With you? Always."
    show lenabra2 at rig
    show robert at lef
    with short
    play music "music/sex.mp3" loop
    scene v9_robert1b
    if lena_tattoo2:
        show v9_mike1_t2
    with long
    "Robert couldn't wait to stick his cock inside me, and that suited me just fine."
    play sound "sfx/ah5.mp3"
    l "Oh, fuck yeah...!"
    if lena_lust < 9:
        call xp_up('lust') from _call_xp_up_110
    "These two awful long weeks away had been putting so much pressure on me. I had been feeling stressed and isolated."
    if v9_lena_sex > 0:
        "Ian had helped me release that tension a bit, but I needed more..."
        "I needed a good, rough fuck!"
    else:
        "I was in so much need to release that tension... And nothing better for that than a good, rough fuck!"
    "Robert, as always, was more than happy to help me scratch that itch."
    scene v9_robert2
    with long
    "I helped and encouraged his efforts by playing with my clit while he penetrated me tirelessly."
    r "You're so damn hot, Lena...! You turn me on like fucking crazy!"
    l "Keep fucking me... Harder! Yes, just like that!"
    "I was glad to see we hadn't lost our compatibility despite not seeing each other for some time."
    "It had taken a few tries, but Robert had finally found the right way to please me... And I was contributing to that with my own fingers, too."
    if lena_axel_desire:
        "Being on all fours for him turned me on so much. Robert had me at his entire disposition, using my body as he pleased."
        "Just like Axel used to do..."
    else:
        "He had me on all fours, enjoying myself and at his entire disposition."
    l "Oh, fuck, I'm gonna cum!"
    r "Yes, baby! Cum for me!"
    play sound "sfx/ah6.mp3"
    l "Mhhhhaaaahh!!" with vpunch
    pause 0.6
    with vpunch
    pause 0.6
    with vpunch
    pause 0.6
    "We kept going at it until late at night, enjoying each other's sweaty bodies."
    "We certainly made up for the lost time."
    stop music fadeout 2.0
    scene lenaroomnight with long
    $ flena = "smile"
    $ frobert = "flirt"
    show lenanude2 at rig
    show robertnude at lef
    with short
    l "Mmmhh... That was good."
    r "It was incredible, as it always is with you..."
    r "Damn, I can't get enough of you, baby!"
    l "I'm satisfied right now... I need to get some sleep."
    l "You can take a shower before leaving if you want."
    $ frobert = "sad"
    r "Oh..."
    $ frobert = "n"
    r "Alright. Let's meet again soon, okay?"
    l "I'll give you a call."
    scene lenaroomnight with long
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH09_S08")
    jump v9lenasunday
## MIKE SEX 2
label v9mikesx:
    scene lenahomenight_dark with long
    $ lena_look = 2
    $ fmike = "smile"
    $ flena = "shy"
    "Mike texted me again to let me know he was at the door and I welcomed him in."
    show lenabra2 at rig
    show mike at lef
    with short
    l "Hi...!"
    mk "Hey, babe. It's good to see you... How have you been?"
    $ flena = "n"
    l "Oh, well... I just came back from my parents' home. I had to go back for a couple of weeks..."
    mk "Oh, so that's where you've been. Family business?"
    $ flena = "sad"
    l "Well, yeah... Let's talk in my room."
    play sound "sfx/door.mp3"
    scene lenaroomnight with long
    $ fmike = "sad"
    "I told Mike about my mom's accident and the current situation."
    show lenabra at rig
    show mike at lef
    with short
    mk "Damn, that sucks. You didn't tell me anything."
    l "I didn't want to bother you..."
    $ fmike = "n"
    mk "Hey, it's no bother. There's not much I can do, but still..."
    mk "You can talk to me."
    menu v9miketalk2:
        "Ask about Mike's job"  if v9miketalka == False:
            $ renpy.block_rollback()
            $ v9miketalka = True
            $ flena = "n"
            l "How's work, by the way? Does DJing pay the bills?"
            $ fmike = "n"
            mk "Not really... I hope it will at some point. I'm working on some tracks..."
            $ flena = "smile"
            l "I'd love to listen to them!"
            mk "Sure, I'll show you one day."
            $ flena = "n"
            l "Making it in the music industry is really hard... I would know."
            mk "Yeah, but someone has to make it, so why not be me? This gig at Blazer is the first step to making a name for myself..."
            mk "I don't get paid much right now, but I will at some point. At least my girl has a stable job, so we can pay the bills!"
            if v9miketalka and v9miketalkb:
                call friend_xp('mike', 1) from _call_friend_xp_145
            jump v9miketalk2

        "Ask about Mike's girlfriend" if v9miketalkb == False:
            $ renpy.block_rollback()
            $ v9miketalkb = True
            $ flena = "n"
            l "So... How's it going with your girl?"
            $ fmike = "n"
            mk "It's okay..."
            l "Just okay?"
            mk "It's fine. It's my first time living together with my girlfriend, so I'm still getting used to it."
            mk "She's pretty busy with her new job though, so she gives me plenty of space, which it's nice. So far everything's been going alright."
            l "How long have you been with her?"
            mk "About a year... And we moved here like three months ago or so."
            l "You look like a pretty independent guy. I still find it hard to believe you'd settle for just one girl."
            $ fmike = "smile"
            mk "Well, you're making that hard, as I'm sure you're aware!"
            $ flena = "flirt"
            l "What can I say? It's not fair for her to have you all for herself!"
            $ fmike = "flirt"
            mk "You little vixen..."
            if v9miketalka and v9miketalkb:
                call friend_xp('mike', 1) from _call_friend_xp_146
            jump v9miketalk2

        "Have sex with Mike":
            $ renpy.block_rollback()
            stop music fadeout 2.0
            $ flena = "slutshy"
            l "I was hoping you'd give me a warm welcome, now that I'm back."
            $ fmike = "flirt"
            mk "That's exactly why I texted you... To welcome you back as you deserve."

    play music "music/dumb.mp3" loop
    mk "I can't stay long, so... Why don't we get down to it?"
    $ flena = "slut"
    l "With pleasure."
    if lena_lust < 9:
        call xp_up('lust') from _call_xp_up_111
    hide lenabra
    show lenanude2 at rig
    with short
    "I stripped down under Mike's watchful eyes, and he followed suit."
    hide mike
    show miketopless at lef
    with short
    $ flena = "slut"
    "I licked my lips lusciously as I ran my hands over Mike's ripped torso."
    l "You're so damn hot..."
    mk "Look who's talking..."
    hide miketopless
    show mikenude at lef
    with short
    mk "My cock's been like this for a while now."
    "I held his rock-hard shaft in my palm. Such a mouth-watering sensation..."
    "And that wasn't the only thing that was getting wet. I felt the warm moisture dripping between my thighs..."
    l "Fuck me, Mike...! I need you to fuck me so bad!"
    scene v9_mike1b
    if lena_tattoo2:
        show v9_mike1_t2
    with long
    play sound "sfx/ah5.mp3"
    l "Oh, fuck yeah...!"
    "This was just what I had been needing!"
    "These two awful long weeks away had been putting so much pressure on me. I had been feeling stressed and isolated."
    "I was in so much need to release that tension... And nothing better for that than a good, rough fuck!"
    "And no better partner for that than Mike... His way of banging me was like his personality:"
    "Direct and unapologetic, intense to the point of being overwhelming, but also playful and charming."
    scene v9_mike2 with long
    "The rhythm of his hips, fast and ruthless, was broken up by slick, slow pumps, only to suddenly return to loud, flesh-slapping pounding."
    "Those were the moves of a pro... I loved how he fucked me!"
    "And he was hot as hell, to boot. I couldn't help but start masturbating while he penetrated me tirelessly."
    if lena_axel_desire:
        "He had me on all fours, at his entire disposition, using my body as he pleased."
        "Just like Axel used to do..."
    else:
        "He had me on all fours, enjoying myself and at his entire disposition."
    l "Oh, fuck, I'm gonna cum!"
    if lena_anal > 1:
        mk "I want your ass, Lena!"
        scene v9_mike3
        with long
        "He shoved it in my rectum right away, without even taking the time to apply some lube to it."
        "He didn't need it."
        "My ass gulped down his entire shaft smoothly like it was a perfect sleeve for Mike's cock."
        mk "Fuck, your ass feels just like a pussy... It's awesome, Lena!"
        "It felt like that for me too. What intense pleasure I was feeling being ravaged by his hard, throbbing manhood!"
        if lena_lust < 9:
            call xp_up('lust') from _call_xp_up_112
        l "Oh, fuck! I'm cumming from my ass!"
        play sound "sfx/ah4.mp3"
        l "Oh fuck, Mikeeeaahhh!!!{w=0.5}{nw}" with vpunch
        pause 0.6
        with vpunch
        pause 0.6
        with vpunch
        pause 0.6
    else:
        play sound "sfx/ah4.mp3"
        l "Aaahhhh!!!{w=0.3}{nw}" with vpunch
        pause 0.6
        with vpunch
        pause 0.6
        with vpunch
        pause 0.6
    "We kept going at it until we were both sweaty, exhausted, and satisfied."
    "When Mike left only a few hours remained for sunrise..."
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH09_S09")
    jump v9lenasunday

##LENA SUNDAY ####################################################################################################################################################################################################################################
label v9lenasunday:
    stop music fadeout 2.0
    call calendar(_day="Sunday") from _call_calendar_17

    $ lena_look = 1
    $ flena = "n"
    play music "music/normal_day2.mp3" loop
    # axel sex
    if v9_axel_sex:
        $ flena = "sad"
        scene lenaroom with long
        "The light of the morning made what happened last night seem like some kind of dream."
        show lenabra with short
        "I still wasn't able to fully accept it. I was trying not to think about it."
        if ian_lena_couple:
            "Not to think about what it meant for me and Ian."
        "I would've liked to forget about it, but my sore pussy reminded me of the reality of what Axel and I had done."
        if lena_robert_dating or lena_mike_dating:
            "I picked up my phone, trying to escape the whirlwind of thoughts that was tormenting my mind."
            if lena_robert_dating and lena_mike_dating:
                l "Seems like Robert messaged me last night... and also Mike."
                "I read Robert's text first."
            elif lena_robert_dating:
                l "Seems like Robert messaged me last night..."
            elif lena_mike_dating:
                l "Seems like Mike messaged me last night..."
            if lena_robert_dating:
                nvl clear
                if v9_luggage == "robert":
                    r_p "{i}Hey baby, what are you doing right now? Can I drop by real quick? I really wanna see you {image=emoji_glasses.webp} {image=emoji_fire.webp}{/i}"
                else:
                    r_p "{i}Hey, baby! I haven't heard from you! You're back in town already, right?{/i}"
                "I wasn't in the mood to meet him. Far from it..."
                if ian_lena_couple:
                    $ v9_lena_robert_over = True
                    $ lena_robert_over = True
                    "Besides, I already decided to kick him to the curb, since I was dating Ian now..."
                    l "I already fucked up, but..."
                    l_p "{i}Sorry Robert, but I'm dating someone now. It was fun but it's time to move on to other things. Good luck{/i}"
                    "Robert texted something back, but I had already put my phone down and I wasn't feeling like explaining myself to him further."
                else:
                    play sound "sfx/sms.mp3"
                    if v9_luggage == "robert":
                        l_p "{i}Sorry, I'm really tired. Some other time!{/i}"
                    else:
                        l_p "{i}Yeah, just got back yesterday, but I'm really busy these days.{/i}"
                if lena_mike_dating:
                    l "I also gave Mike the same treatment."
                    if ian_lena_couple:
                        $ lena_mike_over = True
                    else:
                        l "The last thing I wanted at that moment was to hook up with someone..."
            elif lena_mike_dating:
                nvl clear
                if v9_luggage_sex == "mike":
                    mk_p "{i}Hey babe, are you busy right now? {image=emoji_flirt.webp}{/i}"
                    l_p "{i}Yeah, kinda... {image=emoji_sad.webp}{/i}"
                else:
                    mk_p "{i}Hey babe, how are you doing? Are you back in town already?{/i}"
                    l_p "{i}Yeah, got back just yesterday, but I'm really busy... {image=emoji_sad.webp}{/i}"
                play sound "sfx/sms.mp3"
                mk_p "{i}Really? The truth is I've been missing you... Are you sure you're busy today? {image=emoji_flirt.webp}{/i}"
                l "..."
                if ian_lena_couple:
                    $ lena_mike_over = True
                    l "I can't keep this thing going now that I've decided to be with Ian. I already fucked up, but..."
                    l_p "{i}I'm sorry, Mike. I don't think we can keep seeing each other like that.{/i}"
                    mk_p "{i}Well, that was sudden.{/i}"
                    l_p "{i}I know, but things have changed recently...{/i}"
                    mk_p "{i}I get it, no worries. See you around! {image=emoji_wink.webp}{/i}"
                    l "..."
                    l "Well, that was easy... Disappointingly easy, even..."
                    l "But it's the only thing I could do."
                else:
                    l_p "{i}I'm sorry. Let's meet another day. {image=emoji_sad.webp}{/i}"
                    mk_p "{i}Alright, I'll wait. Let me know when you want to see me!{/i}"
            l "Well, that's that..."
    # axel shoot
    elif lena_axel_dating:
        scene lenaroom with long
        "I didn't manage to sleep as much as I would've liked, but I woke up feeling calmer the next morning."
        show lenabra with short
        "I still hadn't washed out the taste of yesterday's shoot with Axel, but I felt that was behind, now."
        if v9_axel_kiss != "n":
            $ flena = "sad"
            "Obviously, Axel didn't feel the same, but I already knew that..."
            $ flena = "n"
        else:
            "I hoped Axel was moving on, too, for both our own good."
    # else
    else:
        scene lenaroom with long
        "I got up reasonably early the next morning."
        show lenabra with short
        "Even though I hadn't slept that much, I felt the need to jump out of bed. I didn't feel like sleeping in..."
    # louise
    play sound "sfx/door.mp3"
    scene lenahome
    show lenabra
    with long
    if v9_axel_sex:
        "I had a knot in my stomach but went to the kitchen to force myself to have some breakfast."
    else:
        "I went to the kitchen to get some breakfast."
    show lenabra at rig with move
    $ flouise = "n"
    $ louise_look = 2
    show louisebra at lef with short
    l "Oh. Good morning Louise..."
    if lena_louise_sex and lena_reject_louise == False:
        lo "..."
        hide louisebra with short
        $ flena = "worried"
        l "Did she just give me the silent treatment?"
        $ flena = "sad"
        l "Jesus... It really was a bad idea to get in bed with her."
        l "I don't have the energy to deal with this right now. I'll speak with her some other time."
    elif louise_jeremy or lena_reject_louise:
        lo "Hey..."
    else:
        lo "Good morning. Want some coffee?"
        l "Yes, please."
    if lena_louise_sex == False or lena_reject_louise:
        if v9_axel_sex:
            $ flouise = "sad"
            lo "Is everything alright? You don't look so good..."
            $ flena = "worried"
            l "Oh."
            "Was my mental state that obvious?"
            "For a second I thought about telling Louise what had happened, but I wasn't ready to speak about it yet..."
            $ flena = "n"
            l "I'm alright... I just had trouble sleeping."
            lo "How was yesterday's shoot with your ex? Everything alright?"
            $ flena = "sad"
            l "It was... okay."
            "I only told Louise about some parts, omitting a lot of what had happened."
            $ flouise = "serious"
            hide louisebra
            show louisebra2 at lef
            with short
            lo "I already told you Ivy is not to be trusted. What a shitty friend she is."
            l "Yeah, well... It is what it is..."
            lo "You should be more upset! If a friend left me hanging like that, I would be fuming!"
            l "I am, I mean... Never mind."
            $ flouise = "sad"
            lo "You're too soft with her..."
        elif lena_axel_dating:
            $ flouise = "sad"
            lo "How was yesterday's shoot with your ex? Everything alright?"
            if v9_axel_kiss != "n":
                $ flena = "sad"
                l "Not exactly..."
            else:
                l "Yeah, sort of..."
            "I brought Louise up to speed about what had happened."
            $ flouise = "serious"
            hide louisebra
            show louisebra2 at lef
            with short
            lo "I already told you Ivy is not to be trusted. What a shitty friend she is."
            $ flena = "serious"
            if lena_ivy > 5:
                l "She messed up, yeah..."
                lo "You're too soft with her..."
                $ flena = "sad"
            else:
                l "I'm really upset with her."
            lo "I've always told you, you need to be careful around that viper!"
        if louise_jeremy or lena_reject_louise:
            "I chatted with Louise while we had breakfast. I could tell the awkward vibe between us was still there, but I was kinda getting used to it at this point..."
            if ian_lena_couple:
                "I told her Ian and I finally had {i}the talk{/i} and how we had agreed to start dating seriously."
                $ flouise = "sad"
                lo "Really? I thought you didn't want a serious relationship..."
                l "It's different with him."
            elif ian_lena_dating:
                "I told her Ian and I finally had {i}the talk{/i} and how we had decided to keep things casual."
                lo "If that's how you two feel... You never wanted a serious relationship to begin with, right?"
            elif ian_lena_breakup:
                "I told her Ian and I finally had {i}the talk{/i} and how that ended up."
                $ flouise = "sad"
                lo "Oh, I see... He's an idiot if you ask me, but too bad for him..."
        else:
            "I chatted with Louise while we had breakfast. I was hoping some strong coffee would clear my mind and help me get started with my day."
            if ian_lena_couple:
                "I told her Ian and I finally had {i}the talk{/i} and how we had agreed to start dating seriously."
                $ flouise = "happy"
                lo "Really? Congratulations!"
                $ flouise = "sad"
                lo "Now I'm jealous..."
            elif ian_lena_dating:
                "I told her Ian and I finally had {i}the talk{/i} and how we had decided to keep things casual."
                lo "If that's how you two feel... You never wanted a serious relationship to begin with, right?"
            elif ian_lena_breakup:
                "I told her Ian and I finally had {i}the talk{/i} and how that ended up."
                $ flouise = "sad"
                lo "That sucks... He sounds a bit clueless if you ask me!"
                l "I think it's just the opposite..."
        "I had a second cup of coffee before getting started with my day."
    else:
        "I prepared a strong cup of coffee to clear my mind and help me get started with my day."
    # tattooing
    if v9_tat > 0 or v9_piercing > 0:
        $ flena = "n"
        l "I have an appointment with Jessica today. I should get going..."
        scene sexshop with long
        show lena2 at rig
        if jess_bad:
            show jessb at lef
        else:
            show jessg at lef
        with short
        if v9_tat > 0:
            if lena_tattoo1 == False and lena_tattoo2 == False and lena_tattoo3 == False:
                "It was my first time getting a tattoo, so I was pretty nervous..."
                "Jess' needle traced paths on my skin, painfully embedding the lines that were bound to remain with me for the rest of my life."
            else:
                "It wasn't my first time getting a tattoo, so I knew what to expect."
            play sound "sfx/tattoo.mp3"
            scene sexshop with long
            if v9_tat == 1:
                $ lena_tattoo1 = True
                $ flena = "happy"
                "She was right: it was painful, but nothing I couldn't endure, and she finished it pretty quickly."
            elif v9_tat == 2:
                $ lena_tattoo2 = True
                $ flena = "smile"
                "It wasn't a pleasant experience, but I could withstand the pain. I felt excited about getting this tattoo, which made it easier to bear."
            elif v9_tat ==3:
                $ lena_tattoo3 = True
                $ flena = "worried"
                "I would lie if I said it wasn't a challenging experience. I had to endure the bite of the needle for several hours..."
                "But it was worth it. I felt excited and happy to finally get this tattoo."
            if v9_piercing > 0:
                if v9_piercing == 1:
                    $ lena_piercing1 = True
                else:
                    $ lena_piercing2 = True
                "And to finish things off, Jess gave me a navel piercing. It was quick and less painful than I had anticipated."
            "I bought a lotion to take care of my skin during the following days and I went back home."
        elif v9_piercing > 0:
            "It was quick and less painful than I had anticipated."
            "In a matter of minutes, I had my first piercing done."
            scene sexshop with long
            if v9_piercing == 1:
                $ lena_piercing1 = True
            else:
                $ lena_piercing2 = True
            "After that was done I went back to my place."

    scene lenaroom with long
    $ flena = "n"
    $ lena_look = 1
    show lena with short
    if cafe_help:
        l "It's back to work at the café tomorrow. I need to keep helping the Van Dykes with their business..."
        if cafe_nude:
            l "I'll ask them to schedule another life drawing event this week. Hopefully, I can get a cut of the profits this time."
        else:
            l "I'll ask them to schedule a life drawing event this week since I can't work at the gallery... Hopefully, I can get a cut of the profits."
    else:
        l "I should start searching for another job, just in case. The Van Dykes are bound to sell the café any moment now."
        "I did a bit of job hunting and sent a couple of e-mails, hoping I would get a response soon."
    l "I should work on my music, too. I haven't been able to play during these two weeks..."
    play sound "sfx/guitar.mp3"
    scene lena_guitar1 with long
    "I started practicing and revising some of the songs I had been writing lately."
    "My first concert had been okay, but I still felt clumsy. If I wanted to make something of this I would need to take it seriously..."
    if v9_axel_sex:
        $ flena = "blush"
        scene lenaroom
        show lena
        with short
        "But I just couldn't concentrate."
        "I felt weird in my own skin, still shaken by what had happened with Axel."
        "It still felt like some kind of dream, or like it had happened to someone else, not me."
        if ian_lena_couple:
            "And Ian... I couldn't even bring myself to think about it. Like if I ignored those thoughts I could escape the actual reality of things."
        l "This is messing with my head... I can't keep my mind focused on just one thing."
    else:
        "And that meant practicing and studying music for real."
        l "If I want to write my own songs I should probably take some piano and music theory lessons."
        l "And people told me I should learn to record and produce my own tracks..."
        scene lenaroom
        show lena
        with short

# SEYMOUR CALL ######################
    stop music fadeout 2.0
    if lena_job_seymour and v6_axel_pose == 1:
        $ seymour_disposition = 1
    play sound "sfx/ring.mp3"
    l "Hm? I don't know this number..."
    hide lena
    show lena_phone
    with short
    l "Yes?"
    play music "music/seymours_theme.mp3" loop
    show phone_seymour at lef3 with short
    mr "Hello, Lena."
    if seymour_disposition > 1:
        $ flena = "smile"
        l "Mr. Ward! I was wondering when I'd hear from you again..."
        $ flena = "worried"
        l "I'm so sorry about what happened that last night! I had no idea Emma would..."
        mr "Don't worry, that wasn't your fault."
    elif seymour_disposition == 1:
        $ flena = "sad"
        l "Mr. Ward... I wasn't sure if I'd hear from you again after what happened that night..."
        mr "Of course. That wasn't your fault, after all."
    else:
        $ flena = "serious"
        l "It's you... What do you want?"
    hide phone_seymour
    show phone_seymour_evil at lef3
    mr "I have a proposition for you."
    # works with seymour
    if lena_job_seymour and seymour_disposition > 0:
        if v6_axel_pose == 1:
            $ flena = "serious"
            l "I'm not sure I'm comfortable working with you again after that last photo shoot."
            mr "I understand. Let me apologize for that by inviting you to have dinner."
            l "I appreciate the apology, but you don't need to take me out. I'd rather have us leave things as they are."
            mr "I'd rather not. At least I'd like you to hear me out."
            mr "As I said, I have a proposal that you might find very interesting."
            $ flena = "sad"
            l "What kind of proposal?"
            mr "I'll tell you during dinner. That's the appropriate way of conducting business at this level."
            jump v9sycall
        else:
            l "Another photo shoot?"
            mr "Not right away. I'd like to invite you to have dinner before we have another one."
            if seymour_disposition > 1:
                $ flena = "smile"
                l "Sure, I'd love that."
                mr "Good. I have a proposal that you might find very interesting."
                $ flena = "happy"
                l "Really? What's it about?"
                mr "I'll tell you during dinner. That's the appropriate way of conducting business at this level."
                $ flena = "smile"
                l "Oh, yeah, sure... Which night?"
                if seymour_necklace:
                    mr "Tomorrow. I'll pick you up. Don't forget to dress elegantly..."
                    mr "Oh, and wear that Addingworth necklace I gave you. It looked beautiful on you."
                    if v7_necklace_sell:
                        $ flena = "worried"
                        l "Um, sure..."
                        $ flena = "n"
                        l "I'll see you this Monday, Mr. Ward."
                    else:
                        l "Alright! See you this Monday, Mr. Ward."
                else:
                    mr "Tomorrow. I'll pick you up. Oh, and dress elegantly."
                    l "Alright! See you this Monday, Mr. Ward."
                hide phone_seymour_evil
                hide lena_phone
                show lena2
                with short
                l "It seems he wants to keep working with me!"
                $ flena = "worried"
                l "Maybe I should've rejected his invitation, after what Emma told me... I doubt she'd appreciate knowing I'm in business with him..."
                $ flena = "n"
                l "But right now that's exactly what I need. With a patron like him, I will probably be able to solve my money problems."
            else:
                l "Is that really necessary?"
                mr "I'd say it is. It's the right way to conduct business at this level."
                mr "As I said, I have a proposal that you might find very interesting."
                $ flena = "sad"
                l "What kind of proposal?"
                mr "Again, I'll tell you during dinner. Tomorrow."
                "I wondered if it was a good idea to keep working with Seymour, especially after what Emma told me..."
                menu v9sycall:
                    "Accept":
                        $ renpy.block_rollback()
                        if v6_axel_pose == 1:
                            $ seymour_disposition = 1
                            "I wasn't exactly comfortable working with Mr. Ward, but that could solve my money problems for the time being..."
                            "And he was apologizing. Maybe I could hear him out..."
                            l "Alright..."
                        else:
                            $ flena = "n"
                            l "I suppose I could hear you out..."
                        mr "Wonderful."
                        call friend_xp('seymour', 1) from _call_friend_xp_147
                        if seymour_necklace:
                            mr "Tomorrow night. I'll pick you up. Don't forget to dress elegantly..."
                            mr "Oh, and wear that Addingworth necklace I gave you. It looked beautiful on you."
                            if v7_necklace_sell:
                                $ flena = "worried"
                                l "Um, sure..."
                                $ flena = "n"
                        else:
                            mr "Tomorrow night. I'll pick you up. Oh, and dress elegantly."
                        mr "Until then, Lena."
                        hide phone_seymour_evil
                        hide lena_phone
                        show lena2
                        with short
                        l "I'll go and hear what that proposal is, and see where to go from there."
                        $ flena = "sad"
                        l "God knows I could use the money, and with a patron like him..."
                        l "I doubt Emma would appreciate knowing I'm in business with him, though."
                        if seymour_disposition > 1:
                            $ flena = "n"
                            l "Working with him has been okay so far, so..."
                        l "We'll see how that goes..."

                    "Refuse":
                        $ renpy.block_rollback()
                        $ flena = "serious"
                        l "I'm sorry, but I'm not interested in doing business with you anymore, Mr. Ward."
                        hide phone_seymour_evil
                        show phone_seymour at lef3
                        l "I hope you understand."
                        mr "Are you sure about your answer?"
                        l "Yes. Goodbye."
                        jump v9seymourreject

    # avoids seymour
    else:
        if lena_job_seymour:
            l "I already told you: I'm not interested in working with you anymore. That hasn't changed."
        else:
            l "How many times do I need to tell you? I'm not interested in working with you."
        mr "I want to invite you to have dinner."
        if v3_seymour_date:
            l "No thanks, once was already enough. Not interested in doing that again."
        else:
            l "I already refused once. What makes you think I'd agree this time?"
        mr "This time it will be different. As I said, I have a proposal that you might find very interesting."
        "I could recall very well how arrogant and despicable he was with me when I rejected his last offer."
        l "Nothing you can offer me interests me. Please do not call me again."
        label v9seymourreject:
            $ seymour_disposition = 0
        hide phone_seymour
        hide phone_seymour_smile
        show phone_seymour_evil at lef3
        mr "Before you hang up, there's something I need you to know."
        "I should've ended the call there, but I didn't. Something made me want to hear those words."
        mr "I need you to know that nobody will give you work as a model in this city. I made sure of that."
        $ flena = "worried"
        l "Wha--?"
        mr "Not Danny, not the art gallery, and not anyone who's a dime."
        if lena_axel_dating:
            mr "Not even Axel. You'll never make it to Wildcats."
            "He also knew about my photo shoot with Axel?"
        $ flena = "mad"
        l "Who do you think you are!?"
        if cafe_help:
            mr "And that café you've been working at, well, I've decided to buy it."
            $ flena = "worried"
            "What? I had no idea about that...!"
            mr "Mr. Van Dyke was so happy to hear my offer."
        else:
            mr "And that café you've been working at, well, I've decided to buy it. At a very cheap price, I might add."
        mr "You won't be hired by the new management. Nor by anyone in any of my businesses."
        $ flena = "mad"
        if lena_robert_sex:
            "Me getting fired from the restaurant surely was his doing, too!"
        l "Thankfully you don't own every business in Baluart!"
        mr "Not yet, at least. But I don't need to, to make sure you don't get a decent wage in this city."
        mr "And do you realize how easy it is to get your social media accounts blocked?"
        $ flena = "worried"
        mr "I imagine it'd be hard for you to conduct business without a Peoplegram profile."
        if v5_hand_proposal_lena or v5_hand_proposal:
            mr "Not to mention that friend of yours, Ian Watts, would never see his manuscript published..."
            if v5_ian_showup:
                mr "I think we even hired him recently... That might change, of course, depending on your answer."
            $ flena = "mad"
            l "Why are you involving him in this? You're despicable!"
        elif v5_ian_showup:
            mr "Oh, and I think we recently hired that friend of yours, Ian Watts..."
            mr "That might change, of course, depending on your answer."
            $ flena = "mad"
            l "Why are you involving him in this? You're despicable!"
        $ flena = "mad"
        l "What's wrong with you!? Don't you have more important things to do with your time?"
        l "Why can't you just leave me alone!?"
        hide phone_seymour_evil
        show phone_seymour at lef3
        mr "I like to get what I'm after."
        l "And what are you after, exactly?"
        hide phone_seymour
        show phone_seymour_smile at lef3
        mr "A dinner, and for you to hear my proposal. That's all."
        l "Do you intend to blackmail me into accepting that proposal, too?"
        mr "As I see it, I'm just offering a deal and informing you of the consequences, before you decide."
        l "I wonder if the police would see things the same way."
        hide phone_seymour_smile
        show phone_seymour_evil at lef3
        mr "Go ask them. I'm really curious about the answer they'll give you."
        $ flena = "worried"
        "The way he said it gave me a chill. What was he insinuating...?"
        hide phone_seymour_evil
        show phone_seymour_smile at lef3
        mr "Listen, you treat me like a villain because I've only told you what's there for you to lose."
        $ flena = "serious"
        mr "Come have dinner with me and I'll tell you about what you can gain, which greatly outweighs the negatives."
        if v5_hand_proposal_lena or v5_hand_proposal:
            l "If I go, will you leave Ian out of this? And everyone else, too."
            mr "Yes."
            l "Will you then leave me alone even if I reject your proposal?"
        else:
            l "If I agree to go, will you leave me alone even if I reject that proposal?"
        mr "We'll talk more about that tomorrow night. I'll pick you up."
        if seymour_necklace:
            mr "Dress elegantly... Oh, and wear that Addingworth necklace I gave you. It looked beautiful on you."
        else:
            mr "Oh, and dress elegantly."
        mr "Until then, Lena."
        hide phone_seymour_smile
        hide lena_phone
        show lena2
        with short
        $ flena = "worried"
        l "..."
        l "I can't believe this... This man is as vile and dangerous as Emma described."
        "I hadn't felt so scared and helpless in a very long time..."
        l "Does he really have the power to mess with my life this way?"
        if v9_axel_sex:
            "My head was already messed up with what had happened with Axel. And now this...?"
        "It seemed I had no choice but to meet him and hear what he had to say."
        "I was sure I wouldn't like it..."
    stop music fadeout 2.0

# MONDAY  #################################################################################################################################################### ####################################################################################################################################################
    call calendar(_day="Monday", _week=4) from _call_calendar_18

    $ lena_look = 1
    $ fmolly = "smile"
    scene cafe with long
    play music "music/normal_day2.mp3"
    "Monday came and I resumed work at the café after my two-week absence."
    if seymour_disposition == 0:
        $ flena = "sad"
        show lena with short
        "I had so much on my mind..."
        "My conversation with Seymour last night over the phone had me really on edge."
        "I wanted to believe he was mostly bluffing, but the truth was I seemed to be in deep trouble."
        "I had no idea how to deal with a man like him. I was genuinely scared..."
        if v9_axel_sex:
            "And Axel... I was trying not to think about that, but I failed every time."
        show lena at rig with move
        show molly at lef with short
        mo "Lena! So glad to see you again..."
        $ fmolly = "sad"
        mo "Is everything alright? You look a bit... concerned."
        $ flena = "worried"
        l "I'm okay..."
    elif v9_axel_sex:
        $ flena = "sad"
        show lena with short
        "I had so much on my mind, but one thing above all the others: Axel."
        "I was trying not to think about what happened, but I failed every time."
        show lena at rig with move
        show molly at lef with short
        mo "Lena! So glad to see you again..."
        $ fmolly = "sad"
        mo "Is everything alright? You look a bit... concerned."
        $ flena = "worried"
        l "I'm okay..."
    else:
        $ flena = "n"
        show lena with short
        l "Good morning!"
        show lena at rig with move
        show molly at lef with short
        mo "Lena! So glad to see you again."
        mo "How are things back home?"
        l "They're okay, all things considered..."
    if cafe_perry:
        $ fperry = "n"
        show lena at rig3
        show molly at lef3
        with move
        show perry with short
        p "Good morning, Lena."
        l "Oh, Perry. You're here today, too?"
        p "Yeah. Today Ed's g--{w=0.5}gonna teach me how to cook french toast!"
        mo "Perry was supposed to help us for a few days during your absence, but it seems like he'll stick around for a bit longer."
        l "Really? So you'll be my co-worker?"
        p "Who knows. So far I'm liking it here... But I'm not working, I'm just helping."
        mo "He's such a good lad!"
        hide perry with short
        show lena at rig
        show molly at lef
        with move
    $ fmolly = "n"
    mo "How's your mother doing?"
    $ flena = "n"
    l "She'll be okay... What about you, Molly?"
    $ flena = "sad"
    l "I came by the other day and Ed told me you weren't feeling so well lately."
    if cafe_help:
        mo "I'll be fine... These couple of weeks have been a bit more intense than usual because we've been getting more customers!"
        mo "And we have to thank you for that..."
        l "Speaking of which... Any news about selling the café? Did you get any offers?"
        $ fmolly = "sad"
        mo "We did, in fact... I've been wanting to tell you about it."
        $ flena = "worried"
        if seymour_disposition == 0:
            "So what Seymour told me was true..."
        mo "Someone finally reached out to us. He's interested in buying the café and the offer is not that bad..."
        $ fmolly = "serious"
        mo "Ed wanted us to take it. But I'm not ready to part with this café."
        $ fmolly = "n"
        mo "Not yet. I have the feeling we've just started to turn things around..."
        mo "I want to keep pushing."
        $ flena = "n"
        l "So you've decided against selling?"
        mo "Yes. For the time being, at least..."
        mo "You've given us hope, Lena. And I believe we can keep working to make things better, thanks to you."
        $ flena = "happy"
        l "I'm glad to hear!"
        $ flena = "smile"
        if cafe_music:
            l "I will play again. And I could also host my own life drawing events here since it seems the gallery won't hire me..."
        else:
            l "I'll keep hosting my own drawing events here. It's perfect since it seems the gallery won't hire me anymore."
            l "And I can play here someday, too. I'll probably be doing more concerts from now on..."
        $ fmolly = "smile"
        mo "That'd be fantastic, Lena!"
        mo "I feel so full of strength just by thinking about it! Let's get to work!"
        call friend_xp('molly') from _call_friend_xp_148
        $ lena_molly = 12
        l "Yeah."
    else:
        mo "I'm okay, I've just been a bit... low on enthusiasm lately."
        $ flena = "n"
        l "Oh."
        l "Haven't you managed to solve your financial problems?"
        mo "Seems like we did, yeah... Someone finally reached out to us about buying the café."
        if seymour_disposition == 0:
            "So Seymour was telling the truth... It was time to start searching for a new job."
        else:
            "So it was time to start searching for a new job..."
        $ flena = "n"
        l "That's good news, isn't it?"
        mo "What they're paying us is nothing to write home about, frankly... But it'll do."
        mo "I'm sorry we're leaving you without a job in these difficult times, Lena. We'll put in a good word for you..."
        l "It's okay, you don't have to worry about that. I'll manage, I always do."
        l "Now, until you close the deal, let's get to work."
    scene cafe with long
    show lenawork with short
    if cafe_help:
        "It was kind of reassuring getting back to my usual routine. It made me feel like I had something solid to stand on."
        if seymour_disposition == 0 or v9_axel_sex:
            $ flena = "sad"
            "There were some things threatening that stability, however. But I tried to keep them away from my mind."
        else:
            "It wasn't much, but it was something. And I still had faith in being able to build myself up..."
    else:
        "Getting back to my usual routine wasn't too exciting, but it didn't matter: this would be over soon, too."
        if seymour_disposition == 0 or v9_axel_sex:
            $ flena = "sad"
            "It seemed I couldn't find a single solid thing to stand on these days, with so much going on..."
        else:
            "It didn't catch me by surprise, of course. I would've been able to plan ahead a bit more if Mom hadn't had that accident..."
    if holly_gym == False:
        show lenawork at rig with move
        $ flena = "n"
        if v8_holly_sex == "lena":
            $ fholly = "shy"
            show holly3 with short
            h "Hi, Lena...!"
            $ flena = "shy"
            l "Holly! I'm so glad to finally see you!"
            h "Yeah, me too. I wanted to drop by earlier, but you know..."
            "This was the first time I saw Holly since what had happened between us..."
            "We hadn't really talked about it, even though we texted often during these past two weeks."
        else:
            $ fholly = "happy"
            show holly at lef with short
            h "Hi, Lena!"
            $ flena = "happy"
            l "Holly! I'm so glad to finally see you!"
            h "Yeah, me too. I wanted to drop by earlier, but you know..."
        $ flena = "smile"
        l "Yeah, you've been busy. How was the conference?"
        h "It was fun... But most importantly, how are you?"
        if seymour_disposition == 0 or v9_axel_sex:
            $ flena = "sad"
            if seymour_disposition == 0 and v9_axel_sex:
                "I didn't want to tell her about my problems with Seymour, or about Axel..."
                "I tried to appear positive."
            elif v9_axel_sex:
                "I didn't want to tell her about my fuck up with Axel... I tried to appear positive."
            else:
                "I didn't want to tell her about my problems with Seymour... I tried to appear positive."
        $ flena = "n"
        l "I'm managing, you know. Trying to get back in the groove of things."
        $ fholly = "smile"
        hide holly3
        hide holly2
        show holly2 at lef
        with short
        h "Will you be playing again soon?"
        l "So it seems... I don't know where yet, but I have a couple people interested."
        $ fholly = "happy"
        h "That's great! I can't wait to hear you play again!"
        $ flena = "shy"
        if lena_go_holly == 4 and v8_holly_sex != "lena":
            "The memory of the moment when I almost kissed her came to mind."
            "I still didn't understand why I suddenly felt that way. She was just so adorable..."
        l "I've been preparing a new song these days. Would you like to come home sometime and help me check it out?"
        $ fholly = "shy"
        hide holly2
        show holly3 at lef
        with short
        h "Of course... You know I love hanging out with you..."
        if v8_holly_sex == "lena":
            $ fholly = "blush"
            h "And I, uh..."
            l "What is it?"
            h "No, nothing."
            $ fholly = "shy"
            h "I'm glad you're back."
            $ flena = "smile"
            l "I'm glad to be back, too."
        else:
            $ flena = "n"
            l "Let's hang out soon. I can't imagine anyone better to help me write a song!"
            $ fholly = "smile"
            h "Whenever you want."
    scene cafe with long
    stop music fadeout 2.0
    if lena_axel_dating:
        $ flena = "sad"
        "When my shift ended I decided to head down to the gym. I wasn't sure I was in the right mood to see Ivy, but I thought doing some exercise could help me release some tension."
        if holly_gym:
            "I really needed it... Besides, I would get to see Holly, too."
        else:
            "I really needed it..."
    else:
        "When my shift ended I decided to head down to the gym. I hadn't attended Ivy's class for a while now..."
        if seymour_disposition == 0:
            $ flena = "sad"
            "I thought doing some exercise could help me release some tension. I really needed it..."
        if holly_gym:
            $ flena = "n"
            "Besides, I would get to see Holly, too."
    play music "music/jeremys_theme.mp3" loop
    scene polegym with long
    $ lena_look = 2
    $ ivy_look = 2
    $ fivy = "n"
    show lena at rig
    show ivy at lef
    with short
    v "Lena! Hey!"
    # axel
    if lena_axel_dating:
        $ flena = "serious"
        if axel_disposition == 0 and v9_axel_kiss == "n":
            l "Don't \"hey\" me. I'm still mad at you."
            $ fivy = "sad"
            v "I already said I'm sorry..."
            l "Yeah, well, sometimes that alone just doesn't cut it."
            v "What would you have me do? I had no idea things would turn out this way, traffic was..."
            l "I don't want to talk about it right now, okay?"
            v "Sure... Jeez."
        else:
            l "Don't \"hey\" me. You know I'm really mad at you, don't you?"
            $ fivy = "sad"
            v "I know, I'm sorry... I had no idea things would turn out this way, traffic was..."
            l "Please, don't give me bullshit excuses. You know this was hard for me, and you promised you'd be there."
            v "That was my plan... Was it that bad? What happened?"
            if v9_axel_sex:
                $ flena = "blush"
                l "It was, uh..."
                l "It was okay."
                $ fivy = "n"
                "Something prevented me from telling Ivy what had actually happened. I felt ashamed and guilty..."
                "Pretending nothing happened was the only way I found to keep those feelings away. Otherwise, they would overwhelm me."
                v "So, no reason for you to get so mad, right?"
                $ flena = "serious"
                l "You know there is!"
                v "Alright, alright, jeez... I know I messed up, I'm just trying to say I'm sorry..."
            elif v9_axel_kiss == "wait":
                $ flena = "worried"
                l "He kissed me."
                $ fivy = "surprise"
                v "What!? He did?"
                l "Yeah."
                v "And what did you do?"
                $ flena = "serious"
                l "I pushed him away!"
                $ fivy = "sad"
                v "Jeez... That must've been uncomfortable."
                l "It was! If you had been there he wouldn't have tried to pull that off!"
                v "I'm sorry..."
                l "I hope you are. You left me hanging."
            elif v9_axel_kiss == "push":
                $ flena = "mad"
                l "He tried to kiss me!"
                $ fivy = "surprise"
                v "What!? He did?"
                l "Yeah, he did! I had to push him away!"
                $ fivy = "sad"
                v "Jeez... That must've been uncomfortable."
                $ flena = "serious"
                l "It was! If you had been there he wouldn't have tried to pull that off!"
                v "I'm sorry..."
                l "I hope you are. You left me hanging."
        v "So... How's your mom doing?"
    # normal
    else:
        v "Finally! I've missed you!"
        l "I'm glad to be back."
        v "Things aren't the same without you... How's your mom doing?"
    menu:
        "Chat with Ivy":
            $ renpy.block_rollback()
            $ flena = "sad"
            l "She'll manage. But she won't be able to work for quite some time, and my dad is in no shape to get a job right now."
            v "But they have some savings, right?"
            l "Not much, really. They manage for now, but they could really use some of my help."
            v "You've been sending them money all this time."
            if lena_money_family == 2:
                l "That's the only thing that reassures me a bit. But it's not enough to solve their problems."
            elif lena_money_family == 1:
                l "Only when I can, and that's not often..."
            else:
                l "Not really... I haven't, lately..."
            if stalkfap_pro == 2:
                $ fivy = "smile"
                v "But you're making money with Stalkfap, right?"
                $ flena = "n"
                l "A bit, yeah... I don't have a lot of subscribers, but making exclusive content for them pays off."
                v "See? I told you. I don't have that many subscribers either, but if you give them what they want..."
                v "They'll be happy to reward you. And it takes so little effort, honestly!"
            elif stalkfap:
                v "Isn't Stalkfap making you some money?"
                l "Barely. It's not like I'm famous enough to make people want to subscribe..."
                $ fivy = "n"
                v "It's not just about the fame. I don't have that many Peoplegram followers, either."
                l "You have way more than I do."
                v "Yes, but it's also about the content. If you give them what they want..."
                if stalkfap_pro:
                    l "I know, I know. I'm already trying that."
                else:
                    l "I know, I know."
            else:
                v "Maybe it's time for you to really give Stalkfap a try. It can only help!"
                l "I don't know, I really don't like that platform..."
                $ fivy = "n"
                v "You keep making a big deal about it, and it isn't. But have it your way..."
            $ fivy = "smile"
            v "I know it's not your dream job, it isn't mine either, but hey... It's a good way to make some profit while we chase our dreams!"
            hide ivy
            show ivy2 at lef
            with short
            v "If everything goes well, Axel will convince Wildcats to hire me, and then I won't need any other job! No dancing, no teaching..."
            v "Just enjoying the good life!"
            if lena_axel_dating:
                v "Imagine if they hired us both! Wouldn't that be the dream?"
                l "Yeah..."
                if seymour_disposition == 0:
                    $ flena = "sad"
                    "If Seymour really had as much influence as he claimed, that would never happen..."
                else:
                    "I could imagine something like that happening, depending on how things went with Seymour later tonight..."
            else:
                l "You have everything planned."
                if seymour_disposition == 0:
                    "I would like to be as optimistic as Ivy was, but knowing Seymour was using his influence against me..."
                    "It was so distressing."
                else:
                    "I could imagine something like that happening to me too, depending on how things went with Seymour later tonight..."
            $ fivy = "smile"
            hide ivy2
            show ivy at lef
            with short
            if billy_model:
                if billy_trust == 2:
                    v "By the way, I talked with Billy the other day!"
                    $ flena = "n"
                    l "Jeremy's friend? He was supposed to hire us for some shoots and stuff..."
                    v "Yeah. He says he's been preparing to launch his own agency, and he's almost ready."
                    v "He said he'll organize the first shoot soon!"
                    l "Cool. I won't say no to some work coming my way."
                else:
                    v "By the way, I talked with Billy the other day!"
                    l "Jeremy's friend? Didn't he want to hire us for some shoots and stuff...?"
                    v "Yeah. He says he's been preparing to launch his own agency, and he's almost ready."
                    v "He said he'll organize the first shoot soon! Are you in?"
                    $ flena = "sad"
                    l "I don't know... I said I would give it a try, but this guy seems all talk..."
                    v "As long as he pays! Will you say no to work coming your way?"
                    l "I guess not..."
                    $ flena = "n"
            else:
                v "By the way, I talked with Billy the other day! Do you remember him?"
                l "Yeah, Jeremy's friend, the one who wanted to build his own modeling agency or something like that..."
                v "Yeah! He says he's almost ready and he'll organize the first shoot soon!"
                $ flena = "sad"
                l "I have the feeling he's all talk, but let me know how it goes when that shoot finally happens."
                $ fivy = "n"
                v "So you won't join me?"
                $ flena = "n"
                l "Nope, I'll pass for now."

        "I don't feel like talking":
            $ renpy.block_rollback()
            $ flena = "sad"
            l "Everyone's been asking me the same thing. Can we talk about something else?"
            v "Sure."
            if v9_axel_sex and seymour_disposition == 0:
                "I was worried about more pressing concerns. Confronting Seymour... and Axel."
                "I couldn't get him out of my head!"
            elif v9_axel_sex:
                "I was worried about more pressing concerns. Axel..."
                "I couldn't get him out of my head!"
            elif seymour_disposition == 0:
                "I was worried about more pressing concerns. All I could think about was confronting Seymour later tonight..."
    # holly
    if holly_gym:
        $ fholly = "smile"
        $ holly_glasses = False
        $ holly_look = 4
        l "Where's Holly, by the way?"
        if holly_guy and ian_holly_dating == False:
            $ fivy = "flirt"
            v "Oh, now that you mention it... Look at this."
            "Ivy pulled up her phone and showed me a picture."
            show lena at right
            show ivy at left
            with move
            show v9_holly_selfie1 with short
            $ flena = "worried"
            l "Why are you showing this to me? Did Holly send it to you?"
            v "Not to me, but to Mark! They have been chatting over the phone..."
            $ flena = "sad"
            l "And how did you get this picture?"
            v "Mark sent it. I asked him to keep me posted on how things evolve."
            $ flena = "n"
            l "Well, it seems they're evolving pretty well..."
            v "Yeah, now they need to just meet face to face. Mark's been trying to, but Holly's so damn sheepish."
            l "Well, it seems she's starting to let loose quite a bit..."
            hide v9_holly_selfie1 with short
            show lena at rig
            show ivy at lef
            with move
        else:
            v "Oh, she'll show up any minute now."
        if ian_holly_dating:
            $ fivy = "smile"
            v "She's been coming to class religiously these past weeks, even if you weren't here..."
            v "Seems like finally getting some action has lit a fire in her, ha ha!"
            $ flena = "smile"
            l "I'm glad for her..."
            if lena_go_holly == 4:
                $ flena = "sad"
                "That moment when I almost kissed her came to mind."
                "I still didn't understand why I suddenly felt that way. She was just so adorable..."
                $ flena = "n"
            show lena at rig3
            show ivy at lef3
            with move
            show holly with short
            h "Hi, girls! So glad to see you, Lena!"
            v "There you are. We were talking about you and Ian."
            $ fholly = "blush"
            hide holly
            show holly3
            with short
            h "Oh..."
        elif v8_holly_sex == "ivy" or v8_holly_sex == "lenaivy":
            label gallery_CH09_S15:
                if _in_replay:
                    call setup_CH09_S15 from _call_setup_CH09_S15
            hide ivy
            show ivy2 at lef
            with short
            v "Oh yeah, she is. I'm making sure of that..."
            l "What do you mean?"
            if v8_holly_sex == "ivy":
                v "I've been taking care of Holly while you were away. You can join us next time, if you want, ha ha!"
            else:
                v "I've been taking care of Holly while you were away. You should join us again next time, ha ha!"
            show lena at rig3
            show ivy2 at lef3
            with move
            show holly2 with short
            h "Hi...!"
            $ flena = "happy"
            l "Hi, Holly! So glad to see you. How was the conference?"
            h "It was fun..."
            v "Hey, what's up with you? Come here and give me a proper greeting, will you?"
            $ fholly = "blush"
            h "Uh, okay..."
            scene v9_holly8 with long
            "Holly walked up to Ivy obediently, closed her eyes, and kissed her."
            "And it wasn't just a brief kiss on the lips: Ivy held her head in place and stuck her tongue in Holly's mouth."
            "To my surprise, she accepted it and even responded to Ivy's deep, long kiss."
            $ flena = "worried"
            scene polegym
            show ivy at lef3
            show lena at rig3
            show holly3
            with long
            v "That's better. I'm going out of my way to teach you, after all."
            h "Yes... Thank you..."
            l "Um... That was weird."
            if v8_holly_sex == "ivy":
                v "Oh, come on! You've seen us do much worse!"
            else:
                v "Oh, come on! We've all done much worse, together!"
            v "We're just having some fun, right Holly?"
            $ fholly = "shy"
            h "Yeah."
            $ flena = "n"
            "Despite her shyness, she seemed to be okay with it. That night at Ivy's place really changed things..."
            $ renpy.end_replay()
            $ gallery_unlock_scene('CH09_S15')
        else:
            v "I'm surprised, she hasn't missed a single class, even when you've been gone."
            l "I'm glad. I'm sure she'll get a lot of benefits from this."
            show ivy at lef3
            show lena at rig3
            with move
            if v8_holly_sex == "lena":
                $ fholly = "shy"
                show holly3 with short
                h "Hi, girls! I'm so happy to see you, Lena..."
                $ flena = "shy"
                l "Hi, Holly! How was the conference?"
                h "It was fun..."
                "This was the first time I saw Holly since what had happened between us..."
                "We hadn't really talked about it, even though we texted often during these past two weeks."
            else:
                show holly2 with short
                h "Hi, girls! So glad to see you, Lena!"
                l "Hi, Holly! How was the conference?"
                h "It was fun... But most importantly, how are you?"
                if lena_go_holly == 4:
                    $ flena = "sad"
                    "That moment when I almost kissed her came to mind."
                    "I still didn't understand why I suddenly felt that way. She was just so adorable..."
                    $ flena = "n"
                l "I'm managing."
        l "I see you've bought some new sporting apparel, Holly!"
        $ fholly = "shy"
        h "Yeah... Ivy told me what I was wearing before was hardly appropriate for pole dancing..."
        $ fivy = "flirt"
        v "Much better this way, don't you think?"
        play sound "sfx/slap.mp3"
        $ fholly = "surprise"
        hide holly2
        hide holly3
        show holly3
        with vpunch
        "Ivy slapped Holly's ass, making her jump."
        v "Look at how thick you're getting! You're building muscle pretty quick, I'm surprised..."
        $ fholly = "blush"
        v "I guess it's easier to improve when you've never worked out before in your life."
        v "Also, you have wide hips and a pretty thin waist, but you need to get rid of these fat rolls!"
        $ fivy = "n"
        v "You'll be a total hottie when you get in shape!"
        $ fholly = "shy"
        h "You really think so?"
    if v8_minerva_sex:
        $ fholly = "blush"
        $ minerva_look = 2
        $ fminerva = "smile"
        if holly_gym:
            show lena at right
            show ivy at left
            show holly3 at lef
            with move
            show minerva at rig with short
        else:
            show lena at rig3
            show ivy at lef3
            with move
            show minerva with short
        mi "Excuse me. Shall we begin with the class?"
        v "Oh. Yeah, yeah."
    else:
        v "Trust me! {i}Uh-oh{/i}, look at the time. I should get the class started..."
    v "Girls, get in position...!"
    hide ivy with short
    scene polegym with short
    stop music fadeout 3.0
    "Ivy gave us a good workout, just as I was expecting."
    "I didn't want to end up feeling spent, though. I had to meet Seymour in a few hours..."
    scene lenahomenight with long
    if seymour_disposition > 1:
        "I went home to get ready for this important event."
    elif seymour_disposition == 1:
        "I went home to get ready for this business dinner."
    else:
        "I went home and got ready to meet that awful man..."
    jump v9seymourdinner

##################################################################################################################################################################################################################
## v9 SEYMOUR  ##################################################################################################################################################################################################################
##################################################################################################################################################################################################################

label v9seymourdinner:
    $ config.menu_include_disabled = True
    $ greyed_out_disabled = False
    $ robert_look = 2
    $ lena_look = "sexy1"
    if seymour_disposition > 1:
        $ flena = "smile"
        play music "music/flirty.mp3" loop
    elif seymour_disposition == 1:
        $ flena = "n"
        play music "music/flirty.mp3" loop
    elif seymour_disposition == 0:
        $ flena = "sad"
    show lenabra with short
    if seymour_disposition > 1:
        l "I wonder what his business proposal will be about..."
        $ flena = "n"
        l "I'm a bit nervous all things considered, but... I have the feeling working with him would be really interesting."
    elif seymour_disposition == 1:
        l "I'm nervous about that business proposal. I hope it's something I can agree with..."
        $ flena = "sad"
        l "He's not someone I can deal with carelessly. I need to watch my step around him, I've learned that much."
    elif seymour_disposition == 0:
        l "I'm more scared than I'd like to admit, but I can't let him see through me."
        if lena_wits > 4:
            $ flena = "serious"
            l "I'll try to get some evidence of his blackmailing. Maybe I can record our conversation with my phone..."
            l "Then I'll have a case to bring up to the police."
        else:
            l "I wonder if I should really go to the police..."
            $ flena = "serious"
            l "Well, let's see how this goes and then I'll decide. Maybe he'll leave me alone after all..."
    scene lenaroomnight with long
    call screen v9seymourwardrobe
    $ flena = "n"
    show lena with long
    if lena_look == 1:
        l "I guess I'll wear this."
    elif lena_look == "wits" or lena_look == 3:
        $ lena_makeup = 1
        l "I guess I'll wear this. I wonder if it's elegant enough."
    elif lena_look == "charisma":
        $ lena_makeup = 2
        l "I guess I'll wear this. I'm sure it's elegant enough."
    elif lena_look == "athletics":
        $ lena_makeup = 1
        l "I guess I'll wear this. I want to feel comfortable."
    elif lena_look == "lust":
        $ lena_makeup = 1
        l "I'm feeling like wearing this tonight."
    if seymour_necklace:
        if v7_necklace_sell:
            $ flena = "sad"
            l "He asked me to wear that necklace, but I sold it to pay rent..."
            if seymour_disposition > 1:
                l "I'm afraid he'll be disappointed..."
            elif seymour_disposition == 1:
                l "I hope he doesn't ask about it."
            else:
                $ flena = "serious"
                l "He'll be disappointed, but that's his problem."
        else:
            l "Oh, and he asked me to wear that necklace..."
            if seymour_disposition > 1:
                l "Well, I'm not gonna disappoint him!"
            elif seymour_disposition == 1:
                l "I guess I will indulge him."
            else:
                $ flena = "serious"
                l "Well, I have no intention to indulge him..."
                $ flena = "sad"
                l "But maybe if I do, negotiating with him will be easier... It's worth a try."
    $ fstan = "sad"
    play sound "sfx/knock.mp3"
    "Somebody knocked on my door."
    l "Yeah?"
    show lena at rig with move
    play sound "sfx/door.mp3"
    show stan at lef with short
    st "Somebody's on the intercom asking for you. They said they'll wait for you outside..."
    l "Thanks, Stan."
    if lena_stan > 5:
        st "Are you going out to dinner...?"
        if seymour_disposition > 1:
            $ flena = "smile"
            l "Yeah! And I might close the deal of a lifetime, depending on how things go."
            $ fstan = "smile"
            st "Oh, wow. Congratulations, Lena..."
            l "Don't congratulate me just yet. Take care of Lola for me, alright?"
            $ fstan = "smile"
            st "Sure."
        elif seymour_disposition == 1:
            l "More like a job interview... It's... unusual."
            st "Oh. That sounds important."
            l "It might be. Take care of Lola for me, alright?"
            $ fstan = "smile"
            st "Sure."
        elif seymour_disposition == 0:
            $ flena = "sad"
            l "Kind of... Take care of Lola for me, alright?"
            st "Sure, but... Is everything alright? You look kind of worried."
            l "Oh..."
            $ flena = "n"
            l "It's alright, don't worry about it. But thank you for caring."
            $ fstan = "blush"
            st "Um, well, of course..."
            l "Goodnight, Stan."
    hide lena
    hide stan
    with short
    scene street2night
    show limo_night
    with long
    if seymour_necklace and v7_necklace_sell == False:
        $ lena_necklace = "seymour"
    "When I went down I found a small limousine parked in front of the door."
    if seymour_disposition > 0:
        if lena_posh > 3:
            $ flena = "happy"
        else:
            $ flena = "n"
    elif seymour_disposition == 0:
        $ flena = "worried"
    show lena at rig3 with short
    if seymour_disposition > 0:
        if lena_posh > 3:
            l "Oh, wow..."
            l "I've never ridden a limo before..."
        else:
            l "Is he trying to impress me or is this how he usually does things?"
    elif seymour_disposition == 0:
        l "This is way too creepy..."
    show lena at truecenter with move
    "When I walked up to the vehicle, a light flashed and I heard the passenger door unlock."
    hide lena with short
    "I opened it and went inside, expecting to find Seymour, but I was alone."
    play sound "sfx/car.mp3"
    scene streetnight
    show limo_night
    with long
    "The limousine took me on a short, familiar ride."
    if v3_seymour_date:
        "It seems the dinner would take place at my old workplace once again."
    else:
        "We were following the same path I used to take when I worked at the restaurant..."
    if seymour_disposition == 0 and lena_wits > 4:
        "The ride stopped and I got off in front of the hotel, making sure to turn on the recorder on my phone."
    else:
        "The ride stopped and I got off in front of the hotel."
    "Seymour was waiting for me at the door."
    stop music fadeout 3.0
    # greeting
    $ fseymour = "smile"
    if seymour_disposition > 1:
        $ flena = "smile"
    elif seymour_disposition == 1:
        $ flena = "n"
    elif seymour_disposition == 0:
        $ flena = "serious"
    show lena at rig
    show seymour at lef
    with short
    mr "Good evening, Lena..."
    if lena_look == 1:
        $ fseymour = "n"
        mr "I must say I'm disappointed with your choice of outfit. I explicitly told you to dress elegantly."
    elif lena_look == 3:
        mr "I'm glad to see your outfit choice is appropriate for tonight."
    elif lena_look == "wits":
        mr "Let me commend you on your outfit choice. You look lovely tonight."
    elif lena_look == "charisma":
        mr "Let me commend you on your outfit choice. You look absolutely stunning tonight."
    elif lena_look == "athletics":
        $ fseymour = "n"
        mr "I must say I'm disappointed with your choice of outfit. I explicitly told you to dress elegantly."
    elif lena_look == "lust":
        $ fseymour = "n"
        mr "While I appreciate your desire to expose your beauty, I must say your sense of what's elegant needs some tuning."
    # disposition good
    if seymour_disposition > 1:
        if lena_look == 1 or lena_look == "athletics":
            $ flena = "worried"
            if lena_seymour > 0:
                call friend_xp('seymour', -1) from _call_friend_xp_149
            l "I'm sorry, I don't know what I was thinking...!"
            mr "Be more mindful next time. Come on, this way."
        elif lena_look == 3:
            l "Thanks, Mr. Ward."
            mr "Come on, this way."
        elif lena_look == "lust":
            $ flena = "worried"
            l "I, uh..."
            "That was his way of politely telling me I had screwed up..."
            mr "Be more mindful next time. Come on, this way."
        else:
            call friend_xp('seymour', 1) from _call_friend_xp_150
            l "Thank you so much, Mr. Ward."
            "He offered me his hand, gentlemanly."
            mr "Come on, this way."
    # disposition neutral
    elif seymour_disposition == 1:
        if lena_look == 1 or lena_look == "athletics":
            $ flena = "worried"
            if lena_seymour > 0:
                call friend_xp('seymour', -1) from _call_friend_xp_151
            l "I  didn't think it was that important, I mean..."
            mr "It'll have to do. Come on, this way."
        elif lena_look == 3:
            l "Um, thanks, Mr. Ward."
            mr "Come on, this way."
        elif lena_look == "lust":
            $ flena = "worried"
            l "I, uh..."
            "That was his way of politely telling me I had screwed up..."
            mr "It'll have to do. Come on, this way."
        else:
            call friend_xp('seymour', 1) from _call_friend_xp_152
            l "Um, thanks, Mr. Ward."
            "He offered me his hand, gentlemanly."
            mr "Come on, this way."
    # disposition bad
    elif seymour_disposition == 0:
        if lena_look == 1 or lena_look == "athletics" or lena_look == "lust":
            l "I don't care what your opinion on my clothes is, nor do I need your approval."
            if lena_seymour > 0:
                call friend_xp('seymour', -1) from _call_friend_xp_153
            l "Let's get this over with as soon as possible."
            mr "Alright. Come on, this way."
        elif lena_look == 3:
            l "Nothing about tonight is appropriate. Let's get this over with as soon as possible."
            $ fseymour = "n"
            mr "Alright. Come on, this way."
        else:
            l "I'm not here to listen to fake compliments. Let's get this over with as soon as possible."
            $ fseymour = "n"
            mr "Alright. Come on, this way."
    # arrive restaurant
    play music "music/seymours_theme.mp3" loop
    scene restaurant with long
    if seymour_disposition > 0:
        $ flena = "sad"
    show lena2 at rig
    show seymour at lef
    with short
    if v3_seymour_date:
        l "So we'll be having dinner here again..."
        $ fseymour = "n"
        mr "Yes. I always conduct business dinners in this restaurant, it's a rule of mine."
    else:
        l "I'm surprised you've decided to take me out to the place I used to work at..."
        mr "I thought you might find it somewhat upsetting, but I always conduct business dinners in this restaurant. It's a rule of mine."
    mr "Everything has its proper place, and it's not smart to mix things up."
    $ fseymour = "smile"
    mr "Besides, I became the controlling shareholder of this place for a reason."
    show lena2 at rig3
    show seymour at lef3
    with move
    $ frobert = "sad"
    $ fseymour = "n"
    show robert with short
    r "Glad to see you here again tonight, Mr. Ward..."
    if v3_seymour_date:
        "Robert gave me a concerned look but didn't say anything. It wasn't the first time we were in this situation."
    else:
        "Robert was visibly surprised when he saw me, but managed to keep acting professional."
    $ frobert = "n"
    r "Let me show you to the VIP table..."
    hide seymour
    show seymour2 at lef3
    with short
    mr "I can show the lady myself. Take our wallets and phones to the checkroom instead."
    if seymour_disposition == 0 and lena_wits > 4:
        $ flena = "worried"
        "That clever bastard! Did he suspect I would try to record him or something like that?"
        l "I don't mind handing him my purse, but I don't want anyone else to handle my phone."
        $ fseymour = "smile"
        mr "No distractions; we don't need them. Another rule of mine."
        $ flena = "sad"
        mr "Look, I'm handing mine too. I hope you'll do me the same courtesy."
        r "Your phone will be completely secure with us, I'm sure you're aware of that..."
        $ flena = "serious"
        l "I know, you don't need to tell me, Robert."
        $ frobert = "sad"
        r "..."
        mr "So?"
        $ flena = "sad"
        "I couldn't think of a good enough excuse, other than flat out rejecting this dinner."
        "Since I was already there, I might as well hear what he had to say, even if I wasn't able to record it..."
        l "Alright."
        mr "Good."
    else:
        $ flena = "sad"
        l "Our phones too?"
        $ fseymour = "smile"
        mr "Yes. No distractions. Another rule of mine."
    hide robert with short
    "I felt kind of naked after parting with my phone. I didn't like it..."
    show lena2 at rig
    show seymour2 at lef
    with move
    "I followed Seymour to the table and took the seat he was offering. He then sat in front of me."
    hide seymour2
    show seymour at lef
    with short
    if seymour_disposition > 0:
        $ flena = "n"
        mr "Thanks for coming tonight, Lena."
        if seymour_disposition > 1:
            l "It's my pleasure, Mr. Ward."
        else:
            l "Thank you for inviting me, Mr. Ward."
        mr "I'd prefer it if we dropped the formalities to a certain extent. Call me Seymour, please."
        l "Sure..."
        if seymour_necklace:
            if v7_necklace_sell:
                $ fseymour = "n"
                s "I see you didn't fulfill my request to wear the necklace I gave you..."
                $ flena = "sad"
                l "Yeah, I, um..."
                menu:
                    "I forgot it":
                        $ renpy.block_rollback()
                        $ v7_necklace_sell = 1
                        l "I forgot it, I'm sorry."
                        s "I see... Next time try to be more mindful."

                    "I lost it":
                        $ renpy.block_rollback()
                        $ v7_necklace_sell = 2
                        l "I, um, lost it."
                        s "You lost it? How?"
                        l "I'm not sure. I think a pickpocket might've snatched it from my bag while I was shopping downtown..."
                        s "I thought you'd be a bit more careful with a jewel of that caliber..."

                    "I sold it":
                        $ renpy.block_rollback()
                        $ v7_necklace_sell = 3
                        l "I don't have it. The truth is... I sold it."
                        $ fseymour = "sad"
                        s "You sold it?"
                        if lena_seymour > 0:
                            call friend_xp ('seymour', -1) from _call_friend_xp_154
                        l "Yes. I pawned it to pay for rent."
                        $ fseymour = "serious"
                        s "Appalling. A jewel of that caliber doesn't belong in a pawn shop."
                        $ fseymour = "n"

                mr "Anyway... It doesn't matter. We're here to discuss business."

            else:
                s "I'm glad to see you wearing the necklace I gave you. Gold and onyx surely are a perfect match for you."
                if lena_posh > 3:
                    l "Thank you so much! It was a lovely gift."
                else:
                    l "Thanks..."
        if v6_axel_pose == 1:
            $ fseymour = "n"
            s "First of all, let me apologize again for what happened during our last photo shoot."
            $ flena = "sad"
            s "Agnes Addingworth is an important business associate and my desire to please her caused a rather violent situation."
            s "I've realized I should've been more considerate."
            l "It's okay. It was unfortunate, but as long as something like that doesn't happen again we can forget it."
            $ fseymour = "smile"
            $ flena = "n"
            s "Good. Truth be told, I've been greatly enjoying working with you as a model."
        else:
            s "Let me start by saying I've been greatly enjoying working with you as a model."
        s "You immediately caught my eye at the exhibit, and when I heard you talk my initial interest was confirmed."
        if seymour_disposition > 1:
            $ flena = "smile"
            l "So you really found me interesting? Can I know why?"
        else:
            l "Is that so? What did you find interesting about me, exactly?"
    elif seymour_disposition == 0:
        $ flena = "serious"
        l "So, here we are. You caused a lot of trouble to make this damn dinner happen, Mr. Ward."
        mr "What can I say? I like it when things go my way."
        l "Even if that means blacklisting and threatening an innocent girl?"
        mr "I understand you're upset. I haven't used the most subtle negotiation tactics, but you drive a hard bargain, Lena."
        l "I'm not trying to bargain here! All I want is for you to leave me and my friends alone!"
        mr "It's not so simple. I'm interested in you, you see."
        l "Why?"
        mr "I have my reasons, but they shouldn't concern you for now."
        if seymour_necklace and v7_necklace_sell:
            $ fseymour = "n"
            $ v7_necklace_sell = 3
            mr "By the way, I see you failed to fulfill my request and wear the necklace I gave you tonight."
            l "Yeah, I sold it."
            $ fseymour = "sad"
            mr "You sold it?"
            if lena_seymour > 0:
                call friend_xp ('seymour', -1) from _call_friend_xp_155
            l "Yes. I pawned it to pay for rent."
            $ fseymour = "serious"
            mr "Appalling. A jewel of that caliber doesn't belong in a pawn shop."
            $ fseymour = "n"
            mr "But I guess that speaks about how dire your financial situation really is."
            mr "In any case, I'd suggest we cut back on formalities. No need for you to call me Mr. Ward any longer."
        else:
            mr "By the way, I'd suggest we cut back on formalities. No need for you to call me Mr. Ward any longer."
        s "You can call me Seymour."
        l "Is that supposed to make me feel better...?"
    # proposal
    show lena2 at rig3
    show seymour at lef3
    with move
    $ fseymour = "n"
    $ frobert = "n"
    show robert with short
    r "Excuse me, sir, here's the menu. Do you have an idea about what you would like to drink...?"
    s "Why are you even asking? Bring me the same as always. You're expected to know that already, aren't you?"
    $ frobert = "sad"
    $ flena = "sad"
    r "Yes, sir. Sorry. The same as always, coming right away."
    s "Good, and don't interrupt us again."
    hide robert with short
    show lena2 at rig
    show seymour at lef
    with move
    if seymour_disposition > 0:
        $ flena = "n"
        s "Sorry, where was I...?"
        $ fseymour = "smile"
        s "Oh, yeah. I think you're the right choice, so I want to strengthen our business partnership."
    elif seymour_disposition == 0:
        $ flena = "serious"
        s "Sorry, where was I...?"
        l "Look, the only reason I came here is to listen to whatever proposal you have for me, reject it, and tell you one last time to leave me alone before I get the cops involved."
        s "Before you act so harshly, hear me out. I know right now only the negatives are on the table."
        $ fseymour = "smile"
        s "But you haven't thought about all there is for you to gain."
        l "Enlighten me, then."
    $ fseymour = "n"
    s "As I understand it, your financial situation is not the best. And you have to take some extra burdens, too."
    if lena_money_family == 2:
        s "I've seen reflected in your account's movements that you've been periodically sending money to your parents, to this very last month."
    elif lena_money_family == 1:
        s "I've seen reflected in your account's movements that you've been periodically sending money to your parents, almost every month..."
    else:
        s "I've seen reflected in your account's movements that you used to periodically send money to your parents, even though you haven't lately..."
    $ flena = "sad"
    if seymour_disposition > 0:
        "He had access to my financial information? Was there something he didn't know about me?"
    else:
        "He even had access to my banking history? How much did he know about me?"
    if seymour_disposition > 0:
        "It was a bit unnerving..."
        l "Isn't leaking that kind of information illegal...?"
    else:
        $ flena = "serious"
        l "Spying on someone's banking history is a crime, I suppose you're aware of that."
    s "Let's leave petty moral arguments aside, shall we?"
    $ fseymour = "smile"
    s "What I'm offering you is the possibility to be rid of those financial concerns. Any of them."
    $ flena = "sad"
    s "You won't need to slave away at another dead-end job. You'll be able to invest all your time in whatever you wish to pursue."
    s "When it comes to modeling, through me you'll be able to work with the top brands. Agnes Addingworth, Wildcats..."
    $ flena = "worried"
    s "You could easily reach any place you wish. And your friends will get some benefits, too."
    s "I could get your friend Ian to win that contest he's aiming for, for example."
    s "I've been looking around for Hierofant's new breakout star; might as well be him."
    if seymour_disposition > 0:
        s "And that's not even half of it. If I become your patron, you'll have access to everything you can ask for."
        $ flena = "n"
        l "So that's what this is about? You want to become my patron?"
        s "That's exactly it."
        if seymour_disposition > 1:
            "I had trouble believing what I was hearing. Seymour Ward wanted to sponsor me?"
        l "And what does it entail, exactly?"
        s "Taking our partnership to the next level. I want you to become my model."
    else:
        $ flena = "serious"
        l "And what's the price?"
        s "I want to sponsor you."
        $ flena = "sad"
        l "Sponsor me?"
        s "Exactly. I'd like to become your patron."
        s "Under my sponsorship, you'll have access to everything you can ask for, both financially and professionally."
        $ flena = "serious"
        l "Still, I'm sure you want something particular from me in return for all that."
        if v4_seymour_date:
            s "Of course. I want to take our partnership to the next level. I want you to become my model."
        else:
            s "You've denied me the pleasure of working with you thus far. I want you to become my model."
    $ flena = "blush"
    hide lena2
    show lena at rig
    with short
    l "Your model?"
    s "Yes. With exclusivity rights."
    s "That means you'll only work with me, or with a photographer I have previously authorized."
    menu:
        "{image=icon_friend.webp}That sounds like a dream" if seymour_disposition > 1 or lena_seymour > 5:
            $ renpy.block_rollback()
            if seymour_disposition == 1:
                $ seymour_disposition = 2
            $ flena = "shy"
            l "Honestly, that sounds like a dream... And all you want from me is to model exclusively for you?"
            s "That's right. We've worked together already, you should be aware of what my approach to model photography is like."
            s "And for that, I need someone special. Now I'm certain that special someone is you, Lena."
            "This was turning out even better than I expected. Having Mr. Ward as a patron would be so incredibly advantageous..."

        "Seems too good to be true...":
            $ renpy.block_rollback()
            if seymour_disposition == 3:
                $ seymour_disposition = 2
            if seymour_disposition == 2:
                $ seymour_disposition = 1
            if seymour_disposition > 0:
                $ flena = "sad"
                l "I hope this doesn't come across as me being rude, but... Something like that sounds too good to be true."
            else:
                $ flena = "serious"
                l "No offense, but that sounds way too good to be true."
            s "I get why you would think something like that. It is indeed an incredibly advantageous offer, and it's also true."
            l "And all you want from me is to model exclusively for you?"
            l "I mean, you could no doubt sponsor any professional model you'd want. Why go to all this trouble for a nobody like me?"
            s "You're not a nobody in my eyes. Quite the opposite, in fact."
            if v4_seymour_date:
                s "We've worked together already, you should be aware of what my approach to model photography is like."
            else:
                s "You've never worked with me, so I guess you're not aware of what my approach to model photography is like."

        "{image=icon_mad.webp}Where's the catch?" if seymour_disposition < 2:
            $ renpy.block_rollback()
            if seymour_disposition == 1:
                $ seymour_disposition = 0.5
                $ flena = "worried"
                l "I'm don't know, this is... It's too good of an offer."
                "I didn't like this. Something felt off... I knew Seymour wasn't a man that could be easily trusted."
                $ flena = "serious"
                l "There has to be a catch, right?"
            else:
                $ flena = "serious"
                l "And where's the catch?"
            l "You could no doubt sponsor any professional model you'd want. Why go to all this trouble for a nobody like me?"
            s "As you say, I could work with any model I'd want. And as I said, I find you particularly interesting."
            if v4_seymour_date:
                s "We've worked together already, you should be aware of what my approach to model photography is like."
            else:
                s "You've never worked with me, so I guess you're not aware of what my approach to model photography is like."

    hide seymour
    show seymour2 at lef
    with short
    s "I want someone with whom I can deeply explore the connection between the model and the artist."
    s "Intimately."
    if seymour_disposition > 0:
        if seymour_disposition > 1:
            $ flena = "blush"
        else:
            $ flena = "worried"
        l "Intimately? What kind of shoots would we be doing?"
        l "Artistic nudes, lingerie, fashion maybe...? The usual?"
        s "I'm not looking for the usual. I want us to transcend those conventions."
        if seymour_disposition > 1:
            l "Correct me if I'm wrong, but... Are you meaning to say you want our shoots to be sexual in nature?"
            s "Aren't they all?"
            $ flena = "sad"
            l "I'll have to disagree on that, Mr. Ward... I thought this was clear, but modeling has nothing to do with sex work."
        else:
            l "Is that an embellished way of saying you're expecting our shoots to be sexual in nature?"
            s "Aren't they all?"
            l "Mr. Ward... I'm not sure I follow, but let me clarify something:"
            l "I'm a model, not a sex worker. If that's what you're really looking for..."
    elif seymour_disposition == 0:
        l "And here's the catch. I'm sure the shoots you have in mind are sexual in nature."
        s "Aren't they all?"
        l "I'm disappointed in you, Mr. Ward. I had you for a more cultured man."
        l "Let me clarify this for you: I'm a model, not a prostitute."
    $ fseymour = "serious"
    hide seymour2
    show seymour at lef
    with short
    s "I will choose not to get offended at the fact you feel the need to state such an obvious thing in front of me."
    $ fseymour = "n"
    s "I think I've made it clear that I'm not a base individual, nor am I looking for such a vulgar relationship."
    if seymour_disposition > 1:
        $ flena = "blush"
        l "I'm sorry. I probably misunderstood what you said about all photo shoots being sexual in nature..."
    elif seymour_disposition > 0:
        $ flena = "sad"
        l "Then what did you mean when you said all photo shoots are sexual in nature?"
    else:
        l "That's what you sound like when you say all photo shoots are sexual in nature."
    s "I meant to say the relationship between an artist and his model is fundamentally sexual, in the most abstract way."
    s "What is eroticism but the tension between opposites that fight to come together and integrate one another...?"
    $ fseymour = "smile"
    hide seymour
    show seymour2 at lef
    with short
    s "But I digress."
    s "The business relationship I'm offering you will be intimate and, sometimes, delve into sexuality and eroticism."
    $ flena = "worried"
    s "But at no point have I said you're expected to have sexual intercourse with me as a requisite for my patronage."
    s "In fact, the contract explicitly states that I'm not allowed to lay a finger on you... unless you'd want me to."
    if seymour_disposition > 0:
        $ flena = "sad"
        l "A contract?"
        hide seymour2
        show seymour at lef
        with short
        s "Exactly. I took the liberty of writing it down so all the clauses are well understood."
        l "What else is in this contract?"
    else:
        $ flena = "serious"
        "I snickered."
        l "Why would I want you to lay your fingers on me?"
        $ fseymour = "evil"
        hide seymour2
        show seymour at lef
        with short
        s "Exactly. Why would you?"
        $ fseymour = "smile"
        s "Leastways, it's written in this manner in the contract, in case we should be faced with that eventuality down the road."
        l "And what else does this contract say?"
    s "First of all, your salary."
    s "You'll earn {color=#1ED50F}10{/color}{image=icon_money.webp} upfront when signing it and the same amount each subsequent month."
    $ flena = "surprise"
    if seymour_disposition > 0.5:
        l "Say that again...?"
        s "You'll be earning {color=#1ED50F}10{/color}{image=icon_money.webp} per month. Not counting bonuses."
        "It was hard to believe."
    else:
        l "...!"
        s "Not including the bonuses, of course."
        "I couldn't help but freeze for a second, shocked."
    $ flena = "worried"
    "Never in my life had I managed to save up that amount of money, and he was offering to pay me that much each month?"
    "I would earn more than I had earned during my entire life working for him in just a couple of months."
    "And in one year, how much would I have...?"
    "Thinking about it made my head spin."
    s "So, I take it I've piqued your interest?"
    # accept/ decline
    menu:
        "{image=icon_friend.webp}I'm interested" if seymour_disposition > 0:
            $ renpy.block_rollback()
            $ v9_contract = 2
            if seymour_disposition == 1:
                $ seymour_disposition = 2
            elif seymour_disposition == 0.5:
                $ seymour_disposition = 1
            $ flena = "smile"
            hide lena
            show lena2 at rig
            with short
            l "You did..."
            if seymour_disposition == 3:
                "It's not like I could refuse such an offer. Especially coming from someone like Seymour..."
            hide seymour2
            show seymour at lef
            with short
            s "Excellent."
            call friend_xp('seymour', 1) from _call_friend_xp_156
            s "We can go over the whole thing in my office, in that case."
            s "It's just around the corner, on this same block."

        "What other clauses are there?" if seymour_disposition < 3:
            $ renpy.block_rollback()
            $ v9_contract = 1
            if seymour_disposition > 0:
                $ flena = "n"
            else:
                $ flena = "serious"
            l "And what other clauses are in this contract?"
            hide seymour2
            show seymour at lef
            with short
            s "Other than exclusive rights and safeguards when it comes to sexual harassment..."
            s "A non-disclosure agreement, image rights, and a termination clause."
            l "What's that clause like?"
            s "It states you can terminate the contract whenever you like, with immediate effect and with no liabilities, once the first month has expired."
            if seymour_disposition == 0:
                l "That easy?"
                s "Yes."
            s "Of course, my financial support will cease and you'll lose any sponsorship derived from your association with me."
            l "And what about working hours?"
            s "Determined by my schedule. I'm a busy man and I won't be able to schedule our shoots consistently."
            s "Since your wage is enough to free you from any other work commitments, I expect you to keep your schedule open for me."
            s "With room for negotiation, of course."
            if seymour_disposition > 0:
                "Everything sounded pretty reasonable..."
            else:
                $ flena = "sad"
                "Everything sounded surprisingly reasonable..."
            s "In any case, we can go over the whole thing in my office. It's just around the corner, on this same block."
            menu:
                "{image=icon_money.webp}I'm interested":
                    $ renpy.block_rollback()
                    hide lena
                    show lena2 at rig
                    with short
                    if seymour_disposition > 0:
                        if seymour_disposition > 1:
                            $ flena = "smile"
                            "I didn't see any reason to disagree. This was the opportunity of a lifetime..."
                        else:
                            "I was still a bit on the fence about this, but... I couldn't let the opportunity of a lifetime pass me by."
                        if seymour_disposition == 0.5:
                            $ seymour_disposition = 1
                        l "I'm interested in your offer, Mr. Ward."
                        s "Excellent."
                        call friend_xp('seymour', 1) from _call_friend_xp_157
                        s "Then it's time we moved to my office so we can go over it in detail."
                        s "It's just around the corner, on this same block."
                    else:
                        "I was overwhelmed by the situation. I felt helpless and I had nothing to negotiate with."
                        "What other option did I have but to accept?"
                        $ flena = "serious"
                        l "Considering those threats you made over the phone... You don't really give me a choice." 
                        $ fseymour = "smile"
                        s "There was only one smart choice, to begin with. I'm glad you've come to realize it."
                        l "I don't know how you can say that with a straight face, after pushing me around like this."
                        l "I don't need to tell you I don't feel happy, comfortable, or even safe with any of this."
                        s "I'm sure you'll come around. Everything I've told you about the contract is true."
                        $ flena = "sad"
                        s "It's time we moved to my office so we can go over it in detail."
                        s "It's just around the corner, on this same block."

                "Not interested":
                    $ renpy.block_rollback()
                    if seymour_disposition > 0:
                        "This seemed like the solution to most of my problems. The opportunity I had been waiting for."
                        $ flena = "worried"
                        hide lena
                        show lena2 at rig
                        with short
                        "But my gut was telling me I shouldn't accept."
                        "Things that look too good to be true usually aren't. I had learned that much."
                        if seymour_disposition > 1:
                            "And despite being a fascinating individual, I knew Seymour could not be trusted."
                        else:
                            "And Seymour was a controversial individual, someone I knew could not be trusted."
                        "He was far too powerful. Far too dangerous."
                        $ flena = "sad"
                        l "I'm sorry, Mr. Ward..."
                        $ fseymour = "n"
                        l "Your offer is indeed incredible, but I'm afraid I can't accept it."
                        s "I'm surprised... And disappointed. I thought you were intelligent, Lena."
                        if seymour_disposition > 1:
                            l "..."
                            s "Are you aware of what you are rejecting? This is your golden ticket, and you won't get another one."
                            l "I know, but still... It's not the path I want to take in life. I'm not really comfortable with it..."
                        else:
                            $ flena = "serious"
                            l "No need to insult me."
                            s "Are you aware of what you are rejecting? This is your golden ticket, and you won't get another one."
                            l "Maybe, but it's not the path I want to take in life. I'm not comfortable with it."
                        s "And what path are you going to take? Nobody will give you work as a model in this city."
                        $ fseymour = "evil"
                        s "I made sure of that."
                        $ flena = "worried"
                        l "Uh?"
                        s "Not Danny, not the art gallery, and not anyone who's worth a dime."
                        if lena_axel_dating:
                            s "Not even Axel. You'll never make it to Wildcats."
                            "He also knew about my photo shoot with Axel?"
                        if seymour_disposition > 1:
                            l "What the...?"
                        if cafe_help:
                            s "And that café you've been working at, well, I've decided to buy it."
                            $ flena = "worried"
                            s "Mr. Van Dyke was so happy to hear my offer."
                        else:
                            s "And that café you've been working at, well, I've decided to buy it. At a very cheap price, I might add."
                        s "You won't be hired by the new management. Nor by anyone in any of my businesses."
                        $ flena = "mad"
                        l "I should've figured out this was all your doing...!"
                        if lena_robert_sex:
                            l "Me getting fired from this restaurant surely was your doing, too!"
                        l "Thankfully you don't own every business in this city!"
                        s "Not yet, at least. But I don't need to, to make sure you don't get a decent wage in this city."
                        s "And do you have any idea how easy it would be to get your social media accounts blocked?"
                        $ flena = "worried"
                        s "I imagine it'd be hard for you to conduct business without a Peoplegram profile."
                        if v5_hand_proposal_lena or v5_hand_proposal:
                            s "Not to mention that friend of yours, Ian Watts, won't ever see his manuscript published..."
                            if v5_ian_showup:
                                s "I think we even hired him recently... That might change, of course, depending on your answer."
                            $ flena = "mad"
                            l "Why are you involving him in this? You're despicable!"
                        elif v5_ian_showup:
                            s "Oh, and I think we recently hired that friend, of yours, Ian Watts..."
                            s "That might change, of course, depending on your answer."
                            $ flena = "mad"
                            l "Why are you involving him in this? You're despicable!"
                        if seymour_disposition > 1:
                            "To think I fell for Seymour's charade... His polite manners were just a cover, and now his true colors have been revealed."
                        else:
                            "I knew Seymour couldn't be trusted! His polite manners were just a cover, and now his true colors have been revealed."
                        $ seymour_disposition = 0
                        if lena_seymour > 0:
                            call friend_xp('seymour', -1) from _call_friend_xp_158
                            $ lena_seymour = 0
                        $ flena = "mad"
                        l "Why are you doing all this!?"
                        $ fseymour = "smile"
                        s "As I said, I'm very interested in working with you, Lena."
                        l "To the extent of resorting to blackmail? I wonder what the police will think about this!"
                        $ fseymour = "evil"
                        s "Go ask them. I'm really curious about the answer they'll give you."
                        $ flena = "worried"
                        "The way he said it gave me a chill. What was he insinuating...?"
                        $ fseymour = "smile"
                        hide seymour
                        show seymour2 at lef
                        with short
                        s "Look, this shouldn't even pose a dilemma."
                        s "As you can see, the positives far outweigh the negatives. You stand to gain so much, why would you even consider taking the other choice?"
                        $ flena = "serious"
                        l "Do you really expect me to work with you after your threats?"
                        s "I wouldn't call them \"threats\" but rather \"persuasive arguments\"."
                        $ fseymour = "n"
                        s "I'm appalled at the fact that I need to convince you further, but what more do you need?"
                        s "Under my sponsorship, you won't even need to worry about all those petty inconveniences that are plaguing your life right now."
                        $ flena = "sad"
                        s "Money? Work? Opportunities? You'll have more than plenty."
                        s "You'll get everything you wish for."
                        menu:
                            "{image=icon_money.webp}Accept":
                                $ renpy.block_rollback()
                                "I was overwhelmed by the situation. I felt helpless and I had nothing to negotiate with."
                                "What other option did I have but to accept?"
                                $ flena = "serious"
                                l "You leave me with no choice."
                                $ fseymour = "smile"
                                s "There was only one smart choice, to begin with. I'm glad you've come to realize it."
                                l "I don't know how you can say that with a straight face, after pushing me around like this."
                                l "I don't need to tell you I don't feel happy, comfortable, or even safe with any of this."
                                s "I'm sure you'll come around. Everything I've told you about the contract is true."
                                $ flena = "sad"
                                s "It's time we moved to my office so we can go over it in detail."
                                s "It's just around the corner, on this same block."

                            "{image=icon_mad.webp}Refuse!":
                                $ renpy.block_rollback()
                                "His offer was really tempting, that much was undeniable."
                                $ flena = "serious"
                                hide lena2
                                show lena at rig
                                with short
                                "But making a deal with this man was like making a deal with the devil. And he had shown his cards."
                                jump v9seymourreject1

                    else:
                        "This seemed like the solution to most of my problems. The opportunity I had been waiting for."
                        $ flena = "serious"
                        "But making a deal with this man was like making a deal with the devil."
                        "He was tempting me with things that looked too good to be true. And I was certain there would be a price to pay."
                        jump v9seymourreject1

        "{image=icon_mad.webp}Not interested" if seymour_disposition < 1:
            $ renpy.block_rollback()
            label v9seymourreject1:
                $ flena = "mad"
            show lena at rig3 with move
            $ fseymour = "n"
            "I stood up from the table."
            if seymour_disposition < 0.5:
                l "Do you really expect me to work with you after your threats?"
            l "I don't care about what you're offering. And I know exactly what you're after."
            l "I'll say it again: I'm a model, not a prostitute! And I won't let you take advantage of me!" with vpunch
            if lena_will < 2:
                call will_up() from _call_will_up_58
            "A few diners turned their heads toward us, curious about what the ruckus was about."
            s "You're still mistaken, but I see you won't change your mind, even with all the potential benefits you stand to gain."
            l "Get this into your thick skull: I don't want anything from you. All I want is for you to leave me alone."
            l "I did as you asked. I came here and listened to your stupid proposal."
            l "Now I hope you'll extend the same courtesy to me and disappear from my life."
            l "Goodnight."
            $ seymour_disposition = 0
            hide lena with short
            jump v9seymourreject2

    jump v9seymouroffice

## REJECT SEYMOUR AND LEAVE ##########################################################################################################################################################################################################
label v9seymourreject2:
    stop music fadeout 3.0
    scene streetnight with long
    $ flena = "worried"
    show lena with short
    "I left the restaurant as fast as I could. My heart was beating like crazy and my legs felt weak."
    "I was having trouble believing the conversation that had just taken place."
    l "How did I get into this situation?"
    if v9_axel_sex:
        "I was still processing what had happened with Axel, and now this...!"
        "I felt like my head was about to explode. More and more troubles kept piling up, and this last one was the worst of them all."
    else:
        "I couldn't believe my luck. More and more troubles kept piling up, and this last one was the worst of them all."
    "I had opposed one of the most powerful individuals in this city. A man who was seemingly keen on making things as hard for me as possible..."
    "I didn't know what to do. Who to ask for help."
    "The authorities, maybe...?"
    "A part of me was wishfully thinking that maybe my rejection would be enough."
    "That tomorrow morning everything would go back to normal and Seymour would forget about me."
    "A naive hope."
    scene fade with long
    $ lena_makeup = 0
    $ lena_necklace = 0
    pause 1
    jump master_script

## SEYMOUR'S OFFICE ##########################################################################################################################################################################################################
label v9seymouroffice:
    $ lena_seymour_dating = True
    $ v9contracttalk = False # tracking var
    stop music fadeout 2.0
    scene restaurant with long
    "We left the restaurant without even asking for the bill. Seymour didn't need to."
    scene streetnight with long
    "He wasn't lying when he said his office was around the corner. We went into the adjacent building and took an elevator to the top floor."
    scene seymourofficenight with long
    "Seymour's office was more luxurious than I had anticipated."
    "It was more like a lounge, or even a hall: spacious, high-ceilinged, and with colossal windows overlooking the city."
    "An imposing desk dominated the room, and it also had some couches and even a small bar..."
    if seymour_disposition > 1:
        $ flena = "smile"
    elif seymour_disposition == 1:
        $ flena = "n"
    elif seymour_disposition == 0:
        $ flena = "worried"
    show lena2 at rig3
    show seymour2 at lef3
    with short
    if seymour_disposition > 1:
        "I felt like I was stepping into a world different from mine, one that I had never been allowed into... Until now."
        l "Wow... This place is really impressive."
        s "It is meant to be, so thank you for your appreciation."
    elif seymour_disposition == 1:
        "I felt out of place, like I was stepping into a world different from my own."
        l "I've never seen an office like this one..."
        s "Not everyone gets to step into a place like this."
    elif seymour_disposition == 0:
        "I felt like I was stepping into the lion's den. Was I really gonna go through with this?"
        s "Don't be shy. Take a step into my office."
        l "Is this really your office? It doesn't look like one."
    s "This might be my workplace, but it's also a deeply personal space. Especially at this time of day."
    s "Let's take a seat."
    show lena2 at rig
    show seymour2 at lef
    with move
    if seymour_disposition > 1:
        "Seymour ensconced himself on his big chair and I sat in front of him, brimming with anticipation."
        "I was about to sign a deal that would change my life."
    elif seymour_disposition == 1:
        "Seymour ensconced himself on his big chair and I sat in front of him, hesitant."
        "Was I really sure of where I was getting myself into?"
    elif seymour_disposition == 0:
        "Seymour ensconced himself on his big chair and I sat in front of him, reluctant."
        "I had to fight the part of me that wanted to stand up and get the hell away from there."
    "Seymour searched into a drawer and pulled out a stack of papers, handing some to me. A copy of the contract."
    $ fseymour = "n"
    s "Let's see..."
    if v9_contract == 2:
        "Seymour started going over the stipulated clauses."
        s "First. You'll be compensated with {color=#1ED50F}10{/color}{image=icon_money.webp} upfront after signing this contract and the same amount each subsequent month."
        s "Second. I'll reserve exclusive rights when it comes to your modeling work: you can only work for me or for a photographer I have previously authorized."
        s "This also includes the usage rights to the material from our photo shoots. That means everything you post on your social media that's modeling-related will need my express authorization first."
        s "Third. The working hours and number of photo shoots per month will be determined according to my schedule. I'm a busy man and I won't be able to schedule our shoots consistently."
        s "Since your wage is enough to free you from any other work commitments, I expect you to keep your schedule open for me."
        s "Fourth. You must agree to a non-disclosure agreement regarding our business relationship. You can't make it public and you can't discuss any private information you might learn about me or my enterprises."
        $ flena = "sad"
        s "In case you do, you'll be subject to financial liabilities and/or other compensatory charges."
        s "Fifth. You're expected not only to go along with my directions during the shoots, but to add to the experience as well. You should be proactive."
        s "In case I deem your service dissatisfying, I'll be able to terminate our contract with immediate effect."
        s "Sixth. As a safeguard, it's explicitly stated that any kind of physical or intimate relationship is out of the boundaries of this contract and those will only be allowed in case you explicitly consent to them."
        if seymour_disposition > 1:
            $ flena = "blush"
            "That one was a weirdly specific clause, but it was probably better to have it than not to..."
        elif seymour_disposition == 1:
            $ flena = "worried"
            "That one was a weirdly specific clause, but better to have it than not to."
        elif seymour_disposition == 0:
            $ flena = "serious"
            l "Which will never happen."
        s "Seventh and last. You can terminate this contract whenever you like, with immediate effect and with no liabilities, only once the first month has expired."
        if seymour_disposition == 0:
            l "That easy?"
            s "Yes."
        s "Of course, my financial support will cease and you'll lose any sponsorship derived from your association with me."
    else:
        s "I've already briefed you about all the important clauses, but let's go over everything so there are no doubts."
        s "First. You'll be compensated with {color=#1ED50F}10{/color}{image=icon_money.webp} upfront after signing this contract and the same amount each subsequent month."
        s "Second. I'll reserve exclusive rights when it comes to your modeling work."
        s "This also includes the usage rights to the material from our photo shoots, which means everything you post on your social media that's modeling-related will need my express authorization first."
        s "Third. The working hours and number of photo shoots per month will be determined according to my schedule. You're expected to keep your schedule open for me."
        s "Fourth. You must agree to a non-disclosure agreement regarding our business relationship. You can't make it public and you can't discuss any private information you might learn about me or my enterprises."
        $ flena = "sad"
        s "In case you do, you'll be subject to financial liabilities and/or other compensatory charges."
        s "Fifth. You're expected not only to go along with my directions during the shoots, but to add to the experience as well. You should be proactive."
        s "In case I deem your service dissatisfying, I'll be able to terminate our contract with immediate effect."
        s "Sixth. As a safeguard, it's explicitly stated that any kind of physical or intimate relationship is out of the boundaries of this contract and those will only be allowed in case you explicitly agree to them."
        if seymour_disposition > 1:
            $ flena = "blush"
            "That one was a weirdly specific clause, but it was probably better to have it than not to..."
        elif seymour_disposition == 1:
            $ flena = "worried"
            "That one was a weirdly specific clause, but better to have it than not to."
        elif seymour_disposition == 0:
            $ flena = "serious"
            l "Which will never happen."
        s "Seventh and last, as I already told you, you can terminate this contract whenever you like, with immediate effect and with no liabilities, only once the first month has expired."
    s "Do you have any questions?"
    if seymour_disposition > 2:
        $ flena = "n"
        l "No, everything's clear..."
    else:
        menu v9contractmenu:
            "{image=icon_wits.webp}I want to add a clause" if lena_wits > 5 and v9_contract < 3:
                $ renpy.block_rollback()
                $ v9_contract = 3
                if seymour_disposition > 0:
                    l "If you don't mind, I'd like to add an additional clause."
                    s "And which one would that be?"
                    l "In case any of us decides to terminate this contract, any audiovisual material we've taken during our shoots will remain private and won't be shown or distributed."
                    s "Alright. Seems fair."
                else:
                    l "Not so fast. I'd like to add an additional clause."
                    s "And which one would that be?"
                    l "In case any of us decides to terminate this contract, any audiovisual material we've taken during our shoots will remain private and won't be shown or distributed."
                    s "That's rather restrictive..."
                    l "It's my condition."
                    s "So be it."
                "Seymour added my clause to the contract."
                s "Anything else you want to add?"
                l "That should be it."
                jump v9contractmenu

            "What happens if I breach the contract before one month?" if v9contracttalk == False:
                $ renpy.block_rollback()
                $ v9contracttalk = True
                if seymour_disposition > 0:
                    $ flena = "sad"
                    l "Um, so what happens if I breach the contract before one month?"
                else:
                    l "What about the fine print? Let's go over that too."
                    l "What happens if I breach the contract before one month?"
                $ fseymour = "n"
                s "I hope we don't find ourselves in that eventuality, but in that case, you'll be required to reimburse what has been paid to you, plus an additional twenty percent as compensation."
                s "And, most importantly... People in the industry will know you're not a trustworthy employee, so you might have problems finding a high-end job."
                if seymour_disposition == 0:
                    l "You're already making sure of that, so it doesn't really change anything, does it?"
                else:
                    l "I see..."
                s "So, is everything clear now? Any more questions?"
                jump v9contractmenu

            "Everything's clear":
                $ renpy.block_rollback()
                if seymour_disposition > 0:
                    $ flena = "n"
                    l "I'd say everything's clear."
                else:
                    "I revised the contract thoroughly."
                    $ flena = "sad"
                    l "Everything seems to be in order..."

    $ fseymour = "smile"
    s "Good. I already have all your legal information."
    s "All that's left is for you to sign the contract."
    if seymour_disposition > 1:
        $ flena = "smile"
        "Seymour offered me a pen and I took it without hesitation."
        "I felt like I was about to sign the ticket to a new life."
    elif seymour_disposition == 1:
        $ flena = "n"
        "I took the pen Seymour was offering me and paused for a moment."
        "I felt this signature would change my life drastically..."
    elif seymour_disposition == 0:
        $ flena = "sad"
        "Seymour offered me a pen and I took it, still hesitant."
        "I felt like I was about to make a deal with the devil, but what other option did I have?"
        "I didn't like it, but the alternative was much worse... And the terms were really advantageous, that couldn't be denied."
    "The things I could do with that amount of money...!"
    play sound "sfx/drawing.mp3"
    label gallery_CH09_S12:
        if _in_replay:
            call setup_CH09_S12 from _call_setup_CH09_S12
    l "There, signed."
    $ fseymour = "happy"
    s "Excellent."
    $ fseymour = "smile"
    s "The first payment will be processed shortly."
    call money(10) from _call_money_16
    stop music fadeout 3.0
    "Seymour stood up."
    show seymour2 at lef3 with move
    s "Alright, we can get started."
    if seymour_disposition > 0:
        $ flena = "sad"
        l "You mean right now?"
    else:
        $ flena = "worried"
        l "What, right now? Here?"
# SCENE STARTS
    s "Yes. This is a perfect moment, don't you think so?"
    s "There's a box on the table. Open it."
    if seymour_necklace == False:
        "I did, discovering a pair of gold-trimmed, black high heels and a beautifully ornate gold and onyx collar. It looked really expensive..."
    else:
        "I did, discovering a pair of gold-trimmed, black high heels."
    s "This is what you'll be wearing. Strip off everything else."
    if lena_look == 1:
        $ fseymour = "n"
        s "And put a bit of makeup on. I'd appreciate it."
    "So he had planned this out in detail..."
    if seymour_disposition > 0:
        $ flena = "n"
        "Now it was time to do my job."
        hide lena2 with short
        $ lena_makeup = 1
        $ lena_look = 1
        if seymour_necklace == False:
            $ lena_necklace = "seymour"
        "I contemplated the vivid cityscape that lay beneath my feet as I undressed."
        show lenanude2 at rig with long
        "I had posed for several photographers and artists before, but this situation had a very distinct flavor."
        "It felt different from anything I had done before..."
    elif seymour_disposition == 0:
        $ flena = "sad"
        "I contemplated the vivid cityscape that lay beneath my feet as I considered what I was really about to do."
        hide lena2 with short
        $ lena_makeup = 1
        $ lena_look = 1
        if seymour_necklace == False:
            $ lena_necklace = "seymour"
        "A part of me felt scared and disgusted. Another part couldn't stop thinking about all the perks I would get out of this."
        "Was I letting myself get coerced into this? Or was I willingly selling my dignity to Seymour?"
        show lenanude2 at rig with long
        "I couldn't decide. All I knew was I had many reasons to accept Seymour's proposition. Even if I didn't like it."
        "I exhaled and got into a work mindset."
    $ fseymour = "n"
    play sound "sfx/icecube.mp3"
    "While I finished getting ready, Seymour walked to the bar and poured himself a glass of liquor."
    play music "music/sensual.mp3" loop
    "A mellow jazz melody started playing through the office's wireless speakers."
    "It was slow and soft, but had a sensual and playful cadence that was impossible to ignore."
    scene v9_seymour1 with long
    "He then made himself comfortable on one of the couches, looking at me."
    "I waited, standing still, not knowing what to do."
    l "Where's your camera?"
    "After taking a sip of whiskey and savoring it, Mr. Ward finally spoke."
    s "We won't be needing one tonight."
    l "So you're not going to take pictures?"
    s "No need. I want to live the aesthetic experience you provide firsthand."
    "What he was asking was... so intimate."
    "He wanted me to pose not for the camera, but for {i}him{/i}."
    menu:
        "{image=icon_love.webp}Play into Seymour's hands" if seymour_disposition > 1:
            $ renpy.block_rollback()
            $ seymour_disposition = 3
            scene v9_seymour2b
            if lena_tattoo1:
                show v9_seymour2_t1b
            if lena_tattoo2:
                show v9_seymour2_t2b
            if lena_tattoo3:
                show v9_seymour2_t3b
            if lena_piercing1:
                show v9_seymour2_p1b
            elif lena_piercing2:
                show v9_seymour2_p2b
            if v7_necklace_sell == False:
                show v9_seymour2b_sy
            with long
            pause 1
            "A whirlpool of feelings spiraled inside my chest. Anxiety was one of them."
            "Seymour was an imposing audience, and I wanted to please him. I needed to."
            "And felt I could do just that. The obvious interest he had shown for me had filled me with confidence."
            "I was there because he wanted me. All I had to do was let him have me..."
            s "Good... I was wondering if you'd feel intimidated when removing the camera from this equation."
            l "It's ... The connection feels much stronger this way..."
            s "That's exactly the point. The camera is just a pretext. Or how to say it...?"
            s "A dishonest mediator between the artist and the model."
            s "It is {i}me{/i} you're posing for, not the camera."
            s "I'm glad to see you, as a model, are well aware of that fact."

        "I've never done something like this":
            $ renpy.block_rollback()
            scene v9_seymour2
            if lena_tattoo1:
                show v9_seymour2_t1
            if lena_tattoo2:
                show v9_seymour2_t2
            if lena_tattoo3:
                show v9_seymour2_t3
            if lena_piercing1:
                show v9_seymour2_p1
            elif lena_piercing2:
                show v9_seymour2_p2
            if v7_necklace_sell == False:
                show v9_seymour2_sy
            with long
            pause 1
            l "I've never done something like this before..."
            s "Is that how you feel?"
            if seymour_disposition > 1:
                l "I don't know how to explain it, but yeah..."
                l "This feels... different."
            else:
                l "Yes. I always worked with people who drew or photographed me..."
                if seymour_disposition == 1:
                    l "This feels... different."
                else:
                    l "This feels rather... creepy."
            jump v9sycnv1

        "{image=icon_mad.webp}This is not what I agreed to" if seymour_disposition == 0:
            $ renpy.block_rollback()
            scene v9_seymour2
            if lena_tattoo1:
                show v9_seymour2_t1
            if lena_tattoo2:
                show v9_seymour2_t2
            if lena_tattoo3:
                show v9_seymour2_t3
            if lena_piercing1:
                show v9_seymour2_p1
            elif lena_piercing2:
                show v9_seymour2_p2
            with long
            pause 1
            l "Hey, this is not what I agreed to...!"
            s "Yes, it is. You were to model for me, whether I take pictures or not is irrelevant."
            l "It isn't to me. This feels rather... creepy."
            label v9sycnv1:
                s "It is a curious thing how a mind can delude itself, don't you think?"
            s "The camera is just a pretext. Or how to describe it...?"
            s "A dishonest mediator between the artist and the model."
            s "It is {i}me{/i} you're posing for, not the camera. You more than anyone, as a performing model, should be aware of that."
            s "Now do that. Pose."

    scene v9_seymour3
    if lena_tattoo1:
        show v9_seymour3_t1
    if lena_tattoo2:
        show v9_seymour3_t2
    if lena_piercing1:
        show v9_seymour3_p1
    elif lena_piercing2:
        show v9_seymour3_p2
    if v7_necklace_sell == False:
        show v9_seymour3_sy
    with long
    pause 1
    if seymour_disposition == 3:
        "I sat on the couch opposite Seymour and struck an aesthetically pleasing pose."
        "It felt so natural and easy getting into Seymour's game."
        if lena_tattoo1 or lena_tattoo2 or lena_tattoo3:
            mr "I see you've gotten tattooed... It's not exactly my taste, but I don't mind as long as it's tasteful."
        "Whatever awkwardness or stiffness I had during our conversation had evaporated almost completely."
        "All I should concern myself with at that moment was to let Seymour enjoy the view."
    else:
        "I still didn't know what to do exactly, so I went with the first thing that came to my mind."
        "I sat on the couch opposite Seymour, trying to come up with an aesthetically pleasing pose."
        if lena_tattoo1 or lena_tattoo2 or lena_tattoo3:
            mr "I see you've gotten tattooed... It's not exactly my taste, but I don't mind as long as it's tasteful."
        if seymour_disposition > 0:
            "I was trying not to feel awkward, but I wasn't finding the method..."
            "I told myself the contract was already signed, and now was the time to stop worrying and just do my job."
            "If this was a game, I needed to get into it."
        else:
            "It was hard, considering how uncomfortable I felt."
            "But I had signed that contract and decided to play Seymour's game..."
    if seymour_necklace == False:
        s "That necklace looks beautiful on you, Lena... As I thought, gold and onyx are a perfect match for you."
    scene v9_seymour1 with long
    "He looked at me quietly and with an unwavering expression. I couldn't tell what he was thinking at all."
    play sound "sfx/icecube.mp3"
    "His cold, icy eyes didn't move an inch from me, not even when he took small sips of his drink."
    s "Let me ask you something, Lena."
    scene v9_seymour3
    if lena_tattoo1:
        show v9_seymour3_t1
    if lena_tattoo2:
        show v9_seymour3_t2
    if lena_piercing1:
        show v9_seymour3_p1
    elif lena_piercing2:
        show v9_seymour3_p2
    if v7_necklace_sell == False:
        show v9_seymour3_sy
    with long
    # about modeling
    s "Have you ever thought about what being a model really is? What's the meaning behind your craft?"
    if seymour_disposition > 1:
        "His question caught me off guard. I wasn't expecting him to suddenly ask me something like that."
        l "I’d say... being a model is trying to embody beauty, perhaps."
        s "That's a pretty good answer..."
        s "I’ve taken quite some time to think deeply about this, so let me share mine with you."
    elif seymour_disposition == 1:
        "It was a strange question to make all of a sudden, but I tried to think of a reasonable answer."
        l "Well, a model's job is to pose for the artist, isn't it? That's what modeling is."
        s "You're not wrong, but there's much more depth to it than that."
        s "I’ve taken quite some time to think about this, so let me share my answer with you."
    elif seymour_disposition == 0:
        "Why was he suddenly asking me something like that? I had enough trouble concentrating as it was..."
        l "I thought the talk was over and now I just had to model for you."
        s "Surely you can do both. Won't you entertain me?"
        "He was making me even more nervous..."
        l "I can't give you a precise definition right now, I'd need time to think over it."
        s "I’ve taken that time to think deeply about this, so I suppose I'll save you the trouble."
    s "Sit up. Cross your legs and twist your shoulders."
    scene v9_seymour4 with long
    pause 1
    s "Just like that. You have a beautiful figure, Lena. But I'm sure you're aware of that."
    s "In the same way, you should be aware of what becoming a model really is."
    if seymour_disposition == 3:
        "I waited to hear his answer, genuinely intrigued."
        "Seymour's voice had the power to really grip my attention..."
    elif seymour_disposition > 1:
        "I waited to hear his answer. I couldn't help but be intrigued."
    elif seymour_disposition == 1:
        "This felt more like a strange continuation to our prior conversation than a proper shoot, or whatever this was..."
        "But I indulged him, of course."
    elif seymour_disposition == 0:
        "I had no desire to listen to his pretentious monologue, but I had no choice in the matter."
    scene v9_seymour1 with long
    s "To be a model is to self-consciously transform yourself into an object of beauty."
    s "It means seeing yourself through my eyes, escaping the prison of your Self."
    s "Those walls that have been suffocating you all along..."
    scene v9_seymour4
    if v7_necklace_sell == False:
        show v9_seymour4_sy
    with long
    s "It means seeing yourself through my eyes, seeing yourself become the shining object of my desire."
    s "For what is a model other than a reflection of the artist's desire?"
    s "An object to his ego."
    menu:
        "Agree with Seymour":
            $ renpy.block_rollback()
            $ seymour_desire = True
            if seymour_disposition > 1:
                $ seymour_disposition = 3
                "Seymour's words had a powerful gripping effect on me. They sounded so alluring, erasing my inhibitions..."
                scene v9_seymour4b
                if v7_necklace_sell == False:
                    show v9_seymour4b_sy
                with long
                pause 1
                l "Embodying beauty, and becoming art, through your gaze... I get it."
                s "That's it. I knew you'd be the one to understand, Lena."
                s "Tell me: how does my desire make you feel?"
                l "Beautiful... and valuable..."
                s "And powerful. Don't forget powerful, doll."
                "He was right. I possessed the kind of power to make a man like Seymour notice me."
                "He could have anybody he wanted. But he wanted me. And he, more than anybody, could make it worth my while..."
            elif seymour_disposition == 1:
                $ seymour_disposition = 2
                "Seymour's speech had something magnetic about it."
                "I was slowly being drawn into this game of his, feeling more at ease under his unswerving eyes."
                l "Embodying beauty, and becoming art, through your gaze... I get it."
                s "That's it. I knew you'd be the one to understand, doll."
                s "Under my gaze, you become beautiful, valuable... and powerful."
                "I believed him. After all, a man like him who could probably have anyone he wanted, had chosen me."
            elif seymour_disposition == 0:
                "I didn't want to agree with him, but I couldn't help but feel the allure in his words."
                "He was speaking of something I had come to realize myself, deep inside."
                l "I guess you're right..."
                s "Tell me: how do you feel about it, doll?"
                s "Under my gaze, you become beautiful, valuable... and powerful."
                "I was slowly being drawn into this game of his, feeling more at ease under his unswerving eyes."
            s "Feel it. Let those emotions swell inside of you."

        "I'm more than an object" if seymour_disposition < 3:
            $ renpy.block_rollback()
            if seymour_disposition > 1:
                l "Really...? Don't you think a model is more than an object of beauty?"
                s "I'm not belittling your quality as a subject if that's what you mean."
                s "If you were a mere object, I'd have no interest in you, doll."
            elif seymour_disposition == 1:
                l "Any model is more than just an object. First and foremost, I'm a subject."
                s "Of course, I'm not belittling your quality as a subject, doll."
                s "If you were a mere object, I'd have no interest in you."
                "I wasn't sure I liked it when he called me \"doll\", but I didn't say anything."
            elif seymour_disposition == 0:
                l "I'll let you know we models are still human beings, subjects, and not objects."
                s "I'm well aware of that, doll. If you were a mere object, I'd have no interest in you."
                "I hated it when he called me \"doll\", but I didn't say anything."
            s "You do have a soul, and one I'm rather captivated by."
            s "But right now, it is my gaze that ascribes meaning to who you are."
            s "Under it, you become beautiful, valuable... and powerful."
            s "Feel it. Let those emotions swell inside of you."
            if seymour_disposition > 1:
                "Seymour's speech had something magnetic about it. His voice sounded so alluring..."
            elif seymour_disposition == 1:
                "I wasn't sure what he was talking about, but the tone of his voice was kind of comforting."
            elif seymour_disposition == 0:
                "I had no intention of gratifying his inflated speech with another answer. Arguing with him would only upset me..."
                "And getting upset would only make things even harder for me at that moment."

        "...":
            $ renpy.block_rollback()
            l "..."
            if seymour_disposition > 1:
                "I wasn't sure I got his meaning. I had so much stuff in my mind at that moment..."
                "Still, Seymour's speech had something magnetic about it. His voice sounded so alluring..."
            elif seymour_disposition == 1:
                "I wasn't sure I got his meaning. I was trying to concentrate on the task at hand."
                "The tone of his voice was kind of comforting, though. I tried to relax a bit more."
            elif seymour_disposition == 0:
                "I had no intention of gratifying his inflated speech with an answer. Paying attention to it would only upset me..."
                "And getting upset would only make things even harder for me at that moment."
            s "Under my gaze, you become beautiful, valuable... and powerful."
            s "Feel it. Let those emotions swell inside of you."
            if seymour_disposition == 3:
                $ seymour_desire = True
                "He was right. I possessed the kind of power to make a man like Seymour notice me."
                "He could have anybody he wanted. But he wanted me. And he, more than anybody, could make it worth my while..."
            elif seymour_disposition == 2:
                "I believed him. After all, a man like him who could probably have anyone he wanted, had chosen me."

    s "I want you to reach under the seat cushion. You'll find an item there."
    # insert vibrator
    scene v9_seymour5
    if lena_tattoo2:
        show v9_seymour5_t2
    if lena_tattoo3:
        show v9_seymour5_t3
    if v7_necklace_sell == False:
        show v9_seymour5_sy
    with long
    pause 1
    "I did as he asked, and my fingers found something smooth and hard. I pulled it out."
    "He had prepared this, too..."
    "It took me a few seconds to figure out what it was. I had seen something like this at the sex shop."
    "It was some kind of remote-controlled vibrator...!"
    s "Insert it."
    if seymour_disposition == 3:
        stop music fadeout 2.0
        scene v9_seymour5b
        if lena_tattoo2:
            show v9_seymour5_t2b
        if lena_tattoo3:
            show v9_seymour5_t3
        if v7_necklace_sell == False:
            show v9_seymour5_sy
        with long
        play music "music/danger.mp3" loop
        pause 1
        "My first instinct was to obey his command, like I had been obeying the ones before."
        if v9_axel_sex:
            "It was easy and exciting letting myself be guided by him... It was something similar to what Axel made me feel, but not quite the same."
            "He was aggressive and demanding. Seymour was calm and spoke with a certain authority."
        else:
            "It was easy and exciting letting myself be guided by him... Seymour was dominant but calm, speaking with a certain authority."
        "There was no way I could go against his wishes..."
        "I slowly pressed the vibrator into my pussy. It had the size of an egg, but it slid in just fine."
        "I was wetter than I thought..."
    elif seymour_disposition == 2:
        l "..."
        "I hesitated for a second. Was I really about to do this?"
        menu:
            "Do Seymour's bidding":
                $ renpy.block_rollback()
                stop music fadeout 2.0

            "{image=icon_will.webp}I can't do it!" if lena_will > 0:
                $ renpy.block_rollback()
                $ flena = "worried"
                jump v9seymournodildo

        scene v9_seymour5b
        if lena_tattoo2:
            show v9_seymour5_t2b
        if lena_tattoo3:
            show v9_seymour5_t3
        if v7_necklace_sell == False:
            show v9_seymour5_sy
        with long
        play music "music/danger.mp3" loop
        pause 1
        "I was. I obeyed his command, like I had been obeying the ones before."
        if lena_lust < 9:
            call xp_up('lust') from _call_xp_up_113
        if v9_axel_sex:
            "It was so easy letting myself be guided by him... It reminded me of what Axel made me feel, but it wasn't quite the same."
            "He was aggressive and demanding. Seymour was calm and spoke with a certain authority."
        else:
            "It was so easy letting myself be guided by him... Seymour was dominant but calm, speaking with a certain authority."
        "Something instinctive in me made me want to comply with that authority."
        "I slowly pressed the vibrator into my pussy. It had the size of an egg, but it slid in just fine."
        "It was surprisingly lubricated..."
    elif seymour_disposition == 1:
        l "Wha--?"
        l "This is... This is not...!"
        s "Don't hesitate. You were doing fine so far."
        s "Don't let my desire waver, Lena."
        menu:
            "Do Seymour's bidding":
                $ renpy.block_rollback()
                stop music fadeout 2.0

            "I can't do it!":
                $ renpy.block_rollback()
                $ flena = "worried"
                jump v9seymournodildo

        if seymour_desire == False:
            "I knew thinking too much about it would make me freeze. So I didn't."
        scene v9_seymour5b
        if lena_tattoo2:
            show v9_seymour5_t2b
        if lena_tattoo3:
            show v9_seymour5_t3
        if v7_necklace_sell == False:
            show v9_seymour5_sy
        with long
        play music "music/danger.mp3" loop
        if lena_lust < 9:
            call xp_up('lust') from _call_xp_up_114
        pause 1
        if seymour_desire:
            "For some reason, his words dispelled my hesitance, and I just did it."
            if v9_axel_sex:
                "I found it easier if I just let myself be guided by him... It reminded me of what Axel made me feel, but it wasn't quite the same."
                "He was aggressive and demanding. Seymour was calm and spoke with a certain authority."
            else:
                "I found it easier if I just let myself be guided by him... Seymour was dominant but calm, speaking with a certain authority."
            "I slowly pressed the vibrator into my pussy. It had the size of an egg, but it slid in just fine."
            "It was surprisingly moist..."
        else:
            "I just did what he asked, slowly pressing the vibrator into my pussy."
            "It had the size of an egg, and it wasn't easy sliding it in... Especially under Seymour's gaze."
            if stalkfap_pro == 2:
                "I had been doing similar things for my Stalkfap subscribers, but this was definitely more shameful."
            elif stalkfap_pro == 1:
                "I thought I was getting used to this kind of thing on Stalkfap, but this was definitely way more shameful."
            else:
                "I couldn't believe I was really doing something like this... It was hard not to be overwhelmed by shame."
            "I thought about what I would get in return for this: the money, the opportunities..."
            "It made it much easier."
    elif seymour_disposition == 0:
        l "What!?" with vpunch
        play music "music/danger.mp3" loop
        $ flena = "mad"
        $ fseymour = "n"
        scene seymourofficenight
        show lenanude at rig
        show seymour at lef3
        with long
        l "I'm not doing that...!"
        s "I feared this would happen when we reached this point..."
        s "Tell me, what do you have a problem with, exactly?"
        l "What do I have a problem with? What do you think...?"
        l "I thought we had a clause against sexual harassment."
        s "That clause stipulated that no physical relationship would be conducted unless you gave your explicit consent..."
        l "Physical or {i}intimate{/i} relationship!"
        s "By the same metric, wouldn't you consider you baring your naked body before me an intimate act, too?"
        $ flena = "worried"
        l "That's different..."
        scene v9_seymour1 with long
        s "How so? Because it's the job people hire you for? Because you agree to it?"
        s "Well, this is what you agreed to when you signed that contract. Don't fret, I have no intention of laying a finger on you."
        s "I won't move from this couch. So go on."
        menu:
            "Do Seymour's bidding":
                $ renpy.block_rollback()

            "I'm not doing it!":
                $ renpy.block_rollback()
                $ flena = "mad"
                jump v9seymournodildo

        scene v9_seymour5
        if lena_tattoo2:
            show v9_seymour5_t2
        if lena_tattoo3:
            show v9_seymour5_t3
        if v7_necklace_sell == False:
            show v9_seymour5_sy
        with long
        l "..."
        "Was I really about to do this? In front of this despicable man?"
        if stalkfap_pro == 2:
            "I had been doing similar things for my Stalkfap subscribers, but this was completely different..."
        elif stalkfap_pro == 1:
            "I thought I was getting used to this kind of thing on Stalkfap, but this was completely different..."
        if seymour_desire:
            "A part of me hated the idea of giving him this shameful satisfaction. But another one was surprisingly willing to go through with this."
            "I stood to gain so much. If this was all I needed to do..."
            scene v9_seymour5b
            if lena_tattoo2:
                show v9_seymour5_t2b
            if lena_tattoo3:
                show v9_seymour5_t3
            if v7_necklace_sell == False:
                show v9_seymour5_sy
            with long
            pause 1
            "I slowly pressed the vibrator into my pussy. It had the size of an egg, but it slid in just fine."
            "I was surprised to discover how moist I was. This didn't make any sense...!"
            "I tried to keep my cool by focusing on the rewards I would reap. The money, the opportunities..."
        else:
            "I hated the idea of giving him this shameful satisfaction. Of playing into his hands..."
            "And yet I did."
            scene v9_seymour5b
            if lena_tattoo2:
                show v9_seymour5_t2b
            if lena_tattoo3:
                show v9_seymour5_t3
            if v7_necklace_sell == False:
                show v9_seymour5_sy
            with long
            pause 1
            "I slowly pressed the vibrator into my pussy."
            "It had the size of an egg, and it wasn't easy sliding it in... Especially under Seymour's gaze."
            "I already knew I would need to do things I would hate when I signed that contract. I had done so to avoid his threats, yes..."
            "But mainly to get everything he had promised to give me. The money, the opportunities..."
        "They were worth swallowing my pride now that no one was watching."
        "Just him."
        if lena_lust < 9:
            call xp_up('lust') from _call_xp_up_115
    s "Now, continue to show me your beauty."
    # sofa
    scene v9_seymour6
    if lena_tattoo2:
        show v9_seymour6_t2
    if lena_tattoo3:
        show v9_seymour6_t3
    if v7_necklace_sell == False:
        show v9_seymour6_sy
    with long
    pause 1
    if seymour_disposition > 1:
        "It was weird moving with that vibrator suddenly inside of me."
        "Indeed, Seymour hadn't laid a finger on me, but it still felt like he was reaching into me."
        "Into my body, and into my mind."
    elif seymour_disposition == 1:
        "When I moved, I could clearly feel the hard surface of the vibrator pressing against my inner walls."
        if seymour_desire:
            "It was awkward but also weirdly exciting..."
        else:
            "It was hard for me to relax with a foreign object in my insides..."
    elif seymour_disposition == 0:
        "Seymour wasn't touching me, but having a foreign object inside of me made me feel kind of defiled..."
        if seymour_desire:
            "Though it didn't feel as awful as one might expect..."
        else:
            "I was so uncomfortable..."
    "Seymour put down his glass for the first time."
    s "Look at you, doll... Your legs are a marvel, especially in those shoes."
    play sound "sfx/vibrator.mp3"
    show v9_seymour6b
    if lena_tattoo2:
        show v9_seymour6_t2
    if lena_tattoo3:
        show v9_seymour6_t3
    if v7_necklace_sell == False:
        show v9_seymour6_sy
    with vpunch # surprised face
    l "...!"
    scene v9_seymour1b
    with long
    "He had activated a small remote, making the toy inside of me start vibrating."
    s "You have such a wonderful, young body. But it's the soul that animates it which fascinates it."
    s "That's what I really want you to show me."
    s "How that soul shakes. How the body follows."
    play sound "sfx/vibrator.mp3"
    scene v9_seymour6c
    if lena_tattoo2:
        show v9_seymour6_t2
    if lena_tattoo3:
        show v9_seymour6_t3
    if v7_necklace_sell == False:
        show v9_seymour6_sy
    with hpunch #face and hand with remote
    "As he spoke, his thumb slid up the remote slowly, making the vibration more intense."
    "I bit my lip and closed my eyes, suddenly shaken from the inside."
## seymour_disposition =2-3 - squirt
    if seymour_disposition > 1:
        "The buzzing spread like a warm shiver across my genitals, making them ache with anticipation."
        if seymour_disposition == 3:
            "When I felt that acute sensation I realized I had been yearning for it..."
        elif seymour_disposition > 0:
            "I wasn't ready for such an acute sensation. But it was pleasurable..."
        "The vibration increased slowly, steadily, getting to a level of intensity that was about to make my knees buckle..."
        "And then it suddenly stopped."
        scene v9_seymour1b with long
        s "You're perfect, Lena... Just perfect."
        play sound "sfx/vibrator.mp3"
        "He began playing with the vibrator, making it buzz intermittently, at a very low intensity."
        s "Relish in this feeling and keep shining for me."
        scene v9_seymour7
        if lena_tattoo1:
            show v9_seymour7_t1
        if lena_tattoo2:
            show v9_seymour7_t2
        if lena_piercing1:
            show v9_seymour7_p1
        elif lena_piercing2:
            show v9_seymour7_p2
        if v7_necklace_sell == False:
            show v9_seymour7_sy
        with long
        pause 1
        "I let my body dictate my next movement."
        "My knees felt too weak to stand up as Seymour continued to control the vibrations spreading inside of me."
        play sound "sfx/vibrator.mp3"
        "A pulsating quiver was stimulating my insides with a very particular cadence. I whimpered."
        play sound "sfx/ah3.mp3"
        l "Ahhn..."
        s "That's it. The beauty I want to witness is not only in your body. In your voice, also."
        "The vibration continued, getting progressively more intense, so slightly, incrementally..."
        s "In your expression. In every gesture you make."
        if seymour_disposition == 3:
            scene v9_seymour7b
            if lena_tattoo1:
                show v9_seymour7_t1
            if lena_tattoo2:
                show v9_seymour7_t2b
            if lena_piercing1:
                show v9_seymour7_p1
            elif lena_piercing2:
                show v9_seymour7_p2
            if v7_necklace_sell == False:
                show v9_seymour7_sy
            with long
            pause 1
            "My hands moved with a will that wasn't their own."
            "Before I could realize it, one was cupping my breasts, and the other one had slid between my thighs..."
        else:
            "I clasped my fingers around my hair, biting my lip to prevent a moan from escaping."
        s "Feel my will. Embody it."
        play sound "sfx/vibrator.mp3"
        "The buzzing waves continued, reaching higher levels each time and my knees buckled, making me lose my balance."
        "I felt like my entire body was humming, becoming numb. I could just feel one thing:"
        "A quaking sensation in my lower belly that was about to explode...!"
        play sound "sfx/ah2.mp3"
        s "I want you to be the object of my desire, Lena."
        if seymour_desire:
            scene v9_seymour9c
            if lena_tattoo1:
                show v9_seymour9_t1c
            if lena_tattoo2:
                show v9_seymour9_t2c
            if v7_necklace_sell == False:
                show v9_seymour9_sy
            with flash
        else:
            scene v9_seymour9b
            if lena_tattoo1:
                show v9_seymour9_t1
            if lena_tattoo2:
                show v9_seymour9_t2
            if v7_necklace_sell == False:
                show v9_seymour9_sy
            with flash
        if seymour_desire:
            play sound "sfx/orgasm2.mp3"
            l "Yesss...! Aaahhhhh!!!{w=0.6}{nw}" with vpunch
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
        else:
            play sound "sfx/orgasm1.mp3"
            l "Ohhh...! Aaahhhhh!!!{w=0.6}{nw}" with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
        "My body gave out, unable to hold itself together anymore."
        "A jet of fluid gushed out of my pussy as a jolt of mind-melting pleasure made my legs tremble uncontrollably."
        if seymour_desire:
            scene v9_seymour9c_close
        else:
            scene v9_seymour9b_close
        if lena_tattoo2:
            show v9_seymour9_t2_close
        if v7_necklace_sell == False:
            show v9_seymour9_close_sy
        with long
        if lena_will > 0:
            call willdown from _call_willdown_13
        if v9_axel_sex:
            "I couldn't believe it. First with Axel and now this, too...!"
            "Spasm after spasm, I emptied the content of my urethra in front of Seymour."
        else:
            "I couldn't believe it. I was squirting in front of Seymour, emptying the content of my urethra spasm after spasm...!"
        "I felt completely exposed, all my shameful weaknesses laid bare for his eyes to see."
        play sound "sfx/ah6.mp3"
        if seymour_desire:
            scene v9_seymour9c
            if lena_tattoo1:
                show v9_seymour9_t1c
            if lena_tattoo2:
                show v9_seymour9_t2c
            if v7_necklace_sell == False:
                show v9_seymour9_sy
            with flash
        else:
            scene v9_seymour9b
            if lena_tattoo1:
                show v9_seymour9_t1
            if lena_tattoo2:
                show v9_seymour9_t2
            if v7_necklace_sell == False:
                show v9_seymour9_sy
            with flash
        "My legs continued to shake slightly as the last waves of pleasure slowly vanished, like drops of water in the sand."
        "I started to get my senses back, but the room was still spinning around me."
        stop music fadeout 2.0
        scene seymourofficenight with long
        $ flena = "blush"
        $ fseymour = "evil"
        "I was still wobbly when I finally stood up."
        show lenanude2 at rig
        show seymour2 at lef3
        with short
        s "That was such a sublime reaction, Lena..."
        s "I thought I was prepared for everything, yet you still managed to surprise me."
        if seymour_disposition == 3:
            l "I'm so sorry, Mr. Ward! I didn't mean to...! I..."
            $ fseymour = "smile"
            s "Don't apologize, doll."
        else:
            l "Oh, God, I...!"
            "I couldn't decide if I should apologize, hide or scream at Seymour."
            "My mind was a mess...!"
        $ fseymour = "smile"
        s "You've shown me everything I wanted to see, and even more."
        s "I'm immensely pleased..."
## seymour_disposition =0-1 - orgasm, no squirt
    else:
        "The buzzing spread like an acute shiver across my genitals, making them ache."
        if seymour_disposition > 0:
            "I wasn't ready for such an acute sensation. It made me twitch and tense my body..."
        else:
            if seymour_desire:
                "I wasn't ready for such an acute sensation. And why did it feel kinda good...?"
            else:
                "That acute sensation made me clench my hands and tense my entire body..."
        "And that only made me feel the vibration more intensely, almost painfully so."
        "Maybe Seymour recognized something, because he lowered the intensity to a slight buzz, making it much more comfortable."
        if seymour_disposition > 0:
            "Still, this situation was far from comfortable. I was naked in front of Seymour, with a vibrator shoved in my privates, and..."
        else:
            if seymour_desire:
                "Not that I could ever feel comfortable in this situation, but..."
            else:
                "Not that I could ever feel comfortable in this humiliating situation."
        "He began playing with the vibrator, making it buzz intermittently, at very low intensity."
        play sound "sfx/vibrator.mp3"
        "A pulsating quiver began stimulating my insides with a very particular cadence."
        scene v9_seymour1b with long
        s "You're just as I thought, Lena... Just perfect."
        if seymour_disposition == 0:
            s "Forget about your grudges and self-conscious judgment."
        s "Forget about your worries. You have none now."
        s "I'll make sure of it."
        s "All you need to do now is relish in this feeling, let go, and shine for me."
        scene v9_seymour8
        if lena_tattoo2:
            show v9_seymour8_t2
        if lena_tattoo3:
            show v9_seymour8_t3
        if v7_necklace_sell == False:
            show v9_seymour8_sy
        with long
        pause 1
        if seymour_disposition > 0:
            "I didn't have the confidence to stand up without trembling, so I slid from the couch down to the floor."
            "I instinctively adopted a more sheltered pose, trying to cover my shame..."
        else:
            "I didn't have the confidence to stand up without trembling, and I didn't want him to see that."
            "I slid from the couch down to the floor, adopting a more sheltered pose, trying to cover myself..."
        if seymour_desire:
            "I couldn't hold a faint whimper. The vibrator's buzz had me trembling with pleasure..."
        else:
            "The vibrator's buzz had been uncomfortable at first, but now it was getting kind of pleasant. I couldn't hold a faint whimper."
        play sound "sfx/ah3.mp3"
        l "Ahn..."
        s "Don't hide what you're feeling, Lena. Embrace it."
        "The pulsating vibrations continued, getting progressively more intense, so faintly, incrementally..."
        s "Reveal all your beauty to me."
        scene v9_seymour8b
        if lena_tattoo2:
            show v9_seymour8_t2b
        if lena_tattoo3:
            show v9_seymour8_t3b
        if v7_necklace_sell == False:
            show v9_seymour8b_sy
        with long
        pause 1
        "My legs started to slightly tremble."
        if seymour_disposition > 0:
            "I squeezed them together on impulse, trying to stop them from shaking, but that only made the vibrations feel more intense."
            "Somehow Seymour was stimulating me with the perfect rhythm, and my body was reacting to it despite my embarrassment."
            "I felt the pleasure building up more and more, and the only thing I could do was let it take over."
        else:
            "I squeezed them together, trying to stop them from shaking, trying to resist."
            "But that only made the vibrations feel more intense."
            "My body had a mind of its own, and it was reacting to the rhythm in which Seymour was stimulating me."
            "How could I be feeling good from this? I hated it, yet I was enjoying it...!"
        play sound "sfx/vibrator.mp3"
        scene v9_seymour8c
        if lena_tattoo2:
            show v9_seymour8_t2c
        if lena_tattoo3:
            show v9_seymour8_t3c
        if v7_necklace_sell == False:
            show v9_seymour8b_sy
        with long
        pause 1
        l "Nhhh...!"
        "It was like Seymour was reading me perfectly, adjusting the vibration accordingly."
        s "That's it. Feel my will, Lena. Embody it."
        "The trembling spread to my entire body. I tensed up, trying to hold on, but..."
        s "I want you to be the object of my desire, Lena."
        scene v9_seymour9
        if lena_tattoo1:
            show v9_seymour9_t1
        if lena_tattoo2:
            show v9_seymour9_t2
        if v7_necklace_sell == False:
            show v9_seymour9_sy
        with flash
        play sound "sfx/orgasm1.mp3"
        l "No...! Aaahhhhh!!!{w=0.6}{nw}" with vpunch
        with vpunch
        pause 0.6
        with vpunch
        pause 0.6
        "My body gave out and pleasure washed over it, unable to be held back anymore."
        "My legs trembled uncontrollably as I was struck by the sudden orgasm, making me feel like a slave to my own body."
        "I wasn't able to control a thing... That power was in Seymour's hands."
        if lena_will > 0:
            call willdown from _call_willdown_14
        "I had no idea what to say. How to look Seymour in the eye."
        if seymour_disposition > 0:
            "He had surpassed all my barriers. He played me however he pleased, to the point of making me cum..."
        else:
            if seymour_desire:
                "How to even look at myself...!"
            "I was surprised at my own weakness. How easy it had been for him to exploit."
            "How easily he made me cum..."
        "I started to regain control of myself as the last waves of pleasure faded, like drops of water in the sand."
        stop music fadeout 2.0
        scene seymourofficenight with long
        $ flena = "blush"
        $ fseymour = "evil"
        "I looked a bit wobbly when I finally stood up."
        show lenanude2 at rig
        show seymour2 at lef3
        with short
        s "I knew you'd be the one. This session was intensely satisfying."
        $ fseymour = "smile"
        s "Both for me, and for you, I'd say."
        if seymour_disposition > 0:
            l "..."
            "I had no idea what to say. How to look Seymour in the eye."
        else:
            $ flena = "mad"
            l "You...!"
            $ flena = "worried"
            "I wanted to lash out at him, but I had no idea what to say. How to look Seymour in the eye."
        "How to even look at myself...!"
        s "I'm pleased with today's session. That'll be all for today."
## HOME
    $ v9_seymour_orgasm = True     ## DEFINED NEW VARIABLE
    show seymour2 at lef with move
    "Seymour finally stood up from the couch, keeping the same calm, unbreakable demeanor he had been showing this entire time."
    s "It's getting late. Let me call a taxi for you."
    if seymour_necklace == False:
        $ seymour_necklace = True
        s "I'll let you know when our next session will take place. Oh, and take the necklace with you. It's a gift."
        s "Now, until our next meeting, enjoy your new lifestyle..."
    else:
        s "I'll let you know when our next session will take place. Until then, enjoy your new lifestyle..."
    s "You've earned it."
    scene seymourofficenight with long
    scene streetnight with long
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH09_S12")
    pause 1
    play sound "sfx/car.mp3"
    "A car was waiting for me on the street. I got inside and it drove me back home."
    scene lenahomenight_dark with long
    $ lena_necklace = 0
    pause 0.5
    play sound "sfx/shower.mp3"
    scene v1_lena_shower
    if lena_piercing1:
        show v1_lena_shower_p1
    if lena_piercing2:
        show v1_lena_shower_p2
    if lena_tattoo2:
        show v1_lena_shower_t2
    if lena_tattoo3:
        show v1_lena_shower_t3
    with long
    pause 1
    "Once there, I went straight to the bathroom and took a long, warm shower."
    $ lena_makeup = 0
    $ lena_look = 1
    $ flena = "blush"
    scene lenaroomnight with long
    pause 0.5
    show lenabra with long
    if seymour_desire:
        "The experience I had just had was..."
        if seymour_disposition == 3:
            $ flena = "flirtshy"
            l "Oh God, I'm still wet..."
            l "I can't believe how turned on I got. Seymour really is something special..."
            "To think he had managed to make me squirt without even laying a finger on me..."
        elif seymour_disposition == 2:
            l "Oh God, I'm still wet..."
            l "I can't believe how turned on I got. Seymour really knew what buttons to push..."
            "To think he had managed to make me squirt without even laying a finger on me..."
        elif seymour_disposition == 1: # after rewrite this cannot coexist with seymour_desire == True
            l "I can't believe how turned on I got... God, how shameful..."
            l "But it was Seymour who pushed my buttons. And he seemed to know exactly how."
            "I didn't know how to feel about all of this. A part of me was really weirded out, but the other one..."
            "He made me cum, and without laying a finger on me."
            l "Was this wrong? Did I cross a line I shouldn't have...?"
        elif seymour_disposition == 0:
            l "What's wrong with me...? I can't believe how turned on I got..."
            l "Seymour played with me how he pleased. He really knew what buttons to push..."
            "I was so confused with myself. A part of me hated the man and felt threatened by him, forced to do his bidding."
            "But he made me cum... And without laying a finger on me."
        if v9_axel_sex:
            "I had no idea what was going on with my body. Axel made me cum like crazy just the other day, and now Seymour, using just a vibrator..."
            "I had never experienced something like it."
        else:
            "I had only experienced something like that a couple of times before."
            "The first time it happened I was so surprised and embarrassed. I never knew my body was capable of such a thing."
            "And tonight Seymour had caused it to explode a third time. I couldn't hold anything back."
        if ian_lena_couple:
            $ flena = "sad"
            "I thought about Ian and the relationship I had decided to start with him."
            "What would he say if he knew about what happened tonight?"
            if v9_axel_sex:
                "And about what I did with Axel the other day? God, that was so bad..."
            "I couldn't let him know..."
        "And what now? If I were to keep working for Seymour..."
        if seymour_disposition > 1:
            $ flena = "smile"
            l "I don't see why I shouldn't. {color=#1ED50F}10{/color}{image=icon_money.webp} per month... That's just incredible!"
            l "Not to speak of all the other opportunities he's offering."
            l "Tonight was truly life-changing."
            l "From now on, things will never be the same..."
        elif seymour_disposition == 1: # after rewrite this cannot coexist with seymour_desire == True
            $ seymour_disposition = 2
            $ flena = "sad"
            l "It's the smart thing to do. {color=#1ED50F}10{/color}{image=icon_money.webp} per month... That's just incredible."
            $ flena = "n"
            l "Not to speak of all the other opportunities he's offering."
            l "From now on things will be different... Tonight was truly life-changing."
        elif seymour_disposition == 0:
            # $ seymour_disposition = 0.5 #####   seymour_disposition == 0 + seymour_desire equals this 0.5 contradictory state
            $ flena = "sad"
            l "I have to endure at least one month. I will get another {color=#1ED50F}10{/color}{image=icon_money.webp} if I do..."
            l "But to think I could get that amount each month... I would become rich in just one year. Not to speak of all the other opportunities he's offering..."
            $ flena = "worried"
            l "God, what should I do...?"
            "I still didn't know if for better or for worse, but tonight had truly been life-changing..."
    else:
        "I was still in a daze after the experience I had just lived through."
        if seymour_disposition > 1:
            l "Oh God, I'm still wet..."
            l "I can't believe how turned on I got. Seymour really knew what buttons to push..."
            "To think he had managed to make me squirt without even laying a finger on me..."
        elif seymour_disposition == 1:
            l "I can't believe I ended up cumming in front of Seymour... God, how shameful..."
            l "But it was him who pushed my buttons. And he seemed to know exactly how."
            "I didn't know how to feel about all of this. A part of me was really weirded out, but the other one..."
            "He made me cum, and without laying a finger on me."
            l "Was this wrong? Did I cross a line I shouldn't have...?"
        elif seymour_disposition == 0:
            l "What's wrong with me...? I can't believe I ended up cumming in front of Seymour..."
            $ flena = "serious"
            l "He played with me how he pleased. That despicable man..."
            $ flena = "worried"
            "But he really knew what buttons to push..."
            "I was so confused at myself. A part of me hated the man and felt threatened and humiliated by him."
            "But he made me cum... And without laying a finger on me."
        if ian_lena_couple:
            $ flena = "sad"
            "I thought about Ian and the relationship I had decided to start with him."
            "What would he say if he knew about what happened tonight?"
            if v9_axel_sex:
                "And about what happened with Axel the other day? God, that was so bad..."
            "I couldn't let him know..."
        "And what now? If I were to keep working for Seymour..."
        if seymour_disposition == 2:
            $ flena = "sad"
            l "It's the smart thing to do. {color=#1ED50F}10{/color}{image=icon_money.webp} per month... That's just incredible."
            $ flena = "n"
            l "Not to speak of all the other opportunities he's offering."
            l "From now on things will be different... Tonight was truly life-changing."
        elif seymour_disposition == 1:
            $ flena = "sad"
            $ seymour_disposition = 2
            l "I have to, at least for a month. If I do I will get another {color=#1ED50F}10{/color}{image=icon_money.webp}..."
            l "Getting paid that amount per month... That's just incredible. I would be rich in a year."
            l "But I'm not sure I'm comfortable with this... business relationship. I wonder what he'll ask from me next time..."
            "For now, all I could do was wait and see how things unfolded. I was already involved in this, after all..."
            "It was still too early to tell, but tonight could potentially turn my life around."
        elif seymour_disposition == 0:
            $ seymour_disposition = 0.5
            $ flena = "sad"
            l "I have to endure at least one month. I will get another {color=#1ED50F}10{/color}{image=icon_money.webp} if I do..."
            l "But to think I could get that amount each month... I would become rich in just one year. Not to speak of all the other opportunities he's offering..."
            $ flena = "serious"
            l "But what will he ask from me next? I can't forget I'm being blackmailed by this man."
            $ flena = "worried"
            l "Well, kind of... Maybe I shouldn't have signed that damn contract..."
            "The only thing I knew for sure was that tonight could potentially turn my life around..."
            "It still remained to see if for better or for worse."

    scene fade with long
    pause 1
    jump master_script
##REJECT VIBRATOR ####################################
label v9seymournodildo:
    $ seymour_desire = False
    stop music fadeout 3.0
    $ fseymour = "n"
    scene seymourofficenight
    show lenanude at rig
    show seymour at lef3
    with long
    if seymour_disposition == 2:
        $ seymour_disposition = 1
        l "I'm sorry, but I just can't do it...!"
        s "..."
        s "And here I thought we were on the right track. This is disappointing, I'm not going to lie."
        l "It's just... too much. I've never done something like this and I don't feel comfortable right now..."
        s "I can see that. There's no point in pressuring you any further; it's not what I'm after."
    elif seymour_disposition == 1:
        l "I just can't do it. This is too much."
        s "..."
        s "And here I thought we were on the right track. This is disappointing, I'm not going to lie."
        $ flena = "serious"
        l "There was no mention of something like this in the contract... And I'm not comfortable with it."
        s "I can see that. There's no point in pressuring you any further; it's not what I'm after."
    elif seymour_disposition == 0:
        l "I said no. And that's final."
        s "Do I need to remind you about the contract you just signed?"
        l "I don't care. There was no mention of having to do something like this, and I'm not comfortable with it!"
        l "I'm not doing it, and that's it."
        s "..."
        s "I see. There's no point in pressuring you any further; it's not what I'm after."
        $ flena = "serious"
    s "This will be it for tonight."
    if lena_seymour > 0:
        call friend_xp('seymour', -1) from _call_friend_xp_159
    hide seymour
    show seymour2 at lef3
    with short
    show seymour2 at lef with move
    "Seymour finally stood up from the couch, keeping the same calm, unbreakable demeanor he had been showing this entire time."
    if seymour_necklace == False:
        $ seymour_necklace = True
        s "It's getting late. Let me call a taxi for you. Oh, and take the necklace with you. It's a gift."
    else:
        s "It's getting late. Let me call a taxi for you."
    s "I'll let you know when our next session will take place. Until then, try enjoying your new lifestyle."
    s "I'm sure you'll want to get used to it."
    scene streetnight with long
    pause 1
    play sound "sfx/car.mp3"
    "A car was waiting for me on the street. I got inside and it drove me back home."
    scene lenahomenight_dark with long
    pause 0.5
    $ lena_makeup = 0
    $ lena_look = 1
    $ flena = "worried"
    play sound "sfx/door.mp3"
    scene lenaroomnight
    show lenabra
    with long
    if seymour_disposition == 1:
        l "That was so uncomfortable... He really wanted me to insert that vibrator, and then what?"
        l "I should've known this wouldn't be an ordinary job, but to think he'd go this far... Is that what I'm expected to do?"
        l "He didn't force me to do it though, even after I signed that contract. I guess we're still in business..."
        l "I have to endure at least one month. I will get another {color=#1ED50F}10{/color}{image=icon_money.webp} if I do..."
        l "Getting paid that amount per month... That's just incredible. I would be rich inside a year."
        l "But I'm not sure I can go through with this... I wonder what he'll ask from me next time..."
        "For now, all I could do was wait and see how things unfolded. I had no idea what to expect..."
    else:
        l "That was tense... I thought he would force me to rescind the contract, but he left me off the hook."
        l "I wonder if all his threats are just bravado in the end, and I'm falling for it..."
        $ flena = "serious"
        l "I have to endure at least one month. I will get another {color=#1ED50F}10{/color}{image=icon_money.webp} if I do..."
        l "But to think I could get that amount each month... I would become rich in just one year. Not to speak of all the other opportunities he's offering..."
        $ flena = "serious"
        l "But what will he ask from me next? I can't forget I'm being blackmailed by this man."
        l "I'll need to be on my toes. This is a dangerous situation I put myself in..."
    scene fade with long
    pause 1
    jump master_script

##SCREENS ##################################################################################################################
screen v9clothingshoplena():
    if lena_wits > 2 and lena_wardrobe_wits1 == False:
        imagebutton idle "wardrobe_lena_wits1.webp" hover "wardrobe_lena_wits1_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v9shopwits_lena')
    elif lena_wardrobe_wits1:
        imagebutton idle "wardrobe_lena_wits1.webp"
        imagebutton idle "wardrobe_lena_wits1_owned.webp"
    else:
        imagebutton idle "wardrobe_lena_wits1_block.webp"
    if lena_charisma > 2 and lena_wardrobe_charisma1 == False:
        imagebutton idle "wardrobe_lena_charisma1.webp" hover "wardrobe_lena_charisma1_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v9shopcharisma_lena')
    elif lena_wardrobe_charisma1:
        imagebutton idle "wardrobe_lena_charisma1.webp"
        imagebutton idle "wardrobe_lena_charisma1_owned.webp"
    else:
        imagebutton idle "wardrobe_lena_charisma1_block.webp"
    if lena_athletics > 2 and lena_wardrobe_athletics1 == False:
        imagebutton idle "wardrobe_lena_athletics1b.webp" hover "wardrobe_lena_athletics1b_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v9shopathletics_lena')
    elif lena_wardrobe_athletics1:
        imagebutton idle "wardrobe_lena_athletics1b.webp"
        imagebutton idle "wardrobe_lena_athletics1_owned.webp"
    else:
        imagebutton idle "wardrobe_lena_athletics1b_block.webp"
    if lena_lust > 2 and lena_wardrobe_lust1 == False:
        imagebutton idle "wardrobe_lena_lust1.webp" hover "wardrobe_lena_lust1_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v9shoplust_lena')
    elif lena_wardrobe_lust1:
        imagebutton idle "wardrobe_lena_lust1.webp"
        imagebutton idle "wardrobe_lena_lust1_owned.webp"
    else:
        imagebutton idle "wardrobe_lena_lust1_block.webp"
    imagebutton idle "v7shopback.webp" hover "v7shopback_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v9leaveshop_lena')
    add "sexshop_money.webp" pos (1799, 29)
    text "{font=[font_big_noodle_oblique]}{color=#000000}[lena_money]{/color}":
        size 30
        xpos 1815 ypos 56

screen v9tattooshopscreen():
    if lena_tattoo1 == False and lena_money > 1:
        imagebutton idle "tattooshop1.webp" hover "tattooshop1_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v9tat1')
    elif lena_tattoo1:
        imagebutton idle "tattooshop1_owned.webp"
    else:
        imagebutton idle "tattooshop1_blocked.webp"
    if lena_tattoo2 == False and lena_money > 2:
        imagebutton idle "tattooshop2.webp" hover "tattooshop2_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v9tat2')
    elif lena_tattoo2:
        imagebutton idle "tattooshop2_owned.webp"
    else:
        imagebutton idle "tattooshop2_blocked.webp"
    if lena_tattoo3 == False and lena_money > 3:
        imagebutton idle "tattooshop3.webp" hover "tattooshop3_hover.webp" focus_mask True action [ Play ("ch_one", "sfx/paper_click.mp3") ] , Jump ('v9tat3')
    elif lena_tattoo3:
        imagebutton idle "tattooshop3_owned.webp"
    else:
        imagebutton idle "tattooshop3_blocked.webp"
    imagebutton auto "sexshop_leave_%s.webp" pos (856, 730) action Play("ch_one", "sfx/paper_click.mp3"), Jump ('v9notat')
    add "sexshop_money.webp" pos (1799, 29)
    text "{font=[font_big_noodle_oblique]}{color=#000000}[lena_money]{/color}":
        size 30
        xpos 1815 ypos 56

screen v9seymourwardrobe():
    imagebutton idle "wardrobe_lena1.webp" hover "wardrobe_lena1_hover.webp" focus_mask True action SetVariable("lena_look", 1) , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    imagebutton idle "wardrobe_lena3.webp" hover "wardrobe_lena3_hover.webp" focus_mask True action SetVariable("lena_look", 3) , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    if lena_wardrobe_wits1:
        imagebutton idle "wardrobe_lena_wits1.webp" hover "wardrobe_lena_wits1_hover.webp" focus_mask True action SetVariable("lena_look", "wits") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "wardrobe_lena_wits1_block.webp"
    if lena_wardrobe_athletics1:
        imagebutton idle "wardrobe_lena_athletics1.webp" hover "wardrobe_lena_athletics1_hover.webp" focus_mask True action SetVariable("lena_look", "athletics") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "wardrobe_lena_athletics1_block.webp"
    if lena_wardrobe_charisma1:
        imagebutton idle "wardrobe_lena_charisma1.webp" hover "wardrobe_lena_charisma1_hover.webp" focus_mask True action SetVariable("lena_look", "charisma") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "wardrobe_lena_charisma1_block.webp"
    if lena_wardrobe_lust1:
        imagebutton idle "wardrobe_lena_lust1.webp" hover "wardrobe_lena_lust1_hover.webp" focus_mask True action SetVariable("lena_look", "lust") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "wardrobe_lena_lust1_block.webp"






screen mouse_tooltip(screen):


    zorder 9001 tag tooltip

    $ tooltip = GetTooltip()

    if screen == "saveload":
        if tooltip is not None and persistent.JD_phone_allowsavedesc:
            add MouseTooltip(Window(Text(tooltip.replace("JDMOD |","{image=JD_icon_save} "), style="tooltip_text"), style="tooltip_frame", background=Frame("JDMOD/images/gui/tooltip.png", 50, 50)), padding=(0,0))

    elif screen == "choice":
        if tooltip is not None and not _in_replay and persistent.JD_IGG_choice_tooltips:
            add MouseTooltip(Window(Text(tooltip, style="tooltip_text"), style="tooltip_frame", background=Frame("JDMOD/images/gui/tooltip.png", 50, 50, 25, 25)), padding=(0,0))

    elif tooltip is not None:
        add MouseTooltip(Window(Text(tooltip, style="tooltip_text"), style="tooltip_frame", background=Frame("JDMOD/images/gui/tooltip.png", 50, 50, 25, 25)), padding=(0,0))

style tooltip_text:
    font font_big_noodle_oblique
    color "#000"
    size 27
    xmaximum int(config.screen_width*0.25)
    justify True
    line_spacing 0
style tooltip_frame:
    padding (50, 50, 50, 50)
    minimum (int(config.screen_width*0.10), int(config.screen_height*0.10))


init python:
    import pygame

    class MouseTooltip(renpy.Displayable):
        def __init__(self, child, padding=None, **kwargs):
            super(MouseTooltip, self).__init__(**kwargs)
            
            self.child = renpy.displayable(child)
            self.padding = padding or (0,0)
            self.x, self.y = renpy.get_mouse_pos()
        
        def render(self, width, height, st, at):
            
            child_render = renpy.render(self.child, width, height, st, at)
            self.width, self.height = child_render.get_size()
            render = renpy.Render(self.width, self.height)
            
            
            x = self.x + self.padding[0]
            y = self.y + self.padding[1]
            if x + self.width > config.screen_width:
                x = self.x - self.width - self.padding[0]
            if y + self.height > config.screen_height:
                y = self.y - self.height - self.padding[1]
            
            render.blit(child_render, (x,y))
            return render
        
        def event(self, ev, x, y, st):
            
            if ev.type == pygame.MOUSEMOTION:
                self.x, self.y = x, y
                renpy.redraw(self, 0)
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc

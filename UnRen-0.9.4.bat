@echo off
chcp 65001
REM --------------------------------------------------------------------------------
REM Configuration:
REM	Set a Quick Save and Quick Load hotkey - http://www.pygame.org/docs/ref/key.html
REM --------------------------------------------------------------------------------
set "quicksavekey=K_F5"
set "quickloadkey=K_F9"
REM --------------------------------------------------------------------------------
REM !! END CONFIG !!
REM --------------------------------------------------------------------------------
REM The following variables are Base64 encoded strings for unrpyc and rpatool
REM Due to batch limitations on variable lengths, they need to be split into
REM multiple variables, and joined later using powershell.
REM --------------------------------------------------------------------------------
REM unrpyc by CensoredUsername
REM	https://github.com/CensoredUsername/unrpyc
REM Edited to remove multiprocessing and adjust output spacing 44febb0 2019-10-07T07:06:47.000Z
REM	https://github.com/F95Sam/unrpyc
REM --------------------------------------------------------------------------------
REM set decompcab01=

set decompcab01=TVNDRgAAAAA35QAAAAAAADAYAAAAAAAAAwEBAAsABAAMNwAAABgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
set decompcab02=AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHQZAAAIAAEA9aYAAAAAAAAAADhYk6UgAF9faW5pdF9fLnB5AOosAAD1pgAAAACTVlejIABhc3RkdW1wLnB5AFKaAADf0wAAAADyViatIABjb2RlZ2VuLnB5AA1tAAAxbgEAAABMWDSzIABtYWdpYy5weQDkdwAAPtsBAAAA8lb0uyAAc2NyZWVuZGVjb21waWxlci5weQDRTgAAIlMCAAAASlgSjiAAc2wyZGVjb21waWxlci5weQBhFAAA86ECAAAAgVX9lCAAdGVzdGNhc2VkZWNvbXBpbGVyLnB5ALMVAABUtgIAAAAjVaGcIAB0cmFuc2xhdGUucHkA8koAAAfMAgAAAFBYpAIgAHVucnB5Yy5weQCwSQAA+RYDAAAAOFivtiAAdXRpbC5weQAnKAAAqWADAAAANVSuPiAAZGVvYmZ1c2NhdGUucHkAmceUV3YeAIBDS+w9/W/bxpI/X4D8D/toBJJQRnGcpoca9cMpttLo6tiG7bxckBoCRa4sNhTJ8sOKXtH//WZmd8nlckk5Th96D1c3qKX9mJ2dnZ2vnV3vseMk3Wbh7apgQ3/EDvafH7APZRayn8bsyl9FPI959vjRHvxjFzxbh3keJjELc7biGV9s2W3mxQUPXLbMOGfJkvkrL7vlLisS5sVblvIshw7JovDCOIxvmcd8GBLhQeNiBZDyZFlsvIxD+4B5eZ74oQcgWZD45ZrHhVfgkMsQkGHDYsWZcyV7OCMaJ+BehADDmGG1qmWbsFglZcEynhdZ6CMYFxr5URkgJqo6CtehHAS7EzVyhAegyxymggi7bJ0E4RJ/c5pfWi6iMF+5LAgR+qIsoDDHQp/H2Atm8yzJWM4jQg6AhDABmnSNIzXDgVIkbiHJlWPJZpWsm/MJCatlmcUwMKduQQLko3F/4X6BJdhjmURRssE5+kkchDi1/FCu4jXUe4vkjtO0xNLHSQFYC1RwRdJ6pWVVvvKiiC24JB8MHsYIDUvVzDJEIy+AH0IvYmmS0bjmjMcKjzdTdnX++vr95HLKZlfs4vL8H7OT6QlzJlfw3XHZ+9n1m/N31wxaXE7Orj+w89dscvaB/TQ7O3HZ9H8uLqdXV+z8EqHN3l6czqZQPDs7Pn13Mjv7kb2Crmfn1+x09nZ2DXCvz2lMCW02vUJ4b6eXx2/g6+TV7HR2/cFFWK9n12cI+fX5JZuwi8nl9ez43enkkl28u7w4v5oCEicA+Wx29voSBpq+nZ5dj2FgKGPTf8AXdvVmcnqKoyG4yTuYxiUiyo7PLz5czn58c83enJ+eTKHw1RTwm7w6nYrRYHbHp5PZW5edTN5OfpxSr3MARJPElgJN9v7NFEtx1An8O76enZ/hfI7Pz64v4asL0728rnq/n11NXTa5nF0hZV5fnr+lmSJ1odM5wYGuZ1MBCCnfXCBogt/fXU0rmOxkOjkFcFfYWc5VtYdFfvxomQEDz+fLsigzPp+zcI08wco4
set decompcab03=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
set decompcab04=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
set decompcab05=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
set decompcab06=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
set decompcab07=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
set decompcab08=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
set decompcab09=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
set decompcab10=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
set decompcab11=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
set decompcab12=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
set decompcab13=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
set decompcab14=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
set decompcab15=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
set decompcab16=4aXPxXGg5fNtHUZFZfixelCTXjcwekzly7xtGEilFCbhdTIb60gDhDKkU0STYJwHszVexN938zMKCYpupwvd2TH++bXTdnwEry1gzIdyMQARxj7FP78rRbIZci44K386ONhSy63RffDWJBkiO30cRg/q2HOJNZwK78VSQtTpnNw++S148htRe1zteVbOrvRN0bNFZdrzcHAo767O8kmN7TFI+mLDbKMaMIu3/mnkKW3OdAqK6x8bWRHh1ZRzeqpn/bVvA5XaSuXps+DOLMKLnpq6M9uJFHwEIoJYzc+B3sWNKhHFEb2qE2sAWHpz6uhsgiAf6eQ9Ya9Km5QXi1MHsGlhyD1Cd8Eq6ar2TUPdJkp8T9y81Q/8RKtvDdWGGYI1VOabE+hWLVcveFyvhloBotMfqdAyyX7AzZ2Liy0vHhdUQ1u5Mz+r1Ok0d5w9B9Yfmz7g7y+hEnQEg44n4RPb7D4V0IN2jZM/vxtJqNgK/7+FBbJGSmWgL3wDeTBlnwD5BUETaRT536zV6kIk3LAuhZQS3eDdH57MSJ+O1wEwzeKThSLJ7/GpZ3eLdo1oUWcaBh7HgQERv+hSI8rekzqNSPAEo/bNllC4n7ubixsL1c2UajdCKeCCB4dtOYnsf0vCwGUyTUdI/HoN/hahfjCBBd9tq1+Q6BxVZzikUv1m0oBYbdL6KbY8q5u1aVn/M8kRNdgFJ+oLMTK1lNHAu/fdB+pH1a3MWbHaSr7T6WxVzZC6hbKyTphwsAG3YalQulAMhRiUjbFQEV7qysqk2bf68pkkw5MT6J+gJQsjf4NMGmRtz0PVQWg3IHGCVhlKiJfPN4IoELsn/pQN6y+9iWA1mHxPBM7lYHMcX4NZ4owwMjOSZQ/jO3hyD0ccQCah8XdD/UtXpRqBkdKlzOLkEpzaqbvKaf/2MvfardUz20jleDl3KnFWW8PpDUvG9bsF0ACKFLvN28fBZZcXcXE39I7oe9w+tjbKfxdGbTD5OrQysWU4fZKdaii3CS6hSSqpGWZl0/Gpub0Ft/HUyaPG+TjpQYr4m3g0NXy+LKZ/Hlmum4dRfWHT6eXQ/xYehVHrquPFOeuu/Z7sZde/LcS817/L7iFqHI3A9hJDssfruwIW3aA7ioKnq0/Xgq0kKxF2+o54IdCqpWVq9zYprlKxKqci3BN9uCg4mp2k7iLhDPSjSxoZecZyDqO5JiaOk7KC5ZZw2hGNt7Rc2Yny8+ktRGaZe5mPUk6/O85HPIEqbWkpccbhoXojjHqSNjCeUH+KfumHJpcHMXRcaTZFrSgRn3ReZzyekOirxmDDCxBRUne895IeQ9uTvOb4m/DkrmdnSN/PacXVlkYi9DNoIDK8RXP5SkRGgEZ9pFLf1YZQNBPgOYHYqUKVOJtcEoFyZpMCpnNaPhrWJA8ocx6VQ510GhPWCWGCtC3G7MVarvPyIYkwJ67mKcmC085KlU2M1+K6WmL1qLxErD4CkSvBkzrDj3pWnDQYnvJTWKyRWktz7fYM+gLE60FwuP/q6P3mwSDYOQzeHuz/uLM92A7CzUP6TkT//c7R6/13RwG1ONjcO/oQ7L8KNvc+BH/f2dvuBYN/vD0YHB4G+wfU2c6bt7s7A/p1Z29r9932zt4Pwff0JkL5dnfe7BxRt0f7PKTqbGdwiO7eDA62XtPXze93dneOPvSoq1c7R3vo99X+QbAZvN08ONrZere7eRC8fXfwdv9wQCAgRnBvZ+/VAY0zeDPYO+rTuPRbMPiRvgSHrzd3dzEY7pl3NIcDQBls7b/9cLDzw+uj4PX+7vaAfvx+QNBtfr87kMFoalu7mztvesH25pvNHwb81j71gxmiocAYvH89wI8Yc5P+t3W0s7+HyWzt7x0d0NcezfXgyLz8fudw0As2D3YOgZZXB/tvME0glt7Z527ozb2B9AOku2tDTfD93eHAdBlsDzZ3qbdDvCwT1c37S/SfCtvmvCJKxi7NKeZsIDAqXmDPSFpwlbdZohfttM3TZHSZpdDoCpMPD5mrGDxcUtzJ2WRNt+Tll276AGATNX3pbeSeA5FSNVhE/lNmzg67DCD+lDq4pUM0Ta7ZKiBVeHWmBMlOy8wQbV0ufvyUo9EzUzlYsUoq4YOxfQVXs8k0haKPZpzEV0S2iuQ8gXMgU8WSS/WWkTqTEm8HKCTZeFWZDRpDpKuWBHjTntLFpSXHQYI7wx91ztSLeVH2pWNo0emUXrB3DuKwzxhSmn6ScgaGcgqlHgrGrljFqJG4dia4RdVEhoA67MoiSD5toqmTOy1mO9WsefVy25PIzEIUdmrWWc+YElYxJHsSyUpIERbU5NVI4CLYOrMG+lLSO5LnBrNriTddgkMT3mAfMu7mLC6TZ99o/5UVcfTr0c31aUXXWey58+c7IpmOgML3iXDVqjQcYVvwUtwpuEpeAjaNqHqf9OMdzR17UeUYzEv9CeOYnId8XetvAmZrmsItCSCW51W9RP2Yk4AumSyL2LZ8FgdmP4gjmaietW6c8dM9z89+7jFGs5xk4BdSbFtW6E4FAf+IkE2piGVmGKeTpcE/mOyAzEEnYdcqzIsuyfsiZZhWml85j2xe5BygbstyzwWVIetpZABYF9bKlOaA2Ve5eEwLyXIvdXzxO2crQX57cFEpB3RhA0uOZM6c2V/aHmwdfHjrTnWsYTZTrVq1TpVFkDFXL1hRJYhGwYh2Fa2sPvh0gL4yByBXCnzmtnhVxZjQeOer+hs02HdmOZxCklhuVXJO1l7BT9Ic/z3EYTWvijStikEJ+VCMlqpGb9FJp5tzXdXdlKKGvKuStptqx/Tz8fraKkv4Z52Dwd7bD0Rqt5521mux6NU27IY7mS6yIUXZw2jJCTKigdZWRWgEoeU8xdJCTAlOJfXgubiqsSqgGhVvpuOFFdufS8l2nofptlGr3VZjdb09c7Bqd7UX4H8WGEYhtOSowpzS6/CO8szAj7vvi/xjkglFpmu5uNPIM+g6FgBPOYmbA+NSPUxPasdXrpNL9xv3kldNoXIGPn5Jbwh+rlxJGJp5ferEB/xSOtbdNJbMzOrUPo+8ZoLJRjH7+YdnklzEo7v7Hx9OjsTv+A6KnvUXCNecM9tduwcpQGIKnqxxFxoB84+eqRdaqaykBW4oVU0Sd6HsJ7uoIjdg2+794Rynjm2NSAnzC2BHNLjWeszHu+wbusWzh+Be+dTFOvmelHnha1NxlwFKbMnPamtKaZH+AwhanQCt+unN18+QOK15WOPeWW/UG/eS3nnvonfZS/2kRv6bT3C+fuYSnJgDgXmqEinLHy84zJg/ntFLbLlKPHTnv/2Hfuleh34LbghwXlSZxi4dcm0R5//Q5v/Q5j+HNoO6fD6FOJ+NPurKWxY5MNgwSd+gJVSesexURtJkSjw6k9ZcBAVg6ixnSYv7UxEPurjcbSLNJnku/tM38YRopUhTl7PsY6mKoUzuHkyHGGMmUFuMhktu3ZTKDCY2EGQbXHfKy2MJUmbMwpNP3/41dHevtjI7e78LBzn1YhQ8Dp7+5RmtW/UjjtxpFAVfBl+voePVRX3WJlKVOJEmCk88PRM/rPd/mtVfX3eyx7hDo6eWO9AQj3WLWsy5Ep2pOAOYCfC32sl6wYRC2kXzjtY/1RY3dytT1yTjWlDyfu1qlR/5mLHDCoQDI8DY4gwfH540FJ+zTB8fB1+q089jFyo5rRUGks89IGiF/mTS5Q0cxmcjemvz+63twavVtadff/OXZ9/+9W9htcX5bZXjJ/KD0DY3DK8z/4QETxh5i0s/bF4i599/aheX6c8fJ1dZfv2vopzObm4/3f0i0/3h9c7//H33zd7+2/89ODx69+P7f3z4Z4WCJ19tIL/on4EHAbl/9uwbhYzGIn8mLtzYy7koAflIuWjS6qenwrGon57TL39d/WMTzZJbRUKdNXdURg9afQ297lc6bm+rvqv2dDLZC6NEErhhWg4zsNETJa/XLrPNLJ7c/aLqNpsrijOplQT9RCsxuaKEWAnOueotC88ZHVq5WbbzRIxMJlAQ5l7jgst1oXiQIWflz6blULSOHHqlLCtKaahSHLNL8Nkk0Tci+2Zy1d3ZVGksYRLgxzvnqpwJVzPReWIx3iyzFJN6bK3w1HrTvEDBYFYLGux4Lkp1lZurcm316TdRrZ2xz3HbRYqJZa1IFdmux5aQHAkrEk4b2GQklmzXybKcXbG+Ur1tRx0r32pY5y22AEKTdS/cDn1aFc+MvDeeg9vmxae7v/eN17yntu3rqcKgwoUqnwptHrT0ovhHGVolPmLTqtI11jTUabGBX/LJIiqPpwwFSvHNs/Wal+7aqqg79c54EnxDLMvf1I9qtwVTs4trINgzfSWl0nPJdG3D+1C5Tu28tdV1gvg0sjcMghdtplQJ2BLLptMPKa+Qqogp201f1l2vupZQeMY5hZ7VBcTuBUHTC1JX+qkJjHWRpoka0dApFT7t5zMhaD1mXix/ExhxhDEnkJPJuHxpo1Ft6LNozvYf+fZ/bT+YXY1jPXoo8CnvOtqpZ6pUhOxSkUtC97D9Pzy2MkMhAtAIQ0u1Xc1OwzAMvvcpopyIVE3iikA8BPAAE61g4mfVxg4T4t2JHf+laboWicMu+5Fdx/E+24m/f95spa6Y2VwvqbcYP7B6KReh3/jLC1uVR+X7Yff89t5f3rvp1u+2w5kz/A/X7bYvnxF1I7Zn2mEaut313CpMpB03nnIbtuKR5hgETXk044sqak9iJiEg3zIdjTZmSOP1Mc2RKHZUP5GHkGMi1AE9O6Hl4lmtVyJowyxbres3PH025FcZV4ghojCQUYoIowhO9gMyX0J6FozJ56ZyWMgGymHT6KUWintK9M1A5Jy0QAP7MJPm
set decompcab17=RAzLvJokJKg+djMfw6h2Wmr1oBoNh313grtuSr/OjHPuMfX2oG26R4gniRS5g6A2ZDLkEITpdGGbSZeC0tIHUFFDGABCZAHECUfVQ8E6V1PXWeQ5dFqryx0CQ1xqKZrhzuVgwsRWN7leU1duwQBN80f3sbvfuNBS98FwM2v3iQBE8YUay3ltl968jXDCIkNIzyMOBhJJQL3n/ut+kWPgzX1hdSQOSlKt5bXImChb9+0hdsJ5SdM99j+hPiq0GCs64yQmI1bnZes05pgy2gFrw1oEgdPzd9y0Nb6dTXyTDBE2knZRczVMniadVzVMSh0LHlH6DQ8sq16VKJmWyhvAijc4VRt/pfTmoT8AvIGzLi6NOQFmRgzG8hSVYKzF5bpepvhcymb0DWVY9xpfiWAXhKZUd93O+QU=

REM --------------------------------------------------------------------------------
REM rpatool by Shizmob 9a58396 2019-02-22T17:31:07.000Z
REM	https://github.com/Shizmob/rpatool
REM --------------------------------------------------------------------------------
REM set rpatool01=

set rpatool01=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

REM --------------------------------------------------------------------------------
REM !! DO NOT EDIT BELOW THIS LINE !!
REM --------------------------------------------------------------------------------
set "version=forall(v9.4) (240216)"
title UnRen.bat - %version%
:init
REM --------------------------------------------------------------------------------
REM Splash screen
REM --------------------------------------------------------------------------------
cls
echo.
echo     __  __      ____               __          __
echo    / / / /___  / __ \___  ____    / /_  ____ _/ /_
echo   / / / / __ \/ /_/ / _ \/ __ \  / __ \/ __ ^`/ __/
echo  / /_/ / / / / _^, _/  __/ / / / / /_/ / /_/ / /_
echo  \____/_/ /_/_/ ^|_^|\___/_/ /_(_)_.___/\__^,_/\__/ - %version%
echo   Sam @ www.f95zone.to ^& Gideon
echo.
echo  ----------------------------------------------------
echo.

REM --------------------------------------------------------------------------------
REM We need powershell for later, make sure it exists
REM --------------------------------------------------------------------------------
if not exist "%SystemRoot%\system32\WindowsPowerShell\v1.0\powershell.exe" (
	echo	! Error: Powershell is required, unable to continue.
	echo			 This is included in Windows 7, 8, 10. XP/Vista users can
	echo			 download it here: http://support.microsoft.com/kb/968929
	echo.
	pause>nul|set/p=.			Press any key to exit...
	exit
)

REM --------------------------------------------------------------------------------
REM Set our paths, and make sure we can find python exe
REM --------------------------------------------------------------------------------

set /p currentdir=Enter the path to the game, drag'n'drop it or press enter immediately if this tool is already in the desired folder:

IF [%currentdir%] == [] (set "currentdir=%cd%") ELSE (cd "%currentdir%")

if exist "lib\windows-x86_64\python.exe" (
	if not "%PROCESSOR_ARCHITECTURE%"=="x86" (
		set "pythondir=%cd%\lib\windows-x86_64\"
	) else if exist "lib\windows-i686\python.exe" (
		set "pythondir=%cd%\lib\windows-i686\"
	)
) else if exist "lib\windows-i686\python.exe" (
	set "pythondir=%cd%\lib\windows-i686\"
)
if exist "lib\py2-windows-x86_64\python.exe" (
	if not "%PROCESSOR_ARCHITECTURE%"=="x86" (
		set "pythondir=%cd%\lib\py2-windows-x86_64\"
    ) else if exist "lib\py2-windows-i686\python.exe" (
		set "pythondir=%cd%\lib\py2-windows-i686\"
	)
) else if exist "lib\py2-windows-i686\python.exe" (
	set "pythondir=%cd%\lib\py2-windows-i686\"
)
if exist "lib\py3-windows-x86_64\python.exe" (
	if not "%PROCESSOR_ARCHITECTURE%"=="x86" (
		set "pythondir=%cd%\lib\py3-windows-x86_64\"
    ) else if exist "lib\py3-windows-i686\python.exe" (
		set "pythondir=%cd%\lib\py3-windows-i686\"
	)
) else if exist "lib\py3-windows-i686\python.exe" (
	set "pythondir=%cd%\lib\py3-windows-i686\"
)

if not exist "%pythondir%" (
	echo	! Error: Cannot locate python directory, unable to continue.
	echo			 Are you sure we're in the game's root directory?
	echo.
	pause>nul|set/p=.			Press any key to exit...
	exit
)

if exist "game" if exist "renpy" (
	set "renpydir=%cd%\renpy\"
	set "gamedir=%cd%\game\"
) else (
	echo	! Error: Cannot locate game directory, unable to continue.
	echo			 Are you sure we're in the game's root directory?
	echo.
	pause>nul|set/p=.			Press any key to exit...
	exit
)

set "PYTHONHOME=%pythondir%"
if exist "lib\pythonlib2.7" (
	set "PYTHONPATH=%cd%\lib\pythonlib2.7"
) else if exist "lib\python2.7" (
	set "PYTHONPATH=%cd%\lib\python2.7"
) else if exist "lib\python3.9" (
	set "PYTHONPATH=%cd%\lib\python3.9"
)

:menu
REM --------------------------------------------------------------------------------
REM Menu selection
REM --------------------------------------------------------------------------------
set exitoption=
echo	Available Options:
echo	 1) Extract RPA packages
echo	 2) Decompile rpyc files
echo	 3) Enable Console and Developer Menu
echo	 4) Enable Quick Save and Quick Load
echo	 5) Force enable skipping of unseen content
echo	 6) Force enable rollback (scroll wheel)
echo	 7) Deobfuscate Decompile rpyc files
echo	 8) Extract and Decompile
echo	 9) All of the above
echo.
set /p option=.  Enter a number:
echo.
echo  ----------------------------------------------------
echo.
if "%option%"=="1" call :extract
if "%option%"=="2" call :decompile
if "%option%"=="3" call :console
if "%option%"=="4" call :quick
if "%option%"=="5" call :skip
if "%option%"=="6" call :rollback
if "%option%"=="7" call :decompile
if "%option%"=="8" call :extract
if "%option%"=="9" call :extract
call :init

:extract
REM --------------------------------------------------------------------------------
REM Write _rpatool.py from our base64 strings
REM --------------------------------------------------------------------------------
set "rpatool=%cd%\rpatool.py"
echo	Creating rpatool...
if exist "%rpatool%.tmp" (
	del "%rpatool%.tmp"
)
if exist "%rpatool%" (
	del "%rpatool%"
)

REM echo %rpatool%>> "%rpatool%.tmp"
echo %rpatool01%>> "%rpatool%.tmp"
set "rpatoolps=%rpatool:[=`[%"
set "rpatoolps=%rpatoolps:]=`]%"
set "rpatoolps=%rpatoolps:^=^^%"
set "rpatoolps=%rpatoolps:&=^&%"
powershell.exe -nologo -noprofile -noninteractive -command "& { [IO.File]::WriteAllBytes(\"%rpatoolps%\", [Convert]::FromBase64String([IO.File]::ReadAllText(\"%rpatoolps%.tmp\"))) }"

echo.
echo	Remove RPA archives after extraction?
set /p delrpa=.	Enter (y/n):
if "%delrpa%" == "y" (
	echo	+ RPA archives will be deleted
	echo.
)
if "%delrpa%" == "n" (
	echo	+ RPA archives won't be deleted
	echo.
)

REM --------------------------------------------------------------------------------
REM Unpack RPA
REM --------------------------------------------------------------------------------
echo	Searching for RPA packages
cd %gamedir%

if "%delrpa%" == "y" (
	if exist "%pythondir%Lib" (
		"%pythondir%python.exe" -O "%rpatool%" -r "%gamedir%
	) else (
		"%pythondir%python.exe" "%rpatool%" -r "%gamedir%
	)
) else (
	if exist "%pythondir%Lib" (
		"%pythondir%python.exe" -O "%rpatool%" "%gamedir%
	) else (
		"%pythondir%python.exe" "%rpatool%" "%gamedir%
	)
)

echo.

REM --------------------------------------------------------------------------------
REM Clean up
REM --------------------------------------------------------------------------------
echo	Cleaning up temporary files...
if exist "%rpatool%.tmp" del "%rpatool%.tmp"
if exist "%rpatool%" del "%rpatool%"
if exist "%cd%\__pycache__" rmdir /Q /S "%cd%\__pycache__"
cd %currentdir%
echo.
if "%option%" == "9" call :decompile
if "%option%" == "8" call :decompile
call :finish
exit /b

:decompile
REM --------------------------------------------------------------------------------
REM Write to temporary file first, then convert. Needed due to binary file
REM --------------------------------------------------------------------------------
set "decompcab=%cd%\decomp.cab"
set "decompilerdir=%cd%\decompiler"
set "unrpycpy=%cd%\unrpyc.py"
set "deobfuscate=%cd%\deobfuscate.py"
if exist "%decompcab%.tmp" (
	del "%decompcab%.tmp"
)
if exist "%decompcab%" (
	del "%decompcab%"
)
if exist "%decompilerdir%" (
	rmdir /Q /S "%decompilerdir%"
)

if exist "%unrpyc%.tmp" (
	del "%unrpyc%.tmp"
)
if exist "%unrpyc%" (
	del "%unrpyc%"
)

echo %decompcab01%>> "%decompcab%.tmp"
echo %decompcab02%>> "%decompcab%.tmp"
echo %decompcab03%>> "%decompcab%.tmp"
echo %decompcab04%>> "%decompcab%.tmp"
echo %decompcab05%>> "%decompcab%.tmp"
echo %decompcab06%>> "%decompcab%.tmp"
echo %decompcab07%>> "%decompcab%.tmp"
echo %decompcab08%>> "%decompcab%.tmp"
echo %decompcab09%>> "%decompcab%.tmp"
echo %decompcab10%>> "%decompcab%.tmp"
echo %decompcab11%>> "%decompcab%.tmp"
echo %decompcab12%>> "%decompcab%.tmp"
echo %decompcab13%>> "%decompcab%.tmp"
echo %decompcab14%>> "%decompcab%.tmp"
echo %decompcab15%>> "%decompcab%.tmp"
echo %decompcab16%>> "%decompcab%.tmp"
echo %decompcab17%>> "%decompcab%.tmp"
set "decompcabps=%decompcab:[=`[%"
set "decompcabps=%decompcabps:]=`]%"
set "decompcabps=%decompcabps:^=^^%"
set "decompcabps=%decompcabps:&=^&%"
powershell.exe -nologo -noprofile -noninteractive -command "& { [IO.File]::WriteAllBytes(\"%decompcabps%\", [Convert]::FromBase64String([IO.File]::ReadAllText(\"%decompcabps%.tmp\"))) }"

echo.

REM --------------------------------------------------------------------------------
REM Once converted, extract the cab file. Needs to be a cab file due to expand.exe
REM --------------------------------------------------------------------------------
echo	Extracting _decomp.cab...
mkdir "%decompilerdir%"
expand -F:* "%decompcab%" "%decompilerdir%" >nul
move "%decompilerdir%\unrpyc.py" "%unrpycpy%" >nul
move "%decompilerdir%\deobfuscate.py" "%deobfuscate%" >nul

REM --------------------------------------------------------------------------------
REM Decompile rpyc files
REM --------------------------------------------------------------------------------
echo	Searching for rpyc files...
cd %gamedir%
REM set "PYTHONPATH=%pythondir%Lib"

if exist "%pythondir%Lib" (
	if "%option%" == "2" (
		echo    + Searching for rpyc files in %gamedir%
		"%pythondir%python.exe" -O "%unrpycpy%" --init-offset "%gamedir%
	)
	if "%option%" == "9" (
		echo    + Searching for rpyc files in %gamedir%
		"%pythondir%python.exe" -O "%unrpycpy%" --init-offset "%gamedir%
	)
	if "%option%" == "8" (
		echo    + Searching for rpyc files in %gamedir%
		"%pythondir%python.exe" -O "%unrpycpy%" --init-offset "%gamedir%
	)
	if "%option%" == "7" (
		echo    + Searching for rpyc files in %gamedir%
		"%pythondir%python.exe" -O "%unrpycpy%" --init-offset --try-harder "%gamedir%
	)
) else (
	if "%option%" == "2" (
		echo    + Searching for rpyc files in %gamedir%
		"%pythondir%python.exe" "%unrpycpy%" --init-offset "%gamedir%
	)
	if "%option%" == "9" (
		echo    + Searching for rpyc files in %gamedir%
		"%pythondir%python.exe" "%unrpycpy%" --init-offset "%gamedir%
	)
	if "%option%" == "8" (
		echo    + Searching for rpyc files in %gamedir%
		"%pythondir%python.exe" "%unrpycpy%" --init-offset "%gamedir%
	)
	if "%option%" == "7" (
		echo    + Searching for rpyc files in %gamedir%
		"%pythondir%python.exe" "%unrpycpy%" --init-offset --try-harder "%gamedir%
	)
)
echo.

REM --------------------------------------------------------------------------------
REM Clean up
REM --------------------------------------------------------------------------------
echo	Cleaning up temporary files...
cd %currentdir%
if exist "%unrpycpy%o" del "%unrpycpy%o"
if exist "%unrpyc%" del "%unrpyc%"
if exist "%unrpycpy%" del "%unrpycpy%"
if exist "%unrpycpy%.tmp" del "%decompcab%.tmp"
if exist "%decompcab%" del "%decompcab%"
if exist "%decompcab%.tmp" del "%decompcab%.tmp"
if exist "%deobfuscate%" del "%deobfuscate%"
if exist "%deobfuscate%o" del "%deobfuscate%o"
if exist "__pycache__" rmdir /Q /S "__pycache__"
if exist "%decompilerdir%" rmdir /Q /S "%decompilerdir%"
echo.
if "%option%" == "9" call :console
call :finish
exit /b

:console
REM --------------------------------------------------------------------------------
REM Drop our console/dev mode enabler into the game folder
REM --------------------------------------------------------------------------------
echo	Creating Developer/Console file...
set "unren-console=%cd%\game\unren-console.rpy"
set unren-console01=aW5pdCA5OTkgcHl0aG9uOg0KICAgIGNvbmZpZy5kZXZlbG9wZXIgPSBUcnVlDQogICAgY29uZmlnLmNvbnNvbGUgPSBUcnVl

if exist "%unren-console%.tmp" (
	del "%unren-console%.tmp"
)
if exist "%unren-console%" (
	del "%unren-console%"
)

echo %unren-console01%>> "%unren-console%.tmp"
set "unren-consoleps=%unren-console:[=`[%"
set "unren-consoleps=%unren-consoleps:]=`]%"
set "unren-consoleps=%unren-consoleps:^=^^%"
set "unren-consoleps=%unren-consoleps:&=^&%"
powershell.exe -nologo -noprofile -noninteractive -command "& { [IO.File]::WriteAllBytes(\"%unren-consoleps%\", [Convert]::FromBase64String([IO.File]::ReadAllText(\"%unren-consoleps%.tmp\"))) }"

echo	+ Console: SHIFT+O
echo	+ Dev Menu: SHIFT+D
echo.
del "%unren-console%.tmp"

if "%option%" == "9" call :quick
call :finish
exit /b

:quick
REM --------------------------------------------------------------------------------
REM Drop our Quick Save/Load file into the game folder
REM --------------------------------------------------------------------------------
echo	unren-quick.rpy...
set "unren-quick=%cd%\game\unren-quick.rpy"
set unren-quick01=aW5pdCA5OTkgcHl0aG9uOg0KICAgIHRyeToNCiAgICAgICAgY29uZmlnLnVuZGVybGF5WzBdLmtleW1hcFsncXVpY2tTYXZlJ10gPSBRdWlja1NhdmUoKQ0KICAgICAgICBjb25maWcua2V5bWFwWydxdWlja1NhdmUnXSA9ICdLX0Y1Jw0KICAgICAgICBjb25maWcudW5kZXJsYXlbMF0ua2V5bWFwWydxdWlja0xvYWQnXSA9IFF1aWNrTG9hZCgpDQogICAgICAgIGNvbmZpZy5rZXltYXBbJ3F1aWNrTG9hZCddID0gJ0tfRjknDQogICAgZXhjZXB0Og0KICAgICAgICBwYXNz

if exist "%unren-quick%.tmp" (
	del "%unren-quick%.tmp"
)
if exist "%unren-quick%" (
	del "%unren-quick%"
)

echo %unren-quick01%>> "%unren-quick%.tmp"
set "unren-quickps=%unren-quick:[=`[%"
set "unren-quickps=%unren-quickps:]=`]%"
set "unren-quickps=%unren-quickps:^=^^%"
set "unren-quickps=%unren-quickps:&=^&%"
powershell.exe -nologo -noprofile -noninteractive -command "& { [IO.File]::WriteAllBytes(\"%unren-quickps%\", [Convert]::FromBase64String([IO.File]::ReadAllText(\"%unren-quickps%.tmp\"))) }"

echo	Default hotkeys:
echo	+ Quick Save: F5
echo	+ Quick Load: F9
echo.
del "%unren-quick%.tmp"

if "%option%" == "9" call :skip
call :finish
exit /b

:skip
REM --------------------------------------------------------------------------------
REM Drop our skip file into the game folder
REM --------------------------------------------------------------------------------
echo	Creating skip file...
set "unren-skip=%cd%\game\unren-skip.rpy"
set unren-skip01=aW5pdCA5OTkgcHl0aG9uOg0KICAgIF9wcmVmZXJlbmNlcy5za2lwX3Vuc2VlbiA9IFRydWUNCiAgICByZW5weS5nYW1lLnByZWZlcmVuY2VzLnNraXBfdW5zZWVuID0gVHJ1ZQ0KICAgIHJlbnB5LmNvbmZpZy5hbGxvd19za2lwcGluZyA9IFRydWUNCiAgICByZW5weS5jb25maWcuZmFzdF9za2lwcGluZyA9IFRydWUNCiAgICB0cnk6DQogICAgICAgIGNvbmZpZy5rZXltYXBbJ3NraXAnXSA9IFsgJ0tfTENUUkwnLCAnS19SQ1RSTCcgXQ0KICAgIGV4Y2VwdDoNCiAgICAgICAgcGFzcw0K

if exist "%unren-skip%.tmp" (
	del "%unren-skip%.tmp"
)
if exist "%unren-skip%" (
	del "%unren-skip%"
)

echo %unren-skip01%>> "%unren-skip%.tmp"
set "unren-skipps=%unren-skip:[=`[%"
set "unren-skipps=%unren-skipps:]=`]%"
set "unren-skipps=%unren-skipps:^=^^%"
set "unren-skipps=%unren-skipps:&=^&%"
powershell.exe -nologo -noprofile -noninteractive -command "& { [IO.File]::WriteAllBytes(\"%unren-skipps%\", [Convert]::FromBase64String([IO.File]::ReadAllText(\"%unren-skipps%.tmp\"))) }"

echo	+ You can now skip all text using TAB and CTRL keys
echo.
del "%unren-skip%.tmp"

if "%option%" == "9" call :rollback
call :finish
exit /b

:rollback
REM --------------------------------------------------------------------------------
REM Drop our rollback file into the game folder
REM --------------------------------------------------------------------------------
echo	Creating rollback file...
set "unren-rollback=%cd%\game\unren-rollback.rpy"
set unren-rollback01=aW5pdCA5OTkgcHl0aG9uOg0KICAgIHJlbnB5LmNvbmZpZy5yb2xsYmFja19lbmFibGVkID0gVHJ1ZQ0KICAgIHJlbnB5LmNvbmZpZy5oYXJkX3JvbGxiYWNrX2xpbWl0ID0gMjU2DQogICAgcmVucHkuY29uZmlnLnJvbGxiYWNrX2xlbmd0aCA9IDI1Ng0KICAgIGRlZiB1bnJlbl9ub2Jsb2NrKCAqYXJncywgKiprd2FyZ3MgKToNCiAgICAgICAgcmV0dXJuDQogICAgcmVucHkuYmxvY2tfcm9sbGJhY2sgPSB1bnJlbl9ub2Jsb2NrDQogICAgdHJ5Og0KICAgICAgICBjb25maWcua2V5bWFwWydyb2xsYmFjayddID0gWyAnS19QQUdFVVAnLCAncmVwZWF0X0tfUEFHRVVQJywgJ0tfQUNfQkFDSycsICdtb3VzZWRvd25fNCcgXQ0KICAgIGV4Y2VwdDoNCiAgICAgICAgcGFzcw==

if exist "%unren-rollback%.tmp" (
	del "%unren-rollback%.tmp"
)
if exist "%unren-rollback%" (
	del "%unren-rollback%"
)

echo %unren-rollback01%>> "%unren-rollback%.tmp"
set "unren-rollbackps=%unren-rollback:[=`[%"
set "unren-rollbackps=%unren-rollbackps:]=`]%"
set "unren-rollbackps=%unren-rollbackps:^=^^%"
set "unren-rollbackps=%unren-rollbackps:&=^&%"
powershell.exe -nologo -noprofile -noninteractive -command "& { [IO.File]::WriteAllBytes(\"%unren-rollbackps%\", [Convert]::FromBase64String([IO.File]::ReadAllText(\"%unren-rollbackps%.tmp\"))) }"

echo	+ You can now rollback using the scrollwheel
echo.
del "%unren-rollback%.tmp"

call :finish
exit /b

:finish
REM --------------------------------------------------------------------------------
REM We are done
REM --------------------------------------------------------------------------------
echo  ----------------------------------------------------
echo.
echo	Finished!
echo.
echo	Enter "1" to go back to the menu, or any other
set /p exitoption=.	key to exit:
echo.
echo  ----------------------------------------------------
echo.
if "%exitoption%"=="1" goto menu
exit 0

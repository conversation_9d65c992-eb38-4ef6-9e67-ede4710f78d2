## Notify screen ###############################################################
##
## The notify screen is used to show the player a message. (For example, when
## the game is quicksaved or a screenshot has been taken.)
##
## https://www.renpy.org/doc/html/screen_special.html#notify-screen

init offset = -1

screen notify(message):
    zorder 100
    style_prefix "notify"

    frame at notify_appear:
        text "[message]"

    timer 3.25 action Hide('notify')


transform notify_appear:
    on show:
        yoffset -50
        linear .25 yoffset 0
    on hide:
        yoffset 0
        linear .25 yoffset -50

style notify_frame is empty:
    ypos gui.notify_ypos
    xalign 0.5
    background "#000a"
    # background Frame("gui/notify.png", gui.notify_frame_borders, tile=gui.frame_tile)
    padding gui.notify_frame_borders.padding

style notify_text is gui_text:
    properties gui.text_properties("notify")
##################################################################################################################################################################################################################
########################################################### CHAPTER 11 WOUNDS - PROLOGUE #################################################################################################################################################################################
##################################################################################################################################################################################################################
label chapter_eleven:
    $ save_name = "Lena: Chapter 11"

    if lena_louise_sex and lena_reject_louise == False:
        call calendar(_month="July", _day="Wednesday", _week=3) from _call_calendar_104
        jump v11p_louise
    else:
        jump v11p_lenathursday

## LOUISE SCENE ########################################################################################################################################################################################################################
label gallery_CH11_S01:
    if _in_replay:
        call setup_CH11_S01 from _call_setup_CH11_S01

label v11p_louise:
    scene black with long
    pause 0.5
    play sound "sfx/mh1.mp3"
    l "Mhhh... Yes..."
    play music "music/sex_vixen.mp3" loop
    scene v11_louise1
    if lena_piercing1:
        show v11_louise1_p1
    elif lena_piercing2:
        show v11_louise1_p2
    if lena_tattoo1:
        show v11_louise1_t1
    if lena_tattoo3:
        show v11_louise1_t3
    with long
    play sound "sfx/ah6.mp3"
    pause 1
    lo "Mphfff... Nhhh!"
    l "Fuck yes, your tongue feels so good Louise... Don't stop!"
    if louise_dominant:
        "Louise followed my commands, her head bobbing up and down as I straddled her face, my hips rocking and grinding against her."
    else:
        "Louise's head bobbed up and down as I straddled her face, my hips rocking and grinding against her."
    "Late at night, in the intimacy of my room, we could unleash our lust freely and revel in the delights of one another's bodies."
    if louise_dominant:
        "I had been using Louise to pleasure myself quite often lately. She was always at hand and willing..."
        "Having her eat me out was the perfect way to unwind before going to bed."
    else:
        "We had been sharing moments like this quite often lately."
        "Having her eat me out was the perfect way to unwind before going to bed, and she was always willing..."
    "That night I was riding her face with reckless abandon, dragging my sex all over her soft lips and moist tongue."
    if v10_ivy_sex == 3:
        "After Ivy had done it to me, I had been wanting to try it. And Louise was just perfect for that."
    "I felt the heat of her breath and the warmth of her mouth on my pussy, my body shaking as she drove me closer and closer to the edge."
    l "Yes, yes...! Fuck, Louise...!"
    "I dug my nails into her scalp as I pushed myself against her, my pleasure intensifying with every movement."
    play sound "sfx/ah4.mp3"
    l "Ahhhh, yesss!!{w=0.5}{nw}" with flash
    with vpunch
    pause 0.5
    with vpunch
    pause 0.5
    with vpunch
    pause 0.5
    "I let out a loud cry as I felt myself crest over the edge, and I rode the orgasm out until I was completely spent."
    scene lenaroomnight with long
    "When I finally come down from my high, Louise and I lay beside each other, both still panting."
    $ flena = "flirtevil"
    $ flouise = "happy"
    show lenanude2 at rig
    show louisenude at lef
    with long
    lo "Did it feel good?"
    l "Hell yes... You make me so hot, Louise."
    if louise_dominant:
        lo "And you make me so... happy, mistress."
    else:
        lo "And you make me so... happy, Lena."
    "I looked into Louise's eyes, seeing her desire for me burning in her pupils."
    menu:
        "{image=icon_lust.webp}Play with Louise":
            $ renpy.block_rollback()

        "Send her away":
            $ renpy.block_rollback()
            stop music fadeout 3.0
            $ flena = "n"
            l "I'm spent, Louise. I need to sleep."
            $ flouise = "sad"
            lo "Oh. So you want to rest...?"
            l "Yeah. Thanks for tonight, it felt so good."
            if louise_dominant:
                $ flouise = "smile"
                lo "I'm glad... You know you can ask me any time, mistress. I love making you feel good..."
                l "And that's why you're my best friend."
                $ flouise = "happy"
                lo "Yeah."
            else:
                $ flouise = "n"
                lo "Sure... That's what best friends are for, right...?"
                l "And I'm so glad I have you as my bestie."
                $ flouise = "smile"
            l "Good night, Louise."
            lo "Good night..."
            play sound "sfx/door.mp3"
            hide louisenude with short
            show lenanude2 at truecenter with move
            "My relationship with Louise felt odd at first, but after all these weeks I had gotten used to it. It was pretty convenient..."
            if louise_dominant:
                "I still had to beware of her mood swings, but she knew her place for the most part. I had made sure of it."
            else:
                "I still had to beware of her mood swings, but as long as I gave her enough attention, she seemed more than happy to keep this thing going."
            "As far as I knew, Louise wasn't romantically interested in girls... But I had made it clear we were just friends, just in case her clinginess wanted to turn into something else."
            if ian_lena_couple and ian_lena_breakup == False:
                $ flena = "sad"
                "I was already dating Ian, after all... And I wasn't entirely sure if what I was doing could be considered cheating or not."
                if lena_cheating or v9_axel_sex:
                    "Not that it mattered too much, when I knew I had done other things that left no room for doubt."
                    "Mistakes that made me feel ashamed and that Ian couldn't know about... This thing with Louise, in comparison, seemed rather harmless."
                    "Just a game between two good girlfriends."
                else:
                    "It seemed obvious, but... A part of me still found ways to rationalize it as something else."
                    "This thing Louise and I had going on was just a harmless game between two girlfriends..."
            else:
                "And so far, it seemed to be working alright. Just two good friends that made each other feel good..."
            "At least, that's how I wanted to see it."
            hide lenanude2 with short
            pause 0.5
            scene black with long
            pause 0.5
            $ renpy.end_replay()
            jump v11p_lenathursday

    $ flena = "slut"
    l "I'd say it's my turn to make you feel good..."
    lo "Yes, please... I love everything you do to me, Lena."
    if louise_dominant:
        l "If you want it, ask properly."
        scene v11_louise2 with long
        pause 1
        lo "Yes, mistress..."
        l "Good girl... Show me how much you want me to make you feel good."
        lo "I've never wanted anything more in my life... I love how you make me feel, Lena..."
        l "You'll do anything I ask you?"
        lo "Yes..."
        l "And you'll let me do anything I want to you?"
        lo "Yes, mistress... Anything you want..."
    l "Is that so? Let's see then..."
    scene v11_louise3a
    if lena_piercing1:
        show v11_louise3_p1
    elif lena_piercing2:
        show v11_louise3_p2
    if lena_tattoo1:
        show v11_louise3_t1
    if lena_tattoo3:
        show v11_louise3_t3
    with long
    pause 1
    "I made Louise get on all fours on top of the bed and I held her but cheeks, exposing her privates to me."
    menu v11louiseplay:
        "Finger Louise" if v11_louise_finger == False:
            $ renpy.block_rollback()
            $ v11_louise_finger = True
            l "You want me to make you feel good, huh?"
            lo "Yes, please..."
            play sound "sfx/ah1.mp3"
            scene v11_louise3b
            if lena_piercing1:
                show v11_louise3_p1
            elif lena_piercing2:
                show v11_louise3_p2
            if lena_tattoo1:
                show v11_louise3_t1
            if lena_tattoo3:
                show v11_louise3_t3
            with long
            pause 1
            "I began teasing her pussy, running my fingers along her inner lips. I could feel her clit was already hard and ready for me."
            lo "Ahhh, yes...! I love how you touch me, Lena...!"
            lo "Nobody has touched me like this before... I feel like I'm melting..."
            "Louise moaned in delight as I increased the pressure and speed, rubbing my finger around her clit in circles."
            "I could feel the anticipation radiating off her body and I loved the power I had over her... I knew I could make her cum at will."
            jump v11louiseplay

        "Spank her" if v11_louise_spank < 3:
            $ renpy.block_rollback()
            # 1
            if v11_louise_spank == 0:
                $ v11_louise_spank = 1
                "I looked down at the beautiful sight in front of me: Louise on all fours, her ass up in the air, quiet and trembling with anticipation."
                scene v11_louise3c
                if lena_piercing1:
                    show v11_louise3_p1
                elif lena_piercing2:
                    show v11_louise3_p2
                if lena_tattoo1:
                    show v11_louise3_t1
                if lena_tattoo3:
                    show v11_louise3_t3
                with long
                "I couldn't refrain from my desire to slap her butt cheek."
                play sound "sfx/slap2.mp3"
                show v11_louise3cc with fps2
                scene v11_louise3a
                if lena_piercing1:
                    show v11_louise3_p1
                elif lena_piercing2:
                    show v11_louise3_p2
                if lena_tattoo1:
                    show v11_louise3_t1
                if lena_tattoo3:
                    show v11_louise3_t3
                with vpunch
                lo "Ahhh!!"
                "She let out a surprised squeal, making me grin."
                l "You've been a naughty girl, Louise..."
                if lena_lust < 7:
                    call xp_up ('lust') from _call_xp_up_747
                jump v11louiseplay
            # 2
            if v11_louise_spank == 1:
                $ v11_louise_spank = 2
                scene v11_louise3c
                if lena_piercing1:
                    show v11_louise3_p1
                elif lena_piercing2:
                    show v11_louise3_p2
                if lena_tattoo1:
                    show v11_louise3_t1
                if lena_tattoo3:
                    show v11_louise3_t3
                with long
                "She deserved another slap."
                play sound "sfx/slap2.mp3"
                show v11_louise3cc with fps2
                scene v11_louise3a
                if lena_piercing1:
                    show v11_louise3_p1
                elif lena_piercing2:
                    show v11_louise3_p2
                if lena_tattoo1:
                    show v11_louise3_t1
                if lena_tattoo3:
                    show v11_louise3_t3
                with vpunch
                play sound "sfx/pain2.ogg"
                lo "Ouch!"
                "This time I wasn't so gentle, and spanked her harder."
                "Louise's scream sounded painful, but also quite arousing to me..."
                "I could see the redness starting to form on her ass where I had slapped her, leaving my mark."
                jump v11louiseplay
            # 3
            if v11_louise_spank == 2:
                $ v11_louise_spank = 3
                scene v11_louise3c
                if lena_piercing1:
                    show v11_louise3_p1
                elif lena_piercing2:
                    show v11_louise3_p2
                if lena_tattoo1:
                    show v11_louise3_t1
                if lena_tattoo3:
                    show v11_louise3_t3
                with long
                "I was enjoying spanking Louise more than I anticipated. I decided to slap her even harder this third time."
                play sound "sfx/slap2.mp3"
                show v11_louise3cc with fps2
                scene v11_louise3a
                if lena_piercing1:
                    show v11_louise3_p1
                elif lena_piercing2:
                    show v11_louise3_p2
                if lena_tattoo1:
                    show v11_louise3_t1
                if lena_tattoo3:
                    show v11_louise3_t3
                with vpunch
                play sound "sfx/pain.ogg"
                lo "Ahhh!! That hurt...!"
                l "And you like it, don't you?"
                if louise_dominant:
                    lo "Yes, mistress... I like everything you do to me..."
                    l "Good girl."
                else:
                    lo "..."
                    lo "If it's you... I like it..."
                if lena_charisma < 10:
                    call xp_up ('charisma') from _call_xp_up_748
                jump v11louiseplay

        "{image=icon_lust.webp}Tease Louise's asshole" if lena_lust > 5 and v11_louise_ass == False:
            $ renpy.block_rollback()
            $ v11_louise_ass = True
            "I was getting aroused by how responsive Louise was to my touch, and I decided to take things a step further."
            scene v11_louise3d
            if lena_piercing1:
                show v11_louise3_p1
            elif lena_piercing2:
                show v11_louise3_p2
            if lena_tattoo1:
                show v11_louise3_t1
            if lena_tattoo3:
                show v11_louise3_t3
            play sound "sfx/mh1.mp3"
            with long
            lo "Ah!"
            "She let out a little gasp of surprise at first, and I felt her tense up as my tongue traced a wet path around her asshole."
            lo "Lena, that's... Nhhh..."
            l "No one's ever played with your ass before?"
            lo "Not like this... It feels... weird..."
            "My tongue moved further into her, probing and exploring. I felt her body start to relax as I continued to play with her anus."
            l "I'd say you're enjoying this."
            lo "It's... It's nice... I love everything you do to me..."
            scene v11_louise3a
            if lena_piercing1:
                show v11_louise3_p1
            elif lena_piercing2:
                show v11_louise3_p2
            if lena_tattoo1:
                show v11_louise3_t1
            if lena_tattoo3:
                show v11_louise3_t3
            with long
            l "I'm just getting you warmed up..."
            jump v11louiseplay

        "{image=icon_lust.webp}Use a dildo on Louise" if v11_louise_ass:
            $ renpy.block_rollback()
            "I smirked looking at Louise, completely surrendered before me. I could do anything I wanted to her."
            l "I think you're ready for something different..."
            $ renpy.block_rollback()
            call screen v11louisetoys
            "I reached over to the bedside table and grabbed a dildo. Her eyes widened as I held it in front of her."
            play sound "sfx/moan1.mp3"
            # double
            if v11_louise_dildo == 2:
                scene v11_louise3f
                if lena_piercing1:
                    show v11_louise3_p1
                elif lena_piercing2:
                    show v11_louise3_p2
                if lena_tattoo1:
                    show v11_louise3_t1
                if lena_tattoo3:
                    show v11_louise3_t3
                with long
            # normal
            else:
                scene v11_louise3e
                if lena_piercing1:
                    show v11_louise3_p1
                elif lena_piercing2:
                    show v11_louise3_p2
                if lena_tattoo1:
                    show v11_louise3_t1
                if lena_tattoo3:
                    show v11_louise3_t3
                with long
            "Louise let out a gasp as I guided the dildo inside her tight asshole, feeling her body tense up at the sensation."
            lo "Wait, ahhh... I'm not... used to this..."
            l "What, you never had anal?"
            lo "No... Not really... Nhhh!"
            "I moved the dildo in and out of her slowly at first, and then increased the speed. Louise began to moan and squirm beneath me, her breath coming in short, sharp gasps."
            l "Jeremy never fucked you in the ass?"
            lo "No... His was too big, there was no way it would fit..."
            l "And how about this one? I'd say it fits quite nicely."
            "With each thrust, Louise became more and more aroused, until she could no longer contain a loud moan of satisfaction."
            lo "Ahhnn! It feels... weird, but I like it..."
            if v11_louise_dildo == 2:
                menu:
                    "{image=icon_lust.webp}Fuck each other using the dildo":
                        $ renpy.block_rollback()
                        $ v11_louise_dildo = 3
                        lo "Lena... What are you doing?"
                        l "Don't worry. I saw this once in a movie..."
                        play sound "sfx/mh1.mp3"
                        scene v11_louise4 with long
                        l "Nhhh..."
                        "I plugged the other end of the dildo into my wet pussy, feeling its girth reach deep inside of me."
                        "The sex toy was firmly held by Louise's anus, tightly wrapped around it."
                        lo "Ahh... This is... weird..."
                        "I began to move back and forth, pushing against the toy and pleasuring myself. Louise squealed behind me, maybe from pleasure, or maybe from pain."
                        play sound "sfx/mh2.mp3"
                        scene v11_louise5_animation1 with long
                        pause 3
                        "I moved my hips in time with the movements of Louise's body, the toy sliding and bending against both of us with each thrust."
                        "I could feel the vibrations from her end, and her tight asshole gripping the dildo as I thrust in and out."
                        "I had never done something like that... It felt naughty and dirty, and it was turning me on like crazy."
                        l "Fuck... I'll cum again at this rate..."
                        lo "Y-{w=0.3}yes...! Cum...!"
                        scene v11_louise5_animation2 with fps
                        play sound "sfx/orgasm1.mp3"
                        pause 3
                        "Louise was enjoying it now, too. I could tell by the way she was pushing back against me."
                        "The room was filled with the heat of our passion and the smell of our arousal."
                        "We both moaned and screamed in pleasure, encouraging each other to go harder and faster, our pleasure building up..."
                        lo "Lena... Lena...!"
                        scene v11_louise5b with vpunch
                        l "Oh, fuck!! {w=0.3}{nw}"
                        scene v11_louise4 with flash
                        lo "Uggghhh!!!{w=0.5}{nw}" with vpunch
                        pause 0.5
                        with vpunch
                        pause 0.5
                        with vpunch
                        pause 0.5
                        l "Ahhhh..."
                        lo "I can't believe we did something like this..."
                        l "I know... It was crazy."
                        l "Crazy good."
                        jump v11p_louiseend

                    "Make Louise cum":
                        $ renpy.block_rollback()

            l "You're such a dirty girl... I guess you deserve a reward after all."
            scene v11_louise6
            if lena_tattoo2:
                show v11_louise6_t2
            with long
            play sound "sfx/oh1.mp3"
            pause 1
            "I turned Louise around and decided to please her properly, with my mouth on her dripping pussy."
            jump v11p_louisecum

        "Make Louise cum" if v11_louise_finger:
            $ renpy.block_rollback()
            "I turned Louise around and decided to please her properly, with my mouth on her dripping pussy."
            scene v11_louise6
            if lena_tattoo2:
                show v11_louise6_t2
            with long
            if lena_louise < 12:
                call friend_xp ('louise') from _call_friend_xp_940
            pause 1
            play sound "sfx/oh1.mp3"
            label v11p_louisecum:
                lo "Ohhh... Yes, Lena...!"
            "I used my tongue to lap up her pussy juices, swirling it around her clit and around her inner walls."
            "Louise was moaning with pleasure and I could feel her body trembling as I increased the intensity."
            "Soon she was screaming out, her legs trembling uncontrollably up in the air."
            lo "Lena, oh God! I love it...! I...!"
            play sound "sfx/orgasm1.mp3"
            lo "Ahhhh!!! I'm cumming!!!{w=0.5}{nw}" with flash
            with vpunch
            pause 0.5
            with vpunch
            pause 0.5
            with vpunch
            pause 0.5
label v11p_louiseend:
    stop music fadeout 3.0
    scene v11_louise7
    if lena_piercing1:
        show v11_louise7_p1
    elif lena_piercing2:
        show v11_louise7_p2
    if lena_tattoo1:
        show v11_louise7_t1
    with long
    "My relationship with Louise felt odd at first, but after all these weeks I had gotten used to it. It was pretty convenient..."
    if louise_dominant:
        "I still had to beware of her mood swings, but she knew her place for the most part. I had made sure of it."
    else:
        "I still had to beware of her mood swings, but as long as I gave her enough attention, she seemed more than happy to keep this thing going."
    "As far as I knew, Louise wasn't romantically interested in girls... But I had made it clear we were just friends, just in case her clinginess wanted to turn into something else."
    if ian_lena_couple and ian_lena_breakup == False:
        $ flena = "sad"
        "I was already dating Ian, after all... And I wasn't entirely sure if what I was doing could be considered cheating or not."
        if lena_cheating or v9_axel_sex:
            "Not that it mattered too much, when I knew I had done other things that left no room for doubt."
            "Mistakes that made me feel ashamed and that Ian couldn't know about... This thing with Louise, in comparison, seemed rather harmless."
            "Just a game between two good girlfriends."
        else:
            "It seemed obvious, but... A part of me still found ways to rationalize it as something else."
            "This thing Louise and I had going on was just a harmless game between two girlfriends..."
    else:
        "And so far, it seemed to be working alright. Just two good friends that made each other feel good..."
    "At least, that's how I wanted to see it."
    scene black with long
    pause 0.5
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH11_S01")
########################################################################################################################################################################################################################
## LENA THURSDAY ########################################################################################################################################################################################################################
########################################################################################################################################################################################################################
label v11p_lenathursday:
    call calendar(_month="July", _day="Thursday", _week=3) from _call_calendar_105
    scene lenaroom with long
    "The morning light coming through the window made me roll over in my bed, trying to put off waking up."
    $ flena = "sad"
    if v11_louise_finger or v11_louise_dildo > 0:
        $ flouise = "happy"
        show louisenude at lef with short
        lo "Good morning!"
        show lenanude at rig with short
        l "Um... Good morning, Louise..."
        lo "I slept marvelously tonight! What about you?"
        l "It was fine..."
        lo "I'm gonna take a shower. Do you mind if I go first?"
        $ flouise = "flirt"
        lo "Or we could shower together if you want..."
        l "You go on ahead. I need to clear the cobwebs with some coffee first."
        $ flouise = "n"
        lo "Oh, okay... I'll take the first turn, then."
        play sound "sfx/door.mp3"
        hide louisenude with short
        if cafe_help:
            l "{i}*Sigh*{/i}... I could've slept a bit more..."
            $ flena = "n"
            l "Anyway, I should get ready for work."
        else:
            l "{i}*Sigh*{/i}... I wanted to sleep some more... I don't need to go to work this morning for a change."
    else:
        $ lena_look = "sexy"
        play sound "sfx/meow.mp3"
        show lola at lef with short
        "However, I couldn't ignore Lola's insistence, urging me to get up and feed her."
        show lenabra at rig with short
        if cafe_help:
            l "Damn, Lola... I don't go to work until ten o'clock. I could have slept a little longer..."
        else:
            l "Can't you give me a break? I don't need to go to work this morning for a change."
        play sound "sfx/meow.mp3"
        l "Alright, alright. I'm coming."
    play sound "sfx/door.mp3"
    scene lenahome with long
    play music "music/normal_day2.mp3" loop
    $ flena = "n"
    $ lena_look = 1 
    if v11_louise_finger or v11_louise_dildo > 0:
        show lenabra at rig with short
        play sound "sfx/meow.mp3"
        show lola_b at lef with short
        l "Good morning, Lola..."
        l "Yes, yes, I'll feed you some breakfast. Wait..."
        hide lola_b with short
        show lenabra at truecenter with move
    else:
        show lenabra with short
    "I poured some food into Lola's bowl and began brewing some coffee."
    if cafe_help:
        "I had been feeling way more rested since I lost my night job at the restaurant, but the mornings at the café were getting busier."
        "That was good news, of course. The Van Dykes and I had been trying to keep the business afloat, and so far we were succeeding... But that meant our workload increased."
        if cafe_perry:
            "Thankfully, Perry had been helping us out recently, and for free. It was so nice of him."
            call friend_xp ('perry',1) from _call_friend_xp_941
        if lena_seymour_dating:
            if seymour_disposition > 2:
                l "I like it there, but I wonder if there's any point in continuing to work a the café. It's not like I need it anymore..."
                $ flena = "smile"
                "The salary was inconsequential compared to Seymour's patronage. I had made more money in one month working with him than I had made in the entire time I had been employed at the café."
                "And the next paycheck was coming soon..."
            elif seymour_disposition == 1:
                $ flena = "sad"
                l "At this point, the only reason I keep working at the café is to help the Van Dykes... I don't need their salary anymore, having Seymour's patronage."
                "I had made more money in one month working with him than I had made in the entire time I had been employed at the café. And the next paycheck was coming soon..."
                "However, I wasn't sure I should rely on Seymour. Or whether I should even trust him..."
            else:
                $ flena = "worried"
                l "Seymour's patronage is much more lucrative than my job at the café, but... Quitting would be a bad idea."
                $ flena = "serious"
                "I knew I couldn't rely on Seymour. I didn't trust him, not one bit..."
                $ flena = "sad"
                "However, I had made more money in one month working for him than I had made in the entire time I had been employed at the café. And the next paycheck was coming soon..."
        else:
            $ flena = "worried"
            l "I need to make sure the café survives. With Seymour using his influence to blacklist me, this is the only job I can rely on right now..."
            l "So far it's going okay, but I'm worried he'll try something to bring us down..."
            $ flena = "serious"
            l "He likes to pretend he's all high and mighty, but he's just vile and petty. I just hope he gets bored and forgets about me..."
    else:
        "First, I lost my job at the restaurant. And now, the café had gone under, and my employment with it too."
        if lena_seymour_dating:
            if seymour_disposition > 2:
                $ flena = "smile"
                l "It's a shame for the Van Dykes, but thankfully for me I don't need the job anymore. Nor any other for that matter..."
                "Having Seymour's patronage was such a relief. I had made more money in one month working with him than I had made in the entire time I had been employed at the café."
                "And the next paycheck was coming soon..."
            elif seymour_disposition == 1:
                l "Normally I would be worried, but thankfully for me I don't need the job anymore, having Seymour's patronage."
                "I had made more money in one month working with him than I had made in the entire time I had been employed at the café. And the next paycheck was coming soon..."
                $ flena = "sad"
                "However, I wasn't sure I should rely on Seymour. Or whether I should even trust him..."
            else:
                $ flena = "worried"
                "Now, I was completely reliant on Seymour's patronage. I didn't like it, but..."
                "I had made more money in one month working with him than I had made in the entire time I had been employed at the café. And the next paycheck was coming soon..."
                $ flena = "serious"
                l "I know I can't trust him, though..."
        else:
            $ flena = "worried"
            l "Another thing on the list of stuff in my life that went wrong. When will I catch a break?"
            "With Seymour using his influence to blacklist me from most jobs and modeling opportunities, finding employment was proving to be quite a challenge."
            "And I needed to find something soon, or I would go completely broke..."
            if lena_money < 3:
                "In fact, I already was."
    # STAN
    show lenabra at rig with move
    play sound "sfx/door.mp3"
    $ fstan = "n"
    $ stan_look = 1
    $ flouise = "n"
    $ louise_look = 1
    show stan at lef3 with short
    # friends
    if v10_stan_concert:
        if v10_stalkfap == "stan":
            $ fstan = "shy"
            $ flena = "shy"
        else:
            $ fstan = "smile"
            $ flena = "smile"
        st "Oh, hey... Good morning, Lena."
        l "Good morning, Stan. Want some coffee?"
        st "Yes, thanks... I was about to get some breakfast."
        show stan at lef with move
        # stalkfap/ ask 4 date
        if v10_stalkfap == "stan":
            "We hadn't really spoken about it, but my recent photo shoot with Stan came to mind every time I saw him."
            $ flena = "flirtshy"
            "And I knew he was thinking about it too... How could he not?"
            st "So, um... Do you have any plans for this weekend?"
            $ flena = "n"
            l "Yeah... I'm visiting my parents again. I've been busy with my own stuff these past couple of weeks and it's about time I drop by, see how they're doing and help them out..."
            $ fstan = "n"
            st "Oh, I see..."
            $ flena = "shy"
            l "Why? Did you have a proposal in mind or something?"
            $ fstan = "blush"
            st "Well, actually..."
            $ flena = "flirtshy"
            l "Maybe you want to help me create content for Stalkfap again?"
            st "That... That'd be nice, of course..."
            l "Have you been using the pictures you took?"
            st "Using them...?"
            $ flena = "slutshy"
            "I moved my hand up and down while looking at Stan teasingly."
            l "You know, to jack off."
            $ fstan = "drama"
            st "Um, I..."
            l "Come on, you can tell me. It wouldn't be the first time..."
            l "In fact, I watched you do it in front of me. I even had to take a shower to clean your jizz from my body..."
            $ fstan = "perv"
            st "Um, yeah, well... That was... That was quite nice, yeah..."
            $ fstan = "shy"
            st "And I'd love to do it again... Take pictures of you, I mean..."
            "Teasing Stan never failed to amuse me... He always looked torn between shame and desire, between fear and excitement."
            "I felt kinda bad for tormenting him, but... I also gave him something to enjoy, and I liked that."
            "The way he marveled at me was so flattering and kind of endearing."
            $ fstan = "blush"
            st "We can do that again whenever you ask me to, but, um..."
            st "Actually, I wanted to ask you about something else..."
            $ flena = "smile"
            l "Something else?"
            st "Yeah, um..."
            st "As I said, I love taking pictures of you, and more than that..."
            $ flena = "flirt"
            l "I know."
            st "That's not... I mean, it is, but what I'm trying to say is..."
            label v11standeclaration:
                $ flena = "worried"
                st "We've been sharing this apartment for quite some time, and you've always been nice to me..."
                if v3_defend_stan or v4_defend_stan:
                    st "You even defended me in front of injustice, vouching for me."
                st "I could've never even dreamed about having a girl like you care for me, respect me, or even talk to me..."
                if v2_stan_model or v4_stan_shoot:
                    st "And even less giving me the gift of the sight of her beautiful body, like you've done."
                menu:
                    "{image=icon_friend}You deserve it, Stan" if lena_stan > 9:
                        $ renpy.block_rollback()
                        $ flena = "smile"
                        l "You deserve people being nice to you Stan. And you've things worthy of notice."
                        l "I've noticed them for sure!"
                        if lena_charisma < 10:
                            call xp_up ('charisma') from _call_xp_up_749
                        $ fstan = "shy"
                        st "You're the only person who's ever told me something like that... And the most gorgeous girl I've ever seen..."

                    "What are you trying to say?":
                        $ renpy.block_rollback()
                        $ flena = "n"
                        l "I'm not sure I follow, Stan... What are you trying to say?"
                        st "What I'm trying to say is... You're incredible, Lena. You're the most gorgeous girl I've ever seen..."

                    "Thanks?":
                        $ renpy.block_rollback()
                        l "Um... Thanks?"
                        $ fstan = "shy"
                        st "Not to mention how incredible you are. You're the most gorgeous girl I've ever seen..."

                $ fstan = "shy"
                st "You sing and play music, you're smart, nice, and compassionate. You're just perfect..."
                $ flena = "blush"
                l "I wish that was the case, but I'm far from perfect..."
                st "Not to me. You made me regain faith in the human race."
                $ flena = "worried"
                st "I thought I had none left in me, but you made me find it."
                st "I feel I want to lay my sword at your feet, Lena..."
                st "I know I'm not worthy, but you've shared your feelings with me, and it's only honorable that I share mine with you..."
                st "I, um... I want to humbly declare my love for you, Lena."
                $ flena = "drama"
                st "Will you accept it?"
                "That left me disoriented and dumbfounded for a second."
                "When I woke up that morning, the last thing I was expecting was to get a love confession before I even got to finish my morning coffee."
                $ renpy.block_rollback()
                $ timeout_label = "v11standeclaration2"
                $ timeout = 4.0
                menu:
                    "Wait a minute...":
                        $ renpy.block_rollback()
                        $ flena = "sad"
                        l "Stan, wait a minute..."
                        $ fstan = "sad"
                        st "I know the probability of my love being unrequited is high, but I took the determination to be brave."
                        st "I just want you to know how I feel, even if..."
                        l "Stan, wait. Please."
                        if ian_lena_couple and ian_lena_breakup == False:
                            l "You know I'm dating someone already..."
                            st "That's true, but... I believe I bring something to the table he doesn't. Because I know it's impossible he cares about you as much as I do..."
                            l "Wait, wait... Hold your horses, Stan. You're going too fast..."
                        else:
                            l "You're going too fast..."
                        $ fstan = "drama"
                        st "Too fast...?"
                        $ timeout_label = None
                        menu v11standeclaration3:
                            "{image=icon_friend.webp}Let's talk about this..." if stan_change > 0:
                                $ renpy.block_rollback()
                                l "Let's talk about this, Stan..."
                                l "Can you see why this sudden confession could be a bit too much for me and make me feel somewhat... awkward?"
                                $ fstan = "sad"
                                if stan_change == 2:
                                    st "That's right... I'm sorry. I should've taken a more... considerate approach, perhaps?"
                                else:
                                    st "Uh... I guess so. Too much too soon...?"
                                l "Yeah. It's like this came out of the blue, and your declaration was so... grandiloquent."
                                st "Well, yes... Chivalry's not dead."
                                l "And I appreciate that, but... That's not how people deal with actual feelings. That's not how I do it, at least."
                                st "I didn't mean to offend you..."
                                $ flena = "n"
                                l "I know. Now, look."
                                l "I appreciate your sentiment, Stan, but..."
                                menu:
                                    "{image=icon_love.webp}I need time" if ian_lena_couple == False or ian_lena_breakup:
                                        $ renpy.block_rollback()
                                        $ lena_stan_dating = True
                                        l "I need time. Time to get to know you..."
                                        st "But you know me. We've been living together for half a year."
                                        l "To {i}really{/i} get to know you. To connect with you."
                                        $ flena = "sad"
                                        l "And the same goes for you. What do you really know about me?"
                                        $ fstan = "blush"
                                        st "What I know is enough..."
                                        l "You might think that way, but you have to trust me on this: people always fall short of our ideals. I know."
                                        st "I... I can see your point."
                                        st "But does that mean... You're interested in getting to know each other?"
                                        st "Like... going on a date or something?"
                                        $ flena = "shy"
                                        l "That'd be a start, yes."
                                        $ fstan = "shy"
                                        st "Oh. Well, um, in that case..."
                                        $ flena = "sad"
                                        l "But Stan, listen. I want to get to know you... as a friend, first."
                                        $ fstan = "worried"
                                        st "So I am stuck in the friendzone after all...?"
                                        l "All I'm saying people don't like when others push their expectations on them."
                                        $ fstan = "sad"
                                        $ flena = "n"
                                        l "So, let's not call it a date... Just two friends hanging out and getting to know each other better, all expectations aside."
                                        if lena_wits < 10:
                                            call xp_up ('wits') from _call_xp_up_750
                                        $ fstan = "n"
                                        st "Alright... I can do that."
                                        l "Okay... I'm rather short on time these days, as you know, but I promise to find the time to hang out with you next week."
                                        $ fstan = "smile"
                                        st "Awesome..."
                                        $ fstan = "shy"
                                        st "Thank you for this chance, Lena. I will do my best to live up to it."
                                        $ flena = "worried"
                                        l "Now you're making it awkward again..."
                                        $ fstan = "worried"
                                        st "Oh, sorry. I think I've talked more than enough. I'll go back to my room and let you enjoy the rest of your breakfast."
                                        l "Don't apologize... Just relax, okay? That's when I like you the most."
                                        if lena_stan < 12:
                                            call friend_xp ('stan') from _call_friend_xp_942
                                        $ fstan = "smile"
                                        st "I'll try..."
                                        play sound "sfx/door.mp3"
                                        hide stan with short
                                        l "That's not what I expected to happen today..."
                                        "I hoped I had handled that alright. I cared about Stan, and I wanted to help him."
                                        $ flena = "sad"
                                        l "I'm not sure we're on the same page, though... He's a bit... extravagant."
                                        show louise2 at lef3 with short
                                        lo "Hey..."

                                    "I don't feel the same way":
                                        $ renpy.block_rollback()
                                        $ lena_reject_stan = 1
                                        $ flena = "sad"
                                        l "I don't feel the way you do. I'm sorry, Stan."
                                        if ian_lena_couple and ian_lena_breakup == False:
                                            l "Besides, as you know, I'm already dating Ian."
                                            st "So you won't consider the possibility of breaking up with him and..."
                                            l "No."
                                        st "I knew my hopes were doomed..."
                                        st "But I also believe that if I work hard and show you my level of commitment, there's a chance you'll change your mind..."
                                        l "Stan, listen to me. I know you think I do, but I can't be the person you need me to be."
                                        # if stan_change == 0: (BM: impossible because the requirement is stan_change > 0)
                                        #     l "And relationships are not easy. There's a lot to learn before you can even attempt to..."
                                        #     jump v11standeclaration5
                                        $ fstan = "worried"
                                        jump v11standeclaration4

                            "Sorry, I don't feel the same way":
                                $ renpy.block_rollback()
                                $ lena_reject_stan = 1
                                l "I'm sorry, Stan. But I don't feel the way you do..."
                                if ian_lena_couple and ian_lena_breakup == False:
                                    l "And, as I just told you, I'm already dating Ian."
                                    st "So you won't consider the possibility of breaking up with him and..."
                                    l "No."
                                $ fstan = "sad"
                                st "I knew my hopes were doomed..."
                                st "But I also believe that if I work hard and show you my level of commitment, there's a chance you'll change your mind..."
                                $ flena = "serious"
                                l "No, Stan, listen to me. This is not how things work, okay? Not at all."
                                $ fstan = "worried"
                                st "What do you mean...? Which things...?"
                                $ flena = "sad"
                                l "Humans. People. Relationships."
                                $ fstan = "sad"
                                st "Um... I'm willing to learn, if you'd teach me..."
                                $ flena = "worried"
                                l "I'm sorry, Stan, but I can't be the one to teach you. You'll have to learn from someone else."
                                if stan_change == 0:
                                    jump v11standeclaration5
                                $ fstan = "drama"
                                label v11standeclaration4:
                                    call friend_xp ('stan',-1) from _call_friend_xp_943
                                    if lena_stan > 6:
                                        $ lena_stan = 6
                                st "..."
                                l "..."
                                $ fstan = "sad"
                                st "I... don't know what to say now..."
                                $ flena = "sad"
                                l "Me neither. Unless you need me to clarify anything of what I've just said..."
                                st "No, I... I understood everything."
                                l "Good."
                                st "So I guess, um... I'll go back to my room and let you enjoy the rest of your breakfast."
                                play sound "sfx/door.mp3"
                                hide stan with short
                                l "How do I keep getting myself into these situations...?"
                                l "At least that's dealt with... I hope."
                                show louise2 at lef3 with short
                                lo "Hey..."

                            "You're creeping me out!":
                                $ renpy.block_rollback()
                                $ flena = "worried"
                                l "Yes, you're creeping me out right now."
                                label v11stancreeping:
                                    $ lena_reject_stan = 2
                                    $ stan_change = 0
                                $ fstan = "drama"
                                st "C-{w=0.3}creeping you out? How...?"
                                l "You really don't know? Look..."
                                l "Just... Just forget it, alright? You're confused."
                                $ fstan = "worried"
                                st "I'm not. I've thought much about this..."
                                l "Yes, you are. You just don't realize it. It's clear you have much to learn, but I can't be the one to teach you..."
                                st "Learn? About what?"
                                $ flena = "sad"
                                l "About... People. Feelings and communication... Relationships."
                                st "..."
                                label v11standeclaration5:
                                    $ fstan = "mad"
                                st "And how am I to learn if nobody gives me a chance?!"
                                l "I'm sure you'll find someone who will, but she has to be the right person for you..."
                                st "You all say the same! At least have the courage to be honest like I am!"
                                $ flena = "worried"
                                $ fstan = "mad"
                                st "What you think is that guys like me deserve to die virgins!"
                                l "Wha--{w=0.5} That's not what I said...!"
                                st "Your excuses are more of an insult than the truth. I won't have them!"
                                if lena_stan > 1:
                                    call friend_xp ('stan',-1) from _call_friend_xp_944
                                    $ lena_stan = 1
                                play sound "sfx/door_slam.mp3"
                                hide stan with vpunch
                                l "What the...?"
                                $ flena = "serious"
                                l "You've got to be kidding me...!"
                                show louise2 at lef3 with short
                                lo "Hey..."

                    "Excuse me?":
                        $ renpy.block_rollback()
                        label v11standeclaration2:
                            $ flena = "worried"
                        l "E--{w=0.5}excuse me?"
                        $ fstan = "worried"
                        st "What I meant to say is I'm in love with you..."
                        l "I-{w=0.3}I got that part. But where is this even coming from...?"
                        if ian_lena_couple and ian_lena_breakup == False:
                            l "I mean... You know I'm dating someone already."
                            st "That's true, but... I believe I bring something to the table he doesn't. Because I know it's impossible he cares about you as much as I do..."
                            $ flena = "drama"
                            l "Wait, wait. You're going too fast, Stan."
                        else:
                            st "Isn't it obvious? You've always been special to me, Lena. I..."
                            l "Wait, wait. You're going too fast, Stan."
                        call friend_xp ('stan',-2) from _call_friend_xp_945
                        $ fstan = "drama"
                        st "Too fast...?"
                        $ timeout_label = None
                        jump v11standeclaration3

                    "Are you crazy?":
                        $ renpy.block_rollback()
                        $ flena = "surprise"
                        l "What did you just say? Are you crazy?"
                        $ fstan = "drama"
                        $ flena = "worried"
                        if ian_lena_couple and ian_lena_breakup == False:
                            l "Where is this even coming from? Besides, I have a boyfriend already. You know that!"
                            st "That's true, but... I believe I bring something to the table he doesn't. Because I know it's impossible he cares about you as much as I do..."
                        else:
                            l "Where is this even coming from?"
                            st "Isn't it obvious? You've always been special to me, Lena. I..."
                        l "Stop that! You're creeping me out right now."
                        call friend_xp ('stan',-1) from _call_friend_xp_946
                        $ timeout_label = None
                        jump v11stancreeping

        # no stalkfap
        else:
            st "So, um... Do you have any plans for this weekend?"
            $ flena = "n"
            l "Yeah... I'm visiting my parents again. I've been busy with my own stuff these past couple of weeks and it's about time I drop by, see how they're doing and help them out..."
            $ fstan = "n"
            st "Oh, I see..."
            # photo shoot
            if v10_stan_shoot:
                if stan_simp == 2 or stan_simp == 4:
                    $ flena = "shy"
                    l "I know I promised I'd pose for you again, but I haven't found the time for it yet..."
                    $ fstan = "shy"
                    st "Oh, don't worry about it... I'm at your disposal whenever it's okay for you, just let me know..."
                    $ flena = "flirtshy"
                    l "Don't worry, I will... I'll let you take some nice, intimate photos. I'm sure you'll enjoy those."
                    $ fstan = "blush"
                    st "Oh, um... Yeah..."
                    "Teasing Stan never failed to amuse me... He always looked torn between shame and desire, between fear and excitement."
                    "I felt kinda bad for tormenting him, but... I also gave him something to enjoy, and I liked that."
                    if v10_stalkfap == "ian" or v10_stalkfap == "mike":
                        "I could've asked Stan to be my cameraman for that last Stalkfap shoot, but I shied away from the idea at the last minute."
                        "It would've been so exciting and naughty... And the way he marveled at me was so flattering and kind of endearing."
                    else:
                        "The way he marveled at me was so flattering and kind of endearing."
                else:
                    l "I know I promised I'd pose for you again, but I haven't found the time for it yet..."
                    $ fstan = "shy"
                    st "Oh, don't worry about it... I'm at your disposal whenever it's okay for you, just let me know..."
                st "So, um..."
                st "As I said, I'd love to take pictures of you again, and more than that..."
                st "I mean... Don't get it wrong, what I'm trying to say is..."
                jump v11standeclaration
            # just friends
            else:
                l "What about you? Any plans?"
                st "Um... Not really..."
                if stan_simp == 1 or stan_simp == 3:
                    "I had been keeping Stan at a distance, but our relationship remained friendly and polite."
                    "At first, I thought the way Stan looked at me was flattering and kind of endearing, but after I caught him jacking off to me..."
                    "Well, things turned weirdly uncomfortable."
                else:
                    l "Not even some online activities?"
                    st "Well, yeah... We've been farming for this big raid and we're gonna attempt it this Friday night..."
                    l "A raid? Which game are you playing, World of Spellcraft?"
                    st "Federation of Phenomenons. But that's not the point..."
                $ fstan = "blush"
                st "I, uh... I wanted to..."
                l "Huh? What is it?"
                st "It's..."
                # no declaration
                if stan_simp == 1 or stan_simp == 3 or lena_stan < 7:
                    st "..."
                    st "Nevermind."
                    $ flena = "worried"
                    l "Uh... Okay."
                    $ fstan = "sad"
                    st "Oh, by the way..."
                    # findme stan talk about rent issues (should appear later if not triggered here?)
                    if v10_stan_pay == 2:
                        $ fstan = "smile"
                        st "I wanted to thank you again for, you know... contributing to the rent."
                        $ flena = "smile"
                        l "You don't have to. It's my responsibility, too."
                        $ fstan = "sad"
                        st "I wish Louise would see it that way..."
                        $ fstan = "n"
                        st "Anyway, so this month... Can I count on you again?"
                        if lena_seymour_dating or lena_money > 5:
                            l "Yeah, don't worry about it."
                        elif lena_money > 2:
                            l "I'm rather short on money, but yeah... That's my plan."
                        else:
                            $ flena = "sad"
                            l "I'm rather short on money, but... I'll see what I can do..."
                    if v10_stan_pay == 1:
                        $ fstan = "sad"
                        st "About the rent... Will you be able to afford it this month, or...?"
                        $ flena = "sad"
                        l "I'll see what I can do."
                        st "I see... Okay, sorry I brought that up..."
                    if v10_stan_pay == 0:
                        $ fstan = "sad"
                        st "About this month's rent... Will you pay this time, or...?"
                        $ flena = "serious"
                        l "We already discussed that, Stan. You know where I stand..."
                        st "Um... Okay, I understand..."
                        st "I'll see what I can do about it..."
                    play sound "sfx/door.mp3"
                    $ flouise = "serious"
                    show louise2 at left with short
                    $ fstan = "sad"
                    $ flena = "sad"
                    st "I'll finish my breakfast in my room..."
                    hide stan with short
                    show louise2 at lef3 with move
                    lo "Hey..."
                # stan declares
                else:
                    $ fstan = "blush"
                    st "I just, um... I wanted to tell you something."
                    jump v11standeclaration

    # not friends
    elif lena_stan > 0:
        st "Um... Good morning."
        $ flena = "n"
        l "Hey..."
        show stan at lef with move
        "We both stood in silence while Stan grabbed some breakfast in the kitchen."
        $ flena = "sad"
        if stan_simp == 1 or stan_simp == 3:
            "We had been living together for about half a year now, and at first it seemed like Stan was someone I could click together with..."
            "That all changed when I found out about his creepy behavior. I should've never instigated it. Now I felt quite awkward around him."
        elif v2_stan_museum:
            "We had been living together for about half a year now, and I had tried to befriend Stan at first..."
            "But we never really clicked together. He was a really awkward guy..."
        else:
            "We had been living together for about half a year now, but we never clicked together. Stan was a really awkward guy..."
        l "..."
        st "Um... can I have some coffee?"
        $ flena = "n"
        l "Sure..."
        if v10_stan_pay == 2:
            $ fstan = "smile"
            st "By the way... I wanted to thank you again for, you know... contributing to the rent."
            l "You don't have to. It's only fair..."
            $ fstan = "sad"
            st "I wish Louise would see it that way..."
            $ fstan = "n"
            st "Anyway, so this month... Can I count on you again?"
            if lena_seymour_dating or lena_money > 5:
                l "Yeah, don't worry about it."
            elif lena_money > 2:
                l "I'm rather short on money, but yeah... That's my plan."
            else:
                $ flena = "sad"
                l "I'm rather short on money, but... I'll see what I can do..."
            st "Um, alright... So, um... I'll let you finish your breakfast in peace."
        if v10_stan_pay == 1:
            $ fstan = "sad"
            st "By the way... About the rent... Will you be able to afford it this month, or...?"
            $ flena = "sad"
            l "I'll see what I can do."
            st "I see... So, um... I'll let you finish your breakfast in peace."
        if v10_stan_pay == 0:
            $ fstan = "sad"
            st "So, about this month's rent... Will you pay this time, or...?"
            $ flena = "serious"
            l "We already discussed that, Stan. Nothing's changed."
            $ fstan = "worried"
            st "But... You can't expect me to shoulder the extra expense all by myself..."
            l "I don't want to talk about that again, especially not while I'm having breakfast first thing in the morning."
            st "..."
            l "You'll have to find a way to solve this. It was your responsibility to begin with."
            $ fstan = "serious"
            st "So you keep saying..."
            l "What?"
            st "Nothing. I'll be in my room."
        play sound "sfx/door.mp3"
        hide stan with short
        if v10_stan_pay == 0:
            l "Nothing new there..."
        $ flena = "n"
        show louise2 at lef3 with short
        lo "Hey..."
    # enemies
    else:
        $ flena = "serious"
        $ fstan = "worried"
        st "Um... Good morning."
        l "What do you want, Stan?"
        st "I just... I wanted to get some breakfast."
        menu:
            "{image=icon_mad.webp}You don't need that" if v10_stan_pay < 2:
                $ renpy.block_rollback()
                $ flena = "evil"
                l "I don't think you need that. You're fat enough as it is, don't you think?"
                $ fstan = "blush"
                st "..."
                $ flena = "serious"
                l "Can you at least wait until I'm done before stuffing your mouth, right? I can't bring myself to eat if you're around, so scram."
                $ fstan = "serious"
                st "Sure, whatever."

            "Wait for your turn":
                $ renpy.block_rollback()
                $ fstan = "sad"
                l "Well, wait for your turn. I'm having breakfast now, so give me some space, will you?"
                st "Um, I... Sure..."
                l "Come back when I'm done. Your presence makes me so uncomfortable."

            "Be quick about it":
                $ renpy.block_rollback()
                l "Alright, be quick about it. I'm trying to focus over here."
                $ fstan = "n"
                st "Sure..."
                show stan at lef with move
                "I waited for Stan to grab whatever he needed. His presence made me so uncomfortable..."
                l "Are you done?"
                st "Yeah."
                $ fstan = "sad"
                l "Okay, now let me have my breakfast in peace."
                show stan at lef3 with move

        st "..."
        l "What?"
        if v10_stan_pay == 2:
            st "I just wanted to thank you for, you know... contributing to the rent."
            l "I'm not doing it for you. Someone has to pay, or they'll kick us out, all thanks to you."
            st "..."
        if v10_stan_pay == 1:
            st "About the rent... Will you be able to afford it this month, or...?"
            $ flena = "mad"
            l "My finances are none of your business."
            $ stan = "sad"
            st "Well, actually... Um..."
            $ stan = "serious"
            st "Never mind."
        if v10_stan_pay == 0:
            st "So, about this month's rent... Will you pay this time, or...?"
            $ flena = "mad"
            l "Do you have to bring that up first thing in the morning? What a way to ruin my mood for the entire day!"
            $ stan = "sad"
            st "But it's important..."
            l "We already discussed this issue, and nothing's changed. It's your responsibility, so you deal with it."
            $ stan = "serious"
            st "I don't even know why I asked."
        play sound "sfx/door.mp3"
        hide stan with short
        $ flena = "serious"
        l "Finally... I can't stand that guy."
        show louise2 at lef3 with short
        lo "Hey..."

    # LOUISE
    $ flouise = "serious"
    lo "Was that creep nagging you about the rent again?"
    # stan reaction
    if lena_stan_dating or lena_reject_stan > 0:
        if lena_stan_dating:
            $ flena = "blush"
            l "What? No... It was... something else."
            lo "What did he do this time?"
            $ flena = "sad"
            "I knew Louise wouldn't understand. No point in trying to explain it to her."
            l "It's nothing... Forget about it."
        elif lena_reject_stan == 1:
            $ flena = "blush"
            l "What? No... It was... something else."
            lo "What did he do this time?"
            $ flena = "sad"
            l "It's nothing... Forget about it."
        elif lena_reject_stan == 2:
            l "No, that was something different..."
            lo "What did he do this time?"
            $ flena = "sad"
            "I didn't feel like talking about it. I just wanted to have my coffee in peace."
            l "It's nothing... Forget about it."
        if v10_stan_pay == 2:
            lo "I still don't know why you agreed to pay him the extra money..."
            $ flena = "serious"
            l "Well, somebody had to."
            $ flouise = "sad"
            lo "..."
        if v10_stan_pay == 1:
            lo "You're not gonna pay him the extra money this month either, right?"
            l "I'm not sure I can shoulder that expense at the moment, considering my situation..."
        if v10_stan_pay == 0:
            l "Anyway, I'm not paying this month, either. My mind hasn't changed on that."
            lo "Good! We have to stay our ground against him."
    elif lena_stan > 0:
        if v10_stan_pay == 2:
            l "He wasn't nagging me. He thanked me for contributing."
            lo "That's called emotional manipulation, he wants to make sure he keeps getting what he wants from you."
            $ flena = "serious"
            l "Well, somebody has to pay the rent."
            $ flouise = "sad"
            lo "..."
        if v10_stan_pay == 1:
            l "He just wanted to know if I have enough money to pay this month..."
            lo "Ugh, he's insufferable. He should understand your situation and give you some space."
        if v10_stan_pay == 0:
            l "Yeah... I told him I'm not paying this month, either."
            lo "Good! We have to stay our ground against him."
    else:
        l "Yeah. It's so tiresome having to share the apartment with him."
        lo "Right? I wish we could kick him out."
        if v10_stan_pay == 2:
            lo "I still don't know why you agreed to pay him the extra money."
            l "Well, somebody had to."
            $ flouise = "sad"
            lo "..."
        if v10_stan_pay == 1:
            lo "You're not gonna pay him the extra money this month either, right?"
            l "No freaking way. It's not like I can shoulder that expense at the moment anyway."
        if v10_stan_pay == 0:
            l "Yeah... I told him I'm not paying this month, either."
            lo "Good! We have to stay our ground against him."
    # relationship
    hide louise2
    show louise at lef3
    with short
    show louise at lef with move
    if lena_louise_sex and lena_reject_louise == False:
        if v11_louise_dildo == 3:
            $ flouise = "sad"
            "Louise poured herself a cup of coffee and sat next to me."
            lo "Ouch..."
            $ flena = "n"
            l "Does something hurt?"
            lo "Yes, my ass. Last night was..."
            $ flena = "shy"
            l "So hot?"
            $ flouise = "happy"
            lo "Yes... I can't believe you make me do such things..."
            $ flena = "slutshy"
            l "I'd say you enjoy them."
            lo "It's different, that's for sure..."
        else:
            $ flena = "n"
            "Louise poured herself a cup of coffee and sat next to me."
            $ flouise = "happy"
            if v11_louise_dildo > 0:
                lo "Last night was incredible... But my ass hurts a little!"
                $ flena = "slutshy"
                l "What can I say? It was too tempting not to try..."
                lo "I can't believe you make me do such things..."
                l "I'd say you enjoy them."
                lo "It's different, that's for sure..."
            else:
                lo "Last night was incredible... I never enjoyed myself so much with a guy..."
                $ flena = "slutshy"
                l "What can I say? I guess I know what I'm doing..."
        lo "I love being able to try new things with you..."
    elif lena_reject_louise:
        $ flouise = "sad"
        $ flena = "sad"
        "Louise poured herself a cup of coffee, but stood at a distance, quietly."
        if lena_reject_louise == 2:
            "We had barely spoken since I turned her down during our confrontation the night of my concert at the Fortress."
            "It was an uncomfortable situation and I felt kinda bad for being so harsh with her, but at least she hadn't caused any more drama..."
            "I couldn't indulge her cumbersome needs anymore."
        else:
            "Our friendship had cooled down a lot since I rejected her... But I couldn't indulge her cumbersome needs anymore."
            "At least she hadn't caused any more drama since our last conversation the night of my concert at the Fortress..."
        "I tried making some conversation to ease the tension."
    else:
        "Louise poured herself a cup of coffee and sat next to me."
        if v10_jeremy_3some:
            $ flouise = "blush"
            lo "..."
            "Louise had been acting awkwardly around me since that night at Ivy's place, and that hadn't changed after our threesome with Jeremy..."
            if v10_jeremy_3some == 2:
                $ flena = "shy"
                "And I could see where she was coming from. I had stolen the show, after all..."
            else:
                "I thought maybe that would bring us closer and make the situation less thorny, but that hadn't been the case..."
            "I tried making some conversation to ease the tension."
    # summer plans
    $ flena = "n"
    l "So... What are your plans for summer? August is around the corner."
    $ flouise = "n"
    lo "I'll probably go back to my dad's, spend the summer there..."
    $ flena = "sad"
    l "Oh, really? How come?"
    $ flouise = "sad"
    lo "To save some money, mostly. I haven't been able to find a job yet, and companies rarely hire someone during August."
    lo "I thought I could spend the month with my dad, find a summer job there, since the city is on the coast, and not spend any money on food and utilities..."
    $ flouise = "serious"
    lo "Besides, I don't like the situation at the apartment right now."
    $ flena = "sad"
    # apartment situation
    if v10_stan_pay == 2:
        l "I don't like it either, but not paying isn't an option."
        hide louise
        show louise2 at lef
        with short
        lo "But it's unfair!"
        $ flena = "serious"
        l "Unfair or not, it doesn't change the situation."
        $ flouise = "worried"
        l "And the situation is that if we don't pay, the landlord is gonna kick us out."
        l "I'm paying my part, and someone has to pay yours..."
        $ flouise = "serious"
        lo "Let Stan do it."
        menu:
            "{image=icon_charisma.webp}You need to pay, Louise" if lena_charisma > 7:
                $ renpy.block_rollback()
                $ v11_louise_pay = True
                $ flena = "serious"
                l "Listen to me, Louise. You have to pay."
                $ flouise = "worried"
                $ flena = "sad"
                l "Doing nothing won't make the situation go away. Stan and I are doing our part, but we need you to join."
                l "Or are you expecting us to pay your share of the rent?"
                if lena_wits < 7:
                    call xp_up ('wits') from _call_xp_up_751
                hide louise2
                show louise at lef
                with short
                lo "I'm expecting Stan to do it..."
                l "Well, he won't, not for long. And when that happens, either he'll leave or the landlord will force us out. In any case, we'll have to find somewhere else to live..."
                lo "And what can I do? It's not like I have the money, either..."
                $ flouise = "sad"
                lo "My dad said he'd pay for all my expenses until I was done with my studies, and I just finished my degree."
                lo "Maybe I can convince him to keep sending me money for a bit longer, but I have to be a good daughter and go spend the summer with him..."
                lo "And hopefully, I'll be able to get a job starting September. Then we'll see."
                l "And until then?"
                lo "Until then... Well... I'll try to figure things out."
                if lena_reject_louise:
                    if lena_reject_louise == 2:
                        lo "Not that you'd miss me if I was gone... You made that very clear last time."
                    else:
                        $ flouise = "sad"
                        lo "Not that you'd miss me if I was gone, right...?"
                    $ flena = "worried"
                    lo "Anyway, I'm done with my breakfast... Have a nice day."
                    hide louise2 with short
                    l "..."
                    l "Jeez... It doesn't look like she'll get over what happened between us anytime soon..."

            "Stop being childish":
                $ renpy.block_rollback()
                $ flena = "mad"
                l "Stop being childish, Louise! Act like an adult, will you?"
                $ flouise = "blush"
                lo "And what do you want me to do? Bend over and just take it?"
                $ flena = "serious"
                l "I want you to do the responsible thing, or they'll kick us out of this apartment."
                if lena_charisma < 6:
                    call xp_up ('charisma') from _call_xp_up_752
                lo "..."
                $ flouise = "serious"
                lo "Well, maybe I don't want to live in this apartment anymore."
                if lena_reject_louise:
                    if lena_reject_louise == 2:
                        lo "Not that you're gonna miss me when I'm gone... You made that very clear last time."
                    else:
                        lo "Not that you're gonna miss me when I'm gone, right...?"
                $ flena = "worried"
                if lena_louise > 0:
                    call friend_xp ('louise',-1) from _call_friend_xp_947
                lo "I'm done with my breakfast. Have a nice day."
                hide louise2 with short
                l "..."
                if lena_reject_louise:
                    l "Jeez... It doesn't look like she'll get over what happened between us anytime soon..."
                else:
                    l "Jeez... This is not good..."

            "Ask your dad for money":
                $ renpy.block_rollback()
                $ flena = "sad"
                l "You can always ask your dad for money. That's an option I don't have."
                if lena_wits < 6:
                    call xp_up ('wits') from _call_xp_up_753
                $ flouise = "sad"
                lo "I could, but... Who do you think has been paying my part of the rent all this time?"
                if lena_louise > 7:
                    $ v11_louise_pay = True
                    lo "My dad said he'd pay for my expenses until I was done with my studies, and I just finished my degree."
                    $ flouise = "n"
                    lo "Maybe I can convince him to keep sending me money for a bit longer, but I have to be a good daughter and go spend the summer with him..."
                    lo "And hopefully, I'll be able to get a job starting September. Then we'll see."
                    l "And until then?"
                    $ flouise = "serious"
                    lo "Until then, Stan better take care of the issue, as is his responsibility!"
                else:
                    l "Of course... It's not like you have a job..."
                    $ flouise = "serious"
                    lo "No, I don't! And having a degree doesn't seem to be helping me get one!"
                    lo "My dad said he'd pay for my expenses until I finished with my studies, so I don't see him sending even more money now that I'm done with my master's!"
                    l "So what's your plan?"
                    lo "My plan is for Stan to take care of the issue, as is his responsibility!"
                if lena_reject_louise:
                    if lena_reject_louise == 2:
                        lo "But it's not like you'd care if I left the apartment, right? You made that very clear last time..."
                    else:
                        $ flouise = "sad"
                        lo "But it's not like you'd care if I left the apartment, right...?"
                    $ flena = "worried"
                    lo "Anyway, I'm done with my breakfast... Have a nice day."
                    hide louise2 with short
                    l "..."
                    l "Jeez... It doesn't look like she'll get over what happened between us anytime soon..."
                else:
                    l "We'll see how that plays out..."

            "Let's hope he does":
                $ renpy.block_rollback()
                $ flena = "sad"
                l "Well, let's hope he does... But I doubt he'll put up with this for long."
                lo "You said it, we'll see."
                if lena_reject_louise:
                    if lena_reject_louise == 2:
                        lo "Not that you'd miss me if I was gone... You made that very clear last time."
                    else:
                        $ flouise = "sad"
                        lo "Not that you'd miss me if I was gone, right...?"
                    $ flena = "worried"
                    lo "Anyway, I'm done with my breakfast... Have a nice day."
                    hide louise2 with short
                    l "..."
                    l "Jeez... It doesn't look like she'll get over what happened between us anytime soon..."

    if v10_stan_pay == 1:
        l "So you're not gonna pay what Stan is asking."
        lo "No way! And you shouldn't, either!"
        l "It's not like I could, even if I wanted to. I'm rather short on money..."
        lo "Let Stan pay! He has money, I know!"
        lo "He's a programmer or something like that, right? Those guys make bank."
        jump v11louisestanpay
    if v10_stan_pay == 0:
        if lena_stan > 0:
            $ flena = "sad"
            l "I don't like it either... But I don't see how we can re-negotiate, and Stan won't keep paying for us forever."
        else:
            $ flena = "serious"
            l "Yeah, it's infuriating. But we need to keep the pressure on Stan so he either re-negotiates or pays up."
        lo "He has money, I know! He's a programmer or something like that, right? Those guys make bank."
        label v11louisestanpay:
            $ flena = "sad"
        l "I don't know... But we should find a solution, because if we do nothing, the landlord will end up kicking us out."
        $ flouise = "sad"
        lo "Maybe we should start looking for another apartment... Something smaller, cheaper..."
        lo "Or we could kick Stan out and find another roommate? Someone who's not a creep and who can pay up?"
        l "I don't know..."
        lo "In any case, I need to get a job first... My dad said he'd pay for my expenses until I was done with my studies, and I just finished my degree."
        $ flouise = "n"
        lo "Maybe I can convince him to keep sending me money for a bit more, but I have to be a good daughter and go spend the summer with him..."
        l "I see..."
        if lena_reject_louise:
            if lena_reject_louise == 2:
                $ flouise = "serious"
                lo "Not that you're gonna miss me when I'm gone... You made that very clear last time."
            else:
                $ flouise = "sad"
                lo "Not that you're gonna miss me when I'm gone, right...?"
            $ flena = "worried"
            lo "I'm done with my breakfast... Have a nice day."
            hide louise with short
            l "Jeez... It doesn't look like she'll get over what happened between us anytime soon..."
        else:
            l "Well, my turn to take a shower and get ready for the day."
    # ian sms
    if ian_lena_dating:
        hide louise
        hide louise2
        with short
        "I got to my feet, ready to get the day started."
        show lenabra at truecenter with move
        play sound "sfx/sms.mp3"
        $ flena = "n"
        l "A message from Ian..."
        nvl clear
        i_p "{i}Hey, do I get to see you tonight?{/i}"
        if (ian_lena_couple and lena_cheating) or (ian_lena_couple and v9_axel_sex):
            $ flena = "sad"
            "I felt a sting of guilt. Of course, Ian had no idea of my recent misbehavior, or my internal doubts and contradictions..."
            l "I have no idea how I'm gonna resolve that. As long as he doesn't find out, nothing will happen, but..."
            l "I'm worried things could come to the light eventually."
            if v10_wc_bj == "mike":
                "I had fallen into temptation, giving Mike a blowjob in the club's restrooms..."
            elif v10_wc_bj == "mark":
                "I had fallen into temptation, giving a blowjob in the club's restrooms to a guy I met that same night..."
            if v10_stalkfap == "mike":
                "I could've asked Ian, but I wanted Mike to be my partner in crime during that steamy photo shoot."
                if v10_mike_sex:
                    "And I also booty-called Mike the night Ian and I fought. I knew it was wrong, but I did it anyways."
            elif v10_mike_sex:
                "The night Ian and I fought, I ended up booty-calling Mike. I knew it was wrong, but I did it anyways."
            if v9_axel_sex:
                "My biggest mistake was getting in bed with Axel, but I just couldn't help it..."
            l "I need to keep my shit together, otherwise, this could end badly... And I don't want that."
            $ flena = "n"
            "I texted Ian back."
        else:
            $ flena = "smile"
        if cafe_help:
            l_p "{i}Yes! I'm free after my shift {image=emoji_smile.webp}{/i}"
        else:
            l_p "{i}Yes! I'm free all afternoon {image=emoji_smile.webp}{/i}"
        i_p "{i}Cool, I leave the gym at eight. Meet me for dinner?{/i}"
        l_p "{i}Sure, as long as you spend the night with me...{/i}"
        i_p "{i}Should we skip dinner then? {image=emoji_flirt.webp}{/i}"
        menu:
            "{image=icon_love.webp}I love having dinner with you" if ian_lena_couple or lena_ian_love:
                $ renpy.block_rollback()
                $ v11iandinner = True
                $ flena = "shy"
                l_p "{i}No, I love spending time with you, talking, having dinner, and doing other things too... {image=emoji_shy.webp}{/i}"
                if ian_lena_couple:
                    i_p "{i}I love spending time with you, no matter what we do.{/i}"
                    l_p "{i}Let's do all of those things, then. Starting with dinner, and ending with you in my bed, making love to me {image=emoji_kiss.webp}{/i}"
                    i_p "{i}Sounds like a plan{image=emoji_heart.webp}{/i}"
                    $ flena = "happy"
                    l_p "{i}See you tonight, handsome {image=emoji_kiss.webp}{/i}"
                else:
                    i_p "{i}Let's do all of them, then {image=emoji_tongue.webp}{/i}"
                    $ flena = "smile"
                    l_p "{i}Let's! See you tonight, handsome {image=emoji_kiss.webp}{/i}"

            "No, it's okay":
                $ renpy.block_rollback()
                $ v11iandinner = True
                $ flena = "flirtshy"
                l_p "{i}No, I'd like to have dinner with you! But I also want something else... {image=emoji_crazy.webp}{/i}"
                i_p "{i}Luckily for you, I can do both {image=emoji_glasses.webp}{/i}"
                l_p "{i}Perfect! See you tonight, handsome {image=emoji_kiss.webp}{/i}"

            "Yes" if ian_lena_couple == False:
                $ renpy.block_rollback()
                $ flena = "flirt"
                l_p "{i}Yes, let's skip that and get to the good part straight away {image=emoji_devil.webp}{/i}"
                if lena_lust < 8:
                    call xp_up ('lust') from _call_xp_up_754
                i_p "{i}Alright then, I'll grab a bite before coming over.{/i}"
                l_p "{i}Perfect! See you tonight, handsome {image=emoji_kiss.webp}{/i}"
    stop music fadeout 3.0
    scene lenahome with long
    play sound "sfx/shower.mp3"
    scene v1_lena_shower
    if lena_piercing1:
        show v1_lena_shower_p1
    if lena_piercing2:
        show v1_lena_shower_p2
    if lena_tattoo2:
        show v1_lena_shower_t2
    if lena_tattoo3:
        show v1_lena_shower_t3
    with long
    pause 2
    scene street with long
    pause 1

## CAFE WORK ########################################################################################################################################################################################################################
    $ fholly = "smile"
    # holly's look
    if holly_change > 3:
        $ v11_holly_change = True
    $ holly_look = "summer"
    if v11_holly_change:
        $ holly_glasses = 2
    else:
        $ holly_glasses = True
    if cafe_help:
        $ lena_look = 1
        $ fed = "sad"
        $ flena = "n"
        play music "music/normal_day.mp3" loop
        scene cafe with long
        "Morning at the café was lively, and during lunchtime even more clients showed up."
        show lenawork at rig
        show ed at lef
        with short
        "I passed by Ed as he served food to some customers. He looked sweaty and a bit stressed."
        ed "Lena, can you take the order from table four, please? And check if we're out of cheesecake. Has Molly finished baking those...?"
        l "I'm on it. Let me clean up table three first, we have some customers waiting..."
        if cafe_perry:
            $ perry_look = "cafe"
            $ fperry = "happy"
            show lenawork at truecenter
            show ed at lef3
            with move
            show perry at rig3 with short
            p "Don't worry, I got the t--{w=0.5}table. Go t--{w=0.5}take their order."
            $ flena = "smile"
            $ fed = "smile"
            l "Thanks, Perry."
            call friend_xp ('perry') from _call_friend_xp_948
            ed "Perfect. Now I need to run back to the kitchen..."
        else:
            ed "Alright. Sorry, I need to run back to the kitchen."
        scene cafe with long
        "Busy as we were, I had to wait until the afternoon to get a breather, right after people finished their lunch break."
        if lena_athletics < 10:
            call xp_up ('athletics') from _call_xp_up_755
        pause 1
        $ fmolly = "n"
        $ flena = "sad"
        $ fed = "sad"
        show lenawork
        show ed at lef3
        show molly at rig3
        with long
        l "Today's being intense! I don't remember ever working at this pace in here..."
        ed "You tell me! I'm beat!"
        mo "But that's good news. It means our efforts to keep the café in business are working. Even better than expected!"
        $ fed = "smile"
        $ flena = "n"
        if cafe_perry:
            ed "And it's all thanks to Lena. And Perry, of course."
            ed "But without your brilliant ideas we wouldn't have attracted so many people."
        else:
            ed "And it's all thanks to Lena. Without your brilliant ideas we wouldn't have attracted so many people."
        $ flena = "shy"
        $ fmolly = "smile"
        if cafe_music:
            mo "That's right! Your concert was marvelous, and a lot more people started coming since then."
            ed "Yes, and last week's life drawing event has brought more customers this week, too!"
        elif cafe_nude:
            ed "We noticed an increase in business right after that first life drawing event. And the one we hosted last week has brought even more customers!"
        mo "Ed's drawing came out so good that he framed it and hung it on the wall!"
        # findme ed drawing
        ed "What can I say? Having such an inspiring model has not only revived the café, but my artistic inclinations too..."
        $ fed = "sad"
        ed "The only bad thing about things going good is that my back is starting to kill me... Ouch."
        $ flena = "happy"
        mo "Come on, I'll give you a massage later!"
        $ fmolly = "sad"
        $ flena = "n"
        mo "But seriously, I have to thank you both for working this hard. Especially you, Lena..."
        mo "You have gone to a lot of trouble to help us, and you had no obligation to."
        $ flena = "happy"
        l "Well, you never treated me like a simple employee. And I wanted to help..."
        $ fmolly = "n"
        l "After all, that's what you guys hired me to do, right?"
        mo "You've done much more than that. I thank you from the bottom of my heart."
        $ flena = "shy"
        ed "Yes, thank you, Lena."
        l "Stop it! I've lost count of how many times you've thanked me lately..."
        $ fed = "smile"
        ed "It's well deserved!"
        show lenawork at rig
        show molly at right
        show ed at left
        with move
        show holly at lef with short
        h "Hello..."
        ed "Oh, Holly! What are you having today? It's on the house!"
        hide holly
        show holly3 at lef
        with short
        h "Oh, really? Thank you! A matcha latte, then."
        ed "Matcha latte coming right away! And a big slice of cheesecake to go with it!"
        if holly_gym:
            $ fholly = "sad"
            h "No, not the cake... Just the tea, please."
            $ fed = "n"
            $ fmolly = "n"
            ed "Oh, that's right. You're still on that diet, huh?"
            mo "And it shows, don't you think? Look at her, she looks all slender and spiffy!"
            $ fholly = "shy"
            h "Thanks..."
        else:
            $ fholly = "happyshy"
            h "Okay!"
        mo "We got this, Lena. Take a break for a bit."
        stop music fadeout 3.0
        hide molly
        hide ed
        with short
        pause 1
        $ fholly = "happy"
        play music "music/date.mp3" loop
        h "Everyone seems to be in such high spirits today."
        l "Yes, it's been a busy day, and things are looking up..."
        $ flena = "n"
        l "I just hope we can keep the pace and make things stable for once."
        $ fholly = "n"
        h "I'm sure you'll pull through... I'll come here every day if I need to!"
        if cafe_perry:
            l "Don't worry, we're not short on customers right now! That's why we'll probably need some more hands soon..."
        else:
            l "Don't worry, we're not short on customers right now! What we need now are some more hands..."
        l "But enough about that. What about you?"
    # no work
    else:
        $ lena_look = 4
        $ flena = "n"
        $ fholly = "sad"
        if lena_seymour_dating:
            if seymour_disposition < 1:
                "Even though I was under no pressure for it, I felt the obligation to start looking for a new job."
                "I spent the morning looking at offers, but the few I could aspire to paid miserably. Nothing compared to the money I was getting from Seymour..."
                "I didn't find none worthy of my time, so I decided to take a stroll instead and I even took the luxury to eat out at a new restaurant that had opened and I had been wanting to check out."
            else:
                "I was officially jobless, but I was in no hurry at all to find new employment."
                "I had a wonderful morning relaxing, playing guitar, and going for a stroll."
                "I even took the luxury to eat out at a new restaurant that had opened and I had been wanting to check out."
        else:
            "I spent the morning searching for jobs, but the few I could aspire to paid miserably."
            "However, I needed anything I could get, so I applied anyways..."
            "Hopefully, I would get some job interviews, but so far none of my applications received a response."
            "I wondered if Seymour's black hand had something to do with it, but I had no way to be sure..."
        scene cafeteria with long
        "Passed lunchtime, I walked to the cafeteria where I had agreed to meet with Holly."
        show lena at rig3 with short
        "She was already waiting inside when I arrived."
        play music "music/date.mp3" loop
        show holly2 at lef with short
        l "Hey, there you are."
        show lena at rig with move
        h "Hi..."
        l "How are you...?"
        $ flena = "sad"
        l "Oh."
        l "You look kinda sad."
        hide holly2
        show holly at lef
        with short
        h "Yes... I still haven't come to terms with the café finally closing for good last week."
        h "I really liked that place... And I liked Ed and Molly even more. They made that place special. This..."
        "Holly looked around."
        h "I don't like here nearly as much. It's big and crowded and the place feels kind of... soulless, don't you think?"
        menu:
            "It's a shame what happened":
                $ renpy.block_rollback()
                l "It's a real shame what happened... I should've tried to help them somehow, but..."
                l "It felt like I had enough going on in my own life to get involved. Now I feel bad about it."
                $ fholly = "n"
                h "It's not your fault... Other people tried to help too, but in the end, there was nothing we could do."
                l "I'll miss that place..."
                $ fholly = "sad"
                h "Me too."
                if lena_holly < 12:
                    call friend_xp ('holly') from _call_friend_xp_949

            "This place is not so bad":
                $ renpy.block_rollback()
                l "Sadly, there's nothing we can do about that now. We need to move forward and adapt."
                $ flena = "n"
                l "And this place it's not so bad. It has its charm..."
                "I took around too and then saw the prices of the menu printed on the counter."
                $ flena = "sad"
                l "Though it seems coffee just got a tad more expensive."
                h "I'll really miss that place..."

            "It's for the best":
                $ renpy.block_rollback()
                $ flena = "n"
                l "Sadly, there's nothing we can do about that now. We need to move forward and adapt."
                l "What happened was probably for the best. Ed and Molly were getting pretty old, and their business wasn't competitive..."
                $ fholly = "worried"
                h "Do you really see things that way?"
                if lena_holly > 0:
                    call friend_xp ('holly',-1) from _call_friend_xp_950
                $ flena = "sad"
                l "I'm trying to take an optimistic outlook..."
                h "I see... I guess I'm just grieving. I'll really miss that place..."

        $ flena = "n"
        l "Come on, let's talk about something a bit more uplifting."
        $ fholly = "n"

## HOLLY DATE ########################################################################################################################################################################################################################
    # tracking vars for conversation
    $ v11askhollybook = False
    $ v11askhollylove = False
    $ v11askhollyfamily = False
    $ v11askhollyfamily1 = False
    $ v11askhollyfamily2 = False
    menu v11hollyprologue:
        "{image=icon_friend.webp}Love life" if v11askhollylove == False:
            $ renpy.block_rollback()
            $ v11askhollylove = True
            $ flena = "n"
            l "By the way, I wanted to ask you..."
            $ fholly = "n"
            # ian
            if ian_holly_dating:
                $ flena = "smile"
                l "How are things going with Ian?"
                $ fholly = "blush"
                hide holly
                show holly3 at lef
                with short
                h "Oh, they're going good... At least that's what I think..."
                $ flena = "n"
                l "Hm... You don't sound too sure about it..."
                if lena_go_holly > 3:
                    "Holly directed a quick glance at me, but quickly averted her eyes, blushing."
                    $ flena = "sad"
                    h "I am. I just... Sometimes I feel kind of confused, and..."
                    $ fholly = "worried"
                    h "Ah, what am I saying? I'm so stupid..."
                    l "Confused, how?"
                    h "Forget about it... I shouldn't have said anything."
                    $ fholly = "sad"
                    h "I was lucky enough for Ian to notice me, and much more than that... My expectations have been blown away. I should focus on that."
                    "Were Holly's doubts caused by... me? Had she noticed that same intimate tension I felt when I was with her?"
                    if lena_go_holly == 5:
                        "I could barely contain it that last time, having Holly naked in my bed..."
                    "Suddenly I felt rather guilty about it."
                else:
                    $ fholly = "n"
                    h "I didn't mean to sound that way... I really think things are going great, much better than I dared to imagine at first..."
                    $ fholly = "smile"
                h "Ian says he really likes me, and I believe he's being sincere..."
                l "But?"
                $ fholly = "blush"
                h "But... I don't know. I'm aware we're still getting to really know each other, and I have no intention of pressuring him."
                if lena_go_holly > 3:
                    h "It's a bit stupid to think like this, but... We're going on dates, but not {i}dating{/i} just yet."
                    h "And it's been great, but sometimes I wonder..."
                else:
                    l "I see. So you two are going on dates, but not {i}dating{/i} just yet."
                    $ fholly = "sad"
                    h "Yes, something like that... And it's been great, but sometimes I wonder..."
                l "\"Does he feel the same way I do?\""
                h "Yes... Something like that."
                l "Why don't you ask him?"
                $ fholly = "blush"
                h "I wouldn't want to impose on him. He asked to take things slow, so..."
                $ flena = "n"
                l "I understand where are you coming from, but I'd consider bringing up the subject and being honest with your feelings. You and Ian have been dating for some time now..."
                h "I don't know. I just don't want to jinx it."
                l "I wouldn't worry too much about that... But in any case, I already gave you my piece of advice."
                $ fholly = "smile"
                hide holly3
                show holly2 at lef
                with short
                h "Thank you. I'll think about it..."
            # mark
            elif holly_guy > 0:
                if holly_guy > 1:
                    $ flena = "flirtshy"
                    l "I've heard you and Mark have been getting along since that night at the club..."
                    $ fholly = "shy"
                    hide holly
                    show holly3 at lef
                    with short
                    h "Oh, that... Yes, well..."
                    l "Did you go all the way with him yet?"
                    $ fholly = "blush"
                    h "No, not yet... But he wanted to go on a date this weekend..."
                    if holly_guy == 3:
                        l "Really? So nothing happened the night he brought you home?"
                        h "Well, um... Yeah..."
                        h "I gave him a... blowjob in his car."
                        l "So you can be naughty too!"
                        $ fholly = "shy"
                        h "I don't normally drink that much, and... Well, it felt right, so I thought... Why not just go with the flow?"
                        $ flena = "smile"
                        l "That's how it's done. Just relax and I'm sure everything will go just fine."
                    else:
                        $ flena = "smile"
                        l "I see... Well, be sure to enjoy yourself!"
                        $ fholly = "shy"
                        h "I'm a bit nervous..."
                        l "Just relax and I'm sure everything will go just fine."
                    $ fholly = "n"
                    h "That's what Ivy tells me."
                elif holly_guy == 1:
                    $ flena = "smile"
                    l "How are things with Mark? You seemed to be interested in him last night at the club..."
                    $ fholly = "blush"
                    h "Oh, that..."
                    $ fholly = "n"
                    h "We've been texting a bit, but I haven't met him again."
                    l "How come? You don't like him anymore?"
                    h "He's handsome and nice, but... I'm not sure he's what I'm looking for."
                    l "And what are you looking for?"
                    $ fholly = "blush"
                    hide holly
                    show holly3 at lef
                    with short
                    if lena_go_holly > 3:
                        "Holly directed a quick glance at me, but quickly averted her eyes, blushing."
                        $ flena = "shy"
                    h "I'm not sure yet... I guess that's the problem."
                    l "You'll eventually find out, I'm sure."
                    if lena_go_holly > 3:
                        $ fholly = "shy"
                        h "I hope so..."
            # none
            else:
                l "How's your love life going? Any news?"
                hide holly
                show holly3 at lef
                with short
                if lena_holly_sex and v8_holly_sex != "ivy" and v8_holly_sex != "lenaivy":
                    $ fholly = "worried"
                    h "M-{w=0.3}my love life? Why are you asking...?"
                    $ flena = "shy"
                    l "I'm just curious... You told me about that new guy from the office and how he seemed interested in you..."
                    $ fholly = "blush"
                    h "I'm not interested in him at all. You're the only one with who I've... You know..."
                    h "Even though I know we're just friends, so that doesn't count as love life, does it...?"
                    $ flena = "blush"
                    $ fholly = "worried"
                    h "Ah, sorry. I'm talking nonsense now..."
                    menu:
                        "{image=icon_love.webp}It's not nonsense":
                            $ renpy.block_rollback()
                            $ lena_holly_love = True
                            $ flena = "n"
                            l "It's not nonsense..."
                            $ fholly = "blush"
                            l "We're friends, yes, but... What we've shared goes beyond friendship."
                            h "You think so too?"
                            l "Yes, of course... Though I have to admit it's a first for me."
                            if lena_louise_sex or v10_ivy_sex:
                                l "I mean, I have experience with girls. I've experimented with friends before, but with you... It's different."
                                l "It certainly feels that way..."
                            elif lena_fty_lesbo:
                                l "I mean, I know I like girls, even though I have little experience with them..."
                                l "And with you... It certainly feels different than what I'm used to."
                            else:
                                l "I mean... I've never really experienced something like this. Not with a girl..."
                                l "I mean, aside from the physical aspect, the way I feel about you... It's different."
                            h "Different... How?"
                            $ flena = "blush"
                            l "It's... hard to explain. I'm not sure myself."
                            $ flena = "smile"
                            l "All I know is I'm really happy to have met you, and I love this connection we share."
                            $ fholly = "happyshy"
                            h "I love our connection too..."
                            if lena_holly < 12:
                                call friend_xp ('holly') from _call_friend_xp_962
                            "I wasn't entirely sure what that connection really meant, not yet..."
                            "But I was more than willing to find out."
                            if lena_wits < 10:
                                call xp_up ('wits') from _call_xp_up_771

                        "Change the subject":
                            $ renpy.block_rollback()
                            $ flena = "worried"
                            "I wasn't sure how to respond to Holly's words, or what to call this relationship we shared."
                            "Where we just friends? Something more maybe, or something entirely different...?"
                            "Holly was special to me, that much I knew. But what did that mean? What did she really feel for me...?"
                            "I didn't feel ready to find the answers to those questions yet, so I decided to change the subject."
                            $ flena = "n"
                            l "So, um... Nothing to report in the love department, right?"
                            $ fholly = "sad"
                            h "No, I guess not..."
                            call friend_xp ('holly',-1) from _call_friend_xp_963

                else:
                    h "M-{w=0.3}my love life? Nothing really new..."
                    l "What about that new guy from the office? Clark?"
                    $ fholly = "blush"
                    h "He's nice, but..."
                    l "You don't like him?"
                    h "It's not that I don't like him... But..."
                    if lena_holly_sex or lena_go_holly > 3:
                        "Holly directed a quick glance at me, but quickly averted her eyes, blushing."
                        $ flena = "shy"
                        h "I... I like someone else."
                        l "Oh. I see..."
                    else:
                        $ fholly = "sad"
                        h "I still... I like someone else."
                        $ flena = "sad"
                        l "Oh... You mean...?"
                    h "Never mind. Forget I said anything..."
                    $ fholly = "n"
                    h "I'm doing fine, focusing on other things right now..."
                    $ flena = "n"
                    l "I see."
            # stan
            if holly_stan and stan_change > 0:
                l "By the way, have you considered getting in touch with Stan as I suggested?"
                if stan_change == 2:
                    $ fholly = "smile"
                    h "Yes, we've talked a bit through social media. We have to meet one of these days."
                    l "Great! I knew you'd get along."
                    h "He seems really nice..."
                else:
                    h "We've talked a bit through social media, but that's it... It would feel a bit weird meeting just the two of us, without you..."
                    l "Give it a try, I have the feeling you two might get along."
            hide holly2
            hide holly3
            show holly at lef
            with short
            # lena ian breakup
            if ian_lena_breakup:
                $ fholly = "sad"
                h "What about you...? How have things been between you and Ian after... breaking up?"
                if v10_ian_left: # (if v10_lena_mad == "sex" or v10_lena_mad == "stalkfap")
                    $ flena = "sad"
                    l "It's been well... We've been keeping our distance."
                    l "I haven't seen him since Ivy's birthday. And we haven't really talked, either..."
                    h "I see... I'm sorry..."
                    l "I'm sorry too, but we didn't see eye to eye on some important things. It wasn't gonna work."
                    l "Though I wish it had ended differently..."
                    h "You'll have to talk at some point. Do you think you can still be friends?"
                    l "Not friends, but friendly. I'd settle with that."
                else:
                    if v10_lena_dance == "ian":
                        $ flena = "blush"
                        l "Well, um..."
                        if v10_wc_bj == "ian":
                            "I got so horny during Ivy's birthday party I ended up blowing him on a bathroom stall..."
                            "We hadn't talked about it since, like it never happened. But both knew it did..."
                        else:
                            "I remembered the night at the club, dancing with him, grinding..."
                            "Nothing happened, but it was clear there was still a strong tension between us... And we both knew."
                        $ flena = "n"
                        l "It's been... Okay. Nothing out of the ordinary."
                    else:
                        $ flena = "n"
                        l "It's been okay... It's still a bit weird, but we've kept hanging out together and with friends."
                        l "I think we can settle as friends after all. At least, that's what I'd like."
                    $ fholly = "smile"
                    h "I'm glad to hear..."
            # lena ian
            elif ian_lena_dating:
                if ian_lena_couple:
                    $ fholly = "smile"
                    h "What about you? How are you and Ian doing?"
                    if lena_cheating or v9_axel_sex:
                        $ flena = "worried"
                        l "It's... going good, yeah."
                        "On the surface, at least. I hadn't been a good girlfriend..."
                        "But I shouldn't share that with Holly, or with anyone else. Ivy already knew too much..."
                        $ flena = "sad"
                        l "Yeah, it's definitely going great...!"
                    elif ian_lena_sex == False:
                        $ flena = "smile"
                        l "It's going good! I wasn't sure if trying to have a serious relationship was worth the shot, but Ian's different..."
                        $ flena = "shy"
                        "We hadn't gone all the way yet, and even though I could tell he was dying to, he always respected my choice."
                        "I had never experienced that kind of relationship before. It was so different..."
                        $ flena = "evil"
                        "And I liked it."
                    else:
                        l "I'd say it's going pretty good so far... I really enjoy his company."
                        l "I wasn't sure if trying to have a serious relationship was worth the shot, but I feel that if it's with him, it could work..."
                    $ fholly = "sad"
                    h "I'm glad... You two make a great couple."
                    $ flena = "sad"
                else:
                    $ fholly = "blush"
                    h "What about you? How are you and Ian doing...?"
                    $ flena = "n"
                    l "It's going fine... No strings attached and that sort of thing, you know."
                    $ fholly = "sad"
                    h "Yes, of course... I'm happy that you can have that kind of relationship."
                    $ flena = "sad"
                    if lena_ian_love:
                        "I kind of was, too... But at the same time, I couldn't help but feel... unfulfilled."
                "Talking about this subject with Holly was always a bit awkward, considering she used to have a big crush on him..."
                "I decided to change the subject."
            # single
            else:
                if lena_go_holly > 3 or lena_holly_sex:
                    $ fholly = "blush"
                    h "What about you...? Is there someone, um... special in your life?"
                    l "Me? No, I'm not dating anyone. Far from it..."
                    $ fholly = "worried"
                    h "Oh... I... I see."
                else:
                    $ fholly = "smile"
                    h "What about you? Do you have someone... special?"
                    l "Me? No, I'm not dating anyone. Far from it..."
                    h "Oh, I see."
            jump v11hollyprologue

        "Family" if v11askhollyfamily1 == False:
            $ renpy.block_rollback()
            $ v11askhollyfamily = True
            $ flena = "n"
            l "How's everything going at home? Your parents must be proud of what you're accomplishing..."
            $ fholly = "sad"
            h "I don't think they are... I think they see this as just \"a phase\", something I should grow out of at some point."
            $ flena = "sad"
            h "Something I should've probably grown out of already..."
            l "That sucks... But can't they see you have a great talent that's being recognized by people? What more do they want from you?"
            $ fholly = "n"
            h "To them, fantasy books are just that, childish daydreaming written onto paper. They're only really interested in one story, the one in the holy scriptures."
            $ flena = "worried"
            l "I see..."
            menu v11hollyprologuefamily:
                "Holly's parents" if v11askhollyfamily1 == False:
                    $ renpy.block_rollback()
                    $ v11askhollyfamily1 = True
                    l "Your parents must be even more religious and conservative than what you told me at first."
                    h "They're... very set in their own ways. And they play quite a prominent role in the congregation."
                    l "They've always been part of it?"
                    h "My mother was born in it. She met my dad when they were young and brought him into the fold."
                    $ flena = "n"
                    l "So your grandparents on your father's side are not part of the religion?"
                    $ fholly = "sad"
                    h "No, but my father cut ties with them before I was born. I never got to meet them..."
                    $ fholly = "smile"
                    h "But I did have a very good relationship with my other grandmother. She always understood me."
                    $ fholly = "n"
                    h "She was very different from my mother, even though she was raised by her."
                    if v11askhollyfamily2:
                        if lena_holly < 12:
                            call friend_xp ('holly') from _call_friend_xp_951
                        jump v11hollyprologue
                    jump v11hollyprologuefamily

                "Holly's sister" if v11askhollyfamily2 == False:
                    $ renpy.block_rollback()
                    $ v11askhollyfamily2 = True
                    $ flena = "n"
                    if v6_hollyconvo1 or v6_hollyconvo2:
                        l "What about your older sister? You told me she married a few years ago and lives with her husband..."
                    else:
                        l "I believe you mentioned once you had an older, sister, right?"
                        h "Yes, we're five years apart. She married when she was my age and has been living with her husband ever since."
                    l "Are they also part of the religious congregation?"
                    h "Oh, yeah... They met there, and follow that lifestyle to a T. My parents are very proud of her."
                    l "What does she do for a living?"
                    h "She's a wife... And she preaches and works in the community."
                    $ flena = "worried"
                    l "I see... So that's your parent's ideal."
                    $ fholly = "sad"
                    h "Yes... That's what they'd like me to pursue, but..."
                    $ fholly = "n"
                    h "I never really felt the calling, as they say. I never felt like I belonged to that kind of lifestyle."
                    if v11askhollyfamily1:
                        if lena_holly < 12:
                            call friend_xp ('holly') from _call_friend_xp_952
                        jump v11hollyprologue
                    jump v11hollyprologuefamily

                "Ask something else":
                    $ renpy.block_rollback()
                    jump v11hollyprologue

        "Holly's book" if v11askhollybook == False:
            $ renpy.block_rollback()
            $ v11askhollybook = True
            $ flena = "smile"
            l "When's your next book coming out? You were about to finish it..."
            $ fholly = "happy"
            h "Yes, the writing is all done... Now we're finishing the last revision to make sure everything's good to go into print!"
            $ fholly = "smile"
            h "The book is planned to be published right after summer."
            l "I can't wait to read it!"
            $ fholly = "shy"
            h "I can send you the manuscript, if you want..."
            $ flena = "happy"
            l "I'd be honored."
            $ fholly = "n"
            h "I know you have a lot going on, though... Have you had time to write new songs recently?"
            if lena_seymour_dating:
                $ flena = "n"
                l "Actually, yes..."
            elif cafe_help:
                $ flena = "n"
                l "Yes, a bit..."
            else:
                $ flena = "sad"
                l "I've been mostly focusing on finding a new job, but yeah..."
            $ flena = "n"
            l "I've been working on a couple songs, but they're not finished yet. I need to devote more time to it."
            $ fholly = "happy"
            h "I'd love to listen to them once they're done!"
            $ flena = "smile"
            l "Alright, you'll be my test audience."
            h "Yay!"
            jump v11hollyprologue

        "Time to go" if v11askhollylove:
            $ renpy.block_rollback()
            $ flena = "n"
            if cafe_help:
                l "Well, I should get back to work."
                $ fholly = "smile"
                h "Of course."
            else:
                l "We should get going, don't you think?"
                h "Yes."
            if v11askhollybook and v11askhollyfamily1 and v11askhollyfamily2 and lena_wits < 10:
                $ fholly = "shy"
                h "And thanks for showing interest in me..."
                l "That's what friends do!"
                call xp_up ('wits') from _call_xp_up_756

    # lena + holly
    if lena_holly_sex or lena_go_holly > 3:
        $ fholly = "shy"
        hide holly
        hide holly2
        hide holly3
        show holly3 at lef
        with short
        if holly_gym:
            h "I... I hope we can hang out again soon. I had a lot of fun last night at the club..."
        else:
            h "I... I hope we can hang out again soon. I had so much fun last time at your concert..."
        $ flena = "shy"
        l "Sure! You know I love hanging out with you."
        $ fholly = "blush"
        if lena_holly_sex:
            if v8_holly_sex == "ivy" or v8_holly_sex == "lenaivy":
                h "Me too. I love everything we do together... And with Ivy too. You've taught me so much..."
                $ flena = "flirtshy"
                l "Well, yeah... I enjoy doing those things with you too..."
                h "I hope you'll keep teaching me again... soon."
                $ flena = "flirt"
                l "I'm looking forward to that too."
            else:
                h "Me too. I love everything we do together... Just the two of us..."
                l "I love it too. And I'm really looking forward to having some intimacy with you again..."
                $ fholly = "shy"
                h "Really...?"
                $ flena = "happy"
                l "Of course."
            $ fholly = "shy"
            h "And... I also enjoyed that photo shoot we did... But I was too awkward, I need to do better."
            h "Could we... try it again sometime? Only if you want."
            l "You can count on it."
            h "Great..."
            if lena_louise_sex:
                $ fholly = "blush"
                h "By the way, um... Can I ask you something?"
                $ flena = "n"
                l "Of course."
                h "It's... I was just curious, but..."
                h "What's your relationship with Louise, exactly?"
                $ flena = "blush"
                l "Oh, that..."
                h "Never mind. It's not my business, I shouldn't have asked..."
                $ flena = "sad"
                l "It's alright... She and I..."
                if lena_reject_louise:
                    l "We hooked up, but I only see her as a friend. She was looking for something more, though..."
                    l "I told her it had been a mistake, and things aren't so cool between us right now."
                    h "Oh. I see... I..."
                    h "I hope things stay cool between us... I'd hate it if we..."
                    h "I mean, I don't want to be a nuisance to you..."
                    $ flena = "n"
                    l "Don't worry. You and Louise are nothing alike."
                else:
                    $ flena = "n"
                    l "We're friends... Friends with benefits, you could say."
                    h "Oh. I see. I had a feeling there was something going on..."
                    l "Well, it's just that."
                    h "Same as us, right...?"
                    l "You could say that... But you and Louise are nothing alike."
                $ fholly = "n"
                h "I bet she's a very cool girl. She's your friend after all..."
                h "Anyway, I hope we can hang out together again soon..."
        else:
            h "Me too. And I also enjoyed that photo shoot we did..."
            $ flena = "shy"
            if v10_holly_shoot:
                $ fholly = "blush"
                h "You made me feel so... comfortable and... I don't know how to explain it..."
                h "I never felt like that with anyone before."
                if ian_holly_dating:
                    l "Not even with Ian?"
                    h "Y-{w=0.3}yes, but... I still get nervous around him. With you it's... different." #findme - holly_change check?
                    l "Well, we can do another photo session whenever you want..."
                else:
                    l "We can do another one whenever you want..."
            else:
                l "We can do another one whenever you want..."
            h "Great... I'd like that."
    # friends
    else:
        $ fholly = "happy"
        if holly_gym:
            h "I hope we can hang out again soon. I had a lot of fun last night at the club!"
        else:
            h "I hope we can hang out again soon. I had so much fun last time at your concert!"
        $ flena = "smile"
        l "Of course! You know I love hanging out with you too."
        $ fholly = "shy"
        hide holly
        hide holly2
        hide holly3
        show holly3 at lef
        with short
        h "And I also enjoyed that photo shoot we did... But I was too awkward, I need to do better."
        l "We can try again whenever you want!"
        $ fholly = "happyshy"
        if ian_holly_dating:
            h "Thanks... I want to be able to be a girl Ian really wants to be with. I'm so glad I have you to teach me."
            l "Just don't put too much pressure on yourself. Ian likes you already, now you just have to let things flow."
        elif v8_holly_sex == "ivy":
            h "Thanks... I'm so glad I have you and Ivy to teach me. I feel I can finally change..."
            $ flena = "flirtshy"
            l "Hey, that's what best friends are for... Right?"
        else:
            h "Thanks... I'm so glad I have you to teach me."
            l "That's what good friends are for."
    l "This weekend I'm going to my parent's place again, but let's meet next week, okay...?"
    $ flena = "smile"
    l "Wait, are you free tomorrow?"
    if ian_holly_dating:
        $ fholly = "shy"
        h "I have a date with Ian... And later he said something about going to an event with his friends..."
        l "You mean that event organized by Emma and the neighborhood assembly she attends, right?"
        $ holly = "smile"
        h "Yes..."
        l "She invited me too, and I told her I'd be there."
        l "She told me they're opening a civic center or something like that. Apparently, they have restored an abandoned cinema and are going to organize events and meetings there."
        $ fholly = "happy"
        h "Yes, Ian told me about it!"
        l "I'll see you there, then. Take care, Holly."
    else:
        $ fholly = "n"
        h "I told my mom I'd help her with groceries and some stuff around the house..."
        l "Well, after you're done with that, why don't you join us?"
        l "Emma invited me to an event organized by the neighborhood assembly she attends to."
        l "She told me they're opening a civic center or something like that. Apparently, they have restored an abandoned cinema and are going to organize events and meetings there."
        $ fholly = "happy"
        h "That sounds really cool!"
        l "Then tag along with me. It'll be fun."
        $ fholly = "smile"
        if v10_ian_left:
            $ fholly = "sad"
            h "So, Ian will be there, too?"
            $ flena = "sad"
            l "Yes, I suppose so... His friends will be there."
            h "It'll be the first time you see each other since the break-up, right?"
            l "Yeah... I'm not too thrilled about it, but we're bound to run into each other at some point."
            l "It's been two weeks, so maybe we can... I don't know, I'll see how that goes. But I don't want any more drama in my life."
            h "I hope you can talk things over and... turn the page."
            l "I hope so too."
        else:
            h "Alright... I'm looking forward to it!"
    stop music fadeout 3.0
    if cafe_help:
        scene cafe with long
        if ian_lena_dating:
            "After Holly left, I finished my shift at the café and got ready for my date with Ian."
            scene streetnight with long
            jump v11prologueiandate
    else:
        scene cafeteria with long
        if ian_lena_dating:
            "Holly and I said our goodbyes and I left for home. I still had some time before my date with Ian."
            scene streetnight with long
            jump v11prologueiandate
    scene streetnight with long
    pause 1
    jump v11prologuefriday


########################################################################################################################################################################################################################
## IAN DATE ########################################################################################################################################################################################################################
########################################################################################################################################################################################################################
label gallery_CH11_S02:
    if _in_replay:
        call setup_CH11_S02 from _call_setup_CH11_S02

label v11prologueiandate:
    # findme define ian look
    $ ian_look = 2
    $ lena_look = 4
    $ fian = "smile"
    $ flena = "smile"
    if v11iandinner:
        pause 1
        show lena at rig
        show ian at lef
        with long
        l "Hey!"
        i "Hi, beautiful. How was your day?"
        if cafe_help:
            l "It's been pretty busy at the café, but I still have some energy for you."
            $ fian = "happy"
            i "I was hoping that was the case!"
        else:
            $ flena = "n"
            l "Well, you know... Quite relaxed since I'm unemployed now."
            $ fian = "n"
            i "How's your search for a new job going?"
            if lena_seymour_dating:
                l "Oh, well... I'm not in a hurry right now, so... I'm taking it easy."
                $ fian = "smile"
                i "That's great. You deserve to take it easy for a change."
                $ flena = "smile"
                l "I agree!"
            else:
                l "Not good, but I'm not losing hope just yet. But let's talk about something else tonight."
                $ fian = "smile"
                i "Of course."
        i "So, have you decided where you want to have dinner?"
        if lena_seymour_dating:
            l "Yes! I've seen this restaurant on social media... It specializes in tuna and other seafood, and it looks spectacular."
            $ fian = "n"
            i "Sounds expensive."
            l "Don't worry, it's on me."
            $ fian = "worried"
            i "Really? I wouldn't want you to spend too much money on me..."
            l "Just accept my invitation and let's enjoy the night! Come on, let's go!"
            scene streetnight with long
            if lena_ian_love:
                "It was a great evening. The food was delicious, and the company was even better."
                "Spending time with Ian always made me feel happy and relaxed, no matter the place."
            else:
                "It was a great evening. The food was delicious, and I always had a good time with Ian..."
            if ian_lena < 12:
                call friend_xp ('ian') from _call_friend_xp_953
            else:
                if lena_charisma < 10:
                    call xp_up ('charisma') from _call_xp_up_757
            "But only when we finally made it to my place we got to share some real intimacy, enjoying each other's company to the fullest."
        elif cafe_help:
            l "Any place is fine... I haven't had Japanese food in a while, do you dig it?"
            i "Yes, and I know just the place."
            $ flena = "smile"
            l "Lead the way!"
            scene streetnight with long
            if lena_ian_love:
                "The food was nice, but the company was even better. Spending time with Ian always made me feel happy and relaxed, no matter the place."
            else:
                "The food was nice, and the company made it even better. I always had a good time with Ian..."
            if ian_lena < 12:
                call friend_xp ('ian') from _call_friend_xp_954
            else:
                if lena_charisma < 10:
                    call xp_up ('charisma') from _call_xp_up_758
            "But only when we finally made it to my place we got to share some real intimacy, enjoying each other's company to the fullest."
        else:
            $ flena = "n"
            l "Anything will do, as long as it's cheap... What about a hot dog or something like that?"
            i "Alright. I guess we could go to the Fortress, they make good burgers too..."
            $ flena = "smile"
            l "Let's go!"
            scene streetnight with long
            if lena_ian_love:
                "I didn't care too much about dinner, as long as I could spend some time with Ian."
            else:
                "To be honest, I didn't care too much about dinner. I just wanted to have a good time with Ian..."
            if ian_lena < 12:
                call friend_xp ('ian') from _call_friend_xp_955
            else:
                if lena_charisma < 10:
                    call xp_up ('charisma') from _call_xp_up_759
            "And when we finally made it to my place, we got to share some real intimacy and enjoy each other's company to the fullest."
    else:
        pause 1
    scene lenaroomnight with long
    play sound "sfx/ah8.mp3"
    l "Uhhhn..."
    play music "music/sex_hot.mp3" loop
    scene v11_ian0 with long
    pause 1
    l "Yes, Ian... Lick me like that... Ohhh!"
    if v11iandinner == False:
        "Going on dates with Ian was nice, but this was what I truly needed..."
    "His tongue explored my folds, dancing around with such gentle skill that I felt like I was melting from the inside out."
    l "You're so good at this...!"
    i "That's because I love making you feel good..."
    "He wasn't lying. As soon as we got into my bedroom and started making out, he stripped me of my clothes and began kissing and caressing all of my body."
    "I felt my wetness dripping down my thighs as Ian continued to lap me up, pressing his tongue across my slit."
    "It moved in slow circles around my clit, and my body responded to his devoted touch."
    l "Oh, God... You're driving me to the edge..."
    if ian_lust > 6:
        i "Do you want to cum? Cum for me while I eat you out from behind..."
    elif ian_cuck or ian_lena_sex == False:
        l "But I want more..."
        jump v11rideianface
    else:
        i "Do you want to cum? Do it, cum for me..."
    menu:
        "{image=icon_lust.webp}Ride his face" if lena_lust > 7 or v10_lena_sex < 2 or lena_cheating or (lena_louise_sex and lena_reject_louise == False):
            $ renpy.block_rollback()
            label v11rideianface:
                $ v11_lena_dom = True
                if ian_cuck:
                    $ ian_cuck = 1
            scene v11_ian1
            if lena_tattoo1:
                show v11_ian1_t1
            if lena_tattoo2:
                show v11_ian1_t2
            if lena_tattoo3:
                show v11_ian1_t3
            if lena_piercing1:
                show v11_ian1_p1
            elif lena_piercing2:
                show v11_ian1_p2
            with long
            pause 1
            "I turned around, pushed Ian to the bed, and straddled his face, feeling his warm breath on my core."
            i "{i}\*Mhpf!\*{/i}"
            if v10_ivy_sex == 3:
                "I let out a soft moan, pushing my hips down against his face and grinding into him, just like Ivy had done to me."
                if lena_louise_sex and lena_reject_louise == False:
                    "But this time it was me taking control, just like I did with Louise."
                else:
                    "But this time it was me taking control."
            elif lena_louise_sex and lena_reject_louise == False:
                "I let out a soft moan, pushing my hips down against his face and grinding into him, taking control just like I did with Louise."
            else:
                "I let out a soft moan, pushing my hips down against his face and grinding into him, taking control of the situation."
            "Ian's eyes widened in surprise, but I could see he was aroused by my dominance. I reached down and grabbed his hand, guiding it to his penis."
            if ian_cuck:
                l "Now you're gonna jerk off quietly, like a good boy, while I ride your face."
            else:
                l "Masturbate for me while I ride your face..."
            play sound "sfx/oh1.mp3"
            "I moaned, pushing my hips harder against his face. I felt him start to move his hand up and down in rhythm with my movements."
            if ian_cuck or ian_lena_sex == False:
                l "Yes, stroke that cock... Does my pussy taste good? I bet you'd love to stick it inside..."
                if ian_lena_sex == False:
                    l "I'm sure it'd feel much better than your hand... But you know you haven't earned it yet."
                    l "Too bad, because I'm dying to feel a cock inside of me right now...!"
                    if v9_axel_sex or lena_fty_bbc:
                        l "A big, hard juicy cock..."
                else:
                    l "If you make me cum, I'll consider letting you do it... And I hope you do, because I'm dying to feel a cock inside of me right now."
                    if ian_cuck:
                        l "Will it be yours? Or are you gonna force me to look for some other dick?"
            else:
                l "Yes, stroke that cock... Does my pussy taste good? Do you want to stick it inside?"
                l "I'll let you do it... But first you'll have to please me..."
            "I rode him faster and harder, my breath coming in short, ragged gasps as I felt my orgasm approaching."
            "Ian's tongue kept exploring me, curling up and around, drawing out every moment of pleasure, pushing me further into bliss."
            if ian_lena_sex == False:
                "I could feel Ian stroking himself harder, still in rhythm with my hips."
                l "Are you gonna cum? Cum while I ride your face. Show me how much you love it!"
                i "{i}\*Mhpf... Mnhhh!\*{/i}{w=0.5}{nw}" with flash
                with vpunch
                pause 0.5
                "I felt Ian's cum splashing my back as his body tensed and trembled below mine, which was soon to follow."
            l "Yes, yes, yes...!"
            play sound "sfx/ah4.mp3"
            l "Ahhhhh!!!{w=0.5}{nw}" with flash
            with vpunch
            pause 0.5
            with vpunch
            pause 0.5
            with vpunch
            pause 0.5
            if ian_lena_sex == False:
                stop music fadeout 4.0
                scene v11_ian4
                if lena_tattoo2:
                    show v11_ian4_t2
                if lena_tattoo3:
                    show v11_ian4_t3
                show v11_ian4_iansad
                with long
                pause 1
                l "Ahhh, that was nice... And you seemed to enjoy it too!"
                i "Yeah, of course... You know I enjoy giving you pleasure."
                l "I feel so cozy right now... I feel I could fall asleep any moment now."
                if ian_cuck:
                    i "So you don't want to keep going?"
                    l "Tomorrow morning maybe?"
                    i "I have to be at the office early in the morning."
                    l "Next time then... Or I can jerk you off like last time, if you want to."
                    i "..."
                    i "Never mind, forget it."
                    scene black with long
                    "Ian held me and closed his eyes, resigned to sleep. But I could feel his cock still hard and throbbing under the bedsheets."
                else:
                    i "..."
                    i "Of course."
                    scene black with long
                    "This time Ian did not complain. He just held me and closed his eyes, even though his cock was still hard and throbbing under the bedsheets."
                $ ian_cuck = 2
                $ renpy.end_replay()
                $ gallery_unlock_scene("CH11_S02")
                jump v11prologuefriday
            else:
                scene v11_ian4
                if lena_tattoo2:
                    show v11_ian4_t2
                if lena_tattoo3:
                    show v11_ian4_t3
                with long
                pause 1
                i "Well, you seem to have enjoyed that... What got into you?"
                l "You just got me super horny, and I had to do it."
                if v10_lena_sex < 2 or ian_cuck:
                    l "I feel so cozy right now... I feel I could fall asleep any moment now."
                    show v11_ian4_iansad 
                    if lena_tattoo2:
                        hide v11_ian4_t2
                        show v11_ian4_t2
                    with short
                    i "Wait a minute, I believe the deal was that you'd let me fuck you if I made you cum."
                    menu:
                        "{image=icon_friend.webp}Have sex with Ian":
                            $ renpy.block_rollback()
                            hide v11_ian4_iansad with short

                        "{image=icon_sad.webp}Deny him":
                            $ renpy.block_rollback()
                            $ ian_cuck = 2
                            stop music fadeout 4.0
                            show v11_ian4_lenasad with short
                            l "Tomorrow morning maybe?"
                            i "I have to be at the office early in the morning."
                            l "That orgasm left me spent. I'm not feeling all that horny anymore..."
                            i "I see... Well, if that's the case, let's leave it as it is."
                            call friend_xp ('ian',-2) from _call_friend_xp_956
                            l "Or I can jerk you off like last time, if you want to."
                            i "..."
                            i "Never mind, forget it."
                            scene black with long
                            "Ian held me and closed his eyes, resigned to sleep. But I could feel his cock still hard and throbbing under the bedsheets."
                            $ renpy.end_replay()
                            $ gallery_unlock_scene("CH11_S02")
                            jump v11prologuefriday

                else:
                    i "So, what now? Are you still horny? Because I believe I held my part of the bargain..."
                l "Oh, yeah... Don't worry..."
                l "I'm gonna fuck you real good."

        "I want your cock!":
            $ renpy.block_rollback()
            l "I want to cum on your cock! I need it in me...!"
            if lena_lust < 8:
                call xp_up ('lust') from _call_xp_up_760
                pause 0.5

        "Put it in!":
            $ renpy.block_rollback()
            l "Put it in... I want to cum, but put it in!"

    scene v11_ian2
    if lena_tattoo2:
        show v11_ian2_t2
    if lena_tattoo3:
        show v11_ian2_t3
    with long
    pause 1
    if v11_lena_dom:
        "I took matters into my own hands again, making Ian groan with pleasure as I drove his rock-hard tool inside of me."
    else:
        "I turned around and took matters into my own hands, pushing Ian onto the bed and climbing on top of him."
        "My hand reached down and grabbed his rock-hard tool, making Ian groan with pleasure as I drove it inside me."
    play sound "sfx/ah1.mp3"
    l "Mhhh, yes... It's spreading me open... So deep...!"
    i "I can feel you tightening around my cock... You feel amazing, Lena!"
    scene v11_ian2a 
    if lena_tattoo2:
        show v11_ian2_t2
    if lena_tattoo3:
        show v11_ian2_t3
    with long
    pause 1
    "My whole body was shaking and I gripped Ian tightly as I felt him swell inside me. His hands grabbed my ass, pulling me closer and pushing me harder."
    play sound "sfx/oh1.mp3"
    scene v11_ian2b
    if lena_tattoo2:
        show v11_ian2_t2
    if lena_tattoo3:
        show v11_ian2_t3
    with vpunch
    "I threw my head back and gave a loud moan as I rode him harder, pushing back and forth as the intensity increased."
    play sound "sfx/ah9.mp3"
    scene v11_ian2_animation
    if lena_tattoo2:
        show v11_ian2_t2_animation
    if lena_tattoo3:
        show v11_ian2_t3_animation
    with fps
    pause 3
    "His thrusts echoed around the room, making me shout with each one as pleasure smothered all my senses."
    "I raised my hips to meet each one and as they increased I could feel a sweet ache growing between my legs..."
    if lena_cheating:
        "But I also felt the bittersweet taste of the mixture of feelings brewing inside of me."
        "The bliss of being with Ian, and the guilt for having cheated on him... And..."
        "Some kind of anxiety-inducing thrill, a feeling that made me feel ashamed and aroused at the same time."
        "My body just reveled in the sensation of being taken, of giving myself to the one who desired me. And Ian's desire was scorching."
    else:
        "My head spun with pleasure, dizzy with the intensity of our connection."
        "I welcomed every thrust, clinging to him as I rose higher and higher, reaching for bliss within his arms."
        "Sex with Ian was intimate and passionate in a way I hadn't experienced with anyone else..."
        if ian_lena_couple:
            "I felt I could let go, release all control and just give in to his embrace."
            "Let him reach deep into my body and into my soul, share my being with him and melt in the ecstasy of our bond."
        elif lena_ian_love:
            "It was more than sex, at least to me. The strong feelings that were engulfing my entire being were impossible to ignore."
            "Still, I was aware there was a barrier between our emotions. But it felt really thin, especially at this moment."
            "If I could only break through it..."
        else:
            "It felt amazing, but it also made me feel a bit scared."
            "What would happen if I gave in, lowering all my barriers, and allowing him to reach my deepest emotions...?"
    menu v11lenaianprologuesex:
        "{image=icon_love.webp}I love you!" if lena_ian_love > 0 and lena_ian_love < 2:
            $ renpy.block_rollback()
            $ lena_ian_love = 2
            if ian_lena_couple:
                "My feelings gushed out of my body, wanting to be expressed."
                l "I love you, Ian... I love you, I love you!"
                i "Lena...! I love you too!"
                if lena_charisma < 10:
                    call xp_up ('charisma') from _call_xp_up_761
            else:
                "Caught in the heat of the moment, I wasn't able to hold back my feelings from spilling out."
                l "I... I love you, Ian...!"
                if ian_lena_love:
                    i "I..."
                    i "I love you too...!"
                    if ian_lena < 12:
                        call friend_xp ('ian') from _call_friend_xp_957
                # unrequieted love
                else:
                    i "..."
                    hide v11_ian2_animation
                    show v11_ian2a
                    with short
                    "He didn't answer, but I noticed the vigor of his thrusts dropping a notch."
                    "We kept going at it for a while, but the mood turned weird. I felt both of us suddenly withdrawing our emotions, and all because I couldn't keep my mouth shut."
                    if ian_lena > 3:
                        call friend_xp ('ian',-1) from _call_friend_xp_958
                    stop music fadeout 4.0
                    "Finally, we felt exhausted and our love-making came to an end, even though none of us managed to reach climax. I felt guilty because of it."
                    scene v11_ian4
                    if lena_tattoo2:
                        show v11_ian4_t2
                    if lena_tattoo3:
                        show v11_ian4_t3
                    show v11_ian4_iansad
                    show v11_ian4_lenasad
                    with long
                    pause 1
                    i "Are you okay?"
                    l "Yes... I'm just a bit tired, sorry."
                    i "Don't worry... I understand. Do you want me to do something else? I can go back to eating you out..."
                    l "No, it's okay. You don't have to push yourself."
                    i "Alright..."
                    l "..."
                    "I rested my head on Ian's chest, feeling ashamed at my reaction. I should've known better..."
                    "I knew where he stood in this relationship we had. No strings attached... And yet, I couldn't help but feel the way I did."
                    "Why was I so stupid...?"
                    scene black with long
                    $ renpy.end_replay()
                    $ gallery_unlock_scene("CH11_S02")
                    jump v11prologuefriday
            if lena_fty_slave or ian_lena_dom > 1 or lena_lust > 7:
                jump v11lenaianprologuesex
            else:
                jump v11lenaianprologuecum

        "{image=icon_lust.webp}Choke me, please" if lena_fty_slave or ian_lena_dom > 1 or lena_lust > 7:
            $ renpy.block_rollback()
            $ lena_fty_slave = True
            $ v11_lena_choke = True
            l "Ian...! Choke me... I want you to choke me!"
            if ian_lena_dom == 2:
                i "Oh, so you'd like that, huh?"
            elif ian_lena_dom == 1:
                i "If that's what you want... Come here!"
            elif ian_lena_dom == 0:
                i "Uh, for real..?"
                l "Just choke me!"
            scene v11_ian3 with long
            pause 1
            if ian_lena_dom > 0:
                "My request seemed to turn him on even more and he wrapped his hand around my neck. His touch was gentle yet strong, making me quiver with pleasure."
                "I moaned with delight, feeling his grip tightening as he thrust deeper and faster, controlling me from behind, my body trapped between the bed and him."
            else:
                $ ian_lena_dom = 1
                "I turned around and offered Ian my back, letting him take control. He hesitated only a moment before wrapping his hand around my neck."
                "I moaned with delight, feeling his grip tightening as he thrust deeper and faster, getting more and more into it."
            l "Yes, push it deeper...! I want you to make me yours..."
            if lena_cheating:
                "If he did, I couldn't cheat on him anymore... I wouldn't want to."
            if lena_axel_desire:
                "That's how Axel made me feel."
                scene v11_ian3b with Dissolve (2.5)
                pause 0.5
                "I still remembered the feeling of his strong hand around my neck, and his big cock stretching my pussy..."
                "He hurt me, and I ran away from him. But that day, at his apartment... I felt his hand on my neck again, and his cock deep inside of me once more."
                "I felt guilty. I felt dirty. I felt like a slut... And I felt so damn good..."
            else:
                "His grip tightened even more, excited at my words, and I gasped for air with a smile."
            "My body was drifting into a blissful state of euphoria with each thrust, blanketed with numbing pleasure."
            l "That's it, fuck me, fuck me like that..."
            if lena_cheating and lena_axel_desire == False:
                "I felt guilty. I felt dirty. I felt like a slut... And I felt so damn good..."
            if lena_axel_desire:
                l "Ahhh, harder...! I'm yours, I'm daddy's little slut...!"
                scene v11_ian3 with long
            else:
                l "I'm yours, I'm your little slut, Ian...!"
            if ian_lena_dom == 2:
                i "Fuck yes, you are... So be a good little slut and cum for me now."
                "Hearing him talking dirty to me like that was the last push."
            else:
                i "Yes... Yes, you are! And you love it, huh?"
            l "Yes... Oh, fuck..."
            play sound "sfx/oh3.mp3"
            l "God!! Ohhhh!!! {w=0.5}{nw}" with flash
            # findme add art
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            with vpunch
            pause 0.6
            "Finally, I let go, and my orgasm ripped through me, my mind spiraling with it."

        "I'm gonna cum!":
            $ renpy.block_rollback()
            label v11lenaianprologuecum:
                if ian_chad > 3 or lena_fty_slave or ian_lena_dom > 0:
                    l "Don't stop! Keep fucking me, Ian, please...!"
                    i "I'm not stopping... Are you gonna cum? Tell me, are you gonna cum for me, Lena?"
                else:
                    l "Don't stop, Ian...! I'm about to cum...!"
                    i "I'm not stopping... Cum, cum for me Lena!"
            l "Yes! Yes, yes, yes...!"
            "His relentless rhythm pushed me higher and higher, until I felt myself cresting and then crashing over the edge of pleasure."
            hide v11_ian2_animation
            show v11_ian2b
            if lena_tattoo2:
                show v11_ian2_t2
            with vpunch
            play sound "sfx/oh3.mp3"
            l "Ahhhh, I'm cumming!! Ian!!{w=0.5}{nw}" with flash
            with vpunch
            pause 0.5
            with vpunch
            pause 0.5
            with vpunch
            if lena_lust < 8:
                call xp_up ('lust') from _call_xp_up_762
    # end
    stop music fadeout 4.0
    "Ian followed shortly after, spilling his desire and pleasure into me."
    scene v11_ian4
    if lena_tattoo2:
        show v11_ian4_t2
    if lena_tattoo3:
        show v11_ian4_t3
    if lena_axel_desire:
        show v11_ian4_lenasad
    with long
    pause 1
    "I lay in his arms, letting our bodies linger in the pleasant heat of the moment."
    if v11_lena_choke:
        if ian_lena_dom == 2:
            i "Wow... That was so damn hot. You were so turned on tonight!"
        else:
            i "Wow... What was all that about?"
            l "What do you mean?"
            i "You asking me to choke you all of a sudden..."
        if lena_axel_desire:
            i "And... Did you call me {i}daddy{/i} at one point?"
            l "That was..."
            "I felt so bad for thinking about Axel while doing it with Ian... I didn't do it on purpose, but he just popped into my mind..."
            "No, in fact... Axel hadn't left my thoughts for weeks now. I had been trying to put it in the back of my mind, but it kept creeping to the front."
            "I knew there was something wrong, something that needed to be resolved somehow, but... How?"
            i "Um... Are you okay?"
            l "Huh? Yeah, sorry..."
            hide v11_ian4_lenasad with short
            l "I'm just feeling a bit... dizzy after such a strong orgasm."
        else:
            l "Well... That's the effect you have on me. And I think you really enjoy it."
            i "How could I not? Sex with you is just incredible, Lena..."
            l "It is... I love the feeling of you losing all inhibitions and using me to please yourself."
            i "And I love making you moan, and tremble, and watching you cum beneath me."
            l "It was a really strong orgasm..."
    else:
        i "That was so nice... Sex with you is just incredible, Lena."
        l "It is... I love it when you let go and lose yourself in the moment with me."
        i "How could I not? I get so turned on when you moan and tremble for me... And what turns me on the most is watching you cum."
        l "It was a really strong orgasm..."
    if ian_lena_couple:
        l "Hey, what's with that grin on your face? You look pleased!"
        i "Pleased? Well, I certainly am... But I'm smiling because I'm so glad to be here tonight. With you."
        l "Yeah, me too..."
        i "You know how I told you about my doubts about getting into another relationship..."
        l "Do you still have them?"
        i "They've not left me completely yet. But the more time I spend with you, and the more I get to know you, the fainter they become."
        l "We have a sweet talker in here..."
        i "Don't tease me, I'm trying to be serious here."
        l "An unusually serious and grumpy sweet talker, I see... What a rare specimen!"
        l "Maybe that's why I like you..."
        i "Ha ha, get outta here. I'm done opening my heart to you."
        "Ian played offended and turned to the side, but I jumped on him again, hugging and kissing him."
        l "I like it when you open your heart to me. And I also love this thing we have."
        l "I believe it can work... It {i}is{/i} working."
        if ian_cheating:
            show v11_ian4_iansad 
            if lena_tattoo2:
                hide v11_ian4_t2
                show v11_ian4_t2
            with short
        i "Yeah... It is. It definitely is."
        scene black with long
        if lena_cheating:
            "And yet... I was making sure to self-sabotage myself and my relationship with Ian."
            "I felt I was treading on thin ice, and a wrong step could sink both of us in cold water. I had to be careful..."
    else:
        if lena_ian_love == 2:
            show v11_ian4_lenasad with short
            "Thankfully, Ian didn't mention my slip in the heat of the moment. When I told him I loved him..."
            "But he answered the same way. He said he loved me too..."
            "I rested my head on Ian's chest, filled with bittersweet feelings. Being with him like this was fantastic, but so many fears prevented me from completely enjoying the moment."
            scene black with long
            "Fears, doubts, and mistakes... What were we doing, and where was this headed?"
            "All I knew was I didn't want my heart broken again."
        else:
            "I rested my head on Ian's chest, feeling its warmth. He caressed my hair and my back, making me purr like a kitten."
            if lena_ian_love:
                "Being with Ian like this felt perfect... Almost perfect."
                if ian_lena_love:
                    "We shared a special connection, there was no doubt about that. I loved being with Ian, and the way he made me feel..."
                    "I had the impression Ian felt the same way about me, but there was something that prevented us to get closer together."
                    scene black with long
                    "Our fears, our doubts, and our mistakes... My mistakes. All I knew was I didn't want my heart broken again."
                else:
                    "We worked so well together, and yet... I couldn't shake off the feeling we were on a different page, despite all."
                    "I was trying to keep my feelings for him in check, and I wondered if he had the same problem at all."
                    scene black with long
                    "All I knew was I didn't want my heart broken again."
            else:
                "This was so nice... In a way, it felt way too nice for a casual fling."
                "I wondered if we could even call it a fling at this point. We had agreed to keep things casual, but this felt far from it..."
                show v11_ian4_lenasad with short
                if ian_lena_love:
                    "And I knew Ian's feelings for me were beyond casual. I just couldn't accept them, not when I considered all things."
                else:
                    "Did Ian have the same conflicting emotions as me? Or was he totally comfortable with the situation, no strings attached at all?"
                i "Is everything alright?"
                l "Yes, it's nothing... Let's get some sleep."
                scene black with long
                "I wondered... Was I doing the right thing?"
                "All I knew was I didn't want my heart broken again."
    pause 1
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH11_S02")
########################################################################################################################################################################################################################
## LENA FRIDAY ########################################################################################################################################################################################################################
########################################################################################################################################################################################################################
label v11prologuefriday:
    call calendar(_month="July", _day="Friday", _week=3) from _call_calendar_106
    scene lenaroom with long
    $ fian = "smile"
    $ flena = "n"
    play music "music/normal_day2.mp3" loop
    if ian_lena_dating:
        $ ian_look = 3
        "I woke up the next morning feeling the bed shifting beside me."
        $ fian = "n"
        show ianunder at lef with short
        "I opened my eyes and saw Ian getting up, the sheets rustling as he moved."
        show lenanude2 at rig with short
        l "Good morning... What time is it?"
        i "Oh, did I wake you up? Sorry... I have to be at the office shortly, so I need to get going."
        l "Of course... Do you want to take a shower?"
        if v3_gillian_stop:
            $ fian = "smile"
            i "That'd be nice. Together?"
            l "I need five more minutes..."
            i "Alright."
        else:
            i "Yeah, that'd be nice... Thanks."
        scene lenaroom with short
        "I tried to bury my head deeper into the pillow, wishing I could stay in my sleepy state a little longer. I felt so cozy..."
        $ lena_look = "ianshirt"
    else:
        "When I woke up the next morning, I tried to bury my head deeper into the pillow, wishing I could stay in my sleepy state a little longer."
        $ lena_look = 1
    play sound "sfx/sms.mp3"
    "But the buzzing of my phone wouldn't let me. I finally decided to pick it up and check my messages."
    show lenaunder with short
    l "Looks like quite some people wrote to me..."
    $ config.menu_include_disabled = False
    $ greyed_out_disabled = True
    $ v11sms_mom = False
    $ v11sms_emma = False
    $ v11sms_mike = True
    if mike_collab:
        $ v11sms_mike = False
    $ v11sms_mark = True
    if v10_mark_flirt or v10_wc_bj == "mark":
        $ v11sms_mark = False
    $ v11sms_axel = True
    if lena_axel_dating:
        $ v11sms_axel = False
    $ v11sms_photo = False
    menu v11prologuesms:
        "Mom's message" if v11sms_mom == False:
            $ renpy.block_rollback()
            $ v11sms_mom = True
            nvl clear
            lm_p "{i}Honey, at what time are you arriving today?{/i}"
            lm_p "{i}Good morning by the way {image=emoji_dance.webp} {image=emoji_crown.webp} {image=emoji_100.webp}{/i}"
            $ flena = "worried"
            lm_p "{i}Can you tell us? It's important!{/i}"
            lm_p "{i}Good mooorningggg.{/i}"
            l "Ugh."
            $ flena = "sad"
            l_p "{i}Hey Mom, just woke up. My plan is to take the last train, so I'll arrive a bit past midnight I think.{/i}"
            lm_p "{i}This late? {image=emoji_disgust.webp} Why don't you take the train in the afternoon so you get home before dinner?{/i}"
            lm_p "{i}In fact, we could use your help preparing dinner. We've been by ourselves the entire week!{/i}"
            $ flena = "serious"
            l_p "{i}I have plans with some friends, Mom. I'm already spending my weekend with you, so don't complain.{/i}"
            lm_p "{i}So spending some time with your parents is a punishment for you?{/i}"
            l_p "{i}That's not what I said. I'll see you tonight, or tomorrow morning if you're asleep.{/i}"
            l_p "{i}I have things to do now, bye.{/i}"
            $ flena = "sad"
            l "It's so draining having to deal with her first thing in the morning..."
            if v11sms_mom and v11sms_emma and v11sms_mike and v11sms_mark and v11sms_photo and v11sms_axel:
                jump v11prologuefrdy2
            jump v11prologuesms

        "Emma's message" if v11sms_emma == False:
            $ renpy.block_rollback()
            $ v11sms_emma = True
            nvl clear
            e_p "{i}Hey! The opening of the civic center starts at six o'clock! See you there?{/i}"
            "Emma had been helping me way too much to leave her hanging. If she wanted me there, I had to be."
            if lena_seymour_dating and seymour_disposition > 1:
                $ flena = "sad"
                if v10_defend_seymour:
                    "Though we didn't see eye to eye when it came to Seymour... I could tell she didn't like it one bit when I defended him."
                else:
                    "I knew she didn't like Seymour one bit, though... And she didn't like that I was working with him, either."
                $ flena = "n"
            l_p "{i}Yes, I will! Do I need to bring something?{/i}"
            e_p "{i}Bring your guitar! We'll be hosting an open mic, it would be cool if you played!{/i}"
            l_p "{i}I'll think about it...{/i}"
            e_p "{i}Oh, come on, don't be shy now! See you there {image=emoji_crazy.webp}{/i}"
            if ian_holly_dating == False:
                l_p "{i}By the way, I told Holly to join, if that's okay.{/i}"
                e_p "{i}Of course! In fact, the more people the better. And the more the merrier!{/i}"
                e_p "{i}Besides, Holly's super cool. I talked to her last time at the Fortress, she's such a sweetheart {image=emoji_love.webp}{/i}"
                l_p "{i}Yeah, she definitely is.{/i}"
            l "Well, it looks like I'm performing tonight..."
            if v11sms_mom and v11sms_emma and v11sms_mike and v11sms_mark and v11sms_photo and v11sms_axel:
                jump v11prologuefrdy2
            jump v11prologuesms

        "Mike's message" if mike_collab and v11sms_mike == False:
            $ renpy.block_rollback()
            $ v11sms_mike = True
            nvl clear
            mk_p "{i}What's up, Lena? About that collab we talked about, are you still in?{/i}"
            $ flena = "smile"
            l_p "{i}Yes, I am! Do you have anything in mind yet?{/i}"
            play sound "sfx/sms.mp3"
            mk_p "{i}Yes, I just finished a beat and I think your voice would go great with it. Can I send you the track so you can work over it?{/i}"
            l_p "{i}Sure, I'll take a listen and see what I can come up with.{/i}"
            if v10_stalkfap == "mike" or v10_wc_bj == "mike" or lena_mike_dating or v10_mike_sex:
                if v10_stalkfap == "mike":
                    mk_p "{i}Awesome {image=emoji_glasses.webp} Oh, and let me know the next time you need a cameraman for your sexy photo shoots.{/i}"
                    mk_p "{i}I'll be glad to lend you a hand again {image=emoji_devil.webp}{/i}"
                elif v10_wc_bj == "mike":
                    mk_p "{i}Awesome {image=emoji_glasses.webp} Oh, and I still owe you one for the toilet escapade at Blazer. Too bad we got interrupted by an asshole.{/i}"
                    mk_p "{i}So, when you want me to pay you back, you know. Give me a call {image=emoji_wink.webp}{/i}"
                elif lena_mike_dating or v10_mike_sex:
                    mk_p "{i}Awesome {image=emoji_glasses.webp} And when you want to hang out again, you know... Give me a call.{/i}"
                if lena_cheating:
                    $ flena = "sad"
                    "I wanted to answer, but a sting of guilt prevented me. Not that it matters much..."
                    "My guilt proved inconsistent, and I knew I would meet Mike again... Soon."
                else:
                    $ flena = "flirtshy"
                    l_p "{i}I will, don't worry about it.{/i}"
                    if lena_mike_love:
                        l_p "{i}You won't get rid of me easily... I like you too much {image=emoji_devil.webp}{/i}"
            else:
                mk_p "{i}Awesome {image=emoji_glasses.webp} Give me your e-mail and I'll send the file there.{/i}"
                mk_p "{i}I can't wait to see what you've got for me.{/i}"
                if lena_mike_over:
                    $ flena = "n"
                    l "It's a bit weird collaborating with Mike after I told him our fling was over..."
                    l "But this could be interesting. We'll see what comes out of it."
                else:
                    l "Well, this could be interesting... We'll see what comes out of it."
            $ flena = "n"
            if v11sms_mom and v11sms_emma and v11sms_mike and v11sms_mark and v11sms_photo and v11sms_axel:
                jump v11prologuefrdy2
            jump v11prologuesms

        "Unknown message" if (v10_mark_flirt and v11sms_mark == False) or (v10_wc_bj == "mark" and v11sms_mark == False):
            $ renpy.block_rollback()
            if persistent.include_disabled:
                $ config.menu_include_disabled = True
            $ greyed_out_disabled = False
            $ v11sms_mark = True
            $ flena = "n"
            l "Whose number is this? It's not one of my contacts..."
            nvl clear
            if v10_wc_bj == "mark":
                ma_p "{i}Hey Lena, what's up? This is Mark, we met at Ivy's party. Though I'm sure you remember me {image=emoji_devil.webp}{/i}"
            else:
                ma_p "{i}Hey Lena, what's up? This is Mark, we met at Ivy's party. And I'd say we got along rather nicely {image=emoji_smile.webp}{/i}"
            $ flena = "shy"
            if v10_axel_fight == "mark":
                ma_p "{i}Anyway, it was a pity your psycho ex crashed the party. Why don't you make it up to me by getting some drinks together one of these days?{/i}"
            elif holly_guy == 2:
                ma_p "{i}I had to leave earlier than planned, but I'd love to hang out with you again. Are you up to getting a few drinks one of these days?{/i}"
            else:
                ma_p "{i}It sucked that your psycho ex crashed the party, but I'd love to hang out with you again. Are you up to getting a few drinks one of these days?{/i}"
            ma_p "{i}They're on me, of course {image=emoji_wink.webp}{/i}"
            menu:
                "{image=icon_lust.webp}I'd love to":
                    $ renpy.block_rollback()
                    $ flena = "flirtshy"
                    l_p "{i}I'd love to take you up on that offer...{/i}"
                    label v11markaccept:
                        $ lena_mark_dating = True
                    if lena_cheating:
                        "I had already cheated on Ian... What difference would it make?"
                        "I told myself I had to stop, and I would. Soon."
                        play sound "sfx/sms.mp3"
                        ma_p "{i}Awesome, babe. When are you free?{/i}"
                        $ flena = "n"
                        l_p "{i}My life has been a bit of a mess lately and I'm not sure yet.{/i}"
                        l_p "{i}But I'll let you know as soon as I can and we can go grab those drinks.{/i}"
                        ma_p "{i}I'll take your word for it! See you soon then, I hope.{/i}"
                        l_p "{i}You can count on it {image=emoji_kiss.webp}{/i}"
                    elif ian_lena_couple and ian_lena_breakup == False:
                        $ flena = "sad"
                        l_p "{i}But I can't. I have a boyfriend.{/i}"
                        play sound "sfx/sms.mp3"
                        ma_p "{i}Well, I'm not the jealous type. Is he?{/i}"
                        l_p "{i}In this case, he would be. Sorry, I really have to pass up on your offer.{/i}"
                        ma_p "{i}Damn, what a pity. Well, let me know if you change your mind. I'd love to take you out {image=emoji_wink.webp}{/i}"
                        l "It's a shame, but... I decided to be with Ian."
                    else: 
                        play sound "sfx/sms.mp3"
                        ma_p "{i}Awesome, babe. When are you free?{/i}"
                        $ flena = "n"
                        l_p "{i}My life has been a bit of a mess lately and I'm not sure yet.{/i}"
                        l_p "{i}But I'll let you know as soon as I can and we can go grab those drinks.{/i}"
                        ma_p "{i}I'll take your word for it! See you soon then, I hope.{/i}"
                        l_p "{i}You can count on it {image=emoji_kiss.webp}{/i}"

                "What about Holly?" if holly_guy > 0:
                    $ renpy.block_rollback()
                    if holly_guy == 2:
                        l_p "{i}Wait, aren't you dating my friend Holly?{/i}"
                        play sound "sfx/sms.mp3"
                        ma_p "{i}Dating? Barely {image=emoji_laugh.webp}{/i}"
                        ma_p "{i}She was just curious and asked me to help her have some fun, that's all.{/i}"
                        if lena_wits < 10:
                            call xp_up ('wits') from _call_xp_up_763
                        ma_p "{i}So, what do you say? Do you want to have some fun, too? {image=emoji_devil.webp}{/i}"
                    else:
                        l_p "{i}Before that, what's the deal between you and my friend Holly?{/i}"
                        play sound "sfx/sms.mp3"
                        ma_p "{i}No deal, Ivy introduced us and we've been getting to know each other, but I'm not sure she's really interested, to be honest...{/i}"
                        if lena_wits < 10:
                            call xp_up ('wits') from _call_xp_up_764
                        ma_p "{i}So, what about you? Are you interested?{image=emoji_wink.webp}{/i}"
                    menu:
                        "{image=icon_lust.webp}I am":
                            $ renpy.block_rollback()
                            $ flena = "flirtshy"
                            l_p "{i}Actually, I am... {image=emoji_devil.webp}{/i}"
                            jump v11markaccept

                        "No, sorry":
                            $ renpy.block_rollback()
                            l_p "{i}No, sorry. It was fun, but that was it. Take care, bye {image=emoji_smile.webp}{/i}"
                            jump v11markdecline

                "No, thanks":
                    $ renpy.block_rollback()
                    l_p "{i}No, thanks. It was fun, but that was it. Take care, bye {image=emoji_smile.webp}{/i}"
                    label v11markdecline:
                        ma "{image=emoji_disgust.webp} {image=emoji_disgust.webp} {image=emoji_disgust.webp}"

                "Ignore him":
                    $ renpy.block_rollback()
                    $ v11_ignore_mark = True

            $ flena = "n"
            $ config.menu_include_disabled = False
            $ greyed_out_disabled = True
            if v11sms_mom and v11sms_emma and v11sms_mike and v11sms_mark and v11sms_photo and v11sms_axel:
                jump v11prologuefrdy2
            jump v11prologuesms

        "Axel's message" if lena_axel_dating and v11sms_axel == False:
            $ renpy.block_rollback()
            if persistent.include_disabled:
                $ config.menu_include_disabled = True
            $ greyed_out_disabled = False
            $ v11sms_axel = True
            $ flena = "worried"
            l "I have a message from Axel..."
            if v9_axel_sex == False:
                $ flena = "serious"
            l "It's the first time he's tried contacting me since our run-in at the club. What does he want?"
            $ flena = "worried"
            l "Wow, he wrote a big paragraph... This is gonna be dense."
            nvl clear
            x_p "{i}Hi Lena. First of all, I wanted to apologize for the ruckus that was last night.{/i}"
            x_p "{i}I know my apologies mean little to you at this point, but I admit I was out of line, for what is worth.{/i}"
            x_p "{i}I'm sorry I caused you anguish and trouble, and I understand if you're mad at me, you have reasons to be.{/i}"
            if v9_axel_sex:
                l "At least he's admitting he fucked up... Again."
            else:
                $ flena = "serious"
                l "You're right... And I'm fed up with your apologies. I should've never trusted you..."
            x_p "{i}Anyway, I just wanted to update you on the modeling agency situation.{/i}"
            x_p "{i}I presented them your portfolio and they liked it, but they were only interested in hiring a new model for the summer campaign and they gave that spot to another candidate.{/i}"
            $ flena = "sad"
            if v9_axel_sex:
                l "That's too bad... I should've known this was getting nowhere..."
            else:
                l "I should've known this was getting nowhere... This was a waste of my time."
                if v9_axel_kiss != "kiss":
                    $ flena = "serious"
                    l "Axel probably used it as an excuse to get close to me again. He showed his hand when he tried to kiss me."
                    $ flena = "sad"
            l "So they picked someone else... I wonder if Ivy got the job."
            l "Wait, he's still writing..."
            play sound "sfx/sms.mp3"
            x_p "{i}However, I can keep pushing your application if you're interested.{/i}"
            x_p "{i}I'll be working with them and the model they chose this summer; I'm sure I can convince them to give you a chance for the autumn campaign.{/i}"
            menu:
                "{image=icon_friend.webp}Thank you" if lena_axel_desire:
                    $ renpy.block_rollback()
                    $ flena = "n"
                    l_p "{i}Yes, I'd like that... Thank you, Axel.{/i}"
                    x_p "{i}I'll be sure to make them see your potential {image=emoji_wink.webp}{/i}"
                    $ flena = "worried"
                    l "I wonder if this is a good idea after all, considering what happened between us..."
                    l "Maybe if I hadn't slept with Axel, the situation at Ivy's party could've been avoided. What am I even doing...?"
                    x_p "{i}By the way, would you be up to getting a coffee one of these days?{/i}"
                    $ flena = "blush"
                    l "..."
                    jump v11axeltext

                "You don't have to":
                    $ renpy.block_rollback()
                    if v9_axel_kiss != "kiss":
                        $ flena = "serious"
                    l_p "{i}Don't worry about it. You've done plenty already.{/i}"
                    "I knew working with Axel meant trouble. It was obvious we couldn't keep things strictly professional."
                    if v9_axel_sex:
                        "We had tried, and it didn't work out. It was best if I kept my distance, especially after what happened between us..."
                    elif v9_axel_kiss != "kiss":
                        "I didn't want him trying to make a move on me again. I should've kept my distance altogether."
                    else:
                        "We had tried, and it didn't work out. It was best if I kept my distance..."
                    x_p "{i}I really don't mind. As I said, they liked your portfolio already, it's just they liked someone else's better.{/i}"
                    x_p "{i}But I know they'd work with you if I nudge them a bit.{/i}"
                    $ flena = "n"
                    l_p "{i}Well then, let's talk about it again in the fall.{/i}"
                    x_p "{i}Alright. But what about meeting for a coffee before then?{/i}"
                    if lena_axel_desire:
                        $ flena = "worried"
                        l "There it is..."
                        label v11axeltext:
                            l_p "{i}Are you sure it's a good idea, after what happened the other night?{/i}"
                        x_p "{i}That's the reason I'd like to apologize in person and make it up to you somehow. I know my behavior was inexcusable.{/i}"
                        $ flena = "sad"
                        "A part of me wanted to believe him, but I knew Axel all too well to really trust in his words..."
                        "Or perhaps this was his way of trying to make things right?"
                        l_p "{i}I don't know, Axel. I think it's better if we keep things strictly professional, especially considering what happened between us.{/i}"
                        l_p "{i}We've both turned the page, and I wouldn't want to make things more complicated than they need to be.{/i}"
                    else:
                        $ flena = "serious"
                        l "There it is..."
                        l_p "{i}I don't think it's a good idea, considering what happened the other night.{/i}"
                        x_p "{i}You're probably right. My behavior was inexcusable, but I'd like to apologize in person and make it up to you somehow.{/i}"
                        l_p "{i}You're doing enough as it is. And I think it's better if we keep things strictly professional.{/i}"

                "{image=icon_mad.webp}I'm not interested anymore" if lena_axel_desire == False:
                    $ renpy.block_rollback()
                    $ lena_axel_over = True
                    $ lena_axel_dating = False
                    $ flena = "serious"
                    l_p "{i}Just forget about it. We gave it a shot, but it's clear collaborating with you is not working out.{/i}"
                    x_p "{i}So you're not interested anymore?{/i}"
                    l_p "{i}Nope.{/i}"
                    x_p "{i}If it's because of what happened the other night... I know my behavior was inexcusable, but I'm trying to make it up to you.{/i}"
                    x_p "{i}Why don't we meet for coffee and talk it over?{/i}"
                    l "There it is..."
                    if v9_axel_sex:
                        $ flena = "worried"
                        l "I fell for it the first time, but I can't make the same mistake twice... I have no control when I'm around Axel."
                        $ flena = "sad"
                    l_p "{i}I don't think it's a good idea. We've both turned the page, so we should go our separate ways.{/i}"

            x_p "{i}Is this because of Cherry? I know how things looked, but I assure you it's nothing like that.{/i}"
            menu:
                "What's your relationship with her?":
                    $ renpy.block_rollback()
                    $ v11_axel_cherry = True
                    $ flena = "worried"
                    l "I have been wondering about that..."
                    $ flena = "serious"
                    l "I don't believe I can trust him, but let's see what he has to say."
                    l_p "{i}So what were you doing with her that night?{/i}"
                    x_p "{i}We ran into each other, same as with you. She had gone out with some friends, and we met at a nearby bar shortly before leaving.{/i}"
                    x_p "{i}She was going to take a taxi home, and I was headed to my apartment.{/i}"
                    l "That sounds awfully weak as far as excuses go... I don't think he's telling me the entire truth."
                    l_p "{i}You two seemed to be having a lot of fun.{/i}"
                    x_p "{i}We were both tipsy... We get along, but our relationship has been strictly friendly since then.{/i}"
                    l_p "{i}I'll have to take your word for it.{/i}"
                    x_p "{i}It's the truth. Nothing's happened between us since you and I broke up, even though Cherry would've liked to.{/i}"
                    $ flena = "worried"
                    x_p "{i}But I'm past that. I don't want to make the same mistakes from the past. And she was a mistake.{/i}"
                    if lena_axel_desire:
                        call friend_xp ('axel') from _call_friend_xp_959
                        l "Maybe he's being honest...? There's no way for me to tell..."
                    else:
                        $ flena = "serious"
                        l "I shouldn't trust him... He could be lying for all I know."
                    l "I guess it's better to just forget about it. I shouldn't concern myself with it anymore."
                    if lena_axel_dating:
                        $ flena = "n"
                        l_p "{i}Anyway, let me know when you have news from the agency in autumn.{/i}"
                        x_p "{i}Sure. I'll keep you posted then, and you can always write to me if you need something or have any questions.{/i}"
                    else:
                        $ flena = "serious"
                        l_p "{i}Anyway, that's all I wanted to know. You don't need to push me to the agency anymore, I'm fine as it is.{/i}"
                        x_p "{i}Alright, I won't insist then. But if you happen to change your mind, you can always let me know. I'd still like to lend you a hand.{/i}"

                "I don't wanna hear it":
                    $ renpy.block_rollback()
                    $ flena = "serious"
                    l_p "{i}I don't wanna hear it. I don't care about Cherry or your relationship with her, so save your explanations for someone who does.{/i}"
                    if lena_axel > 0:
                        call friend_xp ('axel',-1) from _call_friend_xp_960
                    if lena_axel_dating:
                        x_p "{i}Alright, as you wish...{/i}"
                        x_p "{i}I'll keep you posted about the agency in any case, and you can always write to me if you need something or have any questions.{/i}"
                    else:
                        x_p "{i}Alright, then. I won't insist on the modeling stuff either...{/i}"
                        x_p "{i}But if you happen to change your mind, you can always let me know. I'd still like to lend you a hand.{/i}"

                "{image=icon_mad.webp}I don't give a fuck" if lena_axel_dating == False:
                    $ renpy.block_rollback()
                    $ flena = "serious"
                    l_p "{i}I don't give a flying fuck about Cherry or your relationship with her, so save your excuses for someone who does.{/i}"
                    if lena_axel > 0:
                        call friend_xp ('axel',-1) from _call_friend_xp_961
                    x_p "{i}Alright, then. I won't insist on the modeling stuff either...{/i}"
                    x_p "{i}But if you happen to change your mind, you can always let me know. I'd still like to lend you a hand.{/i}"

            $ flena = "sad"
            l_p "{i}Just one more thing... Out of curiosity, who was the girl that was picked by the agency? Was it Ivy?{/i}"
            x_p "{i}No, it's the other girl I've been working with. Cindy.{/i}"
            $ flena = "worried"
            l_p "{i}I see... Ivy won't be happy about it.{/i}"
            x_p "{i}I tried to get her in, but her style isn't quite what Wildcats is after. They're looking for something more... sophisticated.{/i}"
            if lena_axel_dating:
                l_p "{i}Well, good luck breaking the news to her. Bye, Axel. Take care.{/i}"
                x_p "{i}Talk to you soon.{/i}"
            else:
                l_p "{i}Well, good luck breaking the news to her. Bye, Axel.{/i}"
                l "It's a shame to lose this chance, but... This is the right move. Being around Axel can bring only trouble."
            $ flena = "n"
            $ config.menu_include_disabled = False
            $ greyed_out_disabled = True
            if v11sms_mom and v11sms_emma and v11sms_mike and v11sms_mark and v11sms_photo and v11sms_axel:
                jump v11prologuefrdy2
            jump v11prologuesms

        "Seymour's message" if lena_seymour_dating and v11sms_photo == False:
            $ renpy.block_rollback()
            $ v11sms_photo = True
            nvl clear
            s_p "{i}Hello, Lena. I'd like to schedule another photo shoot this weekend.{/i}"
            $ flena = "sad"
            s_p "{i}I can give you Saturday at one or Sunday at eight. Which works better for you?{/i}"
            l "This is a problem..."
            if seymour_disposition > 1:
                $ flena = "worried"
                l_p "{i}I'm really sorry, but this weekend I have to be at my parent's place. I already told them and they're counting on me.{/i}"
                "I knew he wouldn't like that, since part of our deal was for me to be available at all times. And he was even giving me options to choose from..."
                "I hoped he wouldn't consider it a breach of contract."
            else:
                l_p "{i}I'm sorry, but I can't this weekend. I have family issues to take care of and I can't possibly cancel.{/i}"
                "I knew he wouldn't like that, since part of our deal was for me to be available at all times..."
                $ flena = "serious"
                "But there was no way he expected it to really be that way, right?"
            play sound "sfx/sms.mp3"
            s_p "{i}I understand. Don't worry, take care of your matters. Family comes first.{/i}"
            $ flena = "sad"
            s_p "{i}We'll try to schedule it for next week. I want us to dispose of ample time for our next session.{/i}"
            if seymour_disposition > 1:
                $ flena = "smile"
                l_p "{i}Thank you, Mr. Ward. I really appreciate it.{/i}"
                s_p "{i}You don't need to apologize nor call me Mr. Ward, I thought we had already established that.{/i}"
                s_p "{i}I'll get in touch soon. Relax and don't worry, you don't need to anymore.{/i}"
            else:
                $ flena = "n"
                l_p "{i}Thanks, I appreciate it.{/i}"
                s_p "{i}I'll get in touch soon. Take care and don't worry, you don't need to anymore.{/i}"
                $ flena = "sad"
                l "..."
                if seymour_disposition == 1:
                    l "I'm surprised at how understanding he was. Maybe he's not always so cold and controlling."
                else:
                    l "I'm surprised at how understanding he was. Maybe he's not always a creepy psycho..."
            $ flena = "n"
            if v11sms_mom and v11sms_emma and v11sms_mike and v11sms_mark and v11sms_photo and v11sms_axel:
                jump v11prologuefrdy2
            jump v11prologuesms

        "Kent's message" if lena_seymour_dating == False and v11sms_photo == False:
            $ renpy.block_rollback()
            $ v11sms_photo = True
            nvl clear
            kent_p "{i}Hey there, Lena! I'm sending you the results of our shoot. I'm so pleased with them!{/i}"
            show lenaunder at right with move
            show v11_kent
            if lena_tattoo2:
                show v11_kent_t2
            with short
            kent_p "{i}I've edited my favorite pictures. What do you think?{/i}"
            $ flena = "smile"
            l_p "{i}They're great! I'm glad you like them too.{/i}"
            play sound "sfx/sms.mp3"
            kent_p "{i}I'll be visiting Baluart sometime soon, and I'd love to work with you again.{/i}"
            l_p "{i}Of course! Give me a call when you're in the city and we can schedule another shoot.{/i}"
            kent_p "{i}Perfect! I have to really thank Danny for introducing us {image=emoji_wink.webp}{/i}"
            hide v11_kent
            hide v11_kent_t2
            with short
            show lenaunder at truecenter with move
            l "This is good news... Seems like there's someone who's still willing to hire me as a model."
            $ flena = "sad"
            l "Too bad he's not from here... But it seems he'll actually call me when he's in the city."
            if v11sms_mom and v11sms_emma and v11sms_mike and v11sms_mark and v11sms_photo and v11sms_axel:
                jump v11prologuefrdy2
            jump v11prologuesms

label v11prologuefrdy2:
    if persistent.include_disabled:
        $ config.menu_include_disabled = True
    $ greyed_out_disabled = False
    if v10_ian_left:
        $ flena = "sad"
        l "No messages from Ian, though... I guess he doesn't want to talk to me at all."
        l "But I'll probably see him this afternoon... I wonder how will that go."
    if lena_axel_dating == False and lena_axel_over == False:
        $ flena = "serious"
        l "At least Axel hasn't tried contacting me again after we ran on each other that last night..."
        l "That was sheer bad luck. The further away he stays from me, the better..."
        $ flena = "worried"
        l "He was with Cherry, though... I wonder what's going on there. Are they...?"
        $ flena = "serious"
        l "Why do I even care? Those two deserve each other. As long as they leave me out of it, they can do whatever the hell they want."
        l "I've paid them enough thought as it is. Good riddance."
    if ian_lena_dating:
        show lenaunder at rig with move
        play sound "sfx/door.mp3"
        $ fian = "smile"
        $ ian_look = 2
        show ian at lef with short
        i "Done."
        $ flena = "smile"
        i "Hey, that's my shirt you're wearing... I was wondering where I had left it!"
        $ flena = "happy"
        l "Yes, it's officially mine now!"
        $ fian = "happy"
        i "It's okay, you can have it... It looks better on you anyway."
        $ flena = "smile"
        l "Want some breakfast? Or coffee?"
        $ fian = "smile"
        if ian_lena_sex and ian_cuck < 2:
            i "Sure."
            scene lenahome with long
            show ian at lef
            show lenaunder at rig
            with short
            if ian_lena_couple:
                l "Mhhh... I had such a good night's sleep. Getting an orgasm before bedtime is the best, especially if it's you who's giving it to me."
                l "I should bring you over every night..."
            elif lena_ian_love == 2:
                i "Did you have a good night's sleep?"
                l "Yeah... Sex before before bedtime is the best, especially if it's you..."
                $ fian = "n"
                i "Yeah..."
                l "Too bad I don't have you in my bed every night."
                $ fian = "smile"
            else:
                l "Mhhh... I had such a good night's sleep. Getting an orgasm before bedtime is the best. I should bring you over every night..."
            $ fian = "happy"
            i "I'm happy you're satisfied with my services, ha ha!"
            $ flena = "flirtshy"
            l "What about you? Are you satisfied with mine?"
            $ fian = "confident"
            i "Do you even need to ask? I'm sure you can easily tell how much I enjoy them..."
            if lena_lust < 6 or ian_lena_couple and ian_lena_open == False:
                $ flena = "blush"
            l "I do... But I wonder..."
            l "What about your sexual fantasy...?"
            $ fian = "n"
            i "My sexual fantasy?"
            l "You told us about it at Ivy's party. About the threesome."
            if ian_chad < 3 or ian_lena_open:
                $ fian = "smile"
            i "Oh, that... Well, as I said, it's a fantasy almost every straight guy has. Does it bother you?"
            if lena_lust > 5 or ian_lena_open:
                menu:
                    "{image=icon_lust.webp}We could give it a try":
                        $ renpy.block_rollback()
                        label v11lena3someproposal:
                            $ ian_lena_3some = True
                        $ flena = "shy"
                        l "Actually... I was thinking we could give a try."
                        $ fian = "shy"
                        i "What, really?"
                        if ian_lena_couple:
                            if ian_lena_open:   
                                l "I mean... We said we'd like to experiment together, right?"
                                $ fian = "smile"
                                i "That's true, we said something like that..."
                            else:
                                $ ian_lena_open = True
                                l "I mean... Why not? I think we could both experiment, as long as we do it together..."
                                i "Well, I'm not against that..."
                        else: 
                            l "Sure, why not? It should be simple enough, right? No strings attached and all that."
                            l "If we both want to..."
                            $ fian = "smile"
                            i "Of course... I'm not against that."

                    "Not my thing":
                        $ renpy.block_rollback()
                        label v11lena3someproposalno:
                            $ flena = "sad"
                        l "Honestly... I don't think it's my thing."
                        $ fian = "n"
                        if lena_fty_3some == 1:
                            i "Really? But didn't you have the same fantasy...?"
                            $ flena = "n"
                            l "It's just that, a fantasy... It would be weird doing something like that with you..."
                        elif lena_fty_lesbo:
                            i "Really? But didn't you say you are attracted to girls, too?"
                            $ flena = "n"
                            l "Yeah, but that doesn't mean I want to have a threesome... It would be weird doing something like that with you."
                        i "Oh. I see."
                        if lena_fty_3some == 2:
                            $ flena = "flirtshy"
                            l "But if it were you and another guy..."
                            if alison_jeremy_3some:
                                $ fian = "blush"
                            else:
                                $ fian = "disgusted"
                            i "What, really?"
                            l "It's just a thought..."
                            i "..."
            else:
                menu:
                    "{image=icon_will.webp}We could give it a try" if lena_will > 0:
                        $ renpy.block_rollback()
                        call willdown from _call_willdown_47
                        jump v11lena3someproposal
                    "Not my thing":
                        $ renpy.block_rollback()
                        jump v11lena3someproposalno

            $ fian = "sad"
            i "Whoops, I'm late! I have to get going."
        else:
            i "Don't worry, I'll get some at the office. Gotta go."
        l "Alright, see you this afternoon."
        $ fian = "smile"
        i "Yes. See you later, beautiful."
        "Ian kissed me goodbye and went on his way to work."
        play sound "sfx/door.mp3"
        hide ian with short
    $ flena = "n"
    l "Well, time to get the day started..."
    stop music fadeout 4.0
    if cafe_help:
        l "Today's bound to be even busier than yesterday at the café!"
        if lena_seymour_dating:
            $ flena = "sad"
            l "I almost wish I could stay at home, play guitar or maybe go to the gym..."
            $ flena = "n"
            l "I might not need the money, but Ed and Molly need my help."
        scene street with long
        scene cafe with long
        "As I expected, we got quite a lot of customers that day, at least compared to the time I had started working there."
        if cafe_music: #findme - issue 256
            "People seemed happy to be there, and some asked when would our next life drawing event be, or if I would play at the café again."
        else:
            "People seemed happy to be there, and some asked when would our next life drawing event be, or when I would play or perform again."
        if lena_charisma < 10:
            call xp_up ('charisma') from _call_xp_up_765
        "And Ed and Molly, even though a bit tired, looked quite happy with the situation too."
    else:
        if lena_seymour_dating:
            l "What should I do today? It's great to have time for myself for a change..."
        else:
            $ flena = "sad"
            l "I should keep searching for a job, but I've already sent many resumes this week..."
            l "I guess I can keep doing that for a bit, and then I could..."
        menu:
            "Watch TV":
                $ renpy.block_rollback()
                $ flena = "happy"
                l "I know, I'll watch that new show! It seemed interesting."
                scene lenahome with long
                "I spent the morning relaxing on the couch, watching TV and playing with my phone."
                "I really needed to do nothing at all, and it felt great."

            "Play guitar":
                $ renpy.block_rollback()
                $ flena = "happy"
                l "I'll practice a bit. I have been learning some new songs, and I also have to work on my own..."
                scene lenaroom with long
                play sound "sfx/guitar_long.mp3"
                "I spent the morning playing guitar, writing, and watching some online tutorials."
                if lena_wits < 10:
                    call xp_up ('wits') from _call_xp_up_766
                "It was great having some extra time to practice my craft."

            "Go to the gym":
                $ renpy.block_rollback()
                $ flena = "smile"
                l "I know, I'll drop by the gym. I barely had the time or energy to go, recently. I need to stay in shape!"
                scene street with long
                scene polegym 
                show v2_gym
                if lena_tattoo2:
                    show v2_gym_t2
                if lena_tattoo3:
                    show v2_gym_t3 
                if lena_piercing1:
                    show v2_gym_p1
                elif lena_piercing2:
                    show v2_gym_p2
                with long
                "I got a good workout in: squats, hip thrusts, ab crunches, and even some push-ups..."
                if lena_athletics < 10:
                    call xp_up ('athletics') from _call_xp_up_767
                    if lena_athletics < 10:
                        call xp_up ('athletics') from _call_xp_up_772
                "After taking a shower and having a good meal, I felt relaxed and invigorated."

# EMMA EVENT ########################################################################################################################################################################################################################
########################################################################################################################################################################################################################
    stop music fadeout 3.0
    "When it was time to go to the civic center, I packed my luggage and grabbed my guitar."
    $ perry_look = 1
    $ emma_look = 1
    $ lena_look = 4
    $ ian_look = 2
    $ fholly = "happy"
    $ fian = "smile"
    $ flena = "smile"
    scene ccentre with long
    play music "music/summer_day.mp3" loop
# holly
    if ian_holly_dating:
        "Once there, I saw quite a few people crowding around the entrance to the building. I spotted Ian and Holly amongst them."
        show lena at rig3
        show holly
        show ian at lef3
        with short
        l "Hi, guys."
        i "Hey there. Holly told me you were coming today too."
        l "Emma invited me. It seems this is quite a big deal..."
        show lena at right
        show holly at rig
        show ian at lef
        with move
        show emma at left with short
    else:
        "Once there, I saw quite a few people crowding around the entrance to the building. Holly was waiting for me in the corner."
        show lena at rig
        show holly at lef
        with short
        h "Hi!"
        h "Wow, this place is so nice! I was totally unaware of the push for this project, but it was really cool."
        show lena at rig3
        show holly at truecenter
        with move
        show emma at lef3 with short
# emma
    e "It is! We've been working hard to set this up!"
    e "We're still not done yet, but we're getting there... We want this place to serve as a meeting point for the neighbors and a hub for the assembly."
    if ian_holly_dating:
        i "How come we didn't hear about this until now? You've been keeping it under wraps."
    else:
        l "How come we didn't hear about this until now?"
    $ femma = "n"
    e "We didn't want to attract too much attention before we had everything ready, you see... To avoid unwanted interference."
    h "So this place used to be a cinema?"
    e "Yeah, one of the oldest in town. It went out of business a few years ago, and the building remained boarded up until we decided to reform it."
    l "So the assembly owns it?"
    e "Not quite... It was in the hands of a bank, but they weren't doing anything with it."
    e "The idea to use the space as a civic center for the neighborhood has been on people's minds for quite some time, but the city council never took the necessary steps."
    h "Oh, so you finally got permission?"
    $ femma = "smile"
    e "Nope. We just took matters into our own hands!"
    $ fholly = "sad"
    $ fian = "n"
    $ flena = "sad"
    l "So you're... illegally occupying the building?"
    e "Depending on who you ask!"
    $ fwade = "smile"
    $ fperry = "n"
    if v10_ian_left:
        $ fian = "n"
    if ian_holly_dating:
        show ian at rig3
        show lena at right5
        show holly at rig
        show emma at truecenter
        with move
        show wade at left
        show perry at lef3
        with short
    else:
        show lena at right5
        show holly at rig3
        show emma at rig
        with move
        if ian_cindy_dating == False:
            show ian at lef3
        show perry at lef
        show wade at left
        with short
    w "Hey guys."
    e "Thanks for coming!"
    p "Of course. This is a b--{w=0.5}big deal, isn't it?"
    $ femma = "n"
    e "It is... Did you talk to your father? Is he gonna make it?"
    $ fperry = "meh"
    hide perry
    if ian_holly_dating:
        show perry2 at lef3
    else:
        show perry2 at lef
    with short
    p "Yes... He s--{w=0.5}said he'd come by."
    $ femma = "smile"
    e "That's great! Thank you, Perry!"
    "Emma hugged him, but Perry looked a bit uncomfortable."
    p "You didn't need me to c--{w=0.5}convince him... He already received the assembly's r--{w=0.5}request."
    $ femma = "n"
    e "I just wanted to make sure. This is important!"
    if ian_cindy_dating:
        e "Where's Ian, by the way? Wasn't he coming with you?"
        $ fperry = "n"
        p "I don't know. He s--{w=0.5}said he'd be here..."
        if v10_perry_lie == 3:
            p "He should be here any minute, I guess."
        elif v10_perry_lie > 0:
            $ fperry = "meh"
            p "I have the impression he's been acting w--{w=0.5}weird lately..."
            $ femma = "sad"
            $ flena = "sad"
            $ fholly = "n"
            e "Weird how?"
            p "I can't put my finger on it. It's just a h--{w=0.5}hunch."
            w "Whatever you say. You know him best, after all."
        elif v10_perry_lie == 0:
            $ fperry = "serious"
            p "He's been acting w--{w=0.5}weird lately..."
            $ femma = "sad"
            $ flena = "sad"
            $ fholly = "n"
            e "Weird how?"
            p "I don't know... Like he's h--{w=0.5}hiding something from us..."
            l "Hiding something?"
            w "If you say so... You know him best; you live with him after all."
            $ fperry = "meh"
            p "Yeah, I c--{w=0.5}can't put my finger on it, but he's d--{w=0.5}definetly up to something."
        if v10_ian_left:
            "Maybe I wouldn't meet Ian today, after all..."
            "I wasn't sure if that was good or bad news. I had already made up my mind and wanted to talk to him, see if we could smooth things out..."
        elif ian_lena_dating:
            $ flena = "n"
            l "We agreed to meet this afternoon... I'm sure he'll be here shortly."
    elif v10_ian_left:
        $ flena = "sad"
        "I looked over to Ian. Our eyes met, but he averted his gaze, uncomfortable."
        "I was feeling the same... But I had already made up my mind: I wanted to talk to him, see if we could smooth things out..."
        "I would need to wait for the right moment, though."
    $ femma = "smile"
    e "We have a bar, so go ahead and grab a beer! The event will begin shortly, we're almost ready."
    $ flena = "n"
    $ fholly = "smile"
    $ fperry = "n"
    hide perry2
    if ian_holly_dating:
        show perry at lef3
    else:
        show perry at lef
    with short
    l "Thanks."
    e "I'll be around here if you need me!"
    hide emma with short
# invitation
    p "Let's go grab that b--{w=0.5}beer!"
    if ian_cindy_dating:
        show wade at left
        show perry at lef
        show holly at rig
        show lena at right
        with move
    else:
        show wade at left
        show perry at lef2
        show holly at rig2
        show lena at right
        show ian at truecenter behind perry, holly
        with move
    if v10_ian_left and ian_cindy_dating == False:
        $ flena = "sad"
        "The unspoken tension between Ian and me turned into an awkward silence I felt obligated to break."
        "I said the first thing that came to my mind."
    l "Hm... So Cindy's not coming today?"
# cindy
    if wade_cindy == 2:
        $ fian = "n"
        $ fperry = "meh"
        $ fwade = "n"
        w "Cindy and I are... taking some time apart."
        $ flena = "sad"
        l "Oh. Sorry, I didn't know... I thought things were cool between you."
        w "They're not too bad, but we just haven't been seeing eye to eye lately."
        w "I've been trying to make things work, and my friends here have been trying to help out..."
        w "But she said she's feeling... what were her words?"
        i "Disoriented and unfulfilled."
        w "Yeah, that. So she wanted us to spend some time apart, so she can think and put her feelings in order or something like that."
    else:
        $ fian = "sad"
        $ fperry = "sad"
        $ fwade = "sad"
        w "Cindy and I are... taking a break from our relationship."
        $ flena = "worried"
        l "A break?"
        w "Yeah, something like that... She wanted us to spend some time apart, and I'm cool with it."
        if wade_cindy == 1:
            l "Oh, I see... I thought things were cool between you..."
            $ fwade = "n"
            w "I've been trying, but we just haven't been seeing eye to eye lately. She says she's feeling... What were her words?"
        else:
            $ fwade = "serious"
            w "She's been rather unbearable lately. She always has something to complain about, something that's not up to her standards."
            w "Honestly, I'm fed up. I can't relax at all around her. I hope this makes her calm down a bit."
            w "I'm tired of hearing her talk about how unsatisfied she is."
            l "Did she use that word?"
            $ fwade = "n"
            w "Hm... Not that word. Something like..."
        $ fholly = "sad"
        w "Disoriented and unfulfilled? Yeah, something like that."
    menu:
        "I hope things work out":
            $ renpy.block_rollback()
            $ flena = "n"
            l "Well, I hope you find a way to make things work..."
            if wade_cindy == 2 and lena_charisma < 10:
                call xp_up ('charisma') from _call_xp_up_768
            $ fwade = "n"
            w "I'd say the ball's on Cindy's court right now, though, so there's not much I can do."
            h "Probably giving her the space she asked for is the best option..."
            w "Yeah. And I needed some space too, honestly. I feel I can relax a bit now."
            $ fperry = "n"
            p "Let's ch--{w=0.5}change the subject then, shall we?"

        "It doesn't look good...":
            $ renpy.block_rollback()
            $ flena = "sad"
            l "The way you're talking about it, it doesn't sound good..."
            $ fwade = "sad"
            w "It doesn't?"
            l "Well, when someone uses the words {i}disoriented{/i} and {i}unfulfilled{/i}... It's clear she's having doubts about many things in her life. Your relationship included."
            if wade_cindy < 2 and lena_wits < 10:
                call xp_up ('wits') from _call_xp_up_769
            l "By the looks of it, she's trying to figure out which way she wants to go forward, which things to pursue, and which to leave behind."
            w "So you think she's decided to leave me behind?"
            l "I think she's considering it..."
            w "Fuck. What should I do?"
            h "I, um... I agree with what Lena said, but I don't believe she's come to a decision yet."
            h "That's why she asked to take some time off, to consider things..."
            w "..."
            p "Can we t--{w=0.5}talk about something else? You girls are b--{w=0.5}bumming Wade out."
            $ fwade = "n"
            w "Yeah..."

        "{image=icon_mad.webp}She's more trouble than she's worth" if lena_cindy < 3:
            $ renpy.block_rollback()
            $ flena = "serious"
            l "Ugh, she sounds like a real drag... More trouble than she's worth."
            $ fwade = "sad"
            $ fperry = "sad"
            $ fian = "sad"
            $ fholly = "sad"
            w "You think so?"
            $ flena = "n"
            l "The way you're talking about it... It sounds like you're really fed up with her."
            if wade_cindy < 2 and lena_wits < 10:
                call xp_up ('wits') from _call_xp_up_770
            l "Why do you keep dating her?"
            $ fwade = "n"
            w "Well, because... She has her problems, but in the end I really like her. And she's my girl."
            l "And what do you like about her, exactly?"
            w "Well, I..."
            l "Sorry, I think I'm prying too much into it. You do whatever you feel is right."
            p "Y--{w=0.5}yeah, let's talk about s--{w=0.5}something else..."
            w "..."
    $ fian = "n"
    $ flena = "n"
    $ fholly = "n"
    $ fperry = "n"
    $ fwade = "n"
    if ian_cindy_dating:
        show wade at left
        show perry at lef2
        show holly at rig2
        show lena at right
        with move
        show ian at truecenter behind perry, holly
        with short
        if v10_ian_left:
            $ flena = "sad"
        i "Hey, hi guys..."
        $ fperry = "meh"
        p "T--{w=0.5}there you are. What took you so l--{w=0.5}long?"
        if v10_perry_lie > 1:
            i "I had some work I needed to finish before the weekend. Did I miss anything?"
            $ fperry = "n"
            p "Not r--{w=0.5}really..."
            $ fperry = "happy"
            p "By the way guys, do you have p--{w=0.5}plans for August?"
        else:
            i "I had some stuff to take care of."
            p "S--{w=0.5}stuff? What kind of s--{w=0.5}stuff?"
            $ fian = "serious"
            if v10_perry_lie == 1:
                i "Work stuff. Some of us have a job, you know?"
                p "Whatever."
            else:
                i "Stuff. Why do you care?"
                $ fperry = "serious"
                p "Why are you being so v--{w=0.5}vague about it?"
                i "Because I don't owe you any explanations about what I do with my life."
            w "Don't fight, guys. Why don't you tell them about the beach house, Perry?"
            $ fperry = "happy"
            p "Oh, y--{w=0.5}yeah. Do you guys have p--{w=0.5}plans for August?"
            $ fian = "n"
    else:
        $ fperry = "happy"
        p "By the way guys, do you have p--{w=0.5}plans for August?"
    l "Plans? Not really... Why?"
    p "My p--{w=0.5}parents have a house on the beachside that we r--{w=0.5}rent during the year, but we get to go during s--{w=0.5}summer."
    p "My father is really b--{w=0.5}busy with the election being in October and all, so he p--{w=0.5}plans to only go there a couple weekends, if at all."
    p "Which m--{w=0.5}means we get to use it if we want. We could s--{w=0.5}spend one or two weeks chilling there during August..."
    $ fwade = "happy"
    w "Oh, you brought me there like four or five summers ago. It was really cool."
    p "Are you in t--{w=0.5}too, Ian?"
    i "Sure, I guess... I'm working, but I can take a few days off to go on vacation or something."
    p "What about you, g--{w=0.5}girls? Do you wanna come?"
    $ fholly = "blush"
    if ian_lena_breakup:
        $ flena = "sad"
    h "Um, really? I'm invited to go?"
    if ian_holly_dating:
        $ fian = "smile"
        i "Of course you're invited. And I hope you accept."
        $ fholly = "shy"
        hide holly
        show holly3 at rig2
        with short
        p "The house is pretty b--{w=0.5}big, and it'll be more fun if we get a bunch of people t--{w=0.5}together."
        h "Alright then... I'd love to go."
    else:
        $ fperry = "n"
        p "Sure, why not?"
        h "I wouldn't want you to feel forced to..."
        p "What? Not at all. The house is pretty b--{w=0.5}big, and it'll be more fun if we get a bunch of people t--{w=0.5}together."
        $ fholly = "shy"
        hide holly
        show holly3 at rig2
        with short
        h "Well, in that case... I'd like to accept the invitation."
    p "C--{w=0.5}cool. And you, Lena?"
    if ian_lena_breakup:
        if v10_ian_left:
            $ fian = "worried"
            $ flena = "worried"
            "I looked over to Ian. I couldn't see myself spending a week under the same roof with him after our recent breakup..."
            l "I... I'll think about it. Things are a bit chaotic right now and I'm not sure how free I'll be next month."
        else:
            $ fian = "n"
            "I looked over to Ian. I wasn't sure he'd be okay with me tagging along..."
            l "I'll think about it. Things are a bit chaotic right now and I'm not sure how free I'll be next month."
    else:
        l "I'd like to tag along too, if I can. Things are a bit chaotic right now and I'm not sure how free I'll be next month."
        if ian_lena_dating:
            i "It would be a real shame if you couldn't make it..."
# mayor
    $ fholly = "worried"
    $ fperry = "n"
    $ flena = "n"
    show wade at left
    show perry at lef2
    show holly3 at rig3
    show lena at right5
    show ian at rig behind perry, holly
    with move
    hide holly3
    show holly at rig3
    show mayor with short
    mayor "Good afternoon, youngsters."
    p "Hi, Dad... I was just t--{w=0.5}telling them about the beach house."
    mayor "Oh, of course. You're welcome to use it while me and my wife are in Baluart."
    $ fwade = "smile"
    w "Thank you, Mr. Mayor."
    mayor "Oh, come on, drop the formalities with me, Wade."
    $ fwade = "happy"
    w "The title still impresses me."
    $ fian = "smile"
    i "Emma was waiting for you. She's over there..."
    mayor "Alright. Let's get down to business."
    hide mayor with short
    $ fwade = "n"
    $ fholly = "surprise"
    h "You're the mayor's son?"
    $ fperry = "meh"
    w "You would've never guessed, right? Ha ha ha."
    $ fholly = "sad"
    hide perry
    show perry2 at lef2
    with short
    p "W--{w=0.5}why would she have to? That's my father's j--{w=0.5}job, it has nothing to do with me."
    l "You surely get some perks out of it..."
    p "No, and n--{w=0.5}never have! My father is very... s--{w=0.5}stern with these things."
    $ fholly = "n"
    h "I always heard Mayor Vermeer was an honest and humble man."
    p "Can we stop t--{w=0.5}talking about my father's job? It's quite a b--{w=0.5}boring subject."
    $ holly = "blush"
    h "I'm sorry... I didn't mean to offend you."
    $ fian = "smile"
    i "He gets grumpy because he doesn't want to be associated with anything political."
    $ fholly = "sad"
    $ fwade = "happy"
    w "Yeah, that's too much trouble for him and would interfere with his hands-off approach to life."
    $ fholly = "n"
    p "Shut up. Look, Emma's about to give a speech."
    $ femma = "n"
# emma speech
    scene ccentre with long
    show emma with long
    e "Thanks everyone for coming today! The folks in the popular assembly have been working hard on this project for a while now, and now it's time for you to participate too!"
    e "Many of you knew this movie theater, and even used to come here before it was driven out of business by the new multiplex cinemas."
    e "It's always a shame when a place so deeply rooted in this city disappears, and it's even worse when it's turned into yet another mall, night club or fancy restaurant."
    e "We have enough of those already! What we need are spaces rooted in the community, places to foster culture, and social projects to better our hometown."
    e "That's why we decided to reclaim this place before the bank sells it to some soulless investor and degrades this neighborhood further."
    e "And we even have Mayor Vermeer here today. I was hoping that he would say a few words in support of this initiative..."
    show emma at rig with move
    show mayor at lef with short
    mayor "Of course... First of all, let me say I admire the spirit that drives the initiative, and how the community has come together to carry it out."
    mayor "My party and I share your sentiment about preserving Baluart's heritage and understand the importance of fostering and protecting spaces that enhance and enrich the lives of its citizens."
    mayor "But we must be aware that this initiative, although very laudable, is of dubious legality, and the bank will demand to recover this space that is now its property."
    $ femma = "sad"
    mayor "To prevent the law from being broken and the conflicts that go with it, it is necessary to do things by the book and follow the appropriate legal routes..."
    e "Mayor Vermeer! What is of dubious legality are the bad business practices that are eroding the city!"
    mayor "I know, I know. And I'm here to lend you my support."
    mayor "I undertake to negotiate with the bank the acquisition of this cinema so that it can become part of the public facilities. In this way, it can continue to be used as a cultural center."
    $ femma = "n"
    "Perry's dad's words were met with a round of applause."
    mayor "However, the negotiation will take time, and in that time I ask you to maintain a civic and responsible attitude, of which, moreover, I have no doubt."
    mayor "I am proud to be a witness to the spirit of my fellow citizens, and I hope to be able to live up to your expectations."
    hide mayor with short
    "The mayor stepped down from the scenario with another round of applause. It seemed he was quite liked by the crowd..."
    "His speech was very politician-like, but his words sounded honest."
    show emma at truecenter with move
    $ femma = "smile"
    e "So, there you have it! And to celebrate we're giving way to our open mic! Anyone who wishes to share something with us can take the stage now!"
    e "Let's have fun and make this a city we can be proud of!"
    e "Lena, do you want to open it for us?"
    if lena_charisma > 6:
        $ flena = "smile"
    else:
        $ flena = "shy"
    show lena2 at rig3 with short
    if lena_charisma > 6:
        l "Sure! No problem."
    else:
        l "Do I really have to go first?"
        e "Oh, come on! You're the best musician we have here today, don't play coy!"
        l "Alright, alright..."
    e "Ladies and gentlemen, if you don't know her already, here's Lena and her amazing voice!"
    hide emma with short
    show lena2 at truecenter with move
    play sound "sfx/guitar.mp3"
    scene ccentre with long
# cameo
    "I only intended to play a couple songs, but the crowd asked for more and I ended up playing for fifteen minutes or more."
    "After that other people took the stage. They recited poems, read texts, told jokes, and sang other songs."
    "There really was a sense of community, something increasingly rare in cities like Baluart. I could see why Emma felt so strongly about this..."
    $ flena = "n"
    show lena with short
    "Some people approached me to congratulate me for my performance, or just to chat for a while. I saw two familiar faces walking over."
    show record_owner at lef3
    show stefan at rig3
    with short
    "Martin, the owner of the record store, and Stefan, the owner of the Fortress."
    martin "That was great, Lena. Maybe your best performance yet."
    if (lena_will < 3 and lena_song1 > 4 and lena_song2 > 4) or (lena_will < 3 and lena_wits > 6 and lena_charisma > 6):
        call will_up() from _call_will_up_63
    $ flena = "shy"
    l "What, really?"
    martin "You're getting way more relaxed, I could tell."
    $ flena = "smile"
    l "I guess I'm getting the hang of it after all..."
    stefan "And I never heard a version of \"Stairway to Hell\" that sounded like yours. I loved it."
    martin "Listen to him, he knows as much about music as he does about beer!"
    stefan "By which I guess you mean \"a lot\"."
    show record_owner at left
    show stefan at right
    with move
    show perry at rig
    show wade at lef2
    with short
    p "The craft b--{w=0.5}beers you recommend to me are always the best."
    stefan "Then you'll love the black stout I got from Ireland just today. I was planning to crack it open tonight."
    p "I want a p--{w=0.5}pint of that one!"
    martin "By the way, Lena. I don't know if you're aware, but the city's underground music circuit organizes an annual contest for bands and singer-songwriters."
    $ flena = "n"
    l "You mean the Sonar prize, right?"
    martin "So you've heard about it. Then you know it's a prestigious award and many of the winners have managed to make a living out of their music. Some even became pretty famous!"
    martin "Anyway, I'd be willing to nominate you if you're interested. You need to gather a certain amount of popular votes to enter, but seeing as you have some fans here already..."
    martin "I'm sure you'd have no problem getting enough votes to participate."
    menu:
        "Do you think I could win?":
            $ renpy.block_rollback()
            l "Do you think I could win it?"
            martin "I have no clue... I'd be really surprised if you did, to be frank!"
            $ flena = "sad"
            martin "The level of competition is top-notch! There are always a lot of talented bands and musicians, but you never know things might go down in each edition..."
            martin "But that's not the point, participating in the Sonar will surely be a great learning experience."

        "I'll think about it...":
            $ renpy.block_rollback()
            $ flena = "sad"
            l "I'm not sure, I'll have to think about it..."
            l "That level of competition is top-notch. I don't believe I am good enough."
            martin "You may be right about that, a lot of talented bands and musicians enter the contest each year. But you never know things might go down..."
            martin "Besides, it's not about winning the prize or not, participating in the Sonar can also be a great learning experience."

        "I have no chance of winning!":
            $ renpy.block_rollback()
            $ flena = "worried"
            l "But I have no chance of winning! I'm sure the bands entering are super talented."
            martin "You're right about that, the level of competition is top-notch!"
            martin "But it's not about winning the prize or not, participating in the Sonar can also be a great learning experience."

    stefan "And a great way to dig your feet deeper into the professional music circuit. That can't hurt."
    $ flena = "n"
    l "You two sound convincing... I'm starting to consider it."
    martin "Well, let me know when you've made your decision."
    stefan "Time for work! I'm headed to the bar, see you guys there?"
    p "Yes, save us a t--{w=0.5}table!"
    stefan "Of course. And the first round's on me today."
    $ fperry = "happy"
    hide stefan 
    hide record_owner
    with short
    $ femma = "n"
    show perry at right 
    show wade at left
    show lena at rig
    with move
    show emma at lef with short
    p "A--{w=0.5}awesome!"
    l "I have to get going, too. I need to take the last train."
    w "Yeah, I think we can all go. Seems some free beers are waiting for us at the Fortress."
    w "Are you coming, Emma?"
    e "I still need to take care off some things over here, but I'll join you later."
    w "Great. Where's Ian at?"
    e "He's busy talking to Holly, over there."
    p "Well, t--{w=0.5}tell him to meet us at the bar too!"
    w "Later!"
    hide wade
    hide perry
    with short
# end scene
    l "Hey, do you mind if I leave my guitar here? I'll come pick it up when I come back on Monday."
    e "Sure, no problem! Let me get it for you."
    l "Here."
    hide emma with short
    "I walked over to Ian and Holly to say my goodbyes."
    $ fian = "smile"
    $ fholly = "smile"
    show lena at rig3 with move
    if ian_holly_dating:
        if lena_go_holly > 3:
            $ fian = "sad"
            $ fholly = "blush"
        else:
            $ fholly = "happyshy"
    elif ian_lena_dating:
        if lena_go_holly > 3:
            $ fian = "sad"
            $ fholly = "blush"
        else:
            $ fholly = "n"
    else:
        if lena_holly_sex:
            $ fian = "blush"
            $ fholly = "blush"
        elif holly_gym:
            $ fholly = "blush"
            $ fian = "worried"
    show ian at lef3
    show holly
    with short
    l "I'm leaving, guys. I need to catch the last train."
    if ian_holly_dating or ian_lena_dating:
        if lena_go_holly > 3:
            h "Oh, um, yes..."
            $ fian = "n"
        i "We're leaving too. Do you want us to walk you to the station?"
    else:
        if lena_holly_sex:
            h "Oh, is it time already...?"
            $ fian = "n"
            i "I'll be on my way too. The guys already left, right?"
            h "Do you want me to walk you to the station?"
        elif holly_gym:
            i "Oh, alright..."
            h "I should leave too."
        else:
            h "It's gotten a bit late. I should get going too..."
            i "We're all going, then. Do you want us to walk you to the station?"
    if v10_ian_left:
        $ flena = "sad"
        l "Before that... I'd like to speak to you, Ian, if you're up to it..."
        $ fian = "n"
        $ fholly = "sad"
        i "Sure..."
        h "I'll be... waiting outside."
        stop music fadeout 4.0
        hide holly with short
        show ian at lef
        show lena at rig
        with move
        i "..."
        l "..."
        $ flena = "worried"
        l "So, um..."
        i "This is really awkward."
        $ flena = "sad"
        l "I know. I'm sorry. I just thought we could talk about how we're... handling the situation, since we haven't spoken since we, you know..."
        if ian_lena_couple:
            $ fian = "sad"
            i "Since we broke up?"
            l "Yeah..."
            i "What do you think? It sucks, really hard. You know I was serious about you."
            $ fian = "n"
            i "I can't really know if that was the case for you too, but it doesn't matter anyway."
            i "It's clear we're not on the same page... You see things one way and I see them another, and those are not compatible, it seems."
            l "Yeah, that's how it is..."
            i "So there's nothing else to talk about. I didn't want it to end this way, but better now than later, I guess..."
            l "That's true... But what about now? How do you want us to... handle ourselves?"
            i "I'm not gonna lie. What happened hurt me, and it's hard for me not to blame you."
            i "But I'm not asking you to disappear from my life and my circles... I will need some space, though."
            l "I feel the same..."
            i "We're on the same page about that, at least."
        else:
            i "Since we decided this thing wasn't working out?"
            l "Yeah..."
            i "Well... I didn't want it to end this way. It's not like we were seriously dating, but I'm not happy about what happened. It really sucked."
            i "But it's obvious you see things one way and I see them another, and those are not compatible, it seems."
            l "Yeah, that's how it is..."
            i "Then what else is there to say? We tried, but it failed. I don't want to dwell on it."
            l "And what about now? How do you want us to act around each other?"
            i "I don't know... Naturally I guess, even though that's kinda hard right now."
            l "We'll probably need some space... But if that way we can continue being friends..."
            i "It's possible."
        $ flena = "n"
        l "Thanks for talking it over with me... Let's not keep Holly waiting."
        $ fholly = "n"
    else:
        l "There's no need..."
        $ fian = "smile"
        i "Don't worry, it's on our way anyway."
        $ fholly = "smile"
        l "Let's go then."
# gillian
    stop music fadeout 4.0
    scene street2night with long
    show lena
    show holly at rig3
    show ian at lef3
    with short
    l "So how long do you have to wait to know the results of the contest?"
    i "The winners will be announced by the end of September."
    l "It's a long wait... What will you do now that you finally finished your book?"
    i "I'd like to say I'll start writing another one, but I feel spent after making it in time for the deadline."
    h "It happens to me too, it's normal... You always feel a bit exhausted after finishing a book, like you emptied yourself..."
    $ fholly = "smile"
    h "But soon new stories appear inside you and start pulling for your attention, for you to write them."
    $ fian = "smile"
    i "I hope so. Right now I'm out of good ideas. And I should probably focus on work right now..."
    if v5_ian_showup:
        i "I'm sure I'll get plenty of ideas checking all those manuscripts at work..."
    else:
        $ fian = "n"
        i "Or in finding one, rather. My internship at the magazine ends soon and..."
    $ fian = "disgusted"
    show lena at centerrig
    show holly at rig4
    with move
    $ flena = "worried"
    $ fholly = "sad"
    "Ian suddenly stopped in his tracks, frozen, with his eyes wide open."
    l "Is there something wrong...? {w=0.4}{nw}"
    hide lena
    hide holly 
    with short
    show ian at lef2 with move
    "Someone else was staring at him with a similar expression."
    $ fgillian = "sad"
    $ gillian_extras = "ring"
    $ gillian_look = 2
    show gillian at rig3 with long
    i "Gillian?"
    g "..."
    show gillian at rig2 with move
    g "Hello, Ian."
    scene black with long
    pause 1
    call label_chapter_title from _call_label_chapter_title_10
    jump chapter_eleven_ian


## screens #######################################################################################################################################################################################################################
screen v11louisetoys():
    imagebutton idle "v11_dildo1.webp" hover "v11_dildo1_hover.webp" focus_mask True action SetVariable("v11_louise_dildo", 1) , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    if toy_double:
        imagebutton idle "v11_dildo2.webp" hover "v11_dildo2_hover.webp" focus_mask True action SetVariable("v11_louise_dildo", 2) , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "v11_dildo2_block.webp"

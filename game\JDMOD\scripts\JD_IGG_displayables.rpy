init offset = 2






init python:

    JD_char_imageDir = "gui"
    JD_char_imagePrefix = "phone_text_icon_"
    JD_char_imageExt = "webp"
    JD_char_imagePlaceholder = "gui/phone_text_icon_unk.webp"
    for char in CHAR:
        if renpy.loadable("JDMOD/images/char/" + char + ".webp"):
            globals()["JD_char_image_{}".format(char)] = "JDMOD/images/char/{}.webp".format(char)
        
        elif renpy.loadable("{}/{}{}.{}".format(JD_char_imageDir, JD_char_imagePrefix, char, JD_char_imageExt)):
            globals()["JD_char_image_{}".format(char)] = "{}/{}{}.{}".format(JD_char_imageDir, JD_char_imagePrefix, char, JD_char_imageExt)
        else:
            globals()["JD_char_image_{}".format(char)] = JD_char_imagePlaceholder






init python:
    for char in CHAR:
        
        renpy.image (
            "JD_icon_{}".format(char), 
            Composite(
                (46, 31),
                (0, -8), im.Scale(eval("JD_char_image_{}".format(char)), 46, 46),
                (0, -8), im.Scale("JDMOD/images/char/border.webp", 46, 46)
            )
        )

    for route in CHAR:
        
        renpy.image (
            "JD_icon_{}_route".format(route), 
            Composite(
                (46, 31),
                (0, -8), im.Scale(eval("JD_char_image_{}".format(route)), 46, 46),
                (-2, -10), im.Scale("JDMOD/images/route/border.webp", 50, 50)
            )
        )        
        
        renpy.image (
            "JD_icon_{}_route_big".format(route), 
            Composite(
                (60, 31),
                (0, -16), im.Scale(eval("JD_char_image_{}".format(route)), 60, 60),
                (-2, -18), im.Scale("JDMOD/images/route/border.webp", 65, 65)
            )
        )


    ROUTE_APPEND = [
        "lena_love", 
        "ian_love",
        "alison_voyeur", "alison_love", "alison_share",
        "emma_share",
        "holly_ivy", "holly_ivy_lena", "holly_mark", "holly_mark_voyeur", "holly_robert", "holly_trinity",
        "ivy_jeremy",
        "perry_cherry", "perry_emma",
    ]

    JD_route_imagePlaceholder = JD_char_imagePlaceholder

    for route in ROUTE_APPEND:
        if renpy.loadable("JDMOD/images/route/" + route + ".webp"):
            
            renpy.image (
                "JD_icon_{}_route".format(route), 
                Composite(
                    (46, 31),
                    (0, -8), im.Scale("JDMOD/images/route/{}.webp".format(route), 46, 46),
                    (0, -8), im.Scale("JDMOD/images/route/border.webp", 46, 46)
                )
            )
            
            renpy.image (
                "JD_icon_{}_route_big".format(route), 
                Composite(
                    (60, 31),
                    (0, -16), im.Scale("JDMOD/images/route/{}.webp".format(route), 60, 60),
                    (0, -16), im.Scale("JDMOD/images/route/border.webp", 60, 60)
                )
            )
        else:
            
            renpy.image (
                "JD_icon_{}_route".format(route), 
                Composite(
                    (46, 31),
                    (0, -8), im.Scale(JD_route_imagePlaceholder, 46, 46),
                    (0, -8), im.Scale("JDMOD/images/route/border.webp", 46, 46)
                )
            )
            
            renpy.image (
                "JD_icon_{}_route_big".format(route), 
                Composite(
                    (60, 31),
                    (0, -16), im.Scale(JD_route_imagePlaceholder, 60, 60),
                    (0, -16), im.Scale("JDMOD/images/route/border.webp", 60, 60)
                )
            )



    DSTAT = [
        "athletics",
        "charisma",
        "money",
        "lust",
        "will",
        "wits",
        "pay"
    ]    

    SIGN = [
        "+",
        "-"
    ]

    for stat in DSTAT: 
        
        renpy.image (
            "JD_icon_{}".format(stat), 
            Composite(
                (40, 31),
                (0, -10), im.Scale("JDMOD/images/icons/stat/{}.png".format(stat), 40, 50)
            )
        )
        
        for sign in SIGN: 
            
            renpy.image (
                "JD_icon_{}_{}".format(stat, sign), 
                Composite(
                    (22+35, 31),
                    (0, -10), im.Scale("JDMOD/images/icons/stat/{}.png".format(stat), 40, 50),
                    (22, -20), im.Scale("JDMOD/images/icons/sign/{}.webp".format(sign), 35, 35)
                )
            )


    for main_char in MAIN_CHAR:
        for stat in DSTAT:
            
            renpy.image (
                "JD_icon_{}_{}".format(main_char, stat), 
                Composite(
                    (35+40, 31), 
                    (0, -8), im.Scale(eval("JD_char_image_{}".format(main_char)), 46, 46),
                    (0, -8), im.Scale("JDMOD/images/char/border.webp", 46, 46),
                    (35, -10), im.Scale("JDMOD/images/icons/stat/{}.png".format(stat), 40, 50)
                )
            )
            
            for sign in SIGN: 
                
                renpy.image (
                    "JD_icon_{}_{}_{}".format(main_char, stat, sign), 
                    Composite(
                        (62+35, 31), 
                        (0, -8), im.Scale(eval("JD_char_image_{}".format(main_char)), 46, 46),
                        (0, -8), im.Scale("JDMOD/images/char/border.webp", 46, 46),
                        (35, -10), im.Scale("JDMOD/images/icons/stat/{}.png".format(stat), 40, 50),
                        (62, -20), im.Scale("JDMOD/images/icons/sign/{}.webp".format(sign), 35, 35)
                    )
                )



    RSTAT = [
        "bdick",
        "posh",
        "chad",
        "rel",
        "rel_over",
        "mad",
        "dating",
        "dating_over",
        "love",
        "love_over"
    ]

    SIGN = [
        "+",
        "-",
        "++"
    ]



    CHAR1 = [
        "ian", 
        "lena", 
        "perry", 
        "wade"
    ]

    for stat in RSTAT:
        
        renpy.image (
            "JD_icon_{}".format(stat), 
            Composite(
                (40, 31),
                (0, -8), im.Scale("JDMOD/images/icons/stat/{}.png".format(stat), 46, 46)
            )
        )
        
        renpy.image (
            "JD_icon_{}_broken".format(stat), 
            Composite(
                (40, 31),
                (0, -8), im.Scale("JDMOD/images/icons/stat/{}_broken.png".format(stat), 46, 46)
            )
        )
        
        for sign in SIGN:
            
            renpy.image (
                "JD_icon_{}_{}".format(stat, sign), 
                Composite(
                    (27+35, 31),
                    (0, -8), im.Scale("JDMOD/images/icons/stat/{}.png".format(stat), 46, 46),
                    (27, -18), im.Scale("JDMOD/images/icons/sign/{}.webp".format(sign), 35, 35)
                )
            )

    for char in CHAR:
        for stat in RSTAT:
            
            renpy.image (
                "JD_icon_{}_{}".format(char, stat), 
                Composite(
                    (35+46, 31), 
                    (0, -8), im.Scale(eval("JD_char_image_{}".format(char)), 46, 46),
                    (0, -8), im.Scale("JDMOD/images/char/border.webp", 46, 46),
                    (35, -8), im.Scale("JDMOD/images/icons/stat/{}.png".format(stat), 46, 46)
                )
            )                        
            
            renpy.image (
                "JD_icon_{}_{}_broken".format(char, stat), 
                Composite(
                    (35+46, 31), 
                    (0, -8), im.Scale(eval("JD_char_image_{}".format(char)), 46, 46),
                    (0, -8), im.Scale("JDMOD/images/char/border.webp", 46, 46),
                    (35, -8), im.Scale("JDMOD/images/icons/stat/{}_broken.png".format(stat), 46, 46)
                )
            )
            
            for sign in SIGN:
                
                renpy.image (
                    "JD_icon_{}_{}_{}".format(char, stat, sign), 
                    Composite(
                        (62+35, 31), 
                        (0, -8), im.Scale(eval("JD_char_image_{}".format(char)), 46, 46),
                        (0, -8), im.Scale("JDMOD/images/char/border.webp", 46, 46),
                        (35, -8), im.Scale("JDMOD/images/icons/stat/{}.png".format(stat), 46, 46),
                        (62, -18), im.Scale("JDMOD/images/icons/sign/{}.webp".format(sign), 35, 35)
                    )
                )

    for char1 in CHAR1:
        for char in CHAR:
            for stat in RSTAT:
                
                renpy.image (
                    "JD_icon_{}_{}_{}".format(char1, char, stat), 
                    Composite(
                        (75+46, 31), 
                        (0, -8), im.Scale(eval("JD_char_image_{}".format(char1)), 46, 46),
                        (0, -8), im.Scale("JDMOD/images/char/border.webp", 46, 46),
                        (40, -8), im.Scale(eval("JD_char_image_{}".format(char)), 46, 46),
                        (40, -8), im.Scale("JDMOD/images/char/border.webp", 46, 46),
                        (75, -8), im.Scale("JDMOD/images/icons/stat/{}.png".format(stat), 46, 46)
                    )
                )
                
                
                renpy.image (
                    "JD_icon_{}_{}_{}_broken".format(char1, char, stat), 
                    Composite(
                        (75+46, 31), 
                        (0, -8), im.Scale(eval("JD_char_image_{}".format(char1)), 46, 46),
                        (0, -8), im.Scale("JDMOD/images/char/border.webp", 46, 46),
                        (40, -8), im.Scale(eval("JD_char_image_{}".format(char)), 46, 46),
                        (40, -8), im.Scale("JDMOD/images/char/border.webp", 46, 46),
                        (75, -8), im.Scale("JDMOD/images/icons/stat/{}_broken.png".format(stat), 46, 46)
                    )
                )
                
                for sign in SIGN:
                    
                    renpy.image (
                        "JD_icon_{}_{}_{}_{}".format(char1, char, stat, sign), 
                        Composite(
                            (97+35, 31), 
                            (0, -8), im.Scale(eval("JD_char_image_{}".format(char1)), 46, 46),
                            (0, -8), im.Scale("JDMOD/images/char/border.webp", 46, 46),
                            (35, -8), im.Scale(eval("JD_char_image_{}".format(char)), 46, 46),
                            (35, -8), im.Scale("JDMOD/images/char/border.webp", 46, 46),
                            (70, -8), im.Scale("JDMOD/images/icons/stat/{}.png".format(stat), 46, 46),
                            (97, -20), im.Scale("JDMOD/images/icons/sign/{}.webp".format(sign), 35, 35)
                        )
                    )







style keybind_text:
    font font_big_noodle
    color "#FFF"
    size text_size * 5
    outlines [ (2, "#090909", 0, 0), (2, "#090909", 2, 2) ]
    xalign 0.5
    yalign 0.5

style badc_keybind_text:
    color "#444444"

style goodc_keybind_text:
    color "#B77D0A"
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc

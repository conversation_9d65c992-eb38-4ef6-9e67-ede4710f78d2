## Choice screen ###############################################################
##
## This screen is used to display the in-game choices presented by the menu
## statement. The one parameter, items, is a list of objects, each with caption
## and action fields.
##
## https://www.renpy.org/doc/html/screen_special.html#choice

init offset = -1

default timeout = 4.0

default timeout_label = None

default persistent.timed_choices = True
screen choice(items):
    style_prefix "choice"

    vbox:
        for i in items:
            textbutton i.caption action i.action

    if (timeout_label is not None) and persistent.timed_choices:

        bar:
            xalign 0.5
            yalign 0.1
            #ypos 600
            xsize 1100
            ysize 10
            value AnimatedValue(old_value=0.0, value=1.0, range=1.0, delay=timeout)
        timer timeout action Jump(timeout_label)


## When this is true, menu captions will be spoken by the narrator. When false,
## menu captions will be displayed as empty buttons.
define config.narrator_menu = True


style choice_vbox is vbox:
    xalign 0.5
    ypos 405
    yanchor 0.5

    spacing gui.choice_spacing

style choice_vbox:
    variant "small"
    align (0.5, 0.5)

style choice_button is button:
    properties gui.button_properties("choice_button")
    hover_sound "sfx/paper_hover.mp3"
    activate_sound "sfx/paper_click.mp3"

style choice_button_text is button_text:
    properties gui.button_text_properties("choice_button")
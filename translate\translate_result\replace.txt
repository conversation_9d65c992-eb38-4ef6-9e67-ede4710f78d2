init python:
    if preferences.language == "schinese" :
        def replace_text(s):
            s = s.replace(' and song_1c == ' , '')
            s = s.replace(' or song_1a == ' , '')
            s = s.replace(', ' , '')
            s = s.replace(')  , [ Play (' , '')
            s = s.replace(', color=' , '')
            s = s.replace(' or song_1b == ' , '')
            s = s.replace('] is None or eval(sexshop_items[i][' , '')
            return s
        config.replace_text = replace_text

label v11lenafriday:
    call calendar(_month="July", _day="Friday", _week=4) from _call_calendar_110 
    $ flena = "n"
    $ lena_necklace = 0
    $ lena_makeup = 0
    $ lena_look = 1
    scene lenahome with long
    play music "music/normal_day2.mp3" loop
    "I made my usual morning coffee to kickstart my Friday."
    show lenabra with short
    "I took a sip before pulling up my phone and logging into my bank account. July was ending and that meant it was time to check my finances."
    if cafe_help:
        call money (2) from _call_money_3
        l "There's my paycheck from the café..."
    # seymour
    if seymour_desire:
        if cafe_help == False:
            l "No paycheck from the café this month, but it's not like I need that anymore."
        else:
            l "Not that I need it anymore, but it's nice to have I guess."
        if stalkfap:
            l "<PERSON> asked me to shut down my Stalkfap... Makes sense."
            if stalkfap_pro == 2:
                l "I was starting to make some serious money out of it, but it's nothing compared to what <PERSON> pays me!"
            else:
                l "I wasn't making much money out of it anyway... Especially compared to what <PERSON> pays me!"
        $ flena = "flirtshy"
        l "I've never had this much money... For the first time in my life, I can spend as much as I want without having to worry at all."
        l "It's a weird feeling... But it's also nice. Very nice."
        if seymour_disposition > 1:
            "And last night had been so... fascinating. I had never experienced something like that in any of my previous photo shoots."
            if seymour_disposition > 2:
                "Of course, <PERSON> was unlike any other man I had met before. And I was hungry for more of those extraordinary sensations he made me feel."
        elif seymour_disposition == 1:
            $ flena = "blush"
            "I still had conflicting feelings about last night's photo shoot, though. I had never experienced something like it..."
            "Seymour was a twisted man, but I couldn't deny the fascinating effect he had on me. He was unlike any other man I had ever met..."
        else:
            $ flena = "blush"
            "I still couldn't believe I had accepted to go along with last night's photo shoot. I told myself I couldn't refuse the chance of getting the money he offered..."
            "But the truth was I couldn't deny how fascinating a man like him was, twisted and powerful."
        if ian_lena_dating:
            $ flena = "worried"
            if ian_lena_couple:
                l "What would Ian say if he learned about our business relationship? There's no way he'd approve of it..."
            else:
                l "I wonder what Ian would think of our business relationship... I doubt he'd like to learn about the kind of contract we signed."
            $ flena = "n"
            l "But Seymour said he can help Ian achieve his goal and publish his book. He will be so happy when that happens..."
            $ flena = "sad"
            l "I guess it's better if I keep certain details private. Knowing too much would only trouble him."
    # no seymour
    else:
        if cafe_help == False:
            $ flena = "sad"
            l "No paycheck this month... I need to find a new job fast!"
            if lena_seymour_dating:
                l "Especially now that I've refused to keep working with Seymour. I'm gonna miss that money..."
        elif lena_seymour_dating:
            l "At least I can keep relying on that. But I'm gonna miss Seymour's money..."
        if lena_seymour_dating:
            $ flena = "serious"
            l "But I couldn't keep indulging that man. I let him take advantage of me and my situation, but yesterday was too much..."
            $ flena = "worried"
            l "If I had kept going down the path he laid in front of me... I feel I would've lost a piece of myself."
            $ flena = "sad"
            l "Now I'm on my own again... But I'll make this work."
            if ian_lena_dating:
                l "I hope my choice doesn't affect Ian's ambitions in a negative way. Seymour said he could make his dream of becoming a published author come true..."
                l "But no matter what he offered, I couldn't keep working under him. I doubt Ian would've liked knowing the kind of contract he made me sign..."
        else:
            $ flena = "serious"
            l "Things would be much easier without Seymour Ward messing with my life. I hope he forgets about me fast..."
    # rent
    if v10_stan_pay == 2:
        call money(-4) from _call_money_4
        $ flena = "sad"
        l "Damn, that's expensive... The rent went up this month, but what can I do?"
        l "I told Stan I would chip in. It's the right thing to do."
    elif v10_stan_pay == 1:
        call money(-3) from _call_money_5
        $ flena = "sad"
        l "The rent went up this month, but I told Stan I couldn't afford it..."
        l "He's taking care of it this time, but I don't think he'll be willing to put up with this situation for long."
    else:
        $ flena = "evil"
        call money(-3) from _call_money_6
        l "The rent went up this month, but thankfully I told Stan I wouldn't pay extra. It's on him..."
        l "I wonder how long he'll put up with it, though. I need to take advantage of it for as long as it lasts."
    # money recap
    if lena_money > 9:
        $ flena = "happy"
        l "My bank account never looked better! It's still hard to believe."
        if seymour_desire:
            $ flena = "smile"
            l "Meeting Seymour has really changed my life..."
    else:
        if lena_money < 0:
            $ flena = "drama"
            l "This is terrible... I'm in the red!"
            l "I owe money to the bank now... What am I gonna do?"
            $ flena = "worried"
            l "I don't want to ask anyone for money... And besides, who could I borrow from? Definitely not my parents. Maybe a friend? Ivy, perhaps...?"
            $ flena = "sad"
            l "I don't wanna think about that right now..."
        elif lena_money == 0:
            $ flena = "drama"
            l "And now I'm completely out of savings. If I don't make something happen, I won't be able to pay rent next month..."
            l "How did I get myself into this situation?"
        elif lena_money < 3:
            $ flena = "sad"
            l "I'm really short on money... I need to find a way to earn some during summer, otherwise..."
        elif lena_money < 6:
            $ flena = "n"
            l "I still have some money in my account, but I can't relax. I need to make some money this summer..."
        else:
            $ flena = "smile"
            l "My bank account looks healthy enough. I guess I'm doing okay after all..."
        if seymour_desire:
            $ flena = "smile"
            l "Well, no need to worry. Another big paycheck is coming soon, as long as I keep working with Seymour."
    # stan
    if stan_change > 0 and lena_reject_stan < 2:
        $ fstan = "n"
        if lena_stan_dating:
            $ stan_look = 4
        show lenabra at rig with move
        play sound "sfx/door.mp3"
        show stan at lef3 with short
        $ flena = "n"
        l "Good morning, Stan."
        if lena_stan_dating:
            $ fstan = "smile"
            st "Good morning, Lena."
            l "Want some coffee?"
            st "Yes, please."
        else:
            $ fstan = "worried"
            st "Um, good morning..."
            $ flena = "sad"
            "I could see Stan felt awkward around me after I rejected his advances... Hopefully, he'd get over it at some point."
            l "Want some coffee?"
            $ fstan = "n"
            st "Sure... Thanks."
        show stan at lef with move
        if lena_stan_dating:
            l "What's up with the outfit? Are you exercising?"
            $ fstan = "blush"
            st "Yeah, um... I've been thinking about what you told me the other day, and I think you're right..."
            st "I need to get out of my comfort zone. I want to become more active, and more confident, so I've decided to sign up for the gym."
            $ flena = "smile"
            l "Hey, that's cool! I'm sure it'll help you out and make you feel better."
            $ fstan = "sad"
            st "That's what I'm hoping for, but that'll be my first time stepping into a gym. I'm a bit nervous, to be honest..."
            st "I'm a total noob, so after checking some videos I've decided to hire a personal trainer..."
            $ fstan = "n"
            st "Hopefully, that'll help me get started."
            l "I'm glad to hear. It'll be good for you, I have no doubt about it!"
            $ fstan = "smile"
            st "Thanks, Lena... I really value your advice. You're helping me out a lot..."
            $ flena = "n"
            l "You're the one doing all the work. Keep it up!"
            st "I will. Speaking of which..."
        else:
            "He sat in front of me and I poured him some coffee."
            st "So... Can I ask you something?"
            l "Of course. What is it?"
        $ fstan = "n"
        st "Some time ago you told me you could introduce me to one of the photographers you work with..."
        st "Do you think he'll be willing to teach me? I'd like to take photography a bit more seriously and improve my skills."
        $ flena = "n"
        l "Oh, that's right. I'll give you Danny's contact; I'm sure he'd be up to do a workshop with you."
        $ fstan = "smile"
        st "Thanks, Lena."
        $ flena = "smile"
        if lena_stan_dating:
            l "Seems like you're making a lot of changes!"
            st "Yeah, well... I'm trying. Let's see how that goes."
            st "Anyway, I should get going... Wish me luck on my first workout!"
            l "Go smash it!"
            st "Thanks. Oh, and..."
            $ fstan = "shy"
            st "When you're free again... I'd like to hang out with you again."
            l "Of course. Me too."
            st "Cool. Have a good morning..."
        else:
            l "Sounds like you're making some changes!"
            $ fstan = "n"
            st "Yeah, well... I'm trying. Let's see how that goes."
            st "Anyway... Thanks for the coffee. I'll contact Danny and let you know how that goes."
        hide stan with short
        if lena_money < 1:
            $ flena = "sad"
            l "..."
            l "I wonder... Maybe I could ask Stan for help with my financial situation. He seems to be doing alright for himself..."
            if v10_stan_pay < 2:
                l "But he's already chipping in our part of the rent. I can't ask more from him, can I...?"
            else:
                l "I don't wanna ask, but considering how dire things are..."
            l "I'll have to think about it."
    stop music fadeout 2.0
    scene lenahome with long
    play sound "sfx/shower.mp3" fadeout 1.0
    pause 1
    $ flena = "n"
    $ fed = "n"
    # cafe
    if cafe_help:
        "I took a shower and got ready for work."
        scene street with long
        pause 0.5
        scene cafe with long
        play music "music/normal_day4.mp3" loop
        show lenawork at rig
        show ed at lef
        with short
        l "Hey, Ed. Molly didn't come today?"
        ed "No, I told her to stay at home today too... It's been a long week and she's been feeling a bit under the weather."
        $ flena = "sad"
        l "Again?"
        $ fed = "sad"
        ed "Yes... It's great that things are lively at the café lately, but she can't keep up with this much activity anymore."
        if cafe_perry:
            $ fed = "n"
            ed "Thankfully we have Perry to cover for her. He's been volunteering, but I've offered him a proper job. We can afford it!"
            $ flena = "smile"
            l "That's good to hear. Hopefully, Molly can afford to get some well deserved rest."
        else:
            $ fed = "n"
            ed "I think it's about time we hire someone else... At least now we can afford it!"
            $ flena = "n"
            l "If that lets Molly get some rest..."
        ed "Yeah. And we'll be closing for a couple of weeks during August. We both need a vacation!"
        ed "And you too, of course!"
        $ flena = "smile"
        l "That'd be nice, yes..."
        ed "Do you have any plans for this summer? A holiday trip or something of that sort?"
        if cafe_perry:
            $ perry_look = "cafe"
            $ fperry = "smile"
            show lenawork at rig3
            show ed at lef3
            with move
            show perry with short
            p "Yeah! We're gonna s--{w=0.5}spend some days at my family's house next to the beach!"
            if v10_ian_left or v11_lena_breakup:
                $ flena = "sad"
            ed "That sounds lovely!"
            if v10_ian_left or v11_lena_breakup:
                l "Um, yeah..."
                "I looked at Perry and took him aside."
                hide ed with short
                show lenawork at rig 
                show perry at lef
                with move
                l "Listen, Perry, about that..."
                jump v11perryinvite
            else:
                l "It does..."
                p "By the way, t--{w=0.5}tonight we're meeting at the Fortress, wanna join?"
                l "I'd love to, but I'm working tonight. Ivy asked me to bartend at the club..."
                ed "Oh, I need to get back to the kitchen. More customers just came in!"
                p "I'll help!"
                hide ed 
                hide perry
                with short
        else:
            if v10_ian_left or v11_lena_breakup:
                $ flena = "sad"
                l "I was invited to spend a few days at a friend's house, next to the beach, but I'm not sure if I'll go..."
                ed "Why not? Sounds like a lovely plan, you definitely should! It'll help you relax."
                l "Sadly, it's not that simple..."
            else:
                l "I've been invited to spend a few days at a friend's house, next to the beach."
                ed "That sounds lovely!"
                l "It does..."
            ed "Oh, I need to get back to the kitchen. More customers just came in!"
            hide ed with short
        # ivy appears
        label v11cafemorning:
            if holly_ivy:
                show lenawork at rig3 with move
            elif cafe_perry and ian_lena_breakup == False:
                show lenawork at rig with move
    # home
    else:
        "I was in no rush to get my day started. I took a shower and idled a bit around the house."
        "I had been staying at home the entire week, and I was tired of it, so I decided to go out and get lunch somewhere."
        scene street with long
        $ lena_look = 4
        play music "music/normal_day4.mp3" loop
        show lena with short
        play sound "sfx/ring.mp3"
        l "Someone's calling..."
        hide lena
        show lena_phone
        with short
        label v11perrycall:
            l "Yes?"
        show phone_perry at lef3 with short
        p "Hey, Lena. Are you b--{w=0.5}busy?"
        l "Oh, hi Perry. What's up?"
        p "I wanted to t--{w=0.5}tell you we're meeting at the Fortress tonight to get some beers. Are you d--{w=0.5}down for it?"
        l "I'd love to, but I'm working tonight. Ivy asked me to bartend at the club..."
        p "That's too bad... Anyway, I also wanted to ask you about the beach t--{w=0.5}trip next week. We're counting on you, right?"
        if v10_ian_left or v11_lena_breakup:
            $ flena = "sad"
            l "About that..."
            menu v11perryinvite:
                "I'll go":
                    $ renpy.block_rollback()
                    $ v11_perry_invite = 2
                    $ flena = "n"
                    l "I guess it's alright if I join after all..."
                    if cafe_help and cafe_perry:
                        $ fperry = "smile"
                    else:
                        hide phone_perry
                        show phone_perry_smile at lef3
                    p "Of course! The more the m--{w=0.5}merrier!"
                    $ flena = "sad"
                    l "I just hope Ian is alright with me tagging along."
                    p "Yeah, don't worry. It'll be fine."
                    $ flena = "n"
                    l "Cool..."
                    if cafe_help and cafe_perry:
                        $ fperry = "n"
                    else:
                        hide phone_perry_smile
                        show phone_perry at lef3
                    if cafe_help and cafe_perry:
                        p "I think I should go help Ed... I'll let you know the t--{w=0.5}trip details soon!"
                        hide perry with short
                        jump v11cafemorning
                    else:
                        p "Anyway, I'll let you know the t--{w=0.5}trip details soon! Bye!"
                        hide phone_perry with short
                        if cafe_help:
                            hide lenawork_phone
                            show lenawork
                        else:
                            hide lena_phone
                            show lena
                        with short

                "I haven't decided yet":
                    $ renpy.block_rollback()
                    $ v11_perry_invite = 1
                    l "I don't know, I haven't decided yet."
                    if cafe_help and cafe_perry:
                        $ fperry = "meh"
                    else:
                        hide phone_perry
                        show phone_perry_meh at lef3
                    p "Come on, it'll be f--{w=0.5}fun. The more the merrier!"
                    l "I just don't want to sour the mood. I'm not sure Ian will feel comfortable if I go."
                    if cafe_help and cafe_perry:
                        $ fperry = "n"
                    else:
                        hide phone_perry_meh
                        show phone_perry at lef3
                    p "That's alright. You're both adults, right? And you already t--{w=0.5}talked things over..."
                    l "We did, but these things take time..."
                    p "I just think it'll be so much b--{w=0.5}better if we all go together. Holly will feel much more at ease if you come!"
                    l "Well..."
                    $ flena = "n"
                    l "I'll think about it."
                    p "Yes, do that. I'll let you know the d--{w=0.5}details soon!"
                    if cafe_help and cafe_perry:
                        p "I think I should go help Ed... I hope you accept the i--{w=0.5}invitation after all!"
                        hide perry with short
                        jump v11cafemorning
                    else:
                        p "Anyway, I hope you accept the i--{w=0.5}nvitation after all!"
                        l "I'll let you know..."
                        hide phone_perry with short
                        if cafe_help:
                            hide lenawork_phone
                            show lenawork
                        else:
                            hide lena_phone
                            show lena
                        with short
            
                "I don't think I'll go":
                    $ renpy.block_rollback()
                    $ v11_perry_invite = 0
                    l "Actually, I don't think I'll go."
                    if cafe_help and cafe_perry:
                        $ fperry = "sad"
                    else:
                        hide phone_perry
                        show phone_perry_sad at lef3
                    p "Why?"
                    l "I just don't think it's a good idea, considering how things are between Ian and me."
                    p "Come on, it'll be f--{w=0.5}fine! You're both adults, right? And you already t--{w=0.5}talked things over..."
                    l "We did, but these things take time..."
                    p "I just think it'll be so much b--{w=0.5}better if we all go together. Holly will feel much more at ease if you come!"
                    l "Well..."
                    if cafe_help and cafe_perry:
                        $ fperry = "n"
                    else:
                        hide phone_perry_sad
                        show phone_perry at lef3
                    p "Think it over, alright? We all w--{w=0.5}would like you to come."
                    l "I'll think about it..."
                    if cafe_help and cafe_perry:
                        p "I think I should go help Ed now..."
                        hide perry with short
                        jump v11cafemorning
                    else:
                        p "Alright."
                        hide phone_perry with short
                        if cafe_help:
                            hide lenawork_phone
                            show lenawork
                        else:
                            hide lena_phone
                            show lena
                        with short
        else:
            $ flena = "smile"
            l "That's right. I'm looking forward to it!"
            hide phone_perry
            show phone_perry_smile at lef3 
            p "Me too! It'll be so c--{w=0.5}cool."
            p "See you soon, then!"
            l "Yeah. Thanks, Perry."
            hide phone_perry_smile with short
            if cafe_help:
                hide lenawork_phone
                show lenawork
            else:
                hide lena_phone
                show lena
            with short
        if cafe_help:
            jump v11prologueshop

        l "Now, I'd like something to drink... I think this café just opened up. Let's give it a try."
        scene cafeteria with long
        show lena with short
        if holly_ivy:
            show lena at rig3
        else:
            show lena at rig
        with move

## IVY TALK #############

    $ fivy = "smile"
    $ fholly = "n"
    if holly_ivy:
        $ holly_hair = 2
        show ivy
        show holly at lef3
        with short
    else:
        show ivy at lef with short
    if cafe_help:
        v "Hello!"
        if lena_holly_dating and holly_ivy:
            l "Oh! hi, girls... Here for some coffee?"
            v "No, Holly and I were doing some shopping in the mall and decided to drop by to check on you."
            l "I see...! Seems you're really getting along..."
            v "Of course! How could we not after becoming so intimate the other day?"
            $ fholly = "blush"
            hide holly
            show holly3 at lef3
            with short
            l "Yeah..."
        elif holly_ivy:
            l "Hi, girls! Here for some coffee?"
            v "No, Holly and I were doing some shopping in the mall and decided to drop by to check on you."
        else:
            l "Hi, Ivy. Here for some coffee?"
            v "No, I was doing some shopping in the mall and decided to drop by to check on you."
    else:
        v "Oh, hey Lena!"
        if holly_ivy:
            l "What are you girls doing here?"
            v "I took Holly to do some shopping and we decided to get a smoothie before going home."
            if lena_holly_dating:
                l "I see...! Seems you're really getting along..."
                v "Of course! How could we not after becoming so intimate the other day?"
                $ fholly = "blush"
                hide holly
                show holly3 at lef3
                with short
                l "Yeah..."
        else:
            l "Fancy meeting you here."
            v "I was in the area and decided to get a smoothie to cool down after a shopping spree, ha ha."
    v "Are you ready for tonight?"
    l "I guess so. I haven't bartended in a nightclub yet, but I believe I can handle it."
    v "Have you picked what to wear?"
    l "Not yet. Anything should be fine, right? Or is there some kind of dress code?"
    $ fivy = "flirt"
    v "Whatever you pick, just make sure you look hot. Sexy bartenders make people want to buy more drinks!"
    if lena_lust > 6:
        l "I know, that was what I had in mind."
    elif lena_lust > 5:
        l "I figured that would be the case."
    else:
        $ flena = "sad"
        l "Is it really necessary?"
        v "You will look out of place if you don't!"
    v "I wouldn't pick my favorite outfit, though. It's bound to get messy after an entire night of serving drinks."
    $ flena = "n"
    if lena_posh > 3:
        l "That's true... Maybe I should get something cheap just in case..."
    else:
        l "Sure. I'll get something cheap and simple."
    if holly_ivy:
        v "Now, if you'll excuse us, Holly and I need to try some of our purchases..."
        $ fholly = "blush"
        hide ivy
        show ivy2
        hide holly
        hide holly3
        show holly3 at lef3
        with short
        v "We bought a couple of very interesting items at the sex shop, right?"
        if v11_shower_sex == "3some" or v8_holly_sex == "lenaivy" or holly_guy > 2:
            $ flena = "shy"
        else:
            $ flena = "worried"
        h "Um... Yeah..."
        if holly_guy > 1:
            v "We need to get you ready for your date with Mark tonight!"
        else:
            v "You need to get ready for when you finally get to hook up with a guy! Soon, I hope!"
        play sound "sfx/slap2.mp3"
        with vpunch
        "Ivy slapped Holly's ass." 
        hide ivy2
        show ivy
        with short
        v "Come on, let's go. See you tonight, Lena!"
    else:
        play sound "sfx/sms.mp3"
        v "Whoops, gotta go! See you tonight!"
    hide ivy 
    hide holly3
    with short
    $ holly_hair = 1
    if cafe_help:
        show lenawork at truecenter
    else:
        show lena at truecenter 
    with move
    if lena_holly_dating and holly_ivy:
        $ flena = "worried"
        l "I wasn't expecting Holly and Ivy to get along so quickly... But I guess I shouldn't be surprised after what happened in the showers."
    $ flena = "n"
    if v11_emma_date:
        l "I'm meeting Emma later... I'll ask her to drop by the mall."
    else:
        l "The mall is nearby, I'll go take a look."
    if cafe_help and cafe_perry == False:
        play sound "sfx/ring.mp3"
        l "Someone's calling..."
        hide lenawork
        show lenawork_phone 
        with short
        jump v11perrycall

### SHOPPING ###################################################################################################################################################
label v11prologueshop:
    stop music fadeout 3.0
    $ flena = "n"
    $ femma = "n"
    if seymour_desire:
        $ toy_wand = True
    ## EMMA DATE ################################
    if v11_emma_date:
        scene street2 with long
        $ lena_look = 4
        show lena at rig with short
        "I met Emma after lunch, in front of the record store."
        show emma at lef with short
        $ flena = "smile"
        e "I'm free!"
        l "How was your Friday?"
        e "Slow. I need to get moving!"
        if cafe_help:
            e "How about you? Tired?"
            l "The café was rather busy, but no. I'm saving up some energy for tonight."
        else:
            l "Same for me. I need to save some energy for tonight, but I have plenty to spare."
        e "So, anything you'd like to do? I'm up for anything!"
        if lena_posh > 3 or seymour_desire:
            l "I'm in the mood for some shopping, actually... I need to buy something for tonight, and I think it's about time I get a bikini, too."
            e "Sure, we can do that, if that's what you want..."
        else:
            l "Actually, I need to buy some clothes if you don't mind... And I think it's about time I get a bikini, too."
            e "Sure, we can do that!"
        scene street2 with long
        play music "music/girls_day.mp3" loop
        scene mall with long
        pause 0.5
        show emma at lef
        show lena at rig
        with short
        e "I don't remember the last time I went shopping..."
        l "Really? Where do you get your clothes from?"
        e "All kinds of places! Clothes people leave behind when they leave the apartment, gifts from my friends, or pieces I get at thrift shops and popular markets from time to time."
        e "I once found a sweater that was in perfect condition lying in the trash. You won't believe what some people throw away!"
        if lena_posh > 3 or seymour_desire:
            $ flena = "worried"
            l "Uh... Yeah. I guess someone's trash can be someone else's treasure."
        else:
            l "I guess we don't see much value in some stuff..."
        e "That's right! There's already infinitely more clothes than we need! I never saw the need to produce more and more new clothing."
        e "I prefer to get second-hand clothes. They have a history, and are much cheaper! It's good for the environment too."
        e "What about you? Do you enjoy shopping?"
        menu:
            "{image=icon_charisma.webp}I love it" if lena_posh > 3 or seymour_desire or lena_charisma > 5:
                $ renpy.block_rollback()
                if lena_posh < 5:
                    $ lena_posh += 1
                $ flena = "happy"
                l "I love it! There's something therapeutic about it, you know?"
                $ femma = "sad"
                e "Really? I have a hard time seeing how buying stuff can be therapeutic... But I guess a lot of people feel that way."
                $ flena = "n"
                l "It's not just about buying things; it's the whole experience. It's like a form of self-expression."
                $ femma = "n"
                l "Trying on different outfits, checking out the latest trends, and feeling confident in a new look."
                l "The way you present yourself is super important, and fashion is an art form in itself!"
                e "I can see you're really passionate about it..."
                if lena_wits < 10:
                    call xp_up ('wits') from _call_xp_up_903
                l "I could help you polish your sense of style, if you want... Come on, let's check out some shops!"

            "Yes":
                $ renpy.block_rollback()
                if lena_posh < 4:
                    $ lena_posh += 1
                l "Yes, I do. It's a nice way to unwind and treat yourself from time to time."
                l "I like discovering a unique item or just wandering around the mall, searching for that piece of clothing or outfit that really speaks to me..."
                l "The clothes we wear represent how we see ourselves and how we want to be seen. I'm sure you care about what you wear, too."
                e "You're right... Normally I don't give it too much thought, but it's true at some times I've wanted to look good..."
                l "I can help you with that! Come on, let's check some shops!" 

            "Sometimes":
                $ renpy.block_rollback()
                l "Sometimes. It depends on my mood and, of course, the sales."
                l "I do enjoy discovering a unique item or just wandering around the mall, searching for that piece of clothing or outfit that really speaks to me..."
                e "I see! I enjoy exploring thrift stores from time to time. You can get some cool clothes for just a couple bucks!"
                l "Let's take a look, then!"
        
            "Not really" if lena_posh < 4:
                $ renpy.block_rollback()
                if lena_posh > 1:
                    $ lena_posh -= 1
                l "Not really... I mean, I like wearing nice clothes, but I'm not crazy about jumping on the latest trend or buying exclusive items."
                l "It's not about flashy clothes for me, it's more about comfort and personal style, and not getting caught up in the trap of consumerism..."
                e "I totally get that! We're always so caught up in the next new thing we end up never being able to have enough."
                e "It's like, we're so focused on material stuff that we lose sight of what really makes us happy."
                e "It's not things. It's people."
                if lena_emma < 12:
                    call friend_xp ('emma') from _call_friend_xp_1049
                $ flena = "smile"
                l "Do you think we'll find a thrift shop in this mall?"
                e "Let's check it out!"

        # clothing shop
        show lena at rig3
        show emma at lef3
        with move
        if lena_money < 0:
            $ flena = "worried"
            "I only had pocket money left. My bank account wasn't just empty, but in the red..."
            "Spending any amount of money made me feel anxious, but thankfully I could get what I needed at a very cheap price."
        elif lena_money == 0:
            $ flena = "worried"
            "I only had pocket money left. My bank account was completely empty..."
            "Spending any amount of money made me feel anxious, but thankfully I could get what I needed at a very cheap price."
        elif lena_money < 3:
            $ flena = "n"
            "I was short on money, but thankfully I could get what I needed at a cheap price. I could afford the expense."
        elif seymour_desire:
            "It felt good knowing I could afford to buy whatever I fancied..."
        show eli with short
        eli "Hi! Can I help you with anything?"
        l "We're just gonna browse a bit and try some clothes."
        eli "Alright. The fitting rooms are that way; let me know when you've picked something you wanna try on."
        l "Sure, thanks."
        hide eli with short
        label v11emmashopping:
            menu:
                "{image=icon_pay.webp}Buy outfits" if lena_money > 0:
                    $ renpy.block_rollback()
                    hide emma with short
                    $ flena = "n"
                    show lena at left with move
                    call screen v10clothingshoplena

                    label v11leaveshop_lena1:
                        show lena at rig3 with move
                        show emma at lef3 with short
                        jump v11emmashopping

                "Buy dresses" if v11_lena_dress == 0:
                    $ renpy.block_rollback()
                    hide emma with short
                    $ flena = "n"
                    l "Ivy told me to get something flashy for tonight, but it should also be comfortable to work in."
                    l "I think I'll get one of these cheap dresses... I won't cry over it if it gets lime and vodka spilled all over."
                    show lena at left with move
                    label v11buydress:
                        call screen screen_choice(v11buydress)
                        $ v11_lena_dress = _return
                    if v11_lena_dress == 1:
                        l "This black one looks simple and sexy enough. I like it..."
                    elif v11_lena_dress == 2:
                        l "This one looks glamorous and flashy... Should I get it?"
                    elif v11_lena_dress == 3:
                        l "This one looks pretty comfortable. I can't go wrong with this..."
                    elif v11_lena_dress == 4:
                        l "I wonder if red's the color I should go with. It's really eye-catching, that's for sure."
                    menu:
                        "Buy this one":
                            $ renpy.block_rollback()
                            l "It's decided. I'll get this one."
                            show lena at rig3 with move
                            show emma at lef3 with short
                            e "What did you pick? Oh, that's sexy!"
                            e "I don't believe I've ever worn a dress, now that I think about it."
                            $ flena = "smile"
                            l "You've never worn a dress? That's hard to believe!"
                            e "Well, maybe when I was a kid, but no... I can't remember ever wearing one!"
                            $ femma = "sad"
                            e "I wonder if I should give it a try... To tell the truth, I've been wondering if I should try to be a bit more feminine."
                            menu:
                                "Definitely!":
                                    $ renpy.block_rollback()
                                    if lena_posh < 4:
                                        $ lena_posh += 1
                                    l "Yeah, definitely! I didn't wanna say it, but your style could use a tune-up..."
                                    e "Oh, really? Well... I guess I'm just clueless about these kinds of things."
                                    if lena_emma > 2:
                                        call friend_xp ('emma', -1) from _call_friend_xp_1050
                                    e "I've always felt like one more of the guys, but lately... I don't know, everyone's gotten so pretty."
                                    e "You, Cindy, Cherry, Alison... Even Holly is looking super beautiful these days!"
                                    e "I guess I'd like to know how it feels to be more, I don't know, lady-like. Like you girls."
                                    l "Well, that's easy. You just have to experiment with different styles until you find one you feel comfortable in!"
                                    if lena_charisma < 10:
                                        call xp_up ('charisma') from _call_xp_up_904
                                    $ femma = "n"
                                    e "I think you're right. Thanks!"

                                "Why?":
                                    $ renpy.block_rollback()
                                    $ flena = "n"
                                    l "Why are you wondering that?"
                                    e "Hm... Maybe it's because I've never really tried. I've always felt like one more of the guys, and that's just how I like it."
                                    e "But lately, I don't know... Everyone's gotten so pretty. You, Cindy, Cherry, Alison... Even Holly is looking super beautiful these days!"
                                    e "I guess I'd like to know how it feels to be more, I don't know, lady-like. Like you girls."
                                    l "In that case, you should definitely give it a try. It's fun to play with one's style, and what's there to lose?"
                                    if lena_wits < 10:
                                        call xp_up ('wits') from _call_xp_up_905
                                    $ femma = "n"
                                    e "I think you're right. Thanks!"

                                "There's no need":
                                    $ renpy.block_rollback()
                                    $ flena = "sad"
                                    l "I don't think that's something you need."
                                    e "Still, do you think it's worth giving it a try? Maybe it's impossible for me to pull it off, but..."
                                    e "It's like everyone's gotten so pretty. You, Cindy, Cherry, Alison... Even Holly is looking super beautiful these days!"
                                    l "So you're feeling pressured by that?"
                                    $ femma = "n"
                                    e "I'm not sure. It's something I've never cared about... I've always felt like one more of the guys, and that's just how I like it."
                                    e "But I'd like to know how it feels to be more, I don't know, lady-like."
                                    $ flena = "n"
                                    l "Then go ahead and give it a try... It's alright to try new styles."
                                    e "Yes, that's right!"

                            if lena_bikini == 0:
                                jump v11emmashopping
                            else:
                                l "Alright, let's try on those bikinis."
                                jump v11bikiniscene

                        "I'm not sure":
                            $ renpy.block_rollback()
                            l "On second thought, maybe I should get something different."
                            jump v11buydress

                "Buy bikinis" if lena_bikini == 0:
                    $ renpy.block_rollback()
                    $ lena_bikini = 1
                    label gallery_CH11_S21:
                        if _in_replay:
                            call setup_CH11_S21 from _call_setup_CH11_S21
                    $ flena = "n"
                    l "You said you needed a bikini for this summer, right?"
                    e "Yup!"
                    l "That makes two of us. Let's see what's on offer."
                    show emma at lef
                    show lena at rig
                    with move
                    "We browsed some swimsuits and picked a few to take to the fitting room."
                    e "Shall we try these on?"
                    if v11_lena_dress == 0:
                        l "Wait, I still have to pick a dress..."
                        jump v11emmashopping
                    else:
                        l "Yeah."
                    show emma at lef3
                    show lena at rig3
                    with move
                    label v11bikiniscene:
                        show eli with short
                    eli "You've got everything you wanna try on?"
                    l "Yup."
                    eli "You can use the fitting room at the end of the corridor."
                    hide eli 
                    hide emma
                    with short
                    show lena at right with move
                    hide lena
                    with long
                    l "Let's see..."
                    call screen screen_choice(v11bikinitry)
                    $ lena_bikini = _return
                    pause 0.5
                    show lenabikini with short
                    if lena_bikini == 1:
                        $ flena = "smile"
                        l "This one's so much my style... It's in between cute and sexy. I like it."
                    if lena_bikini == 2:
                        $ flena = "smile"
                        l "I love this one... It looks sexy and elegant!"
                    if lena_bikini == 3:
                        $ flena = "flirt"
                        l "This one's so daring...! I've never worn a bikini like this."
                        if lena_lust < 8:
                            l "I wonder if it's too much..."
                    show lenabikini at rig with move
                    show emmabikini at lef with short
                    e "What do you think about this one...?"
                    if lena_bikini == 1:
                        $ lena_bikini_pic1 = True
                        $ femma = "smile"
                        e  "Oh, I like yours! It really suits you!"
                        l "Thanks! I think so too..."
                    if lena_bikini == 2:
                        $ lena_bikini_pic2 = True
                        $ femma = "smile"
                        e "Oh, I like yours! So classy!"
                        l "Thanks! I think so too."
                    if lena_bikini == 3:
                        $ lena_bikini_pic3 = True
                        $ femma = "surprise"
                        $ flena = "shy"
                        e "Oh, wow! That's a tiny bikini!"
                        l "Yeah... It doesn't leave much to the imagination, does it?"
                        $ femma = "flirt"
                        e "That can be a good thing, depending on the intentions you have when wearing that bikini..."
                        $ flena = "flirtshy"
                        if lena_lust > 7 or lena_fty_show:
                            l "Naughty intentions, of course!"
                        else:
                            l "It's funny. I'm used to wearing less while posing but thinking about wearing this in public..."
                    l "Hey, can you take a picture for me? I wanna see how it looks from behind."
                    $ femma = "n"
                    e "Of course."
                    show emmabikini at lef3
                    show lenabikini at rig3
                    with move
                    scene v11_bikini1_bg
                    show v11_bikini1
                    if lena_tattoo2:
                        show v11_bikini1_t2
                    if lena_tattoo3:
                        show v11_bikini1_t3
                    if lena_bikini == 1:
                        show v11_bikini1a
                    if lena_bikini == 2:
                        show v11_bikini1b
                    if lena_bikini == 3:
                        show v11_bikini1c
                    with long
                    pause 1
                    play sound "sfx/camera.mp3"
                    with flash
                    pause 1
                    e "There. What do you think?"
                    l "Let me see..."
                    $ flena = "smile"
                    scene mall
                    show lenabikini at rig
                    show emmabikini at lef3
                    with long
                    if lena_bikini < 3:
                        l "I really like it..."
                    if lena_bikini == 3:
                        if lena_lust > 7:
                            l "As I said, it doesn't leave much to the imagination... I love it!"
                        else:
                            l "As I said, it doesn't leave much to the imagination... I'm not sure I'm daring enough to wear it in public!"
                            if lena_fty_show:
                                l "It does feel exciting though!"
                    menu v11trybikini:
                        "Try another bikini":
                            $ renpy.block_rollback()
                            l "Let me try another one..."
                            scene mall with long
                            call screen screen_choice(v11bikinitry)
                            $ lena_bikini = _return
                            show lenabikini at rig
                            show emmabikini at lef
                            with long
                            if lena_bikini == 1:
                                $ flena = "smile"
                                if lena_bikini_pic1:
                                    l "It's hard to decide ... Another look usually helps."
                                else:
                                    l "This one's so much my style ... It's in between cute and sexy. I like it."
                            elif lena_bikini == 2:
                                $ flena = "smile"
                                if lena_bikini_pic2:
                                    l "It's hard to decide ... Another look usually helps."
                                else:
                                    l "I love this one ... It looks sexy and elegant!"
                            elif lena_bikini == 3:
                                $ flena = "flirt"
                                if lena_bikini_pic3:
                                    if lena_lust < 8:
                                        l "It does feel liberating wearing this one ... Should I?..."
                                    else:
                                        l "It's hard to decide ... Another look usually helps."
                                else:
                                    l "This one's so daring...! I've never worn a bikini like this."
                                    if lena_lust < 8:
                                        l "I wonder if it's too much..."
                            if (lena_bikini == 1 and lena_bikini_pic1 == False) or (lena_bikini == 2 and lena_bikini_pic2 == False) or (lena_bikini == 3 and lena_bikini_pic3 == False):
                                l "Can you take a picture of this one too?"
                                e "Sure!"
                                scene v11_bikini1_bg
                                show v11_bikini1
                                if lena_tattoo2:
                                    show v11_bikini1_t2
                                if lena_tattoo3:
                                    show v11_bikini1_t3
                                if lena_bikini == 1:
                                    show v11_bikini1a
                                if lena_bikini == 2:
                                    show v11_bikini1b
                                if lena_bikini == 3:
                                    show v11_bikini1c
                                with long
                                pause 1
                                play sound "sfx/camera.mp3"
                                with flash
                                pause 1
                            if lena_bikini == 1 and lena_bikini_pic1 == False:
                                e "I think it really suits you!"
                                l "Thanks! I think so too..."
                            if lena_bikini == 2 and lena_bikini_pic2 == False:
                                e "I like it! So classy!"
                                l "Thanks! I think so too."
                            if lena_bikini == 3 and lena_bikini_pic3 == False:
                                e "That's a tiny bikini!"
                                l "Yeah... It doesn't leave much to the imagination, does it?"
                                e "That can be a good thing, depending on the intentions you have when wearing that bikini..."
                                if lena_lust > 7 or lena_fty_show:
                                    l "Naughty intentions, of course!"
                                else:
                                    l "It's funny. I'm used to wearing less while posing but thinking about wearing this in public makes me feel ... naughty."
                            if (lena_bikini == 1 and lena_bikini_pic1 == False) or (lena_bikini == 2 and lena_bikini_pic2 == False) or (lena_bikini == 3 and lena_bikini_pic3 == False):
                                scene mall
                                show lenabikini at rig
                                show emmabikini at lef3
                                with long
                            if lena_bikini == 1 and lena_bikini_pic1 == False:
                                $ lena_bikini_pic1 = True
                            elif lena_bikini == 2 and lena_bikini_pic2 == False:
                                $ lena_bikini_pic2 = True
                            elif lena_bikini == 3 and lena_bikini_pic3 == False:
                                $ lena_bikini_pic3 = True
                            jump v11trybikini

                        "Buy this one":
                            $ renpy.block_rollback()
                            l "I'm sold! I'll get this one."

                    if (lena_bikini == 1 and lena_bikini_pic2 == False and lena_bikini_pic3 == False) or (lena_bikini == 2 and lena_bikini_pic1 == False and lena_bikini_pic3 == False) or (lena_bikini == 3 and lena_bikini_pic1 == False and lena_bikini_pic2 == False):
                        show emmabikini at lef with move
                    e "I guess I'll get myself this one, then..."
                    if lena_bikini > 1:
                        e "It's a bit plain compared to yours, though!"
                    menu:
                        "{image=icon_lust.webp}Try something hotter!" if lena_lust > 6:
                            $ renpy.block_rollback()
                            $ emma_bikini = True
                            $ v11_emma_pics = 1
                            $ flena = "happy"
                            l "Why don't you go for something hotter? Here, try this bikini bottom..."
                            e "Let's see..."
                            hide emmabikini with short
                            $ emma_look = 2
                            $ femma = "smile"
                            pause 0.5
                            show emmabikini at lef with short
                            e "Do you think it looks good on me?"
                            l "Sure! Now strike a pose!"
                            e "I'm not sure if I know how to do it!"
                            l "It's easy! Turn around, look over your shoulder, and arch your back..."
                            scene v11_bikini1_bg
                            show v11_bikini1_emma
                            show v11_bikini1_emmab
                            with long
                            pause 1
                            e "Like this?"
                            l "Yeah! Stick out your butt more... You've gotta make your best feature pop!"
                            e "You also think so? I guess I should really take more advantage of it..."
                            l "You should! That's why you should get this thong..."
                            scene mall
                            show lenabikini at rig
                            show emmabikini at lef
                            with long
                            e "I'll listen to you, in that case! Honestly, I'd like to be a bit sexier..."
                            label v11bikinipic:
                                show emmabikini at lef3
                                show lenabikini at rig3
                                with move
                            show eli with short
                            eli "How's it going over here? Can I help you with something?"
                            menu:
                                "Take a picture of us!":
                                    $ renpy.block_rollback()
                                    $ v11_emma_pics = 2
                                    $ flena = "happy"
                                    l "Actually... Would you take a picture of us, please?"
                                    eli "Um, sure."
                                    $ femma = "smile"
                                    e "A picture together? Should we strike a pose?"
                                    l "Definitely!"
                                    scene v11_bikini1_bg
                                    show v11_bikini1_emma at v11emmabikinishopping
                                    if emma_bikini:
                                        show v11_bikini1_emmab at v11emmabikinishopping
                                    else:
                                        show v11_bikini1_emmaa at v11emmabikinishopping
                                    show v11_bikini1 at v11lenabikinishopping
                                    if lena_tattoo2:
                                        show v11_bikini1_t2 at v11lenabikinishopping
                                    if lena_tattoo3:
                                        show v11_bikini1_t3 at v11lenabikinishopping
                                    if lena_bikini == 1:
                                        show v11_bikini1a at v11lenabikinishopping
                                    if lena_bikini == 2: 
                                        show v11_bikini1b at v11lenabikinishopping
                                    if lena_bikini == 3:
                                        show v11_bikini1c at v11lenabikinishopping
                                        show v11_bikini1_face at v11lenabikinishopping
                                    with long
                                    pause 1
                                    e "Like this?"
                                    play sound "sfx/camera.mp3"
                                    with flash
                                    pause 1
                                    eli "Alright... I took a few just in case."
                                    $ feli = "n"
                                    if emma_bikini:
                                        menu:
                                            "{image=icon_lust.webp}Tease Emma":
                                                $ renpy.block_rollback()
                                                $ v11_emma_pics = 3
                                                l "Wait, take a few more..."
                                                scene v11_bikini2
                                                if lena_piercing1:
                                                    show v11_bikini2_p1
                                                elif lena_piercing2:
                                                    show v11_bikini2_p2
                                                if lena_tattoo3:
                                                    show v11_bikini2_t3
                                                if lena_bikini == 1:
                                                    show v11_bikini2a
                                                if lena_bikini == 2:
                                                    show v11_bikini2b
                                                if lena_bikini == 3:
                                                    show v11_bikini2c
                                                with long
                                                pause 1
                                                "Moved closer to Emma, my hands sliding down her hips and grabbing her plump buttocks."
                                                l "Make sure you get my friend's ass in frame."
                                                e "Lena...!"
                                                l "I told you to stick out your butt more, like this... You've gotta flaunt it!"
                                                play sound "sfx/camera.mp3"
                                                with flash
                                                "I looked at the shop assistant as I dug my fingers into Emma's flesh."
                                                l "She has an ass to be proud of, don't you think so?"
                                                eli "Um... Yeah, sure..."
                                                e "Thanks, ha ha! I think we have enough pictures already...!"
                                                $ feli = "blush"
                                                if not _in_replay:
                                                    $ gallery_unlock_scene("CH11_S21")

                                            "Thanks!":
                                                $ renpy.block_rollback()

                                    $ flena = "smile"
                                    $ femma = "n"
                                    scene mall
                                    show emmabikini at lef3
                                    show lenabikini at rig3
                                    show eli
                                    with long
                                    l "Thanks!"
                                    eli "It's alright..."
                                    eli "I'll take the clothes you discarded. Go to the cashier when you're ready to pay..."
                                    hide eli with short
                                    if v11_emma_pics == 2:
                                        $ femma = "smile"
                                        show emmabikini at lef
                                        show lenabikini at rig
                                        with move
                                        e "That was naughty!"
                                        $ flena = "flirt"
                                        l "I couldn't resist! You have a great ass..."
                                        e "I usually get that... but from guys!"
                                        $ flena = "happy"
                                        l "Girls have eyes too, you know?"
                                        $ femma = "n"
                                        if ian_lena_dating:
                                            e "Well, thanks... I wish I had a body like yours, though! No wonder Ian is crazy about you!"
                                        else:
                                            e "Well, thanks... I wish I had a body like yours, though! No wonder people are crazy about you!"
                                        $ flena = "smile"
                                        l "I'm sure you have no shortage of suitors either..."
                                        if ian_emma_sex:
                                            if v10_emma_sex:
                                                $ femma = "flirt"
                                                e "I can't complain... I've been having a blast lately!"
                                                l "Even more reasons to buy that bikini!"
                                            elif emma_jeremy:
                                                $ femma = "flirt"
                                                e "I can't complain... I've had some adventures lately."
                                                l "Even more reasons to buy that bikini!"
                                            else:
                                                $ femma = "smile"
                                                e "I can't complain, I guess... Well, except that a really cool thing I had going had to stop, but oh well..."
                                                l "You'll find someone else. Especially once you start wearing that bikini!"
                                        else:
                                            e "Lately not..."
                                            l "Well, that bikini will change that soon!"

                                "We're done":
                                    $ renpy.block_rollback()
                                    $ flena = "n"
                                    l "No, thanks. I think we're done here."
                                    $ femma = "n"
                                    eli "Alright. I'll take the clothes you discarded. Go to the cashier when you're ready to pay."
                                    hide eli with short

                        "{image=icon_charisma.webp}Want me to take a picture?" if lena_charisma > 5:
                            $ renpy.block_rollback()
                            $ v11_emma_pics = 1
                            $ flena = "happy"
                            l "Hey, want me to take a picture for you too?"
                            $ femma = "smile"
                            e "Sure... But I don't know how to pose like you do!"
                            l "It's easy! Turn around, look over your shoulder, and arch your back..."
                            scene v11_bikini1_bg
                            show v11_bikini1_emma
                            show v11_bikini1_emmaa
                            with long
                            pause 1
                            e "Like this?"
                            play sound "sfx/camera.mp3"
                            with flash
                            l "That's right! Looking good!"
                            e "Really? I guess it's easy enough...!"
                            scene mall
                            show lenabikini at rig
                            show emmabikini at lef
                            with long
                            e "It doesn't come naturally to me, though. It feels a bit weird, ha ha!"
                            jump v11bikinipic

                        "Let's go":
                            $ renpy.block_rollback()
                            l "Come on, let's pay and get going."
        
        if ian_emma_sex and emma_bikini == False:
            e "Wait, let me take another look... I saw another bikini I’d like to try on."
            l "Sure! I’ll wait outside."
        # end shopping
        scene mall with long
        $ renpy.end_replay()
        $ emma_look = 1
        pause 0.5
        $ flena = "n"
        $ femma = "n"
        show emma at lef
        show lena at rig
        with short
        if ian_emma_sex and emma_bikini == False:
            e "Alright, done! Shall we get going?"
        else:
            e "So, are we done?"
        if lena_posh > 3 and lena_money > 4: #Lena doesn't have to buy the dress, only lingerie
            menu:
                "{image=icon_pay.webp}Visit the boutique":
                    $ renpy.block_rollback()
                    l "Wait, not yet. There's this boutique I want to check out too."
                    e "Oh, alright. I'll take a bathroom break and wait for you next to the exit."
                    l "Sure."
                    hide emma with short
                    show lena at lef3 with move
                    call screen v10boutique
                    label v11emmaboutique:
                        show lena with short
                        show lena at rig with move
                        show emma at lef with short
                        $ flena = "n"
                        l "Alright, I'm done."
                
                "Yes, that's it":
                    $ renpy.block_rollback()
                    l "Yes, that's it. I have everything I need..."
            
        else:
            l "Yes, that's it. I have everything I need..."
        e "Let's go get a beer! They always set up a bar in the park during summer, we can check it out!"
        if lena_posh < 3:
            l "Fine by me."
        else:
            $ flena = "smile"
            l "Sounds perfect."
        # sex shop
        $ femma = "smile"
        e "Oh, wait... Do you mind if we check the sex shop before we leave?"
        if emma_jeremy == False:
            menu:
                "Let's go":
                    $ renpy.block_rollback()
                "Leave":
                    $ renpy.block_rollback()
                    if lena_money < 6:
                        $ flena = "sad"
                    l "I'd rather leave the mall... I've spent enough money already."
                    $ femma = "n"
                    e "Sure, I understand. Let's get going."
                    jump v11emmadateend

        if lena_lust > 6:
            l "Sure!"
        else:
            l "Sure..."
        scene sexshop with long
        $ femma = "n"
        show lena at rig
        show emma at lef
        with short
        l "Are you looking for something in particular?"
        e "I saw something on the internet and it caught my attention..."
        $ femma = "smile"
        e "Look, here it is."
        show lena at right
        show emma at left
        with move
        show toy_tail with short
        $ flena = "shy"
        e "Isn't it naughty?"
        menu:
            "{image=icon_lust.webp}It is" if lena_anal > 0:
                $ renpy.block_rollback()
                l "Yeah, it is..."
                $ femma = "flirt"
                e "I see we think the same! Have you ever tried one of these?"
                if lena_emma < 12:
                    call friend_xp ('emma') from _call_friend_xp_1051
                l "Not with the tail... Just the plug."
                e "Me too, but it was a long time ago... I much prefer the real thing, if you know what I mean!"
                if lena_anal > 1:
                    $ flena = "happy"
                    l "I know, and we are of the same opinion on that too!"
                else:
                    l "Would you believe I haven't tried that yet?"
                e "I'm not sure what I prefer myself, vaginal or anal... Both feel great, but anal is such a turn-on!"

            "It's not for me":
                $ renpy.block_rollback()
                $ flena = "n"
                if lena_anal > 0:
                    l "I don't think it's for me... I like anal play, but I'm not too convinced about the tail."
                    e "Really? I think it's what gives it the spice! But just the butt plug is nice too."
                    $ flena = "shy"
                    l "Yup. I have one..."
                    $ femma = "flirt"
                    e "Nothing like the real thing, though... I'm not sure what I prefer myself, vaginal or anal... Both feel great, but anal is such a turn-on!"
                else:
                    l "I don't think it's for me... Actually, I'm not into anal sex."
                    $ femma = "n"
                    e "Oh, I see. I'm not sure what I prefer myself, vaginal or anal... Both feel great, but anal is such a turn-on!"

            "I'm not into anal" if lena_anal == 0:
                $ renpy.block_rollback()
                $ flena = "n"
                l "Actually, I'm not into anal sex..."
                $ femma = "n"
                e "Oh, I see. I'm not sure what I prefer myself, vaginal or anal... Both feel great, but anal is such a turn-on!"

        hide toy_tail with short
        $ femma = "smile"
        # lesb
        show toy_double with short
        e "And what about this one? They make up some crazy stuff..."
        e "I wonder, how do you use it exactly? Several ideas come to mind!"
        if toy_double:
            $ flena = "flirt"
            if lena_anal:
                l "Actually, I bought one of these. You can use it like a regular dildo, or bend it so you can plug it in both of your holes."
            else:
                l "Actually, I bought one of these. You can use it like a regular dildo, or bend it and rub the other end against your clitoris. Or you can insert both ends at the same time."
            if v11_louise_dildo > 2:
                l "And my favorite option, sharing the other end with someone else..."
            else:
                l "Or you could share the other end with someone else, but I haven't used it like that..."
            $ femma = "flirt"
            e "That sounds super hot!"
        else:
            if lena_lust > 6 and lena_anal:
                $ flena = "flirtshy"
                l "I imagine you could use it like a regular dildo, or bend it so you can plug it in both of your holes."
            else:
                $ flena = "shy"
                l "I imagine you could use it like a regular dildo, or bend it so you can, you know..."
            $ femma = "flirt"
            e "Or share the other end with someone else!"
        # wand
        hide toy_double with short
        $ femma = "smile"
        show toy_wand with short
        e "Look at this! I've heard these can give you the craziest orgasms!"
        if seymour_desire:
            $ flena = "blush"
            "I could attest to that. The one Seymour had given me was tucked away in the nightstand drawer."
            "That had been one of the most intense frights in my entire life... perhaps the greatest of them all."
        else:
            $ flena = "n"
            l "Really ? I've never tried one of these..."
            $ femma = "n"
            e "Me neither..."
        # jeremy
        hide toy_wand with short
        $ flena = "n"
        if emma_jeremy or lena_fty_bbc:
            $ femma = "flirt"
            e "Oh! And look at this one..."
            show toy_mandingo with short
            if emma_jeremy:
                e "It's humongous! I would say it looked ridiculous if I hadn't seen one very similar to this in real life..."
                if lena_fty_bbc or v7_bbc == "lena":
                    $ flena = "flirtshy"
                    l "Oh, really? Who was the guy?"
                    $ femma = "smile"
                    e "Jeremy!"
                else:
                    $ flena = "worried"
                    l "That's way too big. I wonder who's able to fit something like that inside..."
                    $ femma = "smile"
                    e "It's not easy! It took Jeremy and me some tries until we managed to make it work..."
                $ flena = "surprise"
                l "You and Jeremy? I had no idea..."
                if v7_bbc == "lena":
                    $ flena = "flirt"
                    l "Seems he really gets around..."
                    e "His thing is massive. Biggest I've ever seen... It took us some tries until we managed to make it work!"
                    if lena_jeremy_sex or v10_jeremy_3some:
                        "I had that experience too... Fitting Jeremy's cock hadn't been easy, and there was no way I could take all of it."
                        if v10_jeremy_3some:
                            "But painful and frustrating as it was, it had also been so fucking hot..."
                    elif v8_jeremy_sex:
                        "I hadn't had the chance to give it a try myself... I had only sucked Jeremy off so far, but I wondered if I could take his thing at all..."
                    else:
                        "I hadn't had the chance to give it a try myself... I wondered if I could take his thing at all..."
                    if ian_lena_dating:
                        "I couldn't mention any of it to Emma, of course, even if we shared the experience of being with Jeremy."
                        "I didn't want Ian, Perry, or anyone in their friend circle to catch wind of it, just in case..."
                    else:
                        "I didn't mention anything to Emma, though. Better keep my flirting with Jeremy under wraps, just in case."
                    $ flena = "n"
                    l "So you and Jeremy are friends with benefits?"
                    $ femma = "n"
                    e "Yeah, something like that."
                    l "Lucky you... Big cocks are such a turn-on, right?"
                    $ femma = "smile"
                    e "Yeah! It makes you feel like you're in one of those porn movies, ha ha!"
                    $ femma = "n"
                    e "It's fun, but honestly, I enjoy myself much more with a normal dick."
                    e "A thing that size is just too hard to handle, and there's a lot of stuff you can't do."
                    l "I guess so... But the thrill and excitement you get out of it makes it more than worth it!"
                    
                else:
                    $ flena = "sad"
                    l "Seems he really likes to get around. Louise should've dropped him much sooner..."
                    $ femma = "sad"
                    e "You mean your flatmate? It's true, I heard something about them hooking up..."
                    l "Jeremy was supposed to be her boyfriend, but I think only Louise believed that to be the case. He didn't bother to correct her, though."
                    e "He's not the most mature person... Especially when it comes to girls."
                    $ flena = "n"
                    l "Yet you hooked up with him..."
                    $ femma = "smile"
                    e "But we know where we stand. We've been friends for a long time, and we were just curious about each other, so..."
                    l "So you're friends with benefits or something of that sort."
                    e "Yeah, that's right."
                    if lena_fty_bbc:
                        $ flena = "shy"
                        l "So... He's as big as this dildo?"
                        $ femma = "flirt"
                        e "Close enough! Biggest I've ever seen, that's for sure!"
                        l "Damn... That's entirely too big. But a big cock is such a turn-on, right?"
                        $ femma = "smile"
                        e "Yeah! It makes you feel like you're in one of those porn movies, ha ha!"
                        $ femma = "n"
                        e "It's fun, but honestly, I enjoy myself much more with a normal dick."
                        e "A thing that size is just too hard to handle, and there's a lot of stuff you can't do."
                        $ flena = "flirt"
                        l "I guess so... But the thrill and excitement you get out of it makes it more than worth it!"
                    else:
                        l "If that works for you..."
            else:
                $ femma = "smile"
                e "It's humongous! Have you ever seen one like this in real life?"
                if v7_game:
                    if v7_bbc == "lena":
                        $ flena = "flirt"
                    else:
                        $ flena = "shy"
                    l "Actually, yes..."
                    e "Really? And were you able to handle it?"
                    if v8_jeremy_sex or v10_jeremy_3some:
                        l "Kind of... I still need more practice I think."
                    else:
                        $ flena = "n"
                        if v7_bbc == "lena":
                            l "It never got that far... But I would've liked to give it a try..."
                        else:
                            l "I said I've seen it, not that I did anything with it... But I was tempted."
                else:
                    $ flena = "shy"
                    l "No, not this big... But close!"
                    e "Really? I wonder how you even handle something like this...?"
                    $ flena = "smile"
                    l "It's not easy!"
                e "Oh, really?"
                if v8_jeremy_flirt:
                    if ian_lena_dating:
                        "I didn't want to mention anything about my flirting with Jeremy."
                        "I didn't want Ian, Perry, or anyone in their friend circle to catch wind of it, just in case..."
                    else:
                        "I didn't want to mention anything about my flirting with Jeremy. Better keep that under wraps, just in case..."
                $ flena = "shy"
                l "Big cocks are such a turn-on, don't you think so?"
                $ femma = "smile"
                e "Yeah! It makes you feel like you're in one of those porn movies, ha ha!"
                $ femma = "n"
                e "It's fun, but honestly, I enjoy myself much more with a normal dick."
                e "A thing that size is just too hard to handle, and there's a lot of stuff you can't do."
                $ flena = "flirt"
                l "I guess so... But the thrill and excitement you get out of it makes it more than worth it!"
            hide toy_mandingo with short
        $ femma = "n"
        $ flena = "n"
        show emma at lef
        show lena at rig
        with move
        e "Alright, I'm done here... Or do you wanna buy something?"
        menu:
            "Browse sex toys" if lena_money > 0:
                $ renpy.block_rollback()
                l "Maybe... Let me take a look."
                show emma at left
                show lena at right
                with move

                call open_sexshop from _call_open_sexshop_4

                show emma at lef
                show lena at rig
                with move
                l "Alright, that's it. Let's get going."

            "Let's get going":
                $ renpy.block_rollback()
                l "Let's get going."

        label v11emmadateend:
            stop music fadeout 2.0
        scene street with long
        pause 1
        play music "music/emmas_theme.mp3" loop
        scene park with long
        "We sat on the terrace, under a lovely sun and with views of the river. The breeze was nice and the park was buzzing with activity."
        $ flena = "smile"
        show lena at rig
        show emma at lef
        with short
        l "Baluart's a really lovely city... I'm happy I get to live here."
        $ femma = "sad"
        e "Yeah, me too. Even though it's getting harder by the day... Small businesses closing, rent going up, and crooked individuals pushing their own agendas..."  
        $ flena = "sad"
        e "I hope we manage to turn things around, but I don't see it happening..."
        menu:
            "{image=icon_money.webp}It's not so bad..." if seymour_desire:
                $ renpy.block_rollback()
                $ flena = "n"
                l "I don't think it's that bad..."
                if cafe_help:
                    e "How can you say that? I know you've been helping the café stay afloat, but many other local businesses are struggling or have closed up shop already..."
                else:
                    e "How can you say that? Many local businesses are struggling or have closed up shop already, like the café..."
                l "That's how business works, isn't it? You have to be competitive or other, better businesses will take your place."
                $ femma = "serious"
                e "But we must protect local businesses! They have no way to defend themselves against the corruption that is replacing all the small businesses with corporate-owned establishments in the city!"
                l "You call it corruption, but that's just how the market works."
                e "Do you really think that, or is it Seymour Ward's idea?"
                $ flena = "serious"
                l "I have my own ideas, even if it's hard for you to believe."
                if lena_emma > 0:
                    call friend_xp ('emma',-1) from _call_friend_xp_1052
                $ femma = "sad"
                e "I just don't understand why you want to associate yourself with a man like him..."
                l "It's easy to point fingers and blame your problems on someone else. Especially if that someone is doing good for himself."
                e "It's clear we won't see eye to eye on this..."
                l "So it would seem."
                "A tense silence followed before Emma made an effort to change the subject."
                $ flena = "n"
                $ femma = "n"
                e "So, um... Will you join us at Perry's beach house next week?"

            "{image=icon_friend.webp}You're fighting back" if seymour_desire == False:
                $ renpy.block_rollback()
                $ flena = "n"
                l "But you're fighting back! That means you haven't lost hope..."
                $ femma = "n"
                e "Hope is the last thing to fade, or so they say! But yeah, I still believe it's worth pushing back against the vested interests of some."
                if cafe_help:
                    e "You're also helping us with that! What you're doing for the café is great. Without you, the Van Dykes would've surely closed down!"
                    $ flena = "n"
                    l "Yeah... I'm glad that's working out, at least."
                    e "You're doing a great service to the community! If we're gonna turn this thing around, it's thanks to people like you!"
                    if lena_seymour_dating:
                        $ femma = "sad"
                        e "That's why I can't understand why you're also working with Seymour Ward."
                    else:
                        if lena_charisma < 10:
                            call xp_up ('charisma') from _call_xp_up_906
                        e "I just hope there were more..."
                else:
                    $ femma = "sad"
                    e "It's not easy, though... I'm not sure we're making much progress. The café had to close down, and many other local businesses..."
                    $ flena = "sad"
                    l "Yeah, that sucks. Now I have to find some kind of job before my savings run out."
                    $ femma = "n"
                    e "I'll let you know if I hear about something!"
                    $ flena = "n"
                    l "Please, do..."
                    if lena_seymour_dating:
                        $ femma = "sad"
                        e "But... weren't you working with Seymour Ward already?"
                if lena_seymour_dating:
                    $ flena = "worried"
                    l "Actually, that's over with..."
                    e "Oh, really?"
                    l "Yeah... The truth is he pressured me into working with him, and I felt I couldn't say no."
                    l "The money he offered was too tempting to pass upon. But..."
                    l "In the end, I couldn't trust him. It's just like what you said: he's a dangerous man..."
                    $ femma = "n"
                    e "Well, I'm glad you finally decided to cut ties with him. He's not someone you want to associate yourself with." 
                e "By the way, will you join us at Perry's beach house next week?"

            "I don't see it either":
                $ renpy.block_rollback()
                if lena_seymour_dating:
                    $ flena = "n"
                    l "Yeah, I don't see it either..."
                    e "Yet you still choose to associate yourself with Seymour Ward..."
                    if seymour_desire:
                        $ flena = "serious"
                        l "Again with that? You really have something against him..."
                        e "You know I do! I have reasons to!"
                        l "I think you just have a ton of prejudices against business owners and entrepreneurs. I don't see how he's doing anything wrong..."
                        $ femma = "serious"
                        e "Do you really think that? Come on, you've seen what's going on in Baluart!"
                        if cafe_help:
                            e "If not for you, the café would've had to close down, just like many other small businesses!"
                            $ flena = "n"
                            l "Because we've been working hard... That's how businesses work: if you're not competitive, you need to shut down."
                        else:
                            e "The café ended up having to close down, just like many other small businesses!"
                            $ flena = "n"
                            l "That's how businesses work... If you're not competitive, you need to shut down. It's how it's always been."
                        if lena_emma > 0:
                            call friend_xp('emma',-1) from _call_friend_xp_1053
                        $ femma = "sad"
                        e "I'm afraid we won't see eye to eye on this matter..."
                        l "So it would seem."
                        "A tense silence followed before Emma made an effort to change the subject."
                        $ flena = "n"
                        $ femma = "n"
                        e "So, um... Will you join us at Perry's beach house next week?"
                    else:
                        $ flena = "worried"
                        l "Actually, that's over with..."
                        e "Oh, really?"
                        l "Yeah... The truth is he pressured me into working with him, and I felt I couldn't say no."
                        l "The money he offered was too tempting to pass upon. But..."
                        l "In the end, I couldn't trust him. It's just like what you said: he's a dangerous man..."
                        $ femma = "n"
                        e "Well, I'm glad you finally decided to cut ties with him. He's not someone you want to associate yourself with." 
                        if cafe_help:
                            e "You've been pushing against his agenda too, after all. Without you, the café would've surely closed down!"
                            $ flena = "n"
                            l "Yeah... I'm glad that's working out, at least."
                            e "You're doing a great service to the community! If we're gonna turn this thing around, it's thanks to people like you!"
                            e "I just hope there were more..."
                        else:
                            l "I could've really used the money, though. Now I have to find some kind of job before my savings run out."
                            e "I'll let you know if I hear about something!"
                            $ flena = "n"
                            l "Please, do..."
                        e "By the way, will you join us at Perry's beach house next week?"
                else:
                    l "Yeah, I don't see it either..."
                    if cafe_help:
                        $ femma = "n"
                        e "You've been doing a great job, though. Everyone appreciates what you did for the café, both the Van Dykes and the community."
                        $ flena = "n"
                        l "Yeah... I'm glad that's working out, at least."
                        e "You're doing a great service to the community! If we're gonna turn this thing around, it's thanks to people like you!"
                        e "I just hope there were more..."
                    else:
                        e "It's such a shame the café had to close down. Most local businesses are at risk now, or have shut down already..."
                        l "Yeah, that sucks. Now I have to find some kind of job before my savings run out."
                        $ femma = "n"
                        e "I'll let you know if I hear about something!"
                        $ flena = "n"
                        l "Please, do..."
                    e "By the way, will you join us at Perry's beach house next week?"

        if v11_perry_invite == 3:
            $ flena = "smile"
            l "Yes, of course. I couldn't refuse such an invitation."
            $ femma = "smile"
            e "I'm really looking forward to it! A summer without visiting the beach doesn't feel like summer at all!"
            l "That's what we bought those bikinis for, right?"
            if emma_bikini:
                $ femma = "flirt"
            e "Absolutely!"
        else:
            if v11_perry_invite == 2:
                l "Yes... I'll be joining after all."
                e "That's great! It'll be fun, you'll see!"
                $ flena = "sad"
                l "I hope so. The situation with Ian right now is rather... uncomfortable."  
            elif v11_perry_invite == 1:
                l "I'm still considering it..."
                e "What's there to consider? Come on, you have to come!"
                $ flena = "sad"
                l "I'd like to, but the situation with Ian right now is rather... uncomfortable."
            else:
                $ flena = "sad"
                l "I don't think so. The situation with Ian right now is rather... uncomfortable."
            $ femma = "sad"
            if v11_lena_breakup:
                e "I'm not exactly sure about what happened between you, but you were friends before you started dating, right?"
                l "Something like that... I'd like to keep things friendly with Ian, but it's... awkward."
            else:
                e "I'm not exactly sure about what happened between you, but it looked like you were trying to keep it friendly..."
                l "Yeah, well... We're trying, but it's... awkward."
            e "These things always are... But it would be a shame if that came in the way of you being able to hang out with us!" 
            if v11_perry_invite == 2:
                $ flena = "n"
                l "I know ... That's why I've accepted Perry's invitation. I think it'll be worth it."
                $ femma = "smile"
                e "That's great! I can't wait to debut our new bikinis!"
            elif v11_perry_invite == 1:
                $ flena = "n"
                l "You're probably right ... I'll give it some more thought."
                $ femma = "smile"
                e "Please do! We have to debut our new bikinis after all, right?"
            else:
                l "You might be right..."
                $ femma = "n"
                e "Of course, I am! Come, you won't regret it."
                e "Besides, we have to debut our new bikinis after all, right?"
                $ flena = "n"
                l "Yes..."

        $ femma = "n"
        e "Join us at the Fortress tonight, too! We're meeting for drinks!"
        $ flena = "n"
        l "I can't, I already told Perry ... I'm working at the club tonight. In fact, I should get going."
        $ femma = "n"
        e "Oh, I see. That sounds cool too! Say hi to Jeremy for me."
        stop music fadeout 3.0
        scene park with long
        scene parknight with long
        pause 1

    ## ALONE #########################################
    else:
        scene mall with long
        play music "music/girls_day.mp3" loop
        if cafe_help:
            $ lena_look = 4
            "I finished my shift and dropped by the mall to check out some shops."
        else:
            "I took my time and enjoyed a slow morning before going down to the mall to check out some shops."
        show lena with short
        if lena_money < 0:
            $ flena = "worried"
            "I only had pocket money left. My bank account wasn't just empty, but in the red..."
            "Spending any amount of money made me feel anxious, but thankfully I could get what I needed at a very cheap price."
        elif lena_money == 0:
            $ flena = "worried"
            "I only had pocket money left. My bank account was completely empty..."
            "Spending any amount of money made me feel anxious, but thankfully I could get what I needed at a very cheap price."
        elif lena_money < 3:
            $ flena = "n"
            "I was short on money, but thankfully I could get what I needed at a cheap price. I could afford the expense."
        elif seymour_desire:
            "It felt good knowing I could afford to buy whatever I fancied..."
        show lena at rig with move
        show eli at lef with short
        eli "Hi! Can I help you with anything?"
        l "I'm just gonna browse a bit and try some clothes..."
        eli "Alright. The fitting rooms are that way; let me know when you've picked something you wanna try on."
        l "Sure, thanks."
        hide eli with short
        show lena at truecenter with move
        label v11aloneshopping:
            $ config.menu_include_disabled = False
            $ greyed_out_disabled = True
            menu:
                "{image=icon_pay.webp}Buy outfits" if lena_money > 0:
                    $ renpy.block_rollback()
                    if persistent.include_disabled:
                        $ config.menu_include_disabled = True
                    $ greyed_out_disabled = False
                    $ flena = "n"
                    show lena at left with move
                    call screen v10clothingshoplena
                    label v11leaveshop_lena2:
                        show lena at truecenter with move
                        jump v11aloneshopping

                "Buy dresses" if v11_lena_dress == 0:
                    $ renpy.block_rollback()
                    $ flena = "n"
                    l "Ivy told me to get something flashy for tonight, but it should also be comfortable to work in."
                    l "I think I'll get one of these cheap dresses... I won't cry over it if it gets lime and vodka spilled all over."
                    show lena at left with move
                    label v11buydressalone:
                        call screen screen_choice(v11buydress)
                        $ v11_lena_dress = _return
                    if v11_lena_dress == 1:
                        l "This black one looks simple and sexy enough. I like it..."
                    elif v11_lena_dress == 2:
                        l "This one looks glamorous and flashy... Should I get it?"
                    elif v11_lena_dress == 3:
                        l "This one looks pretty comfortable. I can't go wrong with this..."
                    elif v11_lena_dress == 4:
                        l "I wonder if red's the color I should go with. It's really eye-catching, that's for sure."
                    menu:
                        "Buy this one":
                            $ renpy.block_rollback()
                            l "It's decided. I'll get this one."
                            show lena at truecenter with move
                            jump v11aloneshopping

                        "I'm not sure":
                            $ renpy.block_rollback()
                            l "On second thought, maybe I should get something different."
                            jump v11buydressalone

                "Buy bikinis" if lena_bikini == 0:
                    $ renpy.block_rollback()
                    $ flena = "n"
                    "I browsed some swimsuits and picked a few to take to the fitting room."
                    show lena at right with move
                    hide lena
                    with long
                    l "Let's see..."
                    label v11trybikinialone:
                        hide lenabikini
                        with short
                        call screen screen_choice(v11bikinitry)
                        $ lena_bikini = _return
                    pause 0.5
                    show lenabikini with short
                    if lena_bikini == 1:
                        $ flena = "smile"
                        l "This one's so much my style... It's in between cute and sexy. I like it."
                    if lena_bikini == 2:
                        $ flena = "smile"
                        l "I love this one... It looks sexy and elegant!"
                    if lena_bikini == 3:
                        $ flena = "flirt"
                        l "This one's so daring...! I've never worn a bikini like this."
                        if lena_lust < 8:
                            l "I wonder if it's too much..."
                    scene v11_bikini1_bg
                    show v11_bikini1
                    if lena_tattoo2:
                        show v11_bikini1_t2
                    if lena_tattoo3:
                        show v11_bikini1_t3
                    if lena_bikini == 1:
                        show v11_bikini1a
                    if lena_bikini == 2:
                        show v11_bikini1b
                    if lena_bikini == 3:
                        show v11_bikini1c
                    with long
                    pause 1
                    "I took a look at myself in the mirror."
                    if lena_bikini == 1:
                        $ flena = "smile"
                        l "Yeah, this one is basically perfect. I should get it..."
                    if lena_bikini == 2:
                        $ flena = "smile"
                        l "I definitely love this one. Perfect to bathe in style!"
                    if lena_bikini == 3:
                        l "Damn, this doesn't leave too much to the imagination..."
                        if lena_lust > 7:
                            l "But that's the point. I look hot as hell, but it's so daring..."
                        else:
                            l "It's hot as hell, but I'm not sure I'm daring enough to wear this in public..."
                    $ flena = "smile"
                    scene mall
                    show lenabikini 
                    with long
                    menu:
                        "Try another bikini":
                            $ renpy.block_rollback()
                            l "Let me try another one..."
                            jump v11trybikinialone
                            
                        "Buy this one":
                            $ renpy.block_rollback()
                            l "I'm sold! I'll get this one."

                    scene mall with long
                    pause 0.5
                    $ flena = "n"
                    show lena with short
                    jump v11aloneshopping

                "{image=icon_charisma.webp}Visit the boutique" if lena_posh > 3 and lena_money > 4:
                    $ renpy.block_rollback()
                    if persistent.include_disabled:
                        $ config.menu_include_disabled = True
                    $ greyed_out_disabled = False
                    $ flena = "flirtshy"
                    l "I want to check the boutique... I have some money to spend, after all!"
                    $ flena = "smile"
                    "I walked into the store, checking out the luxurious dresses, classy shoes, and designer bags."
                    show lena at left with move
                    call screen v10boutique
                    label v11leaveboutique:
                        show lena at truecenter with short
                        jump v11aloneshopping

                "{image=icon_lust.webp}Visit the sex shop" if lena_lust > 4 and lena_money > 0:
                    $ renpy.block_rollback()
                    $ flena = "flirtshy"
                    l "I want to check out the sex shop. Let's see if there's something I should buy..."
                    scene sexshop with long
                    show lena with short
                    $ flena = "smile"
                    show lena at right with move

                    call open_sexshop from _call_open_sexshop_5

                    scene mall with long
                    show lena at truecenter with short
                    jump v11aloneshopping

                "Get going" if lena_bikini != 0 and v11_lena_dress != 0:
                    $ renpy.block_rollback()
                    if persistent.include_disabled:
                        $ config.menu_include_disabled = True
                    $ greyed_out_disabled = False
                    $ flena = "n"
                    l "Alright, I've done everything I wanted to do here. Time to head back..."
                    stop music fadeout 3.0
                    scene street with long
                    scene streetnight with long
                    pause 1

## NIGHT ############################################
    scene lenaroomnight with long
    "I got home just in time to get ready for work."
    show lena with short
    l "It's been a while since I worked a night shift... I don't miss my days at the restaurant at all!"
    if lena_athletics > 4 or lena_lust > 6:
        $ flena = "happy"
        l "A nightclub is completely different from a fancy restaurant, though. It should be fun..."
    else:
        $ flena = "sad"
        l "A nightclub is completely different from a fancy restaurant, but I'm sure it'll be as exhausting, if not more."
    $ flena = "n"
    l "Well, let's see how it goes."
    play music "music/normal_day2.mp3" loop
    # billy
    if billy_model:
        play sound "sfx/sms.mp3"
        l "Mh? A message from Billy..."
        nvl clear
        b_p "Yo, Lena! How's it going, are you busy?"
        l_p "Getting ready to go out. I'm working at the club tonight."
        b_p "No way! That's so cool! Too bad I'm not in town, I'd love to see you dance {image=emoji_dance.webp} {image=emoji_glasses.webp}"
        l_p "I'm just bartending. I'll leave the dancing to Ivy {image=emoji_ups.webp}"
        b_p "Well, I hope you show me some of your moves this summer! I've already booked the place for our first shoot!"
        if seymour_desire:
            $ flena = "sad"
            if billy_trust == 0:
                l "I don't know why I said I was on board with this... I guess it seemed like a good opportunity to make some money..."
            if billy_trust == 2:
                l_p "Sounds great, but I'm afraid I won't be able to make it after all {image=emoji_sad.webp}"
            else:
                l_p "Sorry Billy, but I'm afraid I won't be able to make it after all."
            b_p "What? Why not? {image=emoji_sad.webp} {image=emoji_sad.webp} {image=emoji_sad.webp}"
            l_p "I'm under contract with another party and I can't work for other photographers while that lasts."
            b_p "But I've rented a designer house with a pool and all, perfect for some summer photoshoots!"
        elif billy_trust == 2:
            $ flena = "smile"
            l_p "Cool! I've been looking forward to it! What have you planned?"
        else:
            if billy_trust == 0:
                $ flena = "sad"
                l "I don't know why I said I was on board with this... I guess it seemed like a good opportunity to make some money..."
                $ flena = "n"
            l_p "So you pushed through with your idea?"
            b_p "Of course! I've been working hard to set everything up!"
            b_p "I've rented a designer house with a pool and all, perfect for some summer photoshoots!"
        b_p "I've got a few girls, and with you and Ivy on board, the launch of my brand is gonna be a major hit! Just you wait!"
        b_p "I've rented the place for three days, so we'll have plenty of time to have fun too! {image=emoji_crazy.webp}"
        menu:
            "{image=icon_friend.webp}Sounds awesome!" if billy_trust > 0:
                $ renpy.block_rollback()
                if billy_trust < 2:
                    $ billy_trust = 2
                $ flena = "smile"
                l_p "That sounds awesome! {image=emoji_love.webp}"
                b_p "Hell yeah! I have everything planned, we're gonna kill it, you'll see!"
                b_p "All we have to do is shoot some great content, and me and my team will take care of setting up and promoting the brand."
                b_p "With such sexy girls like you, it's gonna be a hit, one hundred percent guaranteed!"

            "You seem pretty excited":
                $ renpy.block_rollback()
                l_p "You sound pretty excited!"
                b_p "I am! It's gonna be amazing! I have everything planned, how to promote the brand and all that..."
                b_p "All that's needed now is for us to shoot some great content! I'm counting on you girls to do that {image=emoji_crazy.webp}"

            "So what's the plan exactly?":
                $ renpy.block_rollback()
                $ flena = "n"
                l_p "So... What's the plan exactly?"
                b_p "We'll shoot some content for social media! Me and my team will take care of setting up and promoting the brand, you don't have to worry about a thing!"
                l_p "Did you get the photographers yet?"
                b_p "Yeah, don't worry! Ivy told me she'd help with that {image=emoji_crazy.webp}"
        if seymour_desire:
            b_p "So come on, you have to join! Talk to your contractor and ask him for permission! I'm sure we can work something out."
            b_p "If necessary, I promise not to post any of the photos you're in. The important thing is that you come!"
            l_p "I'll see what I can do."
        else:
            b_p "Anyway, I'll let you know the details when the time comes. For now, make sure to keep the third weekend of August free!"
        b_p "It's gonna be awesome, you'll see!"
        if seymour_desire:
            l "What Billy offers is not interesting to me from a business standpoint, but it sounds like a fun vacation..."
            l "I wonder if it's worth joining, after all."
        else:
            if billy_trust == 2:
                l "Cool... Seems like Billy's moving forward with his plan after all. Sounds like it will be fun."
            elif billy_trust == 1:
                l "Seems like Billy's moving forward with his plan after all... Let's see how that goes."
            else:
                l "Seems like Billy's moving forward with his plan after all... I suppose there's no harm in giving it a shot."
        l "Anyway, let's get changed."
    hide lena with short
    pause 0.5
    $ flena = "smile"
    $ lena_makeup = 1
    $ lena_look = "clubdress"
    show lena2 with short
    if v11_lena_dress == 1:
        l "There... This dress was definitely the right choice."
    elif v11_lena_dress == 2:
        l "There... Even though it's cheap, this dress looks really fancy! I love it."
    elif v11_lena_dress == 3:
        l "There... This dress is as comfortable as I expected. Perfect for tonight."
    elif v11_lena_dress == 4:
        l "There... Tonight I'll be the woman in red! I feel so sexy."
    $ config.menu_include_disabled = False
    $ greyed_out_disabled = True
    menu v11dressprepare:
        "{image=icon_charisma.webp}Wear jewelry" if lena_charisma > 4 and lena_necklace != "choker2":
            $ renpy.block_rollback()
            l "I need some jewelry to complete the look. Something simple should do."
            show lena_choker2 with short
            pause 1
            $ lena_necklace = "choker2"
            hide lena_choker2
            if v11_lena_extras == "s":
                $ v11_lena_extras = "es"
            else:
                $ v11_lena_extras = "e"
            jump v11dressprepare

        "{image=icon_lust.webp}Wear stockings" if lena_lust > 5 and lena_extras != "stockings":
            $ renpy.block_rollback()
            $ flena = "flirt"
            l "Let me try fishnet stockings with this look..."
            show lena_lust1_stockings with short
            pause 1     
            $ lena_extras = "stockings"
            hide lena_lust1_stockings
            $ flena = "n"
            if v11_lena_extras == "e":
                $ v11_lena_extras = "es"
            else:
                $ v11_lena_extras = "s"
            jump v11dressprepare

        "On second thought..." if lena_necklace == "choker2" or lena_extras == "stockings":
            $ renpy.block_rollback()
            $ flena = "sad"
            l "On second thought... I'm not convinced. Let's try something else."
            hide lena2 with short
            $ flena = "n"
            $ lena_necklace = 0
            $ lena_extras = 0
            $ v11_lena_extras = "n"
            show lena2 with short
            jump v11dressprepare

        "Ready":
            $ renpy.block_rollback()
            if persistent.include_disabled:
                $ config.menu_include_disabled = True
            $ greyed_out_disabled = False
            $ flena = "n"
            l "Alright, I'm ready."

    if ian_lena_dating:
        "When I picked up my phone I saw I had a text from Ian."
        play sound "sfx/sms.mp3"
        nvl clear
        i_p "{i}What's up? Perry told me you're working tonight so you can't join us.{/i}"
        if seymour_desire or lena_money > 5:
            l_p "{i}Yeah, Ivy wanted me to cover for one of the baristas tonight, so I'm helping them out.{/i}"
        else:
            l_p "{i}Yeah, Ivy wanted me to cover for one of the baristas tonight. I'm rather short on money I can get, so I couldn't say no.{/i}"
        i_p "{i}Of course. But I would've liked to see you tonight!{/i}"
        menu:
            "{image=icon_love.webp}/{image=icon_lust.webp}I can drop by after work" if lena_ian_love or lena_lust > 6:
                $ renpy.block_rollback()
                $ v11_ian_sex = True
                $ flena = "flirt"
                l_p "{i}Well... I can drop by your place after work if you're still awake... {image=emoji_flirt.webp}{/i}"
                i_p "{i}If you're not too tired...{/i}"
                l_p "{i}I'll be sure to save up some energy just for you. I really wanna see you too {image=emoji_smile.webp}{/i}"
                i_p "{i}I can't wait. Good luck tonight.{/i}"
                l_p "{i}Have fun! See you later {image=emoji_kiss.webp}{/i}"
                if ian_lena < 12:
                    call friend_xp ('ian') from _call_friend_xp_1054

            "Me too...":
                $ renpy.block_rollback()
                l_p "{i}Me too... I'd ask you if you want me to drop by after work, but it'll be so late and I'm sure I'll be pretty tired {image=emoji_disgust.webp}{/i}"
                i_p "{i}Don't worry, I understand. We'll have plenty of time to be together soon anyway.{/i}"
                l_p "{i}Yup! I'm really looking forward to spending a few days with you guys at Perry's beach house!{/i}"
                i_p "{i}Good luck tonight!{/i}"
                l_p "{i}Thanks! Have fun with the guys! {image=emoji_kiss.webp}{/i}"

            "I can't tonight":
                $ renpy.block_rollback()
                $ flena = "sad"
                l_p "{i}Sorry, but tonight's totally impossible {image=emoji_disgust.webp}{/i}"
                i_p "{i}I know, I know...{/i}"
                if ian_lena > 2:
                    call friend_xp ('ian',-1) from _call_friend_xp_1055
                    pause 0.7
                $ flena = "n"
                l_p "{i}We'll see each other soon anyway. We'll have plenty of time to be together at Perry's beach house.{/i}"
                i_p "{i}Yeah, I'm looking forward to that{/i}"
                i_p "{i}Good luck tonight. Don't work too hard!{/i}"
                l_p "{i}Have fun with the guys {image=emoji_kiss.webp}{/i}"
                
        $ flena = "n"
        l "Alright, gotta get going..."
    stop music fadeout 3.0
### CLUB NIGHT #################################################################################################################################################
    scene streetnight with long
    pause 1
    $ ivy_look = 1
    $ fivy = "flirt"
    $ fjeremy = "smile"
    scene blazer_outside with long
    play music "music/jeremys_theme.mp3" loop
    show lena with short
    "I arrived at the club about half an hour before opening time. Some folks were starting to flock around the entrance already, but the street was still pretty quiet."
    "I knew it would be buzzing with people in a couple of hours... It was gonna be a busy night, that was for sure."
    show lena at rig with move
    show bouncer at lef with short
    $ bo = "{color=#2E2927}Marcel{/color}"
    bo "Hey there, Lena. You're a bit early today, aren't you?"
    l "I'm working here tonight."
    hide bouncer
    show bouncersmile at lef 
    bo "Oh, really? So you're joining Queen Ivy's dancing crew?"
    l "No, I'll be bartending."
    if lena_fty_bbc:
        bo "That's too bad. I'm sure watching you work the pole would be something to see."
        $ flena = "smile"
        l "Maybe some other time. Tonight I'm working the trenches."
        bo "Yeah, you'll have your hands full."
    else:
        bo "I see. You'll have your hands full tonight."
    hide bouncersmile
    show bouncer at lef 
    bo "You're part of the Blazer family tonight, and my job here is to take care of you."
    bo "Ask Jeremy to give you an earpiece. If you have any kind of trouble, don't hesitate to let me know."
    $ flena = "smile"
    l "I'll do that. Thanks!"
    if lena_fty_bbc:
        hide bouncer
        show bouncersmile at lef 
        bo "It's my pleasure, baby."
    scene blazer with long
    $ flena = "n"
    "Inside, the crew were getting everything set for the opening. The nightclub felt very different with the lights on and no music blasting through the speakers."
    show lena at rig with short
    "I went to the bar to find Jeremy."
    show jeremy at lef with short
    if v7_bbc == "lena":
        $ flena = "flirt"
        l "Hi!"
        $ fjeremy = "happy"
        j "Hey, Lena! Ivy told me you'd be working here tonight!"
        l "Yup... It was an offer I couldn't refuse."
        $ fjeremy = "flirt"
        "Jeremy picked up on my flirtatious attitude and responded in kind."
        j "Oh, yeah? And why's that?"
        l "What do you think?"
        j "I don't know... Maybe you wanted to see me?"
        if v8_jeremy_sex or v10_jeremy_3some:
            if v10_jeremy_3some:
                l "Bingo. We haven't seen each other since the threesome with Louise... You haven't been dropping by our place anymore!"
                $ fjeremy = "n"
                j "Yeah... Louise hasn't invited me since then. We've been meeting outside lately."
                $ flena = "serious"
                "So that was the reason... I was sure she was doing it on purpose, keeping Jeremy away from me."
            else:
                l "Bingo. We haven't seen each other since... when? You haven't been dropping by our place anymore..."
                $ fjeremy = "n"
                j "Yeah... Louise hasn't invited me in a while. We've been meeting outside lately."
                $ flena = "sad"
                "So that was the reason... Did Louise suspect something after all? It looked like she was keeping Jeremy away from me..."
            $ flena = "slut"
            "Well, she couldn't prevent me from being close to Jeremy tonight."
        elif v8_jeremy_flirt:
            l "I'm not telling, but you could be onto something..."
            j "Good to know!"
        $ flena = "n"
    else:
        l "Hey..."
        j "Hey, Lena! Ivy told me you'd be working here tonight!"
        if lena_fty_bbc:
            $ flena = "shy"
            l "Yeah, well... She said you needed some help."
            if v7_game:
                "I couldn't help but remember the scene I saw that night at Ivy's place every time I met Jeremy."
            elif v6_spy:
                "It was hard not thinking about the pictures I had found on Louise's phone every time I met Jeremy."
            elif v3_bbc:
                "I couldn't help but remember the scene I saw that night between Louise and Jeremy every time I met him."
            "My friend's lips wrapped around that massive cock of his... It was so lewd, like one of those porn videos I had watched sometimes..."
            "I tried to put those thoughts away and focus on the task at hand. I had come to work tonight."
        else:
            l "Yeah. She said you needed some help."
            $ fjeremy = "happy"
            j "And she got me the best help one can ask for!"
            l "We'll see about that..."
            if lena_jeremy > 6:
                "Despite the drama that had ensued between him and Louise, I thought Jeremy was a pretty cool guy."
                "Having him as a co-worker tonight should be fun."
            else:
                "Jeremy wasn't my favorite person, especially considering how he had treated Louise."
                "I ended up getting involved in their drama and regretting it, but thankfully that was over with now."
                "Tonight he was just another coworker, and I'd treat him as such."
            
    $ fjeremy = "smile"
    j "So, let me show you the ropes! Have you worked at a bar before?"
    $ flena = "n"
    l "At the café and at a restaurant, but never at a nightclub."
    j "Alright, then you probably know what you're doing...! Let me show you the setup we have here."
    j "Here are the fridges, there's ice in this one and beer and soft drinks in that one. We store the glasses here, and we leave the used ones in these crates..."
    show jeremy at lef3
    show lena at rig3
    with move
    $ fmike = "smile"
    $ mike_look = 2
    $ mike_extras = "chain"
    show mike with short
    mk "So you're the new bartender? The bar will be crawling with people tonight."
    if lena_mike_dating:
        $ flena = "flirt"
        l "Oh, yeah? And why's that?"
        $ fmike = "flirt"
        mk "Because everyone will want to get a chance to talk to such a pretty waitress."
        $ fjeremy = "n"
        l "\"Pretty\", huh? I was going for \"hot\" tonight."
        mk "Oh, you definitely got it right... But still, how pretty you are is what stands out the most."
        $ flena = "shy"
        l "You really know how to sweet-talk a girl, huh?"
        "It was hard holding myself back when I was next to Mike, especially if he flirted like that..."
        if lena_cheating:
            "But I knew I had to play it cool. I couldn't risk Ian learning about my side relationship with him."
        else:
            "But I knew I had to play it cool. The less people knew about our fling, the better."
    else:
        l "You think so?"
        mk "Of course. Everyone will want to get a drink from such a pretty waitress."
        if lena_mike_over:
            $ flena = "shy"
            "I had good reasons to end my fling with Mike, but that didn't prevent him from flirting with me."
            "As long as things didn't escalate beyond that point, I didn't mind. It felt kind of nice, actually."
    $ fmike = "smile"  
    if mike_collab:
        mk "Did you make any progress with the beat I sent you?"
        $ flena = "n"
        l "I still haven't had time to properly get into it, sorry... My life's been pretty chaotic lately."
        $ flena = "smile"
        l "But I'll share what I've got with you once I can devote some proper time to it!"
        mk "I can't wait to see what you've got for me."
    else:
        mk "Can I try to convince you to lend me your voice one more time? I have this sick beat and I think you'd be perfect to give it some lyrics..."
        $ flena = "n"
        l "Sorry, my life's been chaos lately... I'm not sure I can devote the time needed to make justice to your music."
        mk "I think you're making a bigger deal out of it than it really is, but okay. If at any point you feel like doing it, let me know."
    show jeremy at left
    show lena at right
    show mike at lef
    with move
    show ivy at rig with short
    v "What's up, sexy people? Ready for tonight?"
    $ fjeremy = "happy"
    j "I'm always set to roll, baby!"
    v "Of course, you are..."
    if v11_lena_dress == 1:
        v "You look killer in that dress, Lena! Classic black never disappoints."
    elif v11_lena_dress == 2:
        v "You look like a Hollywood star in that dress, Lena! You like to keep it flashy even when working."
    elif v11_lena_dress == 3:
        v "I love the sporty look, Lena! Perfect for tonight, right?"
    elif v11_lena_dress == 4:
        v "Wow, what's up with that dress, Lena? You want to take away eyeballs from us dancers tonight, don't you?"
    if lena_extras == "stockings":
        v "Loving the stockings too, by the way. You know how to get better tips, huh?"
    $ fivy = "flirt"
    v "What about you, Mike? Have you prepared a cool session for us tonight?"
    mk "I have some saucy beats... Including the one you asked for."
    $ flena = "sad"
    $ fjeremy = "n"
    v "You liked the dance routine that goes with it, huh? Be sure to play that one when I get on stage!"
    mk "I won't let you down."
    hide ivy
    show ivy2 at rig
    with short
    $ flena = "worried"
    show ivy2 at centerrig with move
    $ fmike = "flirt"
    "Ivy slid her index finger playfully down Mike's chest before turning around."
    v "Oh, I'm sure you won't."
    v "Gotta get changed! Catch you later!"
    hide ivy2 with short
    $ fmike = "smile"
    mk "I gotta check if the sound system's all good and ready. Have a good one, guys."
    hide mike
    with short
    show lena at rig
    show jeremy at lef
    with move
    j "Lucky guy..."
    l "What do you mean, \"lucky guy\"?"
    $ fjeremy = "sad"
    $ flena = "sad"
    l "Do you think he and Ivy...? Have they hooked up?"
    j "Wha-?{w=0.3} No, I mean..."
    $ fjeremy = "n"
    j "I don't know... They've been getting along lately, though."
    l "What about Mike's girlfriend? He still has one, right?"
    $ fjeremy = "sad"
    j "Yeah, um... As far as I know, he does have one."
    $ flena = "worried"
    l "\"As far as you know?\""
    $ fjeremy = "sad"
    j "No, I mean, I do know for a fact he has a girlfriend..."
    j "I don't know if anything happened between him and Ivy, though. But damn, I wish she looked at me that way too."
    if lena_mike_dating:
        $ flena = "serious"
        l "Yeah, I noticed it too..."
        "Ivy knew full well about my fling with Mike, and how much I liked him..."
        if lena_ivy > 7:
            $ flena = "sad"
            "I knew I shouldn't jump to conclusions, but I couldn't help wondering if..."
        elif lena_ivy > 3:
            $ flena = "worried"
            "She wouldn't hook up with Mike and not tell me about it, would she...?"
        else:
            "What the hell was going on? Was she stepping into my territory behind my back?"
    else:
        $ flena = "sad"
        l "Yeah, I got that impression too..."
        if lena_mike_sex:
            "I was in no position to judge, though. I had hooked up with Mike too, not that long ago."
            if lena_ivy < 4:
                $ flena = "serious"
                "Was that the reason Ivy hadn't told me anything about it? I felt she owed me some explanations... once again."
            else:
                "Was that the reason Ivy hadn't told me anything about it? Maybe Jeremy and I were just jumping to conclusions..."
        else:
            "I wouldn't be surprised if Ivy had actually slept with Mike. She probably couldn't care less that he had a girlfriend..."
            if lena_ivy > 7:
                $ flena = "n"
                "I wasn't gonna judge her for it, though. She was one of my best friends, after all..."
            elif lena_ivy > 3:
                $ flena = "sad"
                "She was my friend for many reasons, but that was something I couldn't really get behind..."
            else:
                $ flena = "serious"
                "She was my friend, but she could be such a bitch sometimes."
# ivy jeremy
    $ fjeremy = "n"
    j "Can I ask you something?"
    if lena_mike_dating:
        $ flena = "blush"
        l "Um... Sure..."
    else:
        $ flena = "n"
        l "Sure."
    j "Ivy's your best friend, right? Has she ever, you know..."
    $ flena = "worried"
    j "Has she ever mentioned anything about me?"
    if v8_jeremy_sex or v10_jeremy_3some:
        l "Don't tell me you're still trying to hook up with her?"
        j "Well, she's been rather cold toward me lately, but I know she was interested at some point..."
        j "Do you know what changed? Is it because she hates that I hooked up with Louise?"
        $ flena = "flirt"
        l "Yeah, because of that, and because you're hooking up with her best friend, too!"
        if v10_jeremy_3some:
            l "Don't tell me you're not satisfied with me and Louise, and what's more, both at the same time!"
        else:
            l "I think you have your hands full already with Louise and me... How many girls do you need?"
        $ flena = "slut"
        $ fjeremy = "smile"
        l "You can't possibly be this greedy..."
        j "I can't argue with that... But you know..."
        j "Would it bother you if Ivy and I happened to hook up?"
        menu:
            "It wouldn't":
                $ renpy.block_rollback()
                $ lena_ivy_jeremy = True
                $ flena = "n"
                l "No, I don't think it would... But there's a stretch from that to helping you get in her panties!"
                j "All I'm asking is for you to bring up the subject with her... See how she feels about me, and if you learn anything useful..."
                $ fjeremy = "happy"
                j "Well, I could use a tip or two!"
                l "No promises..."
                if lena_jeremy < 12:
                    call friend_xp ('jeremy') from _call_friend_xp_1056
                l "Now, finish telling me all I need to know before we get to work."
                $ fjeremy = "smile"
                j "Sure!"
        
            "{image=icon_mad.webp}You're mine":
                $ renpy.block_rollback()
                l "Sorry, but that ain't gonna happen. You're mine."
                $ fjeremy = "sad"
                j "Oh. I see..."
                l "Don't give me that face. Aren't you happy with me?"
                $ fjeremy = "flirt"
                j "More than happy..."
                $ flena = "smile"
                l "Now, finish telling me all I need to know before we get to work."
                $ fjeremy = "smile"
                j "Sure!"
    else:
        $ flena = "sad"
        l "She did, and I don't think it was good."
        $ fjeremy = "sad"
        j "No way. Are you for real? What did she say?"
        l "She wasn't a fan of the way you handled the situation with Louise. Or the fact that you let her think she was your actual girlfriend."
        j "That thing really was a big slip, huh... Do you think there's a chance for me to fix it?"
        j "I know she was interested in me at some point. There must be something I can do to rekindle that flame!"
        menu:
            "I could talk to her for you":
                $ renpy.block_rollback()
                $ lena_ivy_jeremy = True
                $ flena = "smile"
                l "So you want me to talk to her on your behalf, right?"
                j "All I'm asking is for you to bring up the subject with her... See how she feels about me, and if you learn anything useful..."
                $ fjeremy = "happy"
                j "Well, I could use a tip or two!"
                $ flena = "n"
                l "No promises..."
                if lena_jeremy < 12:
                    call friend_xp ('jeremy') from _call_friend_xp_1057
                l "Now, finish telling me all I need to know before we get to work."
                $ fjeremy = "smile"
                j "Sure!"

            "{image=icon_mad.webp}It ain't gonna happen":
                $ renpy.block_rollback()
                $ flena = "n"
                l "Forget about it, Jeremy. It ain't gonna happen."
                $ fjeremy = "sad"
                l "Now, finish telling me all I need to know before we get to work."
                $ fjeremy = "n"
                j "Yeah... Sure."
# tutorial
    $ flena = "n"
    $ fjeremy = "smile"
    j "So, the job's pretty simple. Listen to what the guy or gal wants and serve it to them."
    l "Yeah, I got that much figured out already, thanks."
    j "You need to be quick, though! The bar gets crowded fast, especially during the midpoint of the night."
    j "You shouldn't have people waiting for too long. They get mad!"
    l "What are the house rules on how much alcohol to serve in mixed drinks?"
    $ fjeremy = "n"
    j "Don't go overboard... The boss doesn't like it when we deplete the stocks too fast. The less you pour, the more money the club makes with each drink."
    j "People tend to be happier if you're generous, which sometimes gets you a tip. But people also get drunker, faster... Which can be good or bad."
    l "Dealing with drunks sounds like extra work."
    $ fjeremy = "smile"
    j "Yeah, pretty much..."
    if v8_jeremy_sex or v10_jeremy_3some or lena_fty_bbc:
        jump v11bbcalley
    else:
        stop music fadeout 2.0
        j "That's about everything, I guess... They'll let people inside soon. Let's get ready!"
        scene blazer with long
        show lena with short
        jump v11bartending

## BBC ##############################################################
label v11bbcalley:
    stop music fadeout 3.0
    j "Alright. Last thing: we stockpile crates of bottles and used glasses in the back alley. Come, I'll show it to you..."
    scene street2night with long
    "I followed Jeremy through a back door as he continued to explain the logistics of the club."
    show lena at rig
    show jeremy at lef
    with short
    j "If we start running low on beers, grab one of those boxes to restock the fridge. We pile up empty bottles in those crates over there..."
    j "This is also where we take breaks. If the bar's not too busy and you need a smoke or to get some air, you can take five or ten minutes."
    l "Got it..."
    # jeremy
    if v8_jeremy_sex or v10_jeremy_3some:
        menu:
            "{image=icon_lust.webp}Flirt with Jeremy": #findme if v8_jeremy_sex == False Lena doesn't care about Louise at all?
                $ renpy.block_rollback()
                label gallery_CH11_S22:
                    if _in_replay:
                        call setup_CH11_S22 from _call_setup_CH11_S22
                $ v11_bbc = "jeremy"
                $ flena = "flirt"
                play music "music/sex.mp3" loop

            "{image=icon_friend.webp}Marcel mentioned an earpiece...":
                $ renpy.block_rollback()
                l "Marcel mentioned something about an earpiece..."
                $ fjeremy = "sad"
                j "Oh, that's right! It's important, we get instructions over there, and you can use it if there's trouble..."
                show lena at rig3
                show jeremy at lef3
                with move
                play music "music/tension.mp3" loop
                show bouncer with short
                bo "And I'll come to clean up the trash. Have you given Lena the earpiece yet?"
                jump v11marceltalk
        
            "Let's get to work":
                $ renpy.block_rollback()
                l "We can get to work when you want."
                j "Perfect! Let's go."
                scene blazer with long
                show lena with short
                jump v11bartending

        if v11sms2_jeremy:
            l "So... Did you enjoy the last picture I sent you?"
            $ fjeremy = "flirt"
            j "Hell yeah. It was so damn hot..."
            l "You didn't send a picture back. That was so mean..."
            $ fjeremy = "n"
            j "I was busy that night..."
            l "Yeah, I know. Busy with work, just like tonight."
            l "The only difference is that tonight I'm here with you..."
        else:
            l "So... We're alone over here. We don't get many chances like this..."
        $ fjeremy = "flirt"
        j "That's true... Too bad the setting's not the best."
        l "The best for what?"
        j "For, you know... The things we did last time at your place."
        l "Who said we can't do those things here?"
        $ fjeremy = "sad"
        "Jeremy looked around, visibly surprised."
        j "You mean you want to...? Here?"
        stop music fadeout 2.0
        $ flena = "slutshy"
        l "I want to see your cock."
        play music "music/sex_bingo.mp3" loop
        #handjob
        scene v11_bbc2a with long
        "I knelt down, undid Jeremy's zipper, and licked my lips as I pulled out his manhood."
        scene v11_bbc2_animation with fps
        pause 4.0
        j "Damn, Lena...! Somebody might walk in on us..."
        l "Are you nervous? Don't tell me you've never done something like this..."
        j "Not in here, no..."
        l "Well, it's getting harder... Seems like you're more turned on than nervous."
        "That was also true for me, even more so. Having such an exceptional cock in my hands made me unable to hold back."
        if lena_fty_show == False and lena_lust < 6:
            "Normally I wouldn't dare to do something like this in public, but..."
        "I couldn't let a chance like this slip..." 
        menu v11jeremybbc:
            "{image=icon_lust.webp}Take a picture" if (v11_bbc_pic == False and lena_lust > 7) or (v11_bbc_pic == False and v11sms2_jeremy):
                $ renpy.block_rollback()
                $ v11_bbc_pic = True
                if v11sms2_jeremy: 
                    l "Wait, let me take a picture..."
                    j "You want a pic? I already sent you a couple, didn't I...?"
                    l "Yeah, but I wasn't in it."
                elif v8_jeremy_flirt:
                    l "Wait, let me take a picture..."
                    j "You want a pic? I already sent you one, didn't I...?"
                    l "Yeah, but I wasn't in it."
                else:   
                    l "Wait... Let's take a selfie."
                    j "What? A selfie, now...?"
                    l "Yeah. Me and your cock."
                play sound "sfx/giggle.mp3"
                scene v11_bbc1
                if v11_lena_dress == 2:
                    show v11_bbc1_charisma
                elif v11_lena_dress == 4:
                    show v11_bbc1_lust
                if lena_tattoo2:
                    show v11_bbc1_t2
                if lena_necklace == "choker2":
                    show v11_bbc1_choker
                with long
                pause 1
                "I pulled up my phone and got closer to Jeremy, holding his cock against my face."
                play sound "sfx/camera.mp3"
                with flash
                $ lena_jeremy_pics.append("v12_bbc_phone1.webp")
                "Seeing myself on the phone screen as I snapped some shots was incredibly thrilling and arousing."
                "So far, I had been masturbating to pictures and videos of other girls enjoying such monster cocks, but this time I was the star." #findme - bbc porn plot hole?
                "Jeremy's dark meatstick contrasted so nicely with my pale skin, my clear blue eyes, and my pretty lips..."
                if stalkfap_pro > 1 and seymour_desire == False:
                    "I was getting some excellent images to pleasure myself with later, and I could even share them with some of my Stalkfap subscribers for some extra cash..."
                    "But I wanted to enjoy the present moment and the icon of my fantasies, now in my power, hot and throbbing."
                else:
                    "I was getting some excellent images to pleasure myself with later..."
                    "But I wanted to enjoy the present moment and the icon of my fantasies, now in my power, hot and throbbing."
                jump v11jeremybbc

            "Suck it":
                $ renpy.block_rollback()
                "Teasing Jeremy was so fun, but I couldn't endure it anymore. I wanted to satisfy my lust for his big, black cock."
                if v11_bbc_pic:
                    scene v11_bbc3b
                else:
                    scene v11_bbc3
                if v11_lena_dress == 2:
                    show v11_bbc3_charisma
                elif v11_lena_dress == 4:
                    show v11_bbc3_lust
                if lena_necklace == "choker2":
                    show v11_bbc3_choker
                if lena_tattoo2:
                    show v11_bbc3_t2
                with long
                play sound "sfx/bj1.mp3"
                pause 1
                l "Mhhhh..."
                if lena_lust < 10:
                    call xp_up ('lust') from _call_xp_up_907
                j "Ohhh, fuck..."
                "My entire body shivered when my lips made contact with Jeremy's glans, kissing and enveloping it."
                if v11_bbc_pic:
                    "I needed to take a picture of this, too... I channeled the pornstar in me, savoring the treat I had secured for myself." 
                    play sound "sfx/camera.mp3"
                    with flash
                else:
                    "How delightful was this treat I had secured for myself... And how convenient that Jeremy was my co-worker tonight."
                "My pussy tingled, hot, and moist, making me feel the thrill and pleasure of acting like a slut."
                "My bartending shift hadn't even started and I was already on my knees sucking Jeremy's enormous, juicy cock in a back street..."
                "I knew tonight would be fun." 
                if v11_bbc_pic:
                    scene v11_bbc3
                    if v11_lena_dress == 2:
                        show v11_bbc3_charisma
                    elif v11_lena_dress == 4:
                        show v11_bbc3_lust
                    if lena_necklace == "choker2":
                        show v11_bbc3_choker
                    if lena_tattoo2:
                        show v11_bbc3_t2
                    with long
                play sound "sfx/bj5.mp3"
                j "Jesus, Lena...! You're driving me crazy over here...!"
                "Jeremy was now sporting a full hard-on, and I felt shiver under my devoted caresses."
                "Sucking him felt so good...!"
                scene v11_bbc2_animation with long
                l "Seems like you're not nervous anymore..."
                j "I feel I could cum at any moment..."
                l "Already? Seems like you really enjoy me..."
                "I was hypnotized by the view. The stocky, heavy shaft, the thick veins bulging under the dark skin, the swollen glans..."
                "What an incredible icon of manhood..."
        
            "Keep stroking it":
                $ renpy.block_rollback()
                if v11_bbc_pic:
                    scene v11_bbc2_animation with long 
                pause 3.0
                if lena_lust < 8:
                    call xp_up ('lust') from _call_xp_up_908
                "I was hypnotized by the view. The stocky, heavy shaft, the thick veins bulging under the dark skin, the swollen glans..."
                "What an incredible icon of manhood..."

        # findme jeremy extension
        scene v11_bbc4_bg
        if v11_lena_dress == 2:
            show v11_bbc4_jeremy_charisma_animation
        elif v11_lena_dress == 4:
            show v11_bbc4_jeremy_lust_animation
        else:
            show v11_bbc4_jeremy_base_animation
        with long
        "Standing up, I slid his cock between my legs, sensing its warm firmness, hard but tender."
        if lena_jeremy_sex or v10_jeremy_3some:
            l "I want to feel you inside of me again... Last time I couldn't get all of it to fit."
            l "I don't know if that's even possible, but thinking about it makes me so wet..."
        else:
            l "I still wonder if this thing of yours would fit inside of me. I have my doubts, but it makes me so wet that it might be possible..."
        "I moved my hips, jerking Jeremy off with my soft thighs."
        "His shaft was trapped between them, rubbing against my pussy from the base to the tip, sending shivers across my body."
        "I was so turned on...!"
        menu v11jeremy:
            "{image=icon_love.webp}Kiss Jeremy" if not v11_jeremy_kiss:
                $ renpy.block_rollback()
                $ v11_jeremy_kiss = True
                scene v11_bbc4_bg_jeremy
                if v11_lena_dress == 2:
                    show v11_bbc4_bg_charisma
                elif v11_lena_dress == 4:
                    show v11_bbc4_bg_lust
                if v11_lena_dress == 2:
                    show v11_bbc4_jeremy_charisma_animation
                elif v11_lena_dress == 4:
                    show v11_bbc4_jeremy_lust_animation
                else:
                    show v11_bbc4_jeremy_base_animation
                with long
                "I looked into Jeremy's eyes and pressed my lips on his."
                "He was surprised by it, but soon they parted and our tongues met for the first time."
                "I savored his taste as we made out slowly and passionately while I continued to stroke him with my thighs."
                if lena_jeremy < 12:
                    call friend_xp ('jeremy') from _call_friend_xp_1144
                jump v11jeremy

            "{image=icon_lust.webp}Make him cum" if lena_lust > 5:
                $ renpy.block_rollback()
                label v12jeremycum:
                    l "I wish I could fuck you right now... Would you like that?"
                j "Y-{w=0.3}yeah..."
                l "I think you're liking this almost as much. Can you feel how wet and hot my pussy is right now?"
                "I felt Jeremy shiver and tense up as I whispered lewd words into his ear."
                l "Does it feel good? Are you gonna cum?"
                l "Cum for me, Jeremy... I want to make you cum."
                j "Fuck...!"
                if v11_jeremy_kiss:
                    scene v11_bbc4_bg_jeremy
                    if v11_lena_dress == 2:
                        show v11_bbc4_bg_charisma
                    elif v11_lena_dress == 4:
                        show v11_bbc4_bg_lust
                else:
                    scene v11_bbc4_bg
                if v11_lena_dress == 2:
                    show v11_bbc4c_jeremy_charisma
                elif v11_lena_dress == 4:
                    show v11_bbc4c_jeremy_lust
                else:
                    show v11_bbc4c_jeremy_base
                show v11_bbc4_cum
                with flash
                j "Ahhh!!{w=0.6}{nw}" with vpunch
                with vpunch
                pause 0.6
                with vpunch
                pause 0.6
                "To my satisfaction, I felt him shake and tremble as he shot his load between my thighs."
                "That only fanned the fire of my arousal..."
                bo "Hey, you two!"
                stop music
                $ jeremy_look = "cock"
                $ fjeremy = "surprise"
                $ flena = "surprise" 
                scene street2night
                show jeremynude at rig3
                show bouncer at left
                show lenaunder at rig2
                with vpunch
                show lenaunder at center with move
                j "Oh, shit!"
                $ flena = "blush"
                hide lenaunder
                show lena
                with short
                bo "Come on, at least wait until the shift's over to get all frisky like that. You know you shouldn't be doing this here."
                $ fjeremy = "sad"
                j "Sorry, man... Things just got a bit crazy..."
                hide bouncer
                show bouncersmile at left
                bo "It's alright, we've all done crazy shit at one time or another. But get your cock back in your pants; we're about to let people in."

            "Stop":
                $ renpy.block_rollback()
                $ flena = "flirtshy"
                $ fjeremy = "sad"
                $ jeremy_look = "cock"
                stop music fadeout 3.0
                scene street2night
                show jeremynude at lef
                show lenaunder at rig
                with long
                "I took a step back and looked at Jeremy's massive boner with a flirty smile."
                l "So big..."
                hide lenaunder
                show lena at rig
                with short
                "I recomposed my dress, delighting myself in Jeremy's flustered and somewhat confused expression."
                $ fjeremy = "n"
                j "So, um... What now?"
                $ flena = "slut"
                l "Now...? I want your cock to stay like that all night long, thinking about me."
                if lena_charisma < 10:
                    call xp_up ('charisma') from _call_xp_up_1008
                show bouncer at left with short
                bo "Hey, you two..."
                $ flena = "shy"
                $ fjeremy = "sad"
                bo "Really? Come on, Jeremy, get your cock back in your pants."
                bo "You know you shouldn't be doing this here. At least wait until the shift's over to get all frisky like that."
                hide bouncer
                show bouncersmile at left
                bo "It's alright, we've all done crazy shit at one time or another. But get ready; we're about to let people in."

        hide jeremynude
        show jeremy at rig3
        with short
        $ fjeremy = "n"
        j "Uh, yeah."
        hide bouncersmile
        show bouncer at left
        $ renpy.end_replay()
        $ gallery_unlock_scene("CH11_S22")
        bo "And here's your earpiece, Lena... Use it in case you need me to break up a situation."
        bo "I hope next time you're not the one stirring up trouble, though."
        l "Sure... Sorry."
        hide bouncer with short
        j "Come on... Let's man the bar."
        scene blazer with long
        $ flena = "sad"
        show lena with short
        jump v11bartending
    # marcel
    elif lena_fty_bbc:
        show jeremy at lef3
        show lena at rig3 
        with move
        play music "music/tension.mp3" loop
        show bouncer with short
        bo "Have you given Lena the earpiece yet?"
        $ fjeremy = "sad"
        j "Oh, that's right! It's important, we get instructions over there, and you can use it if there's trouble..."
        hide bouncer
        show bouncersmile
        bo "And I'll come to clean up the trash."
        label v11marceltalk:
            $ fjeremy = "smile"
        j "I'll go get you one."
        hide jeremy 
        hide bouncer
        show bouncersmile
        with short
        show bouncersmile at lef
        show lena at rig
        with move
        bo "You're looking gorgeous tonight, Lena... But you always do."
        menu:
            "I'm glad you noticed":
                $ renpy.block_rollback()
                $ flena = "flirtshy"
                l "Oh, really? Well, I'm glad you noticed..."
                call friend_xp ('marcel') from _call_friend_xp_1058
                bo "How could I not? Same goes for everyone in the club... You know you're the type of girl who makes heads turn."
                if lena_extras == "stockings":
                    bo "Especially when you dress like that... Damn, you're hot."

            "Thank you":
                $ renpy.block_rollback()
                l "Thank you..."
            
            "I should get ready for work...":
                $ renpy.block_rollback()
                stop music fadeout 2.0
                l "Um... I should get ready for work. They'll let people in soon."
                hide bouncersmile
                show bouncer at lef
                bo "Right. Well, don't let me keep you."
                call friend_xp ('marcel',-1) from _call_friend_xp_1059
                $ lena_marcel = 2
                hide lena with short
                scene blazer with long
                show lena with short
                jump v11bartending

        hide bouncersmile
        label gallery_CH11_S23:
            if _in_replay:
                call setup_CH11_S23 from _call_setup_CH11_S23
        show bouncer at lef
        bo "By the way... Can I ask you something?"
        bo "I've heard this rumor about you..."
        $ flena = "worried"
        l "A rumor? What rumor?"
        hide bouncer
        show bouncersmile at lef
        bo "You see... A birdie told me you have something for big tools. Big dark tools, more specifically."
        hide lena
        show lena2 at rig 
        with short
        menu:
            "{image=icon_lust.webp}It's true..." if lena_lust > 6:
                $ renpy.block_rollback()
                label v11marceltrue:
                    $ v11_bbc = "marcel"
                $ flena = "shy"
                "I saw where this was leading, and it was equally unexpected and thrilling. I had to confess..."
                l "Well, busted... I'll admit it: it's true."
                bo "Did you get the chance to try one yet?"
                if v8_jeremy_sex or v10_jeremy_3some:
                    l "I did, but only one, and just a couple of times so far..."
                elif v7_bbc == "lena":
                    l "Barely..."
                else:
                    l "No, not yet..."
                if ian_lena_couple:
                    bo "I've also heard you have a boyfriend, though..."
                    $ flena = "blush"
                    l "Yeah... That's also true..."
                    bo "And is he the jealous type? Or rather..."
                    bo "Are you a good girl, or a naughty one?"
                    menu:
                        "{image=icon_lust.webp}Cheat on Ian" if lena_lust > 7:
                            $ renpy.block_rollback()
                            $ flena = "slut"
                            stop music fadeout 3.0
                            l "Actually, I'm a naughty girl... A very, very naughty one."
                            if lena_cheating:
                                "Despite me already cheating on Ian, I didn't want to add another sin to my list..."
                                "But I couldn't pass up this opportunity either."
                            else:
                                $ lena_cheating = True
                                "I didn't want to cheat on Ian, but I couldn't pass up this opportunity either..."
                            bo "In that case, I believe you'll enjoy what I have for you."
                    
                        "Be faithful":
                            $ renpy.block_rollback()
                            $ v11_bbc = False
                            $ flena = "sad"
                            stop music fadeout 3.0
                            l "I'm a good girl... Or at least I try to be."
                            hide bouncersmile
                            show bouncer at lef
                            if lena_cheating:
                                "I was saying that despite cheating on Ian, but... I didn't want to add another sin to the list."
                            bo "I understand... I think being a good girl will end up boring you to death, but who am I to say?"
                            call friend_xp ('marcel',-1) from _call_friend_xp_1060
                            $ lena_marcel = 3
                            l "I think it's best if I get ready for work. They'll let people in soon."
                            bo "Of course... Don't let me keep you."
                            hide lena with short
                            scene blazer with long
                            show lena with short
                            jump v11bartending
                else:
                    if v8_jeremy_sex or v10_jeremy_3some:
                        bo "So, are you curious about experiencing another big, dark tool? Because if you are, I might have one for you..."
                    elif v7_bbc == "lena":
                        bo "That's too bad. But listen... Since you satisfied my curiosity, I'll satisfy yours."
                        bo "If you're interested in experimenting with another big, dark tool, I might have one for you..."
                    else:
                        bo "That's too bad. But listen... Since you satisfied my curiosity, I'll satisfy yours."
                        bo "If you're interested in experimenting with a really big, dark, tool; I might have one for you..."
                    
            "Where did you hear that?":
                $ renpy.block_rollback()
                $ flena = "blush"
                l "What? Where did you hear that?"
                bo "You know, people talk... Apparently, you were boasting about it a few nights ago, during Ivy's birthday I believe."
                l "That's..."
                bo "So, what I wanna know... Is it true or not?"
                menu:
                    "{image=icon_lust.webp}It's true..." if lena_lust > 6:
                        $ renpy.block_rollback()
                        jump v11marceltrue

                    "You've been misinformed":
                        $ renpy.block_rollback()
                        $ flena = "worried"
                        jump v11rejectmarcel
        
            "You've been misinformed":
                $ renpy.block_rollback()
                label v11rejectmarcel:
                    "I wasn't expecting Marcel to suddenly hit on me like that... I politely stopped him in his tracks."
                l "I'm sorry, but you've been... misinformed."
                hide bouncersmile
                show bouncer at lef
                call friend_xp ('marcel',-1) from _call_friend_xp_1061
                $ lena_marcel = 3
                bo "Oh, is that so? That's a pity... I was hoping for the rumor to be true."
                if ian_lena_couple:
                    l "Even if it was... I have a boyfriend, so..."
                    hide bouncer
                    show bouncersmile at lef
                    bo "Of course, of course. Don't blame me for being curious."
                    $ flena = "n"
                    l "Well, I hope your curiosity has been... satisfied."
                else:
                    $ flena = "n"
                    l "I hope that satisfied your... curiosity."
                stop music fadeout 2.0
                l "I think I should get ready for work. They'll let people in soon."
                hide bouncersmile
                show bouncer at lef
                bo "Of course, don't let me keep you."
                hide lena with short
                scene blazer with long
                show lena with short
                $ renpy.end_replay()
                jump v11bartending

        $ flena = "flirt"
        l "Is that so? Can I see it?"
        bo "Be my guest."
        "Marcel stood nonchalantly before me. He brought up the offer, but now expected me to take charge."
        play music "music/sex_bingo.mp3" loop
        #handjob
        scene v11_bbc2a_marcel with long
        "I wasn't about to disappoint him."
        "I knelt down, undid Marcel's zipper, and pulled out his manhood."
        l "Oh, wow...!"
        scene v11_bbc2_marcel_animation with fps
        pause 4.0
        "I marveled at the cock I had in my power as I stroked the shaft."
        "Even though it wasn't completely hard, it was everything I had been expecting, and more."
        if v7_bbc == "lena":
            "So far, the biggest one I had tasted was Jeremy's... And Marcel proved to be a worthy rival."
            "They were both pretty similar, Jeremy's tool being slightly longer, while Marcel's was a bit thicker, so much so my fingers were barely able to wrap around it completely."
        elif v3_spy:
            "So far, the biggest one I had seen was Jeremy's... And Marcel proved to be a worthy rival."
            "They were both pretty similar, Jeremy's tool being slightly longer, while Marcel's was a bit thicker, so much so my fingers were barely able to wrap around it completely."
        else:
            "This was by far the biggest cock I had the pleasure to hold... Not even Axel was this hung."
            "Marcel's cock was both long and girthy, so much so my fingers were barely able to wrap around it completely."
        bo "So, what do you think? Do you like it?"
        l "It's beautiful... I love how it feels in my hands... And it's getting harder..."
        bo "It's not easy to get me hard, but you're not having any trouble with it."
        menu v11marcelbbc:
            "{image=icon_lust.webp}Take a picture" if v11_bbc_pic == False and lena_lust > 7:
                $ renpy.block_rollback()
                $ v11_bbc_pic = True
                l "Can I take a picture?"
                bo "Sure... As long as you're in it too."
                play sound "sfx/giggle.mp3"
                l "Should I take a selfie, then?"
                scene v11_bbc1_marcel
                if v11_lena_dress == 2:
                    show v11_bbc1_charisma
                elif v11_lena_dress == 4:
                    show v11_bbc1_lust
                if lena_necklace == "choker2":
                    show v11_bbc1_choker
                if lena_tattoo2:
                    show v11_bbc1_t2
                with long
                pause 1
                "I pulled up my phone and got closer to Marcel, holding his cock against my face."
                play sound "sfx/camera.mp3"
                with flash
                # $ lena_marcel_pics.append("v12_bbc_phone2.webp") findme define marcel pics gallery
                "Seeing myself on the phone screen as I snapped some shots was incredibly thrilling and arousing."
                "So far, I had been masturbating to pictures and videos of other girls enjoying such monster cocks, but this time I was the star."
                "Marcel's dark meatstick contrasted so nicely with my pale skin, my clear blue eyes, and my pretty lips..."
                if stalkfap_pro > 1 and seymour_desire == False:
                    "I was getting some excellent images to pleasure myself with later, and I could even share them with some of my Stalkfap subscribers for some extra cash..."
                    "But I wanted to enjoy the present moment and the icon of my fantasies, now in my hands, hot and throbbing."
                else:
                    "I was getting some excellent images to pleasure myself with later..."
                    "But I wanted to enjoy the present moment and the icon of my fantasies, now in my hands, hot and throbbing."
                jump v11marcelbbc

            "Suck it":
                $ renpy.block_rollback()
                l "Can I suck it?"
                bo "Of course. That's what I've been waiting for."
                if v11_bbc_pic:
                    scene v11_bbc3b_marcel
                else:
                    scene v11_bbc3_marcel
                if v11_lena_dress == 2:
                    show v11_bbc3_charisma
                elif v11_lena_dress == 4:
                    show v11_bbc3_lust
                if lena_necklace == "choker2":
                    show v11_bbc3_choker
                if lena_tattoo2:
                    show v11_bbc3_t2
                with long
                play sound "sfx/bj1.mp3"
                pause 1
                l "Mhhhh..."
                if lena_lust < 10:
                    call xp_up ('lust') from _call_xp_up_909
                "My entire body shivered when my lips made contact with Marcel's glans, kissing and enveloping it."
                if v11_bbc_pic:
                    "I needed to take a picture of this, too... I channeled the pornstar in me, delighting myself with the treat Marcel was offering me." 
                    play sound "sfx/camera.mp3"
                    with flash
                else:
                    "I couldn't but delight myself in the treat Marcel was offering me. I wasn't expecting this to happen, but luck was on my side..."
                "My pussy tingled, hot, and moist, making me feel the thrill and pleasure of acting like a slut."
                "My bartending shift hadn't even started and I was already on my knees sucking an enormous, juicy cock in a back street..."
                "I knew tonight would be fun." 
                if v11_bbc_pic:
                    scene v11_bbc3_marcel
                    if v11_lena_dress == 2:
                        show v11_bbc3_charisma
                    elif v11_lena_dress == 4:
                        show v11_bbc3_lust
                    if lena_necklace == "choker2":
                        show v11_bbc3_choker
                    if lena_tattoo2:
                        show v11_bbc3_t2
                    with long
                play sound "sfx/bj5.mp3"
                if v10_wc_bj != "n" or v7_mike_bj:
                    bo "Damn, the rumors about your blowjob skills were also true... You're very passionate about it."
                    l "There are a lot of rumors about me, it seems..."
                    bo "Don't worry, I'll keep it a secret how much you love my cock."
                    l "And who wouldn't? With a thing like this..."
                else:
                    bo "Damn, I can see you're a pro at this... You're very passionate about it."
                    l "I just like your cock so much... I can't help it..."
                bo "Most girls find it intimidating. But I'm glad to see you're not afraid of it."
                scene v11_bbc2_marcel_animation with long
                l "Maybe just a bit... I can't even fit it in my mouth..."
                bo "You're wondering if you'd be able to fit it somewhere else, huh?"
                if lena_jeremy_sex or v10_jeremy_3some:
                    l "It would be a challenge. But seeing how wet this gets me, I believe I could do it."
                    bo "Interesting... We can give it a try one of these days, if you want."
                else:
                    l "Yes... I'm not sure that's even possible."
                    bo "You'd be surprised. We can give it a try one of these days, if you want."
                "I was hypnotized by the view. The stocky, heavy shaft, the thick veins bulging under the dark skin, the swollen glans..."
                "What an incredible icon of manhood..."
        
            "Keep stroking it":
                $ renpy.block_rollback()
                if v11_bbc_pic:
                    scene v11_bbc2_marcel_animation with long 
                pause 3.0
                if lena_lust < 8:
                    call xp_up ('lust') from _call_xp_up_910
                "I was hypnotized by the view. The stocky, heavy shaft, the thick veins bulging under the dark skin, the swollen glans..."
                "What an incredible icon of manhood..."
        # findme marcel extension
        bo "Come here, baby..."
        scene v11_bbc4_bg_marcel
        if v11_lena_dress == 2:
            show v11_bbc4_bg_charisma
        elif v11_lena_dress == 4:
            show v11_bbc4_bg_lust
        if v11_lena_dress == 2:
            show v11_bbc4a_marcel_charisma
        elif v11_lena_dress == 4:
            show v11_bbc4a_marcel_lust
        else:
            show v11_bbc4a_marcel_base
        with long
        "Marcel stood me up and met me with a sweet kiss."
        "His lips brushed mine with unexpected tenderness, our tongues dancing in a silent, sensual conversation."
        "Meanwhile, his hard cock between my legs offered a sharp and arousing contrast."
        scene v11_bbc4_bg_marcel
        if v11_lena_dress == 2:
            show v11_bbc4_bg_charisma
        elif v11_lena_dress == 4:
            show v11_bbc4_bg_lust
        if v11_lena_dress == 2:
            show v11_bbc4_marcel_charisma_animation
        elif v11_lena_dress == 4:
            show v11_bbc4_marcel_lust_animation
        else:
            show v11_bbc4_marcel_base_animation
        with fps
        "I began moving my hips, jerking Marcel off with my soft thighs."
        "His shaft was trapped between them, rubbing against my pussy from the base to the tip, sending shivers across my body."
        "I gasped and moaned, feeling so turned on...!"
        bo "Seems like you're having fun with my cock."
        l "Mhhh, yes... What about you? Does it feel good?"
        bo "Hell yeah, baby... You have the most perfect body."
        l "I'd love to feel you inside of me..."
        l "I don't know if that's even possible, but thinking about it makes me so wet..."
        "I felt Marcel shiver and tense up as I whispered lewd words into his ear."
        l "Give it to me... I want to make you cum."
        bo "You might be able to pull that off..."
        "I kissed Marcel again, deeper, steamier, and continued to stroke him with my thighs, pressing down on his shaft."
        bo "Fuck... You know how to move..."
        l "Cum. Cum for me..."
        bo "Yes... Yes...!"
        scene v11_bbc4_bg_marcel
        if v11_lena_dress == 2:
            show v11_bbc4_bg_charisma
        elif v11_lena_dress == 4:
            show v11_bbc4_bg_lust
        if v11_lena_dress == 2:
            show v11_bbc4c_marcel_charisma
        elif v11_lena_dress == 4:
            show v11_bbc4c_marcel_lust
        else:
            show v11_bbc4c_marcel_base
        show v11_bbc4_cum
        with flash
        bo "Unghh!!{w=0.6}{nw}" with vpunch
        with vpunch
        pause 0.6
        with vpunch
        pause 0.6
        "To my satisfaction, I felt Marcel shake and shudder as he shot his load between my thighs."
        "That imposing giant of a man was trembling before me, melting with the pleasure I provided him."
        "That only fanned the fire of my arousal..."
        j "Here's your earpiece..."
        stop music
        $ fjeremy = "surprise"
        $ flena = "surprise" 
        scene street2night
        show jeremy at left
        show bouncer
        show bouncer_cock
        show lenaunder at rig3
        with vpunch
        j "Whoa!"
        bo "Bro, can't you see we're busy over here? Lena and I were getting to know each other a bit better."
        $ flena = "blush"
        $ fjeremy = "sad"
        j "Yeah, uh, sorry man. I had no idea."
        hide bouncer
        hide lenaunder
        hide bouncer_cock
        show bouncersmile
        show lena2 at rig3
        with short
        bo "Never mind. We should get to work, shouldn't we?"
        "Marcel took the earpiece from Jeremy and handed it to me."
        bo "You know the deal. If you need anything, just give me a shout."
        hide bouncersmile with short
        j "..."
        if ian_lena_dating:
            l "Um, that was..."
            if ian_lena_couple:
                j "I wish I hadn't seen that."
                $ flena = "worried"
                l "Me too, but you did. So do me a favor and keep it to yourself, alright?"
                j "..."
                l "Jeremy?"
            else:
                j "Whatever it was, it's between you and Marcel..."
                $ flena = "sad"
                l "Yeah, you're right... But in any case, I'd rather you kept what you saw to yourself, alright?"
            j "Yeah, sure..."
        else:
            $ flena = "shy"
        l "So... Shall we get work started?"
        $ fjeremy = "n"
        j "Let's go."
        scene blazer with long
        show lena with short
        $ renpy.end_replay()
        $ gallery_unlock_scene("CH11_S23")
        jump v11bartending

#### BARTENDING ################################################################################################################################################
label v11bartending:
    $ ivy_look = "gogo" 
    play music "music/edm2.mp3" loop # findme music
    if v11_bbc != False:
        $ flena = "shy"
        "The night had kicked off in a very interesting and naughty manner, but now it was time to get to work."
        if v11_bbc == "marcel":
            "Jeremy was obviously shocked by what he had seen, but didn't make any comment about it. Better this way."
            if ian_lena_dating:
                $ flena = "sad"
                "Hopefully, he wouldn't mention anything to Ian either, but... I should explicitly ask him not to, just in case."
                if lena_cheating:
                    $ flena = "worried"
                    "I had been too careless... If Ian learned about my mischievous games, well... It wouldn't be nice."
        else:
            "Too bad Marcel had interrupted us, but it was to be expected... I knew it was risky, and that made it more fun."
            "I was sure this kind of stuff happened all the time at this club anyway..."
        "I tried to get into the right mindset and focus on the task at hand."
    $ flena = "n"
    "Clients were starting to enter the club, and the first stop for many of them was the bar."
    show lena at rig with move
# ROUND 1
    show lena at rig with move
    # 1
    show john at lef with short
    guy "Hi there! Can you fix me a mixed drink?"
    l "Sure, what do you want?"
    guy "Rum and cola."
    l "Alright."
    "I grabbed a glass, the rum bottle, and some ice cubes."
    menu:
        "Pour generously":
            $ renpy.block_rollback()
            $ v11_bar1 = 3
            $ flena = "smile"
            play sound "sfx/icecube.mp3"
            "I placed two ice cubes in the glass and generously filled it with rum."
            $ fjohn = "smile"
            l "Here you go."
            guy "Thanks!"
            hide john with short

        "Pour moderately":
            $ renpy.block_rollback()
            $ v11_bar1 = 2
            play sound "sfx/icecube.mp3"
            "I placed three ice cubes in the glass and filled the rest of it with rum."
            l "Here you go."
            guy "Thanks."
            hide john with short

        "Pour scarcely":
            $ renpy.block_rollback()
            $ v11_bar1 = 1
            play sound "sfx/icecube.mp3"
            "I placed four ice cubes in the glass and poured a bit of rum."
            $ fjohn = "sad"
            guy "Could you pour a bit more rum?"
            menu:
                "Yes":
                    $ renpy.block_rollback()
                    $ v11_bar1 = 2
                    $ flena = "sad"
                    l "Sure. Sorry..."
                    $ fjohn = "n"
                    guy "That's better... Thanks."
                    hide john with short

                "No":
                    $ renpy.block_rollback()
                    l "Sorry, that's how we pour drinks in here."
                    $ fjohn = "serious"
                    guy "Really?"
                    menu:
                        "Yes, sorry":
                            $ renpy.block_rollback()
                            l "Yes, that's how it is. Sorry."
                            guy "I knew we should've stayed at the bar for another round of drinks..."
                            hide john with short

                        "Call Marcel":
                            $ renpy.block_rollback()
                            $ v11_bar1 = 0
                            $ flena = "serious"
                            "I didn't like this guy's attitude... He could be trouble. I decided to use my earpiece and call Marcel right away."
                            show bouncer at lef3 behind john with short
                            bo "What's the problem?"
                            $ fjohn = "sad"
                            l "He's complaining about the drinks."
                            bo "You, come with me."
                            guy "What? But I didn't do anything...!"
                            bo "I don't care. Get the hell outta here."
                            $ flena = "evil"
                            hide john
                            hide bouncer 
                            with short
                            "Marcel grabbed the guy by the nape and led him away from the bar."
                            l "Nice."
    # 2
    $ flena = "n"
    pause 0.5
    show rosa at lef3 with short
    girl "{i}Psst{/i}! Hey, you! Over here!"
    $ config.menu_include_disabled = False
    $ greyed_out_disabled = True
    menu:
        "Attend to her":
            $ renpy.block_rollback()
            l "Yes?"
            show rosa at lef with move
            girl "I want two Margaritas."
            label v11margaritas:
                $ config.menu_include_disabled = False
                $ greyed_out_disabled = True
                l "Alright."
            hide rosa with short
            show lena at truecenter with move
            l "Margaritas... Let's see, those are made with..."
            menu:
                "{image=icon_wits.webp}Tequila" if lena_wits > 6:
                    $ renpy.block_rollback()
                    jump v11margarita1

                "Tequila" if lena_wits < 7:
                    $ renpy.block_rollback()
                    label v11margarita1:
                        $ v11_bar2 = 3
                    l "I know! Tequila, lime juice, and orange liqueur..."
                    "I mixed the drinks and went back to the customer."
                    $ frosa = "n"
                    show lena at rig with move
                    show rosa at lef with short
                    if v11_bar3 == 0:
                        l "Here you go."
                        girl "I'll pay by card."
                        l "Sure."
                        hide rosa with short
                    else:
                        girl "Took you long enough..."
                        jump v11margaritaslong

                "Rum":
                    $ renpy.block_rollback()
                    $ v11_bar2 = 1
                    l "I know! Rum, cola, and orange liqueur..."
                    label v11margaritafail:
                        "I mixed the drinks and went back to the customer."
                    show lena at rig with move
                    show rosa at lef with short
                    l "Here you go."
                    $ frosa = "mad"
                    girl "What the hell is this?"
                    $ flena = "sad"
                    l "Two Margaritas..."
                    girl "Do you even know how a Margarita is made?"
                    l "Yeah. I think so..."
                    girl "You think so? Can I get serviced by somebody who knows what they're doing?"
                    if v11_bar3 == 0:
                        label v11margaritajeremy:
                            $ flena = "serious"
                            $ fjeremy = "n"
                        show lena at rig3 with move
                        show jeremy with short
                        j "Hey, is everything alright?"
                        girl "Obviously not! I asked for two Margaritas and I don't even know what these are!"
                        j "I'll fix it. Lena, can you take care of the bar while I mix the drinks?"
                        $ flena = "sad"
                        l "Sure."
                        hide rosa
                        hide jeremy
                        with short
                        $ flena = "serious"
                        l "What a bitch..."
                        show lena at rig with move
                    else:
                        jump v11margaritaslong

                "Vodka":
                    $ renpy.block_rollback()
                    $ v11_bar2 = 1
                    l "I know! Vodka, milk, and cinnamon..."
                    jump v11margaritafail

                "Ask Jeremy":
                    $ renpy.block_rollback()
                    $ v11_bar2 = 2
                    $ flena = "sad"
                    $ fjeremy = "smile"
                    show lena at rig with move
                    show jeremy at lef with short
                    l "Hey, Jeremy! How do you mix a Margarita? I can't remember..."
                    $ fjeremy = "happy"
                    j "It's tequila, lime juice and orange liqueur!"
                    $ flena = "n"
                    l "Alright, thanks."
                    hide jeremy with short
                    "I mixed the drinks and went back to the customer."
                    show rosa at lef with short
                    l "Here you go."
                    girl "Took you long enough..."
                    menu v11margaritaslong:
                        "{image=icon_sad.webp}Sorry about that":
                            $ renpy.block_rollback()
                            $ flena = "sad"
                            l "Sorry about that."
                            if v11_bar2 == 1:
                                girl "So, can I get what I asked for?"
                                jump v11margaritajeremy
                            else:
                                girl "I'll pay by card."
                                hide rosa with short
                                $ flena = "serious"
                                if lena_lust > 7:
                                    l "What a bitch..."
                                else:
                                    l "How rude."
                            
                        "Excuse me?":
                            $ renpy.block_rollback()
                            $ flena = "serious"
                            l "Excuse me?"
                            if v11_bar2 == 1:
                                girl "I asked for two Margaritas and it's not what you served me. Not at all!"
                                jump v11margaritajeremy
                            else:
                                girl "Nothing. I'll pay by card."
                                l "Sure..."
                                hide rosa with short
                                if lena_lust > 7:
                                    l "What a bitch..."
                                else:
                                    l "How rude."

                        "{image=icon_mad.webp}Do you have a problem?":
                            $ renpy.block_rollback()
                            $ flena = "serious"
                            l "Do you have a problem?"
                            $ frosa = "mad"
                            if v11_bar2 != 1:
                                girl "Honestly, it kinda looks like you're not sure what you're doing."
                            else:
                                girl "What do you think? I asked for two Margaritas and it's not what you served me. Not at all!"
                            menu:
                                "Ignore her":
                                    $ renpy.block_rollback()
                                    $ v11_rosa_fight = 1
                                    if v11_bar2 == 1:
                                        l "Whatever..."
                                        jump v11margaritajeremy
                                    l "Whatever... Are you gonna pay by card or cash?"
                                    girl "Card."
                                    "She gave me a sour look before paying and disappearing into the crowd."
                                    hide rosa with short
                                    l "What a bitch..."

                                "{image=icon_mad.webp}Insult her":
                                    $ renpy.block_rollback()
                                    $ flena = "mad"
                                    l "And you're such a bitter cunt, you know that?"
                                    $ frosa = "mad"
                                    girl "What the hell did you just call me?"
                                    l "Are you also deaf? I said you're a bitter cunt!"
                                    l "Now roll off to the side. Your ginormous landwhale ass is blocking the bar."
                                    girl "I'll fucking end you!"
                                    play sound "sfx/slap.mp3"
                                    show rosa at centerlef
                                    show lena at rig3
                                    with hpunch
                                    "She leaned over the bar furiously and I leaned back when she tried to pull my hair."
                                    menu:
                                        "{image=icon_athletics.webp}Punch her" if lena_athletics > 4:
                                            $ renpy.block_rollback()
                                            label v11rosapunch:
                                                $ v11_rosa_fight = 4
                                            "I cocked back my fist and uncorked a punch right on her ugly face."
                                            play sound "sfx/big_punch.mp3"
                                            pause 0.5
                                            with flash
                                            $ frosa = "sad"
                                            show rosa at lef
                                            show lena at rig
                                            show rosablood at lef
                                            with vpunch
                                            girl "Ah! O-{w=0.3}ouch!!"
                                            girl "You punched me! This bitch punched me!!"
                                            l "Lean over the bar again if you want another one."
                                            label v11rosafight:
                                                girl "You...! You are...! This is outrageous!!"
                                            $ frosa = "mad"
                                            "She began yelling hysterically, but it didn't take long before Marcel appeared to take her away."
                                            show bouncer at lef3 behind rosa with short
                                            bo "You're coming with me."
                                            $ flena = "evil"
                                            $ frosa = "sad"
                                            if v11_rosa_fight == 4:
                                                girl "She punched me! Look, I'm bleeding! I'm the victim here!"
                                            else:
                                                girl "She slapped me right across the face! I'm the victim here!"
                                            bo "I said you're coming with me, ma'am. Now, don't make me repeat myself."
                                            hide rosa
                                            hide rosablood
                                            hide bouncer
                                            with short
                                            $ fjeremy = "sad"
                                            show jeremy at right with short
                                            j "What happened?"
                                            l "Nothing... Let's go back to work, the customers are waiting."
                                            if lena_charisma < 10:
                                                call xp_up ('charisma') from _call_xp_up_911
                                                pause 0.5
                                            hide jeremy with short

                                        "Slap her":
                                            $ renpy.block_rollback()
                                            label v11rosaslap:
                                                $ v11_rosa_fight = 3
                                            "I cocked back my hand and delivered a slap right to her ugly face."
                                            play sound "sfx/slap2.mp3"
                                            $ frosa = "sad"
                                            show rosa at lef
                                            show lena at rig
                                            with vpunch
                                            girl "Ah! O-{w=0.3}ouch!!"
                                            $ frosa = "mad"
                                            girl "You slapped me! This bitch slapped me!!"
                                            l "Lean over the bar again if you want another one."
                                            if lena_athletics < 5:
                                                call xp_up ('athletics') from _call_xp_up_912
                                            jump v11rosafight

                                        "Call Marcel":
                                            $ renpy.block_rollback()
                                            label v11rosamarcel:
                                                $ v11_rosa_fight = 2
                                            "I had no doubts about what to do. I called Marcel through the earpiece, and he appeared within seconds to resolve the situation."
                                            show bouncer at lef3 behind rosa with short
                                            bo "What's going on here?"
                                            $ flena = "serious"
                                            l "She's causing a scene and tried to attack me."
                                            bo "Come with me, ma'am."
                                            girl "She insulted me! She...!"
                                            bo "I said you're coming with me. Don't make me repeat myself."
                                            $ flena = "evil"
                                            hide rosa
                                            hide bouncer
                                            with short
                                            $ fjeremy = "sad"
                                            show jeremy at right with short
                                            j "What happened?"
                                            $ flena = "n"
                                            l "Just a crazy bitch... Let's go back to work, the customers are waiting."
                                            if lena_wits < 10:
                                                call xp_up ('wits') from _call_xp_up_913
                                                pause 0.5
                                            hide jeremy with short
                                    
                                    if v11_bar2b:
                                        if v11_bar1b == 0 or v11_bar3b == 0 or v11_bar4b == 0:
                                            jump v11bartendingrd2
                                        else:
                                            jump v11bartendingaxel

        "Attend another customer":
            $ renpy.block_rollback()
            $ v11_bar2 = 0
            "There were other people waiting at the bar. I turned around to attend to another customer."
            hide rosa with short

    # 3
    if persistent.include_disabled:
        $ config.menu_include_disabled = True
    $ greyed_out_disabled = False
    if v11_bar3 != 0:
        jump v11barrobert
    $ flena = "n"
    pause 0.5
    show finley at lef3
    show sen at centerlef
    with short
    guy "Hi! Could you get us a beer, a vodka lemon, and two shots, please?"
    l "Got it."
    play sound "sfx/beer.mp3"
    "I mixed the drink, cracked a cold beer open, and poured two tequila shots."
    l "Here you go. Card or cash?"
    $ fsen = "smile"
    guy "Card. But could we get the shots as a present? It's Finley's birthday today!"
    $ ffinley = "sad"
    guy2 "There's no need..."
    guy "Come on, we're celebrating!"
    menu:
        "Give them free shots":
            $ renpy.block_rollback()
            $ v11_bar3 = 3
            $ flena = "smile"
            l "Happy birthday! Alright, don't tell anyone, but the shots are on the house."
            $ ffinley = "smile"
            guy2 "Thank you!"
            l "Have a fun night, guys."
            guy "We will!"
            hide sen
            hide finley
            with short

        "Sorry, but no":
            $ renpy.block_rollback()
            $ v11_bar3 = 2
            l "Sorry, but I can't do that. House rules."
            $ fsen = "n"
            guy "Really? Come on, it's a special night..."
            guy2 "Don't listen to him. I'll pay..."
            guy "Alright, alright! Here's my card... This round's on me."
            l "Alright."
            hide sen
            hide finley
            with short

        "Call Marcel":
            $ renpy.block_rollback()
            $ v11_bar3 = 1
            $ flena = "serious"
            "I didn't like these guys' attitude. I called Marcel."
            show bouncer at left behind finley with short
            bo "What's going on here?"
            $ fsen = "sad"
            l "These guys don't want to pay."
            bo "Is that so?"
            guy "What? No, that's not it...!"
            guy2 "I told you not to ask for free drinks...!"
            bo "This is a business and the drinks cost money. So pay up what you ordered."
            guy "Y-{w=0.3}yes, right away..."
            $ flena = "n"
            guy "Here you go..."
            hide bouncer
            show bouncersmile behind finley at left
            bo "That's better."
            hide bouncersmile
            hide sen
            hide finley
            with short

    if v11_bar2 == 0:
        $ flena = "n"
        $ frosa = "mad"
        pause 0.5
        show rosa at lef with short
        girl "Hello? Can I get serviced over here?"
        l "Yeah. What do you want?"
        girl "Finally! Two Margaritas."
        jump v11margaritas
            
    # robert
    label v11barrobert:
        $ flena = "n"
        $ robert_look = 1
        $ frobert = "n"
        pause 1
    show robert at lef with short
    if lena_robert_sex:
        if lena_robert_over or lena_robert_over2 or v9_lena_robert_over:
            $ frobert = "sad"
            r "Lena...! What are you doing here?"
            l "Hey, Robert... I'm covering for someone tonight. What can I get you?"
            $ frobert = "n"
            r "Rum cola..."
            l "Coming right away."
            play sound "sfx/icecube.mp3"
            "What an unhappy coincidence... I was in no mood to deal with Robert tonight, so I tried to dispatch him quickly."
            l "Here you go."
            r "So, hey... How have you been?"
            if lena_robert > 5:
                $ flena = "sad"
                l "I can't talk right now, Robert. I have work to do."
            else:
                $ flena = "serious"
                l "I can't talk right now, Robert. I'm working, as you can clearly see."
            r "Sure, sorry. We'll catch up another time..."
        else:
            $ frobert = "flirt"
            r "Oh, hey babe! What are you doing here?"
            l "Hey Robert... I'm covering for someone tonight. I didn't know you were coming tonight."
            $ frobert = "n"
            r "My friend got dumped recently and I took him partying to cheer him up. I'd ask you to join, but you're always so busy..."
            l "I'm busy now too. What can I get you?"
            r "Rum cola, please..."
            play sound "sfx/icecube.mp3"
            $ frobert = "flirt"
            r "Damn, you look so hot tonight!"
            r "At what time do you finish your shift? I can take you home if you want..."
            if v11_robert_sex:
                r "Last night was so fucking hot. Have you been thinking about it as much as I did?"
            elif v10_robert_sex:
                r "It's been a while since we last spent the night together, but it seems we're in luck today."
            else:
                r "It's been far too long since we last spent the night together, but it seems we're in luck today."
            $ flena = "sad"
            if v11_ian_sex:
                l "I can't tonight, Robert. I already have plans."
            else:
                l "Some other time, okay? I don't think I'll have the energy at the end of the night."
            $ frobert = "n"
            r "Oh, really...?"
            $ flena = "n"
            l "Really. And now I need to get to work, so if you don't mind..."
            r "Sure, sorry. We'll catch up another time..."
            l "Have fun."
        if holly_robert:
            show robert at lef3 with move
            $ flena = "sad"
            l "Oh, wait! I just remembered something..."
            show robert at lef with move
            r "What is it?"
            $ flena = "smile"
            if lena_robert_over or lena_robert_over2 or v9_lena_robert_over:
                l "I know things didn't end well between us, but... I'd like to introduce you to a friend of mine."
                $ frobert = "sad"
            else:
                l "You see... There's this friend of mine I'd like to introduce you to..."
            r "A friend of yours?"
            l "Yeah. Her name is Holly, and I think you two could hit it off."
            $ frobert = "sad"
            r "Hit it off? You mean..."
            $ flena = "flirtshy"
            l "Well, whatever happens is up to you two. I just want you to meet her and see what's up."
            $ frobert = "smile"
            r "Oh, alright... If that's what you want... Is she here?"
            $ flena = "n"
            l "No. I'll give you her number, text her whenever."
            r "Sure, I'll do that."
            hide robert with short
            l "Alright, mission accomplished. Now let's deal with the customers..."
        else:
            hide robert with short
    else:
        $ flena = "sad"
        l "Oh, hey..."
        r "What are you doing here? You work here now?"
        menu:
            "{image=icon_friend.webp}Be friendly" if lena_robert > 0:
                $ renpy.block_rollback()
                $ v11_robert_talk = 2
                l "Just for tonight... I'm covering up for someone."
                r "I see... So you come here often?"
                l "From time to time."
                r "Me too. It's weird we haven't met before."
                l "So, what can I get you?"
                r "Yeah... Rum cola for me."
                l "Coming right away..."
                play sound "sfx/icecube.mp3"
                l "Here you go."
                r "Thanks. And hey..."
                $ frobert = "sad"
                r "I'm sorry about... You know, back then."
                if v7_fight != "n":
                    l "You mean about causing a fight during my modeling work?"
                    r "Well, yeah, also that. But I meant about being a bit of a jackass to you."
                else:
                    r "I know I was... a bit of a jackass to you."
                $ frobert = "n"
                r "I've been thinking about it and... I believe I owe you an apology. So here it is."
                l "Apology accepted. Now, I don't want to be rude, but there are people waiting for their drink right now..."
                r "Sure, not the best moment."
                r "Anyway, we can get a coffee one of these days if you want. Good luck tonight."
                call friend_xp ('robert',3) from _call_friend_xp_1062
                hide robert with short
        
            "Ignore his question":
                $ renpy.block_rollback()
                $ v11_robert_talk = 1
                l "What can I get you?"
                $ frobert = "serious"
                r "Rum cola... But no need to ignore my question."
                $ flena = "serious"
                l "I'm busy and there are other people waiting for their drinks."
                r "Yeah, whatever."
                if lena_robert > 0:
                    call friend_xp ('robert',-1) from _call_friend_xp_1063
                hide robert with short
                l "Good... Last thing I need is to have him pestering me tonight."
                $ flena = "n"

            "{image=icon_mad.webp}Call Marcel":
                $ renpy.block_rollback()
                $ v11_robert_talk = 0
                $ flena = "serious"
                l "Ugh. Not you. Not tonight."
                "I discretely called Marcel through the earpiece."
                r "What? Did you say something?"
                $ flena = "n"
                l "No... Nothing."
                show bouncer at lef3 behind robert with short
                bo "You. Get away from the bar."
                $ flena = "evil"
                $ frobert = "sad"
                r "Huh? What?"
                bo "You heard me! Let's go!"
                show robert at left with vpunch
                r "What the hell...!?"
                $ frobert = "mad"
                r "Don't put your hands on me, you dumb ape! I didn't do anything wrong!"
                bo "Now you did."
                play sound "sfx/punchgym.mp3"
                hide bouncer
                hide robert
                with vpunch
                "Marcel put Robert in a headlock and dragged him out of the club."
                if lena_robert > 0:
                    call friend_xp ('robert',-1) from _call_friend_xp_1064
                    $ lena_robert = 0
                l "That'll teach him."
        
    # 4
    show lena at rig
    with short
    $ flena = "n"
    $ feli = "n"
    $ eli_look = 2
    show eli at lef with short
    $ eli = "{color=#7E8DA2}Girl{/color}"
    eli "Hi... Can I get a Mojito?"
    $ config.menu_include_disabled = False
    $ greyed_out_disabled = True
    menu:
        "I got it":
            $ renpy.block_rollback()
            $ v11_bar4 = 1
            l "I got it, coming right away."
            play sound "sfx/icecube.mp3"
            eli "Thanks!"

        "I've seen you before...":
            $ renpy.block_rollback()
            $ flena = "smile"
            l "I've seen you before... You're the girl from the clothing shop!"
            if v11_emma_pics == 2:
                $ feli = "blush"
                eli "Yes... I remember you too. I took some pictures for you and your friend..."
                $ flena = "shy"
                l "Thank you for that. I hope we didn't bother you too much!"
                eli "No, don't worry... Not at all."
            elif v11_emma_pics == 1:
                eli "Yes... I took some pictures for you and your friend this afternoon!"
                l "Thank you for that. I hope we didn't bother you too much!"
                eli "Not at all..."
            else:
                eli "Oh, yeah... I remember you too. You came by this afternoon."
            $ flena = "smile"
            l "My name's Lena, by the way."
            $ feli = "n"
            eli "I'm Eli. Nice to meet you!"
            $ eli = "{color=#7E8DA2}Eli{/color}"
            menu v11elidrink:
                "Compliment her" if v11_bar4 < 2:
                    $ renpy.block_rollback()
                    $ v11_bar4 = 2
                    $ flena = "happy"
                    l "I love your outfit, by the way! Looks great on you!"
                    $ feli = "shy"
                    eli "Oh, thank you... You look stunning in that dress too! I wish I could pull off a look like yours!"
                    $ feli = "n"
                    l "I'm sure you can!"
                    eli "If I was as hot as you, maybe."
                    jump v11elidrink
        
                "{image=icon_lust.webp}Flirt with her" if v11_bar4 == 2 and lena_fty_lesbo:
                    $ renpy.block_rollback()
                    $ v11_bar4 = 3
                    $ flena = "shy"
                    l "Well, I think you look very hot too... And cute!"
                    $ feli = "shy"
                    eli "Really? Well, thank you... I think you're super sexy too."
                    eli "Can I, um, follow you on Peoplegram?"
                    $ flena = "happy"
                    l "Of course! Here, write it down..."
                    if lena_charisma < 10:
                        call xp_up ('charisma') from _call_xp_up_914
                    eli "Cool..."
                    $ flena = "smile"
                    l "You wanted a Mojito, right?"
                    eli "Yes, please."
                    l "Alright...Here you go."
                    l "Nice meeting you, Eli!"
                    eli "Likewise!"

                "What brings you here tonight?" if v11_bar4 == 0:
                    $ renpy.block_rollback()
                    l "So what brings you here tonight? Celebrating something special?"
                    eli "Nothing special... Me and my friends just wanted to party a bit tonight."
                    eli "It's my first time here, but I'm liking it. The DJ is pretty good!"
                    l "So you like electronic music?"
                    eli "I like a bit of everything, but yeah, electronic is probably my favorite right now."
                    l "I hope you and your friends have fun tonight!"
                    eli "Thanks!"
                    if v11_bar4 > 1:
                        l "You wanted a Mojito, right?"
                        eli "Yes, please."
                        l "Got it!"
                    else:
                        $ v11_bar4 = 1
                        jump v11elidrink

                "What did you want?":
                    $ renpy.block_rollback()
                    if v11_bar4 == 0:
                        $ v11_bar4 = 1
                    l "Sorry, what did you want?"
                    $ feli = "n"
                    eli "A Mojito, please."
                    l "Right. Coming right away."

        "Let Jeremy handle it":
            $ renpy.block_rollback()
            $ v11_bar4 = 0
            show lena at truecenter
            show eli at lef3
            with move
            show jeremy at rig3 with short
            l "I'll let you handle this one."
            $ fjeremy = "happy"
            j "Nice!"
            hide jeremy
            hide eli
            with short
            hide lena with short

    # ivy dance
    if persistent.include_disabled:
        $ config.menu_include_disabled = True
    $ greyed_out_disabled = False
    scene blazer with long
    "I continued serving customers one after another. So far, I could keep up the pace, but there was no shortage of work..."
    stop music fadeout 2.0
    "And as the night went on and the club got more crowded it would get worse."
    play music "music/ivys_theme.mp3" # findme music
    scene v5_ivy1 with long
    pause 1
    "Thankfully, the go-go dancers gave us a break when they climbed up the podiums to perform."
    "Ivy and her crew were magnets for attention, turning heads around and relieving pressure from us as we continued to fulfill orders."
    "She was the perfect image of a bombshell, captivating the audience with an amazing display of sensuality and athleticism."
    if lena_mike_sex:
        "I couldn't help but wonder if Mike had also fallen prey to her seductive {i}femme fatale{/i} aura. Very few guys would be able to resist it..."
        if lena_mike_love:
            "Thinking about it made my stomach churn. I didn't like the idea, not one bit."
            "I knew I was sharing Mike with her girlfriend, but I still felt in control. I knew I was special to Mike."
        elif lena_mike_dating:  
            "I didn't like the idea... I knew I was sharing Mike with her girlfriend, but I still felt in control of the situation."
        if lena_mike_over:
            "I had put an end to my fling with Mike, but it still felt weird to think about Ivy getting it on with him..."
        elif lena_mike_dating:
            "However, if Ivy had involved herself with him... It would mean her stepping into my territory, and I didn't like that."
        if v10_ivy_sex > 1 or v11_shower_sex == "3some":
            scene v5_ivy2 with long
            pause 1
            "But I was in no position to complain, was I? Even I couldn't resist Ivy's lustful appeal..."
            "Our friendship had taken a turn lately, one that involved intimate and naughty stuff."
            "It was just a game between girlfriends, though..."
    else:
        "I had no doubts about Ivy's ability to get it on with Mike despite him having a girlfriend. She could probably seduce any guy she wanted..."
        if v10_ivy_sex > 1 or v11_shower_sex == "3some":
            scene v5_ivy2 with long
            pause 1
            "Even I wasn't immune to her lustful appeal..."
            "Our friendship had taken a turn lately, one that involved intimate and naughty stuff."
            "It was just a game between girlfriends, though..."
    # cindy
    "While looking at her, I spotted someone in the midst of the crowd. A scene I couldn't ignore."
    $ faxel = "happy"
    $ fcindy = "shy"
    $ axel_look = 2
    $ cindy_look = "party"
    scene blazer with long
    show axel at centerlef
    show cindy2 at centerrig
    with long
    pause 1
    "I recognized them amongst a small group of people. Axel... and Wade's girl, Cindy."
    "Axel looked at her, smiling, and she laughed, touching his arm. "
    "They seemed to be having a really good time, whatever it was they were talking about."
    $ flena = "worried"
    scene blazer with long
    show lena2 
    with short
    if lena_axel_dating or lena_axel_over:
        "A stinging feeling in my stomach made me tense up. What were they doing here?"
        $ flena = "serious"
        "Last time I saw him he was walking with Cherry, and now he was openly flirting with another girl. I had no desire to see that."
        "She probably didn't know Axel's true colors, but she would in due time, if she kept hanging around him."
        if v9_axel_sex: # disposition 2
            $ flena = "blush"
            l "Have they...?"
            "I couldn't help but wonder about their relationship. What was going on between them, exactly?"
            "Had Axel really moved on from our past? Was the girl he held dear now Cindy?"
            if lena_axel_desire:
                $ flena = "serious"
                l "She looks way too basic for Axel. She might be pretty, but he'd never be satisfied with a girl like her..."
            else:
                l "That's none of my business anymore... I shouldn't care about it."
        elif axel_disposition == 1:
            $ flena = "sad"
            l "That's none of my business, though... I don't want to have anything to do with Axel if I can avoid it."
        else:
            l "Misery loves company..."
    else:
        l "What are they doing together...?"
        l "Of all the nights they could've chosen to come, it had to be the one I'm working in here."
        "I hadn't talked to Axel since the night of Ivy's birthday, and I wanted to keep it that way."
        $ flena = "sad"
        l "Hopefully, he doesn't notice me tonight... There's usually trouble when we share the same space."
        
    # mark
    stop music fadeout 2.0
    $ fmark = "n"
    show lena2 at rig with move
    show mark at lef with short
    play music "music/edm.mp3" # findme music
    ma "Good night, Lena."
    $ flena = "worried"
    l "Oh... Hey Mark..."   
    ma "What's up? You look shocked."
    l "No, it's..."
    $ flena = "n"
    hide lena2
    show lena at rig
    with short
    if holly_guy > 2:
        l "I wasn't expecting to see you here tonight. Weren't you on a date with Holly?"
        ma "Just dropped her off a while back. Didn't you know? She had to be home before twelve."
        $ flena = "happy"
        l "Yeah, she's our little Cinderella..."
        $ fmark = "smile"
        ma "Cinderella, huh? That's such a good nickname, ha ha."
        $ fmark = "n"
        ma "But I can see why you and Ivy want to get her out of her shell. She's way too naive!"
        $ flena = "evil"
        l "I believe that's not the case anymore... Not after she met you, that is!"
        $ fmark = "flirt"
        if holly_guy > 3:
            ma "I believe you had something to do with it, too. Holly told me you gave her some advice..."
            l "I'm trying to help her out..."
            ma "I hope you continue to do so. I'm enjoying myself quite a bit, ha ha!"
        else:
            ma "I confess myself guilty... and with much pleasure."
        $ flena = "flirtevil"
        l "I know... I've seen what you've been sharing with Ivy!"
        ma "Oh, really? Seems like you're quite naughty as well..."
        if v10_wc_bj == "mark":
            l "You know I am."
            ma "Oh, yeah. I haven't forgotten about our little escapade in here last time... Like I ever could."
        else:
            $ flena = "shy"
            l "Could be..."
        ma "I can keep you updated on our progress too, if you want."
        l "I won't lie, that'd be interesting..."
    else:
        l "I wasn't expecting to see you here tonight."
        if v10_mark_flirt or v10_wc_bj == "mark":
            ma "Oh, maybe your boyfriend is around? That's what you're worried about?"
            if ian_lena_couple:
                l "No, he's not here tonight."
            elif ian_lena_dating:
                l "I don't really have one, so no worries there."
            else:
                l "I don't have one, so no worries there."
            $ fmark = "flirt"
            ma "Lucky me, then."
        else:
            ma "I come here often. Best club in town!"
            ma "It has the most beautiful bartenders, that's for sure."
    $ flena = "n"
    l "Anyway, what can I get you?"
    if lena_mark_dating:
        $ fmark = "smile"
        ma "Ouch, so cold. So that's all I am now, a simple customer?"
        l "It's just I'm a bit busy over here..."
        $ fmark = "n"
        ma "I know, I see that..."
        if ian_lena_couple and lena_cheating == False:
            ma "I know you have a boyfriend and all that, but..."
        ma "Wanna hang out when your shift's over? I'll drive you home."
        menu:
            "{image=icon_lust.webp}Hook up with Mark" if ian_lena_couple == False or lena_cheating:
                $ renpy.block_rollback()
                $ v11_mark_sex = True
                $ flena = "flirt"
                l "Sounds like a plan..."
                if v11_ian_sex:
                    $ flena = "worried"
                    l "Oh, wait..."
                    $ fmark = "n"
                    "I was supposed to go to Ian's place tonight. But this was a great chance to hook up with Mark..."
                    $ flena = "flirt"
                    "I hadn't had the chance to thoroughly taste him yet, and the power of novelty was too tempting to resist."
                    ma "Is there a problem?"
                    l "No, it's nothing. I'll see you later."
                $ fmark = "flirt"
                if v10_wc_bj == "mark":
                    ma "Great. Last time was phenomenal, but it left me wanting more..."
                    ma "I have to return the favor, for one. And I want to take my time to get to know you better."
                else:
                    ma "Great. We'll finally get some time for just the two of us..."
                    ma "I've been wanting to get to know you better."
                $ flena = "slut"
                l "Don't worry, you will."
                if lena_cheating:
                    "I had told myself to stop cheating on Ian, but... One last time couldn't possibly hurt, right?"
                $ flena = "smile"
                l "Now, what can I get you?"
                    
            "Decline":
                $ renpy.block_rollback()
                $ lena_mark_dating = False
                if ian_lena_couple:
                    $ flena = "n"
                else:
                    $ flena = "sad"
                l "It's a tempting offer, but I'll have to pass."
                if ian_lena_couple and lena_cheating == False:
                    ma "Well, I hope you don't blame me for trying."
                    if v10_wc_bj == "mark":
                        $ fmark = "flirt"
                        ma "At least I got to enjoy you a bit. It'll make for a beautiful memory, ha ha."
                        $ flena = "flirt"
                        l "I'm glad I was able to give you at least that..."
                    else:
                        $ flena = "smile"
                        l "It's alright..."
                else:
                    $ fmark = "sad"
                    ma "How come? I thought you were interested."
                    if v10_wc_bj == "mark":
                        $ fmark = "flirt"
                        ma "That's the message I got when you blew me in the bathroom, at least..."
                        $ flena = "blush"
                        l "That night I partied a bit too hard... Drank too much and did crazy things..."
                        $ fmark = "n"
                        ma "That's alright... Those sorts of things happen."
                        ma "Too bad you don't want us to have another go at it, though."
                        $ flena = "sad"
                        l "Yeah, well..."
                    else:
                        l "We were partying, drinking, you know... It was just some friendly flirting."
                        $ fmark = "n"
                        ma "Oh, really? We've been texting a bit, what about that?"
                        l "As I said... I was just trying to be friendly."
                        ma "Is that so...? Then I guess I got the wrong impression. Excuse me."
                    $ flena = "n"
                    l "Anyway... What can I get you?"

    ma "I'll go with gin and tonic tonight."
# smoke break #############################################################################################################################
    hide mark with short
    $ flena = "n"
    $ fjeremy = "happy"
    $ fivy = "smile"
    show lena at truecenter with move
    show jeremy at rig3 with short
    j "Hey, how are you holding up?"
    l "I'm doing fine so far! What about you, need any help?"
    j "Everything's under control! You're talking to the barmaster here!"
    show ivy2 at lef3
    with short
    v "Hey, barmaster, would you let me steal your sidekick for a smoke break?"
    $ fjeremy = "smile"
    j "Okay, but don't take too long! It's getting pretty busy over here!"
    stop music fadeout 2.0
    hide lena
    hide ivy2
    with short
    scene street2night with long
    play music "music/club_outside.mp3" loop
    $ fivy = "n"
    show ivy at lef
    show lena at rig
    with short
    # jeremy
    if lena_ivy_jeremy:
        if v7_bbc == "lena":
            l "You seem to get along with Jeremy quite well..."
            v "You think so?"
            l "I thought you didn't want to have anything to do with him, since he's dating Louise."
            $ fivy = "flirt"
            if v8_jeremy_sex:
                v "I know that's not an issue for you! You've been having plenty of fun with him, right?"
                $ flena = "flirtshy"
                l "Only a bit... But he still seems determined to hook up with you, or at least try."
                $ fivy = "n"
                $ flena = "n"
        else:
            l "You seem to get along with Jeremy quite well... Especially after what happened with Louise and all that."
            v "What are you trying to say?"
            l "Nothing... Only that he still seems determined to hook up with you, or at least try."
        v "Yeah, I can see he hasn't given up yet... Say what you will, but his tenacity is commendable."
        if ivy_jeremy == 2:
            v "I must admit he's a funny guy... He has a particular kind of charm. Ian spoke highly about him..."
            label v11jeremychance:
                v "Why are you asking? You think I should give him a chance?"
                menu:
                    "Yes":
                        $ renpy.block_rollback()
                        $ ivy_jeremy = 2
                        l "Sure, why not?"
                        $ fivy = "flirt"
                        hide ivy
                        show ivy2 at lef
                        with short
                        if v8_jeremy_sex:
                            v "Oh, so you don't mind sharing him? Of course, you're already sharing him with Louise."
                            if lena_mike_dating:
                                $ flena = "sad"
                                "I didn't care about Jeremy that way... But the same couldn't be said about Mike."
                                "I knew I had to bring up the subject with Ivy, or I wouldn't stop feeling restless."
                            l "Yeah, well... I only got with him because you set things up, so..."
                            $ fivy = "n"
                            $ flena = "n"
                            hide ivy2
                            show ivy at lef
                            with short
                            v "That's right... Sometimes I forget!"
                            l "I mean, you've been playing games with him all this time. There's a reason for that."    
                        else:
                            v "I can't believe it. What did he bribe you with for you to tell me this?"
                            l "Come on, you've been playing games with him all this time. There's a reason for that."
                            hide ivy2
                            show ivy at lef
                            with short
                        v "I don't know. Maybe you're right."
                        if lena_charisma < 8:
                            call xp_up ('charisma') from _call_xp_up_915
            
                    "No":
                        $ renpy.block_rollback()
                        if ivy_jeremy == 2:
                            $ ivy_jeremy = 1
                        l "Honestly? No."
                        if v8_jeremy_sex:
                            $ fivy = "flirt"
                            hide ivy
                            show ivy2 at lef
                            with short
                            v "Of course, you want him all to yourself, right?"
                            v "Though you're sharing him with Louise..."
                            l "Yeah, things are already complicated enough as they are. But I don't care about Jeremy in that way..."
                            if lena_mike_dating:
                                $ flena = "sad"
                                "The same couldn't be said about Mike. I knew I had to bring up the subject with Ivy, or I wouldn't stop feeling restless."
                            $ fivy = "n"
                            hide ivy2
                            show ivy at lef
                            with short
                        else:
                            $ fivy = "flirt"
                            v "Honest and blunt, just how I like it."
                        v "I'll listen to your advice. You're usually right about these kind of things."
                        if lena_wits < 8:
                            call xp_up ('wits') from _call_xp_up_916
                        $ flena = "n"
                        $ fivy = "n"

                    "It's up to you":
                        $ renpy.block_rollback()
                        l "That's up to you... I was just curious."
                        v "I see."
            
        elif ivy_jeremy == 1:
            v "I'm a bit on the fence about him. He's funny and all, but he's also a bit of an idiot."
            jump v11jeremychance
        else:
            $ fivy = "n"
            v "I considered giving him a go, since he has a very interesting {i}asset{/i}... But he loses a ton of points for being an idiot."
            v "I can't take seriously a guy who settles for anything. I'm the big prize, only for real winners, you know?"
            if v8_jeremy_sex:
                v "No offense to you. I'm sure you're having a lot of fun playing with him... But Ian told me he's not to be trusted."
            else:
                v "Besides, Ian told me he's not to be trusted."
            l "I see..."
    $ fivy = "n"
    v "So, how's your night going? Surviving?"
    l "So far."
    play sound "sfx/lighter.mp3"
    show ivy_smoke at lef with long
    $ ivy_extras = "smoke"
    hide ivy_smoke
    "Ivy lit up a cigarette and took a deep drag, exhaling a puff of smoke."
    v "Want one?"
    menu:
        "No, thanks":
            $ renpy.block_rollback()
            l "No, thanks... You know I'm not a big fan."
            v "You never liked it when we started smoking back in high school. Was that the reason you never hung out with us after class?"
            l "I did hang out with you and the guys sometimes... But I preferred spending some time on my own."
            if lena_wits < 10:
                call xp_up ('wits') from _call_xp_up_917
            v "You were such a bookworm... Look at you now, though!"
            if lena_drugs or v10_lena_drug:
                v "Not so straight-edge anymore! The other day I got some of the best molly around, too bad you're working today."
                l "Save me some for another time..."
            elif stalkfap_pro == 0 or lena_lust < 8:
                l "I haven't changed that much..."

        "Yes":
            $ renpy.block_rollback()
            $ lena_smoke = True
            l "Sure, why not?"
            play sound "sfx/lighter.mp3"
            hide lena
            show lena_smoke at rig
            with long
            "I took the cigarette Ivy was offering and lit it up. The acrid flavor of smoke filled my mouth and stung my throat a little bit."
            $ flena = "sad"
            l "Ugh, I quit smoking these a long time ago..."
            $ fivy = "smile"
            v "We used to smoke a lot during high school. Those were the days..."
            $ flena = "n"
            v "Do you remember all those afternoons with the gang at the park?"
            $ flena = "smile"
            l "Yeah, Sylvia, Berta, and the other guys from class... I wonder how they are doing these days. It's been years since I last heard from them."
            v "They were never real friends to us. They got jealous about you and me getting all the attention from the cool guys and started making plans without us. They were so childish..."
            $ fivy = "flirt"
            v "Remember that time we skipped class to meet with those college guys? That day was so much fun."
            l "Yeah... We did some crazy stuff back then."
            if lena_ivy < 12:
                call friend_xp ('ivy') from _call_friend_xp_1065
            $ flena = "n"
    
    # mike
    if lena_mike_sex:
        # dating
        if lena_mike_dating:
            stop music fadeout 2.0
            $ flena = "serious"
            l "Hey... Let me ask you something."
            play music "music/tension.mp3" loop
            l "What's the deal between you and Mike, exactly?"
            $ fivy = "n"
            v "Mike and me? What do you mean?"
            l "Did you fuck him?"
            $ fivy = "sad"
            v "Oh, wow. Where's this coming from all of a sudden?"
            l "I'm not blind, you know? You were flirting with him right in my face."
            $ fivy = "n"
            v "Mike and I get along, you know that."
            l "Do you like him?"
            v "Well, he's really hot, and I dig his vibe..."
            l "So you did fuck him!"
            v "I didn't say that..."
            l "Don't play dumb with me! I can see something's going on between you. And don't dare lie to me!"
            v "Alright, alright. I'll admit it: we did hook up, but only once."
            if lena_mike_love:
                $ flena = "mad"
                l "Really, Ivy? You know how much I like him!"
                if lena_ivy > 0:
                    call friend_xp ('ivy',-1) from _call_friend_xp_1066
            else:
                $ flena = "worried"
                l "Really, Ivy? You know I'm seeing him!"
            v "I didn't know you had an exclusivity clause..."
            $ flena = "serious"
            l "I believe I told you to stay away from him. You didn't even ask me if it was okay for you to hook up with him!"
            $ fivy = "serious"
            v "I didn't know I had to ask permission! Besides, how's that different from what you're doing?"
            v "You didn't ask permission of his girlfriend either, did you?"
            $ flena = "sad"
            if ian_lena_couple:
                v "And you have a boyfriend now too, or at least that's what you want us to believe..."
            menu:
                "It's not the same!":
                    $ renpy.block_rollback()
                    $ flena = "mad"
                    l "It's not the same, and you know it!"
                    $ fivy = "serious"
                    v "How's it any different?"
                    l "For starters, I was hooking up with Mike first. You're supposed to be my friend, and friends don't step on each other's territory!"
                    v "Oh, come on. Mike's not your boyfriend, nor your property."
                    $ fivy = "n"
                    v "Why do you get all jealous of me and not of his girlfriend? She's the one who fucks him every day; I only did it once."
                    $ flena = "serious"
                    l "I already told you: you're supposed to be my friend but you betrayed my trust."
                    $ fivy = "serious"
                    v "Are you sure you're the right person to be talking about that?"
                    l "Don't pull that one on me. You know what you did."
                    v "And is that my fault? He invited me to check out his home studio and it just happened."
                    l "I'm sure there's more to that story than just that."
                    if lena_ivy > 0:
                        call friend_xp ('ivy',-1) from _call_friend_xp_1067
                    v "Think what you want..."
                    if lena_smoke:
                        "A long, tense silence followed while Ivy and I smoked our cigarettes."
                    else:
                        "A long, tense silence followed while Ivy smoked her cigarette."
                    $ fivy = "sad"
                    v "I don't know what else to tell you. He's just a guy, I didn't think it'd be such a big deal to you."
                    l "Then either you don't know me that well or you just don't listen to me."
                    "Maybe I was being irrational, but I felt outraged. For Ivy, but especially for Mike..."
                    $ flena = "sad"

                "You should've asked me first":
                    $ renpy.block_rollback()
                    $ flena = "serious"
                    l "I don't care, you should've asked me first! That's what friends do!"
                    $ fivy = "sad"
                    v "It's not like I was planning it! He invited me to check out his home studio and it just happened."
                    l "I don't wanna hear it, seriously."
                    $ fivy = "n"
                    v "Alright, I'm sorry! He's just a guy, I didn't think it'd be such a big deal to you."
                    $ flena = "sad"
                    if lena_mike_love:
                        l "You know he's not just {i}some guy{/i} to me. I really like him..."
                    else:
                        l "Well, maybe he's just not {i}some guy{/i} to me, you know..."
                    if ian_lena_dating:
                        v "You really like him that much? What about Ian?"
                        if ian_lena_couple:
                            $ flena = "worried"
                            l "That's... That's a different story."
                            v "Is it? I told you it was stupid to get into a serious relationship, but you never listen..."
                            $ flena = "serious"
                            l "We're not talking about that, now. We're talking about what you did."
                        else:
                            l "What about him? Ian doesn't have anything to do with this."
                            v "So you want all the guys to yourself?"
                            $ flena = "serious"
                            l "That's not what I'm saying, and you know that. Stop spinning the story."
                    else:
                        v "You really like him that much? I had no idea..."
                        $ flena = "serious"
                        l "Then either you don't know me that well or you just don't listen to me."
                    v "Okay, okay... As I said, I'm sorry."
                    if lena_smoke:
                        "A long, tense silence followed while Ivy and I smoked our cigarettes."
                    else:
                        "A long, tense silence followed while Ivy smoked her cigarette."
                    $ flena = "sad"
                    "Maybe I was being irrational, but I felt angry. With Ivy, but especially with Mike..."
                     
                "You're right...":
                    $ renpy.block_rollback()
                    $ flena = "sad"
                    l "..."
                    l "Am I being irrational here?"
                    v "Quite a bit, yeah... And a bit of a hypocrite."
                    $ flena = "worried"
                    $ fivy = "n"
                    v "Look, nobody's judging you. I'm not, that's for sure!"
                    v "Do what you will with Mike or whoever you want... More power to you! That's how I do it."
                    v "I'm sorry if I hurt you, I should've told you... But we barely spend time together these days!"
                    if lena_ivy < 12:
                        call friend_xp ('ivy') from _call_friend_xp_1068
                    v "Besides, Mike's just a guy... Really hot, yeah, but I didn't think it'd be such a big deal to you."
                    if lena_mike_love:
                        l "You know he's not just {i}some guy{/i} to me. I really like him..."
                    else:
                        l "Well, maybe he's just not {i}some guy{/i} to me, you know..."
                    if ian_lena_dating:
                        v "You really like him that much? What about Ian?"
                        if ian_lena_couple:
                            $ flena = "worried"
                            l "That's..."
                            v "I told you it was stupid to get into a serious relationship, but you never listen..."
                            l "I'm not proud about it, but I just can't let Mike go."
                            v "You're totally head over heels for him, huh?"
                        else:
                            l "What about him? Ian doesn't have anything to do with this..."
                            v "Since you're dating multiple guys, I didn't think you'd get mad about it..."
                            l "It's not so simple."
                    else:
                        v "You really like him that much? I had no idea..."
                        l "You would know if you paid a little bit more attention to what I tell you."
                    v "Anyway, it's nothing for you to get all riled up about. As I said, it only happened once."
                    v "He invited me to check out his home studio and he made a move on me... It would've been rude to reject him."
                    if lena_smoke:
                        "A long, tense silence followed while Ivy and I smoked our cigarettes."
                    else:
                        "A long, tense silence followed while Ivy smoked her cigarette."
                    $ flena = "sad"
                    "I couldn't help but feel hurt and cheated somehow... I felt mad at Ivy, but especially at Mike..."

            "But should I be surprised? Ivy was right, Mike was sharing his bed with his girlfriend daily, and I was the one meddling with someone else's boyfriend."
            if ian_lena_dating and lena_ian_love:
                "And I had Ian... If my feelings for him were true, then why was I losing my shit over Mike?"
            elif ian_lena_dating:
                "Besides, I was also seeing Ian simultaneously. I shouldn't lose my shit over Mike but..."
        # no dating
        else:
            $ flena = "sad"
            l "Hey... Let me ask you something."
            l "Maybe it's none of my business, but... Is there something going on between you and Mike?"
            $ fivy = "n"
            v "Mike and me? What do you mean?"
            l "Do you like him?"
            v "Well, he's really hot, and I dig his vibe..."
            l "So that's a yes..."
            v "What's this about? Are you bothered by something?"
            l "I saw how you talked to him earlier... Or should I say, how you flirted with him."
            $ fivy = "flirt"
            v "What? Don't tell me you're jealous... I thought you were done with Mike!"
            $ flena = "serious"
            l "That's not it! But I can see something's going on and you're covering it up..."
            $ fivy = "n"
            v "I'm not covering anything up... If you must know, yes, we hooked up the other day."
            $ flena = "worried"
            l "Really...?"
            v "It's not like I was planning it! He invited me to check out his home studio and it just happened."
            $ flena = "sad"
            l "I see..."
            $ flena = "serious"
            l "Well, you could've told me about it."
            v "Are you sure you're not jealous?"
            l "I said that's not it! But I'd like for my friend to tell me about these sorts of things, especially when the guy is one I've been involved with..."
            v "Alright, alright. Sorry about that... I didn't think it would be such a big deal to you."
            if ian_lena_couple:
                $ fivy = "flirt"
                v "Especially now that you have a loving boyfriend!"
            elif ian_lena_dating:
                $ fivy = "flirt"
                v "I thought you decided to focus on our boy Ian for a while..."
            $ flena = "sad"
            l "It's just... He has a girlfriend, you know."
            $ fivy = "flirt"
            v "Yeah, and I give as many fucks as you did. Meaning very few."
            if lena_smoke:
                "I couldn't argue against that. I took a long, deep drag of my cigarette in silence."
            else:
                "I couldn't argue against that. I stood quiet while Ivy smoked her cigarette."
        "Ivy broke the silence with a sudden change of topic."
    else:
        stop music fadeout 2.0
        if lena_smoke:
            "We smoked in silence for a bit. Then, after a long, deep drag, Ivy spoke."
        else:
            "Ivy smoked in silence for a moment. Then, after a long, deep drag, she spoke."
# ian breakup
    if v11_lena_breakup == "cherry":
        $ fivy = "n"
        v "How are you holding up, by the way? After Ian dumped you for Cherry, I mean..."
        $ flena = "sad"
        l "I don't wanna talk about that right now."
        if ian_lena_couple_over:
            v "As you wish... I knew getting into another relationship was a bad idea, though. You need to listen to me."
        else:
            v "As you wish... It looks like you're dealing with it just fine, if you ask me."
        l "As I said, I don't wanna talk, or think, about it."
        stop music fadeout 2.0
        v "Alright, alright..."
    elif v11_lena_breakup == "cindy":
        $ fivy = "n"
        v "How are you holding up, by the way? After Ian dumped you, I mean..."
        $ flena = "serious"
        l "I don't wanna talk about that right now."
        v "As you wish... It looks like you're dealing with it just fine, if you ask me."
        v "Good thing you didn't get into a serious relationship, or things would've been much more dramatic."
        l "As I said, I don't wanna talk, or think, about it."
        stop music fadeout 2.0
        v "Alright, alright..."
# axel cindy
    if lena_mike_dating == False:
        play music "music/tension.mp3" loop
    $ fivy = "n"
    v "So... Did you see them?" 
    menu:
        "Who?":
            $ renpy.block_rollback()
            l "See who?"
            $ fivy = "serious"
            v "Who? Axel and that blonde girl!"
            $ flena = "sad"
            l "Oh, yeah... Cindy."
            v "That one!"

        "Yes":
            $ renpy.block_rollback()
            $ flena = "sad"
            l "You mean Axel and Cindy? Yeah."
            if lena_wits < 7:
                call xp_up ('wits') from _call_xp_up_918
            $ fivy = "serious"
        
        "No":
            $ renpy.block_rollback()
            l "No..."
            $ fivy = "serious"
            v "Really? Axel is here tonight... and he's not alone."
            $ flena = "sad"
            l "Oh, yeah... He's with Cindy."
            v "So you did see them!"

    if lena_axel_dating or lena_axel_over:
        v "I can't believe Wildcats chose her before you or me. She's just so plain, so standard... and have you seen her pictures?"
    else:
        v "I can't believe Wildcats chose her before me. She's just so plain, so standard... and have you seen her pictures?"
    v "And the poses? She has {i}amateur{/i} written all over!"
    if lena_cindy > 2:
        l "I don't know... I barely know the girl, I can't really judge."
        v "Oh, come on, don't be so soft."
    elif lena_cindy > 1:
        l "I don't like her too much either, to be honest. She comes across as quite conceited for how basic she is."
        v "Unbelievable, right?"
    else:
        $ flena = "serious"
        l "Yeah, it's clear she has a high opinion of herself. She's way too conceited for how basic she is."
        v "It's disgusting, right?"
    if lena_axel_dating or lena_axel_over:
        v "Do you think she's Axel's new girl? How do you explain her getting our spot otherwise?"
        $ flena = "sad"
        v "Axel had to push her portfolio to Wildcats instead of ours, I'm sure."
    else:
        v "Do you think she's Axel's new girl? How do you explain her getting that spot in the agency otherwise?"
        $ flena = "sad"
        v "Axel had to push her portfolio to Wildcats instead of mine, I'm sure."
    v "And that only makes sense if she's been fucking him..."
    l "Well, she has a boyfriend..."
    $ fivy = "flirt"
    hide ivy
    show ivy2 at lef
    with short
    v "As if that matters! It's clear that guy's dead weight for her, even I could see that."
    v "Besides, Ian told me their relationship sucks. She's asked him for some time apart, and you know what that means!"
    if ian_cindy_sex:
        v "Though Ian's not innocent in all of this, either. I saw him and the blondie getting it on in the alley next door!"
        $ flena = "surprise"
        l "W-{w=0.5}What?! When?"
        $ fivy = "smile"
        if ian_lena_couple:
            v "Oh, it was before you two became a lovey-dovey couple... I think... Maybe May?"
            $ flena = "worried"
            l "That's barely three months ago..."
            $ flena = "serious"
            l "You never mentioned anything!"
            v "They asked me not to, and I can understand why!"
            l "And didn't you think I had a right to know?"
            v "Hey, as I said, it was before you two decided to get into this serious relationship bullcrap. If anyone should mention it to you, it's him."
            $ flena = "worried"
            l "Fucking his friend's girlfriend behind everyone's back... That's..."
            if lena_mike_sex or lena_cheating:
                v "Like you're one to talk!"
            else:
                v "Hilarious, right?"
        elif ian_lena_dating:
            v "A while back... I think it was May? Jeremy and I caught them getting it on when we went out for a smoke."
            $ flena = "worried"
            l "That's barely three months ago... You never mentioned anything!"
            v "They asked me not to, and I can understand why!"
            $ flena = "serious"
            l "And didn't you think I had a right to know?"
            v "Why, because you're fucking Ian? You two are not exclusive, right?"
            $ flena = "worried"
            l "We're not, but... Fucking his friend's girlfriend behind everyone's back... That's..."
            if lena_mike_sex or lena_cheating:
                v "Like you're one to talk!"
            else:
                v "Hilarious, right?"
        else:
            v "A while back... I think it was May? Jeremy and I caught them getting it on when we went out for a smoke."
            $ flena = "worried"
            if ian_lena_over or ian_lena_breakup:
                l "May... Ian and I were already seeing each other... How come you never mentioned anything?"
                v "They asked me not to, and I can understand why!"
                $ flena = "serious"
                l "And didn't you think I had a right to know?"
                v "Why, because you were fucking Ian? You two weren't exclusive, right?"
                $ flena = "worried"
                l "We weren't, but... Fucking his friend's girlfriend behind everyone's back... That's..."
            else:
                l "That's... Wow, I would've never thought something like that was going on."
            if lena_mike_sex or lena_cheating:
                v "Like you're one to talk!"
            else:
                v "Crazy, right?"
        if lena_mike_sex or lena_cheating:
            $ flena = "blush"
            l "There's no need for you to point it out at every chance you get."
        $ flena = "sad"
        $ fivy = "n"
        hide ivy2
        show ivy at lef
        with short
        "Ivy's revelation had left me astounded. Ian and Cindy..."
        if ian_lena_dating:
            "It was a dark secret he had been keeping from me and from all his friends, save Jeremy."
            "It didn't feel good to learn about it. Not at all."
            if ian_lena_couple:
                "Even if it happened before we became a couple, we were already deeply involved at that time. And he was fooling around with Cindy?"
                "How long did their fling last? Was it a one-time thing or was it still going on...?"
                "Thinking about that last possibility made my stomach churn. No, that couldn't possibly be the case..."
            else:
                "Ian didn't owe me fidelity, Ivy was right about that. But out of all the people he could have been with, Cindy..."
                if lena_ian_love:
                    "It made me feel hurt, and threatened. Angry."
            "To think Ian could be that kind of guy..."
            if lena_cheating:
                "Then again, I was the same kind of person. I had been committing sin after sin, and I didn't have it me to stop."
            else:
                "If Wade or Perry ever learned about it, he'd be in serious trouble. Everyone would."
        else:
            "It was a dark secret he had been keeping from all his friends, save Jeremy. If Wade ever found out..."
            "And these sorts of things tend to get to the light."  
    else:
        hide ivy2
        show ivy at lef
        with short
    $ fivy = "serious"
    v "In any case, I don't like that skinny bitch."
    v "She comes around here flaunting herself as if the club were hers, acting all confident and glamorous, when in reality she's just a nobody Axel picked up at some dive bar."
    if lena_cindy > 2:
        l "I see you really have it out for her."
    elif lena_cindy > 1:
        l "She's not winning any popularity contest with us, that's for sure."
    else:
        $ flena = "serious"
        l "I share the sentiment."
# billy
    $ fivy = "smile"
    hide ivy
    show ivy2 at lef
    with short
    v "Well, she won't be spoiling our other party this summer. We finally have a date for Billy's business trip!"
    $ flena = "n"
    if billy_model:
        l "Yeah, I know. Third weekend of August... Billy told me."
        v "I'm so glad you're coming! We haven't worked together in ages!"
        if lena_ivy > 7:
            v "It'll be so much fun with you around! I'm hyped!"
        elif lena_ivy > 4:
            v "We haven't been able to spend time together lately. Not as much as I'd like, at least!"
            v "It'll be so fun, like old times!"
        else:
            v "I know we've been having some differences lately, but I think this will help us mend some bridges..."
            v "I want to spend some time with my best friend again! It'll be so fun, like old times!"
        if lena_seymour_dating and seymour_desire:
            $ flena = "sad"
            l "The thing is... I've got this contract with my patron, and I can't work with other photographers without his authorization."
            v "Well then, ask him for it!"
            $ fivy = "flirt"
            v "This {i}patron{/i} of yours sounds really mysterious... You'll need to introduce him to me!"
            $ flena = "n"
            l "I'm not sure in which context I could do that..."
            $ fivy = "n"
            v "Context? What are you talking about?"
            $ fivy = "smile"
            v "Don't worry, I won't try and steal him from you! Ha ha ha!"
            l "That's the least of my concerns."
            v "So tell him you want to go on a trip with your best friend. What if you take some pictures with her?"
            l "I'll see what I can do."
            "I didn't need Billy's job right now, but Ivy was right... It could be fun."
        else:
            "The money would be nice, too... I needed every source of income I could get."
            if billy_trust == 2:
                "What Billy proposed sounded pretty awesome. Hopefully, his project would take off, and us with it."
            elif billy_trust == 1:
                "I was still on the fence about the viability of what Billy wanted to build, but I would at least give it a try. What was the worst that could happen?"
            else:  
                "I was highly skeptical about Billy's project being viable, but who knows... Maybe he'd manage to pull it off. What was the worst that could happen?"
    else:
        v "Third weekend of August! Are you coming or what?"
        if billy_trust == 2:
            l "Billy's project sounded awesome, but I don't know... I've never worked with him."
            v "So? There's a first time for everything!"
        elif billy_trust == 1:
            l "I'm not sure Billy's being realistic. He's pumping out his project, but I doubt he'll be able to pull it off."
            v "You're such a pessimist! But even if it fails, you're not risking anything, are you?"
        else:
            $ flena = "sad"
            l "That guy seemed full of shit, if you ask me. He acts so big, but I'm sure he's just pumping smoke."
            v "You always think the worst of people! But even if it's true and it fails, you're not risking anything, are you?"
        if lena_seymour_dating and seymour_desire:
            v "Besides, it's easy money! Plus free holidays!"
        else:
            v "Besides, you need the money, right? Or are you gonna take double shifts again?"
        $ fivy = "n"
        v "And honestly, I'd also like to spend some time with my best friend... Share some fun experiences, like in the old days!"
        if lena_ivy > 7:
            $ flena = "n"
        elif lena_ivy > 4:
            $ flena = "sad"
            v "We haven't been spending time together lately, not as much as I'd like. I don't want us drifting apart!"
        else:
            $ flena = "sad"
            v "I know we've been having some differences lately, but I think this will help us mend some bridges..."
        if seymour_desire:
            l "The thing is... I've got this contract with my patron, and I can't work with other photographers without his authorization."
            v "Well then, ask him for it!"
            $ fivy = "flirt"
            v "This {i}patron{/i} of yours sounds really mysterious... You'll need to introduce him to me!"
            $ flena = "n"
            l "I'm not sure in which context I could do that..."
            $ fivy = "n"
            v "Context? What are you talking about?"
            $ fivy = "smile"
            v "Don't worry, I won't try and steal him from you! Ha ha ha!"
            l "That's the least of my concerns."
            v "So tell him you want to go on a trip with your best friend. What if you take some pictures with her?"
            v "We can ask Billy to not publish them; problem solved."
            l "Why would he evenhire me then?"
            v "Don't worry, I'll talk him into it. I know he'll agree."
            if lena_ivy > 7:
                "I didn't need Billy's job right now, but Ivy was right... It could be fun."
        else:
            l "I'll think about it..."
    $ fivy = "flirt"
    hide ivy2
    show ivy at lef
    show ivy_smoke at lef
    with short
    $ ivy_extras = 0
    "Ivy finished her smoke and threw it on the ground."
    hide ivy_smoke with short
    v "Well, gotta get back to work!"
    v "We have a big fish in the VIP tonight and we have got to keep him entertained..."
    hide ivy
    show ivy2 at lef
    with short
    v "And hopefully, I'll manage to entertain him later too, in private, just the two of us..."
    v "That guy's such a fucking stud!"
    hide ivy2 with short
    if lena_smoke:
        show lena_smoke at truecenter
    else:
        show lena at truecenter 
    with move
    if ian_cindy_sex:
        $ flena = "sad"
        "I was left thinking about the disturbing news I had just learned. Ian and Cindy..."
        if ian_lena_dating:
            "He had hidden it from me, and from everyone else for that matter. Of course, he did."
            if ian_lena_couple:
                "I couldn't stop wondering if they were still seeing each other. I didn't want to believe it to be the case, but..."
            else:
                "I couldn't stop wondering if they were still seeing each other. And if they were, what? What should I do?"
        elif ian_lena_over or ian_lena_breakup:
            "Maybe me and Ian breaking up was the best thing that could happen. If I had learned about this when we were dating..."
            "But now it wasn't something I should concern myself with. Ian and his friend group would deal with that..."
        else:
            "I should probably just pretend like I didn't know anything. It was something for Ian and his friend group to deal with."
        "And what was the deal between Axel and Cindy? Why was everything so fucked up?"
        if lena_mike_dating:
            "And then there was Ivy's confession: she and Mike did really hook up..."
    elif lena_mike_dating:
        $ flena = "sad"
        "I was left thinking about what Ivy had just confessed. So she did have sex with Mike..."
    if lena_mike_dating:
        $ flena = "serious"
        "I was mad at Ivy for not telling me, but Mike didn't either."
        "And if what Ivy told was true, it was him who invited her to his place and made a move on her..."
        "Ivy had been in his bed, the bed he shared with his girlfriend..."
        "A bed he hadn't invited me yet to!"
    if lena_smoke:
        hide lena_smoke
        show lena
        with short
        "I finished my cigarette too and went back inside. There was work to be done."
    else:
        "I took a deep breath and went back inside. There was work to be done."
    stop music fadeout 2.0
    hide lena
    with short
    $ flena = "n"
    pause 1
    
# ROUND 2 ############################################################################################################################################33
    play music "music/edm4.mp3" loop # findme music
    if v11_bar1 == 0:
        $ v11_bar1b = 1
    if v11_rosa_fight > 1:
        $ v11_bar2b = 2
    scene blazer with long
    $ ffinley = "n"
    $ fsen = "n"
    $ fjohn = "n"
    $ feli = "n"
    $ frosa = "n"
    "People crowded around the bar, waiting to be served."

    $ temp = []
label v11bartendingrd2:

    hide lena with short
    call screen v11bartendingscreen()
    $temp_pos, temp_char = _return.split('_')
    $_return = temp_char
    show expression temp_char:
        xpos int(temp_pos)
        xanchor 0.5
    show expression temp_char with ease:
        xalign 0.30 # lef

    if temp_char == 'eli':
        show eli at lef
    elif temp_char == 'finley':
        show finley at lef
    elif temp_char == 'john':
        show john at lef
    elif temp_char == 'rosa':
        show rosa at lef
    hide expression temp_char
    show lena at rig with short

    $temp.append(_return)
    $ renpy.block_rollback()
    $ renpy.call("v11npc%s" % _return)
    return

label v11npcjohn:
    $ flena = "n"
    if (v11_bar2b == 0 and v11_bar3b) or (v11_bar2b == 0 and v11_bar4b):
        guy "Hey, can I-{w=0.3}{nw}?"
        $ frosa = "mad"
        hide john 
        show rosa at lef 
        with hpunch
        $ flena = "sad"
        girl "Hey! Hey!" 
        "She waved her credit hand in front of my face, demanding to be served."
        girl "Can you stop ignoring me, please? I've been waiting for over ten minutes!"
        jump v11rosajump
    $ v11_bar1b = 1
    "The music was loud, so I leaned over the bar to hear what the client was asking for."
    if v11_bar1 == 3:
        $ v11_bar1b = 4
        $ fjohn = "smile"
        guy "Hey, what's your name?"
        l "What?"
        guy "I said what's your name! I'm John!"
        menu:
            "I'm Lena":
                $ renpy.block_rollback()
                $ v11_john_flirt = 2
                $ flena = "smile"
                l "I'm Lena! Nice to meet you, John."
                john "I'm sure you get this all the time, but I need to say it: you're incredibly pretty!"
                l "Thanks for the compliment! Now, what can I get you?"
                john "Rum cola, please!"
                l "Here you go."
                $ fjohn = "n"
                john "Hey, I know you're busy right now, but would you like to hang out and get a coffee or a beer one of these days?"
                menu:
                    "Sure":
                        $ renpy.block_rollback()
                        $ flena = "smile"
                        $ v11_john_flirt = 3
                        l "Sure, why not?"
                        $ fjohn = "smile"
                        john "Awesome! Can I get your Peoplegram?"
                        l "Yes, it's this one... Now, I need to take care of the other customers..."
                        john "Of course! I'll text you and see when it's okay for you to get that coffee!"
                        hide john with short
                        "He seemed like a nice enough guy..."
        
                    "No, thanks":
                        $ renpy.block_rollback()
                        $ flena = "n"
                        if ian_lena_couple:
                            l "I'm sorry, but I already have a boyfriend."
                            $ fjohn = "sad"
                            john "Oh, I see. Of course, you do..."
                        else:
                            l "Sorry John, but I have to decline..."
                            $ fjohn = "sad"
                            john "Oh, that's too bad..."
                        $ fjohn = "n"
                        john "Well, I had to give it a try! I hope I didn't bother you."
                        l "No, don't worry. Now, I need to take care of the other customers..."    
                        john "Of course... Have a good one."
                        hide john with short
        
            "What can I get you?":
                $ renpy.block_rollback()
                $ v11_john_flirt = 2
                "I couldn't afford to waste time making conversation, so I just ignored his question."
                l "What can I get you?"
                $ fjohn = "sad"
                john "Um... Rum cola for me."
                l "Right away."
                "I poured his drink and moved to the next customer. It was clear he wanted to keep talking, but I had no time to indulge him."
        
            "I don't care":
                $ renpy.block_rollback()
                $ v11_john_flirt = 1
                $ flena = "serious"
                l "I don't care what your name is. Just tell me what you want to drink!"
                $ fjohn = "sad"
                john "Hey, there's no need to be so rude..."
                l "I'm busy over here, in case you didn't notice! So tell me or let me attend to someone else!"
                john "Alright, alright... Rum cola, please."
                l "Alright."
                "I had no time to waste. I poured his drink and moved to the next customer."
                $ flena = "n"

    elif v11_bar1 == 2:
        $ v11_bar1b = 3
        guy "Rum cola, please!"
        l "Okay."
        "He watched me as I served him another drink, just like last time."
        guy "..."
        l "Here it is."
        guy "Thanks..."
        "It looked like he was about to say something, but I couldn't afford to pay attention to him. I had other customers to take care of."
    elif v11_bar1 == 1:
        guy "Rum cola, please... And could you be a bit more generous this time?"
        menu:
            "Yes":
                $ renpy.block_rollback()
                $ v11_bar1b = 3
                $ flena = "sad"
                l "Yes..."
                "Maybe last time I had been a bit too conservative. I poured more rum into his glass this time."
                $ fjohn = "smile"
                guy "Thank you!"
                $ flena = "n"
                l "It's alright."
                "It looked like he was about to say something else, but I couldn't afford to pay attention to him. I had other customers to take care of."
        
            "No":
                $ renpy.block_rollback()
                $ v11_bar1b = 2
                l "Sorry, I already told you this is how we serve drinks here."
                $ fjohn = "serious"
                guy "That's bullshit! I just saw your colleague pouring a drink all the way to the top for a girl!"
                $ flena = "serious"
                menu:
                    "Ask him for a drink, then":
                        $ renpy.block_rollback()
                        l "Well then, ask him for a drink! But I doubt you'll get the same treatment!"
                        guy "Who decided to hire such a sassy bitch...?"
                        $ flena = "serious"
                        l "What did you just say?"
                        hide john with short
                        "He just turned around and left without ordering anything."
                        l "Asshole."
        
                    "Call Marcel":
                        $ renpy.block_rollback()
                        $ v11_bar1b = 1
                        $ flena = "serious"
                        "I didn't have time to deal with this guy's demands. I decided to use my earpiece and call Marcel right away."
                        show bouncer at lef3 behind john with short
                        bo "What's the problem?"
                        $ fjohn = "sad"
                        l "He's complaining about the drinks."
                        bo "You, come with me."
                        guy "What? But I didn't do anything...!"
                        bo "I don't care. Get the hell outta here."
                        $ flena = "evil"
                        hide john
                        hide bouncer 
                        with short
                        "Marcel grabbed the guy by the nape and led him away from the bar."
                        l "Nice."

    scene blazer with short
    if v11_bar2b == 0 or  v11_bar3b == 0 or v11_bar4b == 0:
        jump v11bartendingrd2
    jump v11bartendingaxel

label v11npcrosa:
    if v11_bar1b or v11_bar3b or v11_bar4b:
        girl "Finally! I was wondering how much longer you'd keep ignoring me! I've been waiting for over ten minutes!"
    else:
        girl "Finally! I've been waiting for almost ten minutes! How about you speed things up a bit?"
    girl "I came here to have fun, not to spend my whole night waiting to be served!"
    menu v11rosajump:
        "I'm sorry":
            $ renpy.block_rollback()
            $ v11_bar2b = 1
            $ flena = "sad"
            l "I'm sorry, what do you need?"
            girl "Three mojitos and six tequila shots!"
            $ flena = "serious"
            "I gritted my teeth and fulfilled her order. I was working and I didn't want to get into trouble..."

        "You don't need to be so rude":
            $ renpy.block_rollback()
            $ flena = "serious"
            l "You don't need to be so rude, you know?"
            girl "Rude? How am I rude? It's not my fault you're taking forever to do your job!"
            l "We're doing our best, but as you can see we're really busy over here."
            girl "Then stop wasting time and give me three mojitos and six tequila shots!"
            menu:
                "Serve her drinks":
                    $ renpy.block_rollback()
                    $ v11_bar2b = 1
                    "I gritted my teeth and fulfilled her order. I was working and I didn't want to get into trouble..."

                "Ignore her":
                    $ renpy.block_rollback()
                    $ v11_bar2b = 2
                    l "I don't have to deal with you."
                    show lena at rig3 with move
                    $ frosa = "mad"
                    girl "Hey! Hey! Don't you ignore me! I want my drinks!"
                    $ fjeremy = "sad"
                    show jeremy at rig with short
                    j "What's wrong?"
                    girl "I want three mojitos and six tequila shots! Now!!" with vpunch
                    j "On it..."
                    hide jeremy
                    hide rosa
                    with short
                    
                "{image=icon_mad.webp}Insult her":
                    $ renpy.block_rollback()
                    $ flena = "mad"
                    l "Shove them up your ass, you bitter cunt!"
                    jump v11rosainsult

        "{image=icon_mad.webp}Shut the fuck up" if v11_rosa_fight or v11_bar2 < 3:
            $ renpy.block_rollback()
            $ flena = "mad"
            "I had enough of her disgusting attitude."
            l "You're not the only one waiting, so shut the fuck up and let me work!"
            $ frosa = "mad"
            girl "What did you say? Who do you think you are, you fucking skank? Be careful how you talk to me!"
            l "You're such a bitter cunt, you know that?"
            label v11rosainsult:
                $ v11_bar2b = 3
                $ frosa = "mad"
            girl "What the hell did you just call me?"
            l "Are you deaf? I said you're a bitter cunt!"
            l "Now roll off to the side. Your ginormous landwhale ass is blocking the bar."
            girl "I'll fucking end you!"
            play sound "sfx/slap.mp3"
            show rosa at centerlef
            show lena at rig3
            with hpunch
            "She leaned over the bar furiously and I leaned back when she tried to pull my hair."
            menu:
                "{image=icon_athletics.webp}Punch her" if lena_athletics > 4:
                    $ renpy.block_rollback()
                    jump v11rosapunch

                "Slap her":
                    $ renpy.block_rollback()
                    jump v11rosaslap

                "Call Marcel":
                    $ renpy.block_rollback()
                    jump v11rosamarcel

    scene blazer with short
    if v11_bar1b == 0 or  v11_bar3b == 0 or v11_bar4b == 0:
        jump v11bartendingrd2
    jump v11bartendingaxel

label v11npcfinley:
    $ flena = "n"
    if (v11_bar2b == 0 and v11_bar1b) or (v11_bar2b == 0 and v11_bar4b):
        guy "Hi, can I-{w=0.3}{nw}?"
        $ frosa = "mad"
        hide finley 
        show rosa at lef 
        with hpunch
        $ flena = "sad"
        girl "Hey! Hey!" 
        "She waved her credit hand in front of my face, demanding to be served."
        girl "Can you stop ignoring me, please? I've been waiting for over ten minutes!"
        jump v11rosajump
    $ v11_bar3b = 1 
    if v11_bar3 == 3:
        $ ffinley = "smile"
        guy "Hi! Can I get three beers?"
        $ flena = "smile"
        l "Sure! This round's on you?"
        guy "Yeah, it's my turn now to treat my friends. Thank you again for the shots, by the way!"
        $ ffinley = "n"
        guy "I told Sen not to ask; I didn't want to appear rude or something like that..."
        l "No worries, it's not every day you have a birthday! Here you go. I hope you're having a great time."
        $ ffinley = "smile"
        guy "You're too nice! Can I give you a tip?"
        l "Of course! What was your name again?"
        guy "Finley."
        $ finley = "{color=#7E8DA2}Finley{/color}"
        l "Thanks, Finley! Have a great night!"
        if lena_charisma < 8:
            call xp_up ('charisma') from _call_xp_up_919
        finley "You too!"
    elif  v11_bar3 == 2:
        $ ffinley = "sad"
        guy "Hey... I'm sorry about earlier... I told my friend to cut it out, but he was already pretty drunk from before..."
        l "It's alright, no harm done. What can I get you?"
        $ ffinley = "n"
        guy "Two beers, please."
        l "Here you go. Happy birthday!"
        $ ffinley = "smile"
        guy "Oh, thanks!"
    else:
        $ ffinley = "sad"
        guy "Hey... I'm really sorry about earlier... I told my friend to cut it out, but he was already pretty drunk from before..."
        l "As long as you don't ask for more free stuff, it's okay. What do you want?"
        guy "Two beers, please..."
    scene blazer with short
    if v11_bar1b == 0 or  v11_bar2b == 0 or v11_bar4b == 0:
        jump v11bartendingrd2
    jump v11bartendingaxel

label v11npceli:
    $ flena = "n"
    eli "Hi..."
    l "What would you like?"
    if (v11_bar2b == 0 and v11_bar1b) or (v11_bar2b == 0 and v11_bar3b):
        $ frosa = "mad"
        hide eli 
        show rosa at lef 
        with hpunch
        $ flena = "sad"
        girl "Hey! Hey!" 
        "She waved her credit hand in front of my face, demanding to be served."
        girl "Can you stop ignoring me, please? I've been waiting for over ten minutes!"
        jump v11rosajump
    $ v11_bar4b = 1
    if v11_bar4 == 0:
        show lena at rig3
        show eli at lef3
        with move
        $ fjeremy = "happy"
        show jeremy with short
        j "Don't worry, I got her! Another Mojito for you, baby?"
        eli "Yes, please."
        $ fjeremy = "flirt"
        j "You know what? It's on the house... as long as you give me your Peoplegram!"
    else:
        eli "Another Mojito, please."
        if v11_bar4 > 1:
            l "Of course."
            "I noticed her watching me intently as I prepared her drink. I looked back at her and smiled."
            $ flena = "smile"
            l "Here you go."
            if v11_bar4 == 3:
                $ feli = "shy"
                eli "I took a look at your Peoplegram... So you're a model? That's amazing!"
                l "Yeah, I've been doing some modeling as a part-time job."
                eli "I loved your pictures! They're incredible... And you also play and sing, right?"
                $ flena = "shy"
                l "I try to... I've started performing live recently."
                eli "Really? I'd love to come and hear you play sometime!"
                $ flena = "happy"
                l "I announce my concerts on Peoplegram, so feel free to come next time if you can!"
                if lena_charisma < 8:
                    call xp_up ('charisma') from _call_xp_up_920
                eli "Cool."
                $ feli = "n"
                eli "Sorry, I've taken enough of your time! I can see how busy you are."
                l "It's alright. Let's talk some other time."
                eli "Yeah! Thanks, Lena!"
            else:
                eli "Thanks!"
                l "Is there something else I can help you with?"
                eli "I was just thinking it's funny how you were my customer earlier today and now the roles have been reversed."
                l "That's true! It's a funny coincidence..."
                eli "Hey, can I ask you for your Peoplegram? I'd like to follow you."
                l "Of course... Here, I'll write it down for you."
                eli "Great! Thank you."
        else:
            l "On it."
    scene blazer with short
    if v11_bar1b == 0 or v11_bar2b == 0 or v11_bar3b == 0:
        jump v11bartendingrd2
    jump v11bartendingaxel

label v11bartendingaxel:
    stop music fadeout 2.0
    scene blazer with long
    "I lost track of what time it was as work continued tirelessly through the night. The demand seemed never-ending and I was starting to feel the pressure."
    play music "music/dumb.mp3" loop
    scene v5_ivy3 with long
    pause 1
    "Only  when the go-go dancers performed I could take a bit of a breather... But that only lasted for about ten minutes or so, and there were always people wanting to be served."
    "This place was a real money-maker..."
    $ flena = "sad"
    scene blazer
    show lena at rig 
    with long
    "I stretched my sore legs before getting back to serving the next customer."

    # cindy ###############################################################################################
    
    $ flena = "worried"
    $ fcindy = "n"
    show cindy at lef with short
    c "Can I get two tequila shots?"
    $ fcindy = "sad"
    hide cindy
    show cindy2 at lef
    with short
    c "Oh. Lena..."
    $ flena = "sad"
    menu:
        "{image=icon_mad.webp}What are you doing with Axel?" if lena_axel_dating or lena_cindy < 3 or lena_axel_over:
            $ renpy.block_rollback()
            $ flena = "serious"
            l "Yeah... And I've been wondering what reason could you and Axel have to be here tonight, together..."
            $ fcindy = "serious"
            c "Well... That's none of your business, is it?"
            call friend_xp ('cindy',-1) from _call_friend_xp_1069
            $ lena_cindy = 0
            l "Maybe. Maybe I should ask Wade?"
            $ fcindy = "blush"
            l "I'm sure he'll be able to give me the answer I'm looking for, right?"
            $ fcindy = "mad"
            hide cindy2
            show cindy at lef
            with short
            c "You have no idea what you're talking about! And that's one hundred percent not your business!"
            l "Like I care. You like to pretend, but I know your type. I could see through you since the moment I met you."
            $ fcindy = "sad"
            l "You try to come across as some kind of diva, but in reality, you're just a poor, self-pitying, mediocre girl who lacks any self-esteem."
            label v11cindyfight:
                $ fcindy = "mad"
                $ flena = "evil"
            c "Y-{w=0.5}you...!"
            c "At least I don't dress up like a cheap slut!" with vpunch
            l "And there we have it. The most basic defense mechanism. How fitting."
            if lena_wits < 10:
                call xp_up ('wits') from _call_xp_up_921
            c "You think you're so smart and smug, but the truth is you love playing the victim! It's like your whole identity revolves around being the tragic ex."
            $ flena = "mad"
            l "Well, at least I have an identity beyond just being Axel's flavor of the month."
            c "You're just bitter because you know he's moving on and you're stuck serving drinks and coffees!"
            if seymour_desire:
                l "What would you know? I've already made it much further than you ever will."
                l "And I've been working and sacrificing for it, unlike your little \'modeling career\' that Axel conveniently handed to you."
            else:
                l "Oh, you mean the job that actually pays my bills? Unlike your little \'modeling career\' that Axel conveniently handed to you?"
            $ flena = "serious"
            l "Besides, don't you have a boyfriend already? What are you playing at?"
            c "Go fuck yourself!"
            hide cindy with short
            "She flipped me the bird and disappeared without her tequila shots."
            $ flena = "serious"
            l "I can't stand that bitch... I'm not going to let her strut around like she's the queen of the world."

        "Greet Cindy":
            $ renpy.block_rollback()
            $ flena = "n"
            "I put on my best poker face and greeted her."
            l "Hi, Cindy. Partying tonight?"
            $ fcindy = "n"
            hide cindy2
            show cindy at lef
            with short
            c "Yeah. Axel wanted to introduce me to some people we'll be working with..."
            l "I heard you got signed by Wildcats... Congratulations."
            $ fcindy = "smile"
            c "Yeah, I can't believe it myself! It was all thanks to Axel..."
            if lena_cindy < 3:
                call friend_xp ('cindy') from _call_friend_xp_1070
            l "So you want to become a model now?"
            $ fcindy = "serious"
            c "I'd say I already became one... Otherwise, Wildcats wouldn't have signed me, don't you think?"
            l "Yeah... Of course."
            "That was as much conversation as I could stand giving her. I tried dispatching her quickly."
            l "It was two tequila shots, right?"
            $ fcindy = "n"
            c "Yeah..."
            jump v11cindytequila

        "Pour the shots":
            $ renpy.block_rollback()
            "Last thing I wanted that night was to make conversation with her..."
            "I put on my best poker face and tried to dispatch her quickly."
            $ flena = "n"
            l "Two tequila shots coming up..."
            $ fcindy = "n"
            hide cindy2
            show cindy at lef
            with short
            c "Thanks..."
            label v11cindytequila:
                l "..."
                if v9_axel_sex:
                    "She looked at me intently while I poured her drinks. It made me feel irritated for some reason..."
                else:
                    "She looked at me intently while I poured her drinks."
                if lena_extras == "stockings":
                    c "Nice dress... But those stockings are a bit overkill, don't you think?"
                    $ flena = "worried"
                    c "I mean, the dress alone is sexy enough..."
                    $ flena = "serious"
                    if lena_cindy > 1:
                        "I had no interest in hearing her opinion, but I kept my mouth shut."
                    else:
                        l "I don't remember asking for your opinion, but thanks."
                        $ fcindy = "serious"
                elif lena_necklace != "choker2":
                    c "I like your dress... But it could've used some jewelry to complete the look, don't you think?"
                    c "Some earrings or something like that."
                    if lena_cindy > 1:
                        "I had no interest in hearing her opinion, but I kept my mouth shut."
                    else:
                        $ flena = "serious"
                        l "Thanks for your advice, but I have my own sense of style."
                        $ fcindy = "serious"
                else:
                    c "Nice outfit... Simple yet effective. I like it."
                    if lena_cindy > 1:
                        $ flena = "worried"
                        l "Thanks..."
                    else:
                        $ flena = "serious"
                        l "Thanks... I guess."
                    $ flena = "n"
                l "Here are your shots. Cash or card?"
                c "Card. Listen..."
                $ fcindy = "n"
                hide cindy
                show cindy2 at lef
                with short
                c "I hope me and Axel working together isn't a problem for you."
                $ flena = "sad"
                if lena_axel_dating or lena_axel_over:
                    c "I know he offered to push your portfolio to Wildcats too, and, I mean..."
                    c "He's told me about your history together."
                    if v9_axel_sex:
                        $ flena = "blush"
                        c "I know it's been hard for both of you to... turn the page. That's why I wanted to make sure there's no bad blood or anything like that."
                        "What did she know about us? How much had Axel told her?"
                        "Did she know about what happened that day...? I sprung back at her, defensive."
                        $ flena = "serious"
                        l "You mean it's been difficult for {i}him{/i}. I broke up with him and it took him almost a year to stop stalking me."
                        c "So it's alright then, right?"
                    else:
                        $ flena = "serious"
                        l "I don't know what he's told you, but I would take it with a grain of salt..."
                        $ fcindy = "sad"
                        l "And don't worry about the portfolio thing, I told him to leave it be. Having to work with him is a pain I won't put myself through."
                        $ fcindy = "n"
                        c "That's fine. I just wanted to make sure there's no bad blood or anything like that."
                else:
                    c "He offered to push your portfolio to Wildcats but you refused, right?"
                    c "I mean, he's told me about your history together..."
                    l "Then you understand what you're getting yourself into, right?"
                    $ fcindy = "sad"
                    l "Or has he only told you what suits him?"
                    $ fcindy = "n"
                    c "What happened between you is none of my concern... I just wanted to make sure there's no bad blood or anything like that."
                menu:
                    "{image=icon_friend.webp}Warn her about Axel" if lena_cindy > 1:
                        $ renpy.block_rollback()
                        $ flena = "serious"
                        l "Look... I'll give you a word of advice: be careful with Axel."
                        $ fcindy = "serious"
                        c "What's that supposed to mean?"
                        l "I know him better than anyone. He's charming, smart, and caring, especially at first... But he has a darker side to him."
                        c "Strange... That's kind of what he told me about you."
                        $ flena = "worried"
                        c "I'm sorry things didn't work out between you, but it doesn't look good on you speaking ill of him."
                        $ flena = "serious"
                        l "Suit yourself... I was trying to help, but if that's what you get from what I just told you..."
                        if lena_cindy > 2:
                            call friend_xp ('cindy',-1) from _call_friend_xp_1071
                        $ fcindy = "n"
                        c "Thanks, but I'd say I'm doing fine without your {i}help{/i}."
                        l "Well, then. Good luck with your modeling career. I hope you find it fulfilling."
                        c "I'm sure I will."
                        hide cindy2 with short
                        l "Ugh... How can someone be so conceited?"

                    "Dismiss her":
                        $ renpy.block_rollback()
                        "I had the impression she was trying to get a reaction out of me... So, instead, I went back to my poker face."
                        $ flena = "serious"
                        l "Look, I don't mind... Do whatever you want, I don't know why you have to come and tell me any of this."
                        $ fcindy = "serious"
                        l "Axel and I are not a thing anymore. I don't know what's the deal between you, and I don't care to know..."
                        l "So good luck with your modeling career. I hope you find it fulfilling."
                        c "I'm sure I will."
                        hide cindy2 with short
                        $ flena = "sad"
                        l "What was that about...?"

                    "{image=icon_mad.webp}Call her out" if lena_cindy < 2:
                        $ renpy.block_rollback()
                        $ flena = "serious"
                        l "Why do you feel the need to come tell me all this nonsense?"
                        $ fcindy = "sad"
                        l "Are you trying to show off so you can get a reaction out of me?"
                        c "W-{w=0.3}what? No, that's not--"
                        l "Well, here you have it: leave me out of it. I know your type, and I don't have the time or patience to deal with these pantomimes."
                        $ fcindy = "serious"
                        hide cindy2
                        show cindy at lef
                        with short
                        c "My type? Don't make me laugh. You know nothing about me."
                        l "I could see through you since the moment I met you."
                        $ fcindy = "sad"
                        l "You try to come across as some kind of diva, but in reality, you're just a poor, self-pitying, mediocre girl who lacks any self-esteem."
                        jump v11cindyfight

    if ian_lena_dating and ian_cindy_sex:
        $ flena = "worried"
        "Was Ivy telling the truth about her and Ian? She had no reason to lie, but..."
        if ian_lena_couple:
            "If not, had it been a one-time thing, or was it an ongoing affair?"
            "I didn't even want to think about the second option..."
    elif ian_cindy_sex:
        "If what Ivy had told me was true... She was already cheating on her boyfriend. With Ian."
        if ian_lena_over or ian_lena_breakup:
            $ flena = "sad"
            "I didn't like how that knowledge made me feel. To think he had been seeing Cindy while he was also dating me...?"
            "Had it been a one-time thing, or was it an ongoing affair...?"
    $ flena = "sad"
    "I had lost too much time with her already. Customers were piling up at the bar again."
    # robert fight ###################################################################
    if v11_robert_talk > 0:
        "Suddenly, I was startled by the outburst of a fight right next to the bar."
        $ frobert = "mad"
        $ fsen = "mad"
        $ ffinley = "sad"
        hide lena
        show robert at lef
        show sen at truecenter
        show finley at rig3 behind sen
        with short
        play sound "sfx/punchgym.mp3"
        show sen at rig with vpunch
        r "Get out of my face!"
        finley "Stop it, Sen! Just let it go."
        sen "Let go of me, Finley! I'm gonna kick this dude's ass so hard his kids will need therapy!"
        r "Oh, yeah? I'd like to see you try, you fat-headed cunt!"
        sen "Have you looked at yourself in the mirror, you skunky-looking dirtbag?!"
        $ frobert = "sad"
        sen "If mediocrity had a child with disappointment he'd have your face!"
        finley "Sen! Cool it down...!"
        menu:
            "Intervene":
                $ renpy.block_rollback()
                $ flena = "worried"
                show robert at lef3
                show finley at right
                show sen at rig3
                with move
                show lena with short
                l "Hey, hey! What's going on here?"
                $ frobert = "mad"
                r "Lena! This guy started it by getting in my face!"
                sen "It was this douche who made me spill my drink!"
                $ flena = "sad"
                r "Like that was my fault! I didn't do it on purpose, dipshit!"
                "Suddenly, Marcel's colossal figure appeared, making its way through the crowd like a bulldozer."
                show lena at rig
                show robert at left
                with move
                $ frobert = "sad"
                $ fsen = "sad"
                show bouncer at lef behind robert with short
                bo "What's the fucking problem here, huh!?"
                menu:
                    "{image=icon_friend.webp}Side with Robert" if lena_robert > 5:
                        $ renpy.block_rollback()
                        $ flena = "serious"
                        l "It's this guy's fault. He got in Robert's face for no reason."
                        $ fsen = "mad"
                        sen "I had a very good reason! This asshole was dancing like a clown, bothering everyone!"
                        $ frobert = "mad"
                        bo "Enough, you're coming with me."
                        $ fsen = "sad"
                        bo "Now!" with vpunch
                        $ frobert = "flirt"
                        hide sen
                        hide finley
                        hide marcel
                        with short
                        if lena_robert < 6:
                            call friend_xp ('robert',3) from _call_friend_xp_1072
                        elif lena_robert < 10:
                            call friend_xp ('robert',2) from _call_friend_xp_1073
                        elif lena_robert < 12:
                            call friend_xp ('robert') from _call_friend_xp_1074
                        if lena_robert_dating:
                            r "Thanks, baby. I knew you had my back."
                            $ flena = "n"
                            l "I didn't like that guy."
                            r "Yeah, he was an asshole. Are you sure you don't want me to come over tonight?"
                            r "Or maybe we could sneak into the bathroom right now..."
                            l "Robert, I need to get back to work."
                            $ frobert = "n"
                            r "Um, sure..."
                        elif lena_robert_over:
                            r "Thanks, baby... Seems like you still like me after all..."
                            l "You're drunk, Robert. Go home."
                            $ frobert = "sad"
                            r "Uh... Okay."
                        else:
                            $ v11_robert_talk = 2
                            $ frobert = "n"
                            r "Uh, thanks, Lena..."
                            $ flena = "sad"
                            l "I don't think you were at fault this time, but don't get into trouble again, alright?"
                            r "Will do."
                        hide robert with short

                    "Side with Sen":
                        $ renpy.block_rollback()
                        $ flena = "serious"
                        "I pointed my finger at Robert."
                        l "It's this guy's fault. He's always causing trouble."
                        r "What!?" with vpunch
                        bo "Alright, you're out. Now."
                        if lena_robert_dating:
                            r "But Lena...! Why are you doing this to me?"
                        elif v11_robert_talk == 2:
                            r "Lena, no, that's unfair! I already said I'm sorry...!"
                        else:
                            r "What the fuck...! Lena, hey!"
                        l "You're drunk, Robert. Go home."
                        bo "You heard the lady. Let's go!"
                        play sound "sfx/punchgym.mp3"
                        pause 0.2
                        hide bouncer
                        hide robert
                        with vpunch
                        "Marcel put Robert in a headlock and dragged him out of the club."
                        if lena_robert > 0:
                            call friend_xp('robert',-1) from _call_friend_xp_1075
                        show lena at truecenter with move
                        $ ffinley = "smile"
                        finley "Thanks..."
                        $ flena = "n"
                        l "Try not to get into trouble, alright?"
                        sen "Yeah... Sorry about that."
                        $ v11_robert_talk = 0
                            
                    "{image=icon_mad.webp}Both are at fault":
                        $ renpy.block_rollback()
                        $ v11barpoints -= 1 
                        $ flena = "serious"
                        l "Both of them are making a ruckus! We can't work like this!"
                        bo "Alright, both of you, out. Now."
                        if lena_robert_dating:
                            r "But Lena...! Why are you doing this to me?"
                        elif v11_robert_talk == 2:
                            r "Lena, no, that's unfair! I already said I'm sorry...!"
                        else:
                            r "But..."
                        if lena_robert > 0:
                            call friend_xp('robert',-1) from _call_friend_xp_1076
                        bo "I said now!"
                        play sound "sfx/punchgym.mp3"
                        hide bouncer
                        hide robert
                        with vpunch
                        "Marcel put Robert in a headlock and dragged him out of the club."
                        bo "You two, let's go!"
                        hide sen
                        hide finley
                        hide marcel
                        with short
                        $ flena = "n"
                        l "That's better."
                        $ v11_robert_talk = 0

                    "{image=icon_charisma.webp}There's no problem here" if lena_charisma > 6:
                        $ renpy.block_rollback()
                        $ v11barpoints += 1 
                        $ flena = "n"
                        l "It's alright, Marcel. There's no problem here."
                        $ flena = "serious"
                        l "Right, guys?"
                        r "Y-{w=0.3}yeah..."
                        sen "Sure. It's all good."
                        bo "Alright... But I'm keeping an eye on you. Next time I need to show up you're both out."
                        hide bouncer with short
                        $ frobert = "serious"
                        $ fsen = "n"
                        r "You saved yourself an ass-kicking..."
                        $ flena = "serious"
                        l "Shoo, Robert! Go get some fresh air or something!"
                        $ frobert = "sad"
                        pause 0.5
                        hide robert with short
                        $ ffinley = "smile"
                        finley "Thanks..."
                        $ flena = "n"
                        l "Try not to get into trouble, alright?"
                        if lena_wits < 10:
                            call xp_up ('wits') from _call_xp_up_922
                        sen "Yeah... Sorry about that."

            "Ignore them":
                $ renpy.block_rollback()
                "I had no time or interest in dealing with that. Marcel would, shortly." 
                $ flena = "n"
                

    # jack #############################################################################################
    scene blazer with long
    pause 1
    show lena with long
    "I turned around to notice a tall and muscular guy leaning over the bar and looking at me."
    $ flena = "n"
    show lena at rig with move
    show jack at lef with short
    l "Yes?"
    jk "Hey. I haven't seen you before."
    jk "Are you one of the dancers?"
    menu:
        "I wish I was!":
            $ renpy.block_rollback()
            $ lena_jack = 2
            $ v11barpoints += 1 
            $ flena = "sad"
            l "I wish I was! They have it easier than us bartenders!"
            $ fjack = "smile"
            jk "That's probably true, ha ha."
            $ flena = "n"
            $ fjack = "smile"
            jk "I'm Jack."
            $ jk = "{color=#CC0000}Jack{/color}"
            l "Lena. Nice to meet you..."
            jk "Well, Lena, let me say that you're the hottest girl I've seen all night."
            
        "Why do you ask?":
            $ renpy.block_rollback()
            $ lena_jack = 1
            l "Me, a dancer? Why do you ask?"
            $ fjack = "smile"
            jk "They can't hold a candle to you. You're the hottest girl I've seen all night."

        "Obviously not":
            $ renpy.block_rollback()
            $ flena = "worried"
            l "Isn't that obvious? I'm the bartender."
            l "What do you want?"
            jk "I'm sure you're getting a lot of customers tonight... You're the hottest girl I've seen all night."
        
    menu:
        "{image=icon_lust.webp}You're pretty hot yourself..." if (lena_lust > 5 and lena_jack > 0 and ian_lena_couple == False) or (lena_lust > 5 and lena_jack > 0 and lena_cheating) or (lena_lust > 7 and lena_jack > 0):
            $ renpy.block_rollback()
            $ flena = "flirt"
            l "Well, thanks... You're pretty hot yourself!"
            "He looked directly into my eyes, like a hunter, and a cocky smile appeared on his handsome lips."
            $ fjack = "smile"
            jk "I'm glad you like it..."
            $ fjack = "n"
            if lena_jack < 2:
                $ lena_jack = 2
                jk "I'm Jack."
                $ jk = "{color=#CC0000}Jack{/color}"
                l "Lena. Nice to meet you..."
            jk "Well then, Lena. If you feel like sharing a glass of champagne before ending the night, drop by the VIP."
            jk "I'd like you to show me some of your dance moves, too."
            hide jack with short
            $ flena = "slutshy"
            l "Damn, that guy's so hot...! What a hulking frame... His arms were like tree trunks!"
            if lena_bdick > 2 or lena_fty_bbc:
                $ flena = "slut"
                l "I wonder if what he's hiding under those pants is equally big..."
            l "Wait, is this the guy Ivy mentioned before? I think I remember him now..."
            $ flena = "shy"
            l "He used to appear in one of those dating reality shows on TV a few years ago, and now he's some kind of influencer."
            l "Ugh...! I should focus on work..."

        "Ignore his compliment":
            $ renpy.block_rollback()
            $ lena_jack = 1
            $ flena = "worried"
            "I decided to act as if I hadn't heard his compliment. I wasn't interested in flirting with this guy."
            $ flena = "n"
            $ fjack = "n"
            l "So, what can I get you?"
            jk "Oh, I'm well served, thanks. If you want to share a glass of champagne though, come join me at the VIP."
            hide jack with short
            l "No, thanks."
            if lena_wits < 8:
                call xp_up ('wits') from _call_xp_up_923

        "{image=icon_mad.webp}Diss him" if lena_jack < 2:
            $ renpy.block_rollback()
            $ lena_jack = 0
            $ flena = "serious"
            l "Thanks, but I don't remember asking for your opinion."
            jk "Hey, I was being nice..."
            l "Well Mr. Nice Guy, behind you there's a club full of girls to hit on. So don't give me more work than I already have tonight."
            $ fjack = "serious"
            if lena_charisma < 8:
                call xp_up ('charisma') from _call_xp_up_924
            jk "Someone's in a foul mood today. I guess being hot didn't help you get a better job than serving drinks..."
            jk "I wonder why."
            $ flena = "mad"
            if seymour_desire:
                l "What do you know?! Fuck off, beefcake!" with vpunch
            else:
                l "Fuck off, beefcake!" with vpunch
            if jess_bad:
                jk "Bitch."
            else:
                jk "Whatever. Enjoy your night."
            hide jack with short
            l "Ugh...! I hate these kind of guys!"
            $ flena = "serious"
            "I knew I'd have to deal with his type tonight, but it still got on my nerves. Especially when I was so stressed out!"
    
    # bartending end ##############################################################################################################################################
    scene blazer with long
    "The frenzy continued through the night, as I kept serving drinks and clients got progressively drunk."
    stop music fadeout 3.0
    if lena_smoke:
        "I was dying to go outside and have another smoke, but there was no time to take even a short break. People just kept piling up at the bar."
    else:
        "I was dying to go outside and get some air, but there was no time to take even a short break. People just kept piling up at the bar."
    play music "music/edm.mp3" loop # findme music
    "Only when the crowd started to thin out did I realize how late it was."
    $ flena = "n"
    $ fjeremy = "smile"
    show lena at lef
    show jeremy at rig
    with short
    l "Seems like some people are starting to head home already."
    j "Yeah, we're through the worst of it! Now the club will begin emptying until closing time."
    l "Work's not over yet, though."
    hide jeremy with short
    show lena at rig with move
    # axel ###################################################################
    $ flena = "worried"
    $ faxel = "smile"
    show axel at lef 
    with short
    x "Good night, Lena..."
    "The moment I had been trying to avoid all night inevitably happened, as I knew it would."
    if axel_disposition == 2:
        $ flena = "sad"
        l "Hi, Axel... What can I get you?"
        x "A gin and tonic, please."
        l "Alright..."
        x "I didn't believe Cindy when she told me you were working the bar tonight, but it's true..." 
        x "It seems we're bound to run into each other, huh? It's weird how life works."
        l "So it would seem."
        $ faxel = "n"
        x "I hope I'm not bothering you. It just seemed rude to not come say hi before we left."
        l "It's alright, Axel, don't worry."
    if axel_disposition == 1:
        $ flena = "sad"
        l "Axel...What do you want?"
        x "Just to get a drink and say hi."
        x "Cindy told me you were here tonight. It seemed rude to not say anything before we left..."
        x "Looks like we're bound to run into each other, huh? It's weird how life works."
        l "Often times I wish it wasn't so weird."
        $ faxel = "n"
        x "Don't worry. I won't be a source of trouble for you tonight, I promise."
    if axel_disposition == 0:
        $ flena = "serious"
        l "What do you want, Axel?"
        $ faxel = "n"
        x "I just wanted to get a drink and say hi, that's all. Cindy told me you were here tonight."
        $ faxel = "smile"
        x "It seems we're bound to run into each other, huh? It's weird how life works."
        l "Yeah... And when that happens, there's always trouble."
        $ faxel = "n"
        x "Don't worry. I have no intention of being a source of trouble for you tonight."
    if lena_axel_dating == False and lena_axel_over == False:
        x "By the way... I know I didn't get the chance to apologize for last time."
        l "I don't wanna talk about that."
        x "Sure. So..."
        $ faxel = "smile"
        x "What are you doing here? Do you work here now?"
    else:
        x "So... What are you doing here? You work here now?"
    menu:
        "{image=icon_friend.webp}Make conversation" if axel_disposition > 0:
            $ renpy.block_rollback()
            $ axel_disposition += 1 
            $ flena = "n"
            l "Just for tonight... I'm covering for someone."
            if lena_seymour_dating:
                x "Right... It seemed weird for you to work here when you already have Mr. Ward's patronage."
                if seymour_desire:
                    $ flena = "blush"
                    l "Yeah..."
                    $ flena = "n"
                    l "I'm still getting used to it, but it's nice to finally have someone who can support me."
                    $ faxel = "n"
                    x "It's clear he's seen something special in you..."
                    $ faxel = "smile"
                    x "The same could be said for anyone who gets to know you, though."
                else:
                    $ flena = "sad"
                    l "Well, that's no longer the case..."
                    $ faxel = "sad"
                    x "It's not? Did something happen?"
                    l "I just... didn't feel comfortable with his way of doing things."
                    $ faxel = "n"
                    x "I see. That's a pity, though. He's a very useful ally to have..."
                    $ faxel = "smile"
                    x "But you always had a mind of your own. And you're not afraid to work hard..."
                    $ flena = "n"
                    l "I'm already used to it."
                    x "I know you are. But sometimes it's not wrong to accept a bit of help."
            else:
                x "I see. Seems like a hard job."
                l "I'm used to those."
                $ faxel = "n"
                x "Too bad you didn't accept Mr. Ward's offer. It would've been so helpful considering your situation..."
                $ flena = "serious"
                l "I'll deal with my situation myself, like I've always done."
                x "I know you will. But sometimes it's not wrong to accept a bit of help."
            if axel_disposition == 2:
                $ flena = "sad"
            else:
                $ flena = "n"
            l "What about you? Are you celebrating something tonight?"
            jump v11axelcindy
        
        "What are {i}you{/i} doing here?":
            $ renpy.block_rollback()
            if axel_disposition == 2:
                $ flena = "sad"
            else:
                $ flena = "serious"
            l "What about you? What are you doing here tonight?"
            label v11axelcindy:
                l "I see you brought some company..."
            $ faxel = "smile"
            x "Yes... We got together with a couple of agents and another photographer I'll be working with this summer."
            x "The agency has booked a few shoots for next week. We'll be hopping on a yacht and sailing across the islands, visiting exotic beaches and picturesque towns..."
            if lena_axel_dating or lena_axel_over:
                $ flena = "sad"
                l "Sounds quite nice..."
                $ faxel = "n"
                x "It does... I would've liked you to have the opportunity, but the agency chose someone else..." 
                if v9_axel_sex:
                    $ flena = "serious"
                    l "Did they, or was it you who chose her instead of Ivy or me?"
                    l "She's the girl you told me you had started seeing, right?"
                    x "You mean Cindy? Believe it or not, I had nothing to do with the agency's decision. They liked Cindy's profile and picked her."
                    x "And about your second question... Why do you wanna know?"
                    $ flena = "worried"
                    x "I mean, we can talk about it, but it feels kind of weird discussing it with you, considering our history..."
                    $ flena = "serious"
                    l "Then don't... You don't owe me any explanations, not anymore. And the same is true for me."
                    $ faxel = "smile"
                    x "That's right..."
                    if axel_disposition == 2:
                        x "Still, I must say you're looking gorgeous tonight..."
                        $ flena = "blush"
                        l "Thanks... Anyway, what did you want?"
                    else:
                        l "Anyway, what do you want? I have work to do..." 
                    jump v11axelserve
                else:
                    if lena_axel_over:
                        $ flena = "serious"
                        l "It's alright. I wouldn't feel comfortable in that situation anyway."
                        l "Ivy's not exactly happy about it, though. She had a lot of expectations of you."
                    else:
                        $ flena = "n"
                        l "It's alright... I didn't have any expectations anyway. The same cannot be said about Ivy, though..."
                        l "She's quite disappointed in you."
                    x "Well, the final decision wasn't in my hands. I just took some pictures and pushed her portfolio to Wildcats; if she's not happy with that, it's not my problem."
                    $ flena = "n"
                    jump v11axelcindy2
            else:
                $ flena = "n"
                l "Sounds nice... Ivy's not happy about it, though. She was expecting you to get her a job with Wildcats."
                $ faxel = "n"   
                x "Well, the final decision wasn't in my hands. I just took some pictures and pushed her portfolio to Wildcats; if she's not happy with that, it's not my problem."
                label v11axelcindy2:
                    l "And they chose an amateur?"
                    $ faxel = "smile"
                    x "Cindy might be an amateur, but she shows great promise. Her beauty is something special..."
                    menu:
                        "If you say so":
                            $ renpy.block_rollback()
                            l "If you say so..."
                            x "I didn't think you were the jealous type..."
                            $ flena = "serious"
                            l "You were taking your time to say something stupid, and here it is."
                            $ faxel = "happy"
                            x "My bad. But I can feel your animosity toward her..."
                            l "I get that reaction to anything that has to do with you, it seems."
                            x "Alright, ouch. But I'm past trying to change that..."
                            if axel_disposition == 2:
                                x "Still, I must say you're looking gorgeous tonight..."
                                $ flena = "blush"
                                l "Thanks... Anyway, what did you want?"
                            else:
                                l "Anyway, what do you want? I have work to do..."   
                            jump v11axelserve

                        "What's the deal between you two?":
                            $ renpy.block_rollback()
                            $ flena = "serious"
                            l "You're praising her a lot... What's the deal between you two?"
                            if v9_axel_sex:
                                l "She's the girl you told me you had started seeing, isn't she?"
                            $ faxel = "n"
                            x "Why do you wanna know?"
                            $ flena = "sad"
                            x "I mean, we can talk about it, but it feels kind of weird discussing it with you, considering our history..."
                            $ flena = "serious"
                            l "Then don't... You don't owe me any explanations, not anymore. And the same it's true for me."
                            $ faxel = "smile"
                            x "That's right..."
                            if axel_disposition == 2:
                                x "Still, I must say you're looking gorgeous tonight..."
                                $ flena = "blush"
                                l "Thanks... Anyway, what did you want?"
                            else:
                                l "Anyway, what do you want? I have work to do..." 
                            jump v11axelserve

                        "What do you want?":
                            $ renpy.block_rollback()
                            $ flena = "n"
                            l "Anyway, what do you want? I have work to do..."   
                            label v11axelserve:
                                $ faxel = "smile"
                                x "A gin and tonic, please."
                                l "Here you go..."
                            # shot together
                            if lena_axel_dating or lena_axel_over:
                                $ faxel = "n"
                                x "Listen. About the agency thing..."
                                x "I'll start by working with Cindy, but I'm sure if they like my work, they'll let me recommend another model. I'd like that to be you..."
                                $ flena = "sad"
                                $ faxel = "smile"
                                x "They really liked your portfolio, in fact. They were close to choosing you instead of Cindy, but in the end, they went with her."
                                if lena_axel_over:
                                    $ flena = "serious"
                                    l "There's no need. I already told you to drop it off. I'll manage on my own."
                                    if v9_axel_sex:
                                        $ faxel = "n"
                                        x "There's no way you'll accept my help, is there?"
                                        $ flena = "sad"
                                        x "I thought things were okay between us after last night at my place..."
                                        $ flena = "worried"
                                        l "Yeah. I mean..."
                                        $ flena = "serious"
                                        l "You wanted closure and you got it, right? Let's just move on, each one on their own way."
                                        $ faxel = "n"
                                        x "You're right... I got it, but I'm not entirely sure that was the case for you."
                                        $ flena = "blush"
                                        l "What makes you think that?"
                                        x "I can tell you're still tense around me. If there's anything that's bothering you, you can talk to me about it..."
                                        $ flena = "serious"
                                        l "I don't know what you're talking about... I'm fine with how things are, too."
                                        $ faxel = "smile"
                                        x "Alright, I won't press you on it. Just know that if you need to talk, you can always hit me up. I want you to do well..."
                                    else:
                                        $ faxel = "n"
                                        x "Alright, I won't insist anymore."
                                elif v9_axel_sex:
                                    l "It's alright... I'm sure there'll be other chances."
                                    x "You know, I'm glad we can work together again. I know things between us have been bad, and there are some scars that will never fade..."
                                    $ flena = "sad"
                                    x "But I'm happy we both were able to move past that and get the closure we needed."
                                    if lena_axel_desire:
                                        $ flena = "blush"
                                        $ faxel = "happy"
                                        x "There are a lot of things I'll miss, though. It will be hard to find someone who I click with as well as I did with you..." 
                                        l "Well... That's how it is. We've both managed to turn the page..."
                                        $ faxel = "n"
                                        x "Yes. At least, that's how I've felt after what happened at my place... But I can tell you're still tense around me."
                                        x "If there's anything that's bothering you, you can talk to me about it..."
                                        l "It's nothing... I'm fine with how things are, too."
                                        $ faxel = "smile"
                                        x "I know you enough to know that's not entirely true... But I also know I shouldn't press you about it."
                                        x "Just know that if you need to talk, you can always hit me up. I want you to do well..."
                                    else:
                                        $ flena = "worried"
                                        l "Yes... We've both managed to turn the page..."
                                        $ faxel = "happy"
                                        x "There are a lot of things I'll miss, though. It will be hard to find someone who I click with as well as I did with you..." 
                                else:
                                    l "Sounds like a lot has to happen before that. Let me know when the chance is there."
                                    x "I will..."
                                    
        "{image=icon_mad.webp}I can't talk right now" if axel_disposition < 2:
            $ renpy.block_rollback()
            $ flena = "serious"
            l "I can't talk right now, Axel. So, if you don't mind..."
            $ faxel = "smile"
            x "Of course... I'll get a gin and tonic, please."
            if lena_axel_dating or lena_axel_over:
                $ flena = "sad"
                l "Alright..."
                x "Tonight I got together with a couple of agents and another photographer I'll be working with this summer."
                x "If they like my work with Cindy, I'm sure they'll let me push another model. I'd like her to be you, if you're interested..."
                if lena_axel_over:
                    l "No, thanks... I already told you to drop it off. I'll manage on my own."
                    x "Alright, I won't insist anymore."
                else:
                    l "Sounds like a lot has to happen before that. Let me know when the chance is there."
                    x "I will..."
            else:
                l "Here you go."

    # mike appears
    $ fmike = "smile"
    show lena at rig3
    show axel at lef3
    with move
    show mike with short
    $ flena = "worried"
    $ faxel = "n"
    if lena_mike_sex:
        mk "Hey beautiful, can I get some water...?"
    else:
        mk "Hey, can I get some water...?"
    if v10_axel_fight != "ian":
        $ fmike = "serious"
        $ faxel = "serious"
        mk "Oh, it's you again... I hope you're more relaxed tonight."
        x "Yeah. I only get nervous when I have to deal with certain clowns."
        $ faxel = "smile"
        x "Have a good night, Lena. It was nice seeing you."
        hide axel with short
        show lena at rig
        show mike at lef
        with move
        $ fmike = "n"
        mk "What an asshole... Did you really use to date him?"
        l "Yeah..."
        $ fmike = "serious"
        mk "I hope he doesn't cause trouble tonight."
        l "Yeah, me too."
    else:
        $ fmike = "n"
        $ faxel = "n"
        x "Wait a second, friend."
        show axel at truecenter
        show mike at lef3
        with move
        $ faxel = "smile"
        x "Anyway, I won't take any more of your time. It was nice seeing you, Lena."
        x "Have a good night."
        hide axel with short
        show lena at rig
        show mike at lef
        with move
        mk "That was your ex, right?"
        l "Yeah..."
        mk "The one who was causing trouble during Ivy's birthday... I get the feeling he has a chip on his shoulder."
    
    $ faxel = "smile"
    $ fcindy = "shy"
    scene blazer
    show axel at rig
    show cindy2 at centerlef
    with short
    show axel at centerrig behind cindy2 with move
    "I watched Axel go back to Cindy and hand her the drink. She showed him a bright smile and toasted."
    play sound "sfx/toast.mp3"
    if v9_axel_sex:
        "I considered what happened during our last photo shoot a mistake, but maybe Ivy was right and it was exactly what Axel needed to move on."
        "I should feel relieved... Things should be calm now that Axel's interest seemed to be focused on someone else."
        if lena_axel_desire:
            "But that was not how I felt. Quite the opposite..."
            "Something inside of me was growling and retorting, triggering a lot of conflicting and confusing emotions."
        else:
            "It wasn't that easy, though. A part of me felt troubled, but I couldn't put my finger on the exact reason."
        "All I knew was I didn't like what I was seeing..."
    else:
        "I felt kind of relieved... Things should be calm now that Axel's interest seemed to be focused on someone else."
        "But I knew some kind of drama was in store, waiting for the right moment to ensue. That was always the case with Axel."
        "Hopefully, this time I wouldn't be involved in it in any way."

    # mike ###################################################################
    $ fmike = "smile"
    scene blazer
    show lena2 at rig
    show mike2 at lef
    with short
    mk "So, how's the night going?"
    $ flena = "n"
    l "Surviving! Jeremy says the worst is behind us, so that's good."
    l "What about you? Are you done at the DJ table?"
    mk "Yeah, my turn's over tonight. I just need a bottle of water before heading home."
    if lena_mike_dating:
        $ flena = "serious"
        hide lena2
        show lena at rig
        with short
        l "I heard about you and Ivy tonight... I didn't know you two were hooking up, too."
        $ fmike = "n"
        mk "I see... Does that bother you?"
        l "What do you think?"
        mk "It seems obvious it does, but I'm not sure why."
        l "Do I really have to tell you? I would've liked a heads-up if you were planning to get into my closest friend's panties."
        mk "I didn't know I had to report back to you... I mean, I haven't told my girlfriend about you either."
        l "Maybe you should!"
        $ fmike = "serious"
        if ian_lena_couple:
            mk "Is that what you really want? Are you gonna tell your boyfriend about me too?"
            $ flena = "worried"
            l "That's..."
            $ flena = "serious"
        else:
            mk "Is that what you really want?"
        if lena_mike_love:
            l "I thought what you and I had was something... special."
            l "But it seems you're okay cheating on your girl with anyone, not just with me."
        if ian_lena_dating or lena_robert_dating or v10_wc_bj == "mark" or v11_mark_sex or v8_jeremy_sex or v10_jeremy_3some or v10_ivy_sex > 1 or lena_holly_dating or v11_lena_holly_sex:
            mk "I don't think you have the right to complain. I'm not asking you to report to me every time you hook up with someone. And I know you've been sleeping around too."
            $ flena = "worried"
            if ian_lena_couple and lena_cheating:
                mk "And not just with your boyfriend."
            if v10_wc_bj == "mark" and v11_mark_sex:
                mk "Should I get mad at the fact that you sucked Mark off the other night? Or that you're planning to spend the night with him today?"
            elif v10_wc_bj == "mark":
                mk "Should I get mad at the fact that you sucked Mark off the other night? Or that you're planning to spend the night with him today?"
            if ian_lena_dating and ian_lena_couple == False:
                mk "You've been dating that guy, Ian, and I don't even know what relationship you have with him, but you don't see me getting upset about it."
            if v8_jeremy_sex or v10_jeremy_3some:
                mk "I also know you're having fun teasing Jeremy. I'm not gonna get offended and complain about it."
            if lena_robert_dating:
                mk "What about that guy you've been fooling around with? The one who used to work with you at the restaurant... I think I've seen him around here tonight."
            if v10_ivy_sex > 1:
                mk "Not to speak about you getting laid with Ivy too... Yeah, I know about that too."
                l "That's different..."
                mk "I'm sure it is."
            $ flena = "mad"
            l "So that's all I am to you? A simple booty call?"
        else:
            l "I guess I'm just a simple booty call for you, after all."
        mk "If that's how you wanna see things... I'm not sure what you were expecting of me, anyway."
        l "Yeah, I don't know either..."
        mk "Then that's how it is, isn't it? Now, can I get my bottle of water, please?"
        $ flena = "mad"
        l "Sure. Don't choke on it."
        hide mike2 with short
        l "..."
        $ flena = "worried"
        show lena at truecenter with move
        "I regretted what I said as soon as Mike disappeared into the crowd."
        "Why was I getting like this over him and Ivy? Maybe I should've focused my anger on her instead of Mike?"
        "I felt betrayed, but I knew I had no right to feel that way. After all, it was me Mike had been cheating on his girlfriend with..."
        if lena_mike_love:
            "But that made me feel special. I wanted to be special to Mike, just like he was to me..."
        else:
            "And yet... I liked that. It made me feel special. Special to Mike... like he was becoming special to me."
        $ flena = "sad"
        l "What now? I can't stop messing up..."
        "I had no time to dwell on what just happened. I still had work to take care of before the night was over."
    else:
        l "Heading home to your girlfriend?"
        mk "Yeah... Why do you ask?"
        $ flena = "sad"
        l "Oh, it's nothing..."
        $ flena = "n"
        l "You wanted some water, right? Here you go."
        mk "Thanks. See you around, Lena."
        hide mike2 with short
        $ flena = "sad"
        show lena2 at truecenter with move
        if lena_mike_sex:
            "I was tempted to bring up what Ivy had told me before, but I decided it was none of my business. Not anymore..."
            "And before Ivy, I slept with Mike too. My hands were also dirty."
        else:
            "I thought about what Jeremy had told me before."
            "If it was true and Mike's girl learned about what he had been up to, well... That was none of my business."
            if lena_cheating:
                "I was the last person who should judge Mike, after all... I was guilty of the same kind of sins."

# bathroom break ##############################################################################################################################################
    scene blazer with long
    "The club was gradually emptying, with only the most spirited customers still ordering drinks."
    "Closing time was just a few minutes away."
    $ flena = "n"
    show lena at lef with short
    show jeremy at rig3 with short
    l "Hey, Jeremy. I need to take a bathroom break."
    j "Sure! I'll hold the fort!"
    hide jeremy with short
    show lena at truecenter with move
    $ flena = "sad"
    show lena with short
    l "I'm beat... This is definitely more exhausting than working at the restaurant." 
    if v11_bbc == "jeremy":
        $ flena = "flirt"
        "The night had started in a very fun way, though. Too bad I couldn't get some more privacy with Jeremy tonight..."
    elif v11_bbc == "marcel":
        $ flena = "flirt"
        "The night had started in a very fun and unexpected way, though. I had been able to enjoy a new, mouth-watering big black cock..."
        if ian_lena_couple:
            $ flena = "worried"
            "I was really worried about Jeremy walking in on us. If he decided to tell Ian about what he saw..."
        elif ian_lena_dating:
            $ flena = "sad"
            "Too bad Jeremy walked in on us... Hopefully, he wouldn't gossip about what he had seen. Especially to Ian..."
        else:
            "Too bad I didn't get the time or intimacy to enjoy it thoroughly, but there would be another chance for sure."
    if lena_mike_dating:
        $ flena = "worried"
        "My argument with Mike had left a bad taste in my mouth. It was the first time we had fought, and I didn't know if I should be angry with him, Ivy, or myself."
        "After all, what did I expect?"
        "I knew from the beginning that Mike had a girlfriend, and despite how intense our relationship was becoming, he didn't seem to have any intentions of breaking up with her."
        if ian_lena_couple:
            "I wasn't thinking about breaking up with Ian either, but..."
        $ flena = "serious"
        "What truly irritated me was the fact that he had invited Ivy to his place when I hadn't even set foot there."
        "I always invited Mike to my bed, but I hadn't been to his... But Ivy had."
        $ flena = "sad"
        "Was she more special than me? Did he prefer her? Or perhaps I was just a mere pastime, another girl on Mike's long list of flings?"
        if v11_mark_sex:
            $ flena = "n"
            l "At least I have other men interested in me too... Tonight Mark will entertain me. I hope he shows me a good time; I really need it."
            if v11_ian_sex:
                l "Which reminds me... I should text Ian."
        elif v11_ian_sex:
            l "At least I have Ian... I should text him."
    # ian text
    if v11_ian_sex:
        if lena_mike_dating and v11_mark_sex:
            "The plan was to spend the night at his place, but Mark's offering had made me change my mind."
        elif lena_mike_dating:
            l "I'm supposed to go to his place when I'm done here."
        elif v11_mark_sex:
            l "I should text Ian..."
            $ flena = "flirtshy"
            "The plan was to spend the night at his place, but Mark's offering had made me change my mind."
        else:
            l "I should text Ian... Hopefully, he's still awake."
        $ flena = "n"
        "I pulled out my phone and saw he had already texted me."
        nvl clear
        i_p "How's your night going? Surviving?"
        if v11_mark_sex:
            $ flena = "slutshy"
            l_p "Barely {image=emoji_ups.webp} We're almost done, but I'm dead tired..."
            l_p "I think I'll head home after this. I'm sorry, I know I told you to wait for me tonight... {image=emoji_sad.webp}"
            play sound "sfx/sms.mp3"
            i_p "It's alright, I understand. I'm pretty tired too... We'll see each other soon anyway."
            if ian_lena_couple:
                i_p "Stay strong for the rest of your night! I love you {image=emoji_smile.webp}"
                l_p "Yes {image=emoji_smile.webp} Good night, I love you too!"
            else:
                i_p "Stay strong for the rest of your night! I'll be seeing you very soon {image=emoji_smile.webp}"
                l_p "Thanks, handsome! {image=emoji_kiss.webp}"
            $ flena = "flirtshy"
            l "I'm so naughty..."
        else:
            l_p "Yes, I'm almost done! How about you, are you tired?"
            play sound "sfx/sms.mp3"
            i_p "I'm okay, watching a show and waiting for you {image=emoji_smile.webp}"
            l_p "I won't take long now. See you in a bit, handsome {image=emoji_kiss.webp}"                
    elif v11_mark_sex and lena_mike_dating == False:
        $ flena = "flirtshy"
        l "I can't wait to call it a night... I hope Mark will show me a good time; I really need it!"
    else:
        $ flena = "n"
        l "I'm almost done for tonight... I can't wait to get home."
# jack flirt
    if lena_jack == 2 and v9_axel_sex == False:
        label gallery_CH11_S27:
            if _in_replay:
                call setup_CH11_S27 from _call_setup_CH11_S27
        show lena at rig with move
        $ fjack = "n"
        show jack at lef with short
        $ flena = "shy"
        jk "Hey, babe... I was hoping you'd join me for that glass of champagne after all."
        menu:
            "{image=icon_sad.webp}I have a boyfriend..." if ian_lena_couple:
                $ renpy.block_rollback()
                label v11jackbf:
                    $ flena = "sad"
                l "Yeah, about that... I have a boyfriend, you see..."
                jk "And I should be concerned about that because...?"
                if lena_cheating:
                    l "Because I shouldn't cheat on him..."
                    $ fjack = "smile"
                    jk "Come on, you and I both know that's not something you're really worried about. I'm sure it wouldn't be the first time."
                    $ flena = "blush"
                    l "And what makes you think that?"
                    $ fjack = "n"
                    jk "Let's just say I have a sixth sense for that kind of thing. I recognize an adventurous girl when I see one."
                else:
                    $ flena = "serious"
                    l "Because I'm not about to cheat on him with a guy who I just met."
                    $ fjack = "smile"
                    jk "Is that your way of asking me to convince you?"
                    $ flena = "blush"
                    $ fjack = "n"
                    jk "I can play that game, but we both know you're up for a little adventure. Something you can't get from your boyfriend..."
                jk "So, are you brave enough to go for what you want?"
                menu:
                    "{image=icon_lust.webp}Cheat on Ian" if lena_cheating or lena_lust > 7:
                        $ renpy.block_rollback()
                        if lena_cheating:
                            $ flena = "flirt"
                            l "I am..."
                            "He was right. This wouldn't be the first time I cheated on Ian, and so far it had been a very exciting experience."
                            "I couldn't let this chance slip, could I?"
                        else:
                            $ flena = "flirtshy"
                            l "Maybe I am..."
                            "I knew I shouldn't, but... This chance was just too tempting to let it slip."
                            "After all, what was the worst thing that could happen? This would be a harmless, one-and-done affair..."
                        jump v11jacksexscene

                    "Not gonna happen":
                        $ renpy.block_rollback()
                        $ flena = "serious"
                        $ lena_jack = 0
                        l "Sorry, but that ain't gonna happen."
                        $ fjack = "serious"
                        jk "So you're one of those girls who likes to tease and then play innocent, huh? I thought you were interested..."
                        $ flena = "n"
                        l "It was nice meeting you, Jack, but that's all you're gonna get from me."
                        $ fjack = "n"
                        jk "Alright then... I'm already expected in someone's bed tonight anyway."
                        l "I'm sure you are. Have fun."
                        if lena_charisma < 8:
                            call xp_up ('charisma') from _call_xp_up_939
                        hide jack with short
                        $ renpy.end_replay()
            
            "{image=icon_lust.webp}I wanted to..." if ian_lena_couple == False or lena_cheating:
                $ renpy.block_rollback()
                $ flena = "sad"
                l "I wanted to, you know... But I'm working tonight. I couldn't leave the bar."
                $ fjack = "n"
                jk "You're such a hard-working girl, aren't you? But I'd swear you also know how to have a good time..."
                $ flena = "shy"
                l "When I get the chance, yes."
                $ fjack = "smile"
                jk "Well, here's your chance. I was leaving already, but I still have time for a little adventure with you."
                $ flena = "flirt"
                l "And what kind of adventure are you offering?"
                jk "The only way you'll know is by jumping on board."
                $ flena = "slut"
                l "You know how to tempt a girl."
                jk "I'll take that as a yes."
                jump v11jacksexscene

            "I've been busy":
                $ renpy.block_rollback()
                $ flena = "n"
                l "I've been a bit busy tonight... I'm sure you noticed the bar was pretty crowded."
                $ fjack = "smile"
                jk "You're such a responsible girl, aren't you? But I'd swear you also know how to have a good time..."
                menu:
                    "{image=icon_sad.webp}I have a boyfriend..." if ian_lena_couple:
                        $ renpy.block_rollback()
                        jump v11jackbf
                
                    "{image=icon_lust.webp}Flirt back" if ian_lena_couple == False or lena_cheating:
                        $ renpy.block_rollback()
                        $ flena = "flirt"
                        l "Oh, yeah? And what makes you think that?"
                        jk "Let's just say I have a sixth sense for that kind of thing. I recognize an adventurous girl when I see one."
                        l "Adventurous, huh? And what kind of adventure are you offering?"
                        $ fjack = "n"
                        jk "The only way you'll know is by jumping on board."
                        $ flena = "slutshy"
                        l "You know how to tempt a girl."
                        jk "I'll take that as a yes."
                        jump v11jacksexscene
        
                    "I can't tonight":
                        $ renpy.block_rollback()
                        l "Sorry, I can't tonight. As I said, I'm busy working."
                        $ fjack = "n"
                        jump v11rejectjack
        
            "Not gonna happen":
                $ renpy.block_rollback()
                $ flena = "n"
                l "Sorry, but that ain't gonna happen."
                label v11rejectjack:
                    jk "So you're one of those girls who likes to tease and then play innocent, huh? I thought you might be interested..."
                    l "I have other things in my mind right now. It was nice meeting you, Jack, but that's all you're gonna get from me."
                    if ian_lena_couple:
                        l "Besides, I already have a boyfriend..."
                    jk "Playing tough, huh? Too bad."
                    jk "I was just leaving anyway... I'm expected in someone's bed."
                    l "I'm sure you are. Have fun."
                    if lena_charisma < 8:
                        call xp_up ('charisma') from _call_xp_up_940
                    hide jack with short
                    if v11_mark_sex:
                        $ flena = "flirtshy"
                        l "I can't wait to call it a night... I hope Mark will show me a good time; I really need it!"
                    else:
                        $ flena = "n"
                        l "I'm almost done for tonight... I can't wait to get home."
                    $ renpy.end_replay()
    
## AXEL SCENE ##################################################################################################3
    if v9_axel_sex:
        label gallery_CH11_S24:
            if _in_replay:
                call setup_CH11_S24 from _call_setup_CH11_S24
        $ flena = "sad"
        show lena at rig with move
        $ faxel = "smile"
        show axel at lef with short
        "Just as I was about to enter the bathroom, I stumbled upon Axel, alone."
        x "Oh. We cross paths again..."
        l "I thought you were leaving."
        x "We're about to, but it seems I get the chance to say goodbye properly."
        $ faxel = "n"
        x "We were talking about something important before that guy interrupted us..."
        if axel_disposition > 1:
            $ flena = "blush"
            l "That's true..."
            x "I said what I wanted to say, but I feel there's something I need to clarify..."
            "Axel had been opening up to me before Mike cut our conversation short. I wanted to hear what else he had to say..."
            l "I'm listening..."
        else:
            $ flena = "serious"
            l "I believe we said everything that needed to be said..."
            x "You know that's not true. That would take a lot more time... Time I know you don't have tonight."
            $ flena = "sad"
            x "But I feel I need to clarify something before we leave..."
            l "What is it?"
        x "It's about Cindy. As you said, we don't owe each other any explanations, but I just wanted you to know there's nothing between us."
        $ flena = "sad"
        x "I mean, I know she would like there to be, but right now I'm not available. I'm still figuring some things out."
        l "Which things?"
        x "How I feel and what I want in life... and where you fit in all of this."
        l "Me...?"
        x "Things are different now, I know. We're no longer together... and that's fine."
        if lena_axel_over:
            x "But, one way or another, you're still part of my life, even if we won't shoot together again. You always will be."
        else:
            x "But you're still part of my life, and not just because we're still working together. You always will be."
        $ flena = "blush"
        menu:
            "{image=icon_love.webp}You will always be part of mine, too":
                $ renpy.block_rollback()
                $ lena_axel_desire = True
                $ axel_disposition = 3
                stop music fadeout 3.0
                "Axel's words made my heart shrink. We had been at odds, but in the end, we shared something I couldn't simply cast away."
                "Everything changed, nothing ever stayed the same."
                "In this maelstrom that was life, I felt the need to hold onto something that would always be there..."
                "And Axel had been that something to me... until I abandoned it. I thought that would make things right, but..."
                "I was still confused. I was still lost."
                "I still felt the painful bond that never disappeared."
                l "And you will always be part of mine, too... Nothing can take away the moments we shared."
                $ faxel = "smile"
                if lena_charisma < 10:
                    call xp_up ('charisma') from _call_xp_up_925
                "He smiled at me."
                x "I know things are different now, and that's alright. But even if that's the case..."
                x "I hope we can keep sharing new moments."
                show axel at centerlef with move

            "...":
                $ renpy.block_rollback()
                stop music fadeout 3.0
                "I couldn't find a proper response to Axel's words. What he was saying both enticed and frightened me at the same time."
                "Everything changed, nothing ever stayed the same."
                "In this maelstrom that was life, I felt the need to hold onto something that would always be there..."
                if lena_axel_desire:
                    "And Axel had been that something... until I abandoned it. I thought that would make things right, but..."
                else:
                    "I thought Axel could be that to me, but chaos hadn't disappeared from my life. Not back then, and not now..."
                $ flena = "blush"
                l "I... I don't know what to say..."
                $ faxel = "smile"
                "He smiled at me."
                x "We always understood each other better without words, anyway."
                show axel at centerlef with move

            "{image=icon_mad.webp}No, I'm not" if lena_axel_desire == False:
                $ renpy.block_rollback()
                $ axel_disposition = 2
                $ flena = "sad"
                l "No, I'm not... Not anymore."
                $ faxel = "n"
                if lena_axel_dating:
                    l "Our current relationship is strictly professional, and that's how I want it to be."
                    l "All things considered, I think it's better if we keep our distance."
                else:
                    l "I already told you working together wasn't a good idea after all, and I think it's better if we keep our distance."
                x "Even if destiny seems to be pushing us together?"
                l "I don't believe that to be the case. We follow different paths now, and that's how it's meant to be."
                if lena_wits < 10:
                    call xp_up ('wits') from _call_xp_up_926
                x "..."
                $ faxel = "smile"
                x "Of course. That's how it is. Thanks..."
                x "I hope we can get along, even if it's from afar."
                l "Me too..."
                x "Have a good night, Lena. See you next time coincidence brings us together."
                hide axel with short
                l "..."
                "I let out a long sigh of relief."
                l "Did he really move on...?"
                "I wished he had, but now I had my doubts. I regretted once again sleeping with him that last time..."
                "Did that mistake add new fuel to the ashes of our relationship? I didn't want to go through that drama again..."
                jump v11clubend
        
        # kiss
        play music "music/sex_dark.mp3" loop
        scene v11_axel1b
        if lena_necklace == "choker2":
            show v11_axel1_earrings
        with long
        pause 1
        if lena_axel_desire:
            "My body reacted on its own when Axel's hand caressed my chin and he leaned forward."
            "I closed my eyes and parted my lips, accepting his kiss, and tasting his warmth once again."
            "The world spiraled around me in a moment of vertigo, Axel's hand on my chin being the only thing holding me in place."
            "I had no will to deny him. Doing so would've been going against my own nature."
        else:
            l "...!" with vpunch
            "My entire body tensed up when Axel's hand held my chin and his lips pressed down on mine, like struck by an electric shock."
            menu:
                "Kiss him back":
                    $ renpy.block_rollback()
                    $ lena_axel_desire = True
                    "I couldn't find the will to deny him. Doing so would've been going against my own nature."
                    "I closed my eyes and parted my lips, accepting his kiss, and tasting his warmth once again."
                    "The world spiraled around me in a moment of vertigo, Axel's hand on my chin being the only thing holding me in place."
            
                "Stop it...!":
                    $ renpy.block_rollback()
                    "The world spiraled around me in a moment of vertigo, and I knew I was falling."
                    "I tried to get a grip on myself, desperate."
                    play sound "sfx/punchgym.mp3"
                    $ flena = "blush"
                    $ faxel = "n"
                    scene blazer 
                    show lena at rig
                    show axel at lef
                    with hpunch
                    l "No, Axel...!"
                    "He looked at me with an unwavering stare, silent. I couldn't find a single clue to read what was going through his mind."
                    l "This is... What happened last time was a mistake on both of our parts. We're..."
                    scene v11_axel1b
                    if lena_necklace == "choker2":
                        show v11_axel1_earrings
                    with long
                    "I couldn't finish my sentence. I didn't even know what I was trying to say anyway."
                    "I closed my eyes and parted my lips, accepting his kiss on instinct, betrayed by it."

        "My body loosened when Axel's grip tightened around my face. Our tongues met in a slow and intense dance."
        if lena_axel_desire:
            "Everything else disappeared from my mind: I was trapped in this moment, and nothing else had a place in my world."
            "Only him, and what he made me feel..."
        else:
            "I was fumbling for control, and it was slipping from me."
        $ flena = "crazy"
        $ faxel = "n"
        scene blazer
        show lena2 at rig
        show axel at lef
        with short
        if lena_axel_desire:
            "When the kiss ended, I opened my eyes to find Axel's pupils staring deep into mine."
        else:
            "The kiss only ended when Axel willed it. I opened my eyes to find Axel's pupils staring deep into mine."
        "Without uttering a word, he held my hand and led me into the bathroom."
        hide lena2
        hide axel
        with short
        if lena_axel_desire:
            "And I obeyed, equally silent."
        scene blazer_wc with long
        pause 0.5
        # scene kiss 2
        scene v11_axel1
        if lena_necklace == "choker2":
            show v11_axel1_earrings
        with long
        if lena_axel_desire:
            "He pushed me into one of the stalls while we continued to make out passionately, with increasing urgency."
            "I didn't get to see if we were alone in the bathroom, but I didn't even care."
        else:
            "Without even checking if someone else was inside, Axel pushed me into one of the stalls and kissed me again."
            "My hands hovered over his shoulders, unable to find the strength to even try pushing him away."
        if lena_drugs:
            "I felt like I was on drugs again, the room spiraling around me like a whirlwind of colors."
        else:
            "I felt like I was drunk, the room spiraling around me like a whirlwind of colors."
        if lena_axel_desire:
            "I had been apart from Axel for far too long, since that day at his apartment... and I hadn't been able to stop thinking about it."
            "Now we were finally coming together once more, and my shattered world was suddenly restored."
        else:
            "I told myself what happened last time was a mistake. One I wouldn't repeat."
            "But I hadn't been able to stop thinking about it. And now that Axel's hands were on me again..."
        # fingering
        if v11_lena_dress == 2:
            scene v11_axel2b
        elif v11_lena_dress == 4:
            scene v11_axel2d
        else:
            scene v11_axel2a
        if lena_extras == "stockings":
            show v11_axel2_stockings
        if lena_necklace == "choker2":
            show v11_axel2_earrings
        if lena_tattoo3:
            show v11_axel2_t2
        if lena_tattoo1:
            show v11_axel2_t1
        with long
        "Everything that had come between us seemed to evaporate the moment I felt his fingers slide inside me."
        "We had been drifting apart in the current, but now we were reunited like it was meant to be..."
        "My body knew it, and it reacted accordingly. I shivered and trembled, subjugated by a thrilling pleasure only Axel could make me feel."
        if lena_lust < 10:
            call xp_up ('lust') from _call_xp_up_927
        x "This is what you want, isn't it? You're too stubborn to tell me... But that's alright."
        "The walls I had tried to put up crumbled. All the tension that had been building up since we were last together was unraveling, and my body and soul with it."
        "They coiled around Axel, welcoming him deeper inside me in all the ways possible."
        # mouth
        scene v11_axel3
        if lena_necklace == "choker2":
            show v11_axel3_earrings
        with long
        "Axel's fingers slid out of my pussy and caressed my chin and lips, making their way into my mouth."
        "I tasted myself as his fingers pushed deeper, invading my throat and blocking my airways. It was a comforting feeling..."
        x "No one knows you better than I do. The real you... The girl you are when you let go of everything."
        "His low, raspy voice made me shiver again."
        if lena_axel_desire:
            "I felt the juices of my burning furnace dripping down my thighs, my knees weak and shaking with desire."
            x "And you know me better than anyone, too. You know who I am. You know what I want."
        else:
            "My knees buckled, weak, and shaking with anticipation. I couldn't find the will to deny him."
            "Doing so would've been going against my own nature..."
        stop music fadeout 2.0
        "Axel's hand applied a firm but gentle pressure on my jaw, and I understood exactly what he was ordering me to do."
        play music "music/sex_vixen.mp3" loop
        # beg
        scene v11_axel4
        if lena_necklace == "choker2":
            show v11_axel4_earrings
        with long
        "I dropped down to my knees in front of him, letting go of everything, and surrendering to Axel's spell."
        "His fingers left my mouth, but I kept it open and accessible for him."
        x "Do you want it?"
        l "{i}Uh-huh...{/i}"
        x "Is that so? Tell me. Tell me what you want, babygirl."
        play sound "sfx/mh2.mp3"
        "I let out an anguished moan, opening my mouth wider and sticking my tongue out."
        "I knew what Axel wanted from me were not words. What he wanted me to tell him couldn't be said with them."
        "I panted and wiggled my hips, looking at him with imploring eyes."
        x "Very well..."
        play sound "sfx/zipper.mp3"
        scene v11_axel4b 
        if lena_necklace == "choker2":
            show v11_axel4_earrings
        with long
        pause 1
        "Axel undid his zipper and pulled out his bulging manhood, hovering in front of me just out of reach."
        "I moaned with desperation, my heart thundering in my chest. I wanted to jump on it, wrap my lips around it, and take it deep within me..."
        "But I stayed still, begging Axel to give me what I wanted. What I needed."
        x "That's a good girl... And good girls deserve a treat."
        "His fingers caressed my hair, and then I felt them grasping it and forcing me to tilt my head forward."
        if lena_axel_desire:
            # bj
            scene v11_axel5a
            with long
            pause 1
            "A potent shiver shocked my body when I finally felt Axel's hot, hard flesh on my lips."
            "His hands held my head firmly as he slowly slid his cock between them, letting out a soft groan."
            x "That's it... It fits so perfectly..."
            scene v11_axel5b with short
            scene v11_axel5c with short
            l "{i}Ghhhhk...{/i}!"
            "I felt his rigid member stretching my docile throat. It slid almost entirely with ease, just like last time."
            x "So good... Did you miss me?"
            pause 0.5
            scene v11_axel5b with fps
            scene v11_axel5a with fps
            l "{i}Nhhh...! Mhpf...{/i}" 
            x "I missed you too, babygirl."  
            play sound "sfx/dp1.mp3"
            scene v11_axel5_animation1 with fps
            pause 4
            if lena_lust < 10:
                call xp_up ('lust') from _call_xp_up_928
            "I knew he was telling the truth. I could feel it, hard and swollen, fucking my mouth."
            "Axel's strong hands held my head firmly, guiding it in sync with the lustful sway of his hips."
            "I was back in that place I had only found with him. A place where I could let go of everything and give up control." 
            "It made me feel at ease, reassured, and also..."
            scene v11_axel5a with fps
            x "That's it... Suck it like the good little slut you are..."
            play sound "sfx/gag1.mp3"
            scene v11_axel5_animation2 with fps
            x "My good little slut."
            "And also, it made me really fucking horny...!"
            "Entrusting myself to Axel, offering my body to him, letting him push it to its limits, being used and ravaged by him..."
            "It aroused a hungry fire that spread through my belly and consumed my brain, awakening my most primal and lustful self."
            "A tempest of emotions only a man like Axel could brave and quell."
            scene v11_axel5c with vpunch
            pause 0.5
            # breathe
            play sound "sfx/oh4.mp3"
            scene v11_axel4c 
            if lena_necklace == "choker2":
                show v11_axel4_earrings
            with long
            pause 1
            x "Are you mine?"
            "His voice sounded like a raspy, perilous growl of barely-contained craving."
            l "I am..."
            x "Say it."
            l "I'm yours... Daddy."
            # sex
            scene v11_axel6
            with long
            "Axel held me by my armpits and lifted me up to my feet with a sudden pull, making me feel like I was weightless."
            "He pushed me against the wall and I felt him aim his cock at my slit with eager, almost desperate movements."
            "I knew using that word would finally make him lose his cool."
            play sound "sfx/oh1.mp3"
            scene v11_axel7
            with vpunch
            l "Oh, fuck...!"
            scene v11_axel6_animation with fps
            "I began moaning loudly when Axel began ramming his cock into me, driving the entire shaft in a single thrust that pushed my cervix."
            "I didn't care we were in a dirty, public bathroom. I felt equally dirty, excited, and unbridled."
            "Maybe to quiet me down, maybe led by his desire to devour me, Axel wrapped his hands around my neck, gripping it tightly."
            "It was difficult to breathe, but I could get just enough air. My whole body was in pins and needles, and it felt so, so good..."
        else:
            "I was back in that place I had only found with Axel. A place where I could let go of everything and give up control." 
        "At that moment, I had the clear realization I belonged to Axel."
        "Despite all that happened... Despite all the sorrow and anguish I endured, and despite all my fears..."
        "I still belonged to him, and what truly terrified me was that ceasing to be true."
        "Something, or someone, coming between us. Tearing him apart from me. Like Cherry did... And like Cindy wanted to do."
        "And just then, like my fears had been physically manifested..."
        ## axel end
        stop music
        play sound "sfx/punchgym.mp3"
        scene blazer_wc
        with hpunch
        if lena_axel_desire:
            $ lena_axel_desire = 2
            "Two thick and strong arms clinched Axel's torso and yanked him away from me, his cock sliding out in a flash, and my joy with it."
        else:
            $ lena_axel_desire = 1
            "Two thick and strong arms clinched Axel's torso and yanked him away from me, suddenly shattering the spell of the moment."
        play music "music/fight2.mp3" loop
        $ flena = "surprise"
        $ faxel = "sad"
        $ fmarcel = "sad"
        show lenaunder at right
        show marcel at lef3
        show axel 
        show axel_dick
        with short
        bo "Lena!?"
        $ faxel = "mad"
        l "...!"
        $ faxel = "furious"
        play sound "sfx/slap.mp3"
        show marcel at left
        show axel at lef
        show axel_dick at lef
        with hpunch
        play sound "sfx/fall.mp3"
        $ fmarcel = "mad"
        $ flena = "drama"
        with vpunch
        $ renpy.end_replay()
        $ gallery_unlock_scene("CH11_S24")
        "Axel wrestled himself out of Marcel's embrace and pushed him against the wall, making the entire bathroom rumble."
        show axel at truecenter 
        show axel_dick at truecenter
        with move
        bo "You fucker...!"
        show marcel at lef4 with move
        "Marcel advanced menacingly and Axel was prepared to meet him."
        "He was about to step forward too, but I intervened."
        show lenaunder at rig2 with move
        l "Axel, no! Stop!"
        $ faxel = "sad"
        show lenaunder at rig3
        show axel at centerrig 
        show axel_dick at centerrig
        with move
        "He turned his attention to me as I held him in my arms, and went back to his senses."
        $ faxel = "serious"
        $ flena = "worried"
        bo "Come on, Lena! You know you can't be doing this in here!"
        bo "And what the fuck's up with your dude? Does he want to get his face smashed in or what?"
        $ flena = "drama"
        l "No, he doesn't! I'm sorry Marcel, this is my fault. We'll leave right away."
        bo "You can stay, but he's getting out. Either that or {i}I'm{/i} getting him out."
        $ faxel = "mad"
        x "..."
        stop music fadeout 3.0
        $ flena = "sad"
        l "Axel, please. Do as he says."
        $ faxel = "n"
        x "Alright... I'll leave, but don't touch me again. I will not have it."
        bo "Whatever you say, tough guy. Just put your fucking cock back in your pants and get the hell outta here, now."
        hide axel_dick with short
        pause 0.5
        hide axel 
        hide marcel
        with short
        $ flena = "worried"
        l "..."
        scene blazer with short
        "I put my clothes back together before leaving the bathroom and crossing the dance floor while avoiding eye contact with everyone."
        "I wished I could turn invisible."
        $ flena = "drama"
        play music "music/club_outside.mp3" loop
        scene blazer_outside with long
        show lena  with short
        "I finally made it past the crowd and I took a deep breath of fresh air. My entire body was still shaking."
        "It was like someone else had taken control of my body a few moments ago, and now she had suddenly vanished, leaving me back in charge."
        "I could barely believe what I just did. What had just happened was..."
        show lena at rig with move
        $ fivy = "sad"
        $ flena = "worried"
        show ivy at lef with short
        v "Lena! What happened?"
        v "I saw you storming out of the club... Where are you going?"
        l "I'm..."
        $ flena = "sad"
        if lena_smoke:
            l "Do you have a smoke?"
            v "Do you see any pockets on this outfit?"
            l "Fuck... I could really use one right now."
        l "I need a minute."
        hide lena with short
        pause 0.5
        hide ivy with short
        stop music fadeout 2.0
        "I had to take a few minutes to calm down and come to terms with what had just happened."
        if ian_lena_couple:
            "It was bad... Really bad."
            if lena_robert_dating or lena_mike_dating or v11_bbc == "marcel" or v11_mark_sex:
                "I had been cheating on Ian with other people, but in those cases, I felt I was in control of the situation. With Axel, though..."
            else:
                "I told myself I wouldn't cheat on Ian again. I told myself I would stay away from Axel, but..."
        else:
            "It wasn't good... Not good at all."
            "I told myself I would stay away from Axel. That I wouldn't make the same mistake again, that I refused to go backward, but..."
        "I had been trying to fool myself. I knew this could happen again. No..."
        "I had been wanting for it to happen, even if I didn't want to admit it..."
        "And now I was more confused than ever."
    jump v11clubend

# JACK ############################
label v11jacksexscene:
    $ lena_jack = 3
    play music "music/sex_vixen.mp3" loop
    scene v11_jack1 
    if lena_necklace == "choker2":
        show v11_axel1_earrings
    with long
    pause 1
    "He leaned forward without breaking eye contact or losing that playful, confident smile, and I felt his hand on my chin before his lips kissed mine."
    "My thoughts scattered like leaves in the wind as the kiss deepened, my every nerve attuned to his touch."
    "We had only exchanged a few words, but the heat between us was impossible to ignore."
    "He made his dominant attitude clear to me, unapologetic and shameless. It was easy for me to tell he was a man who never hesitated to go after what he wanted, and was accustomed to getting it."
    "I knew he could have any girl he wanted, and it was obvious he knew it too. Yet it was me who had captivated him..."
    "It was flattering, and so exciting. I couldn't deny the pull of his seductive aura, a magnetic force that drew me deeper into the heat of the moment."
    if lena_mike_dating:
        "I almost wished Mike could see us now... Would he feel the same way I felt regarding him and Ivy?"
        "At least it would teach him a lesson... I had no trouble finding someone else to substitute him with."
    $ flena = "crazy"
    $ fjack = "n"
    scene blazer
    show jack at lef
    show lena2 at rig
    with long
    "After a long, passionate kiss, he pulled back and held me by the waist with one of his thick arms."
    "He was a hunter, and I was his prey; his aim, unwavering. And his shot had been accurate, igniting a primal emotion in me."
    "I wanted to put myself in his hands and see what sensations a man like Jack could make me feel..."
    jk "Come with me... I need to have you, right now."
    scene blazer with long
    scene blazer_wc with long
    "He led me to the men's restroom. At this late hour, there were only a couple of drunk guys inside, but I didn't pay them any mind. I couldn't care less."
    play sound "sfx/door.mp3"
    show lena at rig
    show jack at lef
    with short
    "Jack took me by the hand and pushed me into a stall, closing the door behind us"
    "I could sense a growing passion in his gestures and in his deep, penetrating blue eyes, like he could barely hold the reigns of his lust for me."
    "He was ready to devour me, and I was eager to let him."
    play sound "sfx/ah8.mp3"
    # fingering
    scene v11_jack2a
    if lena_necklace == "choker2":
        show v11_axel2_earrings
    if v11_lena_dress == 2:
        show v11_jack2b
    if v11_lena_dress == 4:
        show v11_jack2c
    if lena_tattoo3:
        show v11_axel2_t2
    if lena_tattoo1:
        show v11_axel2_t1
    if lena_extras == "stockings":
        show v11_axel2_stockings 
    with long
    pause 1
    "I gasped when his fingers slid down my belly and into my pussy. They were thick and strong, and they explored me with an expert's touch."
    "Jack kissed my neck and whispered into my ear as he entered deeper into my burning pussy."
    jk "You're so wet, princess... Do I turn you on?"
    "I was struck by a potent shiver that made me moan and tingle."
    l "Yes...!"
    if ian_lena_couple:
        if lena_cheating:
            "The intense thrill of doing something wrong and forbidden was taking over me. Cheating was so exciting...!"
        else:
            "Another man's hand was touching my privates, making me feel good... I really was about to cheat on Ian..."
            "I knew I was doing something wrong, but for some reason I found it so extremely thrilling...!"
        if v11_mark_sex:
            "I was already planning on doing it tonight with Mark, but I couldn't refuse the adventure Jack was offering me. I wanted to discover what a man like him was like..."
        else:
            "Unexpected at this was, I couldn't refuse the adventure Jack was offering me. I wanted to discover what a man like him was like..."
    elif v11_mark_sex:
        "My plan was to end the night with Mark, but I couldn't refuse the adventure Jack was offering me. I wanted to discover what a man like him was like..."
    elif v11_ian_sex:
        "I was supposed to save my energy for Ian tonight, but I couldn't refuse the adventure Jack was offering me. I wanted to discover what a man like him was like..."
    elif ian_lena_dating:
        "I told Ian tonight I would be too tired to end the night with him, yet I couldn't refuse the adventure Jack was offering me. I wanted to discover what a man like him was like..."
    else:
        "Unexpected at this was, I couldn't refuse the adventure Jack was offering me. I wanted to discover what a man like him was like..."
    "I reached down with my hand and cupped Jack's crotch, grasping his erection over the fly. What I felt was more than promising..."
    jk "What's that? You wanna see it?"
    "My answer came with a moan as Jack's fingers continued to make me experience the throes of pleasure."
    l "Yes, I want it... Show it to me. Show me your cock...!"
    jk "Well then, get on your knees."
    # beg
    scene v11_axel4
    show v11_jack8_hand
    if lena_necklace == "choker2":
        show v11_axel4_earrings
    with long
    stop music fadeout 3.0
    "His dominant attitude and commanding tone made me want to obey without a second thought."
    "I wanted to unearth the treasure he had for me, to get the prize my lust desired. My body knew what I had to do for him to give it to me."
    jk "Good girl... You totally deserve it."     
    play sound "sfx/zipper.mp3"
    play music "music/sex_perilous.mp3" loop
    scene v11_jack8
    if lena_necklace == "choker2":
        show v11_axel4_earrings
    with long
    pause 1
    "Jack undid his zipper and pulled out his manhood, hovering in front of me just out of reach."
    "It was massive, just like I had been expecting, thick, long and bulging, and rather intimidating..."
    if v11_bbc:
        "Tonight was my lucky night... I got the chance of enjoying such exceptional cocks, almost too big for me to handle."
    elif lena_jeremy_sex or v8_jeremy_sex:
        "But that only made me want it more. I loved the challenge of handling a cock this big, and tonight was my lucky night."
    else:
        "But that only made me want it more. It had been a long time since I handled a cock this big, but I was definetely up to the challenge."
    "I let out an excited moan, opening my mouth wider and sticking my tongue out."
    jk "What's that? Looks like you like it... Do you want it?"
    l "Yes... Let me taste it..."
    "I panted and wiggled my hips, looking at him with hungry eyes."
    jk "Damn... It's all yours, princess."
    "His fingers caressed my hair, and then I felt them grasping it and inviting me to tilt my head forward."
    # bj
    scene v11_jack3a
    with long
    pause 1
    "A potent shiver shocked my body when I finally felt Jacks's hot, hard flesh on my lips."
    "His hands held my head firmly as he slowly slid his cock between them, letting out a soft groan."
    jk "Oh yeah... Your mouth feels so soft, so good..."
    scene v11_jack3b with short
    scene v11_jack3c with short
    l "{i}Ghhhhk...{/i}!"
    "I felt his rigid member stretching my docile throat, and to my surprise I was able to fit almost all of it."
    "It had been a long time since someone had used me in this way..."
    jk "Damn... This is a nice surprise. I had a hunch you'd be good at this, but your sexy mouth seems to be made for my cock..."
    pause 0.5
    scene v11_jack3b with fps
    scene v11_jack3a with fps
    l "{i}Nhhh...! Mhpf...{/i}" 
    jk "I'm gonna be sure to enjoy you thorougly."  
    play sound "sfx/dp1.mp3"
    scene v11_jack3_animation1 with fps
    pause 4
    if lena_lust < 10:
        call xp_up ('lust') from _call_xp_up_941
    "Jack's strong hands held my head firmly, guiding it in sync with the lustful sway of his hips."
    "I felt his manhood, hard and swollen, stretching my throat. The way he fucked my mouth was both assertive and considerate, testing my limits."
    "With each thrust he was able to fit in a bit more of his herculean member."
    "I felt the grip of Jack's iron fingers becoming more unrelenting, his hips accelerating as his pleasure and lust for me grew."    
    play sound "sfx/gag1.mp3"
    scene v11_jack3_animation2 with fps
    "I let myself go, entrusting myself to this dominant and confident man I had just met."
    "He made me feel I could offer my body to him, letting him push it to its limits, being used and ravaged by him..."
    "It aroused a hungry fire that spread through my belly and consumed my brain, awakening my most primal and lustful self."
    "I didn't care about the consequences; my moral judgment had been suspended and I was only able to live in the present moment, entirely captive to my intense emotions."
    scene v11_jack3c with vpunch
    pause 0.5
    # breathe
    play sound "sfx/oh4.mp3"
    scene v11_jack8b 
    if lena_necklace == "choker2":
        show v11_axel4_earrings
    with long
    pause 1
    l "{i}Ahhhn...{/i}!"
    jk "I feel I could cum if I keep fucking your mouth... But I want to get the full experience..."
    jk "So... Do you think you're ready to take it?"
    "His voice sounded like a raspy, perilous growl of barely-contained craving."
    menu:
        "Yes":
            $ renpy.block_rollback()
            $ lena_jack = 4
            # tease
            scene v11_jack5
            if lena_necklace == "choker2":
                show v11_jack5_earrings
            if v11_lena_dress == 2:
                show v11_jack5_charisma
            if v11_lena_dress == 4:
                show v11_jack5_lust
            if lena_tattoo1:
                show v11_jack5_t1
            if lena_tattoo3:
                show v11_jack5_t3
            if lena_extras == "stockings":
                show v11_jack5_stockings 
            if lena_piercing1:
                show v9_axel4_p1
            elif lena_piercing2:
                show v9_axel4_p2
            with long
            pause 1
            l "Yes, I want it...!"
            "I stood up and helped Jack get his cock free. He immediately took control, holding my leg up and getting behind me."
            "I shivered when he kissed me under my ear and whispered."
            jk "You've made me so fucking hard... It's been a long time since I found a girl as special as you."
            "He repeatedly slapped my slit with the shaft of his iron-hard cock, teasing me."
            if lena_lust > 7:
                l "What are you waiting for? Fuck me... I can't wait to feel that huge cock of yours inside of me."
            else:
                l "Do it... I want it inside me..."
            play sound "sfx/oh4.mp3"
            # insert
            scene v11_jack5b
            if lena_necklace == "choker2":
                show v11_jack5b_earrings
            if v11_lena_dress == 2:
                show v11_jack5_charisma
            if v11_lena_dress == 4:
                show v11_jack5_lust
            if lena_tattoo1:
                show v11_jack5_t1
            if lena_tattoo3:
                show v11_jack5_t3
            if lena_extras == "stockings":
                show v11_jack5_stockings 
            if lena_piercing1:
                show v9_axel4_p1
            elif lena_piercing2:
                show v9_axel4_p2
            with long
            pause 1
            l "Oh, God...!"
            "Jack's spearhead began sliding inside my pussy, spreading it apart like a hot iron rod melting a block of ice."
            "That burning sensation started to spread deeper into me, inch by inch, as Jack slowly penetrated me."
            jk "You're pretty tight... But you can take it."
            "I was. It was pushing me to my limit, drowning me into a boiling mix of pleasure and pain."
            jk "Are you ready? Tonight you're mine, princess."
            # fuck
            scene v11_jack6a
            if lena_necklace == "choker2":
                show v11_jack6_earrings
            if v11_lena_dress == 2:
                show v11_jack6b
            if v11_lena_dress == 3:
                show v11_jack6c
            if v11_lena_dress == 4:
                show v11_jack6d
            if lena_tattoo1:
                show v11_jack6_t1
            if lena_tattoo2:
                show v11_jack6_t2
            if lena_tattoo3:
                show v11_jack6_t3
            if lena_extras == "stockings":
                show v11_jack6_stockings 
            with long
            pause 1
            "Jack lifted me up like I weighed nothing and our genitals aligned much better."
            if lena_athletics < 10:
                call xp_up ('athletics') from _call_xp_up_942
            "His cock kept sliding in, spreading my tight pussy forcefully."
            "And then he slowly pulled back, only to push forward again, each time just a bit deeper."
            "The only reason my pussy was able to take Jacks's cock like that was that I was so insanely wet, but even so, it hurt..."
            "But it was a good kind of hurt."
            "Each thrust eroded whatever thoughts that remained in my mind, evaporating like steam, giving way to raw sexual abandon."
            "This imposing man I barely knew made me feel like I could completely surrender myself to him and the crazy sitution I found myself into."
            "In his strong arms, I even felt liberated from gravity itself."
            "My legs started trembling, possessed by impending ecstasy..."
            jk "What's wrong? Are you going to cum?"
            l "Y--{w=0.5}yes...!"
            jk "I would love to see that. Show me how sexy you are when you cum, princess."
            play sound "sfx/orgasm2.mp3"
            # squirt
            show v11_jack6_cum 
            if lena_necklace == "choker2":
                hide v11_jack6_earrings
                show v11_jack6_earrings
            with fps
            l "Ahhh!! Oooaaahhhh!!!{w=0.3}{nw}" with vpunch
            show v11_jack6_squirt_animation with flash
            pause 2.6
            hide v11_jack6_squirt_animation with short
            "Upon hearing Jacks's command I felt all my strength go, draining out of my body between my legs."
            "I let go of my restraints, shame, resentment and worries."
            "No shame, no blame. Just my mind and my body melting together in the heat of pleasure."
            if lena_lust < 10:
                call xp_up ('lust') from _call_xp_up_943
            jk "Damn! You keep surprising me, princess!"
            if v11_lena_squirt:
                "The world was spinning around me, just like when Ian made me squirt. I was experiencing such strong orgasms lately..."
            elif (v9_seymour_orgasm and seymour_disposition > 1) or seymour_desire:
                "The world was spinning around me, the same way I felt with Seymour. I was experiencing such strong orgasms lately..."
            elif v11_stalkfap > 0:
                "The world was spinning around me, barely aware of where I was. I was experiencing such strong orgasms lately..."
            else:
                "The world was spinning around me, barely aware of where I was. I couldn't remember the last time I came like this..."
            "Slowly, I began getting my senses back as pleasure washed away, but Jack didn't give me time to recover."
            # smear
            scene v11_jack4
            if lena_necklace == "choker2":
                show v11_jack4_earrings
            with long
            pause 1
            "My legs felt weak and I found myself back on my knees, with Jack's cock rubbing on my face, drenched in my juices."
            play sound "sfx/slap3.mp3"
            pause 0.5
            with hpunch
            play sound "sfx/slap3.mp3"
            "He whipped it against my face, tapping it with soft but heavy slaps, making me feel dirty and so damn excited."
            jk "That was so damn hot... You keep blowing away all my expectations!"
            jk "But now's my time to feel good... "
            l "Yes... Use me... Fuck my face like it was a pussy...!"

        "Cum in my mouth":
            $ renpy.block_rollback()
            l "I want you to keep fucking my throat... Cum in my mouth...!"
            jk "Really? Is that what you want?"
            # smear
            scene v11_jack4
            if lena_necklace == "choker2":
                show v11_jack4_earrings
            show v11_jack4_dress
            with long
            play sound "sfx/slap3.mp3"
            pause 0.5
            with hpunch
            play sound "sfx/slap3.mp3"
            "He whipped his cock against my face, tapping it with soft slaps. I felt how hard and heavy it was..."
            "He smeared my face with his cock, wet and sticky with my saliva, making me feel dirty and so damn excited."
            l "Yes... Use me... Fuck my face like it was a pussy...!"
            jk "Well then... If you ask me like that...I can't say no."
            if lena_charisma < 10:
                call xp_up ('charisma') from _call_xp_up_944
                pause 0.5
            
    play sound "sfx/gag1.mp3"
    scene v11_jack3_animation2 with fps
    if lena_jack == 4:
        "My legs were still trembling and my mind foggy while he guided my head with his controlling hand."
        "It wasn't easy taking him in my pussy, and my gullet was in a very similar predicament."
    else:
        "My mind felt foggy while I let him guide my head with his controlling hand."
    "My docile throat offered no resistance to his lustful assault, but Jack's cock squeezed my throat's perimeter, threatening to tear it apart."
    jk "Fuck, that's what I'm talking about, princess!"
    l "{i}Ghhhhk...{/i}!"
    "My throat started to rebel, contracting around Jack's phallus. Unable to breathe, I started to get dizzy, but I held on..."
    jk "Yes, yes... I'm almost there...!"
    "Both Jack's body and voice trembled before me. It was so hot seeing him like that!"
    scene v11_jack3c with flash
    jk "Ugggh, yes!!{w=0.5}{nw}" with vpunch
    with vpunch
    pause 0.6 
    with vpunch
    pause 0.6   
    scene v11_jack3b with fps
    scene v11_jack3a with fps
    if lena_lust < 9 or v10_wc_swallow == False:
        play sound "sfx/ah6.mp3"
    # cum
    scene v11_jack7
    if lena_necklace == "choker2":
        show v11_jack7_earrings
    if lena_tattoo2:
        show v10_wc4_tfull
    with long
    pause 1
    l "Ahhhn...!"
    "I tried to breathe, my mouth was filled to the brim with his thick load, spilling down the corners of my mouth."
    "Jack panted, recovering and looking at me with a satisfied and cocky smile."
    jk "What a beauty... You're on a league of your own, Lena."
    if lena_lust > 8 or v10_wc_swallow:
        "His words of praise only fanned my desire to please, to take this bull of a man deep within me."
        play sound "sfx/gulp2.mp3"
        scene v10_wc4_swallow1 with long
        pause 0.5
        play sound "sfx/ah6.mp3"
        scene v11_jack7
        if lena_necklace == "choker2":
            show v11_jack7_earrings
        show v10_wc4_swallow2
        if lena_tattoo2:
            show v10_wc4_tfull
        with long
        pause 1
        l "{i}Ahhh...{/i}"
        jk "Fuck... You wanna make me hard again?"
        
    $ flena = "crazy"
    $ fjack = "n"
    stop music fadeout 2.0
    scene blazer_wc with long
    play music "music/club_outside.mp3" loop
    "Jack pulled his pants back up and helped me to my feet."
    show lenaunder at rig
    show lena_cum1 at rig
    show jack at lef
    with short
    "The intensity of the experience had left me dizzy and weak, feeling like I was still out of my body..."
    jk "Here's your shoe... It seems it fell off."
    l "Yeah... That was... so intense."
    $ flena = "flirt"
    hide lenaunder
    hide lena_cum1
    show lena2 at rig
    with long
    jk "It' been a long time since I felt this chemistry with a girl... What a pleasant surprise meeting you tonight."
    l "Yes... I can say the same. Unexpected... and so amazing."
    jk "Sadly, it's time for me to get going... But I'll be sure to hit you up if I'm ever in town again."
    menu:
        "{image=icon_love.webp}I need to see you again" if lena_jack == 4:
            $ renpy.block_rollback()
            $ lena_jack_dating = 2
            $ flena = "crazy"
            "His words made my stomach sink. It wasn't what I was expecting to hear."
            l "No..."
            $ fjack = "serious"
            jk "Huh?"
            l "I need to see you again."
            $ fjack = "smile"
            jk "I see... We'll make it happen. I'd love to see you again too."
            l "Soon?"
            jk "Soon."
            $ flena = "flirt"
            l "Here's my number... And you can follow me on Peoplegram too."
            jk "Perfect. Talk to you soon, princess."
            jk "Good night."

        "Sure...":
            $ renpy.block_rollback()
            $ lena_jack_dating = 1
            l "Sure... You can add me on Peoplegram..."
            $ fjack = "smile"
            jk "Got it. Again, this was great. I'll think about this night during the coming weeks, for sure."
            jk "It was a pleasure. Good night, princess."

        "No need":
            $ renpy.block_rollback()
            $ flena = "shy"
            "Now that the heat of the moment had began to dissipate, my mind was coming back to its rational senses."
            l "There's no need for that... I think we can keep this adventure in our hearts and move forward."
            jk "I see. If that's how you want to play it, I'll respect it. I won't ask for your reasons..."
            jk "I'll keep this experience in my memory, just as you said, and be happy I got to share it with you tonight."
            jk "Good night, princess."

    hide jack with short
    l "..."
    show lena2 at truecenter with move
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH11_S27")
    $ flena = "flirt"
    l "Oh, God... I can't believe what just happened."
    l "We barely talked for five minutes, but he managed to turn me on like crazy. I really lost my mind back there..."        
    if lena_jack_dating == 2:
        $ flena = "slut"
        l "And that cock...! It was more than I could handle, or so I thought! I need to have him again!"
    elif lena_jack_dating == 1:
        l "I wouldn't mind seeing him again..."
    else:
        $ flena = "shy"
        l "It was a very fun adventure... A once-in-a-lifetime experience."    
    if ian_lena_couple:
        if lena_cheating:
            $ flena = "slut"
            l "How the hell I'm supposed to stay faithful when there's guys like Jack out there?"
        else:
            $ lena_cheating = True
            $ flena = "blush"
            l "But... I guess I'm a cheater now..."
            l "I don't know why I did it. I mean... I should've said no, but I couldn't."
            l "I didn't want to..."
            "How the hell was I supposed to stay faithful when there were guys like Jack out there?"
            if ian_cuck:
                "I knew I could never tell Ian about this... could I?"
            else:
                "I knew I could never tell Ian about this..."
        if lena_mike_dating:
            l "At least I don't feel as bad about Mike and Ivy anymore."    
    elif lena_mike_dating:
        $ flena = "evil"
        l "Suddenly I don't feel as bad about Mike and Ivy anymore."
    scene blazer_wc with long
    "I recomposed myself before going back to the bar, walking on wobbly legs."
    jump v11clubend

###### END NIGHT ##############################################################################################################################
label v11clubend:
    stop music fadeout 3.0
    scene blazer with long
    "I returned to the bar to finish up my shift."
    "Only the drunkest patrons were still ordering drinks, and it didn't take long before we closed the bar and began to clean up."
    "A few minutes later, the club turned on the lights, sending the last stragglers on their way home."
    # blazer lights?
    play music "music/calm.mp3" loop
    $ fjeremy = "smile"
    $ fivy = "n"
    $ ivy_look = 1
    if lena_axel_desire:
        $ flena = "sad"
    else:
        $ flena = "n"
    show lena at rig
    show jeremy at lef
    with long
    j "Well, that's it for tonight! Good job, Lena."
    l "Thanks... I don't know how you can do this every night..."
    $ fjeremy = "n"
    j "Well, it's not every night... But it sucks I have to work during weekends."
    $ fjeremy = "smile"
    j "But if I wasn't I'd probably be here anyway, so..."
    if v11_bar4 == 0:
        $ flena = "smile"
        l "Yeah, I can imagine... Any luck with the girl with the bangs? I saw you were trying hard tonight!"
        $ fjeremy = "n"
        j "No luck there... Thanks for the assist, though. That was an OG move."
        if lena_jeremy < 12:
            call friend_xp ('jeremy') from _call_friend_xp_1077
        $ fjeremy = "happy"
        j "But! I got two other girls to add me on Peoplegram, now I just have to turn those into dates!"
        $ flena = "n"
        show lena at rig3
        show jeremy at lef3
        with move
        show ivy with short
        v "A date with whom?"
        $ fjeremy = "n"
        j "Just some girls..."
        v "You can't stop flirting with the customers, huh? How many of these bitches have you shagged already?"
        $ fjeremy = "happy"
        j "Why you wanna know? Do I sense a little itty bit of jealousy perhaps?"
        v "That's the dumbest thing I've heard tonight."
        $ fjeremy = "n"
    else:
        show lena at rig3
        show jeremy at lef3
        with move
        show ivy with short
        v "The same's true for me! Night life is not so bad, isn't it?"
    if lena_axel_desire:
        v "So... how did it go?"
        "Ivy gave me a questioning glance and I tried to put on a poker face."
        $ flena = "n"
        l "It was okay..."
        v "Oh, really...?"
    else:
        v "So, how did it go?"
    # POINT CALCULATIONS
    # john
    if v11_bar1 == 0:
        $ v11barpoints -= 1
    elif v11_bar1 == 2:
        $ v11barpoints += 1                  # 3-pour generously/2-pour normal/1-pour scarceley/0-call marcel
    if v11_bar1b == 0:
        $ v11barpoints -= 1  
    elif v11_bar1b > 2:
        $ v11barpoints += 1                  # 4-john flirts/3-pour good/2-pour bad/1-call Marcel
    # rosa
    if v11_bar2 == 3:   
        $ v11barpoints += 1                  # 3-pour correctly/2-ask Jeremy/1-pour incorrectly/0-ignore
    elif v11_bar2 < 2:   
        $ v11barpoints -= 1   
    if v11_bar2b == 1:
        $ v11barpoints += 1
    elif v11_bar2b > 1:
        $ v11barpoints -= 1
    if v11_rosa_fight == 1:
        $ v11barpoints += 1                 # 4-punch her/3-slap her/2-call marcel/1-ignore her
    # finley
    if v11_bar3 == 3:
        $ v11barpoints += 1                 # 3-free shots/2-no free shots/1-call marcel
    if v11_bar3 == 0:
        $ v11barpoints -= 1  
    # eli
    if v11_bar4 == 0 or v11_bar4 == 2:
        $ v11barpoints += 1                 # 3-Flirt with Eli/2-talk to Eli/1-serve Eli/0-Let Jeremy handle her
    if v11barpoints > 2:
        $ fjeremy = "happy"
        j "It was great! Having Lena tonight really made a difference. And we got really good tips!"
        $ flena = "smile"
        call money (2) from _call_money_7
        l "Great! Seems I did a good job after all..."
        if lena_will < 3:
            call will_up () from _call_will_up_61
        if lena_jeremy < 12:
            call friend_xp ('jeremy') from _call_friend_xp_1078
    elif v11barpoints > 1:
        $ fjeremy = "smile"
        j "It was a good night! Busy, but we got some good tips!"
        if lena_money < 5:
            call money (2) from _call_money_139
        else:
            call money (1) from _call_money_140
        l "That's nice..."
    elif v11barpoints > 0:
        $ fjeremy = "smile"
        j "It was alright! Busy night as usual. Let's share the tips..."
        call money (1) from _call_money_141
    else:
        $ fjeremy = "n"
        j "People were troublesome tonight... And they hardly left any tips."
        $ flena = "sad"
        if lena_money < 5:
            call money (1) from _call_money_142
    if lena_axel_desire:
        $ flena = "sad"
        v "It sounds like it was an interesting night..."
        "Ivy continued to look at me funny. Did she already learn about what went down in the restroom or was she silently questioning me...?"
        "I didn't want to talk about it in front of Jeremy, so I diverted the topic of conversation."
        $ flena = "n"
        l "What about you, Ivy? Weren't you going to end the night with that VIP you told me about?"
        $ fivy = "serious"
        v "Turns out he already has plans for tonight. He was a cocky asshole anyway."
        $ fjeremy = "flirt"
        j "Well, I don't have plans for tonight..."
        $ fjeremy = "happy"
        j "Maybe I'm a bit cocky, but I'm not an asshole!"
        $ fivy = "n"
        if ivy_jeremy == 2:
            v "..."
            v "You're both, but alright... I'll drive you home. Let's get going."
            j "Awesome!"
        elif ivy_jeremy == 1:
            v "..."
            v "Some other night, maybe."
            $ fjeremy = "n"
        else:
            v "You're delusional, that's what you are."
            $ fjeremy = "sad"
    if lena_mike_dating:
        $ flena = "sad"
        if lena_jack > 2:
            "I looked at Ivy, now knowing for sure she had fucked Mike..."
        else:   
            "I looked at Ivy, lamenting my fight with Mike. Now I knew for sure they had slept together..."
        "I still didn't know how to feel about the whole thing. All I knew was I didn't feel comfortable around Ivy at that moment."
        $ flena = "n"
        l "Well, guys, I'll be heading home..."
        if lena_axel_desire:
            v "Let's talk soon, alright...? You have stuff to tell me."
            $ flena = "worried"
            l "Yeah, well... I'll do it in private, alright?"
            v "In private, of course. Good night, Lena!"
        else:
            v "I'll drive you home..."
            l "No need. I'll take the bus."
            $ fivy = "sad"
            $ fjeremy = "happy"
            j "If you have an open seat, I'm willing to be the passenger!"
            if ivy_jeremy == 2:
                v "..."
                v "Alright, you can hop in. Let's get going."
                j "Awesome!"
            elif ivy_jeremy == 1:
                v "..."
                v "Some other night, maybe."
                $ fjeremy = "n"
            else:
                v "Not a chance."
                $ fjeremy = "sad"
            l "Good night, guys..."
    else:
        $ flena = "n"
        l "Well, guys, I'll be heading home..."
        if lena_axel_desire:
            v "Let's talk soon, alright...? You have stuff to tell me."
            $ flena = "worried"
            l "Yeah, well... I'll do it in private, alright?"
            v "In private, of course. Good night, Lena!"
        else:
            v "Do you want me to drive you home?"
            if lena_jack > 2:
                $ flena = "shy"
                l "Oh, I thought you had plans for tonight..."
                $ fivy = "serious"
                v "You mean the guy I told you about? He said he'd wait for me, but turns out he bailed without saying anything..."
                v "Fuck him. He was a cocky asshole anyway."
            else:
                l "Weren't you going to end the night with that VIP you told me about?"
                $ fivy = "serious"
                v "Turns out he already has plans for tonight. He was a cocky asshole anyway."
            $ fjeremy = "flirt"
            j "Well, I don't have plans for tonight..."
            $ flena = "n"
            $ fjeremy = "happy"
            j "Maybe I'm a bit cocky, but I'm not an asshole!"
            $ fivy = "n"
            if ivy_jeremy == 2:
                v "..."
                v "You're both, but alright... You can hop in. Let's get going."
                j "Awesome!"
            elif ivy_jeremy == 1:
                v "..."
                v "Some other night, maybe."
                $ fjeremy = "n"
            else:
                v "You're delusional, that's what you are."
                $ fjeremy = "sad"
            l "Good night, guys."

    if v11_mark_sex:
        jump v11marksexscene
    elif v11_ian_sex:
        jump v11iansexscene
    else:
        scene street2night with long
        if lena_axel_desire:
            "I made my way back home, hardly aware of the path I was taking."
            "My mind was still caught up in that restroom, stuck in a spiral I never truly escaped."
            "Stuck between that wall and Axel's body, with his cock deep inside me."
        else:
            if lena_mike_dating:
                "I made my way back home, still unable to shake off the aching sting Ivy's affair with Mike made me feel."
                if lena_jack > 2:
                    "Fucking Jack had made me feel better, but it wasn't enough to stop from thinking back on it."
                if ian_lena_couple:
                    "Mike was just a fling, or so I told myself. And I already had Ian, the person I really wanted to be with. But..."
                    "I couldn't picture myself letting Mike go..."
                elif lena_mike_love:
                    "Mike had been just a fling at first, but that changed somewhere along the line."
                    "Now... I couldn't seem to stop thinking about him..."
                else:
                    "I wanted to think about Mike as a simple fling, but that seemed to have changed somewhere along the line."
                    "At that moment, I couldn't stop thinking about him..."
            else:
                "I made my way home, tired and eager to get into bed."
                if lena_jack > 2:
                    if lena_jack > 3:
                        "My pussy and throat were sore after taking Jack's massive cock, but it had been totally worth it... A memory to treasure."
                    else:
                        "My throat was sore after taking Jack's massive cock, but it had been totally worth it... A memory to treasure."
                if v11_bbc == "marcel":
                    "Before leaving, I made sure to say goodbye to Marcel. Both of us knew we would have to find the opportunity to finish what we'd started today..."
                if v11_bar4 == 3 or v11_john_flirt == 3:
                    "It had been an interesting night. I met someone interesting... We only exchanged Peoplegrams, but I wondered if that could lead somewhere."
                if ian_cindy_sex:
                    "My mind kept going back to what Ivy told me. Did Cindy really cheat on Wade... with Ian?"
            "At least Axel had been true to his word and didn't cause any trouble tonight. Still..."
            "I couldn't help wondering about what was really going on between him and Cindy."
            "Did she know what she was getting into...?"
        stop music fadeout 3.0
        scene black with long
        pause 1
        jump v11epilogueend

###### MARK SEX SCENE ##############################################################################################################################
label gallery_CH11_S25:
    if _in_replay:
        call setup_CH11_S25 from _call_setup_CH11_S25

label v11marksexscene:
    scene blazer_outside with long
    "Mark was waiting for me outside the club."
    $ fmark = "n"
    $ flena = "flirt"
    show lena2 at rig
    show mark at lef
    with short
    l "Hey... I'm finally off."
    ma "Do you still have some energy left?"
    if lena_jack > 2:
        $ flena = "flirtshy"
        "My body was still shaking after being pounded by Jack, and my pussy felt a little sore..."
        "However, I still had some untapped lust that could be satisfied."
    elif v9_axel_sex and lena_axel_desire:
        $ flena = "flirtshy"
        "My body was still shaking after being pounded by Axel, and my pussy felt a little sore..."
        "However, it ended before I could get any satisfaction... Which I intended to remedy."
    l "For you... Yes."
    $ fmark = "flirt"
    ma "Then I'm in luck."
    l "I hope I am, too..."
    ma "Let's find out."
    stop music fadeout 3.0
    play sound "sfx/car.mp3"
    hide mark
    hide lena2 
    with short
    pause 1
    stop sound fadeout 2.0
    "We climbed into Mark's car and he drove us to my place."
    scene streetnight with long
    pause 0.5
    play sound "sfx/door_home.mp3"
    scene lenahomenight_dark with long
    if lena_cheating:
        if lena_jack > 2:
            "Despite having already cheated on Ian tonight, I felt a sting of guilt for bringing Mark home instead of him..."
        else:
            "I felt a sting of guilt for bringing Mark home instead of Ian tonight..."
        "But I couldn't pass up this chance. I had been wanting to give Mark a try since I first saw him."
    else:
        "I brought him to my room right away. I had been wanting to give Mark a try since I first saw him..."
    play sound "sfx/door.mp3"
    scene lenaroomnight with long
    $ fmark = "n"
    $ flena = "smile"
    show lena2 at rig
    show mark at lef
    with short
    ma "So this is your room... It's tiny, but it's nice."
    show mark at lef3
    show lena2 at rig3
    with move
    play sound "sfx/cat_angry.mp3"
    $ fmark = "sad"
    show lola with short
    $ flena = "worried"
    ma "Ah!" with vpunch
    ma "What is that?"
    $ flena = "smile"
    hide lena2
    show lena at rig3
    with short
    l "Oh, sorry! That's Lola!"
    l "She's all dark and blends in the shadows... I should've told you I have a cat."
    $ fmark = "serious"
    $ flena = "sad"
    "Lola didn't seem happy that we had interrupted her rest. She was perched on the bed, glaring at Mark after having hissed at him."
    ma "She doesn't look too friendly."
    l "Wait, I'll kick her out..."
    $ fmark = "n"
    ma "Actually, I'm allergic to cats. Maybe we should move it to the living room...?"
    $ flena = "n"
    l "Alright, wait for me there. I want to get comfortable..."
    l "Oh, and try not to wake up my roommates."
    ma "Sure."
    play sound "sfx/door.mp3"
    hide mark with short
    show lena at rig
    show lola at lef
    with move
    $ flena = "serious"
    l "Why do you always make things this difficult?"
    "Lola glanced at me before flipping around and settling down on the bed, her back to me."
    hide lola with short
    l "What a roommate you are..."
    $ flena = "n"
    show lena at truecenter with move
    l "Anyway... Time to end the night."
    hide lena with short
    scene lenahomenight_dark
    show mark at rig
    with long
    $ lena_necklace = 0
    $ lena_extras = 0
    $ lena_makeup = 0
    $ flena = "flirt"
    pause 1
    l "Alright, I'm done..."
    play music "music/sex_deep.mp3" loop
    show lenanude2 at lef
    with long
    $ fmark = "flirt"
    ma "You've really made yourself comfortable, haven't you..."
    l "How about you? I think you should get comfortable too..."
    # handjob
    scene v11_mark1
    if lena_tattoo1:
        show v11_mark1_t1
    if lena_piercing1:
        show v11_mark1_p1
    elif lena_piercing2:
        show v11_mark1_p2
    with long
    pause 1
    "I laid my hands on Mark's chest, unbuttoning his shirt. He responded by holding my hips and pulling me closer, kissing me."
    "Soon, we were both naked, making out on the couch. I knew what I wanted, and I saw no reason to beat around the bush."
    "My fingers ran over Mark's chiseled abs until they reached his cock, grasping the hardened shaft and squeezing it tightly."
    l "You have such a nice cock..."
    if lena_jack > 3:
        "It would be more manageable than Jack's monster... I needed that, after the pounding he gave me."
    ma "I'm glad you like it... Your body is incredible too. I'm a lucky guy indeed."
    "His breathing quickened with each stroke. I felt him growing harder and thicker, and the heat inside me intensified."
    l "That's true... I'm going to make you feel really lucky tonight."
    # blowjob
    scene v11_mark2
    if lena_tattoo2:
        show v11_mark2_t2
    if lena_tattoo3:
        show v11_mark2_t3
    with long
    pause 1
    if lena_lust < 10:
        call xp_up ('lust') from _call_xp_up_929
    if v10_wc_bj == "mark":
        ma "Mhh, yes... I've been dying to feel your mouth again... You gave me the best blowjob ever."
    else:
        ma "Mhh, yes... I've been dying to feel your sexy lips on my cock." 
    "I felt the heat radiating from Mark's cock on my lips, and I pushed it deeper into my mouth, savoring the unique aroma of his manhood."
    if v11_bbc or lena_axel_desire or lena_jack > 2:
        if (v11_bbc and lena_axel_desire) or (v11_bbc and lena_jack > 2):
            "It was crazy to think this was the third cock I sucked tonight... I felt so slutty."
        else:
            "This was the second cock I sucked tonight... I felt so naughty."
        if v11_bbc == "marcel":
            if v11_bbc_pic:
                "I had a bit of fun with Marcel, and I got some really nice pictures to show for it... It would be so nice to watch them and masturbate to them."
            else:
                "I was happy to end it with Mark, but both Marcel and I knew we would have to find the opportunity to finish what we had started today..."
        elif v11_bbc == "jeremy":
            if v11_bbc_pic:
                "I had a bit of fun with Jeremy, and I got some really nice pictures to show for it... It would be so nice to watch them and masturbate to them."
            else:
                "I had a bit of fun with Jeremy to start the night, and I was happy to end it with Mark. His cock wasn't nearly as big, but I was determined to get a ton of enjoyment out of it." 
        if lena_axel_desire:
            "The memory of Axel's tool ravaging my mouth was still fresh on my mind, and this only intensified it. A part of me wished I still was in that restroom, being claimed by him..."
        if lena_jack > 2:
            "I still couldn't believe I managed to deepthroat Jack like that. Handling Mark's cock was child's play in comparison... but equaly fun."
    else:
        "It felt so good to be able to suck it..."
    if lena_mike_dating:
        "This could've been Mike tonight, but he decided to go home to his girlfriend."
        "It maddened me knowing he had fucked Ivy, especially the fact that they did it at his place, where I hadn't been invited yet..."
        "I stuffed the dick I had in my mouth all the way to my throat, furious. It was his loss!"
    else:
        "I employed my best techniques on Mark's cock, making sure he'd remember me. I stuffed it all the way to my throat, mixing it up with licks and kisses."
    ma "Oh, fuck, so good...!"
    play sound "sfx/bj5.mp3"
    "The living room was filled with the wet sounds of my blowjob and our excited panting. Hopefully, Stan and Louise wouldn't wake up..."
    # film
    if v10_wc_bj == "mark":
        ma "You're so fucking sexy... Do you know what I wanted to do last time?"
        l "Not really. What is it?"
        ma "I wished I took a picture to remember how awesome that moment was... Can I do that now?"
    else:
        ma "You're so fucking sexy... Hey... Would it be okay if I take a picture to remember how great this is?"
    menu:
        "{image=icon_lust.webp}Yes" if stalkfap_pro or lena_lust > 7:
            $ renpy.block_rollback()
            $ v11_mark_sex = 2
            if stalkfap_pro:
                l "You wanna record me like your little Stalkfap model?"
                ma "Hell yeah..."
                l "Film me as much as you want... As long as you send it to me later."
            else:
                l "You wanna take pictures of me sucking your cock...?"
                ma "Or a short video... That would be nice too."
                l "You're so naughty... But I'm feeling really naughty too, so..."
            scene v11_mark2b
            if lena_tattoo2:
                show v11_mark2_t2
            if lena_tattoo3:
                show v11_mark2_t3
            with long
            pause 1
            ma "Awesome..."
            "Mark pulled out his phone and pointed the camera at me. I made sure to give him a good show."
            "My tongue traced wet paths across the shaft, my lips wrapped around it and made it disappear in my throat as I made eye contact with the camera."
            if stalkfap_pro or lena_fty_show:
                "It turned me on so much performing for the camera, acting like a naughty pornstar..."
                "Letting Mark capture such a dirty part of me, surrendering my most naughty and intimate self to the camera... It was such a high."
            else:
                "I knew I was being reckless, but I felt so aroused..."
                "The truth was I loved performing for the camera, acting like a naughty pornstar, letting Mark capture such a dirty and intimate part of me."
            "And the fact that he was a guy I barely knew only made it more thrilling and risky..."
            if lena_charisma < 8:
                call xp_up ('charisma') from _call_xp_up_930
            $ lena_fty_show = True
            if stalkfap_pro > 1 and seymour_desire == False:
                "Maybe I could share the video with some of my most loyal subscribers... They would surely pay good money for the privilege of seeing it."
            if ian_lena_couple:
                "Hopefully, the video would never find its way to Ian. I knew it would be disastrous, but imagining it was kind of turning me on too..."
            
        "No":
            $ renpy.block_rollback()
            l "What? No, sorry... I'm not comfortable with that."
            ma "Of course... I had to ask, though."
            l "Don't worry. You won't need that to remember me..."

    "I took my time savoring Mark's dick. He endured my endless teasing, groaning, and watching me with a lustful smile."
    "He was so sexy..."  
    # POV cowgirl
    scene v11_mark4
    if lena_tattoo2:
        show v11_mark4_t2
    with long
    pause 1
    l "I can't wait anymore. I want you inside of me..."
    ma "I'm all yours, baby."
    "I sat on Mark's lap, sliding his cock into my melting pussy. The connection shocked my entire body with a shiver of pleasure."
    l "Oh, yes...! It's so hard!"
    ma "Of course. I have the sexiest view right now..."
    if v11_mark_sex == 2:
        l "Be sure to get a good angle on it... I wanna see it too... Mhhh!"
        ma "Oh, yeah? You wanna watch your perfect sexy ass bouncing on my hard cock?"
        l "Yes... I wanna remember how good it felt to fuck you...!"
    else:
        ma "Your perfect sexy ass bouncing on my hard cock... You have no idea how much it turns me on."
        l "I think I get a clue..."
    "I began swaying my hips, fucking Mark slow and deep."
    "Every time I impaled myself on Mark's cock, pleasure radiated from my core and my whole body quaked with delight."
    # cowgirl
    scene v11_mark3
    if lena_tattoo1:
        show v11_mark3_t1
    if lena_tattoo2:
        show v11_mark3_t2
    if lena_tattoo3:
        show v11_mark3_t3
    if lena_piercing1:
        show v11_mark3_p1
    elif lena_piercing2:
        show v11_mark3_p2
    with long
    pause 1
    "My hips moved instinctively, raising up and falling down with increasing momentum."
    "I was barely managing to hold in my moans, but the sound of flesh slapping against flesh echoed loudly in the living room."
    "I wanted more. I wanted him deeper."
    play sound "sfx/oh4.mp3"
    l "Fuck... Yes...!" with vpunch
    if lena_athletics < 10:
        call xp_up ('athletics') from _call_xp_up_931
    ma "What happened to being quiet? You're gonna wake up your roommates..."
    l "I don't give a fuck... I can't stop, your cock feels too good!"
    "I felt Mark swell inside me, pushing me to the edge of my arousal."
    menu:
        "{image=icon_lust.webp}Make me your slut!" if lena_lust > 6 and lena_anal:
            $ renpy.block_rollback()
            $ v11_lena_anal = True
            l "I want you to make me your slut...! Fuck me, fuck me however you want!"
            ma "In that case... I know just the way sluts need to be fucked."
            # anal
            scene v11_mark5
            if lena_tattoo1:
                show v11_mark5_t1
            if lena_tattoo3:
                show v11_mark5_t3
            if lena_piercing1:
                show v11_mark5_p1
            elif lena_piercing2:
                show v11_mark5_p2
            with long
            pause 1
            "Mark took control, getting me on my fours and standing up behind me."
            "I felt his erect rod pressing and sliding across my pussy, wet and slippery, teasing me."
            "He wasn't aiming for my slit, though, and soon I felt the tip of his cock pushing into my asshole."
            if lena_anal > 1:
                l "Oh, fuck yes... Fuck me in the ass... I love it!"
                ma "I knew you'd be this kind of girl..."
                l "Yes... I'm a dirty girl who wants you to destroy her ass..."                
            else:
                l "Oh, fuck... You're so bad... Are you gonna fuck my ass?"
                ma "Do you like it?"
                l "This will be my first time..."
                ma "Damn, I'm a lucky guy indeed..."
            play sound "sfx/ah9.mp3"
            "I moaned uncontrollably as Mark made his way inside my rectum."
            "The sensation of my anus being stretched by his hard cock sent shivers all across my body, my mind melting with excitement."
            if lena_anal > 1:
                ma "It went in so easily... I see you're quite the anal slut, huh?"
                l "Yes... I fucking love it...!"
            else:
                ma "Is this really your first time? It seems like you're enjoying it quite a lot..."
                l "Mhhh, yes... I've been wanting to try this for so long..."
            ma "Glad to be of service."

        "Fuck me like you mean it!":
            $ renpy.block_rollback()
            l "Fuck me... Fuck me like you mean it!"
            ma "If that's what you want, I'll give it to you."

        "I'm cumming!":
            $ renpy.block_rollback()
            l "God, yes! I'm cumming!"
            play sound "sfx/orgasm1.mp3"
            with flash
            with vpunch
            pause 0.5
            with vpunch
            pause 0.5
            with vpunch
            if lena_axel_desire:
                "It made me forget about Axel. It stripped away my anguish and confusion, leaving only pleasure."
            if lena_mike_dating:
                "Who needed Mike when I could get any hot guy I wanted? I wish he could see me now and realize his mistake!"
            if lena_cheating:
                "It didn't matter that I was cheating on Ian. This bliss was well worth it."
            "With one final thrust, I shattered into a thousand pieces as I felt myself coming undone."
            jump v11marksexend

    # animation
    scene v11_mark6a with long
    pause 1
    if v11_lena_anal:
        ma "Are you ready? I'm gonna fuck all your holes and make you mine..."
        l "Yes...! Do it! Make me yours!"
    else:
        "Mark took control, getting me on my fours and standing up behind me."
        "I felt his erect rod pressing and sliding across my pussy, wet and slippery, teasing me..."
    play sound "sfx/oh1.mp3"
    scene v11_lenaian6b with fps
    scene v11_mark6c with vpunch
    l "Oh, fuck...!"
    "My whole body tensed up around Mark's cock, so deep inside me. It was such a pleasant and exciting sensation."
    scene v11_mark6_animation with fps
    pause 4
    "The sofa creaked under Mark's thrusts as he fucked me with passionate intensity. He was so good at it..."
    "It was obvious he had a lot of experience, and a guy like him was exactly what I had been looking for."
    "I was glad I took him home tonight, indulging myself in carnal desire without hesitation, and with such a hot guy."
    if lena_axel_desire:
        "I wanted him to fuck me until I came undone, like I was about to do with Axel a few moments ago."
    elif lena_jack > 3:
        "I wanted him to fuck me until I came undone, like Jack had fucked me a few moments ago."
    else:
        "I wanted him to fuck me until I came undone, releasing all the tension that I had been bearing."
    if v11_lena_anal:
        l "Yes...! Yes! You're tearing my ass apart, and I love it!"
    else:
        l "Yes...! Yes! You're so deep inside me! I love it!"
    if lena_axel_desire:
        "It made me forget about Axel. It stripped away my anguish and confusion, leaving only pleasure."
    if lena_mike_dating:
        "Who needed Mike when I could get any hot guy I wanted? I wish he could see me now and realize his mistake!"
    if lena_cheating:
        "It didn't matter that I was cheating on Ian. This bliss was well worth it."
    play sound "sfx/orgasm2.mp3"
    scene v11_mark6c with flash
    with vpunch
    pause 0.5
    with vpunch
    pause 0.5
    with vpunch
    pause 0.5
    "With one final thrust, I shattered into a thousand pieces as I felt myself coming undone."
    "My screams of ecstasy were loud and animalistic. There was no way my roommates didn't hear what was going on in the living room now..."
    
label v11marksexend:
    "Mark barely gave me time to recover. He pulled out and stood next to me, jerking himself off."
    # cum
    scene v11_mark7 with long
    "I got on my knees, obedient and eager, with my mind still shaken by the intense orgasm."
    "Tonight I belonged to Mark, and I would please him however he wanted."
    ma "Oh, yes...! Open wide... I'm gonna cum!"
    show v11_mark7_animation with flash
    pause 0.3
    show v11_mark7_animation
    show v11_mark7e
    with flash
    pause 0.5
    show v11_mark7_animation with fps
    pause 2
    "I felt several jets of hot sperm splashing my lips as Mark painted me with his scent."
    "Ropes of cum dripped down my lips into my mouth, leaving shiny streaks on my cheeks and chin..."
    "Covered with Mark's lust, I took in his scent and his taste..."
    "I had taken a part of him into me, and I surrendered a piece of me to him."
    "It was a pure sexual connection, easy, intense, and delightful. Just what I had been looking for..."
    "It felt amazing to get it."
    stop music fadeout 3.0
    $ fmark = "flirt"
    $ flena = "slut"
    scene lenahomenight_dark 
    show marknude at lef
    show lenanude2 at rig
    show lena_cum1 at rig
    with long
    "Mark lounged on the sofa, breathing heavily and wearing a smile."
    ma "Wow, that was so intense... I knew we would enjoy each other, but you blew my expectations out of the water."
    l "Yeah... I was feeling especially horny tonight... And seems like we have great chemistry."
    ma "I'm glad to know you enjoyed it too."
    if v11_lena_anal:
        if lena_anal < 2:
            $ lena_anal = 2
            l "I did... My ass hurts a bit, though! It was really intense."
            ma "You were asking for it, so..."
            l "I still need to get used to it. Maybe we should try again some other time..."
        else:
            l "Hell yeah... I hope it's not the last time you give my ass some loving."    
    else:
        l "Absolutely... I wouldn't mind repeating this one of these days."
    ma "Whenever you want, baby..."
    call friend_xp ('mark') from _call_friend_xp_1079
    $ lena_mark = 10
    pause 0.5
    scene lenahomenight_dark with long
    if lena_axel_desire:
        "Mark's scent had dulled the memory of Axel, but now that the lust was slowly fading away, what had transpired in the restrooms was coming back with intensity."
        "As much as I tried to push those emotions out of my mind, I couldn't rid myself of them."
        "Perhaps, in reality, I didn't want to..."
    elif lena_mike_dating:
        "The heat of the moment had made me forget about the aching sting the knowledge of Ivy's affair with Mike made me feel, at least momentarily."
        if ian_lena_couple:
            "Mike was just a fling, or so I told myself. And I already had Ian, the person I really wanted to be with. But..."
            "I just couldn't picture myself letting Mike go..."
        elif lena_mike_love:
            "Mike had been just a fling at first, but that changed somewhere along the line."
            "Now... I wanted to stop thinking about him, but it wasn't that easy..."
        else:
            "I wanted to think about Mike as a simple fling, but that seemed to have changed somewhere along the line."
            "I wanted to stop thinking about him, but it wasn't that easy..."
    else:
        "The night had been much more interesting than I anticipated, and the end was just what I had been needing."
        if ian_lena_couple:
            "I loved being with Ian, but I couldn't say no to enjoying some variety... And Mark had met my expectations."
            "I knew I could have him again whenever I wanted. He would answer my call."
        elif v11_ian_sex:
            "I felt a bit bad for having canceled on Ian last minute, but I couldn't say no to enjoying some variety... And Mark had met my expectations."
            "I knew I could have him again whenever I wanted. He would answer my call."
        else:
            "Mark had met my expectations so far... And I knew I could have him again whenever I wanted. He would answer my call."
    scene black with long
    pause 1
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH11_S25")
    jump v11epilogueend

###### IAN SEX SCENE ##############################################################################################################################
label v11iansexscene:
    scene street2night with long
    if lena_axel_desire:
        "I made my way to Ian's place, hardly aware of the path I was taking. I wondered if I should go at all..."
        $ flena = "worried"
        show lena with short
        "My mind was still caught up in that restroom, unable to stop reminiscing what had just happened between me and Axel."
        if ian_lena_couple:
            "How could I even face Ian after that? What the hell was I doing?"
        else:
            "And now I was heading to meet Ian, right after it... What the hell was I doing?"
        "I stopped and took a breather. I needed to get it together..."
        menu:
            "{image=icon_lust.webp}Go to Ian's place" if lena_lust > 6:
                $ renpy.block_rollback()
                $ flena = "serious"
                l "I'm thinking too much about it! What happened happened, and that's it."
                l "It was a mistake, and I have to live with it. All I can do now is move on..."
                if ian_lena_couple:
                    $ flena = "sad"
                    l "If I cancel on Ian last minute he might suspect something's wrong... I don't want that."
                    l "Ending the night with him will help me stop thinking about Axel... And that's exactly what I need right now."
                else:
                    $ flena = "n"
                    l "My plan was to end the night with Ian, and that's what I'm going to do."
                    $ flena = "sad"
                    l "It will help me stop thinking about Axel, and that's exactly what I need right now."
                hide lena with short
                pause 0.5

            "Go home":
                $ renpy.block_rollback()
                $ v11_ian_sex = False
                stop music fadeout 3.0
                l "I can't face him... Not right now, not like this."
                "I pulled up my phone and texted Ian."
                $ flena = "sad"
                nvl clear
                l_p "{i}Hey... I just finished work, but I'm dead tired. I don't think I'll be good company tonight {image=emoji_disgust.webp}{/i}"
                l_p "{i}I'm sorry, I know I told you to wait for me tonight... {image=emoji_sad.webp}{/i}"
                play sound "sfx/sms.mp3"
                i_p "{i}It's alright, I understand. I'm sure it's been an intense night.{/i}"
                l_p "{i}Again, I'm sorry...{/i}"
                i_p "{i}Don't worry, I'm pretty tired too. We'll see each other soon anyway {image=emoji_smile.webp}{/i}"
                i_p "{i}Good night, and rest well {image=emoji_moon.webp}{/i}"
                if ian_lena_couple:
                    l "..."
                    l "I'm really sorry..."
                else:
                    l "That's a relief... But I still feel guilty about it."
                if ian_cindy_sex:
                    "My mind kept going back to what Ivy told me. Did Cindy really cheat on Wade... with Ian?"
                if lena_mike_dating:
                    "I began making my way home, still unable to shake off the aching sting Ivy's affair with Mike made me feel."
                    if ian_lena_couple:
                        "Mike was just a fling, or so I told myself. We both had our own relationships going, but..."
                        "I couldn't picture myself letting Mike go..."
                    elif lena_mike_love:
                        "Mike had been just a fling at first, but that changed somewhere along the line."
                        "Now... I couldn't seem to stop thinking about him..."
                    else:
                        "I wanted to think about Mike as a simple fling, but that seemed to have changed somewhere along the line."
                        "At that moment, I couldn't stop thinking about him..."
                hide lena with short
                pause 1
                scene black with long
                pause 1
                jump v11epilogueend

    else:
        if lena_jack > 2:
            "My pussy and throat were feeling a bit sore after taking Jack's massive cock. And now I was supposed to meet Ian at his place..."
            "I wondered if I should go at all..."
            if lena_cheating:
                "Ian would probably suspect something if I cancelled on him last minute... Besides, it felt bad to do so."
                "And, also... I still had some untapped lust that could be satisfied."
            else:
                "It felt bad to cancel on Ian last minute... Besides, I still wanted to see him."
                "I still had some untapped lust that could be satisfied."
        if lena_mike_dating:
            "I made my way to Ian's place, still unable to shake off the aching sting Ivy's affair with Mike made me feel."
            $ flena = "sad"
            show lena with short
            if lena_jack > 2:
                "Fucking Jack had made me feel better, but it wasn't enough to stop from thinking back on it."
            if ian_lena_couple:
                "Mike was just a fling, or so I told myself. We both had our own relationships going, but..."
                "I couldn't picture myself letting Mike go..."
                "I shook my head. I couldn't be thinking like that."
                "I already had Ian, the person I really wanted to be with..."
            else:
                if lena_mike_love:
                    "Mike had been just a fling at first, but that changed somewhere along the line."
                    "Now... I couldn't seem to stop thinking about him..."
                else:
                    "I wanted to think about Mike as a simple fling, but that seemed to have changed somewhere along the line."
                    "At that moment, I couldn't stop thinking about him..."
                "I shook my head. It was stupid to think like that. It wasn't like I had no one myself..."
                if lena_ian_love:
                    "Ian had become special to me too... And now I was heading to his place to end the night together."
                else:
                    "I was heading to Ian's home right now to end the night together. I should focus on that."
        else:
            if v11_bbc == "marcel":
                "Before leaving, I made sure to say goodbye to Marcel. Both of us knew we would have to find the opportunity to finish what we'd started today..."
                if ian_lena_couple:
                    "I tried to think of it as just simple fun, even though I knew I was doing something I shouldn't. As long as Ian didn't learn about it..."
            else:
                "As I made my way to Ian's place, I went over what had happened during the night."
                if v11_bbc == "jeremy":
                    "I only got to play with Jeremy a bit, but Ian would give me what I was lacking tonight."
            if v11_bar4 == 3 or v11_john_flirt == 3:
                "It had been an interesting night. I met someone interesting... We only exchanged Peoplegrams, but I wondered if that could lead somewhere."
        "At least Axel had been true to his word and didn't cause any trouble tonight. Still..."
        "I couldn't help wondering about what was really going on between him and Cindy."
        "Did she know what she was getting into...?"
        if lena_mike_dating:
            hide lena with short
## arrive home
    label gallery_CH11_S26:
        if _in_replay:
            call setup_CH11_S26 from _call_setup_CH11_S26

    play sound "sfx/doorbell.mp3"
    scene ianhomenight with long
    pause 0.5
    play sound "sfx/door_home.mp3"
    $ fian = "smile"
    $ ian_look = 2
    $ flena = "smile"
    show ian at lef
    show lena at rig3
    with short
    if lena_axel_desire:
        l "Hey..."
    else:
        l "Knock, knock."
    if ian_charisma > 6 and ian_cuck < 2:
        i "Well, well, if it isn't the late-night intruder. What's the secret password?"
        if lena_axel_desire:
            $ flena = "n"
            l "I didn't know I needed a password..."
            i "I was just kidding. Come on in..."
            show lena at rig with move
        else:
            l "Is it... \"Kiss me\"?"
            i "That's the one."
            scene v11_kiss_bg2
            show v11_kiss2
            if lena_necklace == "choker2":
                show v11_kiss1_earrings
            if v11_lena_dress == 2:
                show v11_kiss_c
            elif v11_lena_dress == 4:
                show v11_kiss_d
            else:
                show v11_kiss_b
            if lena_tattoo2:
                show v9_lena1_t2
            with long
            pause 1.5
            $ flena = "n"
            scene ianhomenight
            show lena at rig
            show ian at lef
            with short
        l "Ahh, my feet are killing me. It was one crazy night at the club."
        i "Working hard or hardly working, huh?"
        $ flena = "happy"
        l "You know me, always giving those cocktail shakers a good workout."
        $ fian = "confident"
        i "Well, I hope you saved some of that energy for me."
    else:
        i "There you are... Come in. How did the night go?"
        show lena at rig with move
        l "Ahh, my feet are killing me. It was one crazy night at the club."
        if ian_cuck < 2:
            i "I can imagine... I hope you still have some energy left, though."
        else:
            i "I can imagine... Did you have to deal with a lot of drunk people?"
    if lena_axel_desire:
        $ flena = "n"
        l "Um, yeah..."
    elif ian_cuck > 1:
        l "Nothing I couldn't handle..."
        $ fian = "n"
        i "I bet they were hitting on you all night. That dress is so sexy..."
        $ flena = "shy"
        l "I got hit on quite a bit, I'm not gonna lie... Why, does it bother you?"
        i "I mean... I guess I can't blame them for trying..."
    else:
        $ flena = "flirtshy"
        if ian_lena_couple:
            l "I promised you, didn't I? I know it's late, but I hope you're not sleepy yet..."
        else:
            l "I was sure to save some just for you. I know it's late, but I hope you're not sleepy yet..."
        i "Not at all. I made sure to keep some energy for you too..."
        l "That's great..."
    if ian_cindy_sex:
        $ flena = "sad"
        "My mind kept going back to what Ivy told me. Did Cindy really cheat on Wade... with Ian?"
        $ fian = "n"
        i "Is everything alright?"
        $ flena = "n"
        l "Yeah."
        $ fian = "smile"
        if ian_lena_couple:
            $ flena = "sad"
            "A part of me wanted to ask him, see if Ivy had told the truth. But I couldn't figure out how to bring it up without ruining the mood..."
            if lena_cheating:
                "After all, I had things to hide too. I hadn't been a faithful girlfriend... Was Ian like that too?"
            else:
                "Part of me also wanted to keep believing it was a mistake on Ivy's part. Ian was serious about me, wasn't he...?"
        else:
            "I didn't think she was lying, but if it was true... I wasn't sure I wanted to know for sure."
    $ flena = "n"
    l "I'm dying to get out of these shoes... Can I use your bathroom for a second?"
    $ fian = "smile"
    i "Of course. Make yourself comfortable."
    stop music fadeout 3.0
    play sound "sfx/door.mp3"
    hide lena with long
    $ lena_necklace = 0
    $ lena_extras = 0
    $ lena_makeup = 0
    pause 1
    if ian_cuck > 1:
        l "Alright, I'm done..."
        $ lena_look = "sexy"
        show lenatopless at rig with long
        $ fian = "shy"
        i "Oh, wow... That's what I call getting comfortable."
        menu:
            "{image=icon_sad.webp}Tease Ian":
                $ renpy.block_rollback()
                $ ian_cuck = 3
                $ flena = "slutshy"
                play music "music/sensual.mp3" loop
                $ flena = "slut"
                l "Let's get you comfortable too... You've been waiting for me the whole night, haven't you?"
                i "Yes... I wanted to be with you..."
                l "Good boy... I think you deserve a reward."
                scene v11_kiss_bg2
                show v11_kiss1
                if lena_tattoo2:
                    show v9_lena1_t2
                with long
                "I moved closer to Ian and kissed him. He closed his eyes and responded by placing his hands on my hips, but didn't pull me closer."
                if ian_lena_sex:
                    "I felt his restraint, and I knew he was expecting me to lead the action."
                else:
                    "I felt his restraint, and I knew he was expecting me to lead the action. He didn't want to risk upsetting me by pushing things too far."
                "I was in control... Just how I liked it."
                if lena_lust < 10:
                    call xp_up ('lust') from _call_xp_up_932
                "I laid my hands on Ian's chest, teasingly, and I relieved him of his shirt. I was getting in the mood..."
                scene v11_lenaian1
                if lena_tattoo1:
                    show v11_mark1_t1
                if lena_piercing1:
                    show v11_mark1_p1
                elif lena_piercing2:
                    show v11_mark1_p2
                with long
                pause 1
                if ian_fit > 0:
                    "Soon, we were both naked, making out on the couch. My fingers ran over Ian's abs until they reached his cock, grasping the hardened shaft and squeezing it tightly."
                else:
                    "Soon, we were both naked, making out on the couch. My fingers ran over Ian's belly until they reached his cock, grasping the hardened shaft and squeezing it tightly."
                l "You were waiting for this, weren't you? For me to make you feel good... Well, I know exactly how to do that."
                "I felt him shiver when my tongue began playing around his neck. His breathing quickened with each stroke of my hand."
                l "So, did you like my dress? Did you think it was hot?"
                i "It was... You looked so sexy... I bet you got a lot of attention tonight."
                play sound "sfx/giggle.mp3"
                if v11_bbc:
                    if v11_bbc == "marcel":
                        l "Oh yeah, I did... Even the bouncer tried hitting on me!"
                        i "The bouncer...?"
                        l "Marcel, you've met him... Black, bald, and broad as a barn door. He's kind of intimidating, but he couldn't stop complimenting me on how hot I looked."
                        "For a moment I was tempted to tell Ian about what had really gone down with Marcel, but I refrained. I wasn't sure it was safe to do so... Not yet."
                    elif v11_bbc == "jeremy":
                        l "Oh yeah, I did... I think I gave Jeremy a big hard-on! I'm sure he had trouble working with it..."
                        i "You gave Jeremy... a hard-on?"
                        l "I got that impression... I know he couldn't take his eyes off of me, that's for sure!"
                        "It was exciting toying with the idea of letting Ian know what had been going on between Jeremy and me, but I knew it wasn't safe to do so."
                        "Not yet, at least..."
                    i "..."
                    if v11_bar4 == 3:
                        l "Not only guys showed some interest tonight... A cute girl gave me her Peoplegram too."
                        i "A girl? And... What did she want?"
                        l "I don't know... Maybe I should find out?"
                        if v11_john_flirt == 3:
                            l "I got several contacts tonight. There was also this guy who wanted us to meet someday..."
                            i "And... will you?"
                            l "I don't know. He seemed nice enough. And he was pretty hot!"
                    elif v11_john_flirt == 3:
                        l "There was also this guy who insisted I add him on Peoplegram..."
                        i "And you did?"
                        l "Yeah, he seemed nice enough. And he was pretty hot..."
                else:
                    l "Oh, yeah, I did... Quite a few guys tried to flirt with me."
                    if v11_bar4 == 3:
                        l "And not only guys! A cute girl gave me her Peoplegram too..."
                        i "A girl? And... What did she want?"
                        l "I don't know... Maybe I should find out?"
                        if v11_john_flirt == 3:
                            l "I got several contacts tonight. There was this guy who wanted us to meet someday..."
                            i "And... will you?"
                            l "I don't know. He seemed nice enough. And he was pretty hot!"
                    elif v11_john_flirt == 3:
                        l "This guy insisted I added him on Peoplegram..."
                        i "And you did?"
                        l "Yeah, he seemed nice enough. And he was pretty hot..."
                    else:
                        i "And... What did you do?"
                        l "Nothing... I was too busy with work. But if I had been partying... It would've been much more fun!"
                if lena_jack > 1:
                    l "But the highlight tonight was no doubt Jack... He's this influencer who used to appear on TV."
                    l "He's the biggest, most muscular guy I've ever seen in my life. His shoulders were as large as my head!"
                    i "And... he flirted with you?"
                    if lena_jack > 2:
                        "He did much more than that... But I would keep that secret to myself... At least for now."
                    l "Yeah. He wanted me to pay him a visit in the VIP area..."
                    i "And... What did you do?"
                    l "What do you think?"
                    i "I... I have no idea."
                    l "Would you be mad if something happened?"
                else:
                    l "Are you jealous I'm getting all this attention?"
                    i "I can't be mad about that... I guess I understand the reason..."
                    l "So you wouldn't be mad if something happened?"
                "Ian grunted in response, closing his eyes and gritting his teeth. It seemed like my words were a sweet torture for him."
                l "You didn't say yes... Does that mean you wouldn't get mad? Would that turn you on, maybe...?"
                if v11_tell_dream:
                    l "Like that dirty dream you told me about... The one where your ex got fucked in front of you."
                    "I felt Ian get a goosebump, and his expression changed, but he continued to be silent. I pushed him a bit more."
                    l "Your cock is getting even harder... It turned you on, didn't it?"
                    i "It was just a dream..."
                    l "Maybe your subconscious is trying to tell you something? Maybe you'd really like watching the girl you love being fucked in front of you."
                    l "How did Gillian look? I bet she was enjoying it, right?"
                    l "I wonder if I'd enjoy it too..."
                i "Ahhh... God, Lena...!"
                l "Would you let me... fuck someone else?"
                show v11_lenaian1_cum1 with flash  
                i "Ahhh!!{w=0.5}{nw}" with vpunch
                hide v11_lenaian1_cum1
                show v11_lenaian1_cum2 
                with vpunch
                pause 0.5
                with vpunch
                pause 0.5
                "That pushed Ian over the edge, and I felt him tremble in my hands as he ejaculated. It was so exciting...!"
                if lena_charisma < 10:
                    call xp_up ('charisma') from _call_xp_up_933
                    pause 0.5
                $ flena = "flirtevil"
                $ fian = "blush"
                scene ianhomenight
                show lenanude2 at rig
                show iannude2 at lef
                with long
                "Ian groaned and shivered until his pleasure died down, looked shy and conflicted afterward. It was kind of cute..."
                "I tasted this different and intense kind of arousal I hadn't felt with anything else. It made me want to be a bit more cruel..."
                if lena_axel_desire:
                    "I avoided mentioning anything about Axel, but I wondered what would've happened if I did. Would've he jizzed too?"
                i "..."
                l "Seems you enjoyed that..."
                i "It felt good, but... About what you were saying... I don't think I would..."
                "I cut him off, not wanting to give space to his doubts."
                l "Don't worry about that. I was just teasing you, since you seem to like it... Now's your turn to make me feel good."
                $ flena = "slut"
                l "And you know what I want..."
                i "Yes..."
                if lena_wits < 10:
                    call xp_up ('wits') from _call_xp_up_934
                    pause 0.5
                scene v11_lena7
                if lena_tattoo1:
                    show v10_ian8_t1
                if lena_tattoo2:
                    show v10_ian8_t2
                if lena_piercing1:
                    show v10_ian8_p1
                elif lena_piercing2:
                    show v10_ian8_p2
                with long
                play sound "sfx/ah8.mp3"
                pause 1
                if ian_lena_sex == False:
                    "I wouldn't let Ian have sex with me, but I was content with having him eat me out. He did it so eagerly..."
                else:
                    "I would've liked to have someone fuck me at that moment, but I made Ian cum with my hand..."
                    "Thankfully, he knew how to properly eat me out, and he did it so eagerly."
                if lena_axel_desire:
                    "It felt good, and I knew I would end up cumming too. But compared to what Axel was doing to me, it felt a bit... underwhelming."
                    "As much as I tried to push those emotions out of my mind, I couldn't rid myself of them."
                    "The truth was, deep down, I didn't want to..."
                if lena_jack > 2:
                    "It felt good, and I knew I would end up cumming too. But compared to what Jack was doing to me, it felt a bit... underwhelming."
                    "But anything would feel that way compared to the intense experience I had enjoyed tonight. Jack was something else."
                if lena_mike_dating:
                    "Too bad I couldn't control Mike the way I controlled Ian... He was the kind of guy who didn't let anyone tell him how to live."
                    "Well, maybe his stupid girlfriend... Why the hell had he chosen to be with her? She couldn't be as special as me..."
                if ian_lena_couple:
                    "I had never had a relationship like the one I had with Ian. I knew it was weird and kind of twisted, but..."
                    "I had none of the issues I had in the past. He was obedient and I felt secure. I liked where this was headed..."
                    if lena_cheating:
                        "If I played my cards right, maybe I wouldn't even need to keep hiding my cheating. If I got Ian's blessing to sleep with whoever I wanted..."
                        "That would be incredible. I came like crazy thinking about that."
                else:
                    "This thing I had going on with Ian was  weird and kinda twisted, but it suited me just fine."
                    "I felt secure and in control. I felt like I could have it all..."
                pause 1
                stop music fadeout 3.0
                scene black with long
                pause 1
                $ renpy.end_replay()
                $ gallery_unlock_scene("CH11_S26")
                jump v11epilogueend

            "Go to sleep":
                $ renpy.block_rollback()
                l "Let's go to bed, shall we?"
                $ fian = "n"
                i "Um, sure..."
                call friend_xp ('ian',-1) from _call_friend_xp_1080
                scene ianhomenight_dark with long
                pause 1
                scene black with long
                pause 1
                $ renpy.end_replay()
                jump v11epilogueend
    else:
        $ flena = "flirt"
        l "Alright, I'm done..."
        if ian_lena_couple and lena_cheating == False:
            play music "music/sex_good.mp3" loop
        else:
            play music "music/sex_bright.mp3" loop
        show lenanude2 at rig with long
        $ fian = "confident"
        i "You got comfortable indeed..."
        l "Well... I've been waiting all night to have you all to myself..."
        scene v11_kiss_bg2
        show v11_kiss2
        if lena_tattoo2:
            show v9_lena1_t2
        with long
        pause 1
        "I laid my hands on Ian's chest, relieving him of his shirt. He responded by holding my hips and pulling me closer, kissing me."
        if lena_axel_desire:
            "I had made up my mind. The best way to take Axel's aftertaste off my mind was to give myself to Ian to end the night..."
            if ian_lena_couple:
                "This was what I was supposed to do. Be with my actual boyfriend..."
            else:
                "If someone could do that, it was him..." 
# handjob
    scene v11_lenaian1
    if lena_tattoo1:
        show v11_mark1_t1
    if lena_piercing1:
        show v11_mark1_p1
    elif lena_piercing2:
        show v11_mark1_p2
    with long
    pause 1
    "Soon, we were both naked, making out on the couch. Feeling Ian's body against mine made me tingle with excitement."
    if ian_lena_couple:
        if lena_cheating:
            "I hadn't been faithful to him, but it wasn't because he didn't turn me on. I always enjoyed getting a piece of him..."
        else:
            "He always had this effect on me... I felt so cozy with him, like I was somewhere I belonged..."
    if ian_fit > 0:
        "My fingers ran over Ian's abs until they reached his cock, grasping the hardened shaft and squeezing it tightly."
        l "You're so damn hot... And you have such a nice cock..."
    else:
        "My fingers ran over Ian's belly until they reached his cock, grasping the hardened shaft and squeezing it tightly."
        l "You have such a nice cock..."
    if lena_jack > 2: 
        "It would be more manageable than Jack's monster... I needed that, after the pounding he gave me."
    i "You always manage to get it like this... It feels so good in your hand."
    "We shared passionate and lewd kisses while I caressed his manhood. I felt him shiver and squeeze my ass when my tongue began playing around his neck."
    "His breathing quickened with each stroke. I felt him growing harder and thicker, and the heat inside me intensified."
    i "If you keep going... I feel I could cum just from this..."
    if ian_cuck > 0 or v11_lena_dom or lena_cheating:
        menu:
            "{image=icon_sad.webp}Tease him":
                $ renpy.block_rollback()
                l "I'm simply using my hand... Does it feel that good?"
                "Ian groaned when I whispered into his ear, biting his earlobe and giving his cock a strong squeeze."
                i "Ah, fuck... Yes, it does... Don't be mean..."
                l "I've been waiting the whole night for you to fuck me... What will I do if you cum from just a handjob?"
                i "I won't..."
                l "I'm not sure you can resist... It turns you on when I'm mean, doesn't it?"
                if lena_charisma < 8:
                    call xp_up ('charisma') from _call_xp_up_935
                l "Maybe you want me to use my feet like the other day? I felt like I would've been able to make you cum with them too."
                if ian_lena_dom == 2 or ian_chad > 4:
                    i "You're talking too much... Why don't you use your mouth for something else?"
                else:
                    i "That felt nice, but I prefer your mouth. I want you to suck it..."
                play sound "sfx/giggle.mp3"
                "I giggled, amused at Ian's reaction. He deserved to be obeyed."
                
            "Suck him off":
                $ renpy.block_rollback()
                l "That sounds tempting... But you'll have to hold on. I want to enjoy much more of you."
    else:
        l "That sounds tempting..."
        i "It is, but I won't. I want to enjoy much more of you."
        l "I want to enjoy you too..."
    # blowjob
    play sound "sfx/ah6.mp3"
    scene v11_lenaian2
    if lena_tattoo2:
        show v11_mark2_t2
    if lena_tattoo3:
        show v11_mark2_t3
    with long
    pause 1
    if lena_lust < 10:
        call xp_up ('lust') from _call_xp_up_936
    "I felt the heat radiating from Ian's cock on my lips, and I pushed it deeper into my mouth, savoring his unique aroma that I loved so much."
    if v11_bbc or lena_axel_desire or lena_jack > 2:
        if (v11_bbc and lena_axel_desire) or (v11_bbc and lena_jack > 2):
            "It was crazy to think this was the third cock I sucked tonight... I felt so slutty."
        else:
            "This was the second cock I sucked tonight... I felt so naughty."
        if v11_bbc == "marcel":
            if v11_bbc_pic:
                "I had a bit of fun with Marcel, and I got some really nice pictures to show for it... It would be so nice to watch them and masturbate to them."
            "Ian's tool wasn't nearly as mastodontic, but I loved it nonetheless. I could suck it in its entirety, and that turned me on so much..."
        elif v11_bbc == "jeremy":
            if v11_bbc_pic:
                "I had a bit of fun with Jeremy, and I got some really nice pictures to show for it..."
            else:
                "I had a bit of fun with Jeremy to start the night, and I was happy to end it with Ian."
            "His tool wasn't nearly as mastodontic, but I loved it nonetheless. I could suck it in its entirety, and that turned me on so much..." 
        if lena_axel_desire:
            "The memory of Axel's tool ravaging my mouth was still fresh on my mind, and this only intensified it."
            "I tried pushing it to the back of my mind, but a part of me wished I still was in that restroom, being claimed by him..."
        if lena_jack > 2:
            "I still couldn't believe I managed to deepthroat Jack like that. Handling Ian's cock was easy in comparison... but equaly fun."
    else:
        "It was a pleasure to please him, to feel his swollen, hard flesh on my lips, entering my mouth, pressing on my throat..."
    if lena_mike_dating:
        "This could've been Mike tonight, but he decided to leave home to his girlfriend."
        "It maddened me knowing he had fucked Ivy, especially the fact that they did it at his place, where I hadn't been invited yet..."
        "I stuffed the dick I had in my mouth all the way to my throat, furious. It was his loss!"
    if ian_lena_love:
        i "Oh, fuck, so good...! You're the only one who can make me feel like this..."
    else:
        i "Oh, fuck, so good...! Your blowjobs are the best..."
    play sound "sfx/bj5.mp3"
    "The living room was filled with the wet sounds of my blowjob and our excited panting. Hopefully, Perry wouldn't wake up..."
    # film
    if v10_stalkfap == "ian":
        i "You're so sexy with my cock in your mouth... I think I should film this."
        scene v11_lenaian2b
        if lena_tattoo2:
            show v11_mark2_t2
        if lena_tattoo3:
            show v11_mark2_t3
        with long
        pause 1
        "My only answer was to moan and keep sucking Ian off. It turned me on how he went ahead with his desires..."
        "Our little photo shoot together was one of the most exciting experiences I have shared with anyone. I loved being filmed by him."
        "I made sure to give him a good show this time again."
        label v11lenaianfilm:
            $ v11_ian_sex = 2
            $ lena_fty_show = True
        "My tongue traced wet paths across the shaft, my lips wrapped around it and made it disappear in my throat as I made eye contact with the camera."
        "Letting Ian capture such a dirty, intimate part of me, surrendering my most naughty self to the camera... It was such a high."
        if lena_charisma < 8:
            call xp_up ('charisma') from _call_xp_up_937
        if v10_wc_bj == "ian":
            i "I should've filmed you like this when you dragged me into the club's restroom... You were so damn sexy that night."
            "Reminiscing about it only made my belly tingle with increasing agitation. I could be my naughtiest self with Ian."
        if stalkfap_pro > 1 and seymour_desire == False:
            "Maybe I could share the video with some of my most loyal subscribers... They would surely pay good money for the privilege of seeing it."
        
    elif ian_stalkfap_on or ian_lust > 7:
        i "You're so sexy with my cock in your mouth... It would be so hot to film this, don't you think?"
        menu:
            "{image=icon_lust.webp}Go ahead" if stalkfap or lena_fty_show or lena_lust > 6:
                $ renpy.block_rollback()
                if stalkfap_pro:
                    l "You wanna record me like your little Stalkfap model?"
                    if ian_stalkfap_on == 2:
                        i "Hell yeah... I love watching you be all sexy and slutty on camera."
                    else:
                        i "Yeah... I'm your VIP fan after all, am I not?"
                    l "Film me as much as you want... As long as you send it to me later."
                else:
                    l "You wanna film me sucking your cock...?"
                    i "Yeah... I'd like to be able to watch you when we're apart..."
                    l "What a naughty boy... But alright. I'm feeling really naughty too, so..."
                scene v11_lenaian2b
                if lena_tattoo2:
                    show v11_mark2_t2
                if lena_tattoo3:
                    show v11_mark2_t3
                with long
                pause 1
                "Ian pulled out his phone and pointed the camera at me. I made sure to give him a good show."
                jump v11lenaianfilm

            "Forget about that":
                $ renpy.block_rollback()
                l "Forget about that... I want you to fuck me."

    "I took my time savoring Ian's dick. He endured my endless teasing, groaning, and watching me with a lustful smile."
    "He was so sexy..."  
    # POV cowgirl
    scene v11_lenaian4
    if lena_tattoo2:
        show v11_mark4_t2
    with long
    pause 1
    l "I can't wait anymore. I want you inside of me..."
    i "I'm all yours..."
    "I sat on Ian's lap, sliding his cock into my melting pussy. The connection shocked my entire body with a shiver of pleasure."
    l "Oh, yes...! It's so hard!"
    i "Of course. I have the sexiest view right now..."
    if v11_ian_sex == 2:
        l "Be sure to get a good angle on it... I wanna see it too... Mhhh!"
        i "Is that so? You wanna watch your perfect sexy ass bouncing on my cock...?"
        l "Yes...! I wanna see how hot it looks... Your hard rod penetrating me, balls deep...!"
    else:
        i "Mhhh...! You have no idea how much it turns me on."
        l "I think I get a clue..."
    "I began swaying my hips, fucking Ian slow and deep."
    "Every time I impaled myself on Ian's cock, pleasure radiated from my core and my whole body quaked with delight."
    # cowgirl
    scene v11_lenaian3
    if lena_tattoo1:
        show v11_mark3_t1
    if lena_tattoo2:
        show v11_mark3_t2
    if lena_tattoo3:
        show v11_mark3_t3
    if lena_piercing1:
        show v11_mark3_p1
    elif lena_piercing2:
        show v11_mark3_p2
    with long
    pause 1
    "My hips moved instinctively, raising up and falling down with increasing momentum."
    "I was barely managing to hold in my moans, but the sound of flesh slapping against flesh echoed loudly in the living room."
    "I wanted more. I wanted him deeper."
    play sound "sfx/oh4.mp3"
    l "Fuck... Yes...!" with vpunch
    if lena_athletics < 10:
        call xp_up ('athletics') from _call_xp_up_938
    i "Let's not wake Perry up... I'd hate it if he interrupted us right now."
    if v10_tease_perry:
        l "He can watch and jerk off to us for all I care... I'm not gonna stop!"
        l "It feels too fucking good!"
    else:
        l "It feels too good... I can't stop myself!"
    "I felt Ian swell inside me, pushing me to the edge of my arousal."
    if ian_lena_dom == 2:
        stop music fadeout 2.0
        $ v11_lena_anal = True
        i "I won't hold back either, if that's the case. I'mma fuck you like you deserve..."
        play music "music/sex_vixen.mp3" loop
        # anal
        scene v11_lenaian5
        if lena_tattoo1:
            show v11_mark5_t1
        if lena_tattoo3:
            show v11_mark5_t3
        if lena_piercing1:
            show v11_mark5_p1
        elif lena_piercing2:
            show v11_mark5_p2
        with long
        pause 1
        "Ian took control, getting me on my fours and standing up behind me."
        "I felt his erect rod pressing and sliding across my pussy, wet and slippery, teasing me."
        "He wasn't aiming for my slit, though, and soon I felt the tip of his cock pushing into my asshole."
        if lena_anal > 1:
            l "Oh, fuck yes... Fuck me in the ass... I love it!"
            i "I know... You're such a dirty girl, after all."
            l "Yes... I'm a dirty girl who wants you to destroy her ass..."                
        else:
            l "Oh, fuck... You're so bad... Are you gonna fuck my ass?"
            i "I'd say it's about time I did... Are you ready?"
            l "Yes, but be gentle... This will be my first time."
            i "We'll see about that."
        play sound "sfx/ah9.mp3"
        "I moaned uncontrollably as Ian made his way inside my rectum."
        "The sensation of my anus being stretched by his hard cock sent shivers all across my body, my mind melting with excitement."
        if lena_anal > 1:
            i "It went in so easily... You're quite the anal slut, huh?"
            l "Yes... I'm a slut for you... Your slut. Fuck me however you want!"
        else:
            i "Seems like you're enjoying it quite a lot... Not bad for a first time."
            l "Mhhh, yes... I've been wanting to try this for so long..."
    elif ian_lena_dom:
        i "I won't hold back either, if that's the case. I want to go hard on you..."
    else:
        l "God, yes! I'm cumming!"
        play sound "sfx/orgasm1.mp3"
        with flash
        with vpunch
        pause 0.5
        with vpunch
        pause 0.5
        with vpunch
        if lena_axel_desire:
            "It made me forget about Axel. It stripped away my anguish and confusion, leaving only pleasure."
        if lena_mike_dating:
            "Who needed Mike when I had a guy like Ian? I wish he could see me now and realize what he was losing!"
        "With one final thrust, I shattered into a thousand pieces as I felt myself coming undone."
        jump v11lenaiansexend

    # animation
    scene v11_lenaian6a with long
    pause 1
    if v11_lena_anal:
        i "Are you ready? I'm gonna fuck all your holes and make you mine..."
        l "Yes...! I'm yours... All yours!"
    else:
        "Ian took control, getting me on my fours and standing up behind me."
        "I felt his erect rod pressing and sliding across my pussy, wet and slippery, teasing me..."
        l "Yes, yes...! Fuck me however you want... Make me yours, Ian!"
    play sound "sfx/oh1.mp3"
    scene v11_lenaian6b with fps
    scene v11_lenaian6c with vpunch
    l "Oh, fuck...!"
    "My whole body tensed up around Ian's cock, so deep inside me. It was such an intense and pleasant sensation..."
    scene v11_lenaian6_animation with fps
    pause 4
    "The sofa creaked under Ian's thrusts as he fucked me with passionate intensity."
    "It took him a while to reveal his dominant side to me, but now it came naturally to him. And I loved it so much."
    "The way he took control, his eagerness to impose his desire on me, the raw emotions he shared with me..."
    if lena_axel_desire:
        "I wanted him to fuck me until I came undone, like I was about to do with Axel a few moments ago."
    elif lena_jack > 2:
        "I wanted him to fuck me until I came undone, like Jack had fucked me a few moments ago."
    else:
        "I could give myself up to him and release all that agonizing tension, coming undone under his body."
    if v11_lena_anal:
        l "Yes...! Yes! You're tearing my ass apart, and I love it!"
    else:
        l "Yes...! Yes! You're so deep inside me! I love it!"
    if lena_axel_desire:
        "It made me forget about Axel. It stripped away my anguish and confusion, leaving only pleasure."
    if lena_mike_dating:
        "Who needed Mike when I had a guy like Ian? I wish he could see me now and realize what he was losing!"
    elif lena_cheating:
        "Why was I cheating on Ian for? He could give me exactly what I wanted, what I needed..."
    play sound "sfx/orgasm2.mp3"
    scene v11_lenaian6c with flash
    with vpunch
    pause 0.5
    with vpunch
    pause 0.5
    with vpunch
    pause 0.5
    "With one final thrust, I shattered into a thousand pieces as I felt myself coming undone."
    if v11_lena_anal:
        "My screams of ecstasy were loud and animalistic, shaking under Ian's body."
    else:
        "I let out a long moan, struck by the most delicious orgasm."
    "The world ceased to be for several seconds, and I with it. I was only aware of the intimate presence of Ian inside me."

label v11lenaiansexend:
    "Ian's manhood throbbed inside of me, harder than ever. I wanted him to experience bliss too."
    # cum
    scene v11_lenaian7 with long
    "I got on my knees and opened my mouth, eager, with my mind still shaken by the intense orgasm."
    l "I want it... Give it to me!"
    i "Fuck, yes... Open wide... I'm gonna cum!"
    l "Yes! Mark me with your cum... Shoot it all over my face...!"
    play sound "sfx/ah6.mp3"
    show v11_mark7_animation with flash
    pause 0.3
    show v11_mark7_animation
    show v11_mark7e
    with flash
    pause 0.5
    show v11_mark7_animation with fps
    pause 2
    "I felt several jets of hot sperm splashing my lips as Ian painted me with his scent."
    "Ropes of cum dripped down my lips into my mouth, leaving shiny streaks on my cheeks and chin..."
    "Covered with Ian's lust, I took in his scent and his taste..."
    if lena_jack > 2:
        "Just like with Jack, I had given myself to him, and in return, he had surrendered a part of him to me."
    else:
        "I had given myself to him, and in return, he had surrendered a part of him to me."
        if lena_will < 2:
            call will_up() from _call_will_up_62
    stop music fadeout 3.0
    "A part that belonged to me... Only me."
    $ fian = "confident"
    $ flena = "slut"
    scene ianhomenight_dark with long
    play music "music/ourredstring.mp3" loop
    show iannude2 at lef
    show lenanude2 at rig
    show lena_cum1 at rig
    with long
    "Ian lounged on the sofa, breathing heavily and wearing a smile."
    if v11_lena_squirt:
        i "God, that was so hot... Too bad I didn't manage to make you squirt today."
        $ flena = "shy"
        l "It's not something that happens every time... But I came so hard today too."
        if lena_jack > 2:
            "There was no way I could squirt again after the incredible orgasm Jack gave me. I shivered thinking about it."
    else:
        i "God, that was so hot..."
        if lena_jack > 2:
            l "Yeah..."
            "Not as hot as Jack fucking me in the restroom... God, he made me cum so hard I squirted all over the place."
            "I shivered thinking about it."
    if ian_lena_couple or ian_lena_love:
        $ fian = "smile"
        i "Come here. I wanna hug you."
        $ flena = "shy"
        "I snuggled up next to him on the couch, nestled under his arm and against his warm chest."
        "I breathed in his scent and shivered with delight as he affectionately stroked my hair."
        l "Did you miss me?"
        i "Yeah... I was eager to see you and be like this with you."
        l "Me too..."
    else:
        i "It was worth staying awake for you... It keeps getting better and better."
        l "So it seems..."
        if lena_ian_love:
            "I snuggled up next to him on the couch, nestled under his arm and against his warm chest."
            "I breathed in his scent and shivered with delight as he affectionately stroked my hair."
    if v11_lena_anal and lena_anal < 2:
        $ lena_anal = 2
        $ flena = "shy"
        l "My ass hurts a bit, though! You were really intense..."
        i "Sorry... I couldn't resist. I had been wanting to do it for quite some time..."
        l "I still need to get used to it. Maybe we should try again some other time..."
        i "I'm up for that."
    if ian_lena < 12:
        call friend_xp ('ian') from _call_friend_xp_1081
    $ flena = "n"
    l "I'm finally out of batteries. I feel I could sleep forever..."
    $ fian = "smile"
    i "Let's go to bed, then. It's super late..."
    scene v11_kiss3 
    if lena_tattoo2:
        show v7_lena7end_t2
    if lena_axel_desire:
        show v11_kiss3b
    with long
    if lena_axel_desire:
        "Ian's scent had dulled the memory of Axel, but now that the lust was slowly fading away, what had transpired in the restrooms was coming back with intensity."
        "As much as I tried to push those emotions out of my mind, I couldn't rid myself of them."
        "Perhaps, in reality, I didn't want to..."
    else:
        "The night had been long and tiresome, but the end was just what I had been needing."
    if ian_lena_couple or lena_ian_love:
        if lena_axel_desire:
            "But Axel was a thing of the past. Now things were different."
        "I loved being with Ian... He made me feel I was somewhere I belonged, a place where I could be vulnerable and understood."
        if v11_lena_openup:
            "He trusted me with his emotions too, letting me delve deep and really get to know him. I knew that took bravery, and it also made me feel special and valued."
        else:
            "Things had been a bit tense since Gillian appeared. I could sense he was keeping some of his emotions locked away from me, and yet..."
            "We still connected so well, or so I felt."
        if lena_cheating:
            "He didn't deserve me cheating on him, but I couldn't rid myself of a tempting voice in my head..."
            "A voice that said that, if I kept things like this, I could get the best of both worlds..."
            if lena_jack_dating:
                "I didn't want to give up experiences such as the one Jack gave me tonight. I hoped I would see him again..."
            if ian_cindy_sex:
                "Besides... As far as I knew, he could have been cheating on me too with Cindy..."
        elif ian_lena_couple:
            "With him, I had learned a different way of loving, and being loved was possible."
            "He was helping me heal... And I wished I could do the same for him."
            if ian_cindy_sex or lena_go_holly == 5:
                if ian_cindy_sex:
                    "But there was a dark cloud on the horizon. What Ivy had told me... I wondered what role Cindy played in all of this."
                if lena_go_holly == 5:
                    "Holly had been on my mind too. The kiss she gave me... It meant something."
                "I couldn't help but wonder where all this was going, and if we would have a happy end or not..."
        else:
            "But there was still a distance between us... Were we not ready to finally open up to each other?"
            "I knew where he stood, how he felt about relationships, and yet... I couldn't help but wish we'd get a real chance at being together..." 
            if lena_mike_sex or lena_axel_desire:
                "Of course, things weren't that easy. My emotions were all over the place, and I couldn't seem to get a grip on them."
            if lena_go_holly == 5:
                "Holly had been on my mind too. The kiss she gave me... It meant something."
                "I couldn't help but wonder where all this was going, and if we would have a happy end or not..."
    else:
        if lena_axel_desire:
            "That's why I was hoping focusing on Ian would keep me grounded in the present. Things were different now, or so I told myself..."
        "Ian and I had great chemistry, that was more than obvious by now. We clicked so well together, and yet..."
        if lena_jack > 2:
            if lena_jack_dating:
                "I didn't want to give up experiences such as the one Jack gave me tonight. I hoped I would see him again..."
            else:
                "I didn't want to give up experiences such as the one Jack gave me tonight."
        if ian_lena_love:
            "I knew Ian felt something for me... And I felt something for him too. But..."
        "I didn't feel ready for something serious, or so I told myself. The truth was taking a gamble where my feelings were at stake scared me."
        if v11_lena_openup:
            "Ian had been brave and opened up to me, letting me delve deep and really get to know him. I knew that took bravery, and it also made me feel special and valued."
        else:
            "And I felt Ian was in a similar situation. I could tell he was avoiding opening up to me, keeping his deeper emotions to himself..."
        if lena_go_holly == 5:
            "Holly had been on my mind too. The kiss she gave me..."
        "I couldn't help but wonder where all this was going, and if we would have a happy end or not..."
    stop music fadeout 3.0
    scene black with long
    pause 1
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH11_S26")
    jump v11epilogueend


## END
label v11epilogueend:

    jump master_script



###### SCREENS ###############################################################
define v11bikinitry = [
    # val, img, if_owned, if_true, pos
    (1, 'v11bikini1', "False", "True", (390,540)),
    (2, 'v11bikini2', "False", "lena_charisma > 5", (960, 540)),
    (3, 'v11bikini3', "False", "lena_lust > 6", (1526, 540))
]

define v11buydress = [
    # val, img, if_owned, if_true, pos
    (1, 'v11clubdress1', "False", "True", (1032,316)),
    (2, 'v11clubdress2', "False", "lena_charisma > 5", (1551, 316)),
    (3, 'v11clubdress3', "False", "lena_athletics > 4", (1032, 765)),
    (4, 'v11clubdress4', "False", "lena_lust > 5", (1551, 765))
]

screen screen_choice(data, is_shop = False):
    for val, img, if_owned, if_true, pos in data:
        imagebutton:
            pos pos
            anchor (0.5, 0.5)

            if eval(if_owned):
                idle "%s.webp" % img
                insensitive "%s_owned.webp" % img
            else:
                idle "%s.webp" % img
                hover "%s_hover.webp" % img
                insensitive "%s_block.webp" % img

            if eval(if_true) and not eval(if_owned):
                action Play("ch_one", "sfx/paper_click.mp3"), Return(val)
    
    if is_shop:
        imagebutton auto "sexshop_leave_%s.webp" pos (856, 730) action Play("ch_one", "sfx/paper_click.mp3"), Return(False)
        add "sexshop_money.webp" pos (1799, 29)
        text "{font=[font_big_noodle_oblique]}{color=#000000}[lena_money]{/color}":
            size 30
            xpos 1815 ypos 56

define v11bartendingdata = [
    # char, if_true
    ('finley', "v11_bar3b == 0"),
    ('eli', "v11_bar4b == 0"),
    ('john', "v11_bar1 > 0  and v11_bar1b == 0"),
    ('rosa', "v11_rosa_fight < 2 and v11_bar2b == 0")
]

screen v11bartendingscreen():
    $ k = 0
    $ num = 0
    for char, if_true in v11bartendingdata:
        if eval(if_true):
            $ num += 1

    for char, if_true in v11bartendingdata:
        if eval(if_true):
            $ i = 260 + 480 * k + 240 * (4-num)
            $ k += 1
            imagebutton:
                at bartending_trnasition
                xpos i
                xanchor 0.5
                yalign 1.0
                idle char
                action Return(str(i) + "_" + char)
        
transform bartending_trnasition:
    on hover:
        easein 0.5 zoom 1.00
    on idle:
        easein 0.5 zoom 0.96
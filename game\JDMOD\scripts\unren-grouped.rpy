init 999 python:

    config.developer = True
    config.console = True

    renpy.config.rollback_enabled = True
    renpy.config.hard_rollback_limit = 256
    renpy.config.rollback_length = 256

    def unren_noblock( *args, **kwargs ):
        return
    renpy.block_rollback = unren_noblock
    try:
        config.keymap['rollback'] = [ 'K_PAGEUP', 'repeat_K_PAGEUP', 'K_AC_BACK', 'mousedown_4' ]
    except:
        pass

    def JDblock_rollback(purge=False):
        """
        :doc: blockrollback
        :args: ()

        Prevents the game from rolling back to before the current
        statement.
        """
        
        renpy.game.log.block(purge=purge)
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc


init python:
    renpy.music.register_channel ("ch_one", "sfx", False)
    renpy.music.register_channel ("ch_two", "sfx", False)
    renpy.music.register_channel ("ch_three", "sfx", False)
    renpy.music.register_channel ("ch_four", "sfx", False)
    renpy.music.register_channel ("ch_five", "sfx", False)
    renpy.music.register_channel ("ch_six", "sfx", False)

# init python:
#     if not persistent.set_volumes:
#         persistent.set_volumes = True
#         _preferences.volumes['music'] *= .75
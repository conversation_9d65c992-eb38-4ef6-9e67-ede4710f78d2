init offset = 1

screen save():
    tag menu
    use file_slots(_("Save"))


screen load():
    tag menu

    if main_menu:
        use file_slots(_("Load"))
    else:
        use phone_load

image JD_icon_save = im.Scale("JDMOD/images/gui/JD.png", int(config.screen_height * 0.025), int(config.screen_height * 0.025), yalign = 1.0, yoffset=3)

screen file_slots(title, end=False):
    default page_name_value = FilePageNameInputValue(pattern=_("Page {}"), auto=_("Automatic saves"), quick=_("Quick saves"))
    default hover_slot = None

    use game_menu(title):
        vbox style "file_slots_vbox":

            order_reverse True


            button style "page_label":
                key_events True
                action page_name_value.Toggle()
                input:
                    style "page_label_text"
                    value page_name_value


            grid gui.file_slot_cols gui.file_slot_rows:
                style_prefix "slot"
                xalign 0.5
                spacing gui.slot_spacing
                for i in range(gui.file_slot_cols * gui.file_slot_rows):
                    $ slot = i + 1
                    button:
                        hovered SetLocalVariable("hover_slot", slot)
                        unhovered SetLocalVariable('hover_slot', None)
                        action FileAction(slot)

                        tooltip save_desc(slot)

                        has vbox spacing 2

                        add Frame(FileScreenshot(slot), xysize=(gui.slot_width, gui.slot_height)) xalign 0.5

                        text FileTime(slot, format="%c", empty=_("empty slot")):
                            style "slot_time_text"
                            xalign 0.5

                        text FileSaveName(slot).split("\n")[0].replace("JDMOD |","{image=JD_icon_save} "):
                            style "slot_name_text"
                            xalign 0.5

                        key "save_delete" action FileDelete(slot)

                        if hover_slot == slot:
                            imagebutton:
                                auto "/gui/button/delete_%s_button.png"
                                action FileDelete(slot)
                                xpos 186
                                ypos -196
                                hovered SetLocalVariable("hover_slot", slot)
                                unhovered SetLocalVariable('hover_slot', None)
                        else:
                            add Null(30,30)


            hbox style "page_hbox":
                style_prefix "page"
                spacing gui.page_spacing

                $ realpage = int(FilePageName(0,0))
                $ page = realpage
                if page < 1:
                    $ page = saveload_previus_page

                textbutton _("<<"):
                    if page > 1:
                        action FilePage(1)

                textbutton _("<") action FilePagePrevious()

                if config.has_autosave:
                    textbutton _("{#auto_page}A") action SelectedIf(FilePage("auto")), SetVariable('saveload_previus_page', page)

                if config.has_quicksave:
                    textbutton _("{#quick_page}Q") action SelectedIf(FilePage("quick")), SetVariable('saveload_previus_page', page)

                $ start = max(page - 5 - max(5 + page - saveload_maxpages, 0), 1)
                $ end = min(page + 6 + min(max(6 - page, 0), 6), saveload_maxpages + 1)

                for p in range(start, end):
                    textbutton "[p]" action FilePage(p)

                textbutton _(">"):
                    if page < saveload_maxpages or realpage == 0:
                        action FilePageNext()
                textbutton _(">>"):
                    if page < saveload_maxpages or realpage == 0:
                        action FilePage(saveload_maxpages)

    use mouse_tooltip(screen="saveload")






label JD_master_script():
    if (chapter < JD_chapter and persistent.JD_phone_allowendsave) or chapter == JD_chapter:
        $ quick_menu = False

        pause 0.3
        scene blackbg with long_dissolve
        $ renpy.checkpoint()
        call screen JD_endsave()
        $ renpy.pause(0.5)

        $ quick_menu = True

    if chapter < JD_chapter:
        jump JD_master_script2
    else:
        $ JD_IGG_isactive = False
        jump master_script

screen JD_endsave():
    style_prefix "endsave_JD"
    key "game_menu" action NullAction()
    modal True

    add ImageReference("JD_bg")
    use JD_title("End of Chapter")

    vbox spacing 30:
        text "You reached the end of\n{color=[JD_keybind_color]}{outlinecolor=[JD_keybind_outlines_color]}{size=+40}Chapter %s{/size}{/color}{/outlinecolor}." % get_chapter_number_text() size text_size * 4

        if chapter != JD_chapter + 1:
            text "Even though, save incompatiblities were fixed in v0.13.2.0, saving here is recommended."

        vbox spacing 15:
            frame:
                has button:
                    text "{color=[JD_keybind_color]}{outlinecolor=[JD_keybind_outlines_color]}1.{/color}{/outlinecolor}   Go to Save Menu"
                    xsize 350
                    keysym ("1", "K_KP1")
                    action ShowMenu("phone_save", end=True)

            frame:
                has button:
                    text "{color=[JD_keybind_color]}{outlinecolor=[JD_keybind_outlines_color]}2.{/color}{/outlinecolor}   Continue"
                    keysym ("2", "K_KP2")
                if chapter == JD_chapter:
                    action Show("JD_endsave_jumptolabel")
                else:
                    action Return()

            null height 50

            if persistent.JD_developer:
                frame:
                    has button:
                        text "(DEV ONLY)   Force jumptolabel"
                        action Show("JD_endsave_jumptolabel")

                frame:
                    has button:
                        text "(DEV ONLY)   Go to Load Menu"
                        action ShowMenu("phone_load")

    textbutton "{color=[JD_keybind_color]}{outlinecolor=[JD_keybind_outlines_color]}Esc.{/color}{/outlinecolor}   Go to Main Menu":
        style "returnbutton_JD"
        at menu_right
        action MainMenu(confirm=False)
        keysym "K_ESCAPE"

style endsave_JD_vbox:
    xminimum int(config.screen_width * 0.6)
    xalign 0.5
    yalign 0.5
style endsave_JD_button:
    xminimum 350
style endsave_JD_window:
    background Solid("#ffffff")
style endsave_JD_text:
    text_align 0.5
    size text_size * 3



screen JD_endsave_jumptolabel():
    style_prefix "endsave_JD"
    key "game_menu" action NullAction()
    modal True

    default labelforjump = ""

    add ImageReference("JD_bg")
    use JD_title("End of Chapter")

    vbox spacing 20:
        vbox spacing 0:
            text "You reached the end of the modded content available in this version."
            text "You can check the mod and game version in the top right corner."
            text "If this mod is outdated, a label to jump to is required to switch to the new unmodded content."
            text "Unless it is the end of a Chapter, the label must be entered manually."

        null height 10

        vbox spacing 5:
            text "Type/paste label below:" size text_size * 4
            window:
                yoffset 0
                input:
                    style "JD_text"
                    font font_peach_pen
                    color JD_text_altcolor
                    value ScreenVariableInputValue("labelforjump")
                    allow (
                        "abcdefghijklmnopqrstuvwxyz"
                        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
                        " ' -_!()@:;?<>,.#/"
                        "0123456789"
                    )
                    copypaste True
            frame:
                has button:
                    text "{color=[JD_keybind_color]}{outlinecolor=[JD_keybind_outlines_color]}Enter.{/color}{/outlinecolor}   Jump to label"
                    action Hide("JD_endsave_jumptolabel"), SetVariable("JD_IGG_isactive", False), Jump(labelforjump)
                    keysym "K_RETURN"

        frame:
            has button:
                text "Check JD's Discord for the label"
                action OpenURL("https://discord.gg/XMKgefdjqr")

        frame:
            has button:
                text "Jump to the start of the next Chapter"
                action Hide("JD_endsave_jumptolabel"), Return()


    textbutton "{color=[JD_keybind_color]}{outlinecolor=[JD_keybind_outlines_color]}Esc.{/color}{/outlinecolor}   Go to Main Menu":
        style "returnbutton_JD"
        at menu_right
        action MainMenu(confirm=False)
        keysym "K_ESCAPE"






define config.load_failed_label = "failed_label"

label failed_label:
    scene JD_bg
    $ quick_menu = False
    hide screen ui
    call screen JD_failed_label
    pause

screen JD_failed_label():
    style_prefix "error_JD"
    key "game_menu" action NullAction()
    modal True

    use JD_title()
    vbox:
        text "Your save couldn't be loaded and will revert to the beginning of the chapter.\nThis feature is mostly untested and might mess with the game."
        if chapter > 0:
            textbutton "{color=[JD_keybind_color]}{outlinecolor=[JD_keybind_outlines_color]}1.{/color}{/outlinecolor}   Go to Chapter {}".format(get_chapter_number_text()):
                xsize 510
                keysym ("1", "K_KP1")
                action Hide("failed_label"), Jump("JD_chapter_{}".format(get_chapter_number_text().lower()))
    textbutton "{color=[JD_keybind_color]}{outlinecolor=[JD_keybind_outlines_color]}Esc.{/color}{/outlinecolor}   Go to Main Menu":
        style "returnbutton_JD"
        at menu_bottom
        keysym "game_menu"
        action MainMenu(confirm=False)
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc

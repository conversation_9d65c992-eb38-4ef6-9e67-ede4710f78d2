##################################################################################################################################################################################################################
########################################################### CHAPTER 8  FALLING INTO PLACE #################################################################################################################################################################################
##################################################################################################################################################################################################################

label chapter_eight:
    if persistent.include_disabled:
        $ config.menu_include_disabled = True
    $ greyed_out_disabled = False

    if v6_axel_pose > 1:
        $ v6_axel_work = True
    $ holly_glasses = True

    call label_chapter_title from _call_label_chapter_title_2

##IAN HOLLY HOTEL ##############################################################################################################################################################################################################################
    if ian_holly_sex == False:
        jump v8lenastart
    else:
        $ save_name = "Ian: Chapter 8"

        call calendar(_day="Sunday") from _call_calendar_69

        label gallery_CH08_S01:
            if _in_replay:
                call setup_CH08_S01 from _call_setup_CH08_S01

        scene hotel with long
        play sound "sfx/shower.mp3"
        $ ian_look = 3
        $ fian = "n"
        $ fholly = "shy"
        "The sound of the shower woke me up."
        "The soft light of the morning warmed up the room where Holly and I had spent the night."
        show iannude2 with short
        "Flashes of what had transpired amongst the ruffled bed sheets flooded my thoughts right away."
        if ian_lena_dating:
            $ fian = "worried"
            "I really did sleep with Holly... And it probably had been against my better judgment."
            "Now I had quite a complicated situation on my hands. What should I do about it?"
            $ fian = "sad"
            if ian_lena_love:
                "I had been dating Lena for a while now, and things had been looking rather promising."
                "I wasn't sure what her perspective on things was, but I was well aware of my own feelings for her."
                "She was turning out to be someone really special to me... And she was Holly's friend."
                i "Fuck, I should've kept it in my pants last night. But I really like Holly, too..."
            else:
                "It wasn't like Lena was my girlfriend or anything of that sort, but..."
                "She and Holly had become good friends, and I had just complicated things for the three of us."
                i "Maybe I should've kept it in my pants last night. But I really like Holly, too..."
        else:
            $ fian = "smile"
            "I really did have sex with Holly... And it felt so special."
        "The feeling of her petite and soft body still lingered on my skin. Her sweet smell was still on the bed sheets."
        "The sound of her lovely moans was fresh in my memory..."
        hide iannude2
        show iannude
        with short
        "My body was quick to react to those images, blood quickly rushing to my cock."
        i "Holly..."
        $ fian = "worried"
        play sound "sfx/door.mp3"
        show iannude at lef with move
        show hollynude3 at rig
        show holly_towel at rig
        with short
        h "Oh... Good morning..."
        h "Did I wake you up?"
        "I quickly pulled the bed sheets, covering my swollen dick."
        $ fian = "n"
        i "No... I mean, yeah, but don't worry."
        h "You can keep sleeping if you want, the checkout is at twelve."
        i "You're heading out?"
        h "Yes, there are still a couple of conferences I have to attend. But I'm not in a hurry, so..."
        $ fholly = "flirtshy"
        h "Do you want me to maybe... help you with that?"
        $ fian = "insecure"
        "She pointed at the glaringly obvious tent pole I was sporting. The bed sheets did a pretty awful job at concealing my erection."
        $ fian = "blush"
        i "Uh, well..."
        "I wasn't expecting Holly to make such a proposal! Maybe she wasn't that innocent after all..."
        menu:
            "Accept Holly's offer":
                $ renpy.block_rollback()
                $ v8_holly_bj = True
                $ fian = "shy"
                i "I can't say no to that..."
                $ fholly = "flirt"
                "Holly looked directly into my eyes, though still a bit timidly."
                h "You can't?"
                $ fian = "smile"
                i "I'm afraid not..."
                hide holly_towel
                hide hollynude3
                show hollynude at rig
                with short
                $ fian = "surprise"
                "Holly undid the towel that covered her body, letting it drop to her feet."
                $ fian = "confident"
                i "Especially if you do this!"
                $ fholly = "shy"
                hide hollynude
                show hollynude2 at rig
                with short
                h "It's embarrassing..."
                $ fholly = "flirtshy"
                h "But I need to take care of {i}that{/i}."
                hide hollynude2 with long
                play music "music/sensual.mp3" loop
                $ fian = "shy"
                "She got on her knees in front of me and reached for my cock."
                scene v8_hotel1 with long
                pause 1
                "It throbbed, blood rushing to harden it even more, as soon as I felt Holly's delicate fingers grasp the shaft."
                "She opened her mouth and a shy tongue slid out, caressing my glans."
                if ian_lust > 4:
                    i "Wow, Holly... I never knew you could be so sexy."
                    h "I'm not..."
                    i "Believe me, you are."
                else:
                    i "Wow, Holly..."
                    h "What?"
                i "You turn me on so much."
                if ian_lust < 7:
                    call xp_up('lust') from _call_xp_up_477
                "I saw Holly blush even more before she intensified her blowjob."
                scene v8_hotel2 with long
                pause 1
                h "Nhh..."
                "She wrapped her lips around the glans and tried to suck on it."
                i "Ouch! Watch the teeth..."
                h "I'm sorry...!"
                i "Don't worry. Try to use just your lips..."
                i "Yes, like that..."
                "Holly puckered her lips and slid them around the glans."
                if v7_holly_bj:
                    "I had been witness to her lack of experience last night, and she hadn't gotten better overnight."
                    "I could feel she was unsure of what she was doing, trying to figure it out rather clumsily."
                else:
                    "Her lack of experience seemed quite obvious... I could feel she was unsure of what she was doing, trying to figure it out rather clumsily."
                "I twitched from time to time, when Holly's teeth lightly scraped against my glans again, but I didn't want to undermine her."
                "Rather than complaining, I tried guiding her."
                scene v8_hotel3 with long
                pause 1
                i "Try swallowing it a bit deeper... And stroke the shaft while you use your lips..."
                h "Mnhh... Like this?"
                i "Yeah... Try getting your tongue involved, too..."
                h "Mhh... Nhhh..."
                "She did as I asked, but her rhythm was off and she applied too little pressure with both hands and lips."
                "I knew I wouldn't be able to cum like this... She looked so cute, though..."
                "I had a raging hard-on that was demanding to explode. I knew we were short on time, though..."
                $ v8_hollyask4sex = False
                if v7_holly_rough == False:
                    $ config.menu_include_disabled = False
                    $ greyed_out_disabled = True
                menu v8hollybjtalk:
                    "{image=icon_lust.webp}Ask Holly for sex" if v7_holly_rough and v8_hollyask4sex == False:
                        $ renpy.block_rollback()
                        $ v8_hollyask4sex = True
                        i "Hey, Holly, can we... have sex?"
                        scene v8_hotel1 with long
                        h "I'd rather not... I'm a little sore after last night..."
                        i "Oh, yeah. Of course..."
                        "I hadn't exactly held back last night, giving it to Holly on all fours..."
                        $ config.menu_include_disabled = False
                        $ greyed_out_disabled = True
                        jump v8hollybjtalk

                    "{image=icon_lust.webp}Finish yourself off" if ian_lust > 4:
                        $ renpy.block_rollback()
                        $ v8_holly_cum = True
                        "If I wanted to cum I would need to take matters into my own hands. Quite literally."
                        scene v8_hotel4_animation with long
                        pause 1
                        i "Open your mouth, Holly. Use just your tongue..."
                        "She hesitated for a second, but then closed her eyes and did as I asked."
                        "I jerked myself off, immediately finding the right rhythm and tightness."
                        "The underside of my glans rubbed against Holly's wet and silky tongue, sending jolts of pleasure across the shaft."
                        "I was getting closer...!"
                        "Holly was pretty bad at blowjobs, but having her on her knees, mouth open to receive my cum..."
                        "Was so damn hot!"
                        scene v8_hotel5 with long
                        pause 0.5
                        show v8_hotel5_cum with flash
                        i "Ahhh...!!{w=0.5}{nw}" with vpunch
                        pause 0.5
                        with vpunch
                        pause 0.5
                        with vpunch
                        pause 0.5
                        "Holly flinched when a jet of cum sprayed her face, surprising her."
                        "She had kept her eyes closed the entire time, but her mouth open."
                        "My jizz painted Holly's lips and cheeks, dripping down her chin, as I squeezed the last drops of juice and pleasure with my hand."
                        "She stayed put until I took a step back, satisfied."
                        stop music fadeout 2.0
                        i "Whew..."
                        $ fholly = "flirtshy"
                        $ fian = "shy"
                        scene hotel
                        show hollynude3 at rig
                        show holly_cum1 at rig
                        show iannude at lef
                        with long
                        h "Um... I'll go get cleaned..."
                        i "Oh, sorry, you just took a shower..."
                        h "No, it's okay..."
                        hide hollynude3
                        hide holly_cum1
                        with short
                        $ fian = "blush"
                        "I enjoyed that, but the mood was a bit awkward after the fact."
                        $ fian = "smile"
                        "It felt kinda weird doing something like that to Holly, but she didn't seem disgusted by it."
                        "In fact, I had the impression she was happy that I came."
                        hide iannude
                        show ianunder at lef
                        show hollybra3 at rig
                        with short
                        h "Done."
                        i "Thanks for that, Holly."
                        h "I'm glad you enjoyed it..."
                        $ fholly = "blush"
                        $ renpy.end_replay()
                        $ gallery_unlock_scene("CH08_S01")
                        if persistent.include_disabled:
                            $ config.menu_include_disabled = True
                        $ greyed_out_disabled = False
                        if ian_lena_dating:
                            h "I know I probably shouldn't ask this at this moment, but..."
                            $ fian = "disgusted"
                            h "What happened between us... From now on, what are we...?"
                            "Shit. This was it. The moment to come clean and tell Holly what I should've told her from the get-go."
                            $ fian = "sad"
                            i "Um, Holly... There's something I need to tell you. I was meaning to do it before, but I didn't know if it would be the right thing to do."
                            jump v8hoteltelllena
                        else:
                            h "I know I probably shouldn't ask this at this moment, but... What happened... From now on..."
                            h "How does this, um, change our situation? I mean, you and I..."
                            $ fian = "worried"
                            "I should've expected this. Holly wanted me to clarify what the deal was between us."
                            "I was happy about what happened but, truth be told, I was hoping to get a bit more time to figure out my feelings."
                            jump v8hoteltell

                    "Stop Holly":
                        $ renpy.block_rollback()
                        "After several minutes, I finally gave up. It was clear Holly wasn't gonna make me cum."
                        stop music fadeout 2.0
                        $ fholly = "blush"
                        $ fian = "sad"
                        scene hotel
                        show hollynude3 at rig
                        show iannude at lef
                        with long
                        i "That's enough... It's not working today..."
                        h "I-{w=0.3}I'm sorry...!"
                        $ fian = "smile"
                        i "Don't be. I enjoyed it nevertheless... But I know we don't have much time and I don't want you to be late."
                        i "It's a bit hard to concentrate, you know?"
                        h "I see..."
                        hide iannude
                        hide hollynude3
                        show ianunder at lef
                        show hollybra3 at rig
                        with short
                        i "Let's try again when we have more time."
                        $ fholly = "shy"
                        h "Oh, okay... So... Do you want us to do this again?"
                        $ renpy.end_replay()
                        if persistent.include_disabled:
                            $ config.menu_include_disabled = True
                        $ greyed_out_disabled = False
                        if ian_lena_dating:
                            $ fian = "disgusted"
                            i "Um, I..."
                            "Shit. This was it. The moment to come clean and tell Holly about Lena and me before things got tangled even further."
                            $ fian = "sad"
                            $ fholly = "blush"
                            i "Um, Holly... There's something I need to tell you. I was meaning to do it before, but I didn't know if it would be the right thing to do."
                            jump v8hoteltelllena
                        else:
                            $ fholly = "blush"
                            h "I know I probably shouldn't ask this at this moment, but... What happened... From now on..."
                            h "How does this, um, change our situation? I mean, you and I..."
                            $ fian = "worried"
                            "I should've expected this. Holly wanted me to clarify what the deal was between us."
                            "I was happy about what happened but, truth be told, I was hoping to get a bit more time to figure out my feelings."
                            jump v8hoteltell

            "Refuse":
                $ renpy.block_rollback()
                $ fian = "sad"
                if ian_lena_dating:
                    "Part of me wanted to say yes, but I had to come clean."
                    if ian_wits < 7:
                        call xp_up ('wits') from _call_xp_up_478
                i "Don't worry, you don't need to..."
                $ fholly = "blush"
                if ian_lena_dating:
                    i "In fact, I feel like I should tell you something. Something I probably should've told you yesterday..."
                    hide iannude
                    show ianunder at lef
                    with short
                    "I put on my underpants. I felt weird talking about this while naked..."
                    jump v8hoteltelllena
                else:
                    h "Oh, okay..."
                    h "Was I that bad last night...?"
                    call friend_xp('holly', -1) from _call_friend_xp_640
                    $ fian = "worried"
                    i "What? No, it's nothing like that!"
                    $ fian = "sad"
                    i "It's just... I think it's better if we take it slow..."
                    if ian_wits < 7:
                        call xp_up ('wits') from _call_xp_up_479
                    $ fholly = "sad"
                    h "Oh, of course... Sorry, I didn't mean to..."
                    i "No, no, it's okay. Don't apologize."
                    $ renpy.end_replay()
                    jump v8hoteltell

label v8hoteltelllena:
    i "I don't know how to put it, but... Holly, you should know Lena and I are..."
    i "We have been seeing each other recently."
    h "..."
    $ fholly = "sad"
    h "I knew it..."
    $ fian = "worried"
    i "You did?"
    h "I mean, nobody told me, but it was pretty obvious..."
    if v8_holly_bj:
        call friend_xp('holly', -1) from _call_friend_xp_641
        $ ian_holly = 10
        h "I just wish you had told me before what we just did... I feel kinda stupid right now..."
        i "You're right... I'm sorry..."
    if ian_lena_love:
        i "Lena and I... It's not like we have something serious, but..."
        h "You really like her."
        $ fian = "sad"
        i "Yeah..."
    else:
        $ fian = "sad"
        i "It's not like we're dating or anything like that. We've just been... hooking up."
        i "But I felt I should tell you about it."
    h "I was almost certain that was the case. I've seen you together, how you look at each other..."
    h "But I didn't want to ask. I guess I was afraid my suspicions were actually true... Which they were."
    h "To be frank, when I kissed you... I thought you'd back away and reject me."
    h "But then you kissed me back, and I..."
    h "I was selfish..."
    $ fian = "worried"
    i "No, it was me who was selfish. I should've told you straight away. But I didn't."
    i "I knew I was getting us into trouble, but... I just couldn't refuse the opportunity to be with you."
    $ fholly = "blush"
    h "You're not just saying that to make me feel better about being rejected?"
    if ian_lena_love:
        jump v8chathollyreject
    else:
        menu:
            "{image=icon_love.webp}I'm not rejecting you":
                $ renpy.block_rollback()
                $ ian_holly_dating = True
                $ fian = "sad"
                i "I'm not rejecting you... As I said, Lena and I don't have anything serious..."
                i "But I'm aware of the complicated situation I've put us in..."
                if v5_tell_holly == "wantgf":
                    h "So, that day... When you told me you liked somebody... You weren't talking about Lena?"
                    i "I don't remember. Maybe I was... But I like you too, Holly."
                    h "But when you said \"like\", you didn't mean \"like\" as in... having a serious relationship."
                    i "I guess not. I honestly don't know. I'm not sure I could handle a relationship right now, not because I don't want one, but..."
                if v5_tell_holly == "nogf":
                    h "It would be worse if Lena was your girlfriend. But it seems she's not, right...?"
                    i "As I said, we're just friends with benefits. I'm a single guy."
                    h "I guess you're not looking for a serious relationship, then."
                    i "I guess not. I honestly don't know. I'm not sure I could handle a relationship right now, not because I don't want one, but..."
                if v5_tell_holly == "hategf":
                    h "It would be worse if Lena and you had a serious relationship... But you told me the last thing you want now is a girlfriend."
                    i "Yeah. Honestly, I don't feel prepared for that, after my last breakup."
                i "I'm in a... complex stage of my life at this moment."
                i "There's a lot I still need to figure out, sort out some feelings, heal some wounds..."
                $ fholly = "n"
                h "I understand. Last thing I want to do is pressure you..."
                i "Pressure me? I got myself in this bind... I'm sorry I've dragged you in."
                h "I don't... regret what happened last night. So don't feel bad about it..."
                $ fian = "smile"
                i "I don't regret it, either. I was looking forward to it, in fact... I've liked you for quite some time, Holly."
                $ fholly = "shy"
                h "Really...?"
                i "Yeah. I think you could tell last night, right?"
                $ fian = "n"
                i "Still, I'm sorry for not being completely straightforward about things."
                i "I guess I should talk to Lena, too..."
                $ fholly = "blush"
                h "Would you prefer if we... forgot it ever happened? We don't have to tell her..."
                $ fian = "worried"
                i "No, I can't ask you to do that. It's not fair, and I wouldn't feel right doing it."
                i "I have to own up to my actions... I'll talk to her."
                h "You're right..."
                $ fian = "n"
                i "I'm sorry I've brought you into this mess, Holly."
                h "I have some responsibility, too. Don't feel like it's all on you..."
                i "In any case... I'm sorry, for what it's worth."

            "I'm not making excuses":
                $ renpy.block_rollback()
                label v8chathollyreject:
                    $ fian = "sad"
                i "No, I'm not trying to make excuses, I'm being honest..."
                $ fian = "sad"
                i "I really wanted to be with you like this."
                i "I'm not proud of how I acted, but it was true to how I felt."
                if ian_lena_love:
                    i "This situation has me pretty confused. You're lovely, Holly, but..."
                    $ fholly = "sad"
                    h "But Lena's who you really like."
                    i "I can't say for sure, but... I feel like I'd like to see where that goes."
                    if v5_tell_holly == "wantgf":
                        h "I knew you were not talking about me that day, when you told me you liked someone..."
                        h "I always knew you were talking about Lena."
                        i "I might've blown my chance with her after what happened last night, though..."
                    if v5_tell_holly == "nogf":
                        h "I always thought it was weird you didn't have a girlfriend... I guess it was just a matter of time."
                        i "Lena and I haven't spoken about that yet. I don't even know if I can handle a relationship right now..."
                        i "I'm in a... complex stage of my life right now."
                        i "There's a lot I still need to figure out, sort out some feelings..."
                        i "Anyway, I've probably blown any chance I had with her after what I did last night."
                    if v5_tell_holly == "hategf":
                        h "That time you told me you weren't looking for a girlfriend... But I guess it takes someone like Lena to change that."
                        i "It's true that I wasn't looking for one... I'm still not even sure I can handle a relationship right now."
                        i "There's a lot I still need to figure out, sort out some feelings..."
                        i "Anyway, I've probably blown any chance I had with her after what I did last night."
                    h "Would you prefer if we... forgot it ever happened? We don't have to tell her..."
                    $ fian = "worried"
                    i "No, I can't ask you to do that. It's not fair, and I wouldn't feel right doing it."
                    i "I have to own up to my actions... I'll talk to her."
                    $ fholly = "sad"
                    h "You're right..."
                    i "I'm sorry I've brought you into this mess, Holly."
                    h "I have some responsibility, too. Don't feel like it's all on you..."
                    $ fian = "n"
                    i "In any case... I'm sorry, for what it's worth."
                else:
                    i "This situation has me pretty confused, though. You're lovely, Holly, but..."
                    if v5_tell_holly == "wantgf":
                        h "Don't worry, I get it."
                        h "I knew you were not talking about me that day, when you told me you liked someone..."
                        h "I always knew you were talking about Lena."
                        i "I like her, yeah, but it's not like I'm hoping to become her boyfriend or anything..."
                        i "In fact, I don't think I could be anyone's boyfriend at this moment. I'm in a... complex stage of my life right now."
                        i "There's a lot I still need to figure out, sort out some feelings..."
                    if v5_tell_holly == "nogf":
                        h "I assumed that if you don't have a girlfriend it's because you're not looking for one at the moment."
                        h "So don't worry, I didn't get any ideas..."
                        i "It's true that I'm in a... complex stage of my life right now."
                        i "There's a lot I still need to figure out, sort out some feelings..."
                    if v5_tell_holly == "hategf":
                        h "I didn't forget what you told me that day. I know you're not looking for a girlfriend right now."
                        i "Yeah... There's a lot I still need to figure out, sort out some feelings..."
                    $ fholly = "n"
                    h "I understand. Don't feel like you have to justify yourself..."
                    h "I don't... regret what happened last night. So don't feel bad about it..."
                    i "Still, I'm sorry for not being completely straightforward about things."
                    i "I guess I should talk to Lena, too..."
                    $ fholly = "blush"
                    h "Would you prefer if we... forgot it ever happened? We don't have to tell her..."
                    i "No, I can't ask you to do that. It's not fair, and I wouldn't feel right doing it."
                    i "I have to own up to my actions... I'll talk to her."
                    $ fholly = "sad"
                    h "You're right..."
                    i "I'm sorry I've brought you into this mess, Holly."
                    h "I have some responsibility, too. Don't feel like it's all on you..."
                    $ fian = "n"
                    i "In any case... I'm sorry, for what it's worth."

        h "I should get dressed... They're probably waiting for me."
        i "Oh, yeah, don't be late..."
        hide hollynude3
        hide hollybra3
        hide holly_towel
        with short
        $ fian = "n"
        show ianunder at truecenter with move
        i "..."
        if ian_lena_love:
            i "I'm such an idiot... Now I feel quite awful..."
            i "And I will need to talk to Lena about this. I doubt she'll be thrilled..."
        elif ian_holly_dating:
            i "That was quite awkward... I was worried Holly would get mad, but it seems she didn't."
            i "I'm such an ass...  I should've done things properly from the start."
            i "Now I need to talk to Lena, too. I wonder how she'll react..."
        else:
            i "That was uncomfortable... But at least I got it off my chest."
            i "Now I need to tell Lena about this. I wonder how she'll react..."
        show blackbg
        with long
        jump v8lenastart

label v8hoteltell:
    i "All this is a bit unexpected and... I mean, I had the feeling you liked me, but..."
    $ fian = "n"
    i "I'm just worried I won't manage this situation to your expectations."
    if v5_tell_holly == "wantgf":
        h "My expectations? I... I didn't have any, to be honest."
        h "Though I must admit I was harboring a glimmer of hope when you told me you didn't have a girlfriend and you... liked somebody."
        $ fian = "smile"
        i "I do like you. I think I made that pretty clear last night..."
        $ fian = "sad"
        i "But I'm in a rather... complicated stage in my life."
        i "There's a lot I still need to figure out, sort out some feelings... And I don't know if I can be up to somebody else's expectations..."
        h "As I said, I didn't really have any expectations... What happened is already more than I was hoping for..."
    if v5_tell_holly == "nogf":
        h "I didn't really have any expectations... What happened is already more than I was hoping for..."
        h "I mean, knowing you didn't have a girlfriend gave me the courage to invite you to come here with me, but at the same time, I assumed that if you were single it was because you wanted to be."
        h "I suppose you're not looking for a girlfriend right now..."
        i "I guess not. I honestly don't know. I'm not sure if I could handle a relationship right now, not because I don't want one, but..."
        i "I'm in a rather... complicated stage of my life."
        i "There's a lot I still need to figure out, sort out some feelings..."
        h "I get it, don't feel like you have to justify yourself."
    if v5_tell_holly == "hategf":
        h "Don't worry, I didn't forget what you told me that day. I know you're not looking for a girlfriend right now."
        i "Yeah... There's a lot I still need to figure out, sort out some feelings..."
        h "And about my expectations... Well, I didn't really have any, to be honest."
        h "What happened between us is already more than I was hoping for..."
    h "I'd like to ask one thing, though... Just to know where we're standing right now..."
    h "Was this just a one-time thing, or...?"
    menu:
        "{image=icon_love.webp}It doesn't have to be":
            $ renpy.block_rollback()
            $ ian_holly_dating = True
            $ fian = "smile"
            i "It doesn't have to be... If you're okay with that."
            $ fholly = "shy"
            h "I am..."
            $ fian = "n"
            i "I hope what I told you about my current situation regarding relationships doesn't make you uncomfortable..."
            $ fholly = "blush"
            h "I said I understand, so don't worry... I guess my situation is not that different from yours, after all."
            $ fian = "smile"
            i "Oh, I see. Good to hear."
            h "I should get dressed... They're probably waiting for me."
            i "Oh, yeah, don't be late..."

        "I think it's better this way":
            $ renpy.block_rollback()
            $ fian = "sad"
            i "Yeah... I think it's better this way."
            i "I have the feeling I would only cause trouble for you in this state I'm in right now."
            call friend_xp('holly', -1) from _call_friend_xp_642
            $ ian_holly = 8
            $ fholly = "blush"
            h "I don't think you'd be trouble, but... I respect your choice."
            $ fian = "n"
            i "I hope I didn't mess things up between us..."
            h "No, no... Don't worry..."
            $ fholly = "n"
            h "We'll be fine."
            h "I should get dressed... They're probably waiting for me."
            i "Oh, yeah, don't be late..."

    hide hollynude3
    hide hollybra3
    hide holly_towel
    with short
    $ fian = "n"
    if ian_lena_dating or v8_holly_bj:
        show ianunder at truecenter with move
    else:
        show iannude at truecenter with move
    "I wasn't expecting to have \"the talk\" so soon, but I was glad that we did..."
    if ian_holly_dating:
        $ fian = "smile"
        "I couldn't wait to see how things unfolded between Holly and me."
    else:
        "I didn't want Holly to feel misled by my actions... And I doubted I could give her what she was looking for."
        "At least I had gotten to sleep with her once, and it had been nice, but that was as far as I was intending to take things."
        "Prevention is better than cure, or so they say..."
    show blackbg
    with long
    jump v8lenastart

##LENA ##############################################################################################################################################################################################################################
label v8lenastart:

    $ lena_active = True
    $ ian_active = False
    $ save_name = "Lena: Chapter 8"

    if persistent.include_disabled:
        $ config.menu_include_disabled = True
    $ greyed_out_disabled = False

    show active_lena
    with long
    pause 1.0

    call calendar(_day="Tuesday", _week=1, _month="June") from _call_calendar_70

    $ mike_extras = 0
    $ mike_look = 1
    $ lena_look = 1
    $ emma_look = 1
    $ flena = "smile"
    $ femma = "n"
    play music "music/emmas_theme.mp3" loop
    scene recordstore
    with long
    show lena at rig
    show emma at lef
    with short
    e "That was great!"
    l "You think so?"
    $ femma = "smile"
    e "Yes, you have a great voice! And you're good with the guitar!"
    $ flena = "happy"
    l "Thanks. You're amazing, too. You've caught up really fast with the drums."
    "Emma and I had just finished our first rehearsal, and I had the impression we had done pretty well."
    "Suddenly, I felt a bit more confident about Friday's concert."
    e "I'm giving you very simple beats, but that's alright. Your music is supposed to be the shining star!"
    $ flena = "shy"
    l "Yeah. No pressure, huh?"
    $ femma = "n"
    e "You're still nervous?"
    l "A bit. I'd hoped we'd have more time to practice so we could get it perfect..."
    e "Perfect is the enemy of the good enough."
    menu:
        "I wish I was good enough...":
            $ renpy.block_rollback()
            $ flena = "sad"
            l "I wish I was good enough..."
            e "Oh, come on, don't be like that!"
            e "I think it's more than obvious you already are \"good enough\", more than just that, even."
            e "You should be able to see it too!"
            l "In any case, I'm far from perfect."
            $ femma = "smile"
            e "Good! If you strive to be perfect from the get-go you'll never get anywhere!"

        "Perfection is the enemy of {i}progress{/i}":
            $ renpy.block_rollback()
            $ flena = "n"
            l "Isn't the saying \"perfection is the enemy of {i}progress{/i}\"?"
            if lena_wits < 7:
                call xp_up('wits') from _call_xp_up_480
            e "Maybe. I don't remember."
            $ femma = "smile"
            e "In any case, the meaning's the same! If you strive to be perfect from the get-go you'll never get anywhere!"

        "I don't want to be just \"good enough\"":
            $ renpy.block_rollback()
            $ flena = "serious"
            l "But I don't want to be just \"good enough\". In this world, that's not enough to make the cut."
            if lena_charisma < 7:
                call xp_up('charisma') from _call_xp_up_481
            e "The world can be harsh, yeah... But I think you have everything that's needed to make the cut!"
            $ flena = "n"
            e "However, if you strive to be perfect from the get-go you'll never get anywhere!"
            $ femma = "smile"

    e "Being good enough step after step is all we should aspire to be."
    if lena_emma < 6:
        call friend_xp('emma') from _call_friend_xp_643
        $ lena_emma = 6
    $ flena = "smile"
    l "I guess you're right. Thanks, Emma, both for the encouragement and the help."
    $ femma = "n"
    e "It's my pleasure. I had been itching to play the drums more... It's the best to release the stress!"
    l "You don't look like the kind of girl who gets stressed out easily."
    e "I guess not... But a lot has been going on lately."
    e "A lot of work went into organizing last weekend's protest, and things got pretty rough at the end..."
    $ flena = "worried"
    l "Oh, yeah. I saw it on the local news. Riot police got involved at some point, right?"
    $ femma = "serious"
    e "Yeah, they didn't like we were marching close to rich folks' houses. When they thought we were too close, things got ugly..."
    l "You were there?"
    $ femma = "n"
    e "Yeah, in the front lines. Thankfully nothing serious happened, we just had to run and a couple of friends got smacked with batons. Just bruises."
    hide friend_up
    menu:
        "The situation is worrisome":
            $ renpy.block_rollback()
            l "This whole situation is worrisome... The economic situation has been rather difficult for a few years, but now..."
            $ femma = "sad"
            e "Before it was just the families with really low incomes who felt it, but now it's affecting even the middle-class."
            l "Lots of businesses seem to be struggling, or straight up closing. Like the Van Dyke's café..."
            e "And not only them... I know so many people in that situation. The Fortress and even this record store are having a hard time."
            if lena_emma < 7:
                call friend_xp('emma') from _call_friend_xp_644
                $ lena_emma = 7
            $ femma = "serious"
            e "Ugh, those guys! It gets my blood boiling just thinking about them!"
            $ flena = "n"
            l "Thinking about who?"

        "I try not to get involved in politics":
            $ renpy.block_rollback()
            $ flena = "n"
            l "I guess that's why I try not to get involved in politics. Troublesome and dangerous."
            e "Yeah, I know. But someone has to do something, right? Else they win."
            l "They?"

    $ femma = "serious"
    e "The mobsters who run this city."
    l "Mobsters? I thought Baluart was run by the Mayor... Who turns out to be Perry's father."
    $ femma = "n"
    e "Oh, so you know. Perry doesn't really like to talk about who his father is."
    $ femma = "sad"
    e "Mayor Vermeer is a good man... But sadly, he's not who runs Baluart. I mean, he tries, but..."
    $ femma = "serious"
    e "The ones who are really running things are the cats who have the money. Investors, businessmen, scalpers... Call them however you want."
    e "I prefer the term mobsters."
    $ femma = "n"
    e "Anyway, I don't want to rack your brain with all that ugly stuff. It's not the best topic of conversation!"
    l "It's okay..."
    e "Whatever. I have a question for you!"
    $ flena = "smile"
    l "Ask away."
    e "Maybe it's a stupid one... But I can't really get my head around it."
    e "For someone who's used to posing nude in front of people, you seem way too nervous about this concert!"
    $ flena = "blush"
    l "Oh, that..."
    menu:
        "{image=icon_lust.webp}I have confidence in my looks" if lena_lust > 5:
            $ renpy.block_rollback()
            $ flena = "n"
            l "Modeling is easier for me. I have confidence in my looks, and I know I can give the photographer what he wants."
            $ flena = "sad"
            l "But playing music... It's much more difficult than just being pretty. So much goes into that... And I'm not sure I'm actually good at it."
            e "I think you are. But in any case, if you really want to find out you'll have to expose your art to other people, don't you think so?"
            l "Yeah... But what if they don't like it?"
            e "Well... You do this for yourself, because you love it, or just because you want to get good reactions from others?"
            $ flena = "n"
            l "I get what you mean. I guess that's why I'm giving it a shot..."
            l "I'm nervous, but I want to do it!"
            if lena_charisma < 8:
                call xp_up('charisma') from _call_xp_up_482
            e "And I'm happy to be here to help! You're talented, believe me!"

        "Music is more intimate":
            $ renpy.block_rollback()
            $ flena = "n"
            l "I guess I feel music is more intimate than the sight of nudity."
            l "Taking your clothes off in front of someone indeed shows some level of vulnerability, especially when we're taught to keep our nudity hidden most of the time..."
            l "But to me playing and singing in front of somebody, showing them my music, the songs I wrote..."
            l "It feels like exposing myself to a much higher degree."
            if lena_wits < 8:
                call xp_up('wits') from _call_xp_up_483
            e "I never thought about it that way, but it makes sense..."
            e "However, you're confident about exposing your body, because you know it's beautiful."
            e "You just have to trust that what's inside of you is equally beautiful! It deserves to be exposed, too."
            $ flena = "smile"
            l "Thanks for helping me give this a shot!"

        "I can't explain it":
            $ renpy.block_rollback()
            l "I know it sounds weird, I can't explain it..."
            $ flena = "n"
            l "Maybe it's just because I'm not used to it, as you said. I was nervous the first time I posed in front of a camera..."
            l "But now it feels like just another day in the office."
            $ femma = "smile"
            e "Let's hope the same happens when it comes to playing in front of someone!"

    e "In any case, you shouldn't worry too much about it. You're pretty and talented!"
    e "I can see why Ian likes you."
    if ian_lena_dating:
        if lena_ian_love:
            $ flena = "shy"
            l "I like him too..."
            $ femma = "smile"
            e "Oh, yeah? Like, really really like him?"
            l "Well, I..."
            $ flena = "smile"
            l "He seems like a genuinely great guy. The first one I've met in such a long time."
            l "But I'm not in a spot in my life where I want to rush things. I'd like to take things step by step."
            l "And I don't know what Ian really wants, either."
        elif lena_ian_mad:
            $ flena = "serious"
            l "I'm still mad about what he did..."
            $ femma = "sad"
            e "What happened?"
            l "Didn't he tell you? He got into a fight in my last life drawing event..."
            $ femma = "surprise"
            e "Really?"
            "I told Emma about Ian's confrontation with Robert, and how troublesome and embarrassing it was for me."
            $ flena = "n"
            $ femma = "sad"
            e "Sheesh... I never imagined Ian to be the kind of guy who gets into fights."
            $ femma = "n"
            e "Either he really hates that guy, or he really likes you!"
            l "I can't really say. I know he likes me, of course, but..."
        else:
            $ flena = "happy"
            l "Oh, he does?"
            e "Of course! You don't feel like he does?"
            $ flena = "n"
            l "I do, yeah... But I don't know to what extent."
        l "He never told me about this, but I feel he's quite tentative about getting close to someone emotionally..."
    else:
        $ flena = "n"
        l " Really? Ian never told me that he likes me."
        e "I'd say so. Isn't it pretty clear?"
        l "I don't know. I feel like he is quite tentative about getting close to someone emotionally."
    $ femma = "sad"
    e "Oh, yeah... You're the perceptive type, I see."
    e "You're not wrong. Breaking up with his ex was really tough for Ian."
    $ flena = "sad"
    e "I wasn't around too much when that happened, but I saw how it affected him. In fact, it's just recently that I've been seeing him in a livelier mood."
    l "What happened?"
    e "He was dating this girl, Gillian, who had so much in common with him. I always thought she was really lovely, and a perfect match for Ian..."
    e "But then it turned out she was cheating on him."
    "A familiar sensation stung my stomach. The gut-wrenching reality of treason and the feeling of worthlessness."
    l "I can relate to that..."
    l "For how long was Ian with this girl?"
    e "About four years, if I'm not mistaken."
    l "That's quite a lot..."
    e "He doesn't like to talk about it and I never asked him more than what he was willing to tell, so I don't really know what really went down."
    e "Maybe he'll open up to you..."
    if ian_lena_dating:
        if lena_ian_love:
            l "I hope he does, at some point..."
        else:
            l "I don't want to pry... But if he decides to tell me, I'll gladly listen."
    else:
        l "I don't want to pry. I'll do as you did, and let Ian tell me about it if he wishes."
    $ femma = "n"
    if lena_job_restaurant > 0:
        $ flena = "surprise"
        l "Oh, look at the time!"
        $ flena = "sad"
        l "Sorry, I need to go or I'll be late at the restaurant."
        e "You work so hard!"
        $ flena = "n"
        l "I have to pay the bills..."
    else:
        e "Oh, I need to get going. I have a meeting tonight."
        l "Of course! Sorry for taking up your time."
        $ femma = "smile"
        e "Hey, I said it's my pleasure!"
    e "See you on Thursday to rehearse one last time before the concert?"
    $ flena = "smile"
    l "Sure. Thanks again, Emma."
    if lena_job_restaurant > 0:
        jump  v8restaurant
    else:
        jump v8lenatuesdaynight

## LENA RESTAURANT #############################################################################################################################################################################################################
label v8restaurant:
    stop music fadeout 2.0
    scene streetnight with long
    $ lena_look = 2
    $ flena = "n"
    $ frobert = "n"
    $ robert_look = 2
    $ robert_hurt = 0
    play music "music/flirty.mp3" loop
    scene restaurant with long
    show lenawork with short
    "Working just three nights a week at the restaurant made this job a lot more bearable."
    "The pay was not nearly enough, of course, but at least I wasn't killing myself serving tables night after night."
    show lenawork at rig with move
    if lena_robert_dating:
        show robert at lef3 with short
        "And my relationship with Robert made things more fun. At least I could work with someone I liked..."
        $ flena = "flirtshy"
        if v4_robert_public:
            "We even got naughty at the workplace once, when I blew him in the service room."
            "Maybe it was a thrill worth repeating..."
        else:
            "He even asked me to sneak with him into the service room, once. I declined, but maybe it could be fun, after all..."
            "I had never gotten naughty at the workplace, yet."
        show robert at lef with move
        $ flena = "smile"
        r "Hey baby... How was your rehearsal this afternoon?"
        l "Seems I won't suck as much as I fear, or so says Emma."
        r "I wish I could see you play..."
        $ flena = "sad"
        l "Some other time. Ian and his friends will be there and I don't want another fight..."
        if v7_fight == "n":
            r "But we didn't fight...!"
            $ flena = "serious"
            l "No thanks to you!"
        r "Okay, okay..."
        $ flena = "n"
        r "By the way, can you stay and help me close up shop today?"
        menu:
            "{image=icon_lust.webp}I was thinking the same" if lena_lust > 5 or v4_robert_public:
                $ renpy.block_rollback()
                $ v8_robert_public = True
                r "I want to tell you something..."
                $ flena = "flirt"
                l "You know, I was thinking about..."
                "I playfully ran my finger down Robert's chest."
                l "... getting some one-on-one time with you once the service ends and it's just the two of us here..."
                $ frobert = "smile"
                r "That... That sounds awesome..."
                if v4_robert_public:
                    $ flena = "slutshy"
                    l "I really enjoyed sucking your cock in the service room last time... It was so exciting..."
                    l "I wouldn't mind getting some more of that."
                else:
                    r "Last time you weren't up for it, though..."
                    l "Well, I am now. You've been a good boy, so you deserve a prize..."
                $ frobert = "flirt"
                r "Damn, Lena... You're getting me hard as a rock down there..."
                l "Then it's better if I stop... for now, at least."
                $ flena = "smile"
                l "Let's get this service over with first."
                r "Yes, ma'am!"

            "Sure":
                $ renpy.block_rollback()
                l "Sure, I'll help."
                r "Good. I need to talk to you..."
                $ flena = "sad"
                l "About what?"
                r "I'll tell you later. Look, the first clients just arrived, go get them seated."
                $ flena = "worried"
                l "Sure..."

            "Do I have a choice?":
                $ renpy.block_rollback()
                l "Do I have a choice?"
                r "Um, I guess you do, but I need to talk to you."
                $ flena = "serious"
                l "Again with that? Why don't you just spit it out?"
                r "After we're done with tonight's service. The first clients just arrived, go get them seated."
                $ flena = "worried"
                l "Sure..."

    else:
        $ frobert = "sad"
        show robert at lef3 with short
        $ flena = "serious"
        "Having Robert around was still a pain in the ass, though. Especially since what happened last week."
        if lena_robert_over2:
            "After the pitiful show he put on at the life drawing session I knew I was done with him."
            "He was needy and pushy, and I didn't need any of that in my life."
            "I thought I could have fun with him, but he had ruined it himself."
        else:
            "He had been keeping his distance, so I thought he was over me, but he showed the exact opposite was the case when he came begging to the life drawing session."
            "It had been a mistake hooking up with him in the first place. I should've seen this coming."
        show robert at lef with move
        r "Hey, Lena..."
        l "What?"
        r "Can you stay and help me close up shop today? I need to talk to you."
        l "Again? I made things as clear as they can be, and I haven't changed my opinion..."
        $ frobert = "n"
        r "It's not about that."
        l "Then why don't you just spit it out?"
        r "After we're done with tonight's service. The first clients just arrived, go get them seated."
        $ flena = "worried"
        l "Sure..."
    hide robert with short
    show lenawork at truecenter with move
    if v8_robert_public:
        l "Seems like tonight's work will end with something nice..."
        l "They should pay me more for motivating the staff!"
        "I was looking forward to satisfying my sexual urges, too... But right now I had to focus on work."
    else:
        l "What's up with him and cliffhangers? He loves to do that..."
        "I'll have to wait a few hours to learn what was in Robert's mind. Right now I had to focus on work."
    hide lenawork with short
    "The night went on, as usual, taking orders from picky clients, carrying dishes around, and trying to keep the high standards asked of the staff."
    "After dispatching the last customer and tidying up, only Robert and I were left in the restaurant."
    stop music fadeout 2.0
    if v8_robert_public:
        $ flena = "slutshy"
    else:
        $ flena = "n"

    label gallery_CH08_S02:
        if _in_replay:
            call setup_CH08_S02 from _call_setup_CH08_S02


    show lenawork at rig
    show robert at lef
    with short
## ROBERT SEX
    if v8_robert_public:
        l "So, here we are... Alone, just the two of us, just like you wanted..."
        r "I love being alone with you... Especially when you get all naughty..."
        l "All naughty, just like what I'm about to get?"
        play music "music/sex.mp3" loop
        scene v4_restaurant1 with long
        if v4_robert_public:
            "Just like I had done that other time, I got down to my knees at the same time I undid Robert's pants."
        else:
            "I got down to my knees at the same time I undid Robert's pants."
        "His erect cock sprung out immediately, happy to see me."
        "It always turned me on seeing how quickly Robert's body reacted to me. How strongly he desired me."
        "I showed him my appreciation by sliding my tongue up the shaft, impregnating it with warm saliva."
        r "Oh fuck baby, yeah."
        "Robert held onto one of the tables and leaned back, searching for comfort."
        "He wanted to thoroughly enjoy the work of my playful tongue, and I was gonna make sure he did."
        scene v4_restaurant2 with long
        play sound "sfx/bj1.mp3"
        "I ran my mouth down Robert's shaft until my lips reached his balls."
        "I began licking them, kissing and sucking while my hand continued to stroke his hard cock."
        r "Oh, baby...! Your blowjobs drive me insane, you're so good!"
        "Just the kind of thing I liked to hear... I pampered Robert as a reward, delightfully slurping on his balls and cock."
        "He twitched with every twist of my tongue and the suction of my lips, trembling in pleasure."
        "He was disarmed before the pleasures I was giving him, and the way he panted and moaned showed it clearly."
        "It turned me on even more...!"
        scene v8_robert3 with long
        "I stood up and pulled my own pants down, bending forward in front of Robert."
        l "Fuck me, Robert. Fuck me right now!"
        r "Damn, baby...!"
        scene v8_robert3b with short
        play sound "sfx/ah2.mp3"
        l "Nhhh...!"
        "Robert didn't make me wait. He got behind me and pushed his cock into my pussy."
        "As always, it slid right in, eased in by my moisture."
        l "Mhhh, you're so hard!"
        "He began giving it to me from behind fast and hard from the start. It suited me just fine."
        "His hard tool scraped my insides, making me moan in pleasure..."
        "The situation turned me on so much... Being fucked in that room, usually filled by customers..."
        "If they were still here to see what naughty little me did in the same place they had dined...!"
        r "Shit, baby, I'm cumming! I can't hold it in...!"
        with flash
        r "Ahhhh!!!" with vpunch
        "Climax overcame Robert, who released a loud groan as he trembled."
        l "Don't stop! Keep fucking me!"
        show v8_robert3_cum with short
        "He continued to pump his hips while the orgasm ravaged his body."
        l "Keep going! Yes! Please don't stop!"
        "I could feel his cum spurting out of my pussy while Robert thrust, dripping down my thighs and staining my jeans."
        "And I could also feel his cock starting to shrivel..."
        "No, I was so close! I had to make sure that thing stayed in shape until then!"
        l "Fuck yes, Robert! I can feel your cum filling me up...!"
        l "Push it deep inside me! Keep fucking me with that incredible cock!"
        r "Fuck yes...!"
        "I felt his junk hardening again. Just what I needed...!"
        play sound "sfx/oh1.mp3"
        l "Ahhnnnn!!" with vpunch
        pause 0.6
        with vpunch
        pause 0.6
        with vpunch
        pause 0.6
        play sound "sfx/ah1.mp3"
        l "Ahhh..."
        "Just in time. Robert's cock finally deflated inside of me before pulling out."
        stop music fadeout 2.0
        scene restaurant with long
        "I wiped my crotch with a napkin and pulled my uniform back together."
        show lenawork at rig
        show robert at lef
        with long
        r "Wow, baby, you were so hot just now... You keep surprising me..."
        r "I love it."
        l "You were good, too..."
        $ flena = "smile"
        l "So, you also said you wanted to tell me something?"
        $ frobert = "sad"
        r "Oh, yeah..."
        r "Um, you see... The manager asked me to tell you that they're letting you go..."
        $ renpy.end_replay()
        $ gallery_unlock_scene("CH08_S02")

## NO SEX
    else:
        l "So, what is that mysterious thing you wanted to tell me?"
        r "Yeah, well... I know you're not gonna like this, but..."
        r "The manager asked me to tell you that they're letting you go."

    $ flena = "surprise"
    l "What?"
    r "They are rescinding your contract."
    $ flena = "mad"
    l "What the hell!? I thought this matter was settled!"
    l "They fired Samantha and cut my hours in half. And now they're firing me? What the hell is going on?"
    r "I have no idea... I learned about this just yesterday. And I had to be the one to break the news to you..."
    if v8_robert_public:
        l "You could've told me sooner."
        r "I was going to, but then you got frisky and I didn't want to ruin the mood..."
        $ flena = "worried"
        l "Whatever... Fuck, what now?"
    elif lena_robert_dating:
        l "I'm screwed..."
    else:
        $ flena = "serious"
        l "Is this your doing? A way to get back at me?"
        r "What? No! I had nothing to do with this!"
        r "In fact, I argued against it! You know I vouched for you the first time..."
    $ flena = "worried"
    if cafe_help:
        l "I'm fighting not to lose my job at the café and now I'm losing this one..."
    else:
        l "I'm sure to lose my job at the café, and at least I thought I could count on some money from this job..."
    l "Why the hell are they letting me go? It doesn't make any sense."
    l "I've been working diligently and without me, you'll be short-staffed. They'll need to hire another part-timer..."
    r "I don't get it either. As far as I know, there was no plan to reduce the staff any further. At least I didn't hear anybody talk about it."
    r "I have the feeling this is something that comes directly from the higher-ups..."
    l "You mean someone's targeting me specifically?"
    r "Could be... But I have no clue why that would be the case."
    if lena_job_seymour:
        if v6_axel_pose == 1:
            "It had to be him. That was the only explanation."
            "Seymour had moved the needle so they'd kick me out... But why?"
            "We had a falling out thanks to Axel and that creepy lady, but was he really this petty?"
            "Was this some kind of vengeance? If that was the case..."
            l "I can't believe this..."
        else:
            $ flena = "worried"
            "One of the higher-ups who was targeting me? There was just one possibility: Seymour."
            "But it didn't make sense. We were on good terms. We were working together."
            "In fact, I could contact him and ask him to overturn the manager's decision. He was the owner of this place or something like that, right?"
            $ flena = "serious"
            l "I'll make a call tomorrow... I want to learn what's going on here."
    else:
        $ flena = "serious"
        l "I do."
        "It had to be him. That was the only explanation."
        "Seymour had moved the needle so they'd kick me out... But why?"
        "Was this some kind of vengeance for me turning him down? Was he really this petty?"
        l "I can't believe this..."
    r "I'm sorry, Lena. If I could do something I would, but it's out of my hands..."
    r "I'm afraid this time I can't do anything to help you."
    $ flena = "sad"
    l "I know. It seems this was not meant to last..."
    l "Anyway, thank you for vouching for me the first time. At least I earned some extra money."
    l "I'm gonna go home... I need to think about what to do next."
    r "If I can do anything to help..."
    if lena_robert_dating:
        l "I know. Thanks, Robert."
    else:
        l "Don't worry. You've already done enough."
    scene streetnight with long
    pause 1
    play music "music/normal_day2.mp3" loop
    play sound "sfx/door_home.mp3"
    scene lenaroomnight with long
    $ flena = "sad"
    show lenabra with short
    "The first thing I did when I got home was take off my clothes and fall straight into my bed. I felt so tired..."
    "And not just because of the long day. I was mentally exhausted."
    "These past two months had been nothing but trouble after trouble both with the restaurant and the café."
    "Was it so much to ask to have some stability in my life? Why couldn't I even keep a simple job?"
    show lenabra at rig with move
    play sound "sfx/meow.mp3"
    show lola at lef with short
    "Lola jumped on the bed, looking to be petted. I scratched her forehead."
    play sound "sfx/purr.mp3"
    hide lola
    show lolahappy at lef
    with short
    l "Hey baby... Sometimes seems life is conspiring against me..."
    $ flena = "serious"
    "I wonder if Seymour has really something to do with them suddenly firing me from the restaurant..."
    if lena_job_seymour and v6_axel_pose != 1:
        $ flena = "sad"
        l "If that's the case, I can't imagine why."
    else:
        l "I wouldn't be surprised if that's the case..."
    jump v8lenatuesdaynight2

## LENA TUESDAY NIGHT #############################################################################################################################################################################################################
label v8lenatuesdaynight:

    stop music fadeout 2.0
    $ flena = "n"
    scene lenaroomnight with long
    play sound "sfx/door_home.mp3"
    show lena with short
    play music "music/normal_day2.mp3" loop
    "When I got home it was already late, so I just heated some leftovers in the microwave and went straight to my room."
    hide lena with short
    $ lena_look = 2
    "After that quick bite, I took off my clothes and fell into my bed. I was feeling exhausted."
    show lenabra with short
    l "It's been a long day... I was hoping not having to work night shifts would make things easier, but I'm tired all the same."
    l "Things have been rather stressful lately..."
    show lenabra at rig with move
    play sound "sfx/meow.mp3"
    show lola at lef with short
    "Lola jumped on the bed, looking to be petted. I scratched her forehead."
    play sound "sfx/purr.mp3"
    hide lola
    show lolahappy at lef
    with short
    l "Hey baby..."

label v8lenatuesdaynight2:
    l "I wish I could afford to focus solely on Friday's concert, but problems like to pile up."
    if v7_holly_kiss:
        $ flena = "worried"
        l "And I wonder what's up with Holly... It's been difficult to keep in touch with her these past few days."
        if holly_gym:
            "She hadn't shown up at the gym or the café, and she didn't really seem to be available through the phone, either."
        else:
            "She hadn't shown up by the café and she didn't really seem to be available through the phone, either."
        "I looked at our last text conversation, this last Sunday."
        nvl clear
        l_p "{i}Hey Holly! How was the book fair? Did you have fun? {image=emoji_smile.webp} {/i}"
        h_p "{i}Yeah, it was fun. Just the usual haha {image=emoji_ups.webp}{/i}"
        l_p "{i}How many books did you sign?{/i}"
        l "..."
        l "She never returned my message..."
        $ flena = "n"
        l "Maybe she's been busy with her new book. She told me she tends to get lost in her writing, after all."
        if ian_lena_dating:
            $ flena = "smile"
            l "Sounds like Ian. Maybe all writers are like that..."
    if ian_lena_dating:
        $ flena = "n"
        if v7_holly_kiss:
            l "I haven't seen him either since our last date..."
        else:
            l "I could use some comfort, but I haven't seen Ian yet since our last date..."
        if lena_ian_mad:
            $ flena = "serious"
            l "Quite a disastrous one, by the way. Guys can be so hot-headed sometimes..."
            $ flena = "sad"
            "I was still stingy about him causing that scene with Robert... But it wasn't entirely his fault."
            l "Maybe I overreacted and blamed too much of it on him..."
        elif lena_ian_love:
            $ flena = "smile"
            l "I'm eager to..."
            if ian_lena_sex_late:
                "We had finally slept together... I had been hesitant about it, but it had been worth it."
                "Releasing our pent-up desire had felt great. I was looking forward to repeating that..."
            elif ian_lena_sex:
                "Our sex last time was awesome... Each time kept getting better and better."
                "We just clicked so well together, it was close to perfect."
            else:
                "I really liked him, but we hadn't slept together yet..."
                $ flena = "shy"
                "I could feel he wanted it, and so did I, but a part of me still wanted to hold back... even if I didn't really understand why."
        else:
            if ian_lena_sex_late:
                "We had finally slept together... I had been hesitant about it at first, but I had liked it quite a lot."
                "I was looking forward to repeating that..."
            elif ian_lena_sex:
                "Our sex last time was great, and it seemed to be getting better."
                "It seemed we clicked really well together."
            else:
                "We hadn't slept together yet, and I wondered if we should."
                $ flena = "shy"
                "I could feel he wanted it, and so did I, but a part of me still wanted to hold back... even if I didn't really understand why."

    $ flena = "sad"
    "I rolled over on my bed."
    l "Ahhh... I'm tired, but I can't fall asleep..."
    l "I'm tense. But what did I expect, all things considered?"
    "I looked at Lola, peacefully purring under my petting hand."
    $ flena = "smile"
    l "I wish I knew how to relax like you. You look like you're in heaven right now."
    play sound "sfx/purr.mp3"
    "Lola's response was to purr even louder."
    $ flena = "n"
    "Seeing as I wouldn't be able to imitate my cat, I pulled up my phone, trying to distract myself from my anxiety."
## STALKFAP  #################
    if stalkfap_pro == 0 and stalkfap:
        hide lolahappy
        hide lenabra
        show lenabra2 at rig
        with short
        "I opened the Stalkfap app. I wanted to see if my income had increased."
        $ flena = "sad"
        show lenabra2 at right with move
        show v3_stalkfap2b with short
        l "Not really... I have a few subscribers, but the amount of money I'm getting is negligible..."
        l "I wonder if I made the right choice limiting myself to posting my pro shoots. Maybe I could post a few selfies, as Ivy said..."
        $ flena = "n"
        l "Tasteful ones, of course."
        l "Let's do that now, in fact."
        hide lenabra2
        show lenanude2 at right
        with short
        "I took off my underwear and posed in front of the mirror."
        play sound "sfx/camera.mp3"
        hide v3_stalkfap2b
        show v5_stalkfap1_comp
        with flash
        $ lena_lena_pics.append("v5_stalkfap1_comp")
        l "I think this is a good compromise. Let's see if it gets people a bit more interested..."
        hide v5_stalkfap1_comp with short
        show lenanude2 at truecenter with move
        "I closed the app, but I was still not sleepy."

    elif stalkfap_pro > 0:
        hide lolahappy
        hide lenabra
        show lenabra2 at rig
        with short
        "I opened the Stalkfap app. I wanted to see if my income had increased."
        show lenabra2 at right with move
        show v8_stalkfap1_comp with short
        $ flena = "sad"
        if v7_dance == 2:
            l "Just a bit... Even though I uploaded that dancing video a few days ago..."
            l "Well, at least it's something."
        else:
            l "Barely... Even though I uploaded that dancing video a few days ago..."
        $ flena = "n"
        l "Ivy said it's important to have a good relationship with my paying followers. That I should try and reply to their comments and messages..."
        menu:
            "Check out the comments and messages":
                $ renpy.block_rollback()
                $ v8_stalkfap_comments = True
                l "Let's see the comments..."
                # NUDE
                if v7_dance == 2:
                    if v7_dance_provoke > 0:
                        hide v8_stalkfap1_comp
                        show v8_stalkfap2_comp # boobs
                        with short
                        "{i}\"The way you play with your boobs is so sexy. I wish those were my hands instead of yours!\"{/i}"
                        "{i}\"You are so beautiful and sexy {image=emoji_love.webp} My Goddess.\"{/i}"
                        "{i}\"You are absolutely delicious.\"{/i}"
                        if v7_dance_provoke == 2:
                            hide v8_stalkfap2_comp
                            show v8_stalkfap3 # twerk panties
                            with short
                            "{i}\"Yeah, shake that incredible ass for us, babe!\"{/i}"
                            "{i}\"I never knew you were the kind who liked to twerk, and I love that's the case. More, please {image=emoji_ass.webp}{image=emoji_cum.webp}\"{/i}"
                            "{i}\"I thought about unsubscribing but THIS is bringing me right back {image=emoji_fire.webp}\"{/i}"
                        if v7_dance_provoke == 3:
                            hide v8_stalkfap2_comp
                            show v8_stalkfap4 # twerk nude
                            with short
                            "{i}\"Holy Mother of God, that was hot as fuck! What a nice pussy you've got {image=emoji_cum.webp}{image=emoji_cum.webp}\"{/i}"
                            "{i}\"I thought about unsubscribing but THIS is bringing me right back {image=emoji_fire.webp}\"{/i}"
                            "{i}\"That dance was really slutty, but surely a big dildo or even a partner could improve it\"{/i}"
                        hide v8_stalkfap3
                        hide v8_stalkfap4
                        show v8_stalkfap5_comp # frontal nude
                        with short
                        "{i}\"God damn, what a delicious body. The things I would do to you if you let me...\"{/i}"
                        "{i}\"Jesus, that was hot as fuck! You drive me crazy, baby! I can't wait for more.\"{/i}"
                        $ flena = "smile"
                        l "Seems like that video was a hit! All the comments are very positive... and horny."
                    if v7_dance_provoke == 0:
                        hide v8_stalkfap1_comp
                        show v8_stalkfap5_comp # frontal nude
                        with short
                        "{i}\"God damn, what a delicious body. The things I would do to you if you let me...\"{/i}"
                        "{i}\"You're super sexy, but you should improve your dancing. Make it more spicy for us, please!\"{/i}"
                        "{i}\"Great to see how you're gaining confidence. Baby steps though... I can't wait for more.\"{/i}"
                        $ flena = "smile"
                        l "Seems they really liked the video! Most of the comments are very positive... and horny."
                    l "Some of these guys are out of control... But as long as they pay..."
                # TOPLESS
                if v7_dance == 1:
                    $ flena = "n"
                    if v7_dance_provoke > 0:
                        hide v8_stalkfap1_comp
                        show v8_stalkfap2_comp # boobs
                        with short
                        "{i}\"The way you play with your boobs is so sexy. I wish those were my hands instead of yours!\"{/i}"
                        "{i}\"You are so beautiful and sexy {image=emoji_love.webp} My Goddess.\"{/i}"
                        "{i}\"You are absolutely delicious.\"{/i}"
                        if v7_dance_provoke == 2:
                            hide v8_stalkfap2_comp
                            show v8_stalkfap3 # twerk panties
                            with short
                            "{i}\"Yeah, shake that incredible ass for us, babe!\"{/i}"
                            "{i}\"I never knew you were the kind who liked to twerk, and I love that's the case. More, please {image=emoji_ass.webp}{image=emoji_cum.webp}\"{/i}"
                            "{i}\"I thought about unsubscribing but THIS is bringing me right back {image=emoji_fire.webp}\"{/i}"
                            hide v8_stalkfap3
                            show v8_stalkfap5_comp # frontal topless
                            with short
                        else:
                            hide v8_stalkfap2_comp
                            show v8_stalkfap5_comp # frontal topless
                            "{i}\"Great to see how you're gaining confidence. Baby steps though...\"{/i}"
                            "{i}\"I can't get enough of you. Keep up the great content!\"{/i}"
                        if v7_dance_provoke == 2:
                            l "Seems most people liked my video. Especially the twerking part... I'm glad I took a page out of Ivy's book."
                        else:
                            l "Seems most people liked my video. That's good..."
                        $ flena = "sad"
                        l "But I'm still not making too much money out of this Stalkfap thing. Maybe if I keep posting stuff like this..."
                        $ flena = "n"
                    else:
                        hide v8_stalkfap1_comp
                        show v8_stalkfap5_comp # frontal topless
                        "{i}\"That was nice, but next time lose the panties too! We wanna see everything!\"{/i}"
                        "{i}\"You're one of the “only teasing” ones, huh? Not interested in you just milking your fans.\"{/i}"
                        "{i}\"You are so beautiful and sexy {image=emoji_love.webp} My Goddess.\"{/i}"
                        "{i}\"I love your tits.\"{/i}"
                        "{i}\"This is not what I signed up for. Unsubscribed.\"{/i}"
                        $ flena = "sad"
                        l "What's up with all those negative comments? I thought they would like my little dance..."
                        l "Seems like these guys were expecting some more explicit material. They are harder to please than I thought..."
                        $ flena = "n"
                        l "Thankfully I still have some unconditional fans."
                # BRA
                if v7_dance == 0:
                    hide v8_stalkfap1_comp
                    show v8_stalkfap5_comp # frontal bra
                    with short
                    "{i}\"You're not even taking your bra off? I can't understand why you'd want to upload this video.\"{/i}"
                    "{i}\"I already knew I loved the way you look, but now I love the way you move too!\"{/i}"
                    "{i}\"You're one of the “only teasing” ones, huh? Not interested in you just milking your fans.\"{/i}"
                    "{i}\"This is incredibly lame. Unsubscribed.\"{/i}"
                    $ flena = "worried"
                    l "What the hell...? So many negative comments...!"
                    $ flena = "serious"
                    l "I already knew they wouldn't go crazy over this, but these guys are the worst."
                    l "They just want to see girls shamelessly exposing themselves..."
                    $ flena = "sad"
                    l "It's obvious this isn't nearly shameless enough."
                    l "Thankfully I still have a few unconditional fans..."
                $ flena = "n"
                if lena_lust > 6:
                    l "I also have a few direct messages. Let's see what they say."
                elif lena_lust > 4:
                    l "I also have a few direct messages. I have the impression they won't be exactly polite..."
                    l "Let's see..."
                else:
                    l "I also have a few direct messages. I'm afraid to even take a look at them..."
                    l "Here goes nothing."
                scene lenaroomnight
                show lenabra2 at right
                with short
                show lenabra2 at truecenter with move
                "I had several unread notifications. I opened the first one."
                "{i}\"I've been following you for a while and the content keeps getting better and better. I really enjoy seeing your beautiful posts.\"{/i}"
                menu:
                    "Thank you!":
                        $ renpy.block_rollback()
                        $ v8_stalkfap_dm1 = 1
                        $ flena = "smile"
                        l "This one seems actually nice and respectful. He deserves a reply."
                        l "{i}Thank you! I will keep posting stuff, so I hope you'll keep supporting me {image=emoji_heart.webp}{/i}"

                    "Ignore him":
                        $ renpy.block_rollback()
                        $ flena = "worried"
                        l "This makes me cringe for some reason. I'll ignore him..."

                $ flena = "n"
                l "Let's see, what else...?"
                "{i}\"Hey @bluenightsky, have you ever done some personal requests? Would you be interested in doing custom videos? I'd pay a premium.\"{/i}"
                menu:
                    "{image=icon_lust.webp}I'm open to that" if lena_lust > 4 or lena_passion == "money":
                        $ renpy.block_rollback()
                        $ v8_stalkfap_dm2 = 1
                        l "That's what Ivy told me about..."
                        l "{i}I've never done that before, but I'm open to it. What do you have in mind?{/i}"
                        l "It's late, so I guess I'm not getting a response tonight. Let's wait and see what he's willing to offer..."

                    "I don't do that":
                        $ renpy.block_rollback()
                        l "Nope, not interested."
                        l "{i}Sorry, I don't do that kind of thing. I hope you can enjoy what I post, though!{/i}"

                l "On to the next one..."
                if cafe_nude:
                    "{i}\"That dance was even hotter than your nude modeling the other day at the café.\"{/i}"
                else:
                    "{i}\"That dance was even hotter than your nude modeling the other day at the gallery.\"{/i}"
                $ flena = "worried"
                l "What the hell...? Do I have a stalker?"
                l "This feels way too creepy... I'm definitely not responding to this one."
                l "I don't even want to know who this is."
                $ flena = "n"
                "{i}\"Are you my pinky toe? Because I'd like to bang you on all my furniture!\"{/i}"
                menu:
                    "That's funny!":
                        $ renpy.block_rollback()
                        $ flena = "smile"
                        l "I guess he deserves a reply."
                        l "{i}Ha ha, that's funny {image=emoji_laugh.webp}{/i}"
                        "Surprisingly, he responded almost immediately... with another bad joke."
                        guy "{i}Do you use an inhaler? Because you got assssss, ma{/i}"
                        menu:
                            "Respond":
                                $ renpy.block_rollback()
                                l "{i}I've never used an inhaler, but thanks for the compliment {image=emoji_laugh.webp}{/i}"
                                guy "{i}Let's have breakfast together tomorrow; shall I call you or nudge you?{/i}"
                                menu:
                                    "Respond":
                                        $ renpy.block_rollback()
                                        $ flena = "happy"
                                        l "{image=emoji_laugh.webp} {image=emoji_laugh.webp} {image=emoji_laugh.webp}"
                                        guy "{i}Baby, are you a lion? Because I can see you lion in my bed tonight.{/i}"
                                        menu:
                                            "Respond":
                                                $ renpy.block_rollback()
                                                l "{i}Don't you run out of jokes? {image=emoji_laugh.webp}{/i}"
                                                guy "{i}I'd hide every chair in the world just so you'd have to sit on my face.{/i}"
                                                menu:
                                                    "Respond":
                                                        $ renpy.block_rollback()
                                                        l "{i}Have you tried becoming a stand-up comedian?{/i}"
                                                        guy "{i}Do you work at build-a-bear? Cuz I'd stuff you!{/i}"
                                                        menu:
                                                            "Continue":
                                                                $ renpy.block_rollback()
                                                                $ flena = "smile"
                                                                l "How many stupid puns can one guy have?"
                                                                guy "{i}I'm not a dentist, but I bet I could give you a filling {image=emoji_cum.webp}{/i}"
                                                                $ flena = "n"
                                                                l "An infinite number, apparently..."
                                                                l "Okay, enough of this. I've gone deep enough into this rabbit hole."
                                                                if lena_wits < 6:
                                                                    call xp_up ('wits') from _call_xp_up_484

                                                            "That's enough":
                                                                $ renpy.block_rollback()
                                                                jump v8stopjoke

                                                    "That's enough":
                                                        $ renpy.block_rollback()
                                                        jump v8stopjoke


                                            "That's enough":
                                                $ renpy.block_rollback()
                                                jump v8stopjoke

                                    "That's enough":
                                        $ renpy.block_rollback()
                                        jump v8stopjoke

                            "That's enough":
                                $ renpy.block_rollback()
                                label v8stopjoke:
                                    $ flena = "n"
                                l "Okay, this one was really bad. Enough of this."

                    "Block this clown":
                        $ renpy.block_rollback()
                        $ flena = "worried"
                        l "Ugh, I don't have time for these stupid pick-up lines."
                        $ flena = "n"
                        l "I'll block this clown."

            "Forget it":
                $ renpy.block_rollback()
                $ flena = "sad"
                l "Nah... I don't wanna read what those horny guys post."
                l "It's the same every time. Tasteless comments and unwanted critique. Not what I need right now."

        hide v8_stalkfap1_comp with short
        if v8_stalkfap_comments == False:
            show lenabra2 at truecenter with move
        hide lenabra2
        show lenabra
        with short
        "I closed the app, but I was still not sleepy."
        $ flena = "n"

## IVY PEOPLEGRAM
    "I decided to browse Peoplegram for a bit."
    l "..."
    "I scrolled through some meaningless pictures of cute animals and people I didn't really care about until I stumbled on Ivy's last picture."
    $ flena = "worried"
    if stalkfap and stalkfap_pro == 0:
        show lenanude2 at right with move
    else:
        show lenabra at right with move
    hide lolahappy
    show v8_ivy_pg1
    with short
    pause 1
    $ lena_ivy_pics.append("v8_ivy_pg1.webp")
    l "Wow... This is what she's posting now?"
    $ flena = "n"
    l "So much for subtlety... But she has a ton of likes and comments."
    if stalkfap_pro:
        l "Should I post the same kind of stuff to get more people interested in my Stalkfap? I don't know..."
    elif stalkfap:
        l "This is the kind of pic I would only think of posting on my Stalkfap, not publicly..."
    else:
        l "Our styles are so different... I can't see myself posting these kinds of pics at all."
        l "I'm only comfortable with tasteful nudes..."
    "I decided to check out Ivy's profile to see what else she had been uploading. And I found something I wasn't expecting at all."
    # ivy axel
    hide v8_ivy_pg1
    show v8_ivy_pg2
    with short
    pause 1
    $ lena_ivy_pics.append("v8_ivy_pg2.webp")
    stop music fadeout 2.0
    $ flena = "worried"
    l "Wait, this picture... Where is she? This place looks familiar."
    $ flena = "surprise"
    l "This is... Axel's place?"
    "I was pretty sure it was!"
    $ flena = "worried"
    l "What the hell...? Was this picture taken recently? It looks like it..."
    $ flena = "serious"
    l "Why is Ivy working with Axel? And why at his place?"
    l "She didn't tell me anything about this. In fact, she said she had stumbled upon Axel both times Ian saw them..."
    $ flena = "sad"

    hide v8_ivy_pg2 with short
    if stalkfap and stalkfap_pro == 0:
        show lenanude2 at truecenter with move
    else:
        show lenabra at truecenter with move
    l "Am I jumping to conclusions...? But something's been going on, and she has kept me in the dark."
    $ flena = "serious"
    l "I will need to have another talk with her..."
    scene lenaroomnight with long
    "Needless to say, seeing that didn't exactly help me relax and fall asleep."
    "I spent most of the night rolling around in my bed, wondering if there was something I was missing about all this..."

## LENA WEDNESDAY ######################################################################################################################################################################################################################################################
    $ holly_look = 1

    call calendar(_day="Wednesday") from _call_calendar_71

    $ lena_look = 1
    $ flena = "worried"
    scene lenaroom with long
    play sound "sfx/meow.mp3"
    play music "music/normal_day4.mp3" loop
    show lenaunder at rig
    show lola at lef
    with short
    l "Uh... Is it already time to get up?"
    play sound "sfx/meow.mp3"
    "My cat meowed again, demanding to be fed."
    l "I don't need an alarm having you, Lola."
    "I sighed and left the bed."
    "I had only been able to sleep a few hours. I felt really tired."
    play sound "sfx/door.mp3"
    $ flena = "n"
    scene lenahome
    show lenabra
    show lola_b at lef3
    with short
    "I poured some dry kibble into Lola's bowl and I petted her while she dug her head in, happy."
    play sound "sfx/purr.mp3"
    hide lola_b
    show lolahappy_b at lef3
    with short
    l "I wish I could be happy with so little, like you. In my next life, I want to be a house cat."
## MOM CALL
    play sound "sfx/ring.mp3"
    if lena_lenamom < 4:
        $ flena = "worried"
    l "A call this early in the morning? It can only be from Mom..."
    "I was right, of course."
    hide lenabra
    hide lolahappy_b
    show lenabra_phone
    with short
    if v6_call_mom:
        l "Good morning, Mom."
        show phone_mom at lef3 with short
        lm "Hi, honey. How are you?"
        l "Still surviving. Lots of things happening here, lately..."
        if lena_lenamom > 3:
            l "In fact, this Friday I'll be playing a concert for the first time..."
            hide phone_mom
            show phone_mom_smile at lef3
            lm "A concert? You didn't tell us anything about it!"
            if cafe_music:
                l "It's a small thing. The café hasn't been getting customers and I thought hosting these kinds of events could bring some people in..."
            else:
                l "It's a small thing. Just to test the waters... I know someone who works at the local record store and they offered a spot, so..."

            lm "That's great! I still remember when you were little, always singing and dancing around... You were like a little opera singer!"
            $ flena = "shy"
            lm "You knew by heart all the songs for those animated movies... You could watch them three or four times in a single day."
            l "I was just a kid..."
            lm "Next time you have to tell us about this. Your dad and I would love to see you play!"
            $ flena = "n"
            l "Well, first let's see how this goes..."
            hide phone_mom_smile
            show phone_mom at lef3
            lm "We miss you, honey. You haven't come to visit in what, three months now?"
            $ flena = "sad"
            l "I've been really busy... What about you? How's work going at Polly's produce shop?"
        else:
            lm "You always say the same. We have no clue what's going on with your life!"
            $ flena = "serious"
            l "I'm doing fine, you don't need to worry so much. You know what they say, \"no news is good news\"."
            lm "Well, excuse me for wanting to have news about my only daughter! It's a mother's job to worry."
            l "Yeah, yeah..."
            "I didn't want to talk about my life, so I changed the subject."
            $ flena = "n"
            l "So, how's work going at Polly's produce shop? You're still working there?"
        hide phone_mom
        show phone_mom_sad at lef3
        lm "She's been really nice offering to hire me, but I'm afraid I'm not very reliable."
        lm "This week I had to cancel two shifts because of your dad..."
    else:
        l "Hey... What's up, Mom?"
        show phone_mom at lef3 with short
        lm "Well, nice to see you're still alive! I was getting worried."
        $ flena = "serious"
        "There she was again. The first thing out of her mouth was a reprove..."
        if lena_lenamom > 2:
            call friend_xp('lenamom', -1) from _call_friend_xp_645
            $ lena_lenamom = 2
        l "I spoke to Dad not that long ago, hasn't he told you?"
        lm "Yeah, he did. That was like a month ago!"
        l "It wasn't that long!"
        $ flena = "worried"
        "Was it? I had been losing track of time..."
        lm "So, how are you doing?"
        $ flena = "n"
        l "Surviving. Things at work aren't looking too good, but I'm searching for alternatives..."
        lm "You know you can come back home if you can't afford to keep living in Baluart by yourself... You'd save a lot of money."
        l "Yeah, no thanks... I'm doing fine here, don't worry."
        lm "You should come visit at least. When was the last time you came?"
        $ flena = "worried"
        l "It was... in April, I think."
        lm "It's been almost three months!"
        $ flena = "sad"
        l "I know, I know. I'm just too busy with things here!"
        hide phone_mom
        show phone_mom_sad at lef3
        lm "I really could use you around here. Polly was nice enough to hire me to help her with her produce market, but I'm afraid I'm not very reliable in the current situation."
        lm "This week I had to cancel two shifts because of your dad..."
    $ flena = "worried"
    l "What happened? Is Dad okay?"
    lm "He's been feeling a bit under the weather. We had to go to the hospital yesterday..."
    $ flena = "drama"
    l "Mom! Why didn't you tell me?"
    lm "Well, I'm telling you now. Seems it's nothing too serious, don't worry. He caught the seasonal flu."
    lm "His immune system is still a bit weak, you know."
    l "But he's still in remission?"
    lm "Yes, seems like he really beat his cancer for good. But he still needs someone to look after him, even if he doesn't want to admit it."
    lm "And him getting back to work is out of the question, of course."
    $ flena = "sad"
    l "So how's he doing today?"
    lm "Still a bit feverish, but the doctor said it should be fine."
    l "I will come visit as soon as I can..."
    hide phone_mom_sad
    show phone_mom at lef3
    if lena_lenamom > 3:
        lm "We'd like that."
    else:
        lm "He'd like that."
    if lena_money_family == 2:
        hide friend_up
        lm "And... I want to thank you for still sending money our way every month."
        lm "I know you're also on a tight budget. I'd hoped we could help you, not the other way around..."
        if lena_lenamom < 5:
            call friend_xp('lenamom', 1) from _call_friend_xp_646
        $ flena = "n"
        l "I'll keep helping you as long as I can. You know that."
    elif lena_money_family == 1:
        lm "And... I know you won't be able to help us every month, but we really appreciate it when you can send some money our way."
        l "I know. I'll try to send you what I can spare."
    else:
        lm "And... You know I don't like asking you for money, but if and when you're able to spare some we could really use it."
        l "I know, but I'm not exactly loaded these days..."
        lm "I know, I know. That's why I said {i}if and when{/i}."
    if lena_lenamom > 4:
        lm "Take care, honey. And good luck with that concert of yours!"
        l "Thanks, Mom. I'll let you know when I can go visit."
        l "Bye, Mom."
    else:
        lm "Take care, honey. And let us know when you can come visit."
        l "I will. Bye, Mom."
    hide phone_mom
    hide lenabra_phone
    show lenabra
    with short
    $ flena = "worried"
    "I sighed."
    l "I'm worried about Dad... And Mom's right. A visit is long overdue."
    l "Now I also feel guilty... Just what I needed."
    l "Things keep piling up..."
    scene cafe with long
    "After having some breakfast and taking a shower I headed straight to work."
    show lena at rig with short
    l "Good morning..."
    $ fmolly = "n"
    $ fed = "n"
    $ ed_look = 1
    show molly at lef with short
    mo "Good morning, Lena. You look tired..."
    l "I didn't get much sleep last night..."
    mo "Nervous about Friday?"
    l "Yeah, and some other stuff, too."
    if cafe_music:
        mo "You know you don't have to force yourself to play in order to help us..."
        l "I want to..."
        mo "But I also think you should do it, not for us, but for yourself! Follow your passion!"
    elif cafe_nude:
        l "Anyway, I'm sorry we can't do another life drawing event this week. We should set one up for the next..."
        mo "Don't worry about it. It was a huge success, I'm sure lots of people will come when we do the next one!"
    else:
        mo "I'm sorry we're putting you in this difficult situation..."
        $ flena = "n"
        l "It's not your fault. I'll be fine, don't worry."
    $ flena = "n"
    show lena at rig3
    show molly at truecenter
    with move
    show ed at lef3 with short
    ed "I agree."
    if cafe_music:
        ed "You're a girl of many talents, Lena! You should pursue them."
        ed "I still don't understand why such a beautiful model is working at our humble café."
        ed "I really enjoyed that life drawing session!"
    elif cafe_nude:
        ed "I was surprised myself... We hadn't had that many people in a while!"
        ed "If we keep that up, I have the feeling we'll be just fine..."
        ed "And I really enjoyed that life drawing session myself!"
    else:
        ed "Lena's a girl of many talents! I still don't understand why such a beautiful model is working at our humble café."
        ed "I really enjoyed that life drawing session!"
    if v7_fight != "n":
        ed "Too bad those boys got into a fight, but other than that the experience was fantastic."
    if cafe_music == False:
        mo "You've been really enthusiastic about it! Did you show Lena your drawing yet?"
    else:
        mo "Ed's been talking about it since last week! Did you show Lena your drawing yet?"
    ed "Not yet, wait..."
    hide ed with short
    if ed_callout:
        $ flena = "worried"
        "Great... If it wasn't uncomfortable enough, now my boss was becoming enthusiastic about drawing me naked."
        "Should I call him out again...?"
        $ flena = "n"
    elif lena_ed > 5:
        "Seeing Ed that enthusiastic made him seem younger and more energetic. I was glad."
    show ed at lef3 with short
    ed "Here."
    $ flena = "surprise"
    show v8_ed_drawing with short
    l "Oh, wow! It's pretty good!"
    $ fed = "smile"
    ed "You think so?"
    $ flena = "smile"
    l "Yeah, I'm surprised... I didn't know you were good at drawing!"
    $ fmolly = "smile"
    hide v8_ed_drawing with short
    mo "Ed has always been crafty and artistic, inside and outside the kitchen! That's one of the things that made me fall in love with him."
    $ fed = "perv"
    ed "I just liked arts as a hobby. Drawing, a bit of painting, and some sculpting..."
    mo "You still have your small workshop at home, but it's been a long time since you sat there..."
    mo "Oh, I remember when I was the girl you used to draw...!"
    $ fed = "surprise"
    l "Oh, now {i}that{/i} is surprising."
    ed "That was so many years ago!"
    mo "Before you fattened me up with your delicious cooking!"
    $ fed = "smile"
    ed "Says the woman who bakes the best cakes in the world! My belly is proof of it!"
    if lena_ian_love:
        "It was weird imagining Ed and Molly as a young couple. Maybe they hadn't been that different from Ian and me..."
    else:
        "It was weird imagining Ed and Molly as a young couple. Maybe they hadn't been that different from me..."
    if cafe_help:
        "They also seemed in a better mood since I had come up with the idea to help the café."
        "All things considered, that was a small victory in itself, which I was glad for."
    else:
        "It was good seeing they still retained that much positivity despite the difficult situation their business was in."
        if cafe_steal:
            "I doubted they'd be able to keep it. The café was as much as doomed, and my job with it."
            "When that happened, their mood wouldn't be of any concern to me, though."
        else:
            "I hoped they could keep it."
    $ fmolly = "n"
    mo "Well, enough chit-chat. Let's get to work!"
    $ fed = "n"
    $ flena = "n"
    ed "Yes, ma'am!"
    scene cafe with long
    show lenawork with short
    $ flena = "sad"
    "While I worked, my doubts about what I had seen last night on Peoplegram didn't abandon my thoughts."
    if lena_job_restaurant > 0:
        "And there also was not knowing who kicked me out from the restaurant, and why. It had to be Mr. Ward, right...?"
## IAN TELLS HOLLY
    if v7_holly_kiss and ian_lena_dating:
        $ ian_lena_over = True
        "I also thought about Holly. During a short break, I texted her again."
        nvl clear
        if holly_gym:
            l_p "{i}Hi Holly! How are you? Will we see you this afternoon at pole class?{/i}"
            "I saw her typing, but deleting what she was writing before sending it. A couple of times."
            "She took a while to reply."
            play sound "sfx/sms.mp3"
            h_p "{i}I can't today, I have book stuff to discuss with my publisher {image=emoji_sad.webp} Next week maybe! {image=emoji_ups.webp}{/i}"
            $ flena = "worried"
            l "Something's definitely up with her. She's acting weird..."
            l_p "{i}Okay! I hope to see you next week. We miss you {image=emoji_heart.webp}{/i}"
        else:
            l_p "{i}Hi Holly! How are you? You haven't dropped by the café in a while!{/i}"
            "I saw her typing, but deleting what she was writing before sending it. A couple of times."
            "She took a while to reply."
            play sound "sfx/sms.mp3"
            h_p "{i}I know, this week I'm very busy, I have book stuff to discuss with my publisher {image=emoji_sad.webp} Next week maybe! {image=emoji_ups.webp}{/i}"
            $ flena = "worried"
            l "Something's definitely up with her. She's acting weird..."
            l_p "{i}Okay! I hope to see you next week. You're missed! {image=emoji_heart.webp}{/i}"
        $ flena = "n"
        if cafe_help:
            "I continued with my tasks. Today I noticed a few more customers than usual coming in..."
        else:
            "I continued with my tasks. Not that there was much to do, though."
            "I was getting bored..."
        if ian_wits > 4 or ian_charisma > 4:
            $ ian_look = 3
        else:
            $ ian_look = 1
        $ fian = "sad"
        show lenawork at rig with move
        show ian at lef3 with short
        i "Hey."
        l "Hi, Ian! Lunch break...?"
        $ flena = "sad"
        l "What's with the long face?"
        $ fian = "worried"
        i "Uh, I..."
        $ fian = "n"
        i "Don't worry."
        l "Wait, did something happen at the book fair? I had the feeling that Holly wasn't exactly cheerful..."
        l "Did you fight or...?"
        $ fian = "sad"
        i "Not exactly. Sorry, I didn't know how to bring this up, and I didn't want to disturb you while at work..."
        $ flena = "sad"
        "Suddenly, everything clicked together. Now I was getting the picture."
        if ian_holly_sex:
            i "Holly and I, well, we... slept together."
            $ flena = "worried"
            l "You... slept together? You mean you had sex with her?"
            i "Well... Yeah."
        else:
            i "Holly and I, well... we kissed."
            $ flena = "worried"
            l "You... kissed?"
            i "Actually, it was her who kissed me. It surprised me, but... I kissed her back."
            i "Just for a few seconds, but yeah... That's what happened."
    # LENA LOVE
        if lena_ian_love:
            l "Oh."
            "I couldn't decide how to react to that news. I was having trouble sorting out many conflicting feelings."
            l "But I thought we... You and me, I mean..."
            i "I know this is weird. I wasn't expecting this to happen, not really... And I know you and Holly have become close..."
            $ flena = "serious"
            l "And you and me? I thought we had become close, too...!"
            if ian_lena_love:
                i "I... I thought so, too."
                if  ian_holly_sex:
                    l "Then... How come you and Holly...?"
                    i "We let ourselves get carried away."
                    $ flena = "sad"
                else:
                    $ flena = "sad"
                    l "But you kissed her."
                    i "I did..."
            else:
                $ fian = "worried"
                i "Wha--? You mean close as in like...?"
                l "{i}Bah{/i}, never mind. Forget that."
            $ flena = "sad"
            l "So what's the deal between you and Holly? Are you...?"
            if ian_holly_dating:
                $ fian = "n"
                i "Dating? No, it's not like that... Not exactly."
                i "I'm in a complicated phase right now, but I'd like to see where that goes, so..."
                l "I see... You know what that means for us, right?"
                i "Yeah. I guess I do."
                l "I know Holly's crush on you is for real. And I guess you feel something for her, too."
                l "I'd say you've made your choice... I don't want to get in between you and her, so it's best if we stopped hooking up."
                i "Yeah..."
            elif ian_holly_sex:
                i "No, it's not like that. It was a one-time thing..."
                $ flena = "serious"
                l "I have the feeling Holly wants something more than a one-nighter from you."
                i "Maybe that's the case... But that's all I can give her. It's complicated..."
                l "Sounds to me like you were thinking with your dick. Not complicated at all."
                $ fian = "disgusted"
                i "..."
                $ fian = "sad"
                i "I guess I can't refute that."
            else:
                $ fian = "worried"
                i "What? No, no...!"
                i "It was just a kiss. Something that happened in the spur of the moment."
                i "It didn't go anywhere... I stopped it."
                l "Even if you did that, now the cards are on the table, aren't they...?"
            "I was such a fool. I had been getting my hopes up, but in the end..."
            if ian_holly_dating:
                if ian_alison_dating or ian_cherry_dating:
                    if lena_louise_sex or lena_robert_sex or lena_mike_sex:
                        "I already knew Ian was seeing another girl, but I had been also fooling around with other people. Still, I had hoped Ian and I could become closer with time..."
                    else:
                        "I already knew Ian was seeing another girl, but I still hoped we could get closer with time..."
                    "But it seems he had made his choice, and that choice was Holly."
                elif lena_louise_sex or lena_robert_sex or lena_mike_sex:
                    "I had been fooling around with other people, so maybe I wasn't in a position to feel disappointed... Still, I had hoped Ian and I could become closer with time..."
                    "But it seems he had made his choice, and that choice was Holly."
                else:
                    "I had hoped Ian and I could become closer with time, but it seems he had made his choice."
                    "And that choice was Holly."
            else:
                if ian_alison_dating or ian_cherry_dating:
                    if lena_louise_sex or lena_robert_sex or lena_mike_sex:
                        "I already knew Ian was seeing another girl, but I had been also fooling around with other people. I could understand that, but..."
                    else:
                        "I already knew Ian was seeing another girl. That had me on my guard already, but..."
                    "Holly wasn't just any random girl. She had become a close friend of mine, and I knew her crush on Ian was for real."
                elif lena_louise_sex or lena_robert_sex or lena_mike_sex:
                    "I had been fooling around with other people, so I could understand if Ian did the same thing. But Holly wasn't just some random girl..."
                    "She had become a close friend of mine, and I knew her crush on Ian was for real."
                else:
                    "I wasn't a fan of the idea of Ian fooling around with other girls, but he was free to do so if he wanted."
                    "Holly wasn't just any random girl, though. She had become a close friend of mine, and I knew her crush on Ian was for real."
            l "You're aware of the difficult situation you've put me and Holly in, right...?"
            $ fian = "sad"
            i "Yeah. That's why I felt the need to tell you..."
            l "I suppose you told Holly about us..."
            i "I did."
            $ flena = "sad"
            if ian_holly_dating:
                l "So now she probably thinks she's stealing you away from me."
            else:
                l "So now she probably thinks I'm the reason you turned her down."
            $ fian = "sad"
            i "Well, not exactly. I mean..."
            if ian_holly_dating:
                $ flena = "n"
                l "It's okay... You two want to be together, so I'm happy for you."
                l "I'll need to talk to Holly, though..."
            else:
                if ian_lena_love:
                    i "What happened was a mistake and I can't make excuses. I didn't keep my feelings in check."
                    i "But my feelings for you... I know I'm in no position to say this, but I think..."
                    $ flena = "serious"
                    l "Stop, Ian. I can't do that to Holly."
                    $ flena = "sad"
                    l "It's better if we stop... \"hooking up\"."
                    if ian_holly_sex == False:
                        i "There's nothing between Holly and me, really. It was only a kiss..."
                        l "Ian, please."
                else:
                    i "I'm sorry if I made things more complicated than I should. I think I wasn't completely aware of the situation..."
                    $ flena = "serious"
                    l "The blindest one is the one who refuses to see, but yeah... I guess you weren't aware."
                    $ flena = "sad"
                    l "It'd be best if we stopped... \"hooking up\"."
                    if ian_holly_sex == False:
                        i "It was just a kiss..."
                        l "That's not the reason. I can't do that to Holly, do you understand that?"
                i "I..."
                i "I understand."
    # LENA HOOKUP
        else:
            l "Oh."
            i "I know this is weird. I wasn't expecting this to happen, not really... And I know you and Holly have become close..."
            $ flena = "serious"
            l "Yeah... You've put us in a complicated situation, you're aware of that?"
            i "I am. That's why I'm telling you about it..."
            l "And how are you planning to handle things?"
            if ian_holly_dating:
                $ fian = "n"
                i "I... Honestly, I would like to see where this thing with Holly goes."
                $ flena = "sad"
                l "I see... You know what that means for us, right?"
                i "Yeah. I guess I do."
                l "I know Holly's crush on you is for real. And I guess you feel something for her, too."
                l "I'd say you've made your choice... I don't want to get in between you and her, so it's best if we stopped hooking up."
                i "Yeah..."
            elif ian_holly_sex:
                i "I spoke to Holly and we agreed this would be a one-time thing."
                l "I have the feeling Holly wants something more than a one-nighter from you."
                i "Maybe that's the case... But that's all I can give her. It's complicated..."
                l "Sounds to me like you were thinking with your dick. Not complicated at all."
                $ fian = "disgusted"
                i "..."
                $ fian = "sad"
                i "I guess I can't refute that."
                l "I suppose you told Holly about us..."
                $ fian = "n"
                i "Yeah."
                $ flena = "sad"
                l "So now she thinks I'm the reason you turned her down."
                $ fian = "sad"
                if ian_lena_love:
                    i "Well... Not exactly..."
                else:
                    i "Not really... As I said, I'm in a complicated moment..."
                l "Look... I don't really mind if you fool around with other girls. It's not like we're dating or anything..."
                l "But Holly's just not a random girl. She's become a good friend and I don't want to lose that over what you pulled off..."
                l "It'll be best if we stop hooking up. I hope you understand."
                if ian_lena_love:
                    $ fian = "worried"
                    i "I was hoping that..."
                    l "No, Ian. Being with you was fun, but as I said, I don't want to tangle things up or hurt Holly."
                    $ fian = "n"
                    i "I guess..."
                    i "I guess you're right."
                else:
                    i "It..."
                    $ fian = "n"
                    i "I guess it really is."
            else:
                $ fian = "worried"
                i "I've talked to Holly about it and we'll keep things as they are. We're just friends..."
                $ flena = "sad"
                l "I'd say she wants to be more than just your friend..."
                i "Maybe... But I don't think I can give her that. That's why I didn't take things further than a kiss..."
                l "Even if you did that, now the cards are on the table, aren't they? I suppose you told her about us..."
                $ fian = "n"
                i "Yeah."
                l "So now she thinks I'm the reason you turned her down."
                $ fian = "sad"
                if ian_lena_love:
                    i "Well... Not exactly..."
                else:
                    i "Not really... As I said, I'm in a complicated moment..."
                l "Look... I don't really mind if you fool around with other girls. It's not like we're dating or anything..."
                i "We didn't \"fool around\". It was just a kiss, nothing more."
                l "That's not the issue. Holly's just not a random girl. She's become a good friend and I know her crush on you is for real."
                l "It'll be best if we stop hooking up. I hope you understand."
                if ian_lena_love:
                    $ fian = "worried"
                    i "I was hoping that..."
                    l "No, Ian. Being with you was fun, but as I said, I don't want to tangle things up or hurt Holly."
                    $ fian = "n"
                    i "I guess..."
                    i "I guess you're right."
                else:
                    i "It..."
                    $ fian = "n"
                    i "I guess it really is."
        $ flena = "n"
        l "Is there something else you wanted to tell me?"
        i "No... I guess that's all..."
        l "Noted. Now, if you'll excuse me... Some clients are waiting over there."
        hide ian with short
        show lenawork at truecenter with move
        if lena_ian_love:
            $ flena = "drama"
            "Ivy was right. I shouldn't let myself harbor any romantic feelings..."
            "It was way too soon, and I risked being hurt again. I thought I could get closer to Ian, but..."
            $ flena = "sad"
            if ian_holly_dating:
                "It was clear Ian wasn't looking for a serious relationship. Not with me, at least."
            else:
                "It was clear Ian wasn't looking for a serious relationship."
            "Even if that was the case, I could still consider hooking up with him for my own selfish reasons..."
            "But not if Holly was involved."
            if ian_lena_sex == False:
                l "Now I don't know if it's a good thing we never ended up having sex. Guess I'll never know..."
        else:
            $ flena = "sad"
            if ian_lena_sex:
                "It was fun being with Ian, and we clicked pretty well in bed."
            else:
                "It was fun being with Ian, even though we hadn't had sex yet."
            "However, he was just a hook-up. That's what I had decided and what I told myself."
            "I wasn't entirely sure if he saw things the same way, but he had just shown me he did."
            "If he had decided to get close to Holly, I didn't want to cause trouble or interfere."
            if ian_holly_dating:
                "Even if it had been just a one-time thing, I wanted to make sure not to upset Holly, so better to put some distance between Ian and me."
        $ flena = "serious"
        "As for Ian... I was rather disappointed with how he handled the situation."
        if (lena_ian_love and ian_lena > 5) or ian_lena > 6:
            call friend_xp('lena', -1) from _call_friend_xp_647
            if lena_ian_love:
                $ ian_lena = 5
            else:
                $ ian_lena = 6

        "He should've known better than to play games with Holly and me..."
        $ flena = "sad"
        l "I'm at fault, too... I should've told Holly about us instead of waiting on Ian to do it."
        l "If I had been braver this whole situation could've been avoided..."
        "I would need to talk to Holly. It was obvious she was trying to hide from me..."

##HOLLY
    else:
        if cafe_help:
            "Today I noticed a few more customers than usual coming in..."
        else:
            "There was not much to do, as usual. I was getting bored..."
    # HOLLY TRIP
        if v7_holly_trip:
            #dating Ian
            if ian_holly_dating:
                $ fholly = "happy"
                show holly2 at lef3 with short
                h "Hello!"
                $ flena = "smile"
                l "Hi, Holly! Lunch break?"
                h "Yeah. I'm fancying some of Ed's delicious Eggs Benedict..."
                l "I'll tell him right away."
                "I called in the order and served Holly a drink."
                show holly2 at lef
                show lenawork at rig
                with move
                l "You look happy... Did something good happen?"
                $ fholly = "happyshy"
                hide holly2
                show holly3 at lef
                with short
                h "Actually, yes... This weekend, with Ian..."
                $ flena = "happy"
                l "Oh, finally! That's great, Holly!"
                h "Yeah..."
                l "What happened? Did he take the first step?"
                h "It was me, actually... I kissed him and..."
                h "We slept together."
                l "Now that's progress! And how was it?"
                if v7_holly_rough:
                    $ fholly = "blush"
                    h "It was... intense. I hope Ian didn't think I was too much of a pansy..."
                    $ flena = "smile"
                    l "Intense, huh? Well that's not bad, is it?"
                    $ fholly = "shy"
                    h "No... But I was feeling pretty sore the next day...!"
                    $ flena = "happy"
                    l "Sounds like it was intense, yeah, ha ha!"
                else:
                    $ fholly = "shy"
                    h "It was... everything I had imagined it would be. Well, almost."
                    l "Almost?"
                    $ fholly = "flirt"
                    h "He was caring and sweet and... it felt really good."
                    $ fholly = "blush"
                    h "But I'm not sure if I gave him a really good time. I was a bit nervous..."
                $ flena = "smile"
                l "What's important is that you enjoyed it."
                hide holly3
                show holly2 at lef
                with short
                $ fholly = "shy"
                h "I definitely did..."
                l "So what now? Are you two dating or...?"
                h "I don't know. I think we agreed to take things slow... See where it takes us..."
                $ flena = "sad"
                l "Oh, so no commitment on Ian's part?"
                $ fholly = "n"
                h "I'm not sure... Not in that way... I mean, it seems he likes me enough..."
                h "But he also told me he's in a difficult moment in his life when it comes to relationships..."
                l "Oh, I know. I talked to Emma, a friend of his, just yesterday."
                l "She told me about Ian's ex, and how they broke up after she cheated on him..."
                $ fholly = "worried"
                h "She cheated on him?"
                l "That's what she told me. Seems like it was a pretty hard blow for Ian."
                $ fholly = "sad"
                h "I don't understand why anybody would cheat on a guy like Ian... Now I understand why he's feeling like that."
                $ flena = "n"
                l "That kind of thing can happen to anybody. I should know."
                $ flena = "smile"
                l "But what's important is that you and Ian have finally taken your relationship to a new level!"
                $ fholly = "shy"
                h "Yeah... I hope it goes well..."
                l "Me too. But just try to enjoy the moment, step by step."
                h "Yeah, I will. Thanks, Lena."

            # rejected by Ian
            else:
                $ fholly = "n"
                show holly2 at lef3 with short
                h "Hi..."
                $ flena = "smile"
                l "Hi, Holly! Lunch break?"
                h "Yeah. I could use some of Ed's delicious Eggs Benedict..."
                l "I'll tell him right away."
                "I called in the order and served Holly a drink."
                show holly2 at lef
                show lenawork at rig
                with move
                l "So, how was the book fair?"
                if ian_holly_sex:
                    $ fholly = "blush"
                    hide holly2
                    show holly3 at lef
                    with short
                    h "I..."
                    $ flena = "sad"
                    l "Did something happen with Ian?"
                    h "Well, yeah... We..."
                    h "We slept together."
                    $ flena = "happy"
                    l "Really!?" with vpunch
                    l "And how was it?"
                    $ fholly = "blush"
                    if v7_holly_rough:
                        h "It was... intense. But..."
                    else:
                        h "It was... everything I had imagined it would be. But..."
                    $ flena = "sad"
                    h "He said it was only a one-time thing."
                    $ flena = "serious"
                    l "What? Why?"
                    h "He said it's better if we left it at that. That he didn't feel like he could live up to my expectations, even though I didn't have any..."
                    l "Sounds to me like he took advantage of the situation and now's making excuses!"
                    if ian_lena > 3:
                        call friend_xp('lena', -1) from _call_friend_xp_648
                    $ fholly = "sad"
                    hide holly3
                    show holly2 at lef
                    with short
                    h "It's not like that... It happened because we both wanted it to happen."
                    $ flena = "sad"
                    h "I already had the feeling he was not available, and he told me he's in a difficult moment in his life when it comes to relationships..."
                    l "Well, you're not wrong about that..."
                elif v7_holly_kiss:
                    $ fholly = "blush"
                    h "I..."
                    $ flena = "sad"
                    l "Did something happen with Ian?"
                    hide holly2
                    show holly3 at lef
                    with short
                    h "I... kissed him."
                    $ flena = "smile"
                    l "Oh, you took the dive?"
                    h "Yeah... And I landed flat on my face."
                    $ flena = "sad"
                    l "What happened?"
                    h "At first, he kissed me back, but then... He regretted it."
                    l "What? Why?"
                    $ fholly = "sad"
                    h "He said he didn't feel like he could live up to my expectations, even though I didn't have any..."
                    $ flena = "serious"
                    l "That sounds like a bit of a bullshit excuse..."
                    hide holly3
                    show holly2 at lef
                    with short
                    h "No, I get him. I think so, at least."
                    $ flena = "sad"
                    h "I already had the feeling he was not available, and he told me he's in a difficult moment in his life when it comes to relationships..."
                    l "Well, you're not wrong about that..."
                else:
                    $ fholly = "sad"
                    h "..."
                    $ flena = "sad"
                    l "Something happened?"
                    h "No... Nothing happened..."
                    # lena tells about Ian
                    if ian_lena_dating:
                        l "Oh. I see..."
                        "I felt kind of relieved, but at the same time, I felt sorry for Holly."
                        "It was time to be honest with her about my relationship with Ian."
                        l "Holly... I don't know how to say this, and I should've told you much sooner, but I wasn't certain what was going on between you and Ian..."
                        l "The truth is he and I have... Well, we've met a couple of times and..."
                        $ fholly = "blush"
                        hide holly2
                        show holly3 at lef
                        with short
                        h "You're together, right?"
                        l "Um, well, not like that... We're getting to know each other, but... Yeah, we're close."
                        h "I already knew it... I could tell by looking at you."
                        l "You did?"
                        h "I mean, I wanted to tell myself I could be mistaken, but yeah... It was pretty obvious..."
                        h "Which makes me a horrible person... I wanted something to happen with Ian even though I suspected you and him..."
                        h "I'm sorry."
                        l "No, don't be...! It's me who's sorry. I should've told you what was going on..."
                        l "But I wasn't really sure myself, to be honest."
                        $ fholly = "sad"
                        h "Are you two... dating?"
                        if lena_ian_love:
                            $ flena = "blush"
                            l "I... I don't know. We're going on dates, that's true, and we like each other..."
                            l "I'm not sure what exactly I feel for him, but he's... I feel he's special."
                            if ian_lena_love:
                                h "You're special to him, too."
                            else:
                                h "I see..."
                        else:
                            l "Uh, well, it's not like we have something serious...! We're just friends with benefits, you know..."
                            if ian_lena_love:
                                h "I got the impression his feelings for you may be stronger than that..."
                                $ flena = "worried"
                                l "You think so? He's great and all, but the truth is I'm not looking for a boyfriend right now..."
                                h "I see..."
                            else:
                                h "Oh... So it's that..."
                        hide holly3
                        show holly2 at lef
                        with short
                        h "Anyway, I'm glad for you. You two really look like you belong together."
                        $ flena = "sad"
                        "I felt guilty, like I had just trampled over Holly's dreams and expectations."
                        l "If I had known... I could've kept my distance from Ian..."
                        h "No, no!"
                        $ fholly = "smile"
                        h "I'm happy for you guys... You deserve to find someone who makes you happy..."
                        l "And so do you."
                        $ fholly = "n"
                        h "Maybe..."
                        l "In any case... I'm not sure Ian is looking for someone to \"make him happy\"."
                        h "What do you mean?"
                    # no lena ian
                    else:
                        l "Oh. So Ian and you didn't hit it off?"
                        h "No... We ended up spending the night in the same hotel room, but even so..."
                        hide holly2
                        show holly3 at lef
                        with short
                        h "I don't know what I was expecting. He's not interested."
                        l "It looked like he could be..."
                        h "It was all just wishful thinking. I got the wrong impression."
                        l "That's... too bad..."
                        $ fholly = "n"
                        hide holly3
                        show holly2 at lef
                        with short
                        h "Well, no surprises there, really. At least now I know for sure."
                        l "I'm sorry to hear."
                        h "It's okay. Nothing's changed, after all."
                        $ fholly = "smile"
                        h "It's okay. I already had the feeling he was not available, anyway."

                l "I talked with Emma, a friend of his, and she told me about Ian's ex, and how they broke up after she cheated on him..."
                $ fholly = "worried"
                h "She cheated on him?"
                l "That's what she said. Seems like it was a pretty hard blow for Ian."
                $ fholly = "sad"
                h "I can't believe that could happen to a guy like Ian..."
                $ flena = "sad"
                l "That kind of thing can happen to anybody. I should know."
                h "I can see why he'd feel like that..."
                $ flena = "n"

    # NO TRIP
        else:
            $ fholly = "n"
            show holly2 at lef3 with short
            h "Hello."
            $ flena = "smile"
            l "Hi, Holly! Lunch break?"
            $ fholly = "smile"
            h "Yeah. I'm fancying some of Ed's delicious Eggs Benedict..."
            l "I'll tell him right away."
            "I called in the order and served Holly a drink."
            show holly2 at lef
            show lenawork at rig
            with move
            l "So, how was the book fair?"
            h "It was fun... Pretty busy, though."
            l "That's a good thing, isn't it? Did you meet a lot of your fans?"
            $ fholly = "shy"
            h "Quite a few... But I don't like calling them fans."
            $ flena = "happy"
            l "Well, that's what they are. I know, because I'm one, too!"
            hide holly2
            show holly3 at lef
            with short
            if ian_lena_dating and ian_go_holly == False:
                $ fholly = "blush"
                h "And... How are things with Ian?"
                if lena_ian_love:
                    $ flena = "blush"
                    l "I... Well, we go on dates and we like each other..."
                    l "I'm not sure what I exactly feel for him, but he's... I feel he's special."
                    $ fholly = "smile"
                    h "I'm glad for you both... You two deserve to find someone who will make you happy."
                    $ flena = "smile"
                    l "And so do you."
                    $ fholly = "n"
                    h "Maybe..."
                    $ flena = "n"
                    l "In any case... I'm not sure Ian is looking for someone to \"make him happy\"."
                    h "What do you mean?"
                else:
                    $ flena = "worried"
                    l "Uh, well, it's not like we have something serious...!"
                    $ flena = "n"
                    l "We're just friends with benefits, you know..."
                    $ fholly = "n"
                    h "Oh... So it's that..."
                    l "He's great and all, but the truth is I'm not looking for a boyfriend right now..."
                    l "And I'm inclined to think he's not looking for a girlfriend, either."
                    h "Why's that?"
            else:
                $ fholly = "sad"
                h "Too bad Ian didn't want to come..."
                $ flena = "sad"
                l "Maybe some other time down the line..."
                $ flena = "smile"
                l "Now just concentrate on your own journey! I'm sure you'll be meeting lots of new people!"
                h "I guess..."
                $ flena = "n"
                l "Besides... I'm not sure Ian is exactly available right now."
                h "Why do you think so?"
            l "I talked with Emma, a friend of his, and she told me about Ian's ex, and how they broke up after she cheated on him..."
            $ fholly = "worried"
            h "She cheated on him?"
            l "That's what she said. Seems like it was a pretty hard blow for Ian."
            $ fholly = "sad"
            h "I can't believe that could happen to a guy like Ian..."
            $ flena = "sad"
            l "That kind of thing can happen to anybody. I should know."
            hide holly3
            show holly2 at lef
            with short
            h "I get why he would feel like that..."
            $ flena = "n"

        $ flena = "n"
        l "Hey, I'm gonna get your eggs. You must be hungry!"
        if holly_gym:
            $ flena = "smile"
            l "See you this afternoon at the gym?"
            h "Yeah."
            if holly_guy:
                $ flena = "smile"
                l "Oh, by the way! What about that guy Ivy gave your number to? Has he contacted you?"
                $ fholly = "blush"
                hide holly2
                show holly3 at lef
                with short
                h "Yeah, he did..."
                $ flena = "happy"
                l "So?"
                h "Well, he seems smart and nice enough... But he's smart in a different way than me."
                $ flena = "n"
                l "What do you mean?"
                h "We're interested in different things and it's hard for me to make conversation with him..."
                if ian_holly_dating:
                    $ fholly = "shy"
                    h "Besides, he's not the one I'm interested in..."
                    l "No, he's clearly not!"
                    h "I appreciate Ivy's gesture, but I think I will stop talking to him."
                else:
                    if holly_change > 2:
                        l "Sometimes it takes a while to find how to vibe with somebody. You should meet in person, that would make things more natural."
                        h "That makes sense. Maybe I'll give it a chance."
                    else:
                        l "Sometimes it takes a while to find how to vibe with somebody. Give it a shot if you want, but if you feel it's going nowhere, you don't have to force it."
                        h "Yeah... He's nice enough, though, so it doesn't bother me."
                l "Well, see you later Holly!"
            else:
                l "Later, then!"

        else:
            h "Yes, don't let me keep you from your duties..."
            $ flena = "happy"
            l "By the way, I'm counting on you for this Friday!"
            $ fholly = "smile"
            h "Of course, I wouldn't wanna miss your debut."


    stop music fadeout 2.0
    scene cafe with long
    "The day went on as normal and after my shift was over I headed to the gym."
##GYM IVY
    play music "music/jeremys_theme.mp3" loop
    scene polegym with long
    $ lena_look = 2
    $ flena = "n"
    $ fivy = "n"
    $ ivy_look = 2
    show lena2 at rig
    show ivy at lef
    with short
    v "Hey, girl! You came!"
    l "You always seem surprised to see me here. I've been coming a lot lately!"
    $ fivy = "smile"
    if lena_job_restaurant > 0:
        v "You're better off not having to work so many nights at that restaurant."
        $ flena = "sad"
        l "Yeah, about that... Seems they're finally kicking me out. No more night shifts."
        v "Really? What did you do?"
        $ flena = "serious"
        l "Nothing! But I'll find out the reason they're doing this..."
        v "It might be better this way. Fuck that restaurant."
        $ flena = "n"
        l "Yeah, fuck it... But I need the money."
    else:
        v "That's true. You're better off without that crappy restaurant job."
        l "Happier, yes, but poorer..."
    v "You have other ways to make money, much better ways! Haven't you been listening to me?"
    l "Yeah, yeah... I wish it was as easy for me as it seems to be for you."
    hide ivy
    show ivy2 at lef
    with short
    v "Who said it was easy?"
    l "You did."
    $ fivy = "n"
    hide ivy2
    show ivy at lef
    with short
    v "Well, yeah. Easier than serving tables, that's for sure."
    v "But I'm driven and hard-working too! No reason why it shouldn't be easy for you."
    if stalkfap_pro:
        if v8_stalkfap_dm1 or v8_stalkfap_dm2:
            l "Last night I was replying to some of my followers' messages..."
            v "Good, I see you were actually listening! Give them what they want and they'll shower you with money and gifts."
    # HOLLY MISSING
    if ian_lena_dating and v7_holly_kiss:
        $ flena = "sad"
        if holly_gym:
            l "I see Holly hasn't shown up today, either..."
            v "Yeah. I'm actually surprised she came to so many classes. I always knew she wouldn't last..."
            l "It's not that. I think she's been avoiding me."
        else:
            v "Wait, you're worried about something else. I know that face."
            l "It's about Holly... I think she's been avoiding me."
        $ fivy = "n"
        v "What for? Did you two have a fight?"
        l "No, it's nothing like that. It's about Ian..."
        "I told Ivy about my conversation with Ian this noon."
        $ fivy = "smile"
        hide ivy
        show ivy2 at lef
        with short
        if ian_holly_sex:
            if ian_holly_dating:
                v "I can't believe this! You lost to Holly?"
                $ flena = "serious"
                l "I didn't \"lose\". I'm stepping down since I don't want to get in the middle."
                $ flena = "n"
                l "If she and Ian have a thing for each other I want them to be free to explore that."
                $ fivy = "flirt"
                v "Very gracious on your part."
                $ flena = "serious"
                v "Anyway, Holly's finally gotten some action... Good for her, she really needed that."
                $ flena = "sad"
                v "And luckily for you, Ian's not the only guy around!"
            else:
                v "So he fucked her and then said he didn't want to hurt her so better leave it at that?"
                v "How fucking typical. Guys, they're all the same."
                $ flena = "serious"
                if lena_ian_love:
                    l "Yeah. He acted like a total dick."
                elif cheat_mode:
                    l "Yeah... In bird culture, that's considered a dick move."
                else:
                    l "Yeah, that was a dick move..."
            if lena_ian_love:
                v "It's what I always tell you: forget about looking for another relationship! Just have fun for a while!"
                $ flena = "sad"
                l "I might as well... I thought Ian could be different, but it turns out it was just another disappointment."
                if lena_robert_sex or lena_mike_sex or v7_bbc == "lena":
                    v "You're not a saint either..."
                    $ flena = "serious"
                    l "It's not about that...!"
                    v "Sure, sure..."
                $ fivy = "n"
                v "You really liked him, huh?"
                $ flena = "sad"
                l "I thought I did..."
                $ flena = "serious"
                l "But I'm dealing with enough shit right now as it is. Last thing I need is more stuff to worry about."
            else:
                if ian_holly_dating == False:
                    l "That's why I think it's best if I stop hooking up with Ian. I don't want to make Holly feel even worse about it."
                    v "If you think that's the way to go... Not that Ian is the only guy around, of course."
                l "I just want things to be easy for a change. I'm dealing with enough shit right now as it is. Last thing I need is more stuff to worry about."
            l "I don't want to dwell on this. It's done and that's it."
            $ fivy = "smile"
            v "That's the spirit!"
            $ flena = "n"
        else:
            if v6_holly_kiss == "lena":
                v "Come on, it was only a kiss. You kissed Holly too, have you forgotten?"
                l "But that was different..."
                v "I think you're overreacting."
            elif v6_holly_kiss == "ivy":
                v "Come on, it was only a kiss. I've kissed Holly too, not a big deal!"
                l "But that was different..."
                v "I think you're overreacting."
            else:
                v "It was only a kiss... I think you're overreacting."
            if lena_ian_love:
                $ flena = "serious"
                l "No, I'm not!"
                v "I thought you liked the guy."
                $ flena = "sad"
                l "I did... I mean, I still do, but..."
                l "It's clear his head is not in the right place."
                v "Is yours?"
                l "I..."
                if lena_robert_sex or lena_mike_sex or v7_bbc == "lena":
                    v "I mean, you're not a saint either..."
                    $ flena = "serious"
                    l "It's not about that...!"
                    v "Sure, sure..."
                    $ flena = "sad"
                v "Anyway, better this way. You know I don't think you should get too involved with somebody right now."
                v "Not until you've lived a bit, at least!"
            else:
                l "I don't know... I'm sure Holly's feelings are for real. I don't want to get in the way of that..."
                v "But what does Ian want? He told Holly what happened was a mistake, didn't he?"
                $ flena = "serious"
                l "Yeah. Seems like he didn't really think things through."
                v "Or maybe he did, after she kissed him."
                $ flena = "sad"
                v "Anyway, it's not like he's the only guy around. If he's more trouble than he's worth, feel free to move on!"
            l "I just want things to be easy for a change. I'm dealing with enough shit right now as it is. Last thing I need is more stuff to worry about."
        $ flena = "sad"
        l "In any case, now it seems Holly's avoiding me since Ian told her about us."
        v "She's so awkward. Just call her, she'll get over it."
        $ fivy = "n"
        hide ivy2
        show ivy at lef
        with short
        v "Unless you're mad at her..."
        $ flena = "serious"
        l "I'm not mad at her! If I was mad at somebody, that would be Ian..."
        $ fivy = "flirt"
        v "Yeah, you're definitely mad about him..."
        $ flena = "sad"
    # HOLLY GYM
    elif holly_gym:
        $ holly_glasses = False
        if ian_holly_dating:
            $ fholly = "happy"
        elif v7_holly_kiss:
            $ fholly = "n"
        else:
            $ fholly = "smile"
        $ holly_look = 3
        show ivy at lef3
        show lena2 at rig3
        with move
        show holly2 with short
        h "Good afternoon."
        if ian_holly_dating:
            v "Well, someone looks happy today. Something good happened?"
            $ fholly = "shy"
            h "Is it that obvious? Lena noticed too..."
            v "Whatever, tell me later."
        elif v7_holly_kiss:
            v "What's up with the long face?"
            $ fholly = "blush"
            h "Uh..."
            $ flena = "sad"
            v "Whatever, tell me later."
        else:
            v "There you are. I see you're not quitting just yet."
            h "No... I would like to try and get good at this."
            $ fholly = "sad"
            h "Well, not good... Maybe decent. Or just not terrible..."
            v "Reasonable enough."

    $ flena = "n"
    $ fivy = "n"
    v "Enough talking. Let's start the class!"

    if (holly_gym and ian_lena_dating == False) or (holly_gym and v7_holly_kiss == False):
        v "Holly, go over there and keep practicing the basic moves we've been working on."
        $ fholly = "smile"
        h "Yes!"
        hide holly2 with short
        v "She's so diligent..."
    else:
        show ivy at lef3 with move
    $ v8lenaworkout = False # defines when ivy's convo next takes place
    menu:
        "Confront Ivy about the picture":
            $ renpy.block_rollback()
            $ flena = "sad"
            hide lena2
            if (holly_gym and ian_lena_dating == False) or (holly_gym and v7_holly_kiss == False):
                show lena at rig3
            else:
                show lena at rig
            with short
            show lena at rig with move
            "That picture on Ivy's Peoplegram had been weighing on my mind all day. I wanted to resolve that issue right away."
            l "Hey, Ivy... I need to talk to you."
            v "Can't it wait?"
            $ flena = "serious"
            l "No."
            $ fivy = "n"
            v "Alright..."
            v "Girls, start with our usual warm-up! I'll be with you in a second."
            if lena_charisma < 8:
                call xp_up('charisma') from _call_xp_up_485
            show ivy at lef
            with move
            label v8ivygymtalk:
                v "What's up? You look serious..."
            l "Well, last night I saw your picture on Peoplegram..."
            v "My picture?"
            l "You know. The one Axel took, at his place."
            $ fivy = "sad"
            v "Oh! That one!"
            l "I didn't know you were working with Axel..."
            $ fivy = "n"
            v "Oh, yeah, I wanted to tell you, but I totally forgot."
            menu:
                "{image=icon_friend.webp}I believe you" if lena_ivy > 5:
                    $ renpy.block_rollback()
                    $ flena = "n"
                    l "Considering it's you... I believe it."
                    if lena_will < 1:
                        call will_up() from _call_will_up_47
                    l "Only a madcap like you would forget to bring that up..."
                    v "Hey! But you're not wrong..."
                    $ flena = "serious"
                    l "Still, I want my explanation!"

                "You {i}forgot{/i}?":
                    $ renpy.block_rollback()
                    l "Really? You {i}forgot{/i}?"
                    $ fivy = "sad"
                    v "What? You say that like you don't believe me..."
                    l "Excuse my skepticism, but it sounds rather hard to believe."
                    $ fivy = "serious"
                    v "Well, I was meaning to tell you but it slipped. Why else would I post a pic that you're surely gonna see?"
                    l "That's what I'd like to know!"
                    $ fivy = "n"
                    v "{i}Agh{/i}, that's what I'm trying to do!"

                "You were keeping it a secret!":
                    $ renpy.block_rollback()
                    l "I'm not stupid, you know?"
                    l "It's obvious you were keeping it from me, I'm just wondering why."
                    $ fivy = "sad"
                    v "I know it would seem like that, but why would I keep it a secret and then post that picture for you to see?"
                    l "Because you forgot I could see it?"
                    $ fivy = "serious"
                    if lena_ivy > 6:
                        call friend_xp('ivy', -2) from _call_friend_xp_649
                    elif lena_ivy > 3:
                        call friend_xp('ivy', -1) from _call_friend_xp_650
                    v "Do you really think I'm that dumb?"
                    l "Well, it's you who said you {i}forgot{/i} to tell me in the first place."
                    $ fivy = "n"
                    v "{i}Agh{/i}! Girl, will you listen to me? There's no reason I wanted to keep it secret from you."
                    v "I was meaning to tell you, just like I'm about to do now."
                    v "And besides, we never said I shouldn't talk to Axel after you two broke up."
                    v "You know I've seen him a few times already, and always refused to answer any question related to you."
                    $ flena = "sad"
                    l "That's true..."

            v "Look, this is what happened: I saw Axel's last posts on Peoplegram and found out he was shooting a couple of high-profile models."
            v "You know he's well-considered and works with some agencies... Well, now it seems he's started to work with Wildcats!"
            $ flena = "sad"
            v "You know that's the model agency I've been aiming for... The top of the top!"
            v "If Axel could hook me up with them, well... That would be incredible!"
            l "So you asked him to take some pictures for them?"
            $ fivy = "flirt"
            hide ivy
            show ivy2 at lef
            with short
            v "Yeah... I'm so excited!"
            v "I told you about my plan to get there, which was far-fetched, to begin with, and I could only hope..."
            v "But if Axel puts me in contact with them, well... I can't imagine a better shortcut!"
            v "It'll be literally impossible for them not to notice me!"
            $ flena = "n"
            l "I see... So that's why you're hanging around with Axel now..."
            $ fivy = "n"
            v "I'm not {i}hanging around{/i} with him. I just talked to him and asked him this favor, and he was up for it."
            $ flena = "serious"
            l "And why didn't you tell me?"
            $ fivy = "serious"
            hide ivy2
            show ivy at lef
            with short
            v "Because I wanted to avoid you getting all riled up, just like now!"
            $ fivy = "sad"
            v "I knew if I told you you'd probably make a big deal out of it and I didn't want us to have an argument..."
            if lena_ivy > 5:
                l "What sparked this argument is that you didn't tell me."
            else:
                l "Well, that's exactly what you caused by not being honest with me."
            $ fivy = "smile"
            if v6_axel_work:
                v "Well, I'm telling you now. And I'll also tell you you should call Axel, too."
                $ flena = "sad"
                v "Ask him to get you in touch with Wildcats. This is a great opportunity, Lena!"
                l "I don't know about that... And I don't want to owe him any favors."
                v "I think it's him who owes you for what he did. This would be a way for him to atone!"
                l "Well..."
                if v6_axel_pose > 1:
                    v "You already told me you two worked together just recently! You even posed together!"
                    $ flena = "blush"
                    if v6_axel_pose == 3:
                        "Every time I thought about that photo shoot blood rushed to my cheeks and my belly tingled anxiously."
                    l "That was totally unexpected and due to the circumstances...!"
                else:
                    v "You two already worked together just recently!"
                    v "Even if you had a falling out with your patron, Axel might be up for it."
                    l "That was totally unexpected and due to the circumstances..."
                $ fivy = "n"
                v "It's your choice. I'm not telling you what to do, but if you want my advice..."
                v "Don't let this opportunity slip. I sure won't."
                v "Look, I can go with you to the shoot if it makes you feel safer!"
                l "That'd help, yeah, but still..."
                $ fivy = "flirt"
                v "Come on, imagine if we got into Wildcats together! That'd be the dream!"
            elif v4_axel_date:
                v "Well, I'm telling you now. And I'll also tell you you should call Axel, too."
                $ flena = "serious"
                v "Ask him to get you in touch with Wildcats. This is a great opportunity, Lena."
                l "What? No! I don't want to owe him any favors."
                v "I think it's him who owes you for what he did. This would be a way for him to atone!"
                $ flena = "worried"
                l "I don't know about that..."
                $ fivy = "n"
                v "You already met him and buried the hatchet, didn't you?"
                if lena_axel > 2:
                    l "We kinda did, but..."
                    l "I don't think it would be a good idea. This only works because Axel and I are not in touch."
                    l "Working with him would be... rather complicated."
                elif lena_axel > 1:
                    l "We did meet, but... It's not like everything's fine."
                    l "I still don't want to see him. Posing for him would be... way too complicated."
                else:
                    $ flena = "serious"
                    l "I met him, but I didn't bury anything."
                    l "I still don't want to see him, much less talk or work with him."
                v "It's your choice. I'm not telling you what to do, but if you want my advice..."
                $ flena = "sad"
                v "Don't let this opportunity slip. I sure won't."
                v "Look, I can go with you to the shoot if it makes you feel safer!"
                l "That'd help, yeah, but still..."
                $ fivy = "flirt"
                v "Come on, imagine if we got into Wildcats together! That'd be the dream!"
            else:
                v "Well, I'm telling you now. And I'd also tell you to contact Axel, if you weren't on such bad terms."
                $ flena = "serious"
                l "I won't call him, nor ask any favors!"
                $ fivy = "n"
                l "You think I could pose in front of him again? No dice!"
                v "I know, I know, that's why I said \"I'd tell you to\" and I'm not telling you to."
                v "It's a great opportunity, though..."
                l "I'm glad for you, but I don't need it."
            $ fivy = "n"
            v "So, is your curiosity satisfied?"
            if lena_ivy > 5:
                $ flena = "sad"
            else:
                $ flena = "serious"
            l "Yes. But next time be upfront with me. You can't wait to tell me these things!"
            $ fivy = "smile"
            v "{i}Yeees{/i}, I won't worry you again with my carelessness."
            v "I'll try, at least!"
            if v8lenaworkout:
                hide ivy
                show ivy2 at lef
                with short
                v "Come on now, let's take a shower. I'm in need of one!"
                $ flena = "n"
                l "Yeah, me too..."
            else:
                hide ivy
                show ivy2 at lef
                with short
                v "Come on now, let's get back to the class."
                $ flena = "n"
                l "Sure..."
                scene polegym with long
                "We reincorporated into the class, and I practiced some of the figures Ivy had been teaching me."
                "A few minutes later we hit the showers and left the gym."
            jump v8ivygymend

        "Work out":
            $ renpy.block_rollback()
            $ v8lenaworkout = True
            $ flena = "sad"
            "That picture on Ivy's Peoplegram had been weighing on my mind all day... But I could bear to wait a bit more before getting my answer."
            $ flena = "n"
            "I didn't want to interrupt the class, and I could use a good workout."
            scene v4_gym_lena with long
            "I jumped on the pole and followed Ivy's instructions."
            if lena_athletics < 10:
                call xp_up('athletics') from _call_xp_up_486
            if lena_athletics < 10:
                call xp_up('athletics') from _call_xp_up_487
            "Pole dancing could be so physically tiring, but also quite fun..."
            scene v6_gym_lena with long
            if lena_lust > 4:
                "It was easy feeling sexy once you got the hang of things. At least the basic ones."
            else:
                "I even started to feel sexy, once having gotten the hang of things. At least the basic ones."
                call xp_up('lust') from _call_xp_up_488
            scene v2_pole2 with short
            "No way I could move as Ivy moved. I knew I would never reach that level, but maybe I could get halfway there..."
            scene polegym
            $ fivy = "n"
            show lena at rig3
            show ivy2 at lef
            with long
            "When we were done, I was covered in sweat, and so was Ivy. It had been an intense session..."
            $ flena = "sad"
            "Now was the ideal moment to talk to Ivy, after the other girls had left the room."
            show lena at rig with move
            $ flena = "serious"
            l "Hey, Ivy... I need to talk to you."
            hide ivy2
            show ivy at lef
            with short
            jump v8ivygymtalk

label v8ivygymend:
    stop music fadeout 2.0
    scene streetnight with long
    $ holly_glasses = True
    $ lena_look = 1
    $ ivy_look = 1
    $ holly_look = 1
    $ fholly = "n"
    if holly_gym:
        if v7_holly_kiss and ian_lena_dating:
            $ flena = "sad"
            $ fivy = "n"
            show lena at rig
            show ivy at lef
            with short
            v "You're still worried about Holly?"
            l "Yeah... I don't know how I should approach her."
            v "She looks like a scaredy-cat alright. But you're overthinking stuff."
            v "Just call her and be done with it. And if you want to prove to her you're not mad, invite her this Sunday."
            $ flena = "n"
            l "This Sunday?"
            v "Yeah, I want to go shopping. Let's all go together!"
            l "Holly would probably appreciate the invitation..."
            v "That's right. She's short on friends, I doubt she'd want to lose us over some guy."
            v "Anyway, I gotta run. Let me know how things go, and see you on Sunday!"
            $ flena = "sad"
            l "Wait, will you come to the concert this Friday?"
            v "Oh, yeah, sure. But I won't be able to stay for the after-party, I have to work."
            $ flena = "smile"
            l "Of course. See you on Friday!"
            hide ivy with short
        else:
            show holly3
            show lena at rig3
            show ivy at lef3
            with short
            if ian_holly_dating:
                $ fivy = "flirt"
                $ flena = "smile"
                $ fholly = "happyshy"
                "As we walked outside, we put Ivy up to date about Holly's recent situation."
                v "So you finally got some action! It was about time..."
                h "I guess it was..."
                v "Just please, don't join the \"happy couples group\" just yet."
                l "You really have something against serious relationships."
                $ fivy = "n"
                v "I've seen enough to know how these things go. You should be with me on this, Lena!"
                $ fholly = "shy"
                h "I don't think that'll happen..."
            elif ian_holly_sex:
                $ fivy = "flirt"
                $ flena = "sad"
                $ fholly = "blush"
                "As we walked outside, we put Ivy up to date about Holly's recent situation."
                v "So he fucked you and then said he didn't want to hurt your feelings so better leave it at that?"
                v "How fucking typical. Guys, they're all the same."
                h "..."
                $ fivy = "flirt"
                v "Well, at least you cleared those cobwebs away from your sex life! It was about time..."
                h "I guess I did..."
            elif v7_holly_kiss:
                $ fivy = "smile"
                $ flena = "sad"
                $ fholly = "blush"
                "As we walked outside, we put Ivy up to date about Holly's recent situation."
                v "So you only got a kiss from him? Such a waste..."
                h "I guess that's all he'd consider doing with me. Any further than that was out of the question..."
                v "I'm sure you could've pressed him a bit and got him into bed."
                $ fholly = "worried"
                h "There's no way I could've done that...!"
            if holly_guy:
                if ian_holly_dating:
                    v "Well, don't ignore Mark now that you hooked up with your crush. I went through all the trouble to introduce you!"
                    $ fholly = "n"
                    h "About that... He seems nice, but I'm not interested..."
                    v "You don't have to be. Just talk to him, it'll help you with that awkwardness of yours."
                    $ fholly = "sad"
                    h "Uh... okay."
                else:
                    v "Well, I'm sure you're happy I introduced you to Mark now! He told me you've already talked."
                    $ fholly = "blush"
                    h "Just a bit..."
                    v "You should meet him. You'll like him. And it'll help with that awkwardness of yours."
                    $ fholly = "sad"
                    h "Uh... Maybe one of these days..."
            else:
                $ fivy = "n"
                v "You should've let me introduce you to Mark. It would've been good for you."
                $ fholly = "n"
                h "I'm fine as is, thanks."
                v "Well, you need to work on that awkwardness of yours {i}pronto{/i}."
                $ fholly = "sad"
                h "Uh... Maybe..."
            $ fivy = "smile"
            v "Anyway girls, I gotta run."
            v "But we should meet this Sunday! I wanna do some shopping."
            $ fholly = "n"
            $ flena = "n"
            if lena_money > 3:
                l "Sure! It's been far too long since we went shopping together."
                $ fivy = "flirt"
                v "Not that long if you count our visit to the sex shop!"
            else:
                l "Sure, it could be fun. Not that I will do much shopping myself, but..."
                v "What's important is that we hang out!"
            v "Are you in, Holly?"
            $ fholly = "shy"
            h "Sure."
            v "Cool. See you girls this Sunday, then!"
            $ flena = "sad"
            l "Wait, will you come to the concert this Friday?"
            v "Oh, yeah, sure. But I won't be able to stay for the after-party, I have to work."
            $ flena = "smile"
            l "Of course. See you on Friday!"
            hide ivy with short
    else:
        show lena at rig
        show ivy at lef
        with short
        v "Hey, I gotta run. But I wanted to go shopping this Sunday. Let's go together!"
        if v7_holly_kiss and ian_lena_dating:
            l "Sure... I could use this chance to invite Holly and talk to her, see what's up."
            v "Yeah, why not. At least she's not a pain in the ass like Louise."
        else:
            l "I think Holly wanted to meet this Sunday..."
            v "You can bring her with us. At least she's not a pain in the ass like Louise."
        v "Anyway, see you on Sunday!"
        $ flena = "sad"
        l "Wait, will you come to the concert this Friday?"
        v "Oh, yeah, sure. But I won't be able to stay for the after-party, I have to work."
        $ flena = "smile"
        l "Of course. See you on Friday!"
        hide ivy with short

#####################################################################################################################################################################################################################################################################################################
## LOUISE HOME #####################################################################################################################################################################################################################################################################################################
#####################################################################################################################################################################################################################################################################################################

    play music "music/normal_day2.mp3" loop
    play sound "sfx/door_home.mp3"
    $ flena = "n"
    scene lenahomenight with long
    show lena with short
    "I got home after another long day."
    if v7_bbc == "lena":
        show lena at rig with short
        l "Hello..."
        $ flouise = "blush"
        $ louise_look = 1
        show louise at lef with short
        lo "Oh, hi there, Lena..."
        l "Are you cooking dinner?"
        lo "Yeah, but I... Tonight I will eat in my room. I want to finish this show I've been watching..."
        l "You can watch it on the TV, I don't mind."
        lo "No, um... I'll be more comfortable in my room."
        l "Alright."
        hide louise with short
        show lena at truecenter with move
        $ flena = "worried"
        l "She's still acting awkward around me..."
        "She had been like that since last Friday. I understood why, considering what happened at Ivy's place..."
        "Maybe I had gone too far. Jeremy was still Louise's boyfriend, after all... Even if their relationship wasn't exactly a solid one."
        "It seemed that Louise refused to see it, though."
    else:
        "All I wanted was to go to my room and lay down in peace."
    play sound "sfx/door.mp3"
    scene lenaroomnight with long
    $ lena_look = 4
    $ flena = "sad"
    show lenabra with short
    if ian_lena_dating and v7_holly_kiss:
        if lena_ian_love:
            "The knowledge of what happened between Ian and Holly was weighing on me. I was so disappointed..."
            if ian_holly_sex:
                if ian_holly_dating:
                    "So Ian had feelings for Holly, not for me? What kind of game was he playing?"
                    "In any case, he had proven to be the kind of guy I shouldn't get attached to. I wished I'd known sooner..."
                else:
                    "Ian had proven to be the kind of guy I shouldn't get attached to. I wished I'd known sooner..."
            else:
                "Maybe Ivy was right and I was taking things too seriously. It was only a kiss, after all. Still..."
            l "I don't want to think about that now."
        else:
            "What happened between Ian and Holly had me a bit uneasy. It was rather disappointing."
            if ian_holly_sex:
                "Ian had proven to be the same as most guys. He was just chasing sex."
                if ian_holly_dating:
                    "But maybe he had real feelings for Holly... In any case, it was something for them to sort out."
                else:
                    "He should've known better than to pull that thing off with a girl like Holly."
            else:
                "Maybe Ivy was right and I was taking things too seriously. It was only a kiss, after all."
                "Holly's point of view was probably different, though."
        l "I wish that was my only concern, but that's far from it."
    if lena_job_restaurant > 0:
        "The fact they were suddenly firing me from the restaurant was still sinking in. And only two days remained until I had to play live."
        "I knew I was making a bigger deal of it than I should, but I couldn't help it."
    else:
        "Only two days remained until I had to play live. I knew I was making a bigger deal of it than I should, but I couldn't help it."
    "And then there was what Ivy just told me. The fact that she had been working with Axel..."
    "The opportunity Axel provided was a big one indeed. I couldn't blame Ivy for wanting to jump on it."
    "She shouldn't bear the consequences of what had happened between Axel and me, and I knew she was on my side."
    if v6_axel_work or v4_axel_date:
        "I still wondered if I should follow Ivy's advice and contact Axel..."
        if axel_pictures_watch:
            $ flena = "blush"
            l "It doesn't sound like a good idea, but... Maybe..."
        else:
            l "It doesn't sound like a good idea, honestly."
    else:
        "I could've also benefited a lot from that opportunity, but I didn't want anything that came from Axel."
    $ flena = "sad"
    l "{i}Agh{/i}, it seems tonight will be a hard one, too. I can't fall asleep with all this stress..."
    if lena_louise_sex:
        $ flena = "n"
        l "Maybe I could knock on Louise's door and..."
        if lena_reject_louise:
            $ flena = "sad"
            l "No, that's not a good idea. I think it was a mistake sleeping with her in the first place."
            l "It's better if that doesn't happen again... I don't want to lead Louise to misconceptions."
            l "We're just friends."
        if v7_louise_sex:
            $ flena = "shy"
            l "Last time it helped me unwind. And I could use a good orgasm before going to bed."
            if louise_dominant:
                "I was sure Louise would be up for it, or for anything else I asked for that matter."
                if v7_louise_feet:
                    l "I made her lick my feet... It was weirdly pleasant."
                if toy_collar:
                    l "And I also bought that leash and collar... I was wondering if I could find an excuse to use it."
                    l "Will Louise agree to wear it? I have the feeling she would..."
            else:
                l "The possibility is certainly there..."
            menu:
                "Go to Louise's room":
                    $ renpy.block_rollback()
                    $ v8_louise_sex = True
                    $ flena = "flirtshy"
                    l "Yeah, let's do it."
                    if toy_collar and louise_dominant:
                        "I grabbed the collar and tucked it in my hand, conspicuously."
                    jump v8louisesex

                "Forget it":
                    $ renpy.block_rollback()
                    $ flena = "sad"
                    l "Nah, better forget it..."
                    l "She's been rather clingy lately. I don't want to deal with that this week."

        stop music fadeout 2.0
        l "I'll just try to get some sleep..."
        jump v8lenathursday
    if v7_bbc == "ivy" and louise_jeremy:
        if lena_louise > 6 or (v2_sleep_louise and lena_louise > 4):
            play sound "sfx/knock.mp3"
            "I decided to try and get some sleep when somebody knocked on my door."
            l "Yes?"
            $ flouise = "sad"
            $ louise_look = 1
            play sound "sfx/door.mp3"
            show lenabra at rig with move
            show louisebra at lef3 with short
            lo "Hey... Can I come in?"
            $ flena = "sad"
            l "Sure. What's wrong?"
            show louisebra at lef with move
            "Louise closed the door and sat on the bed next to me."
            lo "I can't sleep... I have been barely able to sleep since Friday, and I'm losing my mind."
            "Louise really looked tired, a bit paler than usual, and with slight dark circles around her eyes."
            "Breaking up with Jeremy had been hard on her, and no wonder..."
            if v7_game:
                l "Things got out of hand that night. We all drank too much and..."
                $ flouise = "serious"
                lo "Are you excusing what they did?"
                l "No, not at all. Ivy went too far this time."
                $ flouise = "sad"
                lo "I wish you could've stopped her..."
            else:
                l "What happened is really awful. Ivy went too far this time."
                lo "I wish you were there... I'm sure it wouldn't have happened..."
            l "I don't think what happened depended on me... Jeremy and Ivy had their own plans, it seems."
            $ flouise = "mad"
            lo "That asshole...! I trusted him... I loved him..."
            lo "He was special to me, and this is how he repays me!"
            $ flouise = "sad"
            lo "I don't know how I will manage to go on from now on. I thought this time I had found someone for real..."
            lo "But he betrayed me, lied to me..."
            l "I know how that feels, believe me."
            lo "If I had only known...! But I was too stupid to see..."
            $ flena = "worried"
            "I knew what kind of games Jeremy was playing behind Louise's back, but I decided not to tell her..."
            $ flena = "sad"
            "It was too late to regret that. I chose not to get involved because I thought it was the right thing to do."
            l "Love is blind... These things happen. Don't blame yourself."
            l "It's clear Jeremy was far from being {i}Mr. Right{/i}. It's better you found out now rather than later."
            l "Now you can move on."
            lo "Thanks, Lena... You're someone I can count on when I really need it..."
            l "Yeah, well... That's what friends are for, right?"
            lo "I... I don't know what I would do if I lost you..."
            stop music fadeout 2.0
            scene v5_louise1
            if lena_piercing1:
                show v5_louise1_p1
            elif lena_piercing2:
                show v5_louise1_p2
            show v5_louise1_eyes1
            with long
            "Suddenly, Louise leaned forward and kissed me."
            "And it was not a small, friendly kiss. She meant it."
            "My surprised reaction was to pull back."
            $ flena = "blush"
            $ flouise = "blush"
            scene lenaroomnight
            show lenabra2 at rig
            show louisebra at lef
            with long
            l "Louise..."
            lo "Lena, I... You're my closest friend, the only person who has been helping me..."
            lo "You're so important to me, you..."
            scene v5_louise1
            if lena_piercing1:
                show v5_louise1_p1
            elif lena_piercing2:
                show v5_louise1_p2
            show v5_louise1_eyes1
            with long
            "Louise kissed me yet again."
            menu:
                "{image=icon_friend.webp}Kiss her back":
                    $ renpy.block_rollback()
                    $ lena_louise_sex_late = True
                    jump v5kisslouise
                    label v8louisefirstend:
                        scene lenaroomnight with long
                        "Louise laid next to me and hugged me, both of us softly panting next to each other."
                        "It was easier to fall asleep after getting some sexual release..."
                        "I would take care of stress and troubles again tomorrow morning. But right now I felt relaxed, and tired..."
                        $ gallery_unlock_scene("CH05_S05")
                        jump v8lenathursday

                "Reject Louise's advances":
                    $ renpy.block_rollback()
                    $ lena_reject_louise = True
                    $ flena = "sad"
                    $ flouise = "blush"
                    scene lenaroomnight
                    show lenabra2 at rig
                    show louisebra at lef
                    with long
                    "And I pulled back yet again, avoiding her kisses."
                    l "I'm sorry, Louise, but... I don't feel this way about you..."
                    lo "I'm... I'm sorry, I shouldn't have..."
                    lo "Oh, I'm so embarrassed...!"
                    l "It's okay, Louise, I understand how you're feeling... But this isn't what you need..."
                    lo "I'm so stupid... I'm sorry...!"
                    $ flouise = "cry"
                    "Tears began rolling down her cheeks."
                    l "No, don't cry...!"
                    "My words were not gonna stop her tears. And if she felt like crying, better let her do it."
                    scene lenaroomnight with long
                    "I let her stay with me on my bed, where she cried herself to sleep."
                    "Needless to say, this didn't exactly help me relax and get some rest..."
                    jump v8lenathursday

        else:
            stop music fadeout 2.0
            jump v8lenathursday

    else:
        "No other choice but to try, though. I closed my eyes and hoped I could rest up a bit."
        stop music fadeout 2.0
        jump v8lenathursday

##LOUISE SEX SCENE (NEW) ################################################################################################################################################################################################################################################
label gallery_CH08_S03:
    if _in_replay:
        call setup_CH08_S03 from _call_setup_CH08_S03

label v8louisesex:
    stop music fadeout 2.0
    $ flena = "flirt"
    scene lenahomenight_dark
    show lenabra2
    with long
    play sound "sfx/knock.mp3"
    "I knocked on Louise's door and entered her room before getting an answer."
    "I knew I didn't need one."
    play sound "sfx/door.mp3"
    $ flouise = "smile"
    $ louise_look = 1
    scene louiseroomnight
    show lenabra2 at rig
    show louisebra at lef
    with long
    lo "Lena..."
    l "Hey, Louise. Are you busy?"
    lo "No. I was just reviewing a couple of e-mails from college before going to bed."
    l "Oh, are you going to sleep? I was feeling lonely tonight..."
    lo "We can sleep together if you're feeling lonely. I'd love that, in fact..."
    lo "I'm also a bit lonely these days..."
    l "I wasn't exactly thinking about sleeping together..."
    if lena_lust < 9:
        call xp_up('lust') from _call_xp_up_489
    if louise_dominant and toy_collar:
        hide lenabra2
        show lenabra at rig
        show hand_collar2 at rig
        with short
        $ flouise = "surprise"
        "I revealed the collar I had been hiding behind my back. Louise's expression was priceless."
        l "I was thinking about putting this around your neck and having a bit of fun together."
        $ flouise = "blush"
        lo "You want me to wear that...?"
        l "Don't you?"
        lo "Okay... We can try it. I've never..."
        l "Take off your clothes."
        $ flouise = "smile"
        lo "Yes..."
    elif louise_dominant:
        l "I was thinking about you taking off your clothes. Right now."
        $ flouise = "blush"
        lo "Oh..."
        $ flouise = "smile"
        lo "Of course..."
    else:
        l "I mean, not in that sense... If you catch my meaning."
        $ flouise = "smile"
        lo "Yes... Yes, I know what you mean."
        lo "And I want it too..."
    hide louisebra
    show louisenude at lef
    with short
    "Louise bared her body before me, waiting for me to take the lead."
# DOMINANT ############################################################################
    if louise_dominant:
        play music "music/sex_deep2.mp3" loop
        "I loved feeling in control."
        l "Get on the bed..."
        ## COLLAR
        if toy_collar:
            $ lena_louise_collar = True
            scene v8_louise1
            show v8_louise1_collar
            with long
            "I stripped down too and tied the collar around Louise's slender neck."
            "The black leather matched perfectly with her pale skin. A perfect complement."
            l "Mhhh... It suits you."
            lo "It's the first time I'm wearing something like this..."
            l "You're always wearing that choker. This feels like an upgrade, don't you think?"
            lo "As long as it's your hand holding the leash, it is... You make me so excited, Lena..."
            "I held Louise's chin in place, my lips teasingly close to hers."
            l "I want you to make me feel good, Louise."
            lo "I love making you feel good... I'll do anything..."
            l "Good girl."
            scene v8_louise2
            show v8_louise2_collar
            with long
            "I rewarded her with a kiss."
            "When my lips brushed against hers I felt Louise's entire body shiver, and she leaned forward, hungry for my kisses."
            "I held her back, my fingers still around her chin. I wanted to make clear I was in control of the situation..."
            "That was enough to show Louise how she was expected to behave. She stayed put, letting me kiss her playfully, subject to my will."
            "I had worn a collar like that before, but I had never been the one holding the leash."
            "Now that I was at the other end, I found the situation thrilling and exciting..."
            "I never knew taking the lead could be so fun."
            scene v8_louise3
            if lena_piercing1:
                show v8_louise3_p1
            elif lena_piercing2:
                show v8_louise3_p2
            with long
            l "Okay, now let me feel your tongue..."
            "I got comfortable and pulled at the leash, bringing Louise to me."
            "She immediately began kissing and fondling my body. Her lips and fingertips rolled over sensitive skin, making me tingle delightfully."
            lo "Lena... You're so beautiful... So soft... You smell so sweet..."
            "She kept praising me while devoting her caresses to my body."
            "When her tongue finally reached my nipples, I moaned with pleasure."
            play sound "sfx/ah1.mp3"
            "The feeling of Louise's warm body all over mine made me so aroused, the stress of the day melting away."
            "This was indeed a great way to unwind... And it could get even better."
            l "Get down. On your knees..."
            scene v8_louise4
            show v8_louise4_collar
            if lena_piercing1:
                show v8_louise4_p1
            elif lena_piercing2:
                show v8_louise4_p2
            with long
            "I sat on the edge of the bed, spreading my legs apart."
            "I didn't need to tell Louise what to do. A slight pull of the leash was all she needed."
            play sound "sfx/ah1.mp3"
            l "Oh, yes..."
            l "Get your face down there. Yeah, bury it right in there..."
            l "I want to feel that nice wet tongue..."
            "Louise lapped up my pussy diligently, looking at me with the eyes of a puppy who wants to be petted by her owner."
            "She wanted to make me feel good... She {i}needed{/i} to make me feel good."
            "And she was doing a good job... This was much better than masturbating, no doubt about it."
            "I had always noticed Louise's desire to please me. That's why it was so easy to be friends with her."
            "That desire to please me had now turned into something else entirely. Or I should rather say {i}I{/i} turned it into something else..."
            "Something lewd and incredibly hot..."
            $ flena = "slutshy"
            $ flouise = "smile"
            scene louiseroomnight
            show lenanude at rig
            show hand_collar at rig
            show louisenude at lef
            show louise_collar at lef
            with long
            l "Up. Let's continue this in my room."
            $ flouise = "blush"
            lo "But what if Stan sees..."
            l "He won't. Come, quick."
            play sound "sfx/door.mp3"
            stop music fadeout 2.0
            scene lenaroomnight with short
            "I pulled Louise's leash and made her follow me to my bedroom. There were some other toys I wanted to put to use..."
            show lenanude at rig
            show hand_collar at rig
            show louisenude at lef
            show louise_collar at lef
            with short
            "I searched in my drawer and found exactly what I was looking for."
            l "That'll do nicely..."
            l "Let's get back to what we were doing."
            play music "music/sex_raunchy.mp3" loop
            scene v8_louise5
            if lena_piercing1:
                show v8_louise5_p1
            elif lena_piercing2:
                show v8_louise5_p2
            with long
            "With another pull of the leash Louise got down to her knees again, and I spread my legs for her to continue pleasuring me."
            "This time, though, I handed her a tool to enhance her work."
            l "Here, use this while you eat me..."
            if toy_badboy:
                show v8_louise5_h3 with short
                play sound "sfx/ah1.mp3"
                "Louise slid the dildo into my dripping pussy. The wide head spread it out nicely... It was such a tight fit."
                l "Mhhh, yeah... That's it, nice and slow..."
                "I felt the ridges of the silicone cock grinding against the walls of my pussy."
            else:
                show v8_louise5_h2 with short
                play sound "sfx/ah1.mp3"
                "Louise slid the dildo into my dripping pussy. I felt the ridges of the silicone cock grinding against my inner walls."
                l "Mhhh, yeah... That's it, nice and slow..."
            "Louise's tongue on my clit made them pulsate, contracting with increasing pleasure."
            l "So good..."
            lo "Am I doing it okay? You're really liking it?"
            l "Yeah... You're such a good girl, Louise... I love it."
            "I grabbed Louise's head and pushed it into my crotch."
            l "Now do it faster... Lick me harder...!"
            "She responded eagerly, doing as instructed. I felt my climax coming closer and closer..."
            play sound "sfx/ah1.mp3"
            l "Ahhhhh!! Yesss!!!" with vpunch
            pause 0.5
            with vpunch
            pause 0.5
            with vpunch
            pause 0.5
            "My body was shaken by the intense release I had been looking for. Such bliss..."
            $ flouise = "blush"
            $ flena = "flirt"
            scene lenaroomnight
            show lenanude at rig
            show hand_collar at rig
            show louisenude at lef
            show louise_collar at lef
            with long
        # NO COLLAR
        else:
            scene v8_louise1
            show v8_louise1_necklace
            with long
            "I stripped down too and joined Louise."
            "She shivered when I ran my hand down her waist."
            "She tried to kiss me, but I held her chin in place, stopping her."
            l "Seems you were looking forward to this..."
            "My lips were teasingly close to hers."
            lo "Yes... I love being like this with you... No guy's made me feel like you do, Lena."
            l "If you want me to make you feel good you'll have to be a good girl and satisfy me."
            lo "I love satisfying you... I'll do anything..."
            "Just what I wanted to hear."
            scene v8_louise2
            show v8_louise2_necklace
            with long
            "I rewarded her with a kiss."
            "When my lips brushed against hers I felt Louise's entire body shiver, and she leaned forward once more, hungry for my kisses."
            "I held her back, my fingers still around her chin. I wanted to make clear I was in control of the situation..."
            "She stayed put, letting me kiss her playfully, subject to my will."
            "I never knew taking the lead could be so fun."
            scene v8_louise3 with long
            l "Okay, now let me feel your tongue..."
            "I got comfortable and gently brought Louise to me."
            "She immediately began kissing and fondling my body. Her lips and fingertips rolled over sensitive skin, making me tingle delightfully."
            lo "Lena... You're so beautiful... So soft... You smell so sweet..."
            "She kept praising me while devoting her caresses to my body."
            "When her tongue finally reached my nipples, I moaned with pleasure."
            play sound "sfx/ah1.mp3"
            "The feeling of Louise's warm body all over mine made me so aroused, the stress of the day melting away."
            "This was indeed a great way to unwind... And it could get even better."
            l "Get down. On your knees..."
            scene v8_louise4
            if lena_piercing1:
                show v8_louise4_p1
            elif lena_piercing2:
                show v8_louise4_p2
            with long
            "I sat on the edge of the bed, spreading my legs apart."
            "I didn't need to tell Louise what to do."
            play sound "sfx/ah1.mp3"
            l "Oh, yes..."
            l "Get your face down there. Yeah, bury it right in there..."
            l "I want to feel that nice wet tongue..."
            "Louise lapped up my pussy diligently, looking at me with the eyes of a puppy who wants to be petted by her owner."
            "She wanted to make me feel good... She {i}needed{/i} to make me feel good."
            "And she was doing a good job... This was much better than masturbating, no doubt about it."
            "I had always noticed Louise's desire to please me. That's why it was so easy to be friends with her."
            "That desire to please me had now turned into something else entirely. Or I should rather say {i}I{/i} turned it into something else..."
            "Something that was bringing me a lot of lewd pleasure."
            "I grabbed Louise's head and pushed it into my crotch."
            l "Do it faster... Lick me harder...! I'm about to...!"
            "She responded eagerly, doing as instructed. I felt my climax coming closer and closer..."
            play sound "sfx/ah1.mp3"
            l "Ahhhhh!! Yesss!!!" with vpunch
            pause 0.5
            with vpunch
            pause 0.5
            with vpunch
            pause 0.5
            "My body was shaken by the intense release I had been looking for. Such bliss..."
            $ flena = "slutshy"
            $ flouise = "smile"
            scene louiseroomnight
            show lenanude at rig
            show louisenude at lef
            with long
        # END
        l "That was just what I needed..."
        lo "Did you cum? Was it good?"
        l "Yeah, it was. It definitely was..."
        lo "Since I've been a good girl... Can I get some too?"
        if v6_louise_orgasm:
            lo "I loved it so much when you licked me down there last time..."
            lo "Did I earn my reward? Please..."
            l "Alright... I also like making you feel good, you know?"
        elif v5_louise_orgasm:
            lo "I loved it so much when you licked me down there the first time..."
            lo "I was hoping you'd do the same last time... But maybe now I've earned my reward?"
            lo "Please..."
            "Last time I was tired and feeling lazy... But it would be just cruel to deny Louise some satisfaction."
            l "Alright... I'll make you feel good, too."
        else:
            $ flouise = "sad"
            lo "You've never done anything to me... down there..."
            lo "I was hoping you could... give me a reward?"
            lo "Please..."
            "I had been rather selfish with Louise. I guess I could give her something in return this time."
            l "Alright... Since you're asking like that, I'll make you feel good, too."
        $ flouise = "happy"
        lo "Thanks...!"
        if toy_collar == False:
            l "Let's move it to my room. I have my toys there..."
            $ flouise = "blush"
            lo "Your toys?"
            l "Aren't we playing?"
            lo "But what if Stan sees..."
            l "He won't. Come, quick."
            play sound "sfx/door.mp3"
            scene lenaroomnight
            show lenanude at rig
            show louisenude at lef
            with short
            "I searched in my drawer and found exactly what I was looking for."
            l "That'll do nicely..."
            l "Let's get back to what we were doing."
        if toy_badboy:
            scene v8_louise6c
            if toy_collar:
                show v8_louise6_collar
            with long
            "I made Louise lay down on my bed and told her to spread her legs."
            if toy_collar:
                "The dildo was still soaked with my juices... It entered Louise's pussy with ease."
            else:
                "Louise's pussy was soaked and shiny... The dildo slid in with ease."
            play sound "sfx/ah1.mp3"
            lo "Ahhnn... Lena..."
            "This was a pretty thick dildo, but I barely felt any resistance pushing it in."
            l "Look at you... You're taking in this bad boy with no problem... You have such a naughty pussy."
            lo "That's because you make me so horny..."
            l "That's true... Your juices are dripping into my bed sheets!"
        else:
            if toy_dildo:
                scene v8_louise6b
            else:
                scene v8_louise6a
            if toy_collar:
                show v8_louise6_collar
            with long
            "I made Louise lay down on my bed and told her to spread her legs."
            if toy_collar:
                "The dildo was still soaked with my juices... It entered Louise's pussy with ease."
            else:
                "Louise's pussy was soaked and shiny... The dildo slid in with ease."
            play sound "sfx/ah1.mp3"
            lo "Ahhnn... Lena..."
            l "You like it, huh? Look at how wet you are..."
            lo "That's because you make me so horny..."
            l "Your juices are dripping into my bed sheets!"
        lo "Ahhh...! I'm..."
        lo "I'm sorry, I'll clean them for you! But please, don't stop!"
        l "Come on, then. What are you waiting for? Cum for me."
        show v8_louise6_hand with short
        lo "Yes, yes..."
        "Louise began rubbing her clit as I kept sliding the dildo in and out, making her legs tremble uncontrollably."
        "She looked so sexy and pitiful... It was hot and amusing."
        if toy_collar:
            "I tugged her leash, holding it tightly."
        l "Come on, give it to me. Cum!"
        "She moved her fingers in frantic circles over her clit, trembling more and more."
        lo "Yes, yes...!"
        play sound "sfx/ah4.mp3"
        lo "Yeees!! Ohhhh!!!" with vpunch
        pause 0.4
        with vpunch
        pause 0.4
        with vpunch
        pause 0.4
# NORMAL ############################################################################
    else:
        play music "music/sex_romantic.mp3" loop
        l "Let's play..."
        scene v8_louise1
        show v8_louise1_necklace
        with long
        "I stripped down too and joined Louise on the bed."
        "She shivered when I ran my hand down her waist."
        "She tried to kiss me, but I held her chin in place, stopping her."
        l "Not so fast..."
        "I felt like teasing her a bit."
        l "Seems you were looking forward to this..."
        lo "Yes... I love being like this with you... No guy's made me feel like you do, Lena."
        l "You give me way too much credit. But I have fun playing with you too, Louise..."
        scene v8_louise2
        show v8_louise2_necklace
        with long
        "I finally leaned in for a slow, nice kiss."
        "When my lips brushed against hers I felt Louise's entire body shiver, and she leaned forward once more, hungry for my kisses."
        "Our tongues danced together, enjoying each other's taste and touch."
        "It was so easy for me to take the lead when I was with Louise..."
        "No, it was inevitable, even. It was like she was asking me to."
        "And I was finding out how much fun that could be."
        scene v8_louise3 with long
        "I got comfortable and gently brought Louise to me."
        "She immediately began kissing and fondling my body. Her lips and fingertips rolled over sensitive skin, making me tingle delightfully."
        lo "Lena... You're so beautiful... So soft... You smell so sweet..."
        "She kept praising me while devoting her caresses to my body."
        "When her tongue finally reached my nipples, I moaned with pleasure."
        play sound "sfx/ah1.mp3"
        "The feeling of Louise's warm body all over mine made me so aroused, the stress of the day melting away."
        "This was indeed a great way to unwind... And it was about to get even better."
        l "Louise... I need you to eat me out."
        scene v8_louise4
        if lena_piercing1:
            show v8_louise4_p1
        elif lena_piercing2:
            show v8_louise4_p2
        with long
        "Louise was quick to oblige."
        "I sat on the edge of the bed, spreading my legs apart, and she dove right in."
        play sound "sfx/ah1.mp3"
        l "Oh, yes..."
        l "I want to feel that nice wet tongue..."
        "Louise lapped up my pussy diligently. She really wanted to make me feel good..."
        "In fact, it was almost like she {i}needed{/i} to make me feel good."
        "And she was doing a good job... This was much better than masturbating, no doubt about it."
        "Louise and I had moved into a new level of intimacy in our friendship, and I was enjoying it quite a bit..."
        l "I'm almost there... Do it a bit faster... Lick me harder...!"
        "She responded eagerly, following my instructions. I felt my climax coming closer and closer..."
        play sound "sfx/ah1.mp3"
        l "Ahhhhh!! Yesss!!!" with vpunch
        pause 0.5
        with vpunch
        pause 0.5
        with vpunch
        pause 0.5
        "My body was shaken by the intense release I had been looking for. Such bliss..."
        $ flena = "slutshy"
        $ flouise = "smile"
        scene louiseroomnight
        show lenanude at rig
        show louisenude at lef
        with long
        l "Thanks... That was just what I needed."
        lo "Did you cum? Was it good?"
        l "Yeah, it was. It definitely was..."
        lo "Um... Can I get some, too?"
        if v6_louise_orgasm:
            lo "I loved it so much when you licked me down there last time..."
            l "Alright... I also like making you feel good, you know?"
        elif v5_louise_orgasm:
            lo "I loved it so much when you licked me down there the first time..."
            lo "I was hoping you'd do the same last time... But maybe now you can..."
            "Last time I was tired and feeling lazy... But it would be just cruel to deny Louise some satisfaction."
            l "Of course... I'll make you feel good, too."
        else:
            $ flouise = "sad"
            lo "You've never done anything to me... down there..."
            lo "I was hoping you could... make me feel good."
            "I had been rather selfish with Louise. I guess I could give her something in return this time."
            l "Of course... You never asked before, so I didn't know if I should..."
        $ flouise = "happy"
        lo "Thanks...!"
        l "Let's move it to my room. I have my toys there..."
        $ flouise = "blush"
        lo "Your toys?"
        l "Yes, you'll like it! Besides, aren't we playing?"
        lo "But what if Stan sees..."
        l "He won't. Come, quick."
        play sound "sfx/door.mp3"
        scene lenaroomnight with short
        show lenanude at rig
        show louisenude at lef
        with short
        "I searched in my drawer and found exactly what I was looking for."
        l "That'll do nicely... Lay down on the bed for me..."
        if toy_badboy:
            scene v8_louise6c
            with long
            l "That's it... Spread your legs..."
            "Louise's pussy was soaked and shiny... The dildo slid in with ease."
            play sound "sfx/ah1.mp3"
            lo "Ahhnn... Lena..."
            "This was a pretty thick dildo, but I barely felt any resistance pushing it in."
            l "Look at you... You're taking in this bad boy with no problem... You have such a naughty pussy."
            lo "That's because you make me so horny..."
        else:
            if toy_dildo:
                scene v8_louise6b
            else:
                scene v8_louise6a
            with long
            l "That's it... Spread your legs..."
            "Louise's pussy was soaked and shiny... The dildo slid in with ease."
            play sound "sfx/ah1.mp3"
            lo "Ahhnn... Lena..."
            l "You like it, huh? Look at how wet you are..."
            lo "That's because you make me so horny..."
        l "So eating me out makes you horny?"
        lo "Yes... All of you... Ahnnn!"
        "I dug the dildo in and out, twisting it rhythmically."
        l "Come on, cum for me..."
        show v8_louise6_hand with short
        lo "Yes, yes..."
        "Louise began rubbing her clit as I kept sliding the dildo back and forth, making her legs tremble uncontrollably."
        "I couldn't deny it was a pretty sexy sight!"
        l "Come on, give it to me. Cum!"
        "She moved her fingers in frantic circles over her clit, trembling more and more."
        lo "Yes, yes...!"
        play sound "sfx/ah4.mp3"
        lo "Yeees!! Ohhhh!!!" with vpunch
        pause 0.4
        with vpunch
        pause 0.4
        with vpunch
        pause 0.4

    stop music fadeout 2.0
    $ flena = "smile"
    $ flouise = "happy"
    scene lenaroomnight with short
    show lenanude at rig
    show louisenude at lef
    if toy_collar and louise_dominant:
        show hand_collar at rig
        show louise_collar at lef
    with long
    l "Satisfied?"
    lo "Yes... Yes, absolutely. That was great, Lena...!"
    l "Nice. I'm satisfied too. Time to go to bed."
    $ flouise = "smile"
    lo "Do you want us to sleep together here or in my room?"
    $ flena = "n"
    l "You know I like to have the bed for myself. Otherwise, I can't get a good night's sleep, and I need it!"
    $ flouise = "sad"
    lo "Oh, sure..."
    if ian_lena_sex and lena_robert_sex:
        lo "You sleep with Ian and Robert in the same bed, though..."
        l "Well, where else could I make them sleep? On the couch?"
        lo "I guess that makes sense..."
    elif ian_lena_sex:
        lo "You sleep with Ian in the same bed, though..."
        l "Well, where else could I make him sleep? On the couch?"
        lo "I guess that makes sense..."
    elif lena_robert_sex:
        lo "You sleep with Robert in the same bed, though..."
        l "Well, where else could I make him sleep? On the couch?"
        lo "I guess that makes sense..."
    l "Good night Louise. It was fun!"
    $ flouise = "n"
    lo "Good night."
    play sound "sfx/door.mp3"
    hide louisenude
    hide louise_collar
    hide hand_collar
    with short
    show lenanude at truecenter with move
    hide lenanude with long
    "It was easier to fall asleep after getting some sexual release."
    "I would take care of stress and troubles again tomorrow morning. But right now I felt relaxed, and tired..."
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH08_S03")
    jump v8lenathursday


#####################################################################################################################################################################################################################################################################################################
## LENA THURSDAY #####################################################################################################################################################################################################################################################################################################
#####################################################################################################################################################################################################################################################################################################

label v8lenathursday:
    call calendar(_day="Thursday") from _call_calendar_72

    $ lena_look = 1
    $ flena = "n"
    $ fed = "n"
    play music "music/normal_day4.mp3" loop
    scene cafe with long
    show lenawork with short
    "It was hard trying to focus during the next morning."
    if v8_louise_sex:
        "That midnight shag with Louise helped me fall asleep, but my mind was still drifting toward other things."
        $ flena = "sad"
        "I had too many things on my plate..."
    elif lena_louise_sex_late:
        $ flena = "sad"
        "What happened last night with Louise was... unexpected. I wondered if I should've rejected her advances."
        "It was clear she was in need of an emotional crutch to deal with Jeremy's betrayal, and I had been there for her."
        "That led to a situation that transcended simple friendship, but if that was what she needed to feel better..."
        $ flena = "sad"
        l "I hope it doesn't become one more thing to add to my list of concerns. My plate is way too full..."
    elif louise_jeremy and lena_reject_louise:
        $ flena = "worried"
        "What happened last night with Louise was... unexpected. And when I saw her this morning I could feel the atmosphere between us was a bit tense."
        "No wonder why..."
        "She was hurt because of what had happened with Jeremy and had tried to find solace in me, I understood that. We talked it over..."
        $ flena = "sad"
        "But it was clear she wasn't happy that I had rejected her advances..."
    else:
        $ flena = "sad"
        "I had been having trouble sleeping these past few days, and I felt really tired."
        "I had too many things on my plate..."
    if cafe_help:
        if cafe_music:
            "Tomorrow was the day of truth. The café was filled with fliers announcing the event we would be hosting."
            "Ed and Molly seemed pretty enthusiastic about it. They were hoping my idea would help the café."
            "They had faith in me and wanted to see me succeed, too..."
            $ flena = "worried"
            "But what if I failed?"
            "What if I messed up, or nobody showed up? Or even worse, what if they left after feeling disappointed?"
        else:
            "Tomorrow was the day of truth. The café was filled with fliers announcing my performance at the record store."
            "Ed and Molly had insisted on showcasing them to promote the event. They really wanted me to succeed..."
            $ flena = "worried"
            "Everybody wanted me to succeed. They were sure I would."
            "But What if I failed?"
            "What if I messed up, or nobody showed up? Or even worse, what if they left after feeling disappointed?"
    else:
        "Tomorrow was the day of truth. The café was filled with fliers announcing my performance at the record store."
        "Ed and Molly had insisted on showcasing them to promote the event. They wanted to help, I guess."
        "Everybody had been encouraging me, telling me there was no way I wouldn't succeed."
        $ flena = "worried"
        "But What if I failed?"
        "What if I messed up, or nobody showed up? Or even worse, what if they left after feeling disappointed?"
    show lenawork at rig with move
    show ed at lef with short
    ed "Hey, Lena, are you feeling alright? You look rather distressed..."
    menu:
        "{image=icon_friend.webp}I have a lot on my mind" if lena_ed > 5:
            $ renpy.block_rollback()
            $ v8_compliment_ed = True
            l "It's just... I have a lot on my mind."
            ed "If you need to get anything off your chest, I'm here to listen."
            l "A lot has been going on lately, and I'm worried about tomorrow's concert."
            ed "You'll do fine..."
            l "That's what everybody tells me! But what if I don't?"
            ed "It'll be fine either way."
            $ flena = "sad"
            l "No, it won't."
            ed "What's the worst thing that can happen if your performance is bad?"
            if cafe_music:
                l "Who knows... For starters, you might have to close the café."
                ed "That's not your responsibility, Lena. You can't burden yourself with that."
            else:
                l "Who knows... I don't wanna even think about it."
                ed "Then don't."
            ed "Even if you do badly, which is unlikely, you will still get plenty more chances to get it right."
            ed "The only way to do it flawlessly is to constantly improve by doing, and failing..."
            $ flena = "n"
            l "I know, I know... \"Perfection is the enemy of progress\". That's what Emma told me."
            $ flena = "shy"
            l "I'm ashamed of all the encouragement and pep talks I'm getting... I feel so silly."
            $ fed = "smile"
            ed "Nothing wrong with being a bit silly. You'll see things from a fresh perspective once the concert is over, I'm sure of it."
            ed "So try not to worry too much. Just remember: many a little makes a mickle!"
            $ flena = "happy"
            l "It's the first time I've heard that saying."
            ed "Well, it's old, like me."

        "I'm just tired":
            $ renpy.block_rollback()
            $ flena = "n"
            l "It's nothing. I'm just a bit tired, is all."
            ed "You know you can tell us if you need to take a rest or you need some kind of help..."
            l "Thanks, Ed, but I can manage. A lot has been going on recently, but I know I just need to take things one step at a time..."
            ed "That's good advice to give to yourself. Life can be overwhelming at times, and what you have to focus on is what's in front of you, nothing else."
            ed "Where I grew up we had this saying... \"Many a little makes a mickle!\""
            $ flena = "smile"
            l "Thanks, Ed. I should get back to work."

        "It's nothing":
            $ renpy.block_rollback()
            $ flena = "n"
            l "It's nothing. I was just... spacing out. Sorry."
            ed "If you say so..."
            l "Yeah. Don't worry."
            if lena_ed > 4:
                "I didn't feel like discussing my problems with Ed. It was a personal matter."
            else:
                "I had no interest in telling Ed about my problems. It was none of his business."

    $ flena = "n"
    $ fmolly = "n"
    $ fed = "n"
    show ed at lef3
    show lenawork at truecenter
    with move
    show molly at rig3 with short
    mo "Is everything alright over here?"
    l "Yeah."
    mo "Ed, did you tell Lena the news?"
    ed "No, I was about to."
    l "What news?"
    ed "I've finally found someone who's interested in the café."
    ed "Well, it was them who contacted me, but it seems someone's finally willing to take it from us..."
    $ flena = "sad"
    l "Oh, so you're gonna sell it?"
    if cafe_help:
        ed "Nothing's decided, yet. You know Molly and I aren't exactly happy about the idea..."
        if cafe_music:
            mo "Besides, we still have to see how tomorrow goes! After all the trouble you've been through..."
            ed "It would be great if your idea works and we get our customers back. That is, if you're still willing to help us, Lena."
        else:
            mo "Besides, it's too soon to decide. The life drawing event was a great success!"
            ed "Yes. If we keep that up, maybe we won't have to sell after all..."
            ed "That is, if you're still willing to help us, Lena."
        $ flena = "n"
        l "Of course... I'm glad you have options, in case my plan doesn't bear fruit."
        ed "I'll tell those guys we are not ready to sell yet, but that we will consider their offer, just in case."
        ed "It's not that good of an offer, to be frank... But it's the only one we've got so far."
        mo "You're doing so much for us, Lena. And I can't find a way to repay you..."
        mo "But if it came to selling the café to these people, I want them to hire you as part of the deal."
        $ flena = "n"
        mo "Hopefully, they'll even raise your salary!"
        l "Thanks, Molly..."
    else:
        $ fed = "sad"
        ed "It's not that good of an offer, to be frank... But it's the only one we've got so far."
        ed "And all things considered..."
        l "I see... I'm glad for you."
        $ fmolly = "sad"
        mo "Well, nothing's decided yet. You know we don't want to sell if we can avoid it..."
        ed "Yeah, well, that's not realistic any more. We need something, and considering the situation..."
        mo "I just wish we could get more! As you said, that offer was not stellar at all."
        ed "Better that than nothing..."
        l "I guess that means I should start looking for another job."
        $ fmolly = "n"
        mo "Not if we can help it. If we're gonna sell to these people, I want them to hire you as part of the deal."
        $ flena = "n"
        mo "Hopefully, they'll even raise your salary!"
        $ fed = "n"
        ed "Let's not get ahead of ourselves..."
        l "Thanks for taking me into consideration, Molly."
        "I appreciated Molly's goodwill, but that was no guarantee of anything."
        "I should really start looking for something else..."
##TATTOO
    scene cafe with long
    "I clocked in my working hours and left for the record store."
    "Emma was waiting for me there to do our last rehearsal."
    if v7tattoobuy and lena_tattoos:
        scene street with long
        show lena with short
        "While I was on my way I got a call."
        play sound "sfx/ring.mp3"
        pause 1
        hide lena
        show lena_phone
        with short
        pause 1
        if jess_bad:
            show phone_jess_bad at lef3
        else:
            show phone_jess_good at lef3
        with short
        js "Hey, it's Jess. I have your tattoo designs ready."
        $ flena = "happy"
        l "Oh, that's great!"
        js "When do you want to get the tat done?"
        l "When do you have an open spot?"
        js "Sunday sounds good?"
        l "Sure!"
        js "Okay, drop by the studio during the afternoon and we'll get you inked."
        l "Awesome! I can't wait."
        if jess_bad:
            js "See you then. And bring the money."
        else:
            js "See you then. Come prepared!"
        l "I will. Bye, Jess."
        hide phone_jess_bad
        hide phone_jess_good
        with short
        $ flena = "smile"
        hide lena_phone
        show lena
        with short
        "I would finally get my first tattoo done... I had been looking forward to it."
        $ flena = "sad"
        l "I hope it's not too painful... And not too expensive."
        $ flena = "smile"
        l "I'm excited, though!"
##EMMA
    scene recordstore with long
    "I spent the next two hours going over the songs I had picked with Emma."
    $ flena = "smile"
    $ femma = "n"
    show lena at rig
    show emma at lef
    with short
    e "That went great!"
    l "Yeah... Even better than the first time."
    e "That's only natural, ha ha."
    l "It's given me a bit more confidence... Thanks, Emma."
    show lena at rig3
    show emma at lef3
    with move
    show record_owner with short
    martin "That sounded great, girls."
    e "Thanks, Martin!"
    if cafe_music:
        l "And thanks for letting me rehearse in your store..."
    else:
        l "And thanks for setting up my concert in your store..."
    martin "That's what we're here for. It's not just about selling CDs and making money..."
    martin "I always wanted this store to help develop the music scene in our city."
    martin "It's a small one, but full of artistic talent."
    if cafe_music:
        martin "If you want to play here sometime, just let us know."
        l "Thank you... I'm afraid I could drive your customers away, though!"
    else:
        l "I'm really thankful for the opportunity! I hope I don't drive your customers away..."
    martin "I sincerely doubt that could happen, ha ha."
    scene streetnight with long
    show lena at rig
    show emma at lef
    with short
    l "Thanks again for everything you're doing, Emma."
    e "Oh, stop it already! I'm tired of hearing that, ha ha."
    menu:
        "Chat with Emma":
            $ renpy.block_rollback()
            $ v8_emma_chat = True
            l "I hope you'll at least let me thank you somehow. I owe you a dinner, at the very least."
            $ femma = "smile"
            e "I can't say no to food... But having the chance to play again is my reward!"
            $ femma = "n"
            e "I almost forgot how fun this is. Damn, I miss being a teenager sometimes..."
            l "It had its perks, I guess."
            e "All you had to worry about was passing your tests and having fun with your friends..."
            l "But everything felt way more dramatic than it needed to be."
            $ femma = "smile"
            e "For most people, but maybe I was a weird teenager. Not a care in the world, back then..."
            e "And most people say I haven't changed that much since then."
            l "You look like the kind of person who doesn't really give a damn about what others think."
            $ femma = "n"
            e "I guess not. It never bothered me what people did, as long as they let me do my thing."
            e "Maybe because of that I never really got along with girls my age..."
            l "Really? You're one of the friendliest people I've ever met!"
            e "So they say! But I suppose I was a bit of a tomboy. Maybe I still am."
            e "I just didn't fit in with the girls. Their dolls, boy bands, and fashion magazines..."
            e "I much preferred playing ball with the guys, joking around, and beating the drums with Ian, Perry, and the crew."
            l "Sounds like you guys were really close."
            e "We shared a lot of stuff back then. I guess we were just a bunch of misfits who ganged up together, but that's always the best kind of gang if you ask me!"
            e "Now everyone's doing their thing, but we're still pretty close, which is really cool."
            $ flena = "n"
            l "That sounds great... Sometimes I miss that. Having a group of friends..."
            l "I've only kept a couple of friends from back in the day. Ivy from high school and Louise from college..."
            e "What happened to everyone else?"
            l "The usual, I guess. Some drifted away, I fought with a few, and I lost interest in most of them."
            $ flena = "sad"
            l "I think I've always had trouble when it comes to people and friendships."
            e "Really? You seem to be a really nice girl."
            $ flena = "n"
            l "Oh, I'm a difficult person, believe me..."
            l "But that's enough about me. I don't like to yap about myself."
            e "Oh, you should. Feels good to open up, you know?"
            $ flena = "smile"
            l "It certainly does."
            if lena_emma < 10:
                call friend_xp('emma') from _call_friend_xp_651
                $ lena_emma += 1
            e "Anyway, it's getting late! See you tomorrow, Lena!"

        "Get going":
            $ renpy.block_rollback()
            l "I should get going..."
            e "Sure."
            e "See you tomorrow, Lena!"

    l "Yes! Good night."
    hide emma with short
    show lena at truecenter with move
## IAN CALL
    "I was about to get home when my phone rang."
    play sound "sfx/ring.mp3"
    $ v8iancall = 0 # what answer Lena gives on the phone, to match Ian's side
    if ian_lena_dating and v7_holly_kiss:
        $ flena = "sad"
        "It was Ian."
        hide lena
        show lena_phone
        with short
        l "Yes?"
        show phone_ian_worried at lef3 with short
        i "Hey, Lena..."
        l "Hey."
        i "So... how's it going?"
        if lena_ian_love:
            $ flena = "serious"
            l "I hope this is not one of those small talk calls to test the waters..."
            i "No, sorry. It isn't."
        else:
            $ flena = "n"
            l "I'm fine, but I doubt you called just to ask me that..."
            i "Yeah, sorry..."
        hide phone_ian_worried
        show phone_ian_sad at lef3
        i "I've called to apologize again for that mess."
        $ flena = "sad"
        i "You're right, I've put you and Holly in a delicate situation. I didn't mean to."
        hide phone_ian_sad
        show phone_ian at lef3
        i "I talked to her, and it is as you said. She feels weird about all this... She's worried you're mad at her."
        l "I'm not..."
        i "I already told her. If there's someone you should be mad at it's me, not her. She did nothing wrong."
        i "She wants to talk to you, but doesn't know how to take the first step... I think you should reach out to her, clear stuff up."
        $ flena = "n"
        i "She'll listen to you."
        l "Alright. I will..."
        if ian_holly_sex:
            if ian_holly_dating:
                l "Be careful about what you do from now on, though. I don't want you hurting Holly."
                i "I don't want to hurt her either."
                $ flena = "n"
                l "I hope so."
            else:
                i "And again, I'm sorry for... thinking with my dick instead of my brain."
                if lena_ian_love:
                    $ flena = "worried"
                    l "Well, it seems to be awfully common with you guys..."
                    $ flena = "sad"
                    l "But at least you're apologizing."
                else:
                    l "You're not the first one and won't be the last one to do so..."
                    l "But at least you're apologizing. It's something."
                i "I'm afraid it's the only thing I can do now."
                $ flena = "serious"
                l "I hope that's enough for Holly, but I doubt it..."
                i "I already talked to her. We've straightened things up."
                l "If you say so..."
        elif v7_holly_kiss:
            $ flena = "serious"
            l "Still, I think you shouldn't have kissed her if you were not interested in her..."
            i "I know, it was just... We were opening up to each other, and then she kissed me, and I..."
            i "Maybe I should've been clearer about my... feelings and all that, but I wasn't expecting her to make a move on me..."
            $ flena = "sad"
            if lena_ian_love:
                l "It seems neither of us was especially clear with our feelings. Maybe that's why things got like this..."
            else:
                l "I think none of us is too fond of openly discussing our feelings..."
            l "At least you had the good sense to stop it there, even if hurting her was inevitable at this point."
            i "I still feel responsible for what happened. I hope I didn't hurt you or caused too much trouble..."
            l "Well, it's..."
            menu v8menuhollylena:
                "{image=icon_friend.webp}It was only a kiss":
                    $ renpy.block_rollback()
                    $ v8iancall = 1
                    $ ian_lena_over = False
                    $ flena = "blush"
                    l "Well, as you said... it was only a kiss."
                    i "Yeah, I..."
                    i "I knew it would be wrong to take it further. It was a mistake letting it happen in the first place."
                    $ flena = "shy"
                    l "Holly likes you... and I can see why."
                    i "She's great, but... Well, you know how I feel."
                    if lena_ian_love:
                        $ flena = "blush"
                        l "To be honest... I'm not entirely sure."
                        i "Oh..."
                    else:
                        $ flena = "n"
                        l "Yeah..."

                "I'm concerned about Holly's feelings":
                    $ renpy.block_rollback()
                    $ v8iancall = 2
                    $ flena = "sad"
                    l "I'm just concerned about Holly's feelings. I don't think she has the experience or fortitude to deal with this."
                    i "I have the feeling she's stronger than we think. But you're right."
                    i "She doesn't deserve to be hurt."
                    l "We can agree on that."

                "{image=icon_mad.webp}You were a jerk":
                    $ renpy.block_rollback()
                    $ v8iancall = 3
                    $ flena = "serious"
                    l "Honestly, you were a jerk."
                    hide phone_ian
                    show phone_ian_sad at lef3
                    i "Alright... I'm not gonna argue with that."

        $ flena = "n"
        hide phone_ian
        hide phone_ian_sad
        show phone_ian at lef3
        i "Anyway... I just wanted to give some words of encouragement."

    elif v7_holly_kiss and ian_holly_dating == False:
        $ flena = "sad"
        "It was Ian."
        hide lena
        show lena_phone
        with short
        l "Yes?"
        show phone_ian_smile at lef3 with short
        i "Hey, Lena. How's it going?"
        $ flena = "serious"
        l "Hey..."
        l "I heard what happened this weekend from Holly."
        hide phone_ian_smile
        show phone_ian at lef3
        i "Oh... Yeah..."
        if ian_holly_sex:
            l "What's wrong with you? You sleep with her and then tell her it was a mistake the next morning?"
            if ian_lena > 4:
                call friend_xp('lena', -2) from _call_friend_xp_652
            elif ian_lena > 3:
                call friend_xp('lena', -1) from _call_friend_xp_653
            i "I know that's not what she wanted to hear, but I had to be honest with my feelings..."
            l "You could've been honest with your feelings before boning her!"
            l "But I guess you didn't want to let the opportunity of getting laid slip... I thought you were not one of those guys, Ian."
            i "Hey... I know I messed up, alright? But we both wanted it to happen."
            l "Sure. But maybe give all the information to Holly beforehand, so she can decide?"
            i "It's not like I had all the information, either. In fact, Holly never said she wanted anything {i}serious{/i} with me."
            l "You'd have to be blind not to see it..."
            i "Maybe. I guess I'm not so perceptive, after all."
            i "In any case, I already talked with Holly and we cleared things up. I know I made things difficult for her, and I'm sorry."
            i "I hope I can do better in the future."
            $ flena = "n"
            l "I hope so, too."
            i "So, if you're done grilling me... I was calling to give some words of encouragement."
        else:
            $ flena = "sad"
            l "You shouldn't have kissed her if you were not interested in her..."
            i "I know, it was just... We were opening up to each other, and then she kissed me, and I..."
            i "Holly's great, but I have the feeling she's looking for something I can't give her right now."
            i "Maybe I should've been clearer about that, but I wasn't expecting her to make a move on me..."
            l "Well... At least you had the good sense to stop it there, even if hurting her was inevitable at this point."
            i "Yeah... I felt awful. But I had to be honest with my feelings."
            $ flena = "n"
            l "That's good."
            hide phone_ian
            show phone_ian_smile at lef3
            i "Anyway, I was calling to give you some words of encouragement..."
    else:
        $ flena = "smile"
        "It was Ian."
        hide lena
        show lena_phone
        with short
        l "Hi!"
        show phone_ian_smile at lef3 with short
        i "Hey, Lena! How's it going?"
        if ian_holly_dating:
            $ flena = "flirtshy"
            l "Hey, Ian! I heard from Holly about what happened this weekend..."
            hide phone_ian_smile
            show phone_ian_shy at lef3
            i "Oh, so she told you? Damn, news flies around here..."
            $ flena = "happy"
            l "Congratulations!"
            i "Congratulations? For what?"
            l "For finally being honest with your feelings for each other!"
            hide phone_ian_shy
            show phone_ian_smile at lef3
            i "She's such a nice girl... I really like her."
            l "She's great. And she's head over heels for you."
            i "Well, let's see how this plays out..."
            $ flena = "serious"
            l "Don't you dare hurt her!"
            hide phone_ian_smile
            show phone_ian at lef3
            i "That's furthest from my intention. But as I told her, we should take things slow, one step at a time."
            $ flena = "n"
            l "Of course. As long as you take care of each other's feelings, everything should be alright."
            hide phone_ian
            show phone_ian_smile at lef3
            i "Anyway, if you're done with the gossip, I was calling to give some words of encouragement!"
    hide phone_ian
    hide phone_ian_smile
    show phone_ian_smile at lef3
    i "Tomorrow's the big day!"
    l "Yeah... I just finished rehearsing with Emma."
    if lena_emma > 6:
        l "She's actually a really cool girl! So energetic and positive..."
        l "Even though she barely knew anything about me she has been so open and helpful..."
    else:
        l "She's been really helpful, especially considering she didn't know me at all..."
    i "Emma's just like that."
    l "You're lucky to have a friend like her."
    i "Well, now she's your friend, too."
    l "I guess she is... Thanks for introducing us."
    if ian_lena_dating and v7_holly_kiss == False:
        hide phone_ian_smile
        show phone_ian_happy at lef3
        i "I'm happy to open my social circle to you. We're all a bunch of geeks and weirdos, but we're nice people, ha ha."
        $ flena = "happy"
        l "Indeed you are. I'm very happy we talked that day at the café..."
        i "Well, that was thanks to you. I doubt I would've had the courage to talk to such a stunning girl."
        $ flena = "shy"
        l "Someone as charming as you? Come on."
        if ian_lena_love:
            i "In any case, I'm also really happy you decided to talk to me that day. It's one of the best things that's happened to me in... a very long time."
            l "You're exaggerating."
            i "Not at all."
        else:
            i "In any case, I'm also really happy you decided to talk to me that day. It's been so fun since then!"
        i "And I'm pumped to see you perform, finally!"
        $ flena = "shy"
        l "Shhh, I don't need any more pressure!"
        i "Sorry, sorry. But it's true that I'm looking forward to that. Perry and I will be there."
        $ flena = "smile"
        l "Thank you for supporting me, Ian."
        hide phone_ian_happy
        show phone_ian_smile at lef3
        i "Of course. Rest up and see you tomorrow! I'm sure you'll crush it!"
        l "Bye! And thanks for calling."
        hide phone_ian_smile
        hide lena_phone
        show lena
        with short
        "I hung up with a smile on my face."
        if lena_ian_love:
            l "I'm lucky to have him in my life."
        else:
            $ flena = "smile"
            l "He's such a great guy."
    elif ian_lena_dating and v7_holly_kiss:
        i "I'm happy to help."
        hide phone_ian_smile
        show phone_ian at lef3
        i "So, about tomorrow... Is it okay if Perry and I show up at the concert?"
        $ flena = "n"
        l "Yeah, of course. No problem."
        hide phone_ian
        show phone_ian_smile at lef3
        i "Cool... I want to show my support, but I wasn't sure you'd be comfortable."
        if ian_lena_over:
            l "It's okay. We're clear on where we stand, now. No more misunderstandings."
            hide phone_ian_smile
            show phone_ian at lef3
            i "Yeah..."
        else:
            l "As I said, don't worry. We're fine."
            i "Glad to know."
        i "Anyway, try resting up and see you tomorrow. I'm sure you'll crush it."
        l "Bye!"
        hide phone_ian_smile
        hide phone_ian
        hide lena_phone
        show lena
        with short
        if ian_lena_over:
            $ flena = "sad"
            "Hanging around Ian was bound to be uncomfortable, at least for a while, but I had made my choice, and he had made his."
            l "This sucks..."
            l "I just want things to be calm for a change."
        else:
            "I wondered if I was doing the right thing, after what happened with Holly..."
            "But I guess I didn't want to give up on Ian just yet."
            l "Let's see how things go..."
    else:
        i "I'm happy to help."
        hide phone_ian_smile
        show phone_ian_happy at lef3
        i "And I'm pumped to see you perform, finally!"
        $ flena = "shy"
        l "Shhh, I don't need any more pressure!"
        i "Sorry, sorry. But it's true that I'm looking forward to that. Perry and I will be there."
        if ian_holly_sex and ian_holly_dating == False:
            $ flena = "n"
            l "Alright! I'm still mad about what happened with Holly, though."
            hide phone_ian_happy
            show phone_ian at lef3
            i "Yeah, I know. It was my mistake. I'll try to make up for it."
            l "Good! Anyway, thanks for calling. I appreciate it!"
            hide phone_ian
            hide lena_phone
            show lena
            with short
            "Ian was a good guy, but he still had to mature a bit..."
            "In any case, I was glad I had met him."
        else:
            $ flena = "smile"
            l "Thank you for supporting me, Ian."
            hide phone_ian_happy
            show phone_ian_smile at lef3
            i "It's the least I can do. Rest up and see you tomorrow!"
            l "Bye! And thanks for calling."
            hide phone_ian_smile
            hide lena_phone
            show lena
            with short
            if v7_holly_kiss and ian_holly_dating == False:
                "I was glad I had met Ian. He was a nice guy... even though he messed up sometimes."
            else:
                "Ian was such a nice guy. I was glad I had met him."

#####################################################################################################################################################################################################################################################################################################
## LENA THURSDAY NIGHT - SEXTING #####################################################################################################################################################################################################################################################################################################
#####################################################################################################################################################################################################################################################################################################

    label gallery_CH08_S04:
        if _in_replay:
            call setup_CH08_S04 from _call_setup_CH08_S04

    stop music fadeout 2.0
    scene lenahomenight with long
    $ flena = "n"
    $ lena_look = "towel"
    play sound "sfx/shower.mp3"
    scene v1_lena_shower
    if lena_piercing1:
        show v1_lena_shower_p1
    if lena_piercing2:
        show v1_lena_shower_p2
    if lena_tattoo2:
        show v1_lena_shower_t2
    if lena_tattoo3:
        show v1_lena_shower_t3
    with long
    pause 1
    "The first thing I did after getting home was take a long and relaxing shower."
    scene lenaroomnight
    show lenaunder
    with long
    if lena_robert_dating:
        "I had left my phone on the desk. When I picked it up I saw Robert had sent me a text."
        nvl clear
        r_p "{i}Hey babe, are you still mad? {image=emoji_sad.webp}{/i}"
        if v7_bbc == "lena":
            play sound "sfx/sms.mp3"
            "I was about to reply to him when I got another message."
            jump v8sextingjeremy
        else:
            jump v8menusexting
    else:
        if ian_lena_dating and v7_holly_kiss == False:
            "I had left my phone on the desk. As I picked up my phone to check my notifications I thought about Ian."
            if lena_ian_love:
                "That call had really put a smile on my face. I kinda wished he was here right now..."
            else:
                "That call had cheered me up. I wished he was here right now..."
            if lena_mike_dating:
                $ flena = "shy"
                "I would really enjoy Mike's company, too..."
                "I hadn't heard from him since last Friday when I saw him at the club..."
                if v7_mike_bj:
                    $ flena = "slutshy"
                    "Giving him a blowjob in the DJ booth was probably one of the hottest and wildest things I had ever dared to do..."
                    "And I had loved every minute of it."
        elif lena_mike_dating:
            "As I picked up my phone to check my notifications I thought about Mike."
            "I hadn't heard from him since last Friday when I saw him at the club..."
            if v7_mike_bj:
                $ flena = "slutshy"
                "Giving him a blowjob in the DJ booth was probably one of the hottest and wild things I had ever dared to do..."
                "And I had loved every minute of it."
    # JEREMY
    if v7_bbc == "lena":
        play sound "sfx/sms.mp3"
        "My phone suddenly vibrated in my hand when a text message rolled in."
        label v8sextingjeremy:
            $ flena = "worried"
        l "I don't know whose number this is..."
        "I opened it."
        play music "music/shooting.mp3" loop
        nvl clear
        un_p "{i}Hey Lena, what's up? How are things?{/i}"
        l_p "{i}Who's this? I don't have this number saved.{/i}"
        if v3_bbc:
            un_p "{i}Oh, sorry. It's Jeremy. Louise used my phone to call you so I had your number.{/i}"
        else:
            un_p "{i}Oh, sorry. It's Jeremy. I got your number from Ivy. I hope you don't mind...{/i}"
        $ flena = "flirt"
        l "So Jeremy's texting me? How come...?"
        "The answer came with his next text."
        play sound "sfx/sms.mp3"
        nvl clear
        j_p "{i}I just wanted to ask, how are things with Louise lately?{/i}"
        $ flena = "sad"
        l_p "{i}They have been rather tense, to be honest... {image=emoji_disgust.webp}{/i}"
        j_p "{i}Why? Did she say something??{/i}"
        l_p "{i}No, it's the opposite. I feel like she's been avoiding talking to me.{/i}"
        j_p "{i}I see... She's been a bit weird with me too since we played that game, but I was worried she was mad at you or something.{/i}"
        $ flena = "n"
        l_p "{i}No, I don't think she is. I think she's probably a bit weirded out by what happened that night {image=emoji_ups.webp}{/i}"
        j_p "{i}It was fucking weird {image=emoji_ups.webp} {image=emoji_laugh.webp}{/i}"
        menu:
            "{image=icon_lust.webp}Flirt with Jeremy" if lena_fty_bbc:
                $ renpy.block_rollback()
                $ v8_jeremy_flirt = True
                $ flena = "flirtshy"
                l_p "{i}Weird but pretty hot... don't you think? {image=emoji_tongue.webp}{/i}"
                "Just thinking about what happened made my body tingle. I had been hypnotized by that big black cock..."
                if ian_lena_love:
                    j_p "{i}It was definitely hot, but I'm not sure we should've done it {image=emoji_ups.webp} {/i}"
                    l_p "{i}Don't worry about Louise. She'll get over it.{/i}"
                    j_p "{i}It's not just about Louise. You and Ian are kind of dating, aren't you?{/i}"
                    if lena_ian_love:
                        $ flena = "blush"
                        l_p "{i}I wouldn't say we're dating... Just getting to know each other.{/i}"
                        j_p "{i}I guess, but still... I'm finding it rather hard to tell him.{/i}"
                        $ flena = "surprise"
                        l "What?"
                        $ flena = "blush"
                        l_p "{i}There's no need to tell him. In fact, I think it's better if you didn't.{/i}"
                        if ian_jeremy > 5:
                            j_p "{i}I don't know about that. He's my bro {image=emoji_disgust.webp} {/i}"
                        else:
                            j_p "{i}I don't know about that. He's my friend, after all...{/i}"
                        $ flena = "n"
                        l_p "{i}Don't give it more importance than it had. It was just a game!{/i}"
                        j_p "{i}Yeah, that makes sense.{/i}"
                        $ flena = "flirt"
                        l_p "{i}A game you enjoyed quite a lot if I recall correctly...{/i}"
                    else:
                        $ flena = "n"
                        l_p "{i}No, we're not dating. We've just been hooking up! {image=emoji_crazy.webp}{/i}"
                        j_p "{i}Are you sure he sees it that way? {image=emoji_roll.webp} {/i}"
                        l_p "{i}He should.{/i}"
                        j_p "{i}If you say so, then that's how it is. Still, I'm finding it rather hard to tell him.{/i}"
                        $ flena = "n"
                        l_p "{i}Listen, let's better keep what happened between us, okay?{/i}"
                        l_p "{i}It's not something other people need to know. What happened at Ivy's place stays at Ivy's place!{/i}"
                        l_p "{i}And I doubt Louise would feel comfortable if other people knew...{/i}"
                        j_p "{i}I guess you're right. It was just a game, after all.{/i}"
                        $ flena = "flirt"
                        l_p "{i}Exactly! The one you enjoyed quite a lot if I recall correctly...{/i}"
                    if v7_bbc_cum:
                        j_p "{i}I sure did... Damn, you really made me cum really fast! {image=emoji_crazy.webp}{/i}"
                        $ flena = "slutshy"
                        l_p "{i}From one to ten, how much did you like it? {image=emoji_flirt.webp}{/i}"
                        j_p "{i}I rate those sexy lips eleven out of ten {image=emoji_glasses.webp}{image=emoji_cum.webp}{/i}"
                    else:
                        j_p "{i}I sure did... But I ended up blue-balled! {image=emoji_crazy.webp}{/i}"
                        $ flena = "slutshy"
                        l_p "{i}Maybe if we had more time I would've managed to finish it... {image=emoji_flirt.webp}{/i}"
                        j_p "{i}Or if Louise let you use more than just your hands...{/i}"
                else:
                    if v7_bbc_cum:
                        j_p "{i}It sure was... Damn, you really made me cum really fast! {image=emoji_crazy.webp}{/i}"
                        $ flena = "slutshy"
                        l_p "{i}I guess that meant you really liked it {image=emoji_flirt.webp}{/i}"
                        j_p "{i}It meant exactly that. I fucking loved those sexy lips... {image=emoji_glasses.webp}{image=emoji_cum.webp}{/i}"
                    else:
                        j_p "{i}It sure was... But I ended up blue-balled! {image=emoji_crazy.webp}{/i}"
                        $ flena = "slutshy"
                        l_p "{i}Maybe if we had more time I would've managed to finish it... {image=emoji_flirt.webp}{/i}"
                        j_p "{i}Or if Louise let you use more than just your hands...{/i}"
                "I was getting horny recalling that night. The experience had been too short, though."
                "I craved more... and I was absolutely sure Jeremy did too."
                "But just to be sure..."
                hide lenaunder
                show lenanude2
                with short
                show lenanude2 at right with move
                play sound "sfx/camera.mp3"
                show v8_sexting1 with short
                pause 1
                "I laid down on my bed and snapped a quick selfie for Jeremy."
                $ lena_lena_pics.append("v8_sexting1.webp")
                l_p "{i}Is it getting hot in here or it's just me? {image=emoji_crazy.webp}{/i}"
                "His response didn't keep me waiting."
                play sound "sfx/sms.mp3"
                j_p "{i}It definitely got a lot hotter just now {image=emoji_surprise.webp} Hot damn babe.{/i}"
                if v7_bbc_cum:
                    l_p "{i}So what were you saying? That you loved my sexy lips?{/i}"
                    j_p "{i}Best I've ever felt {image=emoji_glasses.webp}{/i}"
                    l_p "{i}I loved wrapping them around your cock too...{/i}"
                else:
                    l_p "{i}So what were you saying? That you wished I used more than just my hands?{/i}"
                    j_p "{i}I would've loved to feel those sexy lips {image=emoji_glasses.webp}{/i}"
                    l_p "{i}I would've loved to wrap them around your cock...{/i}"
                "I was getting so turned on..."
                $ flena = "slut"
                l_p "{i}Can I see it? {image=emoji_devil.webp}{/i}"
                j_p "{i}What, you want me to send you a dick pic? {image=emoji_surprise.webp}{/i}"
                l_p "{i}That's right.{/i}"
                j_p "{i}I don't know about that...{/i}"
                l_p "{i}Come on, don't make me beg {image=emoji_roll.webp}{/i}"
                j_p "{i}Well alright, you asked for it...{/i}"
                play sound "sfx/sms.mp3"
                hide v8_sexting1
                show v8_jeremy_sext1
                with short
                pause 1
                $ lena_jeremy_pics.append("v8_jeremy_sext1.webp")
                $ flena = "shy"
                l "Oh my God...!"
                "I bit my lip looking at the image that appeared on my phone screen."
                "It was so huge, and it wasn't even completely hard..."
                $ flena = "slut"
                l "Looks like Jeremy's down to play... It's my turn now."
                play sound "sfx/camera.mp3"
                hide v8_jeremy_sext1
                show v8_jeremy_sext2 with short
                pause 1
                "I laid down on my bed and snapped a quick selfie for Jeremy."
                $ lena_jeremy_pics.append("v8_jeremy_sext2.webp")
                l_p "{i}It's so big... I wonder how much of it would I be able to fit in here {image=emoji_crazy.webp}{/i}"
                l_p "{i}Would you like to give it a try?{/i}"
                $ flena = "flirtshy"
                l "I wonder if I'm taking it too far, but I'm so horny...!"
                "I waited eagerly for Jeremy's response. I could only imagine the face he'd make when seeing the picture I just sent him..."
                l "..."
                $ flena = "smile"
                l "... ..."
                $ flena = "sad"
                l "... ... ..."
                $ flena = "worried"
                l "... ... ... ..."
                hide v8_jeremy_sext2 with short
                show lenanude2 at truecenter with move
                l "He's not responding."
                l_p "{i}Hey, are you still there?{/i}"
                "I typed this but just before sending it, I heard Louise getting out of her room."
                play sound "sfx/door.mp3"
                "She was talking over the phone, and I could faintly hear her words."
                lo "{i}... but you said you get out of the gym at nine. Why weren't you picking up?{/i}"
                lo "{i}You can talk with your friends any other time! I told you I wanted to talk over the phone with you tonight, baby...!{/i}"
                "Her voice dimmed as she walked into the kitchen."
                $ flena = "sad"
                l "Looks like she's talking with Jeremy... She ruined my fun!"
                l "Damn, I was getting so much into it..."
                lo "{i}... what do you mean a guy showed up looking for a fight?{/i}"
                lo "{i}What? You KO'ed him!?{/i}"
                "Louise came back, but I could only hear that bit before she went back into her room."
                play sound "sfx/door.mp3"
                l "So I guess that's as far as my chat with Jeremy goes tonight..."
                "Maybe it was for the best... I was letting my lustful instincts take over, and that was leading me down a very reprehensible path..."
                "However, those instincts had been awakened and now I felt like a kid whose candy just got stolen."
                $ gallery_unlock_scene("CH08_S04")
                if (ian_lena_dating and v7_holly_kiss == False) or lena_mike_dating or lena_robert_dating:
                    $ flena = "flirt"
                    l "Thankfully, I have someone else to play with..."
                    jump v8menusexting
                else:
                    l "{i}Ahhh{/i}, I can't believe myself. I should be thinking about other things..."
                    if v8_stalkfap_dm1 == 1 or v8_stalkfap_dm2 == 1:
                        "Before going to bed I got into my Stalkfap account to see if I had gotten any responses."
                        jump v8stalkfapresponse
                    else:
                        $ flena = "flirtshy"
                        l "I guess I'll find another porn video to watch before going to bed... Last time was so much fun."
                    $ renpy.end_replay()
                    jump v8lenafriday

            "Pull back":
                $ renpy.block_rollback()
                $ flena = "sad"
                l_p "{i}We went too far. It wasn't right...{/i}"
                if ian_lena_love:
                    j_p "{i}That's what I told you, but you and Ivy insisted on it {image=emoji_ups.webp}{/i}"
                    $ flena = "blush"
                    l "He's right... I got really carried away that night."
                    $ flena = "flirtshy"
                    l "But how to resist...?"
                    $ flena = "sad"
                    l "No, I can't act like that, it's way too reckless. I messed up."
                    l_p "{i}Let's pretend it never happened, okay?{/i}"
                    j_p "{i}Have you told Ian about it?{/i}"
                    l_p "{i}No, but since we're gonna act like this never happened, and it won't happen again, there's no need to tell him.{/i}"
                    $ flena = "worried"
                    l_p "{i}Unless you already did.{/i}"
                    j_p "{i}No, but I was meaning to... {/i}"
                    l_p "{i}As I said, there's no need anymore. What happened at Ivy's place stays at Ivy's place! {image=emoji_shy.webp}{/i}"
                    j_p "{i}Fine by me.{/i}"
                else:
                    j_p "{i}Let's blame it on Ivy, shall we?{/i}"
                    $ flena = "smile"
                    l_p "{i}Let's, ha ha. But we can't let her drag us into her nasty games anymore.{/i}"
                    j_p "{i}I'll try to stay strong {image=emoji_strong.webp}{/i}"
                    $ flena = "n"
                    l_p "{i}Good. By the way let's keep what happened between us, okay?{/i}"
                    j_p "{i}okay, if that's important to you.{/i}"
                    l_p "{i}Yeah, I value my privacy. And I doubt Louise would feel comfortable if other people knew...{/i}"
                    j_p "{i}I get you. My lips are sealed.{/i}"
                j_p "{i}Sooo... Everything good?{/i}"
                l_p "{i}Yeah, just got out of the shower. Gonna do some stuff now.{/i}"
                j_p "{i}Cool, I won't keep you. Talk to you some other time! {image=emoji_wink.webp}{/i}"
                l_p "{i}Sure {image=emoji_kiss.webp}{/i}"
                $ flena = "sad"
                l "{i}Ahhh{/i}, I can't believe my propensity for getting into awkward situations. Ivy's a bad influence..."
                $ flena = "shy"
                l "But it really was so much fun."
                if (ian_lena_dating and v7_holly_kiss == False) or lena_mike_dating or lena_robert_dating:
                    $ flena = "flirt"
                    l "Thinking about it is making me kinda horny..."
                    l "Thankfully, I have someone to play with..."
                    jump v8menusexting
                else:
                    $ flena = "n"
                    l "But as I just told Jeremy, \"what happens at Ivy's place stays at Ivy's place\"."
                    l "No point in thinking about it too much. I have other things in my mind right now..."
                    if v8_stalkfap_dm1 == 1 or v8_stalkfap_dm2 == 1:
                        "Before going to bed I got into my Stalkfap account to see if I had gotten any responses."
                        jump v8stalkfapresponse
                    $ renpy.end_replay()
                    jump v8lenafriday

    $ config.menu_include_disabled = False
    $ greyed_out_disabled = True
    menu v8menusexting:
        "Text Ian" if ian_lena_dating and v7_holly_kiss == False:
            $ renpy.block_rollback()
            $ v8_lena_sexting = "ian"
            if persistent.include_disabled:
                $ config.menu_include_disabled = True
            $ greyed_out_disabled = False
            $ flena = "flirt"
            if lena_robert_dating:
                "I was in no rush to respond to Robert's message. I was thinking about Ian instead..."
                if lena_ian_love:
                    "That call had really put a smile on my face. I kinda wished he was here right now..."
                else:
                    "That call had cheered me up. I wished he was here right now..."
            nvl clear
            if v8_jeremy_flirt:
                "I decided to text Ian. I hoped I could get the response I was looking for..."
                hide lenaunder
                show lenanude2
                with short
                show lenanude2 at right with move
                play sound "sfx/sms.mp3"
                show v8_sexting1 with short
                pause 1
                "I sent him the same pic I had just sent to Jeremy, adding a little caption."
                l_p "{i}I'm here thinking about you and that sweet phone call from before... {image=emoji_shy.webp}{/i}"
                "I was sure it'd light a fire in him."
            else:
                if v7_bbc == "ivy":
                    play music "music/shooting.mp3" loop
                "I decided to text Ian. I felt like getting a bit naughty with him..."
                hide lenaunder
                show lenanude2
                with short
                show lenanude2 at right with move
                play sound "sfx/camera.mp3"
                show v8_sexting1 with short
                pause 1
                "I laid down on my bed and snapped a quick selfie for Ian."
                $ lena_lena_pics.append("v8_sexting1.webp")
                l_p "{i}I'm here thinking about you and that sweet phone call from before... {image=emoji_shy.webp}{/i}"
                "I was sure it'd light a fire in him..."
            if lena_lust < 6:
                call xp_up('lust') from _call_xp_up_490

        "Text Robert" if lena_robert_dating:
            $ renpy.block_rollback()
            $ v8_lena_sexting = "robert"
            if persistent.include_disabled:
                $ config.menu_include_disabled = True
            $ greyed_out_disabled = False
            $ v8_sexting_full = True
            $ flena = "n"
            "Robert was awaiting my response."
            "I was pissed about the whole restaurant business, but Robert was not at fault. And I didn't want to wallow on that subject."
            $ flena = "flirt"
            if v8_robert_public:
                "I could use some fun, just like last time... It was so hot having sex in the dining area!"
            else:
                "I could use some fun..."
            hide lenaunder
            show lenanude2
            with short
            if v8_jeremy_flirt:
                show lenanude2 at right with move
                play sound "sfx/sms.mp3"
                show v8_sexting1 with short
                pause 1
                "I sent Robert the same pic I had just sent to Jeremy, adding a little caption."
                l_p "{i}I'm not mad. Does this picture prove it? {image=emoji_flirt.webp}{/i}"
                "I would undoubtedly get from him the reaction I was looking for..."
            else:
                if v7_bbc == "ivy":
                    play music "music/shooting.mp3" loop
                show lenanude2 at right with move
                play sound "sfx/camera.mp3"
                show v8_sexting1 with short
                pause 1
                "I laid down on my bed and snapped a quick selfie for Robert."
                $ lena_lena_pics.append("v8_sexting1.webp")
                l_p "{i}I'm not mad. Does this picture prove it? {image=emoji_flirt.webp}{/i}"
                "I was sure that'd get him going..."
            if lena_lust < 6:
                call xp_up('lust') from _call_xp_up_491

        "Text Mike" if lena_mike_dating:
            $ renpy.block_rollback()
            $ v8_lena_sexting = "mike"
            $ v8_sexting_full = True
            if persistent.include_disabled:
                $ config.menu_include_disabled = True
            $ greyed_out_disabled = False
            $ flena = "flirt"
            if lena_robert_dating:
                "I was in no rush to respond to Robert's message. I was thinking about Mike instead..."
                "I hadn't heard from him since last Friday when I saw him at the club..."
                if v7_mike_bj:
                    $ flena = "slutshy"
                    "Giving him a blowjob in the DJ booth was probably one of the hottest and wildest things I had ever dared to do..."
                    "And I had loved every minute of it."
            if v8_jeremy_flirt:
                "I decided to text Mike. I hoped I could get the response I was expecting from him..."
            else:
                if v7_bbc == "ivy":
                    play music "music/shooting.mp3" loop
                "I decided to text Mike. I felt like getting a bit naughty with him..."
            hide lenaunder
            show lenanude2
            with short
            nvl clear
            if v8_jeremy_flirt:
                show lenanude2 at right with move
                play sound "sfx/sms.mp3"
                show v8_sexting1 with short
                pause 1
                "I sent him the same pic I had just sent to Jeremy, adding a little caption."
                l_p "{i}What are you doing? I'm bored and I feel like playing... {image=emoji_dance.webp}{/i}"
                "I was looking to get a very specific reaction from him..."
            else:
                show lenanude2 at right with move
                play sound "sfx/camera.mp3"
                show v8_sexting1 with short
                pause 1
                $ lena_lena_pics.append("v8_sexting1.webp")
                "I laid down on my bed and snapped a quick selfie for him."
                l_p "{i}What are you doing? I'm bored and I feel like playing... {image=emoji_dance.webp}{/i}"
                "I was looking to get a very specific reaction from him..."
            if lena_lust < 7:
                call xp_up('lust') from _call_xp_up_492
            l "I hope he's not busy with his girlfriend..."

        "Go to bed":
            $ renpy.block_rollback()
            if persistent.include_disabled:
                $ config.menu_include_disabled = True
            $ greyed_out_disabled = False
            $ flena = "n"
            l "I should probably go over tomorrow's songs before going to bed."
            l "That's all I can really think about right now..."

    # STALKFAP
    if v8_stalkfap_dm1 == 1 or v8_stalkfap_dm2 == 1:
        if v8_lena_sexting != "n":
            $ flena = "n"
            hide v8_sexting1 with short
            show lenanude2 at truecenter with move
            "While I waited for a response I decided to check my Stalkfap messages."
        else:
            "Before going to bed I got into my Stalkfap account to see if I had gotten any responses."
        label v8stalkfapresponse:
            if v8_lena_sexting == "n" and v7_bbc == "ivy":
                play music "music/shooting.mp3" loop
            $ flena = "n"
        if v8_stalkfap_dm1 == 1:
            l "The polite guy texted back..."
            "I re-read our conversation."
            guy "{i}\"I've been following you for a while and the content keeps getting better and better. I really enjoy seeing your beautiful posts.\"{/i}"
            l "{i}Thank you! I will keep posting stuff, so I hope you'll keep supporting me {image=emoji_heart.webp}{/i}"
            guy "{i}\"Thank you for responding {image=emoji_love.webp} I will no doubt keep supporting you!\"{/i}"
            guy "{i}\"In fact, I'd like to tip you some extra money, if you're comfortable with it.\"{/i}"
            $ flena = "smile"
            l "{i}Of course! I'd be really grateful {image=emoji_smile.webp}{/i}"
            guy "{i}Great! Here you go {image=emoji_heart.webp}{/i}"
            if lena_money < 4:
                call money(1) from _call_money_65
                $ flena = "happy"
                l "Nice! This will surely help."
            else:
                play sound "sfx/moneyup.mp3"
                "He sent some money. It wasn't much, but everything adds up."
            menu:
                "Reward him with a selfie":
                    $ renpy.block_rollback()
                    $ v8_stalkfap_dm1 = 2
                    $ flena = "smile"
                    l "He deserves a reward."
                    hide lenaunder
                    show lenanude2
                    with short
                    show lenanude2 at right with move
                    play sound "sfx/camera.mp3"
                    show v8_sexting2_comp with short
                    pause 1
                    $ lena_lena_pics.append("v8_sexting2_comp")
                    "I took a sexy selfie and sent it to him."
                    l "{i}Thank you so much! This is for you {image=emoji_lips.webp}{/i}"
                    guy "{i}Oh my God {image=emoji_love.webp} {image=emoji_love.webp} {image=emoji_love.webp}{/i}"
                    guy "{i}You leave me breathless... How can such a beautiful girl exist?{/i}"
                    l "{i}I'm glad you liked it!{/i}"
                    if lena_lust < 7:
                        call xp_up('lust') from _call_xp_up_493
                    guy "{i}You're a treasure. I can't wait for what you post next!{/i}"
                    l "{i}Thank you {image=emoji_kiss.webp} {/i}"
                    l "Easy money, as Ivy said..."
                    hide v8_sexting2_comp with short
                    show lenanude2 at truecenter with move

                "Thank him":
                    $ renpy.block_rollback()
                    $ flena = "smile"
                    l "{i}Thank you so much {image=emoji_lips.webp} {image=emoji_lips.webp}{/i}"
                    guy "{i}My pleasure. I can't wait for what you post next!{/i}"
                    l "Easy money, as Ivy said..."

            if v8_stalkfap_dm2 == 1:
                l "I also got a response from that other guy requesting custom content..."
        if v8_stalkfap_dm2 == 1:
            $ flena = "n"
            if v8_stalkfap_dm1 == False:
                l "That guy who was requesting custom content wrote back..."
            if v7_dance_provoke == 3:
                guy "{i}I want to see your pussy and asshole. I loved that last video where you twerked for us, but I'd like to get a better view, just for me.{/i}"
            elif v7_dance > 1:
                guy "{i}I want to see your pussy and asshole. You showed us everything but that in your last video, you're such a tease.{/i}"
            else:
                guy "{i}I want to see your pussy and asshole. You were only teasing in your last video.{/i}"
            guy "{i}As I said, I'm willing to pay a premium.{/i}"
            $ config.menu_include_disabled = False
            $ greyed_out_disabled = True
            menu:
                "{image=icon_money.webp}Accept his request" if lena_lust < 6:
                    $ renpy.block_rollback()
                    $ flena = "blush"
                    l "Am I really doing this...?"
                    hide lenaunder
                    show lenanude2
                    with short
                    $ flena = "shy"
                    l "Well, I guess so... I want that money."
                    call xp_up ('lust') from _call_xp_up_494
                    jump v8stalkfaprequest

                "{image=icon_lust.webp}Accept his request" if lena_lust > 5:
                    $ renpy.block_rollback()
                    l "I guess I can do that..."
                    hide lenaunder
                    show lenanude2
                    with short
                    label v8stalkfaprequest:
                        show lenanude2 at right with move
                    "I got up and placed the phone on the chair, getting a good angle."
                    l "I really need to buy a phone holder..."
                    show v8_sexting3 with short
                    pause 1
                    $ lena_lena_pics.append("v8_sexting3.webp")
                    "I hit the record button and turned my back on the camera, giving my subscriber a good angle of what he wanted to see."
                    "I wiggled my hips a bit, trying to look playful and seductive."
                    "After teasing him a bit I gave him the full show."
                    $ flena = "shy"
                    hide v8_sexting3
                    show v8_sexting4
                    with short
                    pause 1
                    $ lena_lena_pics.append("v8_sexting4.webp")
                    "I spread my butt cheeks apart, giving him a good look at my asshole."
                    "It was a rather shameful display... But this was what my paying customers were after."
                    "If I could get some extra money out of it, I was happy to do it."
                    "It was just showing off, after all, and there was a screen between that guy and me. That unbridgeable distance made me feel confident."
                    hide v8_sexting4 with short
                    $ flena = "n"
                    show lenanude2 at truecenter with move
                    l "Okay, that's enough. Let's send him the video..."
                    if persistent.include_disabled:
                        $ config.menu_include_disabled = True
                    $ greyed_out_disabled = False

                "Ignore him":
                    $ renpy.block_rollback()
                    $ v8_stalkfap_dm2 = 0
                    $ flena = "worried"
                    l "This guy sounds like a big creep."
                    $ flena = "n"
                    l "Sorry buddy, but you're gonna get ignored."
                    if persistent.include_disabled:
                        $ config.menu_include_disabled = True
                    $ greyed_out_disabled = False

##### IAN
    if v8_lena_sexting == "ian":
        play sound "sfx/sms.mp3"
        $ flena = "flirtshy"
        if v8_stalkfap_dm1 == 1 or v8_stalkfap_dm2 == 1:
            l "Oh, Ian just replied..."
            "I closed the Stalkfap app and opened his text message."
        else:
            l "Ian replied..."
        show lenanude2 at right with move
        show v8_sexting1 with short
        pause 1
        nvl clear
        i_p "{i}I will call you every day if this is what I get in return {image=emoji_love.webp}{/i}"
        l_p "{i}Oh, you like it?{/i}"
        i_p "{i}Like you don't know. I love it. I can't believe how beautiful you are.{/i}"
        l_p "{i}I'm sure that's what you say to all the girls who send you sexy pics.{/i}"
        i_p "{i}Oh, so you think I get a lot of pics like these?{/i}"
        l_p "{i}You don't?{/i}"
        if ian_charisma > 4:
            if v6_ian_selfie:
                i_p "{i}None of them come close to the ones you send me. They're my favorite, by far {image=emoji_wink.webp}{/i}"
            else:
                i_p "{i}None of them come close to the one you sent me. It's my favorite, by far {image=emoji_wink.webp}{/i}"
            $ flena = "happy"
            l "This guy...!"
        else:
            i_p "{i}Not really... Yours are the only ones I want to see, though {image=emoji_shy.webp}{/i}"
        if ian_lena < 12:
            call friend_xp('lena') from _call_friend_xp_654
        $ flena = "flirt"
        l_p "{i}Is that so? {image=emoji_flirt.webp}{/i}"
        if v8_stalkfap_dm1 == 2:
            "He deserved another selfie. I sent him the one I had just taken a moment ago..."
        else:
            "I rolled around on my bed and took another selfie for him."
            play sound "sfx/camera.mp3"
            $ lena_lena_pics.append("v8_sexting2_comp")
        hide v8_sexting1
        show v8_sexting2_comp
        with short
        pause 1
        i_p "{i}Are you sure you're even human?{/i}"
        l_p "{i}What's that supposed to mean?{/i}"
        if ian_charisma > 4 or ian_wits > 4:
            i_p "{i}It means I can't take my eyes off the screen now. I might stare at it until the sun comes up.{/i}"
        else:
            i_p "{i}It means you're way too perfect!{/i}"
        l_p "{i}You're surely exaggerating {image=emoji_laugh.webp}{/i}"
        i_p "{i}In all seriousness, you're the most beautiful girl I've ever met, Lena. I can't believe how lucky I am.{/i}"
        $ flena = "n"
        "I wondered if he was being honest. I had the feeling he was, though I couldn't help but wonder what that ex-girlfriend of his was like."
        "Emma said they were perfect for each other, and losing her was really tough on Ian... Was he still thinking about her?"
        l "What the hell am I doing thinking about that right now? Ian's clearly head over heels for me..."
        $ flena = "flirtshy"
        l "I can use that to get some goodies for myself..."
        l_p "{i}If you like my pics so much I surely can get some in return...{/i}"
        i_p "{i}You want me to send you a picture?{/i}"
        l_p "{i}It's not fair I'm the only one sending them, is it?{/i}"
        if v6_ian_selfie:
            i_p "{i}I sent you one that one time...{/i}"
            l_p "{i}Just one! Come on, don't make a girl beg!{/i}"
        i_p "{i}Okay, wait a second...{/i}"
        if v8_stalkfap_dm2 == 1:
            $ flena = "n"
            "While I waited a notification popped up on my screen. That guy from Stalkfap had responded to my video."
            guy "{i}Hot damn, babe, what a delicious asshole you have. I'd let you sit on my face and lick it clean until you cum.{/i}"
            l "{i}I'm glad you liked it!{/i}"
            guy "{i}You like getting your asshole licked?{/i}"
            l "{i}It depends.{/i}"
            guy "{i}I'm sure I'd make you enjoy it.{/i}"
            "Before I could respond again Ian sent his selfie."
        else:
            "After a couple of minutes, I got what I asked for."
        play sound "sfx/sms.mp3"
        $ flena = "shy"
        hide v8_sexting2_comp
        show v8_sexting_ian
        with short
        pause 1
        $ lena_ian_pics.append("v8_sexting_ian.webp")
        i_p "{i}Here you go {image=emoji_ups.webp}{/i}"
        l_p "{i}What's with that face? {image=emoji_laugh.webp}{/i}"
        i_p "{i}Isn't that how you're supposed to do it? I'm afraid I'm rather new in the sexy selfie business...{/i}"
        $ flena = "happy"
        l_p "{i}You have a lot to learn, indeed... Allow me to say the duck face doesn't suit you!{/i}"
        i_p "{i}Okay, let me try again...{/i}"
        if ian_lust > 4:
            $ v8ianpicnude = True
            hide v8_sexting_ian
            show v8_sexting_ian3
            with short
            pause 1
            $ lena_ian_pics.append("v8_sexting_ian3.webp")
            $ flena = "slutshy"
            l_p "{i}Now we're talking! {image=emoji_shy.webp} {image=emoji_fire.webp}{/i}"
            "That picture was a real turn-on... And so was the conversation."
            "Ian had a beautiful, strong body, a quick-witted mind, and a sense of humor. And that wonderful cock..."
            "I liked him a lot..."
            l_p "{i}Now it's me who has her eyes glued to the screen... Damn, you're one sexy man!{/i}"
            i_p "{i}Don't get too used to it!{/i}"
        else:
            hide v8_sexting_ian
            show v8_sexting_ian2
            with short
            pause 1
            $ lena_ian_pics.append("v8_sexting_ian2.webp")
            $ flena = "shy"
            l_p "{i}That's better... {image=emoji_tongue.webp}{/i}"
            "It was clear Ian felt a bit uncomfortable with the selfie stuff, but he was a turn-on for me either way."
            "He had a beautiful, strong body, a quick-witted mind, and a sense of humor..."
            "I really liked him..."
            l_p "{i}I really like this pic... But next time maybe you could lose the pants, too?{/i}"
            i_p "{i}I thought girls hated dick pics!{/i}"
        menu:
            "{image=icon_lust.webp}Send a dirty selfie" if ian_lena_sex:
                $ renpy.block_rollback()
                $ v8_sexting_full = True
                $ flena = "slutshy"
                if ian_lust > 4:
                    l_p "{i}Don't be like that... Here's a reward for being a good boy.{/i}"
                else:
                    l_p "{i}Not if it's from the guy they like... Or do you hate this?{/i}"
                if v8_stalkfap_dm2 == 1:
                    "Sending him the whole video I had recorded for my Stalkfap subscriber felt too embarrassing..."
                    "I decided to take a snapshot out of it and sent it to Ian. It was naughty enough!"
                else:
                    "I got up and took a very naughty selfie just for him."
                    $ lena_lena_pics.append("v8_sexting3.webp")
                    play sound "sfx/camera.mp3"
                hide v8_sexting_ian2
                hide v8_sexting_ian3
                show v8_sexting3
                with short
                pause 1
                if ian_lust > 4:
                    i_p "{i}Damn, Lena... You're trying to make my cock explode, aren't you?{/i}"
                else:
                    i_p "{i}Damn, Lena... You're trying to give me a heart attack, aren't you?{/i}"
                $ flena = "shy"
                if v8_stalkfap_dm2 == 1:
                    l_p "{i}I had the feeling you'd like it {image=emoji_flirt.webp}{/i}"
                else:
                    l_p "{i}This is for your eyes only {image=emoji_shy.webp}{/i}"
                if (v8_jeremy_flirt and v8_stalkfap_dm1 == 1) or (v8_jeremy_flirt and v8_stalkfap_dm2 == 1):
                    "This whole situation had me so turned on. It started with Jeremy, then chatting with my Stalkfap subscribers, and now this..."
                elif v8_jeremy_flirt:
                    "This whole situation had me so turned on. It started with Jeremy, and now this..."
                elif v8_stalkfap_dm1 == 1 or v8_stalkfap_dm2 == 1:
                    "This whole situation had me so turned on. It started with chatting with my Stalkfap subscribers, and now this..."
                else:
                    "This conversation with Ian had me so turned on... He had something about him that really did it for me."
                if v8_stalkfap_dm2 == 1:
                    $ flena = "n"
                    hide v8_sexting3 with short
                    show lenanude2 at truecenter with move
                    "And speaking of that subscriber, he kept messaging me. He hadn't sent the money yet, though."
                    guy "{i}Show me some more, babe. You're so fucking hot.{/i}"
                    l "{i}So, where's that premium you promised?{/i}"
                    guy "{i}That video wasn't even twenty seconds long. Give me something more, come on.{/i}"
                    guy "{i}I want you to stick a finger in that sexy butt-hole for me. Show me.{/i}"
                    menu:
                        "Ignore him":
                            $ renpy.block_rollback()
                            $ flena = "worried"
                            l "Ugh, what a jackass. I don't have time for you now, buddy."
                            "I ignored him and went back to my conversation with Ian."
                            $ flena = "shy"

                        "{image=icon_lust.webp}Do as he asks" if lena_lust > 6:
                            $ renpy.block_rollback()
                            $ v8_stalkfap_dm2 = 2
                            hide money_up
                            $ flena = "flirt"
                            "I decided to indulge him. Maybe it was because I felt so horny at that moment..."
                            "And I wanted to get that money, of course."
                            l "{i}Alright. Send me the money and I'll do it.{/i}"
                            guy "{i}You promise?{/i}"
                            l "{i}Yeah. I'm pretty horny right now, so you're in luck... I'll slide my wet finger into my asshole for you, but send the money first.{/i}"
                            if lena_money < 5:
                                call money(1) from _call_money_66
                                l "There it is..."
                            else:
                                if v8_stalkfap_dm1 > 0 and lena_money < 6:
                                    call money(1) from _call_money_67
                                    l "There it is... With this and what that other guy sent me, I can at least buy something useful."
                                else:
                                    play sound "sfx/moneyup.mp3"
                                    l "There it is... It's not much compared to what I already have, but every bit helps, I guess."

                    play sound "sfx/sms.mp3"
                    i_p "{i}Lena, are you still there?{/i}"
                    l_p "{i}Yeah, I was just getting more comfortable...{/i}"
                    i_p "{i}For what?{/i}"
                    $ flena = "slutshy"
                    l_p "{i}For this.{/i}"
                    show lenanude2 at right with move
                else:
                    $ flena = "slutshy"
                    l_p "{i}Do you know what I'm thinking about right now?{/i}"
                    i_p "{i}Tell me.{/i}"
                    l_p "{i}Wouldn't you rather I showed you?{/i}"
                hide v8_sexting3
                show v8_sexting5
                with short
                pause 1
                $ lena_lena_pics.append("v8_sexting5.webp")
                "I recorded a short video for Ian. A kind of video I had never sent anybody before..."
                "Sliding my finger over my pussy, I rubbed my clit for a while before sliding it in."
                l "Nhhh..."
                "I touched myself for Ian, thinking about the times we had spent together... How good and warm and fuzzy having sex with him made me feel..."
                "I shivered with pleasure before stopping the recording and sending it to him."
                l_p "{i}This is what I'm thinking about. About your hands on my body. About your hard cock inside of me...{/i}"
                "I had finally shaken off that shame I still felt... I didn't want to hold myself back."
                if v8_stalkfap_dm2 == 2:
                    "I wanted to freely share my naughty side with Ian... I was doing so with that random Stalkfap guy too, after all."
                else:
                    "I wanted to freely share my naughty side with Ian..."
                i_p "{i}I can't wait to be with you again, Lena. It's all I can think about.{/i}"
                $ flena = "shy"
                l_p "{i}Tomorrow, after the concert?{/i}"
                i_p "{i}I'd love to. We have to celebrate, don't we?{/i}"
                l_p "{i}Do I get something special if I do well?{/i}"
                i_p "{i}Nope. You're getting all I have to give either way.{/i}"
                i_p "{i}I'm so eager to make you enjoy yourself, you have no idea...{/i}"
                $ flena = "flirt"
                l "Mhhh..."
                "My finger continued to rub my clit ever so slowly, maintaining the buzz of pleasure. If only Ian was here to make it burst..."
                if v8_stalkfap_dm2 == 2:
                    "Another Stalkfap notification popped up on my screen."
                    guy "{i}Where's the video? I'm still waiting...{/i}"
                    l "Yeah, yeah..."
                else:
                    l_p "{i}I'm dying for you to do that. I want you, Ian.{/i}"
                    i_p "{i}You just have to wait until tomorrow. Then I'll be all yours.{/i}"
                if v8_stalkfap_dm2 == 2:
                    $ flena = "slut"
                    hide v8_sexting5
                    show v8_sexting6
                    with long
                    pause 1
                    $ lena_lena_pics.append("v8_sexting6.webp")
                    l "Uhhh..."
                    "I started a new recording, slowly digging my finger into my asshole, using my own juices as lubricant."
                    if lena_anal > 0:
                        "My anus welcomed my finger. I had been training it lately with the plug..."
                        if lena_anal_first != "n":
                            "Not to mention that delicious anal sex I had gotten a couple of weeks ago."
                            "A hard cock stretching my backside... I couldn't wait to do it again."
                        else:
                            "The time to finally try anal sex had to be near... I wanted to know how it really felt having a cock penetrating my backside."
                    else:
                        "I wasn't used to having stuff put into my butt, but it wasn't too hard for me to slide my finger in."
                        "And it wasn't painful. A bit weird, yeah, but not bad..."
                    "I began moving my finger back and forth, slowly fucking my asshole with it."
                    if v8_stalkfap_dm2 == 2:
                        "To think someone was about to see me doing this... I was about to share the most intimate and naughty moment with some guy whose face I didn't even know."
                        "He had paid for it, though... And I was sure he was eagerly waiting, hard cock in hand."
                        "I wonder if Ian would also masturbate looking at me... Yeah, of course he would..."
                        "I sent the video to both of them, and they responded almost at the same time."
                        $ flena = "slutshy"
                    else:
                        "I was sharing such an intimate and naughty moment with Ian. I wasn't hiding anything from him..."
                        "He would masturbate while watching me do the same, getting turned on by my body, by my impishness..."
                        "I sent him the video."
                        $ flena = "slutshy"
                    if lena_anal_first == "ian":
                        i_p "{i}I'm getting flashbacks from that night together...{/i}"
                        l_p "{i}The night you fucked my ass? {image=emoji_ass.webp}{image=emoji_cum.webp}{/i}"
                        i_p "{i}That one. Is this a way of telling me you'd like to repeat it? Because I'm so down for that {image=emoji_flirt.webp}{/i}"
                    else:
                        i_p "{i}Wow, Lena... Just WOW.{/i}"
                        i_p "{i}I didn't know you liked that. I've never touched you there yet...{/i}"
                        if lena_anal > 0:
                            l_p "{i}I might let you do it next time {image=emoji_fire.webp}{/i}"
                            i_p "{i}I'm so down for that.{/i}"
                        else:
                            l_p "{i}It's something I rarely tried... But I might wanna try it with you {image=emoji_fire.webp}{/i}"
                            i_p "{i}I'm so down for that.{/i}"
                    "I continued fingering my ass while texting. The pleasure was building up dangerously..."
                    if v8_stalkfap_dm2 == 2:
                        "I checked the other guy's message."
                        guy "{i}Fuck, that was actually worth it. You really love playing with your ass, don't you?{/i}"
                        l "{i}I love it {image=emoji_fire.webp} {image=emoji_heart.webp} {/i}"
                        guy "{i}You looked rather innocent but I knew you were a horny little slut. I love being right.{/i}"
                        "Ian got my attention back."
                        i_p "{i}Tomorrow night?{/i}"
                    l_p "{i}Tomorrow night. I want you to make love to my ass {image=emoji_shy.webp} {/i}"
                play sound "sfx/orgasm1.mp3"
                l "Mhhhh!!!" with vpunch
                stop music fadeout 2.0
                hide v8_sexting6
                hide v8_sexting5
                $ flena = "shy"
                show lenanude2 at truecenter with move
                l "Ohhh... Yes..."
                $ flena = "slutshy"
                "The orgasm finally overcame me. So good..."
                l "I got super turned on tonight..."
                "I slowly came back to my senses. I felt relaxed and a bit sleepy..."
                $ flena = "shy"
                l_p "{i}Good night, Ian. This was fun  {image=emoji_shy.webp}{/i}"
                i_p "{i}It was. A lot.{/i}"
                l_p "{i}See you tomorrow {image=emoji_kiss.webp} {image=emoji_moon.webp}{/i}"
                if v8_stalkfap_dm2 == 2:
                    $ flena = "smile"
                    "I also replied to my satisfied customer."
                    l "{i}Enjoy the video. And if you want to send some more money my way, I'll be happy to show you more {image=emoji_kiss.webp}{/i}"
                if v8_stalkfap_dm2 == 1:
                    $ flena = "n"
                    "While I had been sexting with Ian my Stalkfap subscriber had been messaging me. Quite a lot."
                    l "What does this guy want...?"
                    guy "{i}Hey.{/i}"
                    guy "{i}Are you there?{/i}"
                    guy "{i}Show me how you fuck your ass with your finger, come on. You've given me almost nothing so far. I won't pay just for that short video.{/i}"
                    guy "{i}What, you know I'm right and you're ignoring me?{/i}"
                    jump v8stalkfapfight
                $ flena = "n"
                l "I hope tonight I can get some quality sleep..."
                $ renpy.end_replay()
                $ gallery_unlock_scene("CH08_S04")
                jump v8lenafriday

            "That's enough sexting":
                $ renpy.block_rollback()
                stop music fadeout 2.0
                hide v8_sexting_ian2
                hide v8_sexting_ian3
                with short
                show lenanude2 at truecenter with move
                if ian_lena_sex:
                    $ flena = "shy"
                    l_p "{i}By the way, about tomorrow... Would you like to come home after the concert?{/i}"
                    l_p "{i}You know, to... celebrate {image=emoji_shy.webp}{/i}"
                    i_p "{i}Of course. You don't even need to ask {image=emoji_glasses.webp}{/i}"
                    $ flena = "smile"
                    l_p "{i}Awesome. I hope it goes well, though.{/i}"
                    i_p "{i}It will!{/i}"
                else:
                    $ flena = "smile"
                    l_p "{i}I should go to bed. Lots of things to prepare tomorrow...{/i}"
                    i_p "{i}Let me know if you need anything. I'll be there!{/i}"
                if lena_ian_love:
                    l_p "{i}Good night, Ian. See you tomorrow {image=emoji_heart.webp}{/i}"
                else:
                    l_p "{i}Good night, Ian. See you tomorrow {image=emoji_kiss.webp}{/i}"
                i_p "{i}Good night beautiful. And thanks for the pictures, I really love them.{/i}"
                if v8_stalkfap_dm2 == 1:
                    $ flena = "n"
                    l "Now, let's see what that guy wanted..."
                    "I opened the Stalkfap app again. I had yet another message, but no money had been paid."
                    guy "{i}Show me some more, babe. You're so fucking hot.{/i}"
                    l "{i}So, where's that premium you promised?{/i}"
                    guy "{i}That video wasn't even twenty seconds long. Give me something more, come on.{/i}"
                    jump v8stalkfapdilemma
                else:
                    $ renpy.end_replay()
                    $ gallery_unlock_scene("CH08_S04")
                    jump v8lenafriday

###### MIKE
    elif v8_lena_sexting == "mike":
        play sound "sfx/sms.mp3"
        $ flena = "flirt"
        if v8_stalkfap_dm1 == 1 or v8_stalkfap_dm2 == 1:
            l "Oh, Mike just replied...!"
            "I immediately closed the Stalkfap app and opened his text message."
        else:
            l "Oh, Mike replied...!"
        show lenanude2 at right with move
        show v8_sexting1 with short
        nvl clear
        mk_p "{i}Hey, Lena... I'm glad to see you know exactly what I like {image=emoji_fire.webp}{/i}"
        l_p "{i}What is it? Me? {image=emoji_flirt.webp}{/i}"
        mk_p "{i}You and your beautiful booty.{/i}"
        "Reading Mike's words made my body instantly tingle. He had this strong effect on me..."
        l_p "{i}Does it bring back memories?{/i}"
        mk_p "{i}Yeah, of my hard cock grinding between those perfect butt cheeks...{/i}"
        if lena_mike < 12:
            call friend_xp('mike') from _call_friend_xp_655
        $ flena = "slutshy"
        l "Nice, it seems he's in the mood for some sexting..."
        if v8_stalkfap_dm1 == 2:
            "He deserved another selfie. I sent him the one I had just taken a moment ago..."
        else:
            "I rolled around on my bed and took another selfie for him."
            play sound "sfx/camera.mp3"
            $ lena_lena_pics.append("v8_sexting2_comp")
        hide v8_sexting1
        show v8_sexting2_comp
        with short
        pause 1
        l_p "{i}Then this must bring back memories too {image=emoji_crazy.webp}{/i}"
        mk_p "{i}Even better memories. Damn, how I love those boobs! {/i}"
        $ flena = "slut"
        l_p "{i}You came between them... and you can do it again whenever you want {image=emoji_cum.webp}{/i}"
        if v7_mike_bj:
            l_p "{i}Or maybe you wanna cum in my mouth again, like last Friday...{/i}"
        mk_p "{i}How can you be such a bad girl? {image=emoji_devil.webp}{/i}"
        l_p "{i}Don't you like it?{/i}"
        mk_p "{i}I love it. You get me rock hard like no other girl.{/i}"
        l_p "{i}Show me.{/i}"
        if v8_stalkfap_dm2 == 1:
            $ flena = "n"
            "While I waited a notification popped up on my screen. That guy from Stalkfap had responded to my video."
            guy "{i}Hot damn, babe, what a delicious asshole you have. I'd let you sit on my face and lick it clean until you cum.{/i}"
            l "{i}I'm glad you liked it!{/i}"
            guy "{i}You like getting your asshole licked?{/i}"
            l "{i}It depends.{/i}"
            guy "{i}I'm sure I'd make you enjoy it.{/i}"
            "Before I could respond again Mike sent a file to me."
        else:
            "After a couple of minutes, I got what I asked for."
        play sound "sfx/sms.mp3"
        hide v8_sexting2_comp
        show v8_sexting_mike
        with short
        pause 1
        $ lena_mike_pics.append("v8_sexting_mike.webp")
        $ flena = "flirtshy"
        l "Oh, my... He showed me that and more."
        "I looked at Mike's chiseled body, that confident smile, those sexy tattoos..."
        "And that hard, big cock that was able to make me cum like crazy."
        l "Fuck, he's so hot..."
        "My hand instinctively reached down to my pussy. I was willing to go all out tonight..."
        if v8_stalkfap_dm2 == 1:
            "I sent him the short video I had just made for my Stalkfap subscriber."
            hide v8_sexting_mike
            show v8_sexting3
            with short
            pause 1
            l_p "{i}This is all yours to enjoy...{/i}"
            hide v8_sexting3
            show v8_sexting4
            with short
            pause 1
            l_p "{i}However you want.{/i}"
            mk_p "{i}That video was pure fire {image=emoji_fire.webp}{image=emoji_fire.webp}{image=emoji_fire.webp} {/i}"
            mk_p "{i}You're gonna make my cock explode. And I want it to explode inside both those holes.{/i}"
        else:
            "I got up and took a very naughty selfie just for him."
            play sound "sfx/camera.mp3"
            hide v8_sexting_mike
            show v8_sexting3
            with short
            pause 1
            $ lena_lena_pics.append("v8_sexting3.webp")
            l_p "{i}Since you showed me yours I'll show you mine {image=emoji_shy.webp} {/i}"
            mk_p "{i}You're gonna make my cock explode. And I want it to explode inside of that pussy.{/i}"
            if lena_anal_first == "mike":
                mk_p "{i}Or maybe we could try anal again. That ass needs some training...{/i}"
                mk_p "{i}Let me see it {image=emoji_ass.webp} {/i}"
            else:
                mk_p "{i}I'd like to take a peek at that sexy ass of yours, too. Show me everything.{/i}"
            "There was no way I could deny him."
            play sound "sfx/camera.mp3"
            hide v8_sexting3
            show v8_sexting4
            with short
            pause 1
            $ lena_lena_pics.append("v8_sexting4.webp")
        if lena_anal_first == "mike":
            mk_p "{i}You moaned like crazy when I stuck my cock in there {image=emoji_glasses.webp}{/i}"
            l_p "{i}And I can't wait for you to do it again...{/i}"
            mk_p "{i}I want to fuck all of your holes and make you moan even louder.{/i}"
        else:
            mk_p "{i}Do you do anal? {image=emoji_glasses.webp}{/i}"
            if lena_anal == 2:
                l_p "{i}Yes, I'm not super experienced, but I like it quite a lot...{/i}"
            if lena_anal == 1:
                l_p "{i}Not really, but I've been preparing myself to do it...{/i}"
            if lena_anal == 0:
                l_p "{i}Not really, but if it's with you, we could give it a go... {/i}"
            mk_p "{i}I'm so down to try it with you. I want to fuck all of your holes.{/i}"
        $ flena = "slut"
        if (v8_jeremy_flirt and v8_stalkfap_dm1 == 1) or (v8_jeremy_flirt and v8_stalkfap_dm2 == 1):
            "This whole situation had me so turned on. It started with Jeremy, then chatting with my Stalkfap subscribers, and now this..."
        elif v8_jeremy_flirt:
            "This whole situation had me so turned on. It started with Jeremy, and now this..."
        elif v8_stalkfap_dm1 == 1 or v8_stalkfap_dm2 == 1:
            "This whole situation had me so turned on. It started with chatting with my Stalkfap subscribers, and now this..."
        else:
            "This conversation with Mike had me in heat... He really awakened my inner she-wolf!"
        if v8_stalkfap_dm2 == 1:
            $ flena = "n"
            hide v8_sexting4 with short
            show lenanude2 at truecenter with move
            "And speaking of that subscriber, he kept messaging me. He hadn't sent the money yet, though."
            guy "{i}Show me some more, babe. You're so fucking hot.{/i}"
            l "{i}So, where's that premium you promised?{/i}"
            guy "{i}That video wasn't even twenty seconds long. Give me something more, come on.{/i}"
            guy "{i}I want you to stick a finger in that sexy butt-hole for me. Show me.{/i}"
            menu:
                "Ignore him":
                    $ renpy.block_rollback()
                    $ flena = "worried"
                    l "Ugh, what a jackass. I don't have time for you now, buddy."
                    "I ignored him and went back to my conversation with Mike."
                    $ flena = "shy"

                "{image=icon_lust.webp}Do as he asks" if lena_lust > 6:
                    $ renpy.block_rollback()
                    $ v8_stalkfap_dm2 = 2
                    hide money_up
                    $ flena = "flirt"
                    "I decided to indulge him. Maybe it was because I felt so horny at that moment..."
                    "And I wanted to get that money, of course."
                    l "{i}Alright. Send me the money and I'll do it.{/i}"
                    guy "{i}You promise?{/i}"
                    l "{i}Yeah. I'm pretty horny right now, so you're in luck... I'll slide my wet finger into my asshole for you, but send the money first.{/i}"
                    if lena_money < 5:
                        call money(1) from _call_money_68
                        l "There it is..."
                    else:
                        if v8_stalkfap_dm1 > 0 and lena_money < 6:
                            call money(1) from _call_money_69
                            l "There it is... With this and what that other guy sent me, I can at least buy something useful."
                        else:
                            play sound "sfx/moneyup.mp3"
                            l "There it is... It's not much compared to what I already have, but every bit helps, I guess."

        hide v8_sexting4
        show v8_sexting5
        with long
        pause 1
        $ lena_lena_pics.append("v8_sexting5.webp")
        $ flena = "slut"
        "I pointed the camera to my crotch and started recording a short video. A kind of video I had never sent anybody before..."
        "Sliding my finger over my pussy, I rubbed my clit for a while before sliding it in."
        l "Nhhh..."
        "I touched myself for Mike, thinking about our sex together... How hot it was having him eating me out, using my boobs, pounding into me..."
        "I shivered with pleasure before I stopped the recording and sent it to him."
        l_p "{i}Come here and fuck me {image=emoji_devil.webp} {image=emoji_cum.webp}{/i}"
        mk_p "{i}I wish I could right now.{/i}"
        $ flena = "flirtevil"
        l_p "{i}When?{/i}"
        if v7_mike_bj:
            mk_p "{i}Soon. I'm dying to... And I have to repay you for that blowjob {image=emoji_wink.webp}{/i}"
        else:
            mk_p "{i}Soon. I'm dying to...{/i}"
        "My finger continued to rub my clit ever so slowly, maintaining the buzz of pleasure. If only Mike was here to give it to me hard..."
        if v8_stalkfap_dm2 == 2:
            $ flena = "flirt"
            "Another Stalkfap notification popped up on my screen."
            guy "{i}Where's the video? I'm still waiting...{/i}"
            l "Yeah, yeah..."
            "I had to prepare myself first. And now..."
        else:
            "I wasn't done. Not when I was that horny."
        "I started a new recording."
        $ flena = "slutshy"
        hide v8_sexting5
        show v8_sexting6
        with short
        pause 1
        $ lena_lena_pics.append("v8_sexting6.webp")
        l "Uhhh..."
        "I slowly dug my finger into my asshole, using my own juices as lubricant."
        if lena_anal > 0:
            "My anus welcomed my finger. I had been training it lately with the plug..."
            if lena_anal_first != "n":
                "Not to mention that delicious anal sex I had gotten a couple of weeks ago."
                "A hard cock stretching my backside... I couldn't wait to do it again."
            else:
                "The time to finally try anal sex had to be near... I wanted to know how it really felt having a cock penetrating my backside."
        else:
            "I wasn't used to having stuff put into my butt, but it wasn't too hard for me to slide my finger in."
            "And it wasn't painful. A bit weird, yeah, but not bad..."
        "I began moving my finger back and forth, slowly fucking my asshole with it."
        if v8_stalkfap_dm2 == 2:
            "To think someone was about to see me doing this... I was about to share the most intimate and naughty moment with some guy whose face I didn't even know."
            "He had paid for it, though... And I was sure he was eagerly waiting, hard cock in hand."
            "Mike was surely doing the same. Masturbating with my dirty videos, the kind of videos he should only receive from his girlfriend."
            "But he said it himself. No other girl turned him on as much as I did."
            $ flena = "flirt"
        else:
            "I was sharing such dirty and intimate videos with Mike... Something he should only receive from his girlfriend."
            "But would jerk off thinking about me instead. He desired to see me. To fuck me."
            "He said it himself. No other girl turned him on as much as I did."
            "I sent him the video."
            $ flena = "flirt"
        if lena_anal_first == "mike":
            mk_p "{i}Hell yeah... I see you can't wait either.{/i}"
            l_p "{i}No, I can't. Make me yours again...{/i}"
        elif lena_anal == 2:
            mk_p "{i}Hell yeah... I see you can't wait either.{/i}"
            l_p "{i}No, I can't. Take my ass already...{/i}"
        else:
            mk_p "{i}Hell yeah... I see you really wanna try it.{/i}"
            l_p "{i}Yes. Take my ass already...{/i}"
        "I continued fingering my ass while texting. The pleasure was building up dangerously..."
        if v8_stalkfap_dm2 == 2:
            "I checked the other guy's message."
            guy "{i}Fuck, that was actually worth it. You really love playing with your ass, don't you?{/i}"
            l "{i}I love it {image=emoji_fire.webp} {image=emoji_heart.webp} {/i}"
            guy "{i}You looked rather innocent but I knew you were a horny little slut. I love being right.{/i}"
            "Mike got my attention back."
        mk "{i}I can't wait to make you mine, all mine. My sexy little anal slut {image=emoji_devil.webp}{/i}"
        "Reading that was the final push I needed."
        $ flena = "shy"
        play sound "sfx/orgasm1.mp3"
        l "Mhhhh!!!" with vpunch
        hide v8_sexting6
        show lenanude2 at truecenter with move
        stop music fadeout 2.0
        l "Ohhh... Yes..."
        $ flena = "slutshy"
        "The orgasm finally overcame me. So good..."
        l "Oh God, that was insane. Mike really makes me wanna act like a bitch...!"
        "I slowly came back to my senses. I felt relaxed and a bit sleepy..."
        $ flena = "shy"
        l_p "{i}I hope that day comes soon. Good night, Mike. Don't make me wait! {image=emoji_kiss.webp} {image=emoji_moon.webp}{/i}"
        mk_p "{i}I'll save a night just for you real soon. I can't wait, baby {image=emoji_crazy.webp}{/i}"
        if v8_stalkfap_dm2 == 2:
            $ flena = "smile"
            "I also replied to my satisfied customer."
            l "{i}Enjoy the video. And if you want to send some more money my way, I'll be happy to show you more {image=emoji_kiss.webp}{/i}"
        if v8_stalkfap_dm2 == 1:
            $ flena = "n"
            "While I had been sexting with Mike my Stalkfap subscriber had been messaging me. Quite a lot."
            l "What does this guy want...?"
            guy "{i}Hey.{/i}"
            guy "{i}Are you there?{/i}"
            guy "{i}Show me how you fuck your ass with your finger, come on. You've given me almost nothing so far. I won't pay just for that short video.{/i}"
            guy "{i}What, you know I'm right and you're ignoring me?{/i}"
            jump v8stalkfapfight
        $ flena = "flirtshy"
        l "I'm still excited... I hope I can fall asleep tonight."
        $ renpy.end_replay()
        $ gallery_unlock_scene("CH08_S04")
        jump v8lenafriday

##### ROBERT
    elif v8_lena_sexting == "robert":
        play sound "sfx/sms.mp3"
        $ flena = "smile"
        if v8_stalkfap_dm1 == 1 or v8_stalkfap_dm2 == 1:
            l "Oh, Robert just replied..."
            "I closed the Stalkfap app and opened his text message."
            show lenanude2 at right with move
            show v8_sexting1 with short
        else:
            "I knew I would get a quick reply from Robert."
        nvl clear
        r_p "{i}I see... Relaxing much? {image=emoji_devil.webp}{/i}"
        r_p "{i}I'm glad you're not mad over what happened the other night...{/i}"
        l_p "{i}I don't wanna talk, or think, about it. That's why I sent you the picture.{/i}"
        l_p "{i}Do you need another one to understand where I'm trying to get at? {image=emoji_flirt.webp}{/i}"
        r_p "{i}Yes, just to be sure I'm not misunderstanding you {image=emoji_glasses.webp}{/i}"
        $ flena = "flirt"
        l_p "{i}Feeling cheeky today, are we?{/i}"
        if v8_stalkfap_dm1 == 2:
            "I was feeling playful. I sent him the one I had just taken a moment ago..."
        else:
            "I was feeling playful. I rolled around on my bed and took another selfie for him."
            play sound "sfx/camera.mp3"
            $ lena_lena_pics.append("v8_sexting2_comp")
        hide v8_sexting1
        show v8_sexting2_comp
        with short
        pause 1
        if lena_robert < 12:
            call friend_xp('robert') from _call_friend_xp_656
        r_p "{i}Fuck, you're so hot, babe! I'm the luckiest guy on earth!{/i}"
        l_p "{i}You like the view?{/i}"
        r_p "{i}My cock is about to explode.{/i}"
        l_p "{i}Well, don't blow your wad yet and tell me what you would do to me... {image=emoji_devil.webp} {/i}"
        r_p "{i}I would get on top of you and kiss you all over, especially those incredible tits.{/i}"
        r_p "{i}I would lick them and squeeze them, maybe stick my dick between them...{/i}"
        r_p "{i}And I would also get between your legs and eat you out until you cum like you've never come before {image=emoji_cum.webp} {/i}"
        l_p "{i}I'd love to see that...{/i}"
        play sound "sfx/sms.mp3"
        hide v8_sexting2_comp
        show v8_sexting_robert
        with short
        pause 1
        $ lena_robert_pics.append("v8_sexting_robert.webp")
        "Robert sent me a selfie showing the goods. His cock indeed looked like it was ready to burst..."
        r_p "{i}And then I would pull this out and fuck you all night, cumming inside of you.{/i}"
        r_p "{i}Just the way you like it {image=emoji_cum.webp}{image=emoji_flirt.webp}{/i}"
        if v8_stalkfap_dm2 == 1:
            $ flena = "n"
            "I was getting into the mood when a notification popped up on my screen. That guy from Stalkfap had responded to my video."
            guy "{i}Hot damn, babe, what a delicious asshole you have. I'd let you sit on my face and lick it clean until you cum.{/i}"
            l "{i}I'm glad you liked it!{/i}"
            guy "{i}You like getting your asshole licked?{/i}"
            l "{i}It depends.{/i}"
            guy "{i}I'm sure I'd make you enjoy it.{/i}"
            "Robert reclaimed my attention."
        r_p "{i}How does that sound? You like it?{/i}"
        "My hand instinctively reached down to my pussy. I was letting myself loose..."
        if v8_stalkfap_dm2 == 1:
            "I raised the temperature even more by sending Robert the short video I had just made for my Stalkfap subscriber."
            hide v8_sexting_robert
            show v8_sexting3
            with short
            pause 1
            l_p "{i}You're getting me wet...{/i}"
            hide v8_sexting3
            show v8_sexting4
            with short
            pause 1
            l_p "{i}... and really horny.{/i}"
            r_p "{i}Holy fuck babe, you're pure fire {image=emoji_fire.webp}{image=emoji_fire.webp}{image=emoji_fire.webp} You're so hot and naughty and sexy!{/i}"
            r_p "{i}You're gonna make my cock explode. And I want it to explode inside both those holes.{/i}"
        else:
            "I got up and took a very naughty selfie to raise the temperature even more."
            play sound "sfx/camera.mp3"
            hide v8_sexting_robert
            show v8_sexting3
            with short
            pause 1
            $ lena_lena_pics.append("v8_sexting3.webp")
            l_p "{i}You're getting me wet...{/i}"
            r_p "{i}Damn, I love that pussy... The best one I've ever had...{/i}"
            if lena_anal_first == "robert":
                r_p "{i}And I liked your ass even better. I want to fuck it over and over again.{/i}"
                r_p "{i}Show it to me, babe. I wanna see it.{/i}"
            else:
                r_p "{i}I'd like to take a peek at that sexy ass of yours, too. Show me everything, babe.{/i}"
            "He was getting greedy... I liked it."
            play sound "sfx/camera.mp3"
            hide v8_sexting3
            show v8_sexting4
            with short
            pause 1
            $ lena_lena_pics.append("v8_sexting4.webp")
        if lena_anal_first == "robert":
            l_p "{i}It was so hot the way you fucked my little asshole last time... You stretched it out so well.{/i}"
            r_p "{i}Fuck, you're so slutty babe, I love it. And I can't wait to fuck your ass again {image=emoji_cum.webp} {image=emoji_fire.webp}{/i}"
            l_p "{i}I hope this time you can make me cum... I really want to know what it feels like...{/i}"
        else:
            r_p "{i}Do you do anal? {image=emoji_crazy.webp}{/i}"
            if lena_anal == 2:
                l_p "{i}Yes, I'm not super experienced, but I like it quite a lot...{/i}"
            if lena_anal == 1:
                l_p "{i}Not really, but I've been preparing myself to do it...{/i}"
            if lena_anal == 0:
                l_p "{i}Not really, but I've been wondering if I should give it a go... {/i}"
            r_p "{i}I'm so down to try it with you. I want to cum in all your holes {image=emoji_cum.webp} {image=emoji_fire.webp}{/i}"
        $ flena = "slut"
        if (v8_jeremy_flirt and v8_stalkfap_dm1 == 1) or (v8_jeremy_flirt and v8_stalkfap_dm2 == 1):
            "I was having fun with this situation, and I felt excited. It started with Jeremy, then chatting with my Stalkfap subscribers, and now this..."
        elif v8_jeremy_flirt:
            "I was having fun with this situation, and I felt excited. It started with Jeremy, and now this..."
        elif v8_stalkfap_dm1 == 1 or v8_stalkfap_dm2 == 1:
            "I was having fun with this situation, and I felt excited. It started with chatting with my Stalkfap subscribers, and now this..."
        else:
            "I was having fun acting slutty with Robert, making him horny at will..."
        if v8_stalkfap_dm2 == 1:
            $ flena = "n"
            hide v8_sexting4 with short
            show lenanude2 at truecenter with move
            "And speaking of that subscriber, he kept messaging me. He hadn't sent the money yet, though."
            guy "{i}Show me some more, babe. You're so fucking hot.{/i}"
            l "{i}So, where's that premium you promised?{/i}"
            guy "{i}That video wasn't even twenty seconds long. Give me something more, come on.{/i}"
            guy "{i}I want you to stick a finger in that sexy butt-hole for me. Show me.{/i}"
            menu:
                "Ignore him":
                    $ renpy.block_rollback()
                    $ flena = "worried"
                    l "Ugh, what a jackass. I don't have time for you now, buddy."
                    "I ignored him and went back to my conversation with Robert."

                "{image=icon_lust.webp}Do as he asks" if lena_lust > 6:
                    $ renpy.block_rollback()
                    $ v8_stalkfap_dm2 = 2
                    hide money_up
                    $ flena = "flirt"
                    "I decided to indulge him. Maybe it was because I felt so horny at that moment..."
                    "And I wanted to get that money, of course."
                    l "{i}Alright. Send me the money and I'll do it.{/i}"
                    guy "{i}You promise?{/i}"
                    l "{i}Yeah. I'm pretty horny right now, so you're in luck... I'll slide my wet finger into my asshole for you, but send the money first.{/i}"
                    if lena_money < 5:
                        call money(1) from _call_money_70
                        l "There it is..."
                    else:
                        if v8_stalkfap_dm1 > 0 and lena_money < 6:
                            call money(1) from _call_money_71
                            l "There it is... With this and what that other guy sent me, I can at least buy something useful."
                        else:
                            play sound "sfx/moneyup.mp3"
                            l "There it is... It's not much compared to what I already have, but every bit helps, I guess."

        hide v8_sexting4
        show v8_sexting5
        with long
        pause 1
        $ lena_lena_pics.append("v8_sexting5.webp")
        $ flena = "slut"
        "I pointed the camera to my crotch and started recording a short video. A kind of video I had never sent anybody before..."
        "Sliding my finger over my pussy, I rubbed my clit for a while before sliding it in."
        l "Nhhh..."
        "I touched myself for Robert, thinking about our sex together... His hands grabbing my body passionately, his cock pumping deep inside me so eagerly..."
        "I shivered with pleasure before I stopped the recording and sent it to him."
        l_p "{i}You filled this with your jizz last time... Full to the brim {image=emoji_devil.webp} {image=emoji_cum.webp}{/i}"
        r_p "{i}And I'll fill it every time, until my balls are empty!{/i}"
        "My finger continued to rub my clit ever so slowly, maintaining the buzz of pleasure. If only Robert was here to scratch this itch for me..."
        r_p "{i}That was so hot... You made me cum like crazy  {image=emoji_cum.webp}{image=emoji_cum.webp}{image=emoji_cum.webp}{/i}"
        $ flena = "n"
        l "Oh, so he's finished..."
        $ flena = "flirtshy"
        l "Well, I'm almost there too. I can finish this on my own..."
        if v8_stalkfap_dm2 == 2:
            "Besides, I still had a video to make, as my subscriber reminded me."
            "Another Stalkfap notification popped up on my screen."
            guy "{i}Where's the video? I'm still waiting...{/i}"
            l "Yeah, yeah..."
            "I started the recording."
            $ flena = "slutshy"
            hide v8_sexting5
            show v8_sexting6
            with short
            pause 1
            $ lena_lena_pics.append("v8_sexting6.webp")
            l "Uhhh..."
            "I slowly dug my finger into my asshole, using my own juices as lubricant."
            if lena_anal > 0:
                "My anus welcomed my finger. I had been training it lately with the plug..."
                if lena_anal_first != "n":
                    "Not to mention that delicious anal sex I had gotten a couple of weeks ago."
                    "A hard cock stretching my backside... I couldn't wait to do it again."
                else:
                    "The time to finally try anal sex had to be near... I wanted to know how it really felt having a cock penetrating my backside."
            else:
                "I wasn't used to having stuff put into my butt, but it wasn't too hard for me to slide my finger in."
                "And it wasn't painful. A bit weird, yeah, but not bad..."
            "I began moving my finger back and forth, slowly fucking my asshole with it."
            "To think someone was about to see me doing this... I was about to share the most intimate and naughty moment with some guy whose face I didn't even know."
            "He had paid for it, though... And I was sure he was eagerly waiting, hard cock in hand."
            "He would masturbate while watching me do the same, getting turned on by my body, by my impishness..."
            "I sent the video and awaited his response while I continued fingering my ass. The pleasure was building up dangerously..."
            guy "{i}Fuck, that was actually worth it. You really love playing with your ass, don't you?{/i}"
            l "{i}I love it {image=emoji_fire.webp} {image=emoji_heart.webp} {/i}"
            guy "{i}You looked rather innocent but I knew you were a horny little slut. I love being right.{/i}"
            "Reading that was the final push I needed."
            $ flena = "shy"
            play sound "sfx/orgasm1.mp3"
            l "Mhhhh!!!" with vpunch
            hide v8_sexting6
            stop music fadeout 2.0
            l "Ohhh... Yes..."
            $ flena = "slutshy"
            "The orgasm finally overcame me. So good..."
            $ flena = "shy"
            "I slowly came back to my senses. I felt relaxed and a bit sleepy..."
            l "Oh God, that was crazy. I feel like such a bad girl..."
            "I sent a final reply to my satisfied customer."
            l "{i}Enjoy the video. And if you want to send some more money my way, I'll be happy to show you more {image=emoji_kiss.webp}{/i}"
            scene lenaroomnight with long
            "After that, I got into bed and tried to fall asleep."
            $ renpy.end_replay()
            $ gallery_unlock_scene("CH08_S04")
            jump v8lenafriday
        else:
            "I continued rubbing my pussy, bringing myself closer and closer to the brink of orgasm, until..."
            play sound "sfx/orgasm1.mp3"
            l "Mhhhh!!!" with vpunch
            hide v8_sexting5
            $ flena = "shy"
            show lenanude2 at truecenter with move
            stop music fadeout 2.0
            l "Ohhh... Yes..."
            $ flena = "slutshy"
            "The climax finally overcame me. So good..."
            $ flena = "shy"
            if v8_stalkfap_dm2 == 1:
                "While I had been sexting with Robert my Stalkfap subscriber had been messaging me. Quite a lot."
                $ flena = "n"
                l "What does this guy want...?"
                guy "{i}Hey.{/i}"
                guy "{i}Are you there?{/i}"
                guy "{i}Show me how you fuck your ass with your finger, come on. You've given me almost nothing so far. I won't pay just for that short video.{/i}"
                guy "{i}What, you know I'm right and you're ignoring me?{/i}"
                jump v8stalkfapfight
            else:
                "I slowly came back to my senses. I felt relaxed and a bit sleepy..."
                scene lenaroomnight with long
                "Now I could go to bed and try to fall asleep."
                $ renpy.end_replay()
                $ gallery_unlock_scene("CH08_S04")
                jump v8lenafriday

#### STALKFAP 2 ONLY
    elif v8_stalkfap_dm2 == 1:
        "I got his response pretty quickly."
        guy "{i}Hot damn, babe, what a delicious asshole you have. I'd let you sit on my face and lick it clean until you cum.{/i}"
        l "{i}I'm glad you liked it!{/i}"
        guy "{i}You like getting your asshole licked?{/i}"
        l "{i}It depends.{/i}"
        guy "{i}I'm sure I'd make you enjoy it.{/i}"
        l "{i}So, where's that premium you promised?{/i}"
        guy "{i}That video wasn't even twenty seconds long. Show me some more, babe. You're so fucking hot.{/i}"
        menu v8stalkfapdilemma:
            "That's not what we agreed":
                $ renpy.block_rollback()
                $ flena = "serious"
                l "What's up with this guy?"
                l "{i}I'm sorry, but that's not what we agreed.{/i}"
                guy "{i}Come on, stick a finger in that butt-hole for me. Show me.{/i}"
                l "{i}I said this is not what we agreed. You said you'd pay extra for a custom video and I did just what you asked.{/i}"
                label v8stalkfapfight:
                    $ flena = "serious"
                    guy "{i}You fucking sluts, all you care about is money.{/i}"
                $ flena = "worried"
                guy "{i}You think you can ask for free money just because you're hot and willing to show your dirty ass to people? You disgust me.{/i}"
                $ flena = "mad"
                l "What the fuck!?"
                l "{i}Fuck yourself! It's you who's offering money to see me naked, are you aware of that or is your head too far up your ass for you to notice?{/i}"
                guy "{i}You whores are all the same. Not my fault your tits are all you have the world to offer.{/i}"
                guy "{i}Don't worry, you won't get a single cent more out of me. Unsubscribed.{/i}"
                if lena_charisma > 4:
                    l "{i}Go eat shit and die, loser.{/i}"
                else:
                    l "{i}Do me a favor and get lost, loser.{/i}"
                l "I can't believe this asshole...! What the hell is wrong with people?"
                $ flena = "serious"
                l "Fuck... Now I'm all riled up and angry... And I didn't even get any money for it."
                $ flena = "worried"
                "That had been a really uncomfortable experience... I should be more cautious with these kinds of guys from now on..."
                if v8_lena_sexting == "ian":
                    $ flena = "sad"
                    "Such a shame, after the great time I had sexting with Ian. This kinda ruined it..."
                if v8_lena_sexting == "mike":
                    $ flena = "sad"
                    "Such a shame, after the exciting time I had sexting with Mike. This kinda ruined it..."
                if v8_lena_sexting == "robert":
                    $ flena = "sad"
                    "Such a shame, after the fun time I had sexting with Robert. This kinda ruined it..."

            "What do you want to see?":
                $ renpy.block_rollback()
                "I wasn't exactly liking his attitude, but I decided to indulge him a bit more."
                l "{i}What do you want to see, exactly?{/i}"
                guy "{i}I want you to stick a finger in that sexy butt-hole for me. Show me.{/i}"
                menu:
                    "{image=icon_lust.webp}Do as he asks" if lena_lust > 6:
                        $ renpy.block_rollback()
                        $ v8_stalkfap_dm2 = 2
                        hide money_up
                        $ flena = "shy"
                        l "{i}Alright. Send me the money and I'll do it.{/i}"
                        guy "{i}You promise?{/i}"
                        if v8_lena_sexting != "n":
                            l "{i}Yeah. I'm pretty horny right now, so you're in luck... I'll slide my wet finger into my asshole for you, but send the money first.{/i}"
                        else:
                            l "{i}Yeah. I'm getting kinda horny, so you're in luck... I'll slide my wet finger into my asshole for you, but send the money first.{/i}"
                        $ flena = "slutshy"
                        "He made the payment right away."
                        if lena_money < 5:
                            call money(1) from _call_money_72
                        else:
                            if v8_stalkfap_dm1 > 0 and lena_money < 6:
                                call money(1) from _call_money_73
                                l "There it is... With this and what that other guy sent me, I can at least buy something useful."
                            else:
                                play sound "sfx/moneyup.mp3"
                                l "There it is... It's not much compared to what I already have, but every bit helps, I guess."
                        guy "{i}This better be worth it.{/i}"
                        show lenanude2 at right with move
                        show v8_sexting5
                        with short
                        $ lena_lena_pics.append("v8_sexting5.webp")
                        if v8_lena_sexting != "n":
                            "I wasn't lying when I wrote that I was getting horny. And not only because of the sexting..."
                            "Being told what to do and showing myself off was quite appealing, too, even if it was by some random guy."
                        else:
                            "I wasn't lying when I wrote that I was getting horny. Being told what to do and showing myself off was surprisingly appealing..."
                        "And since I was doing this, I might as well enjoy it."
                        "I began the video by filming a close-up of my crotch, sliding my finger over my pussy..."
                        "I rubbed the clit for a while, building up the moisture, before sliding it in."
                        l "Nhhh..."
                        "I got even hornier as I played with my pussy, enjoying the pleasure I was providing to myself."
                        "That was enough foreplay. Time to show that guy what he wanted to see..."
                        hide v8_sexting5
                        show v8_sexting6
                        with short
                        pause 1
                        $ lena_lena_pics.append("v8_sexting6.webp")
                        play sound "sfx/mh1.mp3"
                        l "Uhhh..."
                        "I slowly dug my finger into my asshole, using my own juices as lubricant."
                        if lena_anal > 0:
                            "My anus welcomed my finger. I had been training it lately with the plug..."
                            if lena_anal_first != "n":
                                "Not to mention that delicious anal sex I had gotten a couple of weeks ago."
                                "A hard cock stretching my backside... I couldn't wait to do it again."
                            else:
                                "The time to finally try anal sex had to be near... I wanted to know how it really felt having a cock penetrating my backside."
                        else:
                            "I wasn't used to having stuff put into my butt, but it wasn't too hard for me to slide my finger in."
                            "And it wasn't painful. A bit weird, yeah, but not bad..."
                        "I began moving my finger back and forth, slowly fucking my asshole with it."
                        "To think someone was about to see me doing this... I was about to share the most intimate and naughty moment with some guy whose face I didn't even know."
                        "He had paid for it, though... And I was sure he was eagerly waiting, hard cock in hand."
                        "He would masturbate while watching me do the same, getting turned on by my body, by my impishness..."
                        hide v8_sexting6 with short
                        show lenanude2 at truecenter with move
                        l "I'm enjoying this, but I think it's enough..."
                        "I stopped the recording and sent it to my subscriber."
                        l "{i}So, is this worth it?{/i}"
                        guy "{i}Fuck yeah. Damn, you really love playing with your ass, don't you?{/i}"
                        l "{i}I love it {image=emoji_fire.webp} {image=emoji_heart.webp} {/i}"
                        guy "{i}You looked rather innocent but I knew you were a horny little slut. I love being right.{/i}"
                        l "{i}Enjoy the video. And if you want to send some more money my way, I'll be happy to show you more {image=emoji_kiss.webp} {/i}"
                        stop music fadeout 2.0
                        $ flena = "flirtshy"
                        l "That was actually kinda sexy... Now I'm all hot and bothered."
                        l "I guess I will finish the job before going to sleep..."
                        scene lenaroomnight with long
                        "It didn't take me long to make myself cum, and I tried getting some sleep right after that."

                    "I'm not doing that":
                        $ renpy.block_rollback()
                        l "{i}I'm not doing that, sorry.{/i}"
                        guy "{i}Why not?{/i}"
                        l "{i}That's not what we agreed on. You said you'd pay extra for a custom video and I did just what you asked.{/i}"
                        l "{i}Can I get that premium you promised?{/i}"
                        jump v8stalkfapfight


    else:
        stop music fadeout 2.0
        scene lenaroomnight with long
        "I decided to call it a day and try to get some sleep. I was in need of it."
        if v8_stalkfap_dm1 > 1:
            $ gallery_unlock_scene("CH08_S04")
    $ renpy.end_replay()
    jump v8lenafriday

########################################################################################################################################################################################################################################################################
##LENA FRIDAY CONCERT ########################################################################################################################################################################################################################################################################
########################################################################################################################################################################################################################################################################
label v8lenafriday:
    stop music fadeout 2.0
    call calendar(_day="Friday") from _call_calendar_73

    $ flena = "sad"
    $ lena_look = 1
    $ fmolly = "n"
    play music "music/normal_day4.mp3" loop
    scene cafe with long
    "Next morning flew by astonishingly fast."
    show lenawork with short
    "I was fidgety and distracted during work, my mind focused on the moment I had been dreading."
    "It was almost upon me..."
    show lenawork at rig with move
    show molly at lef with short
    mo "You look anxious, Lena."
    l "Yeah... I'm a bit nervous."
    if cafe_music:
        mo "Why don't you call it a day and go home to get ready? You're doing this for us, after all, so technically it counts as work!"
        $ flena = "n"
        l "Thanks, Molly. I'll be back in a few hours."
    else:
        mo "Why don't you call it a day and go home to get ready? Ed and I can take it from here."
        if cafe_nude:
            mo "You've already done more than enough for this café. You deserve some time to yourself."
        $ flena = "n"
        l "Thanks, Molly... I'll see you on Monday."
        mo "Good luck with tonight's concert!"
    scene street with long
    "I went home to get myself ready. The last moments of calm before the storm..."
    $ flena = "worried"
    scene lenaroom
    show lena
    with long
    l "Alright, let's see here..."
    "I revised all my papers and notes, making sure I wasn't forgetting anything. Everything had to be perfect..."
    "I was feeling really agitated..."
    l "Damn, I thought I had this more under control after these past few days..."
    "I closed my eyes and took a deep breath."
    l "Relax... You're making a big deal out of things. I'm not a kid on her first day at school anymore..."
    $ flena = "n"
    l "It's just a concert in front of a small audience. And most of them will be friends."
    if v8_jeremy_flirt:
        "I looked at my phone, trying to find something to calm me down a bit."
        "Jeremy texted me late last night, but I hadn't answered yet."
        nvl clear
        j_p "{i}Hey, sorry for ghosting you. Louise called and had me on the phone for like an hour {image=emoji_ups.webp}{/i}"
        l_p "{i}I know, I heard. Don't worry about it.{/i}"
        play sound "sfx/sms.mp3"
        "He replied right away."
        j_p "{i}I really liked that picture, though. I won't complain if you decide to send another {image=emoji_flirt.webp}{/i}"
        "As much as I wanted, I couldn't get into it at that moment. I had to focus on tonight."
        l_p "{i}I might, at another moment. By the way, you'll keep this between us, right?{/i}"
        if ian_lena_love == False:
            j_p "{i}Of course! My lips are sealed {image=emoji_wink.webp}{/i}"
            l_p "{i}This also goes for everything that happened at Ivy's place. You haven't told Ian and the others, right?{/i}"
            j_p "{i}Not yet, at least not all the details... But I was meaning to.{/i}"
            l_p "{i}Don't. And I doubt Louise would feel comfortable if other people knew. And I also value my privacy.{/i}"
            j_p "{i}Alright, if that's important to you.{/i}"
        else:
            j_p "{i}Of course! My lips are sealed...{/i}"
        j_p "{i}As long as I get some more pictures like that one... {image=emoji_crazy.webp}{/i}"
        l_p "{i}Be a good boy and you might {image=emoji_devil.webp}{/i}"
        if v6_axel_work or v4_axel_date:
            "As I stared at my phone, what Ivy told me the other day popped into my mind."
        else:
            "I put down my phone. I had to choose the outfit for tonight."
    else:
        "For some reason, just at that moment what Ivy told me the other day came back to mind."
##AXEL CALL
    $ flena = "sad"
    "The opportunity Axel presented..."
    "Ivy said he would surely accept if I asked him to contact that big modeling agency, Wildcats."
    if v6_axel_work or v4_axel_date:
        "Was I really considering the possibility, after all that had happened between us?"
    else:
        $ flena = "serious"
        "But the last thing I wanted was to have anything to do with my crazy ex-boyfriend. Especially after the stunt he pulled last time."
    $ flena = "worried"
    l "Why am I thinking about that now?"
    if cafe_help:
        "I was running out of options, though. I had lost my job at the restaurant, I was barely getting any modeling gigs and the café was about to close, unless we managed to pull something off."
    else:
        "I was running out of options, though. I had lost my job at the restaurant, I was barely getting any modeling gigs and the café was about to close for sure."
    if stalkfap_pro:
        "I couldn't rely on just Stalkfap and those life drawing events to keep me afloat. And what was the alternative? Getting another waitressing job?"
    else:
        "I couldn't rely on just those life drawing events to keep me afloat. And what was the alternative? Getting another waitressing job?"
    "That wasn't going to cut it... Especially considering my family's financial situation."
    l "And here I am playing musician... What the hell am I even doing?"
    if v6_axel_work or v4_axel_date:
        menu:
            "{image=icon_friend.webp}Call Axel":
                $ renpy.block_rollback()
                $ lena_axel_dating = True
                $ flena = "blush"
                "I picked up my phone and dialed his number, but didn't press \"call\"."
                if v6_axel_work:
                    "This shouldn't be that big of a deal. I had already worked with him recently, and Ivy offered to come with me..."
                    if lena_axel_desire:
                        "That wasn't the issue, though. That dream..."
                        scene v7_dream2 with flash
                        "Dreams tend to fade from memory rather easily. This one didn't."
                        "The images, the sensations, still lingered in my mind... and body."
                        "Sensations I had felt for real when we did that photo shoot together. His hands on my body again..."
                        scene lenaroom
                        show lena
                        with long
                        if axel_pictures_watch:
                            "I also remembered those Polaroids I found between the pages of my notebook. The ones I ended up masturbating to..."
                        "I knew there was something below the surface I should worry about, something I should probably deal with."
                        "Instead, I chose to call Axel."
                    elif v6_axel_pose > 1:
                        if v6_axel_pose == 3:
                            "And the situation escalated way more than I could've foreseen. That photo shoot had awakened some feelings in me I wasn't proud of..."
                            "And that dream..."
                            if axel_pictures_watch:
                                "I also remembered those Polaroids I found between the pages of my notebook. The ones I ended up masturbating to..."
                            "I knew there was something below the surface I should worry about, something I should probably deal with."
                            "Instead, I chose to call Axel."
                        else:
                            "The situation was rather forced, and it made me feel quite uncomfortable, to the point of having to put a stop to it."
                            if axel_pictures_watch:
                                "I remembered those Polaroids I found between the pages of my notebook. The ones I ended up masturbating to..."
                                "But I had things under control. I knew when to say no."
                                "There shouldn't be any problem with me calling Axel..."
                            if axel_pictures_destroy:
                                "I had also gotten rid of those Polaroids I found between the pages of my notebook. I was committed to moving on from the past."
                                "I had things under control. There shouldn't be any problem with me calling Axel..."
                    elif v6_axel_pose == 1:
                        "But that didn't turn out so well. I refused to pose with him when asked to do so."
                        $ flena = "serious"
                        "Or I should rather say coerced, not asked..."
                        $ flena = "blush"
                        "It wasn't the same thing posing with him as modeling for him, though. That much should be fine."
                        if axel_pictures_watch:
                            "I remembered those Polaroids I found between the pages of my notebook. The ones I ended up masturbating to..."
                            "But I had things under control. I knew when to say no."
                            "There shouldn't be any problem with me calling Axel..."
                        if axel_pictures_destroy:
                            "I had also gotten rid of those Polaroids I found between the pages of my notebook. I was committed to moving on from the past."
                            "I had things under control. There shouldn't be any problem with me calling Axel..."
                elif v4_axel_date:
                    if lena_job_seymour or v6_agnes_shoot:
                        "That meeting I had with him had supposedly given us both closure. He had been acting civil after that, even when I refused to work with him."
                    else:
                        "That meeting I had with him had supposedly given us both closure. He had kept his distance ever since."
                    "Maybe he had really moved on..."
                    if axel_pictures_watch:
                        "Had I, though? I remembered those Polaroids I found between the pages of my notebook. The ones I ended up masturbating to..."
                        "But I had things under control. I knew when to say no."
                        "There shouldn't be any problem with me calling Axel..."
                    if axel_pictures_destroy:
                        "I knew I had. I got rid of those Polaroids I found between the pages of my notebook. I was committed to moving on from the past."
                        "I had things under control. There shouldn't be any problem with me calling Axel..."
                if lena_axel == 3:
                    "I was convinced we could deal with each other in a civil, mature way. And Ivy offered to come with me."
                    "I really needed to jump on this opportunity.  "
                elif lena_axel == 2:
                    "I wasn't exactly comfortable around Axel, and dealing with him would surely be a challenge..."
                    "But Ivy offered to come with me, and I needed to jump on this opportunity."
                else:
                    $ flena = "serious"
                    "I wasn't thrilled about having to ask him any favors, or about working with him again..."
                    "At least Ivy offered to come with me, and I needed to jump on this opportunity."
                $ flena = "sad"
                hide lena
                show lena_phone
                with short
                "I pressed \"call\"."
                l "..."
                show phone_axel at lef3 with short
                if lena_axel > 1:
                    x "Hey, Lena. Is everything alright?"
                    l "Hi, Axel. Yes, why do you ask?"
                    if v6_axel_pose == 1 or (lena_job_seymour and v6_axel_work == False):
                        x "I wasn't expecting to get a call from you. Especially after what happened at the photo shoot."
                        l "Yeah, well... I'm sorry about that, but you can surely understand me..."
                        hide phone_axel
                        show phone_axel_smile at lef3
                        x "I do. Don't worry about it."
                    else:
                        x "I wasn't expecting to get a call from you."
                        l "Well, yeah..."
                else:
                    x "Lena..."
                    l "Hi, Axel."
                    x "Is everything alright?"
                    l "Yeah."
                    if v6_axel_pose == 1 or (lena_job_seymour and v6_axel_work == False):
                        x "I'm surprised you're calling. Especially after what happened at the photo shoot..."
                        l "Well, you surely can understand why I reacted like that..."
                        x "Sure, I do. Don't worry about it."
                    else:
                        x "I'm surprised you're calling..."
                        l "I wasn't planning to, but..."
                hide phone_axel
                hide phone_axel_smile
                show phone_axel_smile at lef3
                x "Is there anything I can do for you?"
                l "Yes, in fact... That's the reason why I'm calling."
                l "I spoke to Ivy and she told me about the pictures you took for her..."
                x "To try and get Wildcats interested in her, yeah."
                l "She told me I should speak to you. It's a good modeling opportunity, so..."
                x "I'll be happy to help."
                if lena_axel > 1:
                    $ flena = "n"
                    l "Really? You will?"
                    x "Of course... It's the least I can do, after all..."
                else:
                    l "That's it? That easy?"
                    x "Would you rather I asked for something in return?"
                    l "No, I'd rather not..."
                x "I'll tell you the same I told Ivy, though: there's no guarantee of anything."
                x "Wildcats is an extremely picky agency from what I hear, and they have plenty of models as it is."
                x "I've only started working with them recently, so it's not like I have a lot of influence..."
                x" But they seem to like my work and I'll probably work with them again, so having your pictures in my portfolio might get you noticed."
                x "Especially if I make a direct suggestion you're a model they might want to work with..."
                $ flena = "n"
                l "That'd be helpful."
                x "Let me check my agenda and I'll text you to set an appointment in one or two weeks..."
                if lena_axel > 1:
                    l "Alright. Let me know when you can set the shoot up..."
                    l "And thanks, Axel."
                    x "You don't need to thank me. Take care, Lena."
                else:
                    $ flena = "worried"
                    l "Can't you use some of my old pictures?"
                    x "I'd rather not. I only have my most recent and best work in my portfolio. Those old pictures... They're not up to my current standards."
                    $ flena = "sad"
                    l "Alright... Let me know when you can set the shoot up."
                    x "I will. Take care, Lena."
                hide phone_axel_smile
                hide lena_phone
                show lena
                with short
                if lena_axel > 1:
                    l "That was easy..."
                    l "I guess it was the right choice to make, after all."
                else:
                    l "That wasn't so bad..."
                    l "I hope I made the right choice."
                $ flena = "n"
                l "Now I have to get ready for the concert! I haven't decided what outfit to wear yet..."

            "I don't need him" if lena_axel_desire == False:
                $ renpy.block_rollback()
                $ flena = "blush"
                l "This is a bad idea. Anyone would be able to tell."
                if v6_axel_pose > 1:
                    l "Especially after what happened during that photo shoot... And the dream I had after that..."
                elif v6_axel_pose == 1:
                    l "I refused to pose with him for a reason. I'm not comfortable at all around him."
                if v4_axel_date:
                    "That meeting I had with him had supposedly given us both closure, but still..."
                l "I'm not ready for this. It would surely end in a disaster."
                if axel_pictures_watch:
                    "I remembered those Polaroids I found between the pages of my notebook. The ones I ended up masturbating to..."
                    l "No, no. This is so clearly a bad idea. I'm not over him, not yet."
                $ flena = "serious"
                l "I need to pull myself together!"
                l "I can't resort to him... No matter how good that modeling opportunity sounds. I can manage things myself."
                jump v8rejectaxel

    else:
        $ flena = "serious"
        l "Pull yourself together, Lena! This is not the moment to think like this."
        $ flena = "n"
        l "Not everything is so terrible... I'll manage, I'm sure."
        label v8rejectaxel:
            $ flena = "n"
            if cafe_help:
                l "We can still save the café. It's not much, but at least I'll still have a stable job."
            if billy_model:
                l "There's also that guy, Billy... He seemed interested in doing business with us. That could lead to something."
            if lena_job_seymour and v6_axel_pose != 1:
                l "And most importantly, Mr. Ward is still interested in me as a model. That could prove to be really profitable."
            if stalkfap_pro:
                l "Worst case scenario, I still have Stalkfap. I bet I can get more money out of that."
        l "I don't need Axel. I never did."
        if lena_passion == "model":
            $ flena = "sad"
            l "Losing this opportunity will hurt my modeling career, and that really sucks. This is a really difficult choice to make, but..."
        if lena_passion == "money":
            $ flena = "sad"
            l "It hurts to lose this opportunity to make some money. This is a really difficult choice to make, but..."
        $ flena = "n"
        l "I will pull this off myself, starting with today's concert!"
        l "Which reminds me... I need to choose an outfit for today."
    l "Let's see..."
    $ v8_sy = False # Lena wears Seymou'rs necklace
    $ v8_choker = False    # Lena wears the choker
    show lena at left with move
    hide lena with short
    label v8concertoutfitpick:
        $ lena_look = "sexy1"
    show lenabra2 at left
    with long
    call screen v8lenawardrobe
    if lena_look == 4:
        hide lenanude
        hide lenabra2
        hide lena
        show lena at left
        with long
        l "Something casual will do just fine. I want to feel at ease today..."
    if lena_look == 3:
        hide lenanude
        hide lenabra2
        show lena at left
        with long
        l "I should pick something a bit nice for today, I guess. It's a special day..."
    if lena_look == "wits":
        hide lenanude
        hide lenabra2
        hide lena
        show lena at left
        with long
        l "Perfect. Cute and elegant but still casual enough. I like it."
    if lena_look == "sexy1":
        hide lenanude
        hide lenabra2
        hide lena
        show lena at left
        with long
        l "This is what I need... At least I will look good even if I play badly!"
    menu:
        "Pick this outfit":
            $ renpy.block_rollback()
            l "This is the look."

        "Try something else":
            $ renpy.block_rollback()
            l "Maybe I can try something different?"
            hide lena
            show lenabra2 at left
            with short
            jump v8concertoutfitpick

    show lena at truecenter with move
    l "Should I wear a necklace?"
    menu v8picknecklace:
        "{image=icon_lust.webp}Wear the choker" if lena_lust > 4:
            $ renpy.block_rollback()
            l "Maybe that choker will look good..."
            hide lena with short
            $ lena_necklace = "choker"
            show lena with short
            menu:
                "Wear it":
                    $ renpy.block_rollback()
                    $ v8_choker = True
                    $ flena = "smile"
                    l "I like how it looks!"

                "Try something else":
                    $ renpy.block_rollback()
                    l "I'm not sure... Let's rethink this."
                    hide lena with short
                    $ lena_necklace = 0
                    show lena with short
                    jump v8picknecklace

        "{image=icon_charisma.webp}Wear the onyx necklace" if v7_necklace_sell == False and seymour_necklace and lena_charisma > 3:
            $ renpy.block_rollback()
            l "Tonight might be a good moment to wear that beautiful necklace Mr. Ward gifted me..."
            hide lena with short
            $ lena_necklace = "seymour"
            show lena with short
            menu:
                "Wear it":
                    $ renpy.block_rollback()
                    $ v8_sy = True
                    $ flena = "happy"
                    l "Yes, I like it!"

                "Try something else":
                    $ renpy.block_rollback()
                    l "I'm not convinced..."
                    hide lena with short
                    $ lena_necklace = 0
                    show lena with short
                    jump v8picknecklace

        "No necklace":
            $ renpy.block_rollback()
            l "Like this is okay."

    l "Now a bit of makeup and I'll be done..."
    scene lenaroom with long
    scene lenahome with long
    $ lena_makeup = 1
    $ flena = "n"
    $ flouise = "smile"
    $ louise_look = 1
    play sound "sfx/door.mp3"
    show lena at rig with short
    l "Alright."
    show louise at lef with short
    lo "Are you ready?"
    l "Yeah. As ready as I can be."
    if lena_louise_sex_late:
        "Louise and I hadn't talked about what happened the other night yet."
        "Maybe we should, but I wasn't sure. We were close friends, she was going through a bad breakup... These things can happen."
        "It seemed that helped her, at least. She looked way more cheerful."
    elif v7_bbc == "lena":
        "The tension between Louise and me had cleared up a bit, or so it felt to me."
        if v8_jeremy_flirt:
            "I shouldn't have kept flirting with Jeremy through text messages, but..."
            "I wasn't the only one pushing this situation, was I?"
        else:
            "I should try and stay away from Jeremy from now on..."
# STAN JOINS
    if lena_stan > 5:
        $ fstan = "smile"
        show lena at rig3
        show louise at truecenter
        with move
        show stan at lef3 with short
        st "It's time?"
        l "Yeah."
        $ flouise = "serious"
        lo "Are you coming too?"
        $ fstan = "n"
        st "Yeah... Lena invited me to."
        lo "..."
        "It looked like Louise wanted to complain or say something, but she held her tongue."
        lo "Alright."
        l "Let's go, guys."
    else:
        lo "Let's go!"
##SOCIAL MOMENT
    if ian_holly_dating:
        $ holly_look = 2
    stop music fadeout 2.0
    play music "music/girls_day.mp3" loop
    if cafe_music:
        scene cafe_concert
    else:
        scene recordstore
    with long
    $ flena = "n"
    $ femma = "n"
    $ fivy = "smile"
    $ fian = "smile"
    $ fperry = "n"
    $ fed = "n"
    $ fmolly = "smile"
    $ fseymour = "smile"
    $ seymour_look = 1
    $ ivy_look = "sexy"
    $ ian_look = 3
    $ holly_look = 1
    $ perry_look = 1

    if cafe_music:
        "When I arrived at the café Ed had already started settings things up."
        "He cleared the space and set up a small stage in front of the counter. The audience wouldn't take long to start arriving."
        show lena
        show emma at lef3
        show ed at rig3
        with short
        e "Hey! Can I get some help with the drums?"
        ed "On it."
        l "Let me help too."
        ed "You're already helping us by setting this up! Don't worry."
        if v8_sy:
            e "Wow, what a beautiful necklace! It looks expensive!"
            l "It is..."
            e "Where did you get it? Ah, never mind, tell me later. Let's finish setting things up first!"
        hide emma
        hide ed
        with short
    else:
        "When I arrived at the record store Emma was already setting the stage up."
        show lena
        show emma at lef3
        show record_owner at rig3
        with short
        e "Hey! Here you are."
        l "Can I help with something?"
        martin "I just finished setting up the equipment. The stage is all yours as soon as the audience starts arriving."
        if v8_sy:
            e "Wow, what a beautiful necklace! It looks expensive!"
            l "It is..."
            e "Where did you get it? Ah, never mind, tell me later. Let's finish setting things up first!"
        else:
            "It wouldn't take long."
        hide emma
        hide record_owner
        with short
    $ flouise = "happy"
    show louise at lef3
    if lena_stan > 5:
        $ fstan = "smile"
        show stan at left5 behind louise
    with short
    lo "You're finally getting on stage! Nervous?"
    $ flena = "sad"
    l "More than I'd like, yeah..."
    show lena at rig3 with move
    $ flouise = "surprise"
    $ fstan = "surprise"
    $ flena = "n"
    show ivy with short
    v "Hey there, baby! How are you feeling on the day of your debut?"
    if v7_bbc != "lena":
        $ flouise = "mad"
        lo "What is she doing here?"
        $ flena = "worried"
        $ fivy = "smile"
        v "Isn't it obvious? I'm here to support my best friend."
        lo "You...!"
        "Damn. I wasn't counting on this."
        $ flena = "sad"
        l "Please girls, let's not fight..."
        v "Oh, fighting is the last thing I want to do! I'm here to support you."
        $ fivy = "flirt"
        hide ivy
        show ivy2
        with short
        v "Stirring up a fight right now would be the least helpful thing."
        $ flouise = "serious"
        lo "...!"
        if lena_stan > 5:
            lo "Let's go, Stan!"
            $ fstan = "worried"
            hide louise with short
            st "Uh? Oh... okay."
            hide stan with short
        else:
            lo "Whatever."
            hide louise with short
        $ fivy = "n"
        hide ivy2
        show ivy
        with short
        show ivy at lef
        show lena at rig
        with move
        v "Jeez. She really can't stand me."
        $ flena = "serious"
        l "Can you blame her?"
        if v7_game:
            v "I hope you're not blaming {i}me{/i}! You were there, too!"
            l "Yeah, so?"
            v "You saw what happened. Jeremy had other priorities other than his \"girlfriend\". Not that we didn't know that already..."
            v "If anything, I think I did her a favor."
        else:
            v "I hope you're not blaming {i}me{/i}!"
            v "If anything, I think I did her a favor. She was too naive to see that her \"boyfriend\" had other priorities than her..."
        $ flena = "sad"
        l "I doubt she sees it that way."
        v "Beats me. But I'm not here to talk about her!"
        $ fivy = "smile"
    else:
        $ flouise = "serious"
        lo "Oh, so you also came..."
        v "What do you mean, \"also\"? I'm here to support my best friend!"
        $ flena = "smile"
        v "No way I could miss this."
        lo "Yeah, of course."
        $ fivy = "flirt"
        hide ivy
        show ivy2
        with short
        v "By the way, is Jeremy coming, too?"
        $ flena = "shy"
        $ flouise = "blush"
        lo "Uh, no, he's... busy."
        v "I'm sure he is. Too bad, huh, Lena?"
        l "I don't mind. It's not like we're that close..."
        $ flouise = "serious"
        if lena_stan > 5:
            lo "I'll go save a seat."
            lo "Let's go, Stan!"
            $ fstan = "worried"
            hide louise with short
            st "Huh? Oh... okay."
            hide stan with short
        else:
            lo "I'll go save a seat."
            hide louise with short
        l "Stop being a trouble-maker!"
        v "Me? Look who's talking, ha ha!"
        v "I'm surprised she hasn't grilled you out after what happened at my place... I guess she really considers you her friend."
        l "It was just a game... She knows that."
        v "I'm sure she does. But let's not talk about her!"
        $ flena = "smile"
        $ fivy = "smile"
        hide ivy2
        show ivy
        with short
        show ivy at lef
        show lena at rig
        with move
    v "What's wrong with you? You look even more panicked than the first time you posed naked in front of a camera!"
    $ flena = "worried"
    l "Yeah, well... I thought I had it under control, but I'm not so sure anymore."
    $ fivy = "n"
    v "No matter how many years we've known each other, sometimes I really don't get you, girl."
    v "Why the hell would this make you nervous? You've posed naked in front of a room full of people!"
    $ flena = "serious"
    l "I don't know why it is so hard to understand! Everyone's asking me the same question..."
    $ flena = "sad"
    l "To me playing my songs in front of people is..."
    l "It means showing the world something much more intimate than just my body."
    v "You really are a weird one..."
##IAN TALK
    $ v8concertian = 0 ## what Lena responds to Ian when he offers help
    show ivy at left
    show lena at right
    with move
    show ian at rig
    show perry at lef
    with short
    if ian_lena_dating and ian_lena_over == False and lena_ian_mad == False:
        i "That's part of Lena's irresistible charm."
        $ flena = "smile"
        l "Ian, Perry! You came!"
        i "Was there any doubt?"
        if v7_holly_kiss:
            if lena_ian_love:
                "What happened between Ian and Holly was still bugging me... But I couldn't help but feel happy Ian showed up. He usually managed to brighten up my day."
            else:
                "What happened between Ian and Holly was still bugging me... But I still was happy to see Ian. I liked having him around."
        p "Hey..."
        $ fperry = "meh"
        "Perry looked at Ivy."
        p "I'm P-{w=0.5}-Perry, nice to m-{w=0.5}-meet you."
        $ fivy = "smile"
        hide ivy
        show ivy2 at left
        with short
        v "Oh, what's this? Do I make you nervous?"
        p "No... I have a stutter."
        $ fivy = "n"
        hide ivy2
        show ivy at left
        with short
        v "Oh."
        $ fivy = "smile"
        v "And here I was feeling imposing!"
        hide perry
        show perry2 at lef
        with short
        p "{i}You're imposing alright...{/i}"
        v "What was that?"
        p "N-{w=0.5}nothing."
        i "Can we do something for you, Lena?"
        menu:
            "{image=icon_love.webp}Ask for a kiss" if lena_ian_love:
                $ renpy.block_rollback()
                $ v8concertian = 1
                $ flena = "shy"
                l "I could use a good luck kiss..."
                if v7_holly_kiss:
                    $ fian = "worried"
                    i "Uh, I would love to, but... maybe now's not the best moment."
                    $ flena = "sad"
                    $ fian = "n"
                    i "Someone's here to see you."
                    jump v8concerthollytalk
                else:
                    if ian_lena_love:
                        $ fian = "shy"
                        i "Of course."
                    else:
                        $ fian = "blush"
                        i "Oh... Sure."
                    if cafe_music:
                        scene cafe_concert
                    else:
                        scene recordstore
                    if lena_look == 3:
                        show v8_iankiss1
                    if lena_look == 4:
                        show v8_iankiss2
                    if lena_look == "wits":
                        show v8_iankiss3
                    if lena_look == "sexy1":
                        show v8_iankiss4
                    with long
                    "Ian embraced me and I leaned in for a kiss."
                    "Having him there, feeling his arms around my waist, made me feel safer."
                    "If everything went wrong tonight, at least I'd end the day in his arms."
                    $ fivy = "flirt"
                    $ fian = "smile"
                    $ flena = "happy"
                    if cafe_music:
                        scene cafe_concert
                    else:
                        scene recordstore
                    show ivy at left
                    show lena at right
                    show ian at rig
                    show perry2 at lef
                    with long
                    i "I hope it helped."
                    l "I think it did."
                    if ian_lena < 12:
                        call friend_xp('lena') from _call_friend_xp_657
                    v "Save it for later, lovebirds!"

            "You being here is enough":
                $ renpy.block_rollback()
                $ v8concertian = 2
                l "Don't worry. You being here is enough..."
                if lena_ian_love:
                    l "It means a lot to me."
                else:
                    l "I really appreciate it."
                i "That's what friends are for."
                $ fivy = "flirt"
                v "Yeah... {i}Friends{/i}."

            "I'm fine":
                $ renpy.block_rollback()
                $ v8concertian = 3
                l "Don't worry. I'm fine."

        if v7_holly_kiss:
            $ fian = "n"
            i "By the way... Someone's here to see you."
            jump v8concerthollytalk

    else:
        i "Being normal is way too boring."
        $ flena = "smile"
        l "Ian, Perry! You came."
        p "Hey..."
        if ian_lena_over:
            i "Of course..."
            $ fian = "n"
            $ flena = "n"
            "Interacting with Ian was a bit awkward, considering our last conversation."
            $ flena = "sad"
            if lena_ian_love:
                "Whatever we had going on had come to an abrupt stop. And I resented him for it."
                "I was starting to really enjoy our relationship... But Ian disappointed me."
                "I was dealing with that in a very civil way, though. And so was he, thankfully."
            else:
                "Whatever we had going on had come to an abrupt stop. At least he was dealing with it in a very civilized and mature way..."
                "Even if his actions that had led us here hadn't been exactly mature."
        else:
            i "Of course! I told you we wouldn't miss it."
        $ flena = "n"
        $ fperry = "meh"
        "Perry looked at Ivy."
        p "I'm P-{w=0.5}-Perry, nice to m-{w=0.5}-meet you."
        $ fivy = "smile"
        hide ivy
        show ivy2 at left
        with short
        v "Oh, what's this? Do I make you nervous?"
        p "No... I have a stutter."
        $ fivy = "n"
        hide ivy2
        show ivy at left
        with short
        v "Oh."
        $ fivy = "smile"
        v "And here I was feeling imposing!"
        hide perry
        show perry2 at lef
        with short
        p "{i}You're imposing alright...{/i}"
        v "What was that?"
        p "N-{w=0.5}nothing."
        $ fian = "smile"
        i "So... Can we do something for you, Lena?"
        if ian_lena_over:
            l "Don't worry. I'm fine."
            $ fian = "n"
            i "Alright... By the way, someone's here to see you."
            jump v8concerthollytalk
        else:
            $ flena = "smile"
            l "Don't worry. You being here is enough..."
            l "I really appreciate it."
            i "That's what friends are for."
        if lena_ian_mad:
            $ fperry = "serious"
            p "I hope that guy d-{w=0.5}doesn't show up today... He's not c-{w=0.5}coming, right?"
            $ flena = "sad"
            $ fian = "sad"
            l "You mean Robert?"
            p "Yeah, that asshole."
            $ flena = "serious"
            l "No, he's not. Last thing I need is another fight..."
            $ fivy = "flirt"
            i "I'm really sorry..."
            p "Don't apologize, dude. It wasn't your f-{w=0.5}fault!"
            menu:
                "{image=icon_mad.webp}I'm still mad!":
                    $ renpy.block_rollback()
                    $ ian_lena_over = True
                    l "That barely matters. The result was the same, and it made me feel really ashamed!"
                    if ian_lena > 4:
                        call friend_xp('lena', -1) from _call_friend_xp_658
                    $ fian = "n"
                    $ fperry = "sad"
                    $ fivy = "n"
                    i "I see you're still mad about it. I really messed up big time, huh?"
                    l "Yeah..."
                    v "I see what happened as something flattering..."
                    l "We see things very differently, then."
                    $ flena = "n"
                    l "But please, let's not talk about that right now."
                    i "Sure. Sorry that Perry brought that up."
                    $ fperry = "meh"
                    p "Hey!"

                "{image=icon_friend.webp}Forgive Ian":
                    $ renpy.block_rollback()
                    $ flena = "sad"
                    l "I know... I got really upset because it was a very embarrassing situation for me..."
                    $ flena = "n"
                    l "But Perry's right. It wasn't your fault, Ian."
                    if ian_lena < 12:
                        call friend_xp('lena') from _call_friend_xp_659
                    $ fian = "smile"
                    v "It wasn't so awful! I see what happened as something flattering..."
                    $ flena = "smile"
                    l "Next time you deal with it, then!"

    if ian_lena_dating and v7_holly_kiss:
        label v8concerthollytalk:
            "Ian pointed to the side."
        hide perry2
        hide ivy
        with short
        show ian at left
        show lena at lef
        with move
        $ fholly = "blush"
        show holly3 at right with short
        l "Holly..."
        hide ian with short
        show lena at lef
        show holly3 at rig
        with move
        $ flena = "smile"
        l "Hey! You came..."
        h "Yes..."
        h "I wasn't sure if it was a good idea, but Ian said I should..."
        l "I'm glad you're here to support me."
        $ fholly = "worried"
        "Holly looked up at me."
        h "Lena, I'm so sorry. I..."
        l "It's okay, don't worry about it."
        l "We can talk about that later, but you should know I'm not upset with you."
        $ fholly = "blush"
        h "Still, I... I don't know what to say."
        l "This Sunday Ivy and I will go shopping. We'd like you to join us."
        h "Really?"
        l "Yeah. And we'll be able to talk more then."
        l "Right now just relax and listen, okay? That's what you came here for, after all."
        h "Okay..."
        hide holly3 with short
        show lena at truecenter with move
        $ flena = "worried"
        "I wished I could follow my own advice. I was so nervous...!"
    else:
        show ian at truecenter with move
        if v8concertian == 1:
            $ fholly = "n"
            show holly3 at rig with short
            h "Am I... interrupting something?"
            $ flena = "blush"
            l "Oh, no, we were just..."
            v "Lena was in need of some encouragement, ha ha."
            hide holly3
            show holly at rig
            with short
            h "I can see why... This place is packed!"
        else:
            $ fholly = "smile"
            show holly at rig with short
            h "This place is packed!"
        $ flena = "worried"
        if cafe_music:
            scene cafe_concert
        else:
            scene recordstore
        show lena at right
        with short
        "I looked around. Holly was right..."
        show lena at truecenter with move
    if cafe_music:
        "I had never seen so many people in the café!"
        "Some were familiar faces, but quite a lot of new people had decided to show up to the event."
        "In any case, this was exactly what we wanted. My idea to bring more people to the café was working..."
        "I just hoped it didn't come crashing down after my performance!"
    else:
        "The record store was crawling with people. I saw some familiar faces, but most of them were new to me."
        "I wondered if the regulars knew they were about to see a newbie..."
        "I hoped the manager warned them, else they would surely feel disappointed..."
    $ fholly = "smile"
    $ fian = "smile"
    $ fperry = "n"
    $ fivy = "smile"
    $ flouise = "smile"
    $ fstan = "smile"
    if cafe_music:
        scene cafe_concert
    else:
        scene recordstore
    show ivy at right
    show holly2 at rig2
    show perry at left
    show ian at lef2
    show lena
    with short
    v "Stop being nervous already! You've got this!"
    h "I'm sure you'll do a great job."
    i "You're way too hard on yourself. People will like it!"
    $ flena = "n"
    hide perry
    if lena_stan > 5:
        hide ian
        show stan at lef2 behind lena
    show louise at left5
    with short
    lo "Yes! You'll do great!"
    if lena_stan > 5:
        st "I think so too."
    "Quite a lot of people had gathered to support me on this day."
    "I had always been rather short of friends, especially in these last couple of years, but that seemed to be changing..."
    $ flena = "smile"
    "I was grateful."
    if cafe_music:
        hide ian
        hide stan
        hide holly2
        show molly at rig2 behind lena
        show emma at lef2 behind lena
        with short
        mo "Are you ready, Lena? I think we should start..."
        $ flena = "n"
        l "Yeah. okay. Let's do this..."
        e "Come on, let's go!"
    else:
        hide ian
        hide stan
        show emma at lef behind lena with short
        e "Shall we get started? It's about time!"
        $ flena = "n"
        l "Yeah. okay. Let's do this..."
    v "Break a leg!"

## PLAYING ######################################################

    stop music fadeout 2.0
    if cafe_music:
        scene cafe_concert
    else:
        scene recordstore
    show lena
    with long
    "I took a deep breath, trying to quiet down my racing heartbeat. The moment had come."
    "I had received so much encouragement from everybody... Too much, even."
    "But that wasn't going to help me. The voice I needed to hear that encouragement from the most was still silent: my own."
    "I hadn't been able to find that faith in myself yet. But I was willing to dig deep and keep searching."
    hide lena with long
    "I stepped on the stage and picked up my guitar, ready to take the dive, hoping to find that faith during the fall."
    play sound "sfx/guitar_long.mp3"
    if cafe_music:
        scene v8_concert_bg1 at transf_bg_music
        show v8_concert_emma_cafe at transf_emma_music
        if lena_look == 4:
            show v8_concert_lena1_cafe at transf_lena_music
        elif lena_look == 3:
            show v8_concert_lena2_cafe at transf_lena_music
        elif lena_look == "sexy1":
            show v8_concert_lena3_cafe at transf_lena_music
        else:
            show v8_concert_lena4_cafe at transf_lena_music
        if v8_sy:
            show v8_concert_lena_sy at transf_lena_music
        if v8_choker:
            show v8_concert_lena_choker at transf_lena_music
    else:
        scene v8_concert_bg2 at transf_bg_music
        show v8_concert_emma_store at transf_emma_music
        if lena_look == 4:
            show v8_concert_lena1_store at transf_lena_music
        elif lena_look == 3:
            show v8_concert_lena2_store at transf_lena_music
        elif lena_look == "sexy1":
            show v8_concert_lena3_store at transf_lena_music
        else:
            show v8_concert_lena4_store at transf_lena_music
        if v8_sy:
            show v8_concert_lena_sy at transf_lena_music
        if v8_choker:
            show v8_concert_lena_choker at transf_lena_music
    with long
    "My fingers strummed the strings, plucking the first notes off the guitar."
    menu:
        "Play {i}\"Broken Dreams\"{/i}":
            $ renpy.block_rollback()
            $ v8_song = 1
            "I decided to start with the song that had gotten me writing again."
            "{i}\"Broken Dreams\"{/i}... It was a song born from dreadful feelings, hopefully transformed into something beautiful."
            "It's what I was hoping to share with my audience..."
            if lena_song1 == 6:
                play music "music/broken_dreams3.mp3" loop fadein 0.3
            else:
                play music "music/broken_dreams2.mp3" loop

        "Play {i}\"Shine Again\"{/i}":
            $ renpy.block_rollback()
            $ v8_song = 2
            "I decided to start with the last song I had written."
            "{i}\"Shine Again\"{/i}... I felt my will to stand back up and keep going was what gave meaning to this song."
            "It was perfect for this moment."
            if lena_song2 == 6:
                play music "music/shine_again2.mp3" loop
            else:
                play music "music/shine_again1.mp3" loop
    # findme
    if cafe_music:
        scene v8_concert_bg1 at transf_bg_music_anim
        show v8_concert_emma_cafe at transf_emma_music_anim
        if lena_look == 4:
            show v8_concert_lena1_cafe at transf_lena_music_anim
        elif lena_look == 3:
            show v8_concert_lena2_cafe at transf_lena_music_anim
        elif lena_look == "sexy1":
            show v8_concert_lena3_cafe at transf_lena_music_anim
        else:
            show v8_concert_lena4_cafe at transf_lena_music_anim
        if v8_sy:
            show v8_concert_lena_sy at transf_lena_music_anim
        if v8_choker:
            show v8_concert_lena_choker at transf_lena_music_anim
    else:
        scene v8_concert_bg2 at transf_bg_music_anim
        show v8_concert_emma_store at transf_emma_music_anim
        if lena_look == 4:
            show v8_concert_lena1_store at transf_lena_music_anim
        elif lena_look == 3:
            show v8_concert_lena2_store at transf_lena_music_anim
        elif lena_look == "sexy1":
            show v8_concert_lena3_store at transf_lena_music_anim
        else:
            show v8_concert_lena4_store at transf_lena_music_anim
        if v8_sy:
            show v8_concert_lena_sy at transf_lena_music_anim
        if v8_choker:
            show v8_concert_lena_choker at transf_lena_music_anim
    with ease
    if v8_song == 2:
        if lena_song2 == 6:
            pause 30
        else:
            pause 10
            "The melody began to play, and my voice followed."
            if song_2a == "crack":
                l "{i}Heart shut, the fence is up. The lock is hard to crack.{/i}"
            if song_2a == "rusty":
                l "{i}Heart shut, the fence is up. The lock is getting rusty.{/i}"
            if song_2a == "closed":
                l "{i}Heart shut, the fence is up. The lock is also closed.{/i}"
            l "{i}But who cares? You took the key... and ran away.{/i}"
    else:
        if lena_song1 == 6:
            pause 30
        else:
            pause 10
            l "{i}So vast and dark, this cosmic field of dreams we live in...{/i}"
            if song_1a == "real":
                l "{i}With our child-like hands, reaching out, trying to grasp something real.{/i}"
            if song_1a == "precise":
                l "{i}With our child-like hands, reaching out, trying to grasp something precise.{/i}"
            if song_1a == "cool":
                l "{i}With our child-like hands, reaching out, trying to grasp something cool.{/i}"
    "I chose to believe in myself, not because I was sure things would turn out as I hoped, but because I was letting myself take the chance."
    "I loved music, and I didn't want to feel afraid of something that I loved."
    "I heard the music, and my voice, but it almost felt like they belonged to someone else. I was just listening, trapped in my head."
    "A part of me wanted to be afraid. It had its own reasons to."
    "But a part of me wanted to spread her arms and embrace the world. The good, the bad..."
    "Even if I was afraid of that world outside of myself, I also was in love with it."
    "So many experiences I was grateful for. So many things I wanted to share and experience."
    "My music was a way of reaching out to the world. My way of showing appreciation for what I had received,"
    "My way of sharing who I was."
    "The music continued to play. It still felt like it was playing all by itself."
    "But bit by bit I began feeling more involved. Deeply involved."
    "I was doing it without doing anything."
    if v8_song == 2:
        if lena_song2 == 6:
            stop music fadeout 3.0
            pause 3
        else:
            if song_2b == "weeping":
                l "{i}Gray clouds in the weeping sky, when will you let the day shine?{/i}"
            if song_2b == "gray":
                l "{i}Gray clouds in the gray sky, when will you let the day shine?{/i}"
            if song_2b == "sad":
                l "{i}Gray clouds in the sad, sad sky, when will you let the day shine?{/i}"
            l "{i}Shine, shine on me, I want to be awake again.{/i}"
            stop music fadeout 3.0
            l "{i}So shine, day. Shine on me again.{/i}"
    else:
        if lena_song2 == 6:
            stop music fadeout 3.0
            pause 3
        else:
            l "{i}Like two moons on a collision course, this is our gravity...{/i}"
            if song_1b == "tragedy":
                l "{i}Like a star that burns out too quick, this is our tragedy.{/i}"
            if song_1b == "story":
                l "{i}Like a star that burns out too quick, this is our story.{/i}"
            if song_1b == "stuff":
                l "{i}Like a star that burns out too quick, this is our stuff.{/i}"
            l "{i}Ours until we burned out.{/i}"
            stop music fadeout 3.0
            l "{i}Ours until we ceased to be...{/i}"
    "My voice quieted and my fingers strummed the guitar, playing the last few chords of the melody."
    play sound "sfx/applause.mp3"
    if cafe_music:
        scene cafe
    else:
        scene recordstore
    $ flena = "shy"
    $ femma = "smile"
    show lena2 at rig
    show emma at lef
    with long
    "I looked at the crowd, and I was met with a round of applause. They seemed to be having a good time, relaxed, and with smiles on their faces."
    if v8_song == 2:
        if lena_charisma < 7:
            call xp_up('charisma') from _call_xp_up_497
        if lena_song2 > 4 and lena_charisma < 10:
            call xp_up('charisma') from _call_xp_up_498
    else:
        if lena_wits < 7:
            call xp_up('wits') from _call_xp_up_495
        if lena_song1 > 4 and lena_wits < 10:
            call xp_up('wits') from _call_xp_up_496
    if ian_lena_dating and ian_lena_over == False:
        "Ian looked at me and gave me a thumbs up."
        "His smile was contagious."
    else:
        "A smile appeared on my lips too."
    $ flena = "happy"
    "I looked back at Emma."
    l "Next one?"
    e "Let's go!"
    play music "music/emmas_theme.mp3" loop
    if cafe_music:
        scene cafe
    else:
        scene recordstore
    with long
    "The concert went on, and my fears slowly melted away."
    "I wasn't worried about the result anymore, at least not now, while I played."
    "Even when I made some mistake, or my voice didn't hit a note perfectly, I didn't dwell on it."
    "I was having fun, and so were Emma and the audience, or so it felt."
    $ flena = "n"
    $ femma = "smile"
    $ fed = "smile"
    $ fmolly = "smile"
    $ fian = "happy"
    $ flouise = "happy"
    $ fivy = "n"
    $ fholly = "happy"
    if cafe_music:
        scene cafe_concert
    else:
        scene recordstore
    with long
    play sound "sfx/applause.mp3"
    show lena
    show emma at left
    with short
    if (v8_song == 1 and lena_song1 == 6) or (v8_song == 2 and lena_song2 == 6):
        $ flena = "happy"
        "I was showered with applause. Everybody loved that last song."
    elif (v8_song == 1 and lena_song1 > 3) or (v8_song == 2 and lena_song2 > 3):
        $ flena = "smile"
        "I was met with applause. It seemed everybody liked that last song."
    else:
        "I got a round of applause. It seemed most people were satisfied with my performance."
    show emma at lef3 with move
    l "Well, that's it. We did it..."
    e "It was fun! Now go and talk to your audience, I'm sure they want to congratulate you!"
    e "I'll go get us some drinks!"
    hide emma with short
    show louise at right
    show holly at rig2
    show ian at lef2
    if lena_stan > 5:
        show stan at left5 behind ian
    else:
        show perry at left5 behind ian
    with short
    lo "That was amazing, Lena! I can't believe it, you're such an artist!"
    i "You were awesome, congrats!"
    h "That was beautiful... You should be proud!"
    $ flena = "shy"
    l "Stop it, guys...! That's way too much praise!"
    "I felt a bit overwhelmed, but I was happy. All my worries had been for naught... In the end, things turned out alright!"
    if cafe_music:
        "And quite a lot of people had shown up... Maybe this would really serve to help the café, after all."
    if lena_stan > 5:
        st "I'm no expert, but I liked it a lot... You sounded like any other professional singer I've ever heard."
        l "Thanks, Stan."
    else:
        p "I must admit you're really good, Lena. And I don't give praise easily."
        i "Believe him. He doesn't, ha ha."
    i "You're really talented, Lena."
    mr "I concur."
    $ flena = "blush"
    $ fian = "surprise"
    hide louise
    hide holly
    hide stan
    hide perry
    with short
    show lena at rig3
    show ian at left
    with move
    show seymour with short
    l "Mr. Ward...!"
    if v5_ian_showup or v7_holly_trip:
        mr "Nice seeing you too, Ian."
        $ fian = "smile"
        i "My pleasure..."
    else:
        $ fian = "blush"
    if v8_sy:
        mr "I'm glad to see you're wearing the necklace I gave you for such a special occasion..."
    if lena_job_seymour:
        if v6_axel_pose == 1:
            $ flena = "serious"
            l "What are you doing here...?"
            $ fian = "n"
            mr "I learned you'd be playing in this... unremarkable place, and my curiosity got the better of me."
            mr "I wasn't aware of this other talent of yours. I must say I'm rather impressed."
            mr "You're really the complete package, aren't you?"
            l "I'm surprised you had any interest in dropping by such an {i}unremarkable{/i} place, or in someone as unremarkable as me."
            mr "I don't recall ever saying you were unremarkable."
            l "But you dismissed me in a rather unpleasant manner."
            $ fseymour = "n"
            $ fian = "sad"
            mr "That's true. A decision you forced out of me, and one that I regret."
            l "You and your lady friend were the ones forcing an unacceptable situation on me."
            mr "It was quite a disappointing interaction. But I value you enough to offer a second chance."
        else:
            if seymour_disposition > 1:
                $ flena = "smile"
                l "I wasn't expecting to see you here today!"
            elif seymour_disposition > 0:
                $ flena = "n"
                l "I wasn't expecting to see you here today."
            elif seymour_disposition == 0:
                $ flena = "worried"
                l "I wasn't expecting to see you here today..."
                $ fian = "n"
            mr "I learned you'd be playing in this... unremarkable place, and my curiosity got the better of me."
            mr "I wasn't aware of this other talent of yours. I must say I'm rather impressed."
            mr "You're really the complete package, aren't you?"
            if seymour_disposition > 1:
                l "I don't know about that..."
            elif seymour_disposition > 0:
                l "Thank you, Mr. Ward."
            elif seymour_disposition == 0:
                $ flena = "n"
                l "I'll take that as a compliment, I guess."
                mr "As it was intended to be."
            mr "Your voice is as beautiful as your body. I was right to think you were something special..."
    else:
        $ flena = "serious"
        l "What are you doing here?"
        $ fian = "n"
        mr "I learned you'd be playing in this... unremarkable place, and my curiosity got the better of me."
        mr "I wasn't aware of this other talent of yours. I must say I'm rather impressed."
        mr "You're really the complete package, aren't you?"
        l "Do me a favor and cut out the formalities, will you?"
        $ fian = "disgusted"
        l "What's your reason to show up at such an {i}unremarkable{/i} place? Did you come here to gloat, or...?"
        mr "As I said, I just wanted to satisfy my curiosity. See how you were doing after rejecting my generous offer."
        l "I thought you'd wait for me to crawl back asking for favors... Changed your mind?"
        mr "You're remarkably stubborn, I must admit that. But I have the feeling you'll be open to re-negotiate the terms of our possible partnership..."
    $ flena = "surprise"
    $ fian = "surprise"
    $ fseymour = "surprise"
    $ femma = "mad"
    $ seymour_look = "stain"
    stop music
    play sound "sfx/splash.mp3"
    mr "...!" with vpunch
    "Mr. Ward's expensive suit got completely soaked when someone spilled a whole glass of beer on him."
    play music "music/tension.mp3" loop
    "Intentionally."
    $ fseymour = "serious"
    hide seymour
    show seymour2
    with short
    mr "What the...?!"
    show emma at lef3 with short
    e "You have some nerve showing your face around here!"
    $ flena = "worried"
    $ fian = "disgusted"
    i "Emma!"
    mr "Who the hell are you?"
    e "You don't know me, but I know you very well!"
    e "You're the guy who's extorting people out of their homes and businesses!"
    e "You and your crooked friends are the ones fucking up the city's economy with your unethical practices!"
    mr "I see. So you're one of those noisy peasants that insist on sparking public unrest with your ideologically misguided protests and propaganda."
    e "You're the only one here with a misguided ideology. And you dare walk into this place after threatening these people's livelihoods."
    e "Well, you're not welcome here."
    mr "I won't waste my breath trying to argue against such a dull interpretation of my persona and business enterprises. But know that your rude attitude speaks volumes about your moral standing."
    e "People who live in glass houses shouldn't throw stones! This is nothing compared to what your bunch is putting people through!"
    e "Do I have to pour another beer on that fancy suit of yours to make you understand you're not welcome here?"
    mr "Pretending to be the spokesperson for all these people can bring trouble upon you, and also them."
    mr "I'd watch out if I were you."
    $ fseymour = "n"
    "Seymour turned to face me."
    mr "In light of this... unpleasant inconvenience, I'll be taking my leave. Let's talk another time, Lena."
    if v6_axel_pose == 1 or seymour_disposition == 0:
        $ flena = "serious"
        l "I doubt it."
        mr "We'll see."
    elif seymour_disposition > 1:
        $ flena = "blush"
        l "Y-{w=0.3}yeah... I..."
        l "I don't know what to say."
        mr "Don't fret. See you, doll."
    else:
        l "I, uh... Sure."
    hide seymour2 with short
    show ian at truecenter
    show emma at lef3
    show lena at rig3
    with move
    i "Emma! What the hell?"
    $ femma = "sad"
    e "Sorry... But that guy is the worst!"
    $ fian = "worried"
    i "He's just a publisher..."
    $ femma = "serious"
    e "No, Ian, he's not. He has had his nose in every shady business that's going on in Baluart for quite a while."
    i "Really?"
    $ femma = "sad"
    e "I'm sorry, Lena... I hope I haven't ruined the night for you."
    if v6_axel_pose == 1 or seymour_disposition == 0:
        $ flena = "n"
        l "He deserved it."
        "That wasn't the way I was expecting the concert to end at all, but it was kind of gratifying seeing Emma disrespect Seymour like that..."
        $ flena = "sad"
        "But it would be naive to think this wouldn't have consequences..."
    elif seymour_disposition > 1:
        $ flena = "worried"
        l "I would've preferred it if you hadn't done that."
        e "Jeez, I... I got carried away, I can't stand that guy."
        "I knew Mr. Ward was an influential individual, but I had no idea to what extent. And he had some enemies, too..."
        "I wasn't sure if they stood a chance of doing anything against him, though."
    else:
        $ flena = "sad"
        l "It's not how I was expecting this to end, that's for sure..."
        e "Jeez, I... I got carried away, I can't stand that guy."
        "I knew Mr. Ward was an influential individual, but I had no idea to what extent. And he had some enemies, too..."
    stop music fadeout 2.0
    "What was I getting tangled up into?"
    jump v8b

##SCREENS ################################################################################################################################################################################################
screen v8lenawardrobe():

    imagebutton idle "v8_wardrobe1.webp" hover "v8_wardrobe1_hover.webp" focus_mask True action SetVariable("lena_look", 4) , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    if lena_charisma > 3:
        imagebutton idle "v8_wardrobe2.webp" hover "v8_wardrobe2_hover.webp" focus_mask True action SetVariable("lena_look", 3) , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "v8_wardrobe2_lock.webp"
    if lena_wardrobe_wits1:
        imagebutton idle "v8_wardrobe3.webp" hover "v8_wardrobe3_hover.webp" focus_mask True action SetVariable("lena_look", "wits") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "v8_wardrobe3_lock.webp"
    if lena_lust > 3:
        imagebutton idle "v8_wardrobe4.webp" hover "v8_wardrobe4_hover.webp" focus_mask True action SetVariable("lena_look", "sexy1") , [ Play ("ch_one", "sfx/paper_click.mp3") ] , Return() at fade_in_skill
    else:
        imagebutton idle "v8_wardrobe4_lock.webp"


transform transf_bg_music:
    zoom 1.1
    xalign 0.5

transform transf_bg_music_anim:
    subpixel True
    zoom 1.1
    xalign 0.5
    easein 10 xalign 0.0
    ease 10 xalign 1.0
    easeout 10 xalign 0.5
    repeat

transform transf_emma_music:
    xanchor 0.0
    xpos -0.075

transform transf_emma_music_anim:
    subpixel True
    xanchor 0.0
    xpos -0.075
    easein 10 xpos 0.0
    ease 10 xpos -0.15
    easeout 10 xpos -0.075
    repeat

transform transf_lena_music:
    xanchor 1.0
    xpos 0.93

transform transf_lena_music_anim:
    subpixel True
    xanchor 1.0
    xpos 0.93
    easein 10 xpos 1.06
    ease 10 xpos 0.8
    easeout 10 xpos 0.93
    repeat
############################################################################################################################################################################
## CHARACTER NAME AND COLOR DEFINITIONS ####################################################################################################################################
############################################################################################################################################################################


## Main characters #############################################################
define i = Character("Ian", color="#B77D0A")
define i_p = Character("Ian", kind=nvl, what_slow_cps=0)

define l = Character("Lena", color="#38317F")
define l_p = Character("Lena", kind=nvl, what_slow_cps=0)

## Prominent or named characters ###############################################
# Alphabetical
define a = Character("Alison", color="#0FB286")
define a_p = Character("Alison", kind=nvl, what_slow_cps=0)

define ag = Character ("Agnes", color="#7667C5")

define x = Character("Axel", color="#2988C2")
define x_p = Character("Axel", kind=nvl, what_slow_cps=0)

define b = Character("Billy", color="#19C694")
define b_p = Character("Billy", kind=nvl, what_slow_cps=0)

define cha = Character ("Charles", color="#7E8DA2")

define ch = Character ("Cherry", color="#BC215A")
define ch_p = Character("Cherry", kind=nvl, what_slow_cps=0)

define c = Character("Cindy", color="#B0A000")
define c_p = Character("Cindy", kind=nvl, what_slow_cps=0)

define ld = Character("Dad", color="#6064A7")

define dan = Character ("Danny", color="#7E8DA2")
define dan_p = Character("Danny", kind=nvl, what_slow_cps=0)

define ed = Character ("Ed", color="#DD7D0A")

define e = Character("Emma", color="#B72E45")
define e_p = Character("Emma", kind=nvl, what_slow_cps=0)

define g = Character ("Gillian", color="#CA1456")

define h = Character("Holly", color="#A978A7")
define h_p = Character("Holly", kind=nvl, what_slow_cps=0)

define v = Character("Ivy", color="#8F0082")
define v_p = Character("Ivy", kind=nvl, what_slow_cps=0)

define j = Character("Jeremy", color="#B51515")
define j_p = Character("Jeremy", kind=nvl, what_slow_cps=0)

define js = Character("Jessica", color="#C12B68")
define js_p = Character("Jessica", kind=nvl, what_slow_cps=0)

define lo = Character("Louise", color="#820628")
define lo_p = Character("Louise", kind=nvl, what_slow_cps=0)

define ma = Character("Mark", color="#932F12")
define ma_p = Character("Mark", kind=nvl, what_slow_cps=0)

define mayor = Character ("Mayor Vermeer", color="#8C5C0E")
define mwife = Character ("Mayor's wife", color="#B0833A")

define mk = Character ("Mike", color="#AB2B44")
define mk_p = Character("Mike", kind=nvl, what_slow_cps=0)

define mi = Character("Minerva", color="#503184")
define mi_p = Character("Minerva", kind=nvl, what_slow_cps=0)

define mihu = Character("Minerva's husband", color="#2E2927")

define mo = Character ("Molly", color="#E46D93")

define lm = Character("Mom", color="#6064A7")
define lm_p = Character("Mom", kind=nvl, what_slow_cps=0)

define nat = Character("Nat", color="#129391")

define p = Character("Perry", color="#EA5606")
define p_p = Character("Perry", kind=nvl, what_slow_cps=0)

define r = Character ("Robert", color="#425565")
define r_p = Character("Robert", kind=nvl, what_slow_cps=0)

define s = Character("Seymour", color="#79A2D1")
define s_p = Character("Seymour", kind=nvl, what_slow_cps=0)

define st = Character("Stan", color="#426559")
define st_p = Character("Stan", kind=nvl, what_slow_cps=0)

define vi = Character ("Victor", color="#F16B0E")

define w = Character("Wade", color="#548212")
define w_p = Character("Wade", kind=nvl, what_slow_cps=0)

define wen = Character("Wen", color="#3E679E")

define mr = Character("Mr. Ward", color="#79A2D1")

define yuri = Character("Yuri", color="#B4360A")

define mercedes = Character("Mercedes", color="#B4360A")

# Main characters together
define ic = Character ("{color=#B77D0A}Ian{/color} + {color=#B0A000}Cindy{/color}")
define ip = Character ("{color=#B77D0A}Ian{/color} + {color=#EA5606}Perry{/color}")
define pw = Character ("{color=#EA5606}Perry{/color} + {color=#548212}Wade{/color}")

## Miscellaneous or unnamed characters #########################################

define guy = Character("Guy", color="#7E8DA2")
define guy2 = Character("Guy", color="#1B53A1")
define girl = Character("Girl", color="#7E8DA2")
define woman = Character("Woman", color="#7E8DA2")
define man = Character("Man", color="#7E8DA2")


define bo = Character("Bouncer", color="#2E2927")

define staff = Character("Staff", color="#7E8DA2")

define cur = Character("Curator", color="#7E8DA2")

define bum = Character("Bum", color="#7E8DA2")

define drunk = Character("Drunk", color="#425565")

define wai = Character("Waitress", color="#38317F")

define eli =  Character("Shop assistant", color="#7E8DA2")

define ref = Character("Referee", color="#7E8DA2")

define fg = Character("Fighter", color="#CC0000")


define al = Character("Alice", color="#BF1919")

define milo = Character("Milo", color="#2E2927")

define kent = Character("Kent", color="#7E8DA2")
define kent_p = Character("Kent", kind=nvl, what_slow_cps=0)

define cl = Character("Clark", color="#932F12")

define stefan = Character("Stefan", color="#837E7C")

define martin = Character("Martin", color="#7E8DA2")

define tom = Character("Tom", color="#7E8DA2")

define pt = Character("Peter", color="#25338d")

define john = Character("John", color="#7E8DA2")
define jh_p = Character("John", kind=nvl, what_slow_cps=0)

define finley = Character("Guy", color="#6730C6")

define sen = Character("Sen", color="#1B53A1")

define jk = Character("Guy", color="#CC0000")

define reporter = Character("Reporter", color="#7E8DA2")

define un = Character("???", color="#7E8DA2")
define un_p = Character("???", kind=nvl, what_slow_cps=0)

define ar = Character("Man", color="#7E8DA2")

define gal = Character("Gallery", color="#C5002A")


define penisbreath = Character("Penisbreath", color="#B77D0A")
define pikachu = Character("Pikachu", color="#B0A000")
define lola = Character("Lola", color="#8F0082")
define god = Character("God", color="#D1AC07")
define ek = Character("Eva Kiss", color="#F0147B")
##################################################################################################################################################################################################################
########################################################### CHAPTER 8 PART 2  FALLING INTO PLACE #################################################################################################################################################################################
##################################################################################################################################################################################################################

label v8b:

    $ lena_active = False
    $ ian_active = True
    $ save_name = "Ian: Chapter 8"

    scene blackbg
    with long
    show active_ian
    with long
    pause 1.0

    call calendar(_day="Thursday") from _call_calendar_86

    play music "music/normal_day.mp3" loop
    $ ian_look = 3
    $ fian = "n"
## MAGAZINE
    if ian_job_magazine == 2:
        scene magazine with long
        "Work at the magazine kept going as usual. I despised it."
        show ian with short
        if ian_defy_minerva:
            "At least I didn't need to suffer Minerva's mobbing anymore, and I got to do reviews on better books."
            "Getting the upper hand on her had lifted up my spirits a bit, but that didn't make me enjoy the work more. It was the same old crap."
            "Hopefully, I could change jobs soon. I was still waiting for that response from Hierofant..."
            "If I only got that job offer... But they were taking their time to give a verdict!"
        elif ian_minerva_sex:
            "At least I didn't need to suffer Minerva's mobbing anymore, and I got to do reviews on better books."
            "Not to mention the drastic and steamy turn my relationship with her had taken..."
            if v7_minerva_sex:
                "And it hadn't happened just once... Last week I gave it to her again, in her office."
                "I thought these kinds of things only happened in TV shows and porn movies, but I guess those get their inspiration from reality, after all."
                "And I so happened to be experiencing one of those wild, real-life stories..."
                "In any case, I was looking to change jobs. I was still waiting for that response from Hierofant..."
            else:
                "I still felt weird about it. And she wanted more..."
                "I had turned her down, but that didn't erase what happened, and the tense, uncomfortable atmosphere between us."
                "What the hell was I thinking? And what the hell was going through that woman's deranged mind?"
                "Hopefully, I could change jobs soon. I was still waiting for that response from Hierofant..."
            "If I only got that job offer... But they were taking their time to give a verdict!"
        else:
            "I just tried to switch my mind off and carry on with my duties, doing what was expected of me, expending as little energy as possible."
            "It felt tiresome either way."
            "Hopefully, I could change jobs soon. I was still waiting for that response from Hierofant..."
            "If I only got that job offer... But they were taking their time to give a verdict!"
        i "{i}Ahhh...{/i} I can't wait to get out of here..."
        "I leaned back in my chair and went over the recent events of this past weekend."
## RIDER
    elif v7_effort_job > 0:
        scene v8_rider2 with long
        "I followed the GPS directions while I pedaled on the bike."
        i "Fuck... Couldn't this guy order from a restaurant closer to his home? He's making me go across the entire goddamn city!"
        if ian_job_magazine == 1:
            "After almost being fired by Minerva, I got my paycheck cut in half and four free days a week. I had decided to use that free time to get a part-time job."
            "Finding one had proven to be harder than I thought. So hard, the only offer I found was working as a rider for one of those food delivery services."
        else:
            "After Minerva fired me I had been desperately trying to find a job. It was proving to be even harder than I thought."
            "So hard, the only offer I found was working as a rider for one of those food delivery services."
        "Go to a restaurant, pick up the order and deliver it as fast as possible to the customer's home. Needless to say, I was overqualified for this kind of job."
        "Not that it mattered... I was in desperate need of money and I didn't have any other available option..."
        if ian_stipend == 2:
            "Well, I could always try asking Dad for more money, but I had already done that..."
            "Asking to get a higher stipend was not feasible. And it would be just way too humiliating. I was supposed to be on my way to becoming self-sufficient..."
        else:
            "Well, I could always try asking Dad for more money, but I had already decided against that."
            "It was time I became completely self-sufficient. I wanted to. I needed to."
        if v7_effort_job == 2:
            "I had decided to devote all my efforts to earning some money, so I jumped on this job without too much hesitation and I was taking as many orders as possible, both at noon and most nights."
        else:
            "This was far from what I had in mind, but I decided to jump on this job without too much hesitation and I was now making deliveries in the mornings and afternoons."
        "Perry had an old bike lying around, so I borrowed it and I started my new job as a delivery boy that same week. This would have to do for now..."
        "At least, until I heard from Hierofant. I was still keeping my hopes up about getting that job..."
        scene street with long
        "After making that delivery, I pedaled to another food joint to pick up a new order. At least I was getting a good workout..."
        show ian with short
        "As I waited in front of the restaurant to pick up the food, I went over the recent events of this past weekend."
## NINI
    else:
        scene ianroom with long
        "It was a lazy Thursday morning."
        "After almost being fired by Minerva, I got my paycheck cut in half, but also four free days a week."
        if v7_effort_gym:
            "Free days I could use for anything I wanted... including writing, or going to the gym."
        else:
            "Free days I could use for anything I wanted... including writing."
        show v2_ianwrite with short
        "That day I wasn't making too much progress, though..."
        "Maybe I should've looked for a part-time job... But with my salary, meager as it was, and Dad's stipend, I had enough to cover my monthly expenses."
        "Not much money left for anything else, though..."
        if v5_ian_showup:
            "When would Hierofant send their response? I was still waiting to see if I got that job they were offering..."
        $ ian_look = 2
        scene ianroom
        show ian
        with short
        i "{i}Ahhh...{/i} I'm having a severe case of writer's block today..."
        "I leaned back in my chair as I went over the recent events of this past weekend."

## BOTH ######################################################################
######## holly trip
    if v7_holly_trip:
        # had sex
        if ian_holly_sex:
            if ian_lena_over:
                $ fian = "worried"
                "The weekend at the book fair with Holly..."
                "Yesterday I showed up at the café to talk to Lena and break the news to her. I had to."
                i "What a disaster that was..."
                if ian_lena_love:
                    "It was obvious that me sleeping with Holly would mean losing Lena. What the hell had I been thinking?"
                    "I really liked Holly, but what I felt for Lena... Didn't I tell Jeremy I was falling for her?"
                    $ fian = "sad"
                    "I had messed up and blew my chance in a spectacular fashion. I had been greedy and now I was left without Lena or Holly... and I understood why."
                else:
                    "It was obvious that me sleeping with Holly would mean losing Lena. I should've seen that coming."
                    if ian_holly_dating:
                        $ fian = "smile"
                        "But... Holly and me..."
                        "Well, I was happy about what happened. I really liked Holly... and I was looking forward to seeing where things would go with her."
                        $ fian = "sad"
                        "But now Holly was avoiding Lena. She felt she had done something wrong... That was my fault, and I should do something about it."
                    else:
                        if ian_chad < 5:
                            $ ian_chad += 1
                        $ fian = "sad"
                        "And now Holly was avoiding Lena. She felt she had done something wrong... That was my fault, and I should do something about it."
            elif ian_holly_dating:
                $ fian = "smile"
                "The weekend I spent with Holly..."
                i "That was great. The book fair, the people, what happened between Holly and me..."
                i "It was perfect, even if a bit unexpected."
                "I really liked Holly, and the feeling was mutual. I was looking forward to seeing where things would go with her."
            else:
                if ian_chad < 5:
                    $ ian_chad += 1
                "The weekend at the book fair with Holly... We really slept together..."
                "I felt a bit bad about what happened later, but I had to be honest with Holly."
                "I liked Holly, but I had the feeling I would only disappoint her."
                $ fian = "smile"
                "Our night together was something to remember, though."
        # just kiss
        elif v7_holly_kiss:
            "The weekend at the book fair with Holly..."
            "She made a move on me and we kissed. I should've seen it coming."
            "Maybe I led her on a bit too much... But that was not the kind of relationship I wanted to have with her."
            if ian_lena_dating:
                $ fian = "worried"
                "Especially considering my relationship with Lena."
                "Yesterday I showed up at the café to talk to her and told her what happened. I wanted her to hear it from me."
                i "What a disaster that was..."
                "I had tried to tell her it had just been a kiss. An innocent one... But had it really been?"
                if ian_lena_love:
                    "I had messed up big time. And I had lost Lena because of it..."
                else:
                    "I had made a mess of things, and Lena wasn't happy about it."
                "Not only that, but now Holly felt guilty and was avoiding her. That too was my fault, and I should do something about it."
            "Needless to say, Holly felt pretty awkward around me now. Nobody likes being rejected..."
        # nothing happened
        else:
            "The weekend at the book fair with Holly..."
            $ fian = "smile"
            "It had been a great experience. I felt invigorated."
            $ fian = "n"
            "Though the atmosphere had turned rather awkward with Holly. When I realized we would be sharing a room..."
            "I had the feeling she was expecting something to happen, but I kept my distance."
            if ian_lena_dating:
                "I knew getting involved with her would only cause problems with Lena, and I didn't want that."
                if ian_lena_love:
                    "I was developing some real feelings for Lena, and I wasn't about to put that in jeopardy. She was important to me."
                else:
                    "I liked how my relationship with her was developing, and hooking up with Holly wasn't worth losing that."
            else:
                "I just didn't feel like that about Holly, even though I liked and admired her."
################ cindy party
    elif v7_cindy_kiss:
        $ fian = "worried"
        "What happened with Cindy was... just crazy."
        if ian_cindy_sex:
            $ fian = "disgusted"
            "I had fucked her. In a dark alleyway. During Wade's birthday."
            "And we had been caught by Jeremy and Ivy."
            i "Fuck..."
            "I felt a wrench in my gut just thinking about it. What the hell had I been thinking?"
            "I had stabbed Wade in the back in the worst possible way... But I wasn't the only one at fault."
            $ fian = "blush"
            "Cindy also wanted it. She wanted it as much as I did... Maybe even more."
            "The passion I felt coming from her..."
            $ fian = "worried"
            i "Fuck, thinking about it is getting me horny. There was no way I could resist."
            i "She's just too much..."
        else:
            $ fian = "blush"
            "I really did kiss her... And she kissed me back..."
            "I just couldn't resist her lips. And if I hadn't listened to the voice of reason and got a hold of myself again..."
            "Who knows how that situation could've ended up."
            $ fian = "disgusted"
            "Still, I felt a wrench in my gut just thinking about it. What the hell had I been thinking?"
            "I had been secretly dreaming about it. And when it happened... I had really stabbed Wade in the back."
            $ fian = "blush"
            "But Cindy... She also wanted it... I felt the burning passion in her kisses..."
            "Did she really feel like that about me? Or had she been just drunk and frustrated?"
        $ fian = "sad"
        "In any case, things had grown cold since then. Cold as ice."
        "I tried writing to Cindy, but she had been ignoring me."
        "I needed to talk to her about what happened, but it was clear she didn't want to."
        "I had no idea of what to do now..."
        i "I got myself neck-deep into trouble... What kind of friend am I?"
    else:
        if wade_cindy == 2:
            "Wade's birthday party had been rather disappointing, but at least it seemed he and Cindy were hanging in there."
            "Their relationship seemed to be going through a rough patch, but I had faith in them."
            "Breakups are the worst, and I didn't want my friends to go through one."
        else:
            "Wade's birthday party turned out really messy..."
            if v7_follow_cindy:
                $ fian = "blush"
                "Cindy and I had had a very tense moment in that back alley. For a moment I had the feeling we were about to kiss..."
                "Thankfully, I had snapped out of it. If that had really happened, well... I couldn't fathom the consequences."
                $ fian = "n"
                "I had been dreaming about it, and when the chance presented itself... I made the right choice. Some dreams are better kept in the realm of fantasy."
                "But Wade's relationship with Cindy seemed to be nearing the breaking point. They had been having quite a few fights lately, and this last one had been particularly hard..."
            else:
                "As usual, Perry had gotten into trouble, but that was far from the worst of it."
                $ fian = "sad"
                "Wade's relationship with Cindy seemed to be nearing the breaking point. They had been having quite a few fights lately, and this last one had been particularly hard..."
            "I wondered what would happen with them, but things weren't looking good..."

# lena
    if ian_lena_dating and ian_lena_over == False and v7_holly_kiss == False:
        if v7_cindy_kiss:
            $ fian = "worried"
            "And what did this mean for me and Lena?"
            if ian_lena_love:
                "I thought I was catching feelings for Lena, yet then that happened with Cindy..."
                "What did I really feel for her? For them both?"
                "I knew I really enjoyed being with Lena, more than I had enjoyed being with anyone else in such a long time."
                $ fian = "n"
                "I didn't want to lose her..."
            else:
                "I supposed nothing needed to change between us... So far we were friends with benefits. No strings attached."
                $ fian = "smile"
                "But I really enjoyed being with her, more than I had enjoyed being with anyone else in such a long time."
                "I was looking forward to keep exploring our relationship..."
        else:
            $ fian = "smile"
            "Thinking about Lena really brightened up my mood. I still couldn't believe how lucky I was."
            "She was clever, charming, and incredibly beautiful. How the hell had I managed to catch the interest of such a girl?"
            if ian_lena_love:
                "She was indeed special... And I was starting to feel things for her that I hadn't felt for someone in such a long time..."
            else:
                "I liked our easygoing relationship. We were taking things at our own pace, no strings attached. Just what I needed."
            "I really enjoyed being with her, more than I had enjoyed being with anyone else in such a long time."
            "I was looking forward to keep exploring our relationship..."
        $ fian = "smile"
        "This Friday was an important day for her. Her first public performance."
        "I knew she was fretting over it. I should call her later to give some words of encouragement."
    else:
        "Lena's concert was coming up. I knew she was fretting over it."
        if ian_lena_over or v7_holly_kiss:
            $ fian = "sad"
            "It would be awkward as hell, and maybe she didn't want to hear from me, but still..."
            i "I should call her later to give some words of encouragement."
        elif lena_ian_mad:
            $ fian = "n"
            "The mood had been a bit weird since my fight with Robert. I caused trouble for Lena and made her mad..."
            i "I should call her later to give some words of encouragement. Maybe she's forgiven me for what happened..."
        else:
            $ fian = "smile"
            i "I should call her later to give some words of encouragement. She'll appreciate it."
            "I was glad we had talked that day at the café. She was such a cool girl."
            "Meeting her was one of the best things that had happened lately in my life. She was someone really worth having as a friend."
            "Though it was hard seeing such an attractive girl as a simple friend..."

    if ian_job_magazine == 2:
        $ fian = "n"
        "I looked at the clock."
        i "Is it lunch break yet? Man, this is boring."
    elif v7_effort_job > 0:
        $ fian = "n"
        i "..."
        i "Damn, they're taking so long with this order. Hurry it up, I'm losing money!"
    else:
        $ fian = "n"
        i "Stop daydreaming, Ian... I need to focus."

## ALISON SEXTING #############################################################
    if ian_alison_dating:
        nvl clear
        play sound "sfx/sms.mp3"
        "My vibrating phone pulled me out of my stupor."
        "Alison had sent me a message..."
        show ian at left with move
        show v8_selfie_alison1 with short
        $ ian_alison_pics.append("v8_selfie_alison1.webp")
        $ fian = "shy"
        a_p "{i}Thinking about you {image=emoji_heart.webp}{/i}"
        i "Damn! This is a surprise..."
        $ fian = "confident"
        i "A very welcome one. Looks like Alison is in a playful mood..."
        "Let's write back to her."
        menu:
            "Nice":
                $ renpy.block_rollback()
                i_p "{i}Nice {image=emoji_glasses.webp}{/i}"
                a_p "{i}That's all you've got to say?{/i}"
                i_p "{i}That's all you've got to show?{/i}"
                call friend_xp('alison', -1) from _call_friend_xp_739
                a_p "{i}Idiot... I'm not sending you any more selfies!{/i}"
                $ fian = "smile"
                i_p "{i}I was joking! I really like what I see, you know that already {image=emoji_heart.webp}{/i}"
                a_p "{i}Girls like it when guys show some appreciation, you know! Especially when I'm taking the risk of taking this picture at the office...{/i}"
                i_p "{i}You naughty girl {image=emoji_devil.webp}{/i}"

            "I love your boobs":
                $ renpy.block_rollback()
                $ v8_alison_sexting = 1
                i_p "{i}Damn, I really love your boobs {image=emoji_fire.webp}{/i}"
                a_p "{i}I know, you've given me ample proof of that {image=emoji_devil.webp}{/i}"
                i_p "{i}So where's the other one? I can only see one...{/i}"
                if ian_lust < 7:
                    call xp_up('lust') from _call_xp_up_558
                a_p "{i}Don't be greedy... I took the risk of taking this picture at the office just for you!{/i}"
                i_p "{i}You naughty girl {image=emoji_devil.webp}{/i}"

            "I hope your boss doesn't catch you":
                $ renpy.block_rollback()
                $ v8_alison_sexting = 1
                i_p "{i}You naughty girl, taking pictures at the office...{/i}"
                i_p "{i}I hope your boss doesn't catch you {image=emoji_devil.webp}{/i}"
                a_p "{i}That would be problematic indeed...{/i}"
                i_p "{i}Even if you got fired you wouldn't have any trouble finding a new job. You're smart and super hot, who wouldn't hire you?{/i}"
                if ian_charisma < 8:
                    call xp_up('charisma') from _call_xp_up_559
                a_p "{i}You sweet-talker... You really know what a girl likes to hear {image=emoji_flirt.webp} {/i}"
                i_p "{i}I'm just saying it as I see it...{/i}"

        hide v8_selfie_alison1 with short
        show ian at truecenter with move
        menu:
            "{image=icon_lust.webp}Ask for another picture" if ian_lust > 4:
                $ renpy.block_rollback()
                $ fian = "confident"
                i_p "{i}So, can I get another picture? You woke up my appetite {image=emoji_crazy.webp} {/i}"
                a_p "{i}Don't you have enough with that one? I thought you'd be happy with it! {/i}"
                menu:
                    "{image=icon_charisma.webp}Do it for me" if ian_charisma > 4 and v8_alison_sexting > 0:
                        $ renpy.block_rollback()
                        $ v8_alison_sexting = 2
                        i_p "{i}When it's you, I never have enough with just one. You should know that already.{/i}"
                        a_p "{i}You're terrible. You're gonna really make me do this...{/i}"
                        i_p "{i}Do it for me. I want you to be naughty, Alison. Very naughty.{/i}"
                        if v5_alison_public:
                            i_p "{i}Like when I fingered you in the middle of the nightclub.{/i}"
                            a_p "{i} Gosh, that was so hot...{/i}"
                        elif v7_alison_bj == 2:
                            i_p "{i}Like the other day, when you let me fuck your mouth...{/i}"
                            a_p "{i} Gosh, you were so aggressive...{/i}"
                        else:
                            i_p "{i}Like last time at my place, when you asked me to fuck you...{/i}"
                            a_p "{i} Gosh, you got me so horny...{/i}"
                        a_p "{i}But I really can't right now. You'll have to wait.{/i}"
                        a_p "{i}I can't wait to spend some time with you again, by the way. And to be done with this debacle we have at the office.{/i}"
                        i_p "{i}I'll try to bear it until then...{/i}"
                        a_p "{i}I need to get back to work... But I'll be thinking about you {image=emoji_love.webp}{/i}"
                        $ fian = "smile"
                        i "Damn, now she's gotten me all horny... Alison can be a pretty naughty girl."

                    "Don't insist":
                        $ renpy.block_rollback()
                        $ fian = "n"
                        i "It seems she doesn't feel comfortable taking another selfie..."
                        i_p "{i}Of course, I'm happy with that one. Thanks for thinking about me.{/i}"
                        a_p "{i}I hope we can spend some time together soon, when I'm done with this debacle we have at the office.{/i}"
                        i_p "{i}Meanwhile, I'll delight myself with this selfie you sent me.{/i}"
                        a_p "{i}That's not the only picture of me you have...{image=emoji_flirt.webp}{/i}"
                        i_p "{i}No, and I'm so glad that's the case {image=emoji_tongue.webp}{/i}"
                        i_p "{i}I should get back to work. Good luck with your debacle! {/i}"

            "I was thinking about you too":
                $ renpy.block_rollback()
                $ fian = "smile"
                i_p "{i}I was thinking about you too, by the way.{/i}"
                a_p "{i}Oh, you were?{/i}"
                i_p "{i}You don't believe me?{/i}"
                a_p "{i}Do you have any way to prove it?{/i}"
                i_p "{i}Yes. But you'll have to wait until we see each other face to face.{/i}"
                if ian_alison < 12:
                    call friend_xp('alison', 1) from _call_friend_xp_740
                a_p "{i}I like how that sounds... I can't wait for that. And to be done with this debacle we have at the office.{/i}"
                i_p "{i}Meanwhile, I'll delight myself with this selfie you sent me.{/i}"
                a_p "{i}That's not the only picture of me you have...{image=emoji_flirt.webp}{/i}"
                i_p "{i}No, and I'm so glad that's the case. What would I do with just one? {image=emoji_tongue.webp}{/i}"
                a_p "{i}Don't be greedy! I need to go back to work.{/i}"
                a_p "{i}I'm glad you liked my picture by the way {image=emoji_glasses.webp}{/i}"
                i_p "{i}Feel free to send as many as you want {image=emoji_crazy.webp} {/i}"

            "Thanks for the picture":
                $ renpy.block_rollback()
                $ fian = "smile"
                i_p "{i}Thanks for the picture! I should get back to work.{/i}"
                a_p "{i}Oh, okay.{/i}"
                a_p "{i}I hope we can spend some time together soon, when I'm done with this debacle we have at the office.{/i}"
                i_p "{i}Sure, me too. Good luck with that!{/i}"

        if ian_job_magazine == 2:
            $ fian = "n"
            "I put away my phone and continued to wait for lunch break. It was almost time."
        elif v7_effort_job > 0:
            $ fian = "n"
            i "Oh, finally. The food's ready."
            scene v8_rider2 with long
            "I picked up the order and continued making deliveries till the afternoon."
            "I had enough time to get a quick bite myself before going to the gym."
        else:
            $ fian = "n"
            "I put my phone away and tried to get back to writing, but it was hopeless."
            i "It's almost lunchtime..."
# no alison
    else:
        if ian_job_magazine == 2:
            $ fian = "n"
            "Thankfully, it was almost time."
        elif v7_effort_job > 0:
            $ fian = "n"
            i "Oh, finally. The food's ready."
            scene v8_rider2 with long
            "I picked up the order and continued making deliveries till the afternoon."
            "I had enough time to get a quick bite myself before going to the gym."
        else:
            $ fian = "n"
            "I tried to get back to writing, but it was hopeless."
            i "It's almost lunchtime... And then I have to go to the gym."

# Holly talk
    if v7_holly_kiss and ian_lena_dating:
        if ian_job_magazine == 2:
            "But before leaving I had something important to do."
            $ fian = "n"
            $ fholly = "n"
            show ian at lef with move
            show holly2 at rig with short
            i "Hey, Holly... Can I talk to you?"
            $ fholly = "blush"
            hide holly2
            show holly3 at rig
            with short
        else:
            "But before that, I had something important to do."
            if v7_effort_job > 0:
                scene street with long
            $ fian = "n"
            hide ian
            show ian_phone
            with short
            play sound "sfx/ring.mp3"
            i "..."
            show phone_holly_blush at lef3 with short
            h "Hi, Ian..."
            i "Hey, Holly... Can I talk to you?"
        h "Yeah, of course..."
        i "It's about Lena. I spoke to her yesterday..."
        h "Oh... And... What did she say?"
        if ian_holly_dating:
            i "Well... She's kinda disappointed with me. But she's glad you and I, uh, confessed our mutual feelings to each other."
            h "She must be so mad at me..."
            i "Not at all. You don't have to worry about it. She's only mad at me, and rightly so."
            h "But that's not fair. I knew you and her... I mean..."
        elif ian_holly_sex:
            i "Well... She's pretty pissed at me. She doesn't like how I handled the situation, and I can see why..."
            h "She must be pissed at me too..."
            i "She's not. Quite the opposite, in fact. She's just mad at me, for hurting you."
            h "I'm okay, I already told you...! I don't blame you for what happened..."
            i "I know, I know. Still, Lena's right to blame me."
            h "But that's not fair. I knew you and her... I mean..."
        else:
            i "Well, she was quite... disappointed with the situation."
            h "I knew it. She probably hates me."
            i "No, not at all. She's pissed at me, and rightly so, but not at you."
            h "Why? That's not fair. It was me who kissed you. I knew you and her... I mean..."
        i "It doesn't matter. And she's not mad at you, I promise."
        i "In fact, she's worried... She would really like to talk to you."
        if ian_job_magazine == 2:
            hide holly3
            show holly2 at rig
            with short
            $ fholly = "sad"
        else:
            hide phone_holly_blush
            show phone_holly_sad at lef3
        h "Really...?"
        i "Yeah. I think she genuinely values your friendship."
        h "That makes me feel even worse."
        i "Holly, I'll tell you again. You did nothing wrong. This one's on me."
        i "Let me take it on the chin and go talk to Lena. She misses you and I'd hate to be the cause of you two drifting apart."
        h "You think I should... go to her concert this Friday?"
        $ fian = "smile"
        i "You certainly should. She'd feel let down if you don't show up."
        if ian_job_magazine == 2:
            $ fholly = "n"
        else:
            hide phone_holly_sad
            show phone_holly at lef3
        h "Alright... I will."
        i "Good. And I'm sorry I got you tangled up in this mess..."
        $ fian = "n"
        i "I should've been more clear about what was going on between Lena and me."
        if ian_holly_dating:
            $ fian = "smile"
            i "But I don't regret it, since it allowed us to be together..."
            if ian_job_magazine == 2:
                $ fholly = "shy"
            else:
                hide phone_holly
                show phone_holly_shy at lef3
            h "Yes... I..."
            h "I don't regret it either."
            i "Well, I gotta go now. See you tomorrow, alright?"
            h "Yeah. Until tomorrow!"
        else:
            h "I should've asked before making a move, but I was afraid of the answer. I'm at fault, too..."
            i "Stop it, Holly. I gotta go now, so see you tomorrow, alright?"
            h "Yes. Bye, Ian."
        if ian_job_magazine == 2:
            hide holly2 with short
        else:
            hide ian_phone
            show ian
            hide phone_holly
            hide phone_holly_shy
            with short
        $ fian = "n"
        i "Alright, I'm glad that's dealt with. Now..."

    stop music fadeout 2.0
    if ian_job_magazine == 2:
        scene street with long
        "I left the office and went straight to the gym."
    elif v7_effort_job > 0 and v7_holly_kiss and ian_lena_dating:
        scene street with long
        "I grabbed my bag and headed to the gym."
    elif v7_effort_job == 0:
        scene street with long
        "I packed my things and left for the gym."

## GYM ########################################################################################################################################################################################################################################################################

    play music "music/jeremys_theme.mp3" loop
    scene gym with long
    $ fian = "worried"
    $ fjeremy = "smile"
    $ jeremy_look = 2
    # jiujitsu
    if jiujitsu > 1:
        $ ian_look = "gi"
        "Training with Wen was as hard as usual."
        show v8_jiujitsu with short
        "I had been rolling with him for some weeks now, learning the basic positions and transitions, and some submissions."
        "I had the feeling I was picking things up pretty fast, and now I was starting to understand how to use the mass of my body to manipulate my opponent's."
        "A Jiu-jitsu bout felt a lot like a chess game, but instead of playing with pieces on a board, you used your body on a mat."
        "You had to be aware of your position, improve it step by step, read and anticipate your opponent's movements..."
        "It required a lot of intelligence and technique."
        play sound "sfx/slap.mp3"
        scene gym
        show v3_jiu5b
        with vpunch
        if ian_athletics < 10:
            call xp_up('athletics') from _call_xp_up_560
        "Of course, Wen kicked my ass every single time."
        scene gym
        show ian at lef
        show wensmile at rig
        with long
        i "I give up. It's impossible to beat you."
        wen "You've been practicing Jiu-jitsu for less than three months! Of course you're not gonna beat me."
        if v7_effort_gym:
            if jiujitsu < 3:
                $ jiujitsu += 1
            if ian_athletics < 10:
                call xp_up('athletics') from _call_xp_up_561
            hide wensmile
            show wen at rig
            wen "But you've been making very good progress... I'm quite impressed, in fact."
            i "It doesn't feel like it..."
            wen "Maybe I'm being a bit too hard on you. But you're really applying yourself."
            $ fian = "n"
            if tournament:
                i "Well, there's that tournament coming. I don't want to get the shit beat out of me."
                hide wen
                show wensmile at rig
                wen "That's indeed the best possible motivation to improve, screw earning colored belts, ha ha!"
                wen "Well, keep working hard at it and I think you'll be fine. To get good you need to put in the hours, there's no other way around it."
            else:
                i "Go hard or go home, right?"
                wen "With that attitude you should've entered the tournament. You're still in time..."
                i "Nah, I'm good. I do this as a hobby, I don't need to get the shit beat out of me in some weird tournament."
                hide wen
                show wensmile at rig
                wen "That's actually the best possible motivation to improve, screw earning colored belts, ha ha!"
                wen "In any case, keep up the good work. To get good you need to put in the hours, there's no other way around it."
        else:
            wen "You're making good progress for a beginner, but you have a great teacher..."
            hide wensmile
            show wen at rig
            if tournament:
                wen "You could use some more training, though. Especially considering you decided to join the tournament."
                $ fian = "n"
                i "Yeah, well... We'll see about that. The idea sounded good, but I don't want to get the shit beat out of me."
                hide wen
                show wensmile at rig
                wen "That's indeed the best possible motivation to improve, screw earning colored belts, ha ha!"
                wen "But to get good you need to put in the hours, there's no other way around it."
            else:
                wen "You could use some more training, though. To get good you need to put in the hours, there's no other way around it."
                $ fian = "n"
                i "I'm doing this just as a hobby... I have other stuff I need to devote time to in my life."
                hide wen
                show wensmile at rig
                wen "Everyone has their priorities... But I'm gonna grill you anyway."
                i "That's reassuring..."
    # kickboxing
    else:
        $ ian_look = 7
        "Training with Yuri was intense, as usual."
        scene v5_pads1 with short
        "We had been drilling the basics during these past few weeks, working a ton on cardio and technique."
        "Yuri showed me effective combinations, always stressing proper defense and head movement."
        "His pad work was really good, fluid, and precise. I felt my punches would be really effective in a fight..."
        play sound "sfx/punchgym.mp3"
        scene v5_pads1 with vpunch
        if ian_athletics < 10:
            call xp_up('athletics') from _call_xp_up_562
        "I was also getting better with my kicks. Turned out I had good hip dexterity and could throw a mean roundhouse kick."
        yuri "Good! I like how you turn your hip on that one!"
        scene gym
        show ian at lef
        show yuri at rig
        with long
        if v7_effort_gym:
            if kickboxing < 3:
                $ kickboxing += 1
            if ian_athletics < 10:
                call xp_up('athletics') from _call_xp_up_563
            yuri "That was a good session. You're getting sharp!"
            i "I still feel pretty gassed out..."
            if tournament:
                yuri "As you should. I'm pushing you hard... You have the tournament coming up, after all."
                $ fian = "n"
                i "Yeah... I'd like to be prepared. I don't want to get the shit beat out of me."
                yuri "You're putting in the time and the work. I think you should be fine."
            else:
                yuri "As you should. I'm pushing you hard... Are you sure you don't want to enter that tournament?"
                $ fian = "n"
                i "Nah, I'm good. I do this as a hobby, I don't need to get the shit beat out of me in some weird tournament."
                yuri "Afraid of losing brain cells, huh? I guess that's important for a writer."
                yuri "But you've been training pretty hard anyway. That's a good attitude."
        else:
            yuri "That was a good session, but you still need to work on that cardio. You're gassed out!"
            i "I'm a writer, not an athlete... I spend most of my time sitting down in front of a screen."
            if tournament:
                yuri "You should seriously consider devoting more hours to this if you're serious about entering that tournament."
                $ fian = "n"
                i "Yeah, well... We'll see about that. The idea sounded good, but I don't want to get the shit beat out of me."
            yuri "Everyone has their priorities, but to get good you need to put in the hours, there's no other way around it."
            $ fian = "n"
            i "In the end, I'm doing this just as a hobby, so..."
    #jeremy
    if jiujitsu > 1:
        show wensmile at rig3
        show ian at lef3
        with move
        show jeremy with short
        j "So, how's it going around here? I see you rolling around like kids in the playground!"
        wen "Wanna play with us?"
        $ fjeremy = "n"
        j "Nah, I'm fine."
        show wensmile at right
        show jeremy at left
        show ian at lef
        with move
        show yuri at rig with short
        yuri "If you're serious about entering the tournament you'll need to learn some basic wrestling skills, else you'll be done as soon as you have to face a grappler."
        j "My takedown defense is as solid as a rock. I think that's enough."
        yuri "We'll have Wen put that to the test."
        $ fian = "smile"
        wen "It'll be my pleasure."
        j "As long as I don't have to wear one of those white pajamas..."
        if tournament:
            yuri "The same goes for you, Ian. You should also practice your striking aside from jiu-jitsu."
            if v7_effort_gym:
                i "That sounds good. I've already decided to put in some extra hours at the gym."
                yuri "Good. I've seen you already know how to throw a punch, but getting better can't hurt, can it?"
            else:
                $ fian = "n"
                i "I'm afraid I don't have too much time for that. But I already know how to throw a punch."
                yuri "Sure, but getting better can't hurt, can it?"
    else:
        $ fjeremy = "n"
        show yuri at rig3
        show ian at lef3
        with move
        show jeremy with short
        j "Working the pads is fun and all, but when will we get back to sparring? The tournament is almost upon us!"
        yuri "You're of a single mind, huh? Don't worry, you'll have time to put everything into practice."
        hide yuri
        show yurismile at rig3
        yuri "And if you're serious about entering the tournament, you'll need to practice some basic wrestling skills, too, else you'll be done as soon as you have to face a grappler."
        $ fjeremy = "n"
        if tournament:
            yuri "Same goes for you, Ian."
            if v7_effort_gym:
                i "That sounds good. I've already decided to put in some extra hours at the gym. Learning something new can't hurt, can it?"
            else:
                $ fian = "n"
                i "I'm afraid I don't have too much time for that. I'll have to bet everything on my striking."
                yuri "A risky bet."
        j "My takedown defense is as solid as a rock. I think that's enough."
        show yurismile at right
        show jeremy at rig
        show ian at left
        with move
        show wensmile at lef with short
        wen "I'll be putting it to the test."
        $ fian = "smile"
        j "As long as I don't have to wear one of those white pajamas..."
        yuri "That will be the last of your concerns once Wen puts his hands on you, ha ha!"
        j "You two are a bunch of sadists."
    $ fjeremy = "smile"
    if v7_fight == "win_punch" or v7_fight == "win_throw":
        j "Ian, you haven't told them about your fight yet, have you?"
        if v1_fight or v1_fight_kick or v1_fight_grappling:
            wen "Another street fight? You like getting into trouble, don't you?"
        else:
            wen "Are you that eager to put into practice what you're learning here?"
        $ fian = "n"
        i "It's not like that. I'm a pretty chill guy, but this dude... He's the one who stirs shit up."
        yuri "I don't condone students getting into fights, but... If you're gonna fight, I hope you won!"
        $ fian = "smile"
        if v7_fight == "win_punch":
            i "Yeah. I slipped his punch, threw a right, and knocked the fighting spirit out of him."
        if v7_fight == "win_throw":
            i "Yeah. I took him down and knocked the will to fight out of him."
        if tournament:
            yuri "I know sometimes you just want to smack some mofos, but try and save it for the tournament!"
        else:
            yuri "I know sometimes you just want to smack some mofos, but try to stay out of trouble!"
        wen "But keep training in case you need to smack some mofos again, ha ha!"
        j "I'm getting jealous. I want to put my skills to the test in a real fight, too!"
        yuri "Don't you go around looking for problems, now! Save it for the tournament!"
    if v7_fight == "lose":
        j "Ian, you haven't told them about your fight yet, have you?"
        $ fian = "sad"
        i "I'd rather not."
        wen "You got your ass kicked, didn't you?"
        i "Something like that."
        yuri "I guess that means you need some more training."
        i "I guess... Or maybe I should stay out of fights altogether."
        if tournament:
            wen "The tournament will prove useful to you, you'll see."
        else:
            yuri "That's always the wisest choice... But we can't always be wise, ha ha."
        j "I'm getting jealous. I want to put my skills to the test in a real fight, too!"
        yuri "Don't you go around looking for problems, now! Save it for the tournament!"
    if v7_fight == "drawup":
        j "Ian, you haven't told them about your fight yet, have you?"
        if v7_fight == "drawdown":
            j "Ian, you haven't told them about your fight yet, have you?"
            $ fian = "sad"
            i "I'd rather not."
            wen "You got your ass kicked, didn't you?"
            i "No, I wouldn't say that... But I didn't win, either. It was pretty pathetic."
        if v7_fight == "drawup" or v7_fight == "drawdown":
            $ fian = "n"
            i "There's not much to tell..."
            yuri "I don't condone students getting into fights, but... If you're gonna fight, I hope you won!"
            i "Nobody won. It was just a petty scuffle, but if someone came out on top, I guess that was me. Hard to tell."
        yuri "I guess that means you need some more training."
        i "I surely do."
        yuri "And maybe try to stay out of street fights."
        wen "At least until you're properly ready, ha ha."
        j "I'm getting jealous. I want to put my skills to the test in a real fight, too!"
        yuri "Don't you go around looking for problems, now! Save it for the tournament!"
    j "Come on Ian, let's hit the showers."
##GYM END ##################################################################################################################################################################################################################################################
    stop music fadeout 2.0
    scene streetnight with long
    $ fian = "n"
    $ fjeremy = "smile"
    $ ian_look = 3
    $ jeremy_look = 1
    show ian at lef
    show jeremy at rig
    with short
    "We got a couple of protein shakes from the vending machine and sat in front of the gym, like every other day."
    if jiujitsu > 1:
        i "Fuck, I'm beat..."
        i "Grappling and ground fighting is the most exhausting physical activity I've ever done."
    else:
        i "I'm beat. Yuri really knows how to work us out."
    # cindy
    if ian_cindy_sex:
        $ fjeremy = "sad"
        j "So... Any news on the \"Cindy front\"?"
        $ fian = "worried"
        i "No... She still hasn't replied to my text, and I don't think I should push her..."
        j "So you don't know if she told Wade yet."
        i "Nope."
        j "Do you think she will?"
        i "It doesn't seem too likely, but who knows..."
        j "You should tell Wade before she does. Be a bro."
        $ fjeremy = "n"
        i "I doubt I can be a \"bro\" to Wade anymore."
        j "Yeah."
        $ fjeremy = "serious"
        j "What were you thinking, dude?"
        i "I wasn't thinking. I mean..."
        i "Lately, there was this weird sexual tension growing between Cindy and me. I wasn't sure at first, but..."
        j "Well, no doubt about that now. Normally I'd congratulate you on scoring such a hottie, but... Dude, she's Wade's girlfriend."
        $ fian = "serious"
        i "I know, I know!"
        i "I fucked up big time. But I got into that situation and I wasn't able to handle it."
        $ fian = "n"
        i "You can't simply say no to a girl like Cindy."
        if alison_jeremy_block:
            $ fjeremy = "n"
            j "I just find it funny that a guy who told me not to step into another dude's relationship goes and does this..."
            call friend_xp('jeremy', -1) from _call_friend_xp_741
            i "Huh?"
            j "That's what you told me when I said I wanted to fuck Alison too. \"Basic etiquette\", you said."
            j "\"You don't try to sleep with your friend's hookups\"."
            $ fian = "sad"
            i "That's..."
            j "That's a case of big, fat hypocrisy."
            $ fian = "serious"
            i "Alright, alright! You're right. I'm a piece of shit."
        elif alison_jeremy_doubt:
            $ fjeremy = "n"
            j "To think you were {i}uncomfortable{/i} with me trying to fuck Alison too..."
            i "I know I fucked up, okay? No need to point out the irony of the situation."
            j "Look, I'm of the same mind: if a girl wants it, it's not a crime to give it to her. Especially a girl like Cindy."
            j "But as I said... She's not just a \"girl\". She's your bro's girl."
            $ fian = "sad"
            i "I said I'm well aware of that..."
        else:
            j "I'm of the same mind: if a girl wants it, it's not a crime to give it to her. Especially a girl like Cindy."
            j "But as I said... She's not just a \"girl\". She's your bro's girl."
            $ fian = "sad"
            i "I said I'm well aware of that... It was fucked up."
        $ fjeremy = "n"
        j "I'm not trying to judge here, God knows I'm far from perfect. But now you have something serious to deal with."
        $ fian = "sad"
        j "You either try to talk to Cindy as soon as possible or you come clean in front of Wade. But you can't afford to remain idle in this matter."
        i "You're probably right."
        i "And... Sorry I made you carry this burden. You and Ivy..."
        if ian_lena_dating:
            j "Don't worry, we won't say a thing. To Wade or... Lena."
        else:
            j "Don't worry, we won't say a thing."
        if ian_jeremy > 7:
            j "I've got your back, bro... Even if you're on the wrong side of the law."
            $ fian = "smile"
            i "Thanks, man. I've got your back, too."
        elif ian_jeremy > 3:
            j "I wish I hadn't seen anything, and I'm sure you do too... But we're bros, so I got your back."
            $ fian = "n"
            i "Thanks... I really appreciate it."
            j "Don't mention it."
        else:
            j "I wish I hadn't seen anything, and I'm sure you do too... But I'm not about to betray you, so..."
            j "I'll be a good friend."
            $ fian = "n"
            i "Thanks... I really appreciate it."
        $ fjeremy = "smile"
        j "Let's not talk more about that subject. The less I know, the better."
        j "In fact, I don't even know what we were talking about!"
        $ fian = "smile"
        i "Good."
    # louise
    if v7_bbc == "ivy":
        if louise_jeremy:
            $ fian = "n"
            i "So, how does being single again feel? You finally broke up with Louise..."
            $ fjeremy = "n"
            j "It was bound to happen sooner or later. It was messier than it had to be, but that's Ivy's fault..."
            $ fjeremy = "smile"
            j "I'll miss having sex with Louise, she really did it for me. But it was getting a bit boring."
            j "I need to be free to explore the world."
            i "I don't know why you agreed to date her in the first place..."
            $ fjeremy = "n"
            j "We were dating, yeah, but you know I never explicitly said I was her \"boyfriend\"..."
            i "Yeah, yeah."
            $ fian = "smile"
            i "Well, I'm glad to see you're not affected by what happened."
            $ fjeremy = "happy"
            j "You know me, I'll be fine!"
        else:
            $ fian = "n"
            i "So, have you heard from Louise? Is she still mad at you for what happened?"
            $ fjeremy = "n"
            j "I guess, I don't know. I haven't spoken to her since she broke up with me that night at the club."
            $ fjeremy = "smile"
            j "But it's alright. I need to be free to explore the world!"
            i "I don't know why you agreed to date her in the first place..."
            $ fjeremy = "n"
            j "We were dating, yeah, but you know I never explicitly said I was her \"boyfriend\"..."
            i "Yeah, yeah."
            $ fian = "smile"
            i "Well, I'm glad to see you're happy with the bachelor life."
            $ fjeremy = "happy"
            j "Couldn't be happier!"
    # emma new
    if emma_jeremy:
        hide friend_up
        $ fjeremy = "flirt"
        j "Oh, by the way..."
        j "I need to thank you for something!"
        $ fian = "n"
        i "For what?"
        "Jeremy pulled out his phone and showed me a picture."
        show ian at left
        show jeremy at right
        with move
        show v8_emma_jeremy with short
        $ fjeremy = "happy"
        j "For this!"
        menu:
            "You scored with Emma?":
                $ renpy.block_rollback()
                # emma_jeremy = 2
                $ ian_jeremy_pics.append("v8_emma_jeremy.webp")
                $ ian_emma_pics.append("v8_emma_jeremy.webp")
                $ fian = "surprise"
                i "That's Emma! You scored with her already?"
                j "Yeah! I just fucked her the other day."
                $ fian = "smile"
                i "Man, that was FAST!"
                j "Well, yeah, what can I say? I followed the lead you gave me and indeed, it doesn't take much to get her going!"
                j "She was up for it right away, so we met and..."
                j "You were right, she's a real nympho!"
                i "It surprised me too. I always knew she was a sexually open girl, but she likes to get kinky..."
                j "She had this girly side I wasn't aware of... But at the same time, she was cool and fun, it was weird."
                $ fjeremy = "flirt"
                j "And it is as you said, she's a sucker for anal sex!"
                $ fian = "confident"
                i "Fucking her ass was like fucking a pussy, pretty amazing."
                if ian_lust < 7:
                    call xp_up('lust') from _call_xp_up_564
                $ fjeremy = "n"
                j "Unfortunately, I couldn't really experience it... We tried it, but my cock wouldn't really fit."
                i "Sucks for you, bro. I really enjoyed it."
                $ fjeremy = "flirt"
                j "But she was fingering herself like mad while I crushed her pussy!"
                i "Nice."
                hide v8_emma_jeremy with short
                show ian at lef
                show jeremy at rig
                with move
                $ fian = "smile"
                if ian_alison_sex and alison_jeremy:
                    i "So this is the second girl we've both fucked..."
                    $ fjeremy = "happy"
                    j "Eskimo brothers on the double! I owe you for this one."
                    play sound "sfx/high5.mp3"
                    "Jeremy high-fived me."
                else:
                    $ fian = "happy"
                    i "So, we've become Eskimo brothers, you and me, ha ha."
                    $ fjeremy = "happy"
                    j "Hell yeah. I owe you for this one."
                    play sound "sfx/high5.mp3"
                    "Jeremy high-fived me."

                if ian_jeremy < 11:
                    call friend_xp('jeremy', 2) from _call_friend_xp_742
                elif ian_jeremy < 12:
                    call friend_xp('jeremy', 1) from _call_friend_xp_743

                hide v8_emma_jeremy with short
                show ian at lef
                show jeremy at rig
                with move
                $ fian = "smile"
                i "I need to ask you this, though..."

            "I don't need to see this":
                $ renpy.block_rollback()
                # emma_jeremy = 1
                $ fian = "disgusted"
                i "I don't need to see this, thanks!"
                hide v8_emma_jeremy with short
                show ian at lef
                show jeremy at rig
                with move
                $ fjeremy = "n"
                j "Dude, since when are you so prudish?"
                $ fjeremy = "happy"
                $ fian = "n"
                j "Anyway, that was Emma! I just fucked her the other day."
                $ fian = "smile"
                i "Already? Man, that was FAST!"
                j "Well, yeah, what can I say? I followed the lead you gave me and indeed, it doesn't take much to get her going!"
                if ian_jeremy < 12:
                    call friend_xp('jeremy', 1) from _call_friend_xp_744
                j "She was up for it right away, so we met and..."

        $ fian = "n"
        i "How come you always have pictures of the girls you've fucked? How do you do it?"
        $ fjeremy = "smile"
        j "What do you mean how I do it?"
        i "You just casually ask them, \"Hey, is it okay if I take a couple of pictures?\"."
        j "Pretty much, yeah."
        i "No way. That's too easy."
        $ fjeremy = "happy"
        j "What can I say? It must be my incredible sex appeal, ha ha!"
        j "That, and also you'd be surprised with some of the kinky things girls are willing to do when they are really turned on."
        j "They're not so different from us horny beasts, after all!"
        $ fian = "smile"
        i "I'll try to remember that."

    # alison
    if alison_jeremy and ian_alison_dating == False:
        $ fian = "n"
        i "Speaking of which... How's it going with Alison?"
        if v7_alison_voyeur:
            $ fjeremy = "flirt"
            j "I haven't seen her in a while... Those videos I sent you were from last time!"
            $ fjeremy = "smile"
            j "She says she's busy with work or something, but she said she wants us to spend a weekend together somewhere."
        else:
            $ fjeremy = "smile"
            j "I haven't seen her in a while. She says she's busy with work or something, but she said she wants us to spend a weekend together somewhere."
        i "So she's not going to replace Louise?"
        j "What? No, not at all. Alison and I want the same thing, and we're both clear about it."
        j "We like to hook up from time to time, but that's it. Just for fun."
        if ian_alison_sex:
            j "What about you? Have you hooked up with her again?"
            i "No... I don't think she wants to."
            if ian_lena_dating:
                j "Do you think she's jealous of you and Emma? Or about you and Lena, maybe..."
            else:
                j "Do you think she's jealous of you and Emma?"
            i "Who knows... But it doesn't make sense for her to be jealous and hook up with you at the same time."
            if ian_wits > 5:
                $ fian = "worried"
                i "Wait, maybe that's exactly what's happening..."
            j "I don't know about that. Chicks often don't make sense!"
            if alison_jeremy_3some > 0:
                $ fjeremy = "flirt"
                j "But when I pitched her the idea of a threesome she didn't say no right away..."
                $ fian = "n"
                i "What did she say?"
                $ fjeremy = "happy"
                j "She wanted to know if you were up for it, also. I told her you were, and she said she'd think about it."
                i "I never said I was up for it. I'm still considering it, in fact."
                j "Come on man, why can't you people be honest? I think both of you want to do it but you're afraid to say so."
                j "Try to be more like me, you'll see life gets much better!"
            elif ian_jeremy > 5:
                $ fjeremy = "flirt"
                j "I can ask her about it, if you want. I have the feeling she'd like to fuck with you again..."
                j "We can both give her some loving!"
                $ fian = "smile"
                i "Don't get carried away!"
        else:
            $ fian = "smile"
            i "Good for you, then. At least you're on the same page."
            if alison_jeremy_3some > 0:
                $ fjeremy = "flirt"
                j "By the way, I pitched her the idea of a threesome, just like we talked about..."
                $ fian = "n"
                i "And... what did she say?"
                $ fjeremy = "happy"
                j "she didn't say no right away. She wanted to know if you were up for it, and I told her you were."
                i "I never said I was up for it. I'm still considering it, in fact."
                j "Whatever man, she also said she'd think about it. But the way I see things, both of you want to do it but you're afraid to say so."
                j "Try to be more honest, like me, and you'll see life gets much better!"

    # lena
    if v7_bbc == "lena":
        $ fian = "smile"
        i "By the way, you still haven't told me about that \"crazy game\" Ivy came up with..."
        if ian_lena_love:
            $ fjeremy = "n"
            j "Oh yeah, about that... Turns out that..."
        else:
            $ fjeremy = "smile"
            j "Oh, yeah. So Louise, Lena and I went to Ivy's place and..."
##MILO FIGHT
    $ v8calmmilo = False
    show ian at lef3
    show jeremy at rig3
    with move
    show alison_ex with short
    play music "music/tension.mp3" loop
    guy "Who's the one?"
    $ fian = "worried"
    $ fjeremy = "n"
    j "Huh?"
    guy "Tell me. Is it you, Ian?"
    "I knew this guy. I had seen him a few times, but that unkempt beard and his growing baldness had thrown me off for a second."
    "He was Milo, Alison's ex-boyfriend."
    j "Hey, Milo... Long time no see. Why are you here?"
    milo "Don't play dumb! You know why I'm here."
    milo "Who's the one fooling around with Alison?"
    "He was clearly in a very agitated mood. Not good."
    $ v8extalk = 0 # contextual menu flag: 1-calm down 2-taunt
    menu v8exfight:
        "Calm Milo down" if v8calmmilo == False:
            $ renpy.block_rollback()
            $ v8calmmilo = True
            $ v8extalk = 1
            "I tried to defuse the situation."
            i "Milo, slow down. We don't know what's going on..."
            if ian_wits < 8:
                call xp_up('wits') from _call_xp_up_565
            milo "Yes, you do!"
            i "Still, if you could explain everything from the start..."
            milo "Isn't it obvious? I've been trying to talk to Alison, but she keeps avoiding me..."
            milo "I know it's because she's with someone else. And I know it's either you or Jeremy!"
            j "Seems Alison's tongue slipped."
            milo "So tell me! Who is it?!"
            jump v8exfight

        "Taunt him":
            $ renpy.block_rollback()
            $ v8extalk = 2
            $ fian = "serious"
            if ian_chad < 5:
                $ ian_chad += 1
            i "What's it to you? She's no longer your girlfriend."
            milo "She would be again if it wasn't for one of you two!"
            milo "I've been trying to talk to Alison, but she keeps avoiding me..."
            milo "And I know it's because she's with someone else. And I know it's either you or Jeremy!"
            i "Dude, be real. We're not the cause of Alison dumping your sorry ass."
            i "You earned that yourself, without anyone's help."
            if ian_charisma < 8 and v8calmmilo == False:
                call xp_up('charisma') from _call_xp_up_566
            j "Bro, is taunting him the smartest option...?"
            milo "What would you know?!"
            i "It's fucking obvious, looking at you. Do you think you're acting like a normal person right now?"
            milo "You'd do the same in my situation. If someone did to you what you guys did to me!"
            milo "Now tell me! Who is it? Who's banging Alison!?"
            jump v8exfightian

        "It's me, I'm with Alison" if ian_alison_dating:
            $ renpy.block_rollback()
            i "It's me. I've been with Alison."
            milo "I knew it! I always knew there was something weird going on between you two!"
            i "What are you talking about? This is something that happened recently."
            if ian_charisma < 8 and v8extalk == 0:
                call xp_up('charisma') from _call_xp_up_567
            i "When you two were together we..."
            milo "You think I'm stupid, too? I knew I should've never trusted you... None of you!"
            j "Bro, this dude is losing it."
            i "He might've already lost it..."
            milo "What, you think I'm crazy, too?"
            milo "After what you two did to me... How could she... I love her!"
            jump v8exfightian

        "It's Jeremy":
            $ renpy.block_rollback()
            if alison_jeremy:
                $ fian = "n"
                i "It's Jeremy. He's been banging Alison."
                $ fjeremy = "surprise"
                j "Dude, what...?"

                call friend_xp('jeremy', -1) from _call_friend_xp_745
                if ian_jeremy > 7:
                    $ ian_jeremy = 4
                else:
                    $ ian_jeremy = 3

                milo "You! You and Alison!?" with vpunch
                $ fjeremy = "serious"
                "Jeremy looked at me."
                j "Thanks, \"bro\"."
                i "Hey, you made your bed and you'll have to lie in it."
                jump v8exfightjeremy
            else:
                $ fian = "evil"
                i "Actually, it was Jeremy."
                $ fjeremy = "surprise"
                j "What!? Didn't they teach you it's wrong to tell lies, bro?"
                milo "So it was you, Jeremy? You and Alison!?"
                j "No, it wasn't!"
                $ fjeremy = "n"
                j "She wouldn't let me..."
                $ fian = "happy"
                milo "What!?"
                i "Ha ha ha!"
                if ian_charisma < 8 and v8extalk == 0:
                    call xp_up('charisma') from _call_xp_up_568
                milo "You think it's funny, you piece of shit?"
                jump v8exfightian

        "Actually, it's both of us" if ian_alison_sex and alison_jeremy:
            $ renpy.block_rollback()
            $ fian = "n"
            i "Actually... It's both of us. Jeremy and I are Eskimo brothers."
            milo "What?"
            i "It means we've both banged the same girl. Alison in this case."
            j "But not at the same time! Not yet at least..."
            milo "What!?" with vpunch
            j "Maybe it wasn't a good idea to tell him the Eskimo brothers thing."
            $ fian = "happy"
            i "It's not something I'm ashamed of, bro. Our brotherhood should be celebrated."
            $ fjeremy = "smile"
            j "That's true. I love you, bro. No homo."
            $ fian = "smile"
            i "No homo."
            call friend_xp('jeremy', 1) from _call_friend_xp_746
            milo "What the fuck are you clowns going on about!?" with vpunch
            $ fian = "n"
            $ fjeremy = "n"
            milo "Alison slept with both of you? No way!"
            jump v8exfightian

        "I'm not telling you":
            $ renpy.block_rollback()
            $ fian = "serious"
            i "I'm sorry, but I'm not talking to you unless you calm yourself first and act like a civilized person."
            milo "Don't act all high and mighty! It's you who wronged me! One of you two!"
            if v8extalk == 0:
                milo "I've been trying to talk to Alison, but she keeps avoiding me..."
                milo "And I know it's because she's with someone else. And I know it's either you or Jeremy!"
            milo "So now you're gonna tell me! You owe me that!"
            $ fjeremy = "serious"
            if alison_jeremy:
                milo "If you have any honor as a man, you'll step up and..."
                j "It's me, okay? Alison and I have been seeing each other."
                j "Now, go bother someone else."
            else:
                milo "You're gonna tell me, or I'll force you to!"
                j "You're not gonna force anybody, okay buddy? Now get the hell out of here before you get hurt."
            jump v8exfightjeremy

label v8exfightian:
    play music "music/fight3.mp3" loop
    milo " You ruined it! You destroyed my life!"
    $ fian = "serious"
    if ian_chad > 4:
        i "Dude, get the hell out of here before you get hurt..."
    elif ian_chad > 2:
        i "You're talking out of your mind right now, Milo. You should go home and..."
    else:
        i "Milo, please, calm down. I think it's best if you go home and..."
    milo "I'm not going anywhere!"
    $ fjeremy = "sad"
    milo "{i}Rhaaaa!!{/i}" with vpunch
    "Milo charged at me, fist raised!"
    $ timeout = 5.0
    $ timeout_label = "v8exavoid"
    menu:
        "Punch him":
            $ renpy.block_rollback()
            $ v8_alison_ex = "ian"
            stop music fadeout 1.0
            play sound "sfx/big_punch.mp3"
            pause 0.5
            scene v8_fight_ian with vpunch
            "I intercepted him with a punch of my own."
            "My fist connected right on the dome and Milo fell to the ground like a puppet whose strings have been suddenly cut."
            $ fian = "worried"
            $ fjeremy = "surprise"
            play sound "sfx/fall.mp3"
            scene streetnight
            show jeremy at rig3
            show ian at lef3
            with short
            j "Woah! You knocked him out!"
            i "Shit, did I hit him too hard?"
            if ian_athletics < 10:
                call xp_up ('athletics') from _call_xp_up_569
            jump v8exend

        "{image=icon_athletics.webp}Throw him" if jiujitsu > 0:
            $ renpy.block_rollback()
            $ v8_alison_ex = "ian"
            play sound "sfx/slap.mp3"
            scene v8_fight_ian2 with fps
            "I wasn't gonna let him hit me."
            "I intercepted his punch and twisted my whole body around, like I had done in training so many times now."
            "Milo's body was launched upwards before landing on the hard ground with all my weight behind him."
            $ fian = "worried"
            $ fjeremy = "surprise"
            stop music fadeout 1.0
            play sound "sfx/big_throw.mp3"
            scene streetnight with vpunch
            show jeremy at rig3 with short
            show ian at lef3 with short
            j "Woah! You knocked him out!"
            i "Fuck, I slammed him on concrete! It's not the same as doing it on the mat..."
            if ian_athletics < 10:
                call xp_up ('athletics') from _call_xp_up_570
            jump v8exend

        "Avoid the fight":
            $ renpy.block_rollback()
            label v8exavoid:
                $ fian = "worried"
            play sound "sfx/miss.mp3"
            show ian at truecenter
            show alison_ex at left
            with move
            "I ducked under Milo's punch, avoiding his attack."
            i "Hey, that's enough! I don't wanna fight you!"
            if ian_wits < 8:
                call xp_up('wits') from _call_xp_up_571
            milo "You will, if you have any honor left!"
            show alison_ex at lef3 with move
            "He readied himself to attack again."
            milo "{i}Rhaaaa!!{/i}" with vpunch
            jump v8exfightjeremy2

label v8exfightjeremy:
    milo " You ruined it! You destroyed my life!"
    $ fian = "worried"
    milo "{i}Rhaaaa!!{/i}" with vpunch
    "Milo shouted as he charged Jeremy, fist raised."
    label v8exfightjeremy2:
        $ v8_alison_ex = "jeremy"
    stop music fadeout 1.0
    play sound "sfx/big_punch.mp3"
    pause 0.5
    scene v8_fight_jeremy with vpunch
    "He didn't get far."
    "Jeremy punched him right in the dome and Milo fell to the ground like a puppet whose strings have been suddenly cut."
    $ fian = "surprise"
    play sound "sfx/fall.mp3"
    scene streetnight
    show jeremy at rig3
    show ian at lef3
    with short
    i "Woah! You knocked him out!"
    $ fjeremy = "sad"
    j "He came at me! I just defended myself..."
    $ fian = "worried"
    label v8exend:
        milo "{i}UuUuUhHh...{/i}"
    "Milo moaned, squirming on the ground. Some people on the street were taking notice of what had just happened."
    $ fjeremy = "n"
    j "Come on, let's get out of here. He seems okay."
    scene streetnight with short
    "We didn't want any more trouble, so we bailed before Milo had the chance to fully get back to his senses."

## HOME ##########################################################
    $ timeout_label = None
    $ ian_milo_agenda = True
    scene ianhomenight with long
    play sound "sfx/door_home.mp3"
    play music "music/normal_day.mp3" loop
    "I arrived at my place just as I finished talking to Lena over the phone."
    if ian_lena_dating and v7_holly_kiss == False:
        $ fian = "happy"
        show ian_phone
        with short
        i "... I doubt I would've had the courage to talk to such a stunning girl."
        show phone_lena_shy at lef3 with short
        l "Someone as charming as you? Come on."
        if ian_lena_love:
            i "In any case, I'm also really happy you decided to talk to me that day. It's one of the best things that's happened to me in... a very long time."
            l "You're exaggerating."
            i "Not at all."
        else:
            i "In any case, I'm also really happy you decided to talk to me that day. It's been so fun since then!"
        i "And I'm pumped to see you perform, finally!"
        l "Shhh, I don't need any more pressure!"
        i "Sorry, sorry. But it's true that I'm looking forward to that. Perry and I will be there."
        hide phone_lena_shy
        show phone_lena_smile at lef3
        l "Thank you for supporting me, Ian."
        $ fian = "smile"
        i "You have all my support. Rest up and see you tomorrow! I'm sure you'll crush it!"
        l "Bye! And thanks for calling."
        hide phone_lena_smile
        hide ian_phone
        show ian
        with short
        if lena_ian_love:
            $ fian = "happy"
            "Talking to Lena always lifted my spirit up. I felt so lucky to have her in my life."
        else:
            "Talking to Lena always put me in a good mood. I was happy to have met her."
    elif ian_lena_dating and v7_holly_kiss:
        $ fian = "n"
        show phone_lena at lef3
        show ian_phone
        with short
        i "... So, about tomorrow... Is it okay if Perry and I show up to the concert?"
        l "Yeah, of course. No problem."
        $ fian = "smile"
        i "Cool... I want to show my support, but I wasn't sure you'd be comfortable."
        if ian_lena_over:
            l "It's okay. We're clear on where we stand, now. No more misunderstandings."
            $ fian = "n"
            i "Yeah..."
        else:
            l "As I said, don't worry. We're fine."
            i "Glad to know."
        i "Anyway, try resting up and see you tomorrow. I'm sure you'll crush it."
        l "Bye!"
        hide phone_lena
        hide ian_phone
        show ian
        with short
        if ian_lena_over:
            $ fian = "sad"
            i "That was rather tense... But I already knew it would be."
            "Lena was not happy about what happened with Holly, but at least she still wanted to hang out with me, or so it seemed..."
            "Only as friends, of course."
            $ fian = "n"
            i "At least it's something... I was afraid she would kick me out of her life."
        else:
            $ fian = "smile"
            i "I wasn't expecting this, but it seems she's not as mad as she was yesterday..."
            i "Has she forgiven me for what happened with Holly? I really hope so..."
    else:
        $ fian = "happy"
        show phone_lena_shy at lef3
        show ian_phone
        with short
        i "... And I'm pumped to see you perform, finally!"
        l "Shhh, I don't need any more pressure!"
        i "Sorry, sorry. But it's true that I'm looking forward to that. Perry and I will be there."
        if ian_holly_sex and ian_holly_dating == False:
            hide phone_lena_shy
            show phone_lena at lef3
            l "Alright! I'm still mad about what happened with Holly, though."
            $ fian = "n"
            i "Yeah, I know. It was my mistake. I'll try to make up for it."
            l "Good! Anyway, thanks for calling. I appreciate it!"
            hide phone_lena
            hide ian_phone
            show ian
            with short
            i "She didn't like me sleeping with Holly and then telling her I didn't want anything serious at all..."
            i "I can't blame her. She's worried about her friend and maybe I was a bit of a dick."
        else:
            hide phone_lena_shy
            show phone_lena_smile at lef3
            l "Thank you for supporting me, Ian."
            $ fian = "smile"
            i "It's the least I can do. Rest up and see you tomorrow!"
            l "Bye! And thanks for calling."
            hide phone_lena_smile
            hide ian_phone
            show ian
            with short
            i "There, I gave her my words of encouragement. I hope that helps her even if just a bit."
    $ fperry = "n"
    show ian at lef with move
    show perry at rig with short
    if ian_lena_dating:
        p "Having some l--{w=0.5}lovey-dovey talk with your girl?"
        if ian_lena_over:
            i "I already told you. She's not my girl, it's over."
        else:
            $ fian = "n"
            i "Please, don't call her \"my girl\" in front of her tomorrow."
            p "Why? Isn't she?"
            if ian_lena_love:
                i "We haven't had the talk yet. You would just make her uncomfortable... again."
            else:
                i "She's not, I already told you. We're just friends..."
                p "Yeah, sure."
    else:
        p "You were talking to L--{w=0.5}Lena?"
        i "Yeah, wishing her good luck for tomorrow."
    $ fperry = "smile"
    p "Anyway, d--{w=0.5}do you have plans for this Saturday?"
    i "Not yet..."
    p "Emma wants to get some beers here t--{w=0.5}tomorrow night."
    $ fian = "smile"
    i "Okay, cool. She said she wanted to show us something last week..."
    i "So, who's coming?"
    if v7_cindy_kiss:
        p "You, me, Emma..."
        $ fian = "n"
        i "What about Wade... and Cindy?"
        $ fperry = "meh"
        p "I told him, but he's s--{w=0.5}spending the weekend with Cindy. She wanted to go visit some village on the c--{w=0.5}coast or something like that."
        i "Oh... I see."
        p "But we don't need them."
    elif wade_cindy == 2:
        p "You, me, Emma..."
        i "What about Wade and Cindy?"
        $ fperry = "meh"
        p "I told him, but he's s--{w=0.5}spending the weekend with Cindy. She wanted to go visit some village on the c--{w=0.5}coast or something like that."
        i "Good for them."
        p "Whatever. We don't need them."
    else:
        $ fperry = "meh"
        p "You, me, Emma... Probably Wade, if C--{w=0.5}Cindy allows it."
    i "Have you told Alison?"
    p "No, but she already said she would be super b--{w=0.5}busy with work, didn't she?"
    i "That's right..."
    if ian_lena_dating and ian_lena_over == False:
        $ fian = "smile"
        i "I could tell Lena to join us."
        $ fperry = "n"
        p "Sure, why not. She's c--{w=0.5}cool."
        if ian_cherry_dating == False:
            $ fperry = "smile"
            p "And I've also invited Cherry."
            if v2_cherry_home:
                $ fian = "worried"
                i "You did? I'm not sure if it's a good idea that she and Lena meet..."
                p "Why not?"
                if ian_cherry_sex:
                    $ fian = "n"
                    i "Because, you know... I fucked her."
                else:
                    i "Because, you know... Cherry and I hooked up, kind of..."
                p "That was quite a while ago, and it hasn't happened since, right?"
                $ fian = "n"
                i "Nope."
                p "Then it shouldn't be a problem."
                i "I guess."
            else:
                i "Oh, and she agreed to come?"
                p "Yeah, s--{w=0.5}she said she'd drop by."
                i "Nice. I didn't know you had such convincing power..."
                $ fian = "confident"
                i "Is it a date or something...?"
                $ fperry = "n"
                p "Nothing of that sort. She's j--{w=0.5}just a cool girl and I thought it'd be fun to invite her. She said y--{w=0.5}yes, so..."
                $ fian = "smile"
                i "That's good. I also think she's cool."
    elif ian_cherry_dating:
        $ fperry = "n"
        p "You could invite Cherry. She's c--{w=0.5}cool, and we haven't seen her for some time."
        i "It's been a while since I talked to her, but I could invite her."
        p "It'd be n--{w=0.5}nice if she joined."
    else:
        $ fperry = "smile"
        p "I've also invited Cherry."
        $ fian = "smile"
        i "Oh, and she agreed to come?"
        p "Yeah, s--{w=0.5}she said she'd drop by."
        i "Nice. I didn't know you had such convincing power..."
        $ fian = "confident"
        i "Is it a date or something...?"
        $ fperry = "n"
        p "Nothing of that sort. She's j--{w=0.5}just a cool girl and I thought it'd be fun to invite her. She said y--{w=0.5}yes, so..."
        $ fian = "smile"
        i "That's good. I think she's cool, too."
    i "Alright, so that's the plan for Saturday. I'll be sure to buy some beers for our guests..."
    i "You get your own, or else you'll wipe out our stock, considering your drinking habits."
    p "Yeah, yeah, whatever. I'll b--{w=0.5}buy my own."
    i "By the way, you won't believe what just happened. Do you remember Milo, Alison's ex...?"
    stop music fadeout 2.0
    "I cooked myself some dinner while telling Perry about the recent incident, and after that, I went to my room."
    play sound "sfx/door.mp3"
    scene ianroomnight with long
    $ ian_look = 2
    $ fian = "n"
    show ian with short
    if v7_effort_job > 0:
        i "I'm tired as hell after pedaling around the city all day and then going to the gym..."
        i "I don't know if I'll be able to keep this up! And I should do some writing before going to bed..."
    else:
        i "I'm tired as hell after that training session, but I should do some writing before going to bed..."
##LENA SEXTING
    if v8_lena_sexting == "ian":
        play sound "sfx/sms.mp3"
        i "Hm? A message... From Lena."
        play music "music/shooting.mp3" loop
        show ian at left with move
        $ fian = "surprise"
        show v8_sexting1 with short
        $ ian_lena_pics.append("v8_sexting1.webp")
        nvl clear
        l_p "{i}I'm here thinking about you and that sweet phone call from before... {image=emoji_shy.webp}{/i}"
        $ fian = "shy"
        i "Wow, I wasn't expecting this... But it's a very welcome surprise!"
        $ fian = "smile"
        i "Damn, look at her... She's the most beautiful girl I've ever seen."
        if v7_cindy_kiss:
            $ fian = "n"
            i "Well, it's a very contested podium between Cindy and Lena..."
            $ fian = "smile"
        "I texted her my reply."
        i_p "{i}I will call you every day if this is what I get in return {image=emoji_love.webp}{/i}"
        l_p "{i}Oh, you like it?{/i}"
        i_p "{i}Like you don't know. I love it. I can't believe how beautiful you are.{/i}"
        l_p "{i}I'm sure that's what you say to all the girls who send you sexy pics.{/i}"
        i_p "{i}Oh, so you think I get a lot of pics like these?{/i}"
        l_p "{i}You don't?{/i}"
        if ian_charisma > 4:
            $ fian = "confident"
            if v6_ian_selfie:
                i_p "{i}None of them come close to the ones you send me. They're my favorite, by far {image=emoji_wink.webp}{/i}"
            else:
                i_p "{i}None of them come close to the one you sent me. It's my favorite, by far {image=emoji_wink.webp}{/i}"
        else:
            i_p "{i}Not really... Yours are the only ones I want to see, though {image=emoji_shy.webp}{/i}"
        l_p "{i}Is that so? {image=emoji_flirt.webp}{/i}"
        play sound "sfx/sms.mp3"
        hide v8_sexting1
        show v8_sexting2_comp
        with short
        $ ian_lena_pics.append("v8_sexting2_comp")
        $ fian = "shy"
        i "Oh, fuck."
        "Looking at Lena's perfect body had my cock throbbing and swelling inside my pants. She turned me on beyond reason."
        "And the fact that she was taking these pictures just for me was even better. She enjoyed showing off for me."
        $ fian = "confident"
        i_p "{i}Are you sure you're even human?{/i}"
        l_p "{i}What's that supposed to mean?{/i}"
        if ian_charisma > 4 or ian_wits > 4:
            i_p "{i}It means I can't take my eyes off the screen now. I might stare at it until the sun comes up.{/i}"
        else:
            $ fian = "smile"
            i_p "{i}It means you're way too perfect!{/i}"
        l_p "{i}You're surely exaggerating {image=emoji_laugh.webp}{/i}"
        $ fian = "smile"
        i_p "{i}In all seriousness, you're the most beautiful girl I've ever met, Lena. I can't believe how lucky I am.{/i}"
        l_p "{i}If you like my pics so much I surely can get some in return...{/i}"
        "She was in a really playful mood..."
        i_p "{i}You want me to send you a picture?{/i}"
        l_p "{i}It's not fair I'm the only one sending them, is it?{/i}"
        if v6_ian_selfie:
            i_p "{i}I sent you one that one time...{/i}"
            l_p "{i}Just one! Come on, don't make a girl beg!{/i}"
        i_p "{i}Okay, wait a second...{/i}"
        play sound "sfx/door.mp3"
        $ fian = "n"
        scene ianhomenight
        show ian
        with long
        "I didn't have any mirrors in my room, so I moved to the bathroom."
        if v6_ian_selfie:
            i "Let's see... It's been ages since I sent a sexy selfie to a girl, except for that one I sent Lena..."
            i "I can't be repetitive. How should I do this...?"
        else:
            i "Let's see... It's been ages since I sent a sexy selfie to a girl. And it's not something I've ever done much..."
            i "How should I do this?"
        hide ian
        show ianunder
        with short
        "I took off my shirt and stood in front of the mirror."
        i "..."
        show ianunder at left with move
        show v8_sexting_ian
        with short
        i "Is this pose good? I feel a bit ridiculous..."
        "I twisted my torso and contracted my muscles, trying to make them pop. I had been working out for a reason..."
        i "I can't breathe like this..."
        play sound "sfx/camera.mp3"
        $ ian_ian_pics.append("v8_sexting_ian.webp")
        "I snapped the pic and looked at it, pondering if it was worthy of being sent."
        i "Ah, fuck it. Unlike Lena, I'm not a model."
        play sound "sfx/sms.mp3"
        i_p "{i}Here you go {image=emoji_ups.webp}{/i}"
        l_p "{i}What's with that face? {image=emoji_laugh.webp}{/i}"
        i_p "{i}Isn't that how you're supposed to do it? I'm afraid I'm rather new in the sexy selfie business...{/i}"
        l_p "{i}You have a lot to learn, indeed... Allow me to say the duckface doesn't suit you!{/i}"
        $ fian = "n"
        i "I'm not sure she was satisfied with the funny angle. I should take this seriously."
        $ fian = "smile"
        i_p "{i}Okay, let me try again...{/i}"
        hide v8_sexting_ian
        with short
        i "Let's see..."
        show v8_sexting_ian2 with short
        i "Maybe like this..."
        stop music fadeout 2.0
        $ fperry = "sad"
        hide v8_sexting_ian2
        show perry
        with short
        p "What are you doing?"
        $ fian = "worried"
        i "What the--?"
        $ fian = "serious"
        i "Can I have some privacy while in the bathroom, for fuck's sake?"
        $ fperry = "meh"
        hide perry
        show perry2
        with short
        p "Hey, you didn't close the d--{w=0.5}door. What are you taking pictures for?"
        i "None of your business."
        p "I didn't know you were such a n--{w=0.5}narcissist..."
        i "It's not like that! Will you get lost already?"
        play sound "sfx/door_slam.mp3"
        hide perry2 with vpunch
        p "Hey! I need to take a shit! How much longer are you gonna take with the s--{w=0.5}selfies?"
        $ fian = "blush"
        i "..."
        i "Jeez, that was embarrassing..."
        $ fian = "n"
        i "Where was I...?"
        play music "music/shooting.mp3" loop
        if v8ianpicnude:
            show v8_sexting_ian2 with short
            i "There, I'm looking good like this..."
            i "On second thought, though..."
            play sound "sfx/camera.mp3"
            hide v8_sexting_ian2
            hide ianunder
            show iannude at left
            show v8_sexting_ian3
            with short
            $ ian_ian_pics.append("v8_sexting_ian3.webp")
            i "I should be equitable with Lena. She wasn't wearing anything..."
            play sound "sfx/sms.mp3"
            i "Aaand sent."
            l_p "{i}Now we're talking! {image=emoji_shy.webp} {image=emoji_fire.webp}{/i}"
            l_p "{i}Now's me who has her eyes glued to the screen... Damn, you're one sexy man!{/i}"
            $ fian = "happy"
            i "Ha, she liked it!"
            i_p "{i}Don't get too used to it!{/i}"
        else:
            $ fian = "smile"
            play sound "sfx/camera.mp3"
            show v8_sexting_ian2 with short
            $ ian_ian_pics.append("v8_sexting_ian2.webp")
            i "There. This will have to do..."
            play sound "sfx/sms.mp3"
            l_p "{i}That's better... {image=emoji_tongue.webp}{/i}"
            l_p "{i}I really like this pic... But next time maybe you could lose the pants, too?{/i}"
            i_p "{i}I thought girls hated dick pics!{/i}"
        if v8_sexting_full:
            if ian_lust > 4:
                l_p "{i}Don't be like that... Here's a reward for being a good boy.{/i}"
            else:
                l_p "{i}Not if it's from the guy they like... Or do you hate this?{/i}"
            hide v8_sexting_ian2
            hide v8_sexting_ian3
            show v8_sexting3
            with short
            $ ian_lena_pics.append("v8_sexting3.webp")
            $ fian = "surprise"
            i "Holy shit! She's on fire tonight...!"
            $ fian = "happy"
            i "I'm one lucky guy!"
            if ian_lust > 4:
                i_p "{i}Damn, Lena... You're trying to make my cock explode, aren't you?{/i}"
            else:
                i_p "{i}Damn, Lena... You're trying to give me a heart attack, aren't you?{/i}"
            if v8_stalkfap_dm2 == 1:
                l_p "{i}I had the feeling you'd like it {image=emoji_flirt.webp}{/i}"
            else:
                l_p "{i}This is for your eyes only {image=emoji_shy.webp}{/i}"
            scene ianroomnight with long
            $ fian = "shy"
            show ian at left with short
            "I put my clothes back on and went to my room to finish the conversation."
            show v8_sexting5 with short
            $ ian_lena_pics.append("v8_sexting5.webp")
            "Lena really was on fire. Things got even hotter than I was expecting... and I totally loved it."
            hide v8_sexting5
            with short
            "We said goodnight to each other with the promise of celebrating together tomorrow after Lena's concert."
            "I couldn't wait... Especially after those pics Lena sent me!"
        else:
            scene ianroomnight with long
            $ fian = "shy"
            "I put my clothes back on and went to my room to finish the conversation."
            show ian with short
            "We said goodnight to each other with the promise to see each other tomorrow."
            "I hoped that we could do more than just that... Especially after those pics Lena sent me!"
        "I had a raging hard-on that needed to be satisfied."
        i "No writing tonight, I guess..."

##ALISON SEXTING
    if v8_alison_sexting > 0:
        play sound "sfx/sms.mp3"
        $ fian = "n"
        if v8_lena_sexting == "ian":
            "I was about to give myself some enjoyment when my phone buzzed with another message."
        else:
            "I was about to sit down and get to it when my phone buzzed with a text message."
        i "It's Alison's... I should tell her about what happened with Milo."
        "That idea withered as soon as I saw what Alison had actually sent me."
        $ fian = "surprise"
        if v8_lena_sexting != "ian":
            play music "music/shooting.mp3" loop
        show ian at left with move
        show v8_selfie_alison3_comp with short
        $ ian_alison_pics.append("v8_selfie_alison3_comp")
        nvl clear
        a_p "{i}Are you still in a feisty mood like this morning?{/i}"
        $ fian = "confident"
        if v8_lena_sexting == "ian":
            "Alison's message couldn't have come at a better moment."
            i_p "{i}Hell yeah, especially if you show me those magnificent boobs...{/i}"
            a_p "{i}You seem to be obsessed with my boobs!{/i}"
        else:
            i "Damn, those magnificent boobs..."
            i_p "{i}I wasn't, but you got me right back into it with this pic.{/i}"
            a_p "{i}I knew you'd like it. You seem to be a bit obsessed with my boobs!{/i}"
        i_p "{i}You can't blame me for it. They're just wonderful {image=emoji_love.webp}{/i}"
        a_p "{i}I'm starting to think that's the only thing you like about me...  {image=emoji_roll.webp} {image=emoji_crazy.webp}{/i}"
        i_p "{i}Not true. I also like being between your legs, in that empty space below you.{/i}"
        i_p "{i}With my cock inside of you and you riding me till orgasm {image=emoji_flirt.webp}{/i}"
        a_p "{i}I like the sound of that. I really love riding you {image=emoji_fire.webp} {/i}"
        i_p "{i}You also love being on all fours, if I recall correctly...{/i}"
        hide v8_selfie_alison3_comp
        show v8_selfie_alison4
        with short
        $ ian_alison_pics.append("v8_selfie_alison4.webp")
        a_p "{i}If it's with you, I love every position.{/i}"
        if v8_alison_sexting == 2:
            a_p "{i}By the way, I believe I owe you another selfie from this morning.{/i}"
        else:
            "Alison sent another selfie, this one even more daring."
        scene v7_jerkoff_animation1b
        show v8_selfie_alison4 at ianjerk
        with short
        "I couldn't refrain myself anymore. I unzipped my pants and started jacking off."
        if v8_lena_sexting == "ian":
            i "Tonight must be my lucky night! I can't believe this..."
            i "First Lena starts sexting me horny as hell, and now Alison...!"
        else:
            i "She's really horny tonight...!"
        menu:
            "{image=icon_charisma.webp}I'd love to fuck your ass" if ian_charisma > 4:
                $ renpy.block_rollback()
                $ v8_alison_sext = 3
                i_p "{i}I'd love to get behind you, just like that, and slide my cock into that sexy ass of ours... {/i}"
                if ian_lust < 6:
                    call xp_up('lust') from _call_xp_up_572
                a_p "{i}Ian! {image=emoji_shy.webp}{/i}"
                a_p "{i}Unfortunately, I don't like anal. But you can fuck my pussy as much as you want...{/i}"
                a_p "{i}Or my mouth, or my tits...{/i}"
                i_p "{i}I'll have to settle with that.{/i}"
                a_p "{i}You fuck me so good, Ian. Nobody's fucked me like you before, and I want more.{/i}"

            "{image=icon_lust.webp}I'd love to cum inside your pussy" if ian_lust > 5 or v6_alison_cum:
                $ renpy.block_rollback()
                $ v8_alison_sext = 2
                i_p "{i}I'd love to shoot my load inside that hot, nice pussy {image=emoji_cum.webp}{/i}"
                if v6_alison_cum:
                    a_p "{i}You already did once, even though you were not supposed to...{/i}"
                    i_p "{i}What can I say? I just couldn't resist the temptation.{/i}"
                    a_p "{i}Yeah, I'm afraid it was impossible to resist... You were fucking me so good...{/i}"
                else:
                    a_p "{i}Ian! {image=emoji_shy.webp} {image=emoji_devil.webp}{/i}"
                    i_p "{i}Imagining it turns me on so much.{/i}"
                    a_p "{i}Feeling your cum inside me... Oh, God.{/i}"
                    a_p "{i}You fuck me so good, Ian. Nobody's fucked me like you before, and I want more.{/i}"

            "I love giving it to you from behind":
                $ renpy.block_rollback()
                $ v8_alison_sext = 1
                i_p "{i}And I fucking love giving it to you from behind.{/i}"
                a_p "{i}You can give it to me any way you want, Ian.{/i}"
                a_p "{i}Nobody's fucked me like you before, and I want more.{/i}"

        i "Damn, this is nuts. Alison...!"
        i_p "{i}I wish I was there with you, pumping my hips into you. Just the way you like it, making you moan and tremble.{/i}"
        a_p "{i}Yes, please. I can't wait to be done with the audit and have you pounding inside of me...{/i}"
        hide v8_selfie_alison4
        show v8_selfie_alison5 at ianjerk
        with short
        $ ian_alison_pics.append("v8_selfie_alison5.webp")
        a_p "{i}Just like this.{/i}"
        "Alison sent me a short video."
        "She was lying on her bed, phone between her legs, and for a few seconds, I saw how she dug her fingers inside her wet pussy."
        "She dug them deeply, moaning and contorting her hips, masturbating while thinking about my cock inside of her."
        i_p "{i}You're gonna make me blow my load, Alison {image=emoji_fire.webp}{/i}"
        a_p "{i}You're also masturbating?{/i}"
        i_p "{i}What do you think?{/i}"
        a_p "{i}I showed you mine... Now show me yours.{/i}"
        "Was she asking me for a dick pic?"
        "If I was going to send her one it should be quick. I was about to cum...!"
        hide v8_selfie_alison5
        show v8_selfie_dick at ianjerk
        with short
        $ ian_ian_pics.append("v8_selfie_dick.webp")
        "She had sent me a video. I decided to repay her in kind."
        "I pushed the recording button and closed my eyes, finally letting myself go."
        scene v7_jerkoff1b
        show v7_jerkoff_cum1
        with flash
        i "Anhhh!!!"
        with vpunch
        pause 0.7
        with vpunch
        pause 0.7
        with vpunch
        $ fian = "smile"
        scene ianroomnight
        show ianunder
        with long
        stop music fadeout 2.0
        i "{i}Whew...{/i} That was nice."
        "Alison seemed to like the video."
        play sound "sfx/sms.mp3"
        if v8_alison_sext == 2 or v6_alison_cum:
            a_p "{i}Oh my God. I wish you shot that inside of me...{/i}"
        else:
            a_p "{i}Oh my God. I wish you shot that all over me...{/i}"
        i_p "{i}And I wish that was my cock in place of your fingers. Though I really enjoyed seeing them in action... {image=emoji_devil.webp}{/i}"
        a_p "{i}You got me so horny I couldn't help myself {image=emoji_ups.webp}{/i}"
        if ian_alison_like == 2:
            a_p "{i}We could rent a nice hotel room for that weekend we talked about and spend all day in bed {image=emoji_flirt.webp}{/i}"
            i_p "{i}That sounds awesome. I can't wait.{/i}"
            a_p "{i}Me neither. Good night, Ian {image=emoji_love.webp}{/i}"
        else:
            a_p "{i}I'm really missing having you around these days...{/i}"
            i_p "{i}We can hook up again once your workload lightens.{/i}"
            a_p "{i}Yeah, sure. I can't wait for that. Good night, Ian.{/i}"
        "I put the phone away."
        i "That was awesome... Alison keeps on surprising me!"
        i "But it seems I'm not gonna get any writing done tonight..."
        "I decided to call it a day and go to bed."

    elif v8_lena_sexting == "ian":
        stop music fadeout 2.0
        scene ianroomnight with long
        "I decided to call it a day and give myself some satisfaction before going to sleep."
    else:
        scene ianroomnight with long
        "I forced myself to sit in front of the computer and write, but barely anything of substance came out of it."
        "It seemed I wouldn't be doing anything useful that night, so I decided to call it a day and go to sleep."
## CHERRY
    if ian_cherry_dating:
        $ ian_look = 3
        $ fian = "n"
        show ianunder with short
        "As I was slipping into bed, Cherry came to mind."
        if ian_lena_dating and ian_lena_over == False:
            "I hadn't heard from her in a while..."
            "I thought about texting her, but maybe it was best if we didn't get in touch for a while."
            $ fian = "worried"
            "Lena had learned I had been sleeping with Cherry the night prior to our date thanks to Perry..."
            "She didn't complain, but it was clear she wasn't happy learning about it, and understandably so."
            $ fian = "n"
            if v7_holly_kiss:
                i "It's best to avoid any more close calls, especially after what just happened with Holly."
            else:
                i "It's best to avoid any more close calls for now..."
            i "I'll write to Cherry some other time."
        else:
            $ fian = "smile"
            i "Perry suggested I invite her this Saturday. Maybe she'd come..."
            "I picked up my phone and texted Cherry."
            nvl clear
            i_p "{i}Hey, how have you been? I was thinking about you and remembered I haven't heard from you in a while {image=emoji_wink.webp}{/i}"
            i_p "{i}This Saturday Perry, me and a couple of friends will be having some beers at our place. Maybe you'd like to join, if you don't have other plans!{/i}"
            "Her reply didn't keep me waiting."
            play sound "sfx/sms.mp3"
            ch_p "{i}Hey, Ian! Thanks for thinking about me {image=emoji_smile.webp}{/i}"
            ch_p "{i}It's been a very busy week at the office, but I'm sure Alison already told you that.{/i}"
            ch_p "{i}Anyway, I'm not as busy as she is, and I don't have a plan for this Saturday yet...{/i}"
            ch_p "{i}Wouldn't I be out of place if I came? I won't know anybody.{/i}"
            i_p "{i}Not at all. You know Perry and me, and Emma's the most welcoming and easygoing girl you'll ever meet.{/i}"
            ch_p "{i}Sounds good... Let me think about it, alright?{/i}"
            i_p "{i}Of course. We'd love to have you {image=emoji_smile.webp}{/i}"
            ch_p "{i}Thanks! Good night {image=emoji_kiss.webp} {/i}"
            i "She seems undecided... I hope she comes."
            i "Well, let's see what she says tomorrow."

## IAN FRIDAY ######################################################################################################################################################################################################################################################

    call calendar(_day="Friday") from _call_calendar_87

    $ ian_look = 3
    scene ianroom with long
    if v3_gillian_stop:
        "I woke up feeling well-rested that Friday morning."
        $ fian = "n"
        show ianunder with short
        "Thankfully, I hadn't been having any more dreams about Gillian again since that day, more than a week ago."
        "I hoped my subconscious would continue to behave and let me move forward in peace."
    else:
        "I woke up feeling a bit disoriented the next morning."
        $ fian = "worried"
        show ianunder with short
        "I had the impression I dreamed about Gillian again, but I couldn't remember what the dream was about..."
        "All that reminded me was that now familiar aftertaste of longing and regret."
        show ianunder at left with move
        $ fian = "depress"
        show v3_gillian2 with short
        "I pulled out my phone and looked at her picture, the memory of a moment frozen in time."
        "The ghost of a moment, long dead by now."
        i "..."
        $ fian = "n"
        hide v3_gillian2 with short
        show ianunder at truecenter with move
        i "I need to get started with my day..."
        scene ianroom with long
    play music "music/normal_day.mp3" loop
# morning
    if ian_job_magazine < 2:
        $ ian_look = 2
        $ fian = "n"
        if v7_effort_job > 0:
            $ ian_look = 1
            "I used Friday morning to do some chores before getting to work again."
            scene v8_rider1 with long
            "At least there was no shortage of deliveries to make and I could keep myself busy."
            "If my father saw me working as a rider, though... He would surely be disappointed."
            "But I was trying to put my life together, and for that I was willing to do whatever was necessary."
        else:
            "I used Friday morning to do some chores and progress some more on my book."
            show ian with short
            "I still wasn't feeling inspired... I had a lot on my mind."
            i "The day of the contest is getting close. This is not the time to be experiencing writer's block!"
            if v7_effort_weed:
                i "Maybe I could roll up a joint and give it a little puff. Might help with inspiration..."
            elif v7_effort_gym:
                i "Maybe if I do some exercise I will feel refreshed."
                hide ian with short
                "I began doing some push-ups, squats, and ab crunches in my room, took a shower, and got to writing again."
                if ian_athletics < 10:
                    call xp_up('athletics') from _call_xp_up_573
            else:
                i "I should try and write some more, even if I'm not feeling inspired..."
                play sound "sfx/keyboard.mp3"
                hide ian
                show v2_ianwrite
                with short
                "I forced myself to sit down and work. A professional writer doesn't sit idle and wait for inspiration."
                if ian_wits < 8:
                    call xp_up('wits') from _call_xp_up_574
## OFFICE
    else:
        label gallery_CH08_S05:
            if _in_replay:
                call setup_CH08_S05 from _call_setup_CH08_S05

        scene magazine with long
        $ fian = "n"
        show ian with short
        "As usual, I went to the office first thing in the morning."
        "Thankfully, it was finally Friday."
        if ian_minerva_sex:
            $ minerva_look = 3
            $ fminerva = "mad"
            "I was about to sit at my desk when I saw Minerva arriving..."
            show ian at lef3 with move
            show minerva at rig with short
            "She was sporting a dress I had never seen her wearing before. She was looking good..."
            "When Minerva walked by me our eyes met."
            menu:
                "{image=icon_lust.webp}Touch her ass" if ian_lust > 5 and v7_minerva_sex:
                    $ renpy.block_rollback()
                    $ v8_minerva_flirt = 3
                    $ fian = "confident"
                    show ian at lef with move
                    "I got closer to her and, after making sure nobody was looking, I gave her ass a good squeeze."
                    $ fminerva = "furious"
                    play sound "sfx/slap.mp3"
                    mi "What the hell do you think you're doing!?" with vpunch
                    call friend_xp('minerva', -1) from _call_friend_xp_747
                    $ fian = "n"
                    "Not the reaction I was expecting."
                    $ fian = "serious"
                    i "Now you're acting all prudish?"
                    mi "Shut your mouth. Now's not the moment."
                    play sound "sfx/door_slam.mp3"
                    hide minerva with vpunch
                    "She turned around and slammed shut the door to her office."
                    i "Jeez, seems someone's in a pissy mood this morning."

                "Compliment her":
                    $ renpy.block_rollback()
                    $ v8_minerva_flirt = 1
                    $ fian = "smile"
                    show ian at lef with move
                    i "That's a nice outfit. It suits you."
                    "Minerva looked at me with a sour expression and disappeared into her office without answering back."
                    $ fian = "n"
                    play sound "sfx/door_slam.mp3"
                    hide minerva with vpunch
                    i "Not the reaction I was expecting... Seems someone is in a bad mood this morning."

                "Tease her":
                    $ renpy.block_rollback()
                    $ v8_minerva_flirt = 2
                    $ fian = "confident"
                    show ian at lef with move
                    i "I'm glad to see you've finally done away with that horrendous scarf and decided to dress properly..."
                    $ fminerva = "furious"
                    mi "Did I ask for your opinion?"
                    play sound "sfx/door_slam.mp3"
                    hide minerva with vpunch
                    $ fian = "n"
                    "Minerva shut the door to her office without giving me time to answer back."
                    call friend_xp('minerva', -1) from _call_friend_xp_748
                    $ fian = "serious"
                    i "Seems someone's in a pissy mood this morning."

                "...":
                    $ renpy.block_rollback()
                    "I decided to keep my mouth shut and steer clear of her."
                    "She had an angry and sour look on her face, and I knew what that meant."
                    play sound "sfx/door_slam.mp3"
                    hide minerva with vpunch
                    "The way she closed shut the door to her office only confirmed my assessment."
                    show ian at lef with move
                    i "Seems someone is in a bad mood this morning."
        if ian_holly_dating:
            if ian_minerva_sex == False:
                show ian at lef with move
            $ fholly = "happyshy"
            show holly2 at rig with short
            h "Good morning!"
            $ fian = "smile"
            i "Good morning, Holly."
            "Seeing her smile really brightened up my day. Sharing the office with her was the only good thing about working at the magazine."
            if ian_lena_dating:
                i "Have you decided if you're coming to tonight's concert?"
                $ fholly = "blush"
                hide holly2
                show holly3 at rig
                with short
                h "Yes, I will... But I'm a bit nervous."
                i "Don't worry. As I already told you, Lena's not mad at you at all."
                i "Besides, I'll be right there with you if you need me."
                $ fholly = "shy"
                h "Thanks..."
            else:
                i "Do you want us to go together to tonight's concert?"
                $ fholly = "smile"
                h "I have a meeting with my publisher this afternoon, so I might be a bit late..."
                i "Oh, okay. I'll see you there, then."
                $ fholly = "happy"
                h "Sure thing!"
        elif ian_lena_dating and v7_holly_kiss:
            if ian_minerva_sex == False:
                show ian at lef with move
            $ fholly = "n"
            show holly2 at rig with short
            h "Good morning..."
            $ fian = "smile"
            i "Oh, good morning, Holly."
            i "Have you decided if you're coming to tonight's concert?"
            $ fholly = "blush"
            hide holly2
            show holly3 at rig
            with short
            h "Yes, I will... But I'm a bit nervous."
            i "Don't worry. As I already told you, Lena's not mad at you at all."
            $ fholly = "n"
            h "I'll take your word for it."
            i "You can trust me on this one... I'll see you there tonight, then."
            h "Yeah."
        scene magazine with long
        "I continued working on my tasks during the rest of the morning."

##VICTOR WRITES #########################################################
    if ian_victor_agenda == True or ian_switch_review or ian_honest_review:
        $ ian_job_victor = True
        if ian_job_magazine < 2:
            $ fian = "n"
            if v7_effort_job > 0:
                scene street with long
                "After making several deliveries I took a pause to eat some lunch myself."
            else:
                scene ianhome with long
                "After a few hours in front of the computer, I decided to take a lunch break."
            show ian with short
            play sound "sfx/ring.mp3"
            "It was then that I got an unexpected call."
        else:
            $ fian = "n"
            play sound "sfx/ring.mp3"
            "My coffee break was interrupted by an unexpected call."
        hide ian
        show ian_phone
        with short
        i "Yes?"
        show phone_victor at lef3 with short
        if ian_victor_agenda == True:
            vi "Hey there!"
            $ fian = "smile"
            i "Hey! What's up, Victor?"
            "I had been waiting for his call, and I wasn't sure if I would be getting it. I was immediately thrilled."
            vi "We're preparing the first issue of our magazine, as we discussed at the fair, and we're trying to fill in the spots for collaborators."
            vi "You're still interested?"
            i "Of course! I'm definitely in."
            i "What do you need?"
            vi "We will be publishing articles, book reviews, and short stories. Can you provide us with any?"
            $ fian = "n"
            i "Right now I'm working on a book, so I don't have any short stories, and I'm not exactly an article writer..."
            if ian_honest_review:
                $ fian = "smile"
                i "But I have one of those satirical reviews I'm writing you could use."
                hide phone_victor
                show phone_victor_smile at lef3
                vi "Yeah, I love those! I was thinking about adding a column with your reviews in the magazine."
                $ fian = "happy"
                "A column all for myself in a magazine? I didn't think twice before jumping in."
                i "I'll send you guys the latest review I've been working on as soon as I'm done with it."
                i "And feel free to suggest any books you feel could be fun for me to write a satire about."
                hide phone_victor_smile
                show phone_victor at lef3
                vi "I have a couple in mind already! Glad to have you on board."
            else:
                $ fian = "smile"
                if ian_job_magazine == 0:
                    i "But I could write some reviews for your magazine, same as I was doing for the one I was working for before."
                else:
                    i "But I could write some reviews for your magazine, same as I'm doing for the one I'm working for now."
                i "Only, this time they'd be one hundred percent honest."
                hide phone_victor
                show phone_victor_smile at lef3
                vi "Interesting..."
                vi "I've been thinking about our conversation and I think you and I see things in a similar way. We're both against those bullshit doctored reviews..."
                vi "I'd like to have a column in our magazine dedicated to reviewing bad books that are currently being published, in a critical, satirical way."
                $ fian = "happy"
                "I could get a column all for myself in a magazine? I didn't think twice before jumping in."
                if ian_switch_review:
                    i "Turns out I've already written a couple of those!"
                    vi "Is that so? See, we really think alike!"
                    vi "Send those my way, and I also have in mind a couple of books that deserve that treatment..."
                else:
                    i "That sounds pretty fun. It's something I could do."
                    vi "Nice! I already have several books in mind..."
                i "Let me know and I'll get to writing."
                vi "Good! Glad to have you on board!"
            i "Thank you for the opportunity."
            vi "My pleasure! Let's keep in touch, Ian."
            vi "Have a good one!"
            $ fian = "smile"
            hide phone_victor
            hide phone_victor_smile
            hide ian_phone
            show ian
            with short
            "I was feeling pretty pumped about this."
            "There wasn't any money involved in it so far, but for the first time I was managing to sneak into the literary world, and with Victor White, no less."
            i "Maybe things are starting to take a turn for me..."
        elif ian_switch_review or ian_honest_review:
            guy "Hey there!"
            i "Who's this?"
            if ian_switch_review:
                guy "Holly Watson gave me your contact info. I'm Victor White."
                $ fian = "surprise"
                "Victor White, the author of the Delbaeth Saga!"
                $ ian_victor_agenda = True
                $ fian = "happy"
                i "Nice to meet you! I'm a big fan!"
                vi "I know, judging by that review you wrote, ha ha! Man, you were too gracious."
                call friend_xp('victor', 1) from _call_friend_xp_749
                "Holly told me she talked to him during one of those book fairs, and she gave him my number... I wasn't really sure if I'd get that call, but here it was!"
                $ fian = "smile"
                i "It was an honest review... which I guess it's hard to come by these days."
                vi "That's exactly right. Holly told me you're the one who actually wrote it, even though it was attributed to her."
                $ fian = "n"
                i "Yeah... Well, sadly things are not too democratic at our magazine."
                vi "I've heard a bit about the practices that go around that place. Sadly, it's like that in most magazines."
            else:
                guy "I'm Victor White. I got your contact info from Holly Watson..."
                $ fian = "surprise"
                $ ian_victor_agenda = True
                "What the hell?"
                "I was on the phone with Victor White, the reputed author of the Delbaeth Saga!"
                $ fian = "happy"
                i "Nice to meet you! I'm a big fan, and I really enjoyed your last book..."
                i "This is a surprise. Holly didn't mention anything...!"
                vi "She didn't? Well, it came up a bit unexpectedly while we were talking about stuff."
                vi "I mentioned I had read these hilarious satirical reviews and she said she knew the guy who was writing them."
                i "Oh, wow."
                i "That'd be me, yeah. What a coincidence."
                hide phone_victor
                show phone_victor_smile at lef3
                vi "Indeed!"
                vi "A few people sent that \"Poker Love\" review you wrote to me. It cracked me up, and it was so true."
                "I had no idea Victor White knew about those reviews I was posting! Doing so had been worth it...!"
                hide phone_victor_smile
                show phone_victor at lef3
                vi "I like your tone, man. It's acid as fuck and paints a true picture of what's going on in our industry nowadays."
            vi "That's why a few people and I are launching our own magazine, one with an honest voice."
            if ian_switch_review:
                vi "Going back to that review you wrote, I really liked it. And not only because you praised the hell out of me!"
                vi "I can see you have a knack for writing yourself and spotting the deeper meaning encoded in stories..."
                i "Yeah, I'm an aspiring writer myself. I can only dream of being in your position, or Holly's, but I'm trying."
                vi "Glad to hear! If that's the case, would you like to participate in our magazine?"
                vi "We can't really pay at this moment, but if you're interested in collaborating, we'd love to publish one of your reviews."
            else:
                vi "I've heard you're an aspiring writer, and we're looking for collaborators..."
                vi "We can't really hire people right now, but if you're interested in participating, we'd love to publish one of your reviews."
            $ fian = "surprise"
            i "What? For real?"
            if ian_switch_review:
                vi "Sure! We will be publishing articles, book reviews, and short stories. Can you provide us with any?"
                $ fian = "n"
                i "Right now I'm working on a book, so I don't have any short stories, and I'm not exactly an article writer..."
                $ fian = "smile"
                i "But recently I started a blog where I post bad book reviews. They are... quite harsh, so to speak, but in a funny manner, or at least that's the idea."
                hide phone_victor
                show phone_victor_smile at lef3
                vi "I just read one like that a while ago and it cracked me up! It was about this piece of crap called \"Poker Love\"..."
                $ fian = "happy"
                i "Yup, that's the one."
                vi "Wait, you wrote that? What a coincidence!"
                vi "I like your tone, man. It's acid as fuck and paints a true picture of what's going on in our industry nowadays."
                vi "We could add a satire column with your reviews in the magazine, that'd be fun."
                $ fian = "happy"
            else:
                vi "Sure! We will be publishing articles, book reviews, and short stories."
                hide phone_victor
                show phone_victor_smile at lef3
                vi "We could even add a satire column with your reviews in the magazine, that'd be fun!"
                $ fian = "happy"
            "No way... I could get a column all for myself in a magazine?"
            "I didn't think twice before jumping in."
            i "I'll send you guys the review I've been working on as soon as I'm done with it."
            i "And feel free to suggest any books you feel could be fun for me to write a satire about."
            hide phone_victor_smile
            show phone_victor at lef3
            vi "I have a couple in mind already! Glad to have you on board."
            i "Thank you for the opportunity."
            vi "My pleasure! Let's keep in touch, Ian."
            vi "Have a good one!"
            $ fian = "smile"
            hide phone_victor
            hide phone_victor_smile
            hide ian_phone
            show ian
            with short
            "I was feeling really pumped about this. Finally, some good news..."
            "There wasn't any money involved in it so far, but for the first time I was managing to sneak into the literary world, and with Victor White, no less."
            i "This looks like a big step in the right direction..."

## MINERVA SEX ###############################################################################################################################################################
    if ian_minerva_sex:
        $ fian = "n"
        scene magazine with long
        "It was finally time to go home. People started leaving the magazine, but Minerva remained inside her office."
        show ian with short
        "Her door had remained shut since this morning..."
        menu:
            "{image=icon_lust.webp}Enter Minerva's office":
                $ renpy.block_rollback()
                $ v8_minerva_sex = True

            "Go home":
                $ renpy.block_rollback()
                i "She seemed to be in an awful mood today. I don't want to deal with that."
                i "It's better if I keep my involvement with Minerva to a minimum..."
                "I picked up my things and decided to go home. I could use some free time before going to Lena's concert."
                hide ian with short
                $ renpy.end_replay()
                jump v8ianfriday

        stop music fadeout 2.0
        show ian at lef3 with move
        "I got up and opened the door to her office."
        play sound "sfx/door.mp3"
        $ fminerva = "mad"
        show minerva at rig with short
        "Minerva seemed to be focused on work. She lifted her eyes up from her computer to glance at me."
        mi "What do you want?"
        "I closed the door behind me and got closer to her desk."
        show ian at lef with move
        if v8_minerva_flirt == 3:
            $ fian = "serious"
            i "You were pretty rude this morning..."
            $ fminerva = "furious"
            mi "How dare you put your hands on me in the workplace? Do you want us to get found out?"
            i "If I recall correctly it was you who first put your hands on me at the workplace."
            $ fminerva = "mad"
            mi "Yeah, when there was no one who could see."
            $ fian = "confident"
            i "I'm not stupid. I made sure nobody was looking. I'm not gonna give up our little secret, don't worry."
        elif v8_minerva_flirt == 2:
            $ fian = "serious"
            i "You were pretty rude this morning..."
            mi "Me, rude? Do you think what you said to me was even barely appropriate?"
            $ fian = "confident"
            i "I've said and done plenty of inappropriate things to you, and you didn't seem to mind."
            i "In fact, I'd say you rather enjoyed it."
        elif v8_minerva_flirt == 1:
            i "You were pretty rude this morning..."
            mi "What?"
            i "If that's the way you're gonna respond when I give you a compliment I'll stop doing so."
            mi "What were you expecting? A thank you?"
            i "A smile would've been nice."
            mi "I'm not in the mood to be smiling these days."
            $ fian = "confident"
            i "Really? I thought I gave you some reasons to smile last time..."
        else:
            i "You looked rather upset this morning. Is there a problem?"
            mi "Nothing that you should concern yourself with."
            $ fian = "smile"
            i "Good to know."
            mi "Is there something else you want?"
            $ fian = "confident"
            i "I don't know. I felt like spending some time with you."
        "Minerva shut her computer off and stood up, taking a look at the time."
        mi "Unfortunately today I don't have time to play with you. I'm a very busy woman and running this magazine is very demanding."
        "I wasn't gonna let Minerva boss me around."
        play music "music/sex.mp3" loop
        scene v8_minerva1 with long
        pause 1
        "I grabbed her by the waist and decisively pulled her toward me."
        i "I'm sure you didn't pick this dress today for no reason..."
        mi "You think I did it for you? Don't flatter yourself...!"
        "Her tone was defiant, but she didn't try to escape from my embrace. I took the cue."
        "I ran my hand down her waist, grabbing her thigh, and tangled my fingers in her hair, pulling her head backward, gently but firmly."
        "I felt her gasp."
        i "Isn't the reason that you wanted to look good? Because you do."
        mi "Maybe..."
        "I slowly pulled up Minerva's skirt. She wasn't resisting. Quite the opposite, in fact..."
        if v8_minerva_flirt == 3:
            i "I like the way your ass looks in this skirt. I couldn't restrain myself, I had to touch it..."
        elif v8_minerva_flirt == 2:
            i "You really look sexy all in black and without that stupid handkerchief around your neck."
        elif v8_minerva_flirt == 1:
            i "I wasn't lying when I said this outfit suits you. You look sexy in black..."
        else:
            i "I like this new style..."
        "I pressed my hard cock against her ass."
        i "I think you can tell I like it."
        mi "Mhhh... I do..."
        "There was no more defiance in her voice. It had turned smooth and silky..."
        "I dug my hand into her panties, searching for her pussy."
        "My fingers slipped into her slit easily, drawing another gasp from Minerva."
        i "Damn, you're already wet. Are you sure you don't have time to play with me today?"
        mi "I need to attend a stupid school meeting this afternoon..."
        "Minerva contorted her hips as I fingered her, rubbing her ass against my erection."
        i "I'm sure your husband can handle it by himself."
        mi "That's the problem... He's not going. I have to take care of it, as always."
        i "That doesn't sound fair. Is there a reason he can't go?"
        mi "Yes. He's a jackass..."
        $ fian = "n"
        $ fminerva = "mad"
        scene magazine
        show ian at lef
        show minerva at rig
        with short
        "Suddenly, Minerva broke away from my hold."
        show minerva at rig3 with move
        "She walked to her desk and picked up her phone."
        hide minerva
        show minerva2 at rig3
        with short
        i "What are you doing?"
        $ fminerva = "evil"
        mi "Calling my husband and getting a fair deal."
        show minerva2 at rig with move
        $ fian = "evil"
        "While she waited for the tone, Minerva grabbed me by the shirt and had me sit on her chair."
        "This was getting interesting..."
        mihu "{i}Yes? What's up, dear?{/i}"
        "I faintly heard the voice coming out of the speaker."
        $ fminerva = "mad"
        mi "Listen, Arnold. I can't go to the meeting today."
        mihu "{i}What? But I thought you said you'd do it...{/i}"
        scene v8_minerva2 with long
        pause 1
        "Minerva got on her knees and skillfully unzipped my pants with just one hand, making my boner pop out."
        "She grabbed my cock and gave it a quick lick before answering her husband."
        mi "I know, but I have so much work these weeks. I have to stay at the office a bit longer today."
        scene v8_minerva3 with long
        pause 1
        "Minerva wrapped her lips around my cock and began sucking on it. I twitched."
        i "Mhhh... Oh, shit..."
        mihu "{i}Can't it wait? You know I can't go to the school today, I have my weekly book club meeting.{/i}"
        scene v8_minerva2 with long
        mi "And I have to deal with something at the office. What's more important?"
        scene v8_minerva3 with long
        mi "Nhhh..."
        i "Ooff..."
        "Minerva went back to sucking my cock after every sentence. She was looking directly into my eyes as she did so while talking to her husband over the phone..."
        "This situation was totally wicked, which only served to turn me on even more."
        mihu "{i}I can't cancel on them on such short notice...!{/i}"
        mi "Well, I surely can't cancel work! Someone needs to show up at the school meeting, and you're the only one who can, so get going!"
        mihu "{i}This is not what we agreed...{/i}"
        scene v8_minerva4 with vpunch
        play sound "sfx/gag1.mp3"
        pause 1
        mi "Ghhhk...!"
        "As if to respond to her husband's complaint, Minerva impaled herself on my cock, swallowing it deep."
        i "Mhhh, yeah...! Suck it..."
        "Having a woman like Minerva blowing me, acting like a bitch in heat, while talking to her whiny husband, had my blood rushing."
        mihu "{i}Minerva?{/i}"
        scene v8_minerva2 with long
        mi "Will you stop acting like a man-child, for the love of God?"
        mi "That \"book club\" is just an excuse for you and your friends to drink whiskey and smoke cigars, and you do that every Friday."
        mi "So, for once, how about you take some responsibility? Am I asking too much?"
        scene v8_minerva3 with long
        mihu "{i}Don't start with that again! I'm also stressed out, and the book club is my release valve!{/i}"
        scene v8_minerva4 with long
        i "Oh, fuck..."
        mihu "{i}You go to your yoga classes two or three times a week. I think it's me who's not asking for that much.{/i}"
        scene v8_minerva2 with long
        mi "Enough whining. I can't go today and that's how it is."
        mi "So, either you show up at the school meeting or they'll be wondering why our son's parents don't give a fuck about his academic life."
        scene v8_minerva3 with long
        "Minerva ended the call without giving her husband time to reply."
        "I had been at the end of that bloody temper in the past, but how different things were now..."
        "In fact, seeing her acting like a bitch only made me want to fuck her even more!"
        play sound "sfx/ah7.mp3"
        scene v8_minerva5 with vpunch
        pause 1
        i "Come here!"
        "I stood her up forcefully and shoved my cock into her pussy right away."
        "She loved it."
        mi "Fuck yes! Give it to me!"
        mi "Ram your cock up my pussy!"
        "She clasped her hand around my neck, and I did the same on her hair."
        i "I'll fuck you like you deserve to be fucked. I'll fuck you like the slut you are!"
        mi "Yes!!"
        "I slammed my hips against Minerva, penetrating her roughly."
        "Her grip on my neck tightened, as did her moans and pants."
        "My testosterone was running wild, making me feel like the fucking man."
        "It felt so fucking good."
        show v8_minerva5_cum with flash
        i "Ahhh!!!" with vpunch
        "I came, shooting my load inside of Minerva, but I didn't stop."
        "I continued to pound her even harder, tightening my muscles, keeping my blood from rushing out of my cock..."
        mi "Yes, oh yes!!"
        mi "Soil me! Push that cum deep inside my pussy!"
        play sound "sfx/oh2.mp3"
        mi "Ohhhh!!!" with vpunch
        pause 0.7
        with vpunch
        pause 0.7
        with vpunch
        pause 0.7
        if ian_lust < 10:
            call xp_up ('lust') from _call_xp_up_575
        "Minerva came seconds after me, still locked in that lustful and violent embrace."
        scene magazine with long
        $ fian = "confident"
        $ fminerva = "flirt"
        stop music fadeout 2.0
        "When our pleasure died down I pulled out and we rearranged our clothes."
        show ian at lef
        show minerva at rig
        with short
        mi "Thank you. I was in need of that."
        i "Next time try being a bit nicer to me."
        mi "I don't think you like me being nice to you..."
        menu:
            "Yeah, you're right":
                $ renpy.block_rollback()
                $ fian = "serious"
                i "Yeah, you're right about that... The only reason I'm fucking you is that I despise you."
                mi "Serves me just fine, as long I get what I want."
                $ fian = "n"
                i "Whatever you say."

            "{image=icon_friend.webp}You could always try" if ian_minerva > 3:
                $ renpy.block_rollback()
                $ ian_minerva_dating = True
                $ fian = "smile"
                i "You could always try."
                mi "..."
                $ fminerva = "n"
                mi "Maybe I will."

        scene street with long
        "I got out of the office and left Minerva to her devices."
        $ renpy.end_replay()
        $ gallery_unlock_scene("CH08_S05")
        $ fian = "n"
        show ian with short
        "I almost felt bad for what I had just done. Almost."
        "It was clear Minerva's marriage wasn't exactly a happy one. Not that I cared, that was none of my business."
        $ fian = "confident"
        "In fact, I was enjoying quite a bit being the lover, for once... I had never found myself in this situation, and it was pretty exciting."
        if ian_holly_dating:
            $ fian = "n"
            "This relationship with Minerva was something I was interested in keeping secret, though."
            i "Especially now, taking Holly into consideration..."
            i "What would happen if she knew I was banging Minerva in the office?"
            i "I should be more careful from now on..."
        elif ian_lena_dating:
            $ fian = "sad"
            "Was I being a jackass, though? I had fucked Minerva just a few hours before going to Lena's concert..."
            if ian_lena_over:
                $ fian = "n"
                i "It doesn't matter. As much as I hate it, Lena and I are done."
                i "Shagging Minerva was a good way of coping with it, I guess."
            else:
                if ian_lena_love:
                    i "Yeah, I probably am... Even though Lena and I are not officially dating or anything, I think I'd like that scenario coming true."
                    i "But for now... This is in a completely different, private sphere of my life."
                else:
                    $ fian = "n"
                    i "Well, this had nothing to do with her. It's in a completely different, private sphere of my life..."
                i "It shouldn't interfere with things, as long as Minerva and I keep this secret, which we will."
        hide ian with short
        jump v8ianfriday

    else:
        stop music fadeout 2.0
        scene streetnight with long
        "The day went on until it was time for me to go to Lena's concert."
        jump v8ianfriday

##LENA CONCERT ############################################################################################################################################################################################################################################################################################
label v8ianfriday:
    $ fian = "smile"
    $ fholly = "smile"
    $ ian_look = 3
    $ holly_look = 1
    if cafe_music:
        scene cafe_concert with long
        "The café was looking different from any other time I had seen it before."
        "Not only because of the open space and the small stage, but because of the number of clients there."
        "Normally it was a very quiet place, but that night it was brimming with life. Quite a number of people had shown up to see Lena play, her friends included."
    else:
        scene recordstore with long
        "The record store was brimming with people that night, and the atmosphere felt uplifting and positive."
        "Quite a number of people had shown up to see Lena play, her friends included."
    play music "music/ourredstring.mp3" loop
    if cafe_music:
        scene v8_concert_bg1:
            subpixel True
            zoom 1.1
            xalign 1.0
            ease 10 xalign 0.0
            ease 10 xalign 1.0
            repeat
        show v8_concert_emma_cafe:
            subpixel True
            xanchor 0.0
            xpos -0.15
            ease 10 xalign 0.0
            ease 10 xpos -0.15
            repeat
        if lena_look == 4:
            show v8_concert_lena1_cafe:
                subpixel True
                xanchor 1.0
                xpos 0.8
                ease 10 xpos 1.06
                ease 10 xpos 0.8
                repeat
        elif lena_look == 3:
            show v8_concert_lena2_cafe:
                subpixel True
                xanchor 1.0
                xpos 0.8
                ease 10 xpos 1.06
                ease 10 xpos 0.8
                repeat
        elif lena_look == "sexy1":
            show v8_concert_lena3_cafe:
                subpixel True
                xanchor 1.0
                xpos 0.8
                ease 10 xpos 1.06
                ease 10 xpos 0.8
                repeat
        else:
            show v8_concert_lena4_cafe:
                subpixel True
                xanchor 1.0
                xpos 0.8
                ease 10 xpos 1.06
                ease 10 xpos 0.8
                repeat
        if v8_sy:
            show v8_concert_lena_sy:
                subpixel True
                xanchor 1.0
                xpos 0.8
                ease 10 xpos 1.06
                ease 10 xpos 0.8
                repeat
        if v8_choker:
            show v8_concert_lena_choker:
                subpixel True
                xanchor 1.0
                xpos 0.8
                ease 10 xpos 1.06
                ease 10 xpos 0.8
                repeat
    else:
        scene v8_concert_bg2:
            subpixel True
            zoom 1.1
            xalign 1.0
            ease 10 xalign 0.0
            ease 10 xalign 1.0
            repeat
        show v8_concert_emma_store:
            subpixel True
            xanchor 0.0
            xpos -0.15
            ease 10 xalign 0.0
            ease 10 xpos -0.15
            repeat
        if lena_look == 4:
            show v8_concert_lena1_store:
                subpixel True
                xanchor 1.0
                xpos 0.8
                ease 10 xpos 1.06
                ease 10 xpos 0.8
                repeat
        elif lena_look == 3:
            show v8_concert_lena2_store:
                subpixel True
                xanchor 1.0
                xpos 0.8
                ease 10 xpos 1.06
                ease 10 xpos 0.8
                repeat
        elif lena_look == "sexy1":
            show v8_concert_lena3_store:
                subpixel True
                xanchor 1.0
                xpos 0.8
                ease 10 xpos 1.06
                ease 10 xpos 0.8
                repeat
        else:
            show v8_concert_lena4_store:
                subpixel True
                xanchor 1.0
                xpos 0.8
                ease 10 xpos 1.06
                ease 10 xpos 0.8
                repeat
        if v8_sy:
            show v8_concert_lena_sy:
                subpixel True
                xanchor 1.0
                xpos 0.8
                ease 10 xpos 1.06
                ease 10 xpos 0.8
                repeat
        if v8_choker:
            show v8_concert_lena_choker:
                subpixel True
                xanchor 1.0
                xpos 0.8
                ease 10 xpos 1.06
                ease 10 xpos 0.8
                repeat
    with long
    "Despite her initial nervousness, when she started singing Lena seemed to overcome it like it had never been there."
    if ian_lena_dating:
        "Watching her perform was mesmerizing. She was as beautiful as her voice, and her melodies filled the whole store so perfectly."
        if ian_lena_over:
            "I felt a quiet pain in my stomach."
            if ian_lena_love:
                "My heart had been closed shut since Gillian. She had been the first one to make me feel like I could really love someone again..."
            "Lena was unlike any other girl I had ever met. It was unreal how captivating and stunning she was... And I had blown my chance at being with her."
            if ian_holly_dating:
                $ fian = "sad"
                $ fholly = "n"
                if cafe_music:
                    scene cafe_concert
                else:
                    scene recordstore
                show ian at rig
                show holly at lef
                with long
                "But that sacrifice was not without reward. There was someone else I valued."
                "Holly was by my side, watching Lena perform. We both felt a bit awkward about this situation."
                if ian_lena_love:
                    "I couldn't deny my conflicted feelings. Looking at Lena still made my heart swell, and I doubted that feeling would go away anytime soon."
                else:
                    "I couldn't deny my conflicted feelings. I still had a crush on Lena, and I doubted that feeling would go away anytime soon."
                "She and Holly were so different. But I felt a strong attraction to her, too."
                "The cards were on the table and hesitating wouldn't do any good. It was not fair to Holly."
                "While the music continued to play, I discretely held her hand."
                $ fian = "smile"
                $ fholly = "happyshy"
                hide holly
                show holly3 at lef
                with short
                "She looked at me and smiled. She was so cute, her smile so pure and heartfelt..."
                "What pulled me toward Holly was beyond simple physical attraction. She was undeniably cute, even though she was far from being the shining muse Lena was..."
                "But I wanted to get to know Holly better. I wanted us to grow closer and share our inner worlds..."
                "I squeezed Holly's hand tighter."
                "And I wanted to make her shiver and moan like she had that wonderful night at the hotel, many times."
                if cafe_music:
                    scene v8_concert_bg1
                    show v8_concert_emma_cafe
                    if lena_look == 4:
                        show v8_concert_lena1_cafe
                    elif lena_look == 3:
                        show v8_concert_lena2_cafe
                    elif lena_look == "sexy1":
                        show v8_concert_lena3_cafe
                    else:
                        show v8_concert_lena4_cafe
                    if v8_sy:
                        show v8_concert_lena_sy
                    if v8_choker:
                        show v8_concert_lena_choker
                else:
                    scene v8_concert_bg2
                    show v8_concert_emma_store
                    if lena_look == 4:
                        show v8_concert_lena1_store
                    elif lena_look == 3:
                        show v8_concert_lena2_store
                    elif lena_look == "sexy1":
                        show v8_concert_lena3_store
                    else:
                        show v8_concert_lena4_store
                    if v8_sy:
                        show v8_concert_lena_sy
                    if v8_choker:
                        show v8_concert_lena_choker
                with long
            else:
                "I was so stupid..."
        else:
            "No wonder I was falling for her... It was unreal how captivating and stunning she was, and not just physically. Her charm was impossible to resist."
            if ian_lena_love:
                "My heart had been closed shut since Gillian. Lena had been the first one to make me feel like I could really love someone again..."
                "And that made me feel afraid. Could this even work? A girl like Lena and me?"
            else:
                "Looking at her made the crush I had on her become even bigger, dangerously so."
                "Something inside of me wanted to avoid getting emotionally attached to her. To anyone."
                "But another part wanted to get closer to Lena. I didn't want to lose her."
            "I still couldn't believe my luck. How the hell did I manage to get such a perfect girl interested in me?"
            if v7_holly_kiss:
                "I had almost blown my chances at being with her with what happened with Holly. I should've known better..."
            if ian_cherry_dating or ian_alison_dating:
                "Lena knew I had been dating other girls on the side, though. She still wanted to see me, but I wondered how she really felt about that."
                if ian_lena_love:
                    "Had I blown my chances of having a relationship with her already?"
                else:
                    "I didn't want a relationship with Lena, but... What did I really want?"
            if lena_robert_sex:
                if v7_holly_kiss or ian_cherry_dating or ian_alison_dating:
                    "That wasn't my only worry, though. I hated to learn Lena had been dating that douchebag, Robert..."
                else:
                    "Something really irked me, though. I hated to learn Lena had been dating that douchebag, Robert..."
                if lena_robert_over:
                    "She said she had dumped him already, but to think a guy like that had managed to get so close to Lena..."
                else:
                    "Was she still seeing him? While she was dating me, too? What did she see in a guy like that?"
                    "What did she see in me...?"
                "And Robert wasn't the only guy in the mix. The knowledge of Axel actually being Lena's ex-boyfriend had been looming over me since that night."
            else:
                "Something had been in the back of my mind for a while now. The knowledge of Axel actually being Lena's ex-boyfriend weighed on me."
            "I didn't know much about their relationship, but it was clear it wasn't something fleeting. And they seemed were a lot of unresolved issues..."
            if axel_knows_dating:
                "I had witnessed myself how hung up Axel still was on her, to the point of getting aggressive."
            else:
                "And if Ivy was telling the truth, Axel hadn't turned the page on Lena. Far from it."
            "And what about her? Was she really over him?"
            "It was hard feeling at ease going down that line of thought, so I switched that off and focused my attention back on the performance."
    else:
        "Watching her perform was close to mesmerizing. She wasn't only beautiful, but also talented..."
        "It was unreal how captivating and stunning she was. Any guy would be head over heels to date a girl like her."
        "I was trying not to be one of those, but it wasn't an easy task. Not at all."
        "I was happy enough to have met her and gotten this close with her, even if it was just as friends. But it was impossible not to see how incredible Lena was..."
        "Too incredible for a guy like me, probably."
        if ian_holly_dating:
            if cafe_music:
                scene cafe_concert
            else:
                scene recordstore
            show ian at rig
            show holly at lef
            with long
            "Those thoughts were not fair. Not when the person who I really wanted to get close to was standing right next to me."
            "While we watched Lena perform, I discreetly held Holly's hand."
            $ fholly = "happyshy"
            hide holly
            show holly3 at lef
            with short
            "She looked at me and smiled. She was so cute, her smile so pure and heartfelt..."
            "What pulled me toward Holly was beyond simple physical attraction. She was undeniably cute, even though she was far from being the shining muse Lena was..."
            "But I wanted to get to know Holly better. I wanted us to grow closer and share our inner worlds..."
            "I squeezed Holly's hand tighter."
            "And I wanted to make her shiver and moan like she had that wonderful night at the hotel, many times."
            if cafe_music:
                scene v8_concert_bg1
                show v8_concert_emma_cafe
                if lena_look == 4:
                    show v8_concert_lena1_cafe
                elif lena_look == 3:
                    show v8_concert_lena2_cafe
                elif lena_look == "sexy1":
                    show v8_concert_lena3_cafe
                else:
                    show v8_concert_lena4_cafe
                if v8_sy:
                    show v8_concert_lena_sy
                if v8_choker:
                    show v8_concert_lena_choker
            else:
                scene v8_concert_bg2
                show v8_concert_emma_store
                if lena_look == 4:
                    show v8_concert_lena1_store
                elif lena_look == 3:
                    show v8_concert_lena2_store
                elif lena_look == "sexy1":
                    show v8_concert_lena3_store
                else:
                    show v8_concert_lena4_store
                if v8_sy:
                    show v8_concert_lena_sy
                if v8_choker:
                    show v8_concert_lena_choker
            with long
    "Emma seemed to be having a great time playing with Lena, and she complimented her melodies perfectly."
    if v8_song == 1:
        "Lena closed the show with one of her own songs, a heartbreaking ballad that moved everyone in the audience."
    if v8_song == 2:
        "Lena closed the show with one of her own songs, an emotional ballad that had everyone in the audience swaying their heads to the melody."
    "They both were rewarded with applause and ovation. The concert had been a success."
    "And then..."
    $ flena = "surprise"
    $ fian = "surprise"
    $ fseymour = "surprise"
    $ femma = "mad"
    $ seymour_look = "stain"
    stop music
    play sound "sfx/splash.mp3"
    if cafe_music:
        scene cafe_concert
    else:
        scene recordstore
    show ian at left
    show emma at lef3
    show seymour2
    show lena at rig3
    with vpunch
    $ fseymour = "serious"
    "Disaster."
    if cafe_music:
        scene cafe_concert
    else:
        scene recordstore
    with long
    pause 1
    scene street2night with long
    if ian_lena_dating and ian_lena_over == False:
        jump v8lenadate
    elif ian_holly_dating:
        jump v8hollynight
    else:
        jump v8ianfridayend

##HOLLY ####################
label v8hollynight:
    play music "music/hollys_theme.mp3" loop
    $ fian = "n"
    $ fholly = "n"
    show ian at lef
    show holly2 at rig
    with short
    "The event ended and I offered to walk Holly home."
    "She appreciated it, and it was an excuse to spend some time alone with her."
    i "It sucks that the concert ended that way... Emma really jumped the gun there."
    h "Yeah... Mr. Ward looked pretty upset."
    $ fian = "sad"
    i "So did Emma, though... I don't ever remember seeing her that mad."
    i "And I had no idea Mr. Ward had his hand in so many businesses. I always thought he was just a very big publisher..."
    h "I didn't know either. I mean, it is known he is a businessman and entrepreneur aside from his work at Hierofant, but to think he has something to do with Baluart's social problems..."
    $ fian = "n"
    "I didn't know what to make of the situation. Were Emma's accusations really accurate?"
    $ fian = "smile"
    i "Anyway... Other than that, the night was almost perfect. Lena nailed it."
    $ fholly = "happy"
    h "Yes... I'm glad she finally found the courage to share her art with everyone."
    i "She was so nervous about it, and in the end, there was no need for it..."
    $ fholly = "n"
    h "I understand her... I still feel weird when people tell me they read and enjoyed my books."
    h "I can hide behind the pages of my book, but I imagine having to perform live in front of people must be nerve-wracking!"
    if ian_lena_over:
        i "So... Did you get a chance to talk to Lena?"
        $ fholly = "sad"
        hide holly2
        show holly3 at rig
        with short
        h "Not yet... I mean, we talked a bit, but she said she wanted to meet this Sunday to go shopping."
        h "I suppose we'll talk more then."
        i "But you saw she wasn't mad at you, right?"
        $ fholly = "smile"
        h "Yeah... She's too nice..."
        i "So, you're busy on Sunday... What about Saturday?"
        $ fholly = "n"
        h "I have another meeting with my publisher. We're almost done with my new book..."
    else:
        i "I enjoyed watching the concert with you."
        $ fholly = "shy"
        hide holly2
        show holly3 at rig
        with short
        h "Me too... Thanks for holding my hand."
        i "Why thank me?"
        $ fholly = "blush"
        h "I don't know, I... I just appreciated it."
        i "I will do that again, then."
        i "Are you free tomorrow?"
        $ fholly = "n"
        h "Uh... No, I have another meeting with my publisher. We're almost done with my new book..."
        i "And what about Sunday?"
        h "Lena wanted me to go shopping with her and Ivy."
    $ fian = "n"
    i "Oh, I see. You're a busy girl..."
    $ fian = "smile"
    i "I wanted to hang out with you, but we'll need to find another day."
    $ fholly = "shy"
    h "I really want to hang out with you too..."
    h "I'm sure we can find a moment next week... Just for us."
    i "Just for us."
    h "I'll get going... My parents must be waiting for me."
    i "Good night, Holly."
    $ fholly = "blush"
    h "Um..."
    # scene v10_holly1_bg3 # street bg
    # show v10_holly1a # findme - needs blue shirt variant
    "She leaned in and kissed me."
    "I had been waiting for this all night..."
    $ fholly = "happyshy"
    scene street2night
    show holly at rig
    show ian at lef
    with long
    h "Good night!"
    hide holly with short
    show ian at truecenter with move
    i "She's so lovely..."
    $ fian = "shy"
    "I was left feeling something I hadn't felt in a long time..."
    "Were those butterflies in my stomach?"
    i "What's up with me? I'm not a teenager anymore..."
    "I had to admit it did feel good kinda feeling like one again, though..."
    jump v8iansaturday

##ALONE
label v8ianfridayend:
    if ian_cherry_dating and ian_lena_dating == False:
        play music "music/normal_day2.mp3" loop
    $ fian = "n"
    $ fperry = "meh"
    show ian at lef
    show perry at rig
    with short
    "I walked home with Perry after the incident."
    p "Well, that s--{w=0.5}sucked."
    i "It did. Emma really jumped the gun on that one..."
    i "Mr. Ward looked pretty upset."
    p "So did Emma... I don't ever r--{w=0.5}remember seeing her that mad."
    p "That's the guy you were trying t--{w=0.5}to be noticed by, right?"
    i "Yeah."
    p "Seems he's a shady one..."
    i "I had no idea Mr. Ward had his hand in so many businesses. I always thought he was just a very big publisher..."
    p "I don't know, but I'd say Emma m--{w=0.5}made an enemy tonight."
    p "That's why I always say it's better to stay out of p--{w=0.5}politics and stuff... They're only trouble."
    p "I hope this doesn't r--{w=0.5}ruin our plans for tomorrow. I'm r--{w=0.5}really looking forward to it..."
    "I didn't know what to make of the situation. Were Emma's accusations really accurate?"
    jump v8iansaturday

## LENA DATE ###################################################################################################################################################################################################################
label v8lenadate:
    $ fian = "n"
    $ flena = "sad"
    $ flouise = "n"
    $ fstan = "sad"
    play music "music/normal_day2.mp3" loop
    show ian at lef
    show lena at rig
    show louise at right
    if lena_stan > 5:
        show stan at left
    with short
    if lena_stan > 5:
        "The mood was a bit weird as I walked Lena home. Her roommates were coming with us."
        if louise_jeremy == False:
            "I wasn't too acquainted with Louise, but I had the feeling she wasn't too comfortable around me, me being good friends with her ex-boyfriend."
        else:
            "I wasn't too acquainted with Louise, but she seemed to be a rather quiet girl, at least around me."
        "And then there was that other guy, Stan. He was even quieter, and I felt some kind of distrust toward me coming from him."
        "Or was I imagining things?"
    else:
        "The mood was a bit weird as I walked Lena home. Louise was coming with us."
        if louise_jeremy == False:
            "I wasn't too acquainted with her, but I had the feeling she wasn't too comfortable around me, me being good friends with her ex-boyfriend."
        else:
            "I wasn't too acquainted with her, but she seemed to be a rather quiet girl, at least around me."
    "I felt the need to break the ice, so I addressed the elephant in the room."
    i "Well, that sucked. I had no idea Emma was against Mr. Ward..."
    l "Seems she has strong reasons to be..."
    lo "Who was that guy, anyway? Is he some kind of big wig or something?"
    if lena_stan > 5:
        st "He's a big book publisher, if I recall correctly..."
        i "Yeah, head of Hierofant publishing. And judging by what Emma said, he has other businesses, too..."
    else:
        i "He's the owner of Hierofant, one of the major publishers in the country. And judging by what Emma said, he has other businesses, too..."
    lo "Makes sense. Nobody gets that rich by just selling books, right?"
    i "Some people do... At least that's what I think."
    lo "And what's the deal with that man and you, Lena?"
    if v6_axel_pose == 1 or seymour_disposition == 0:
        l "He has been hounding me for a while, wanting to hire me as a model, but we're not on good terms."
        l "He gives me the creeps..."
        lo "He must be really interested in you to hound you like that..."
    elif seymour_disposition > 1:
        l "We met at an exhibition and he hired me to model for him."
        l "He's the best client I've ever had... He's more of a patron, in fact."
        $ flouise = "sad"
        lo "Then it's bad news that a friend of yours attacked him like that..."
    else:
        l "We met at an exhibition and I've posed for him a couple of times..."
        lo "I bet he wasn't happy about that girl attacking him like that..."
    $ flena = "n"
    if v6_axel_pose == 1 or seymour_disposition == 0:
        l "Let's drop that subject, alright? I don't want him to ruin my night."
    elif seymour_disposition > 1:
        l "Can we change the subject? It was an unfortunate event on an otherwise great night."
    else:
        l "Let's change the subject... It was not how I was expecting the night to end, but I'm pretty happy with how things went otherwise."
    $ fian = "smile"
    $ flouise = "smile"
    $ fstan = "n"
    i "That's right. You nailed it tonight."
    lo "See? You didn't need to be so nervous..."
    $ flena = "shy"
    l "I feel a bit stupid now, but I really had no idea of how it would turn out until I started playing."
    i "That's why it's great that you decided to do it. I hope we can go listen to you again many more nights."
    if cafe_music:
        l "Let's not jump the horse. Let's see if this really serves to bring more people to the café..."
        $ flouise = "n"
        lo "I'm sure it will. I knew you were talented, but it's even unfair how gifted you are..."
    else:
        l "Let's not jump the horse. Let's see if the store wants to invite me some other time..."
        $ flouise = "n"
        lo "I'm sure they will. I knew you were talented, but it's even unfair how gifted you are..."
    lo "Why can't I be a bit like you, too?"
    l "You're exaggerating!"
    i "I also sometimes wonder if you're really some kind of alien in disguise..."
    $ flena = "smile"
    l "Shut up!"
    if lena_stan > 5:
        $ fstan = "serious"
        st "Not an alien. An angel..."
        $ fian = "worried"
    "We arrived at their place."
    $ fian = "shy"
    $ flena = "shy"
    i "So..."
    if lena_louise_sex:
        lo "..."
        if lena_stan > 5:
            st "..."
        $ fian = "worried"
        i "... ..."
        lo "... ..."
        if lena_stan > 5:
            st "... ..."
            $ flena = "n"
            l "You guys can go ahead. I'll stay here talking with Ian for a bit."
            $ fstan = "sad"
            lo "Oh, alright..."
            $ flouise = "serious"
            lo "Come on, Stan! Don't bother them!"
            st "Sure..."
            hide louise
            hide stan
            with short
            $ fian = "n"
            i "They don't like leaving you alone, do they?"
            l "I guess they are protective of me or something."
            i "They're very different roommates than Perry."
        else:
            $ flena = "n"
            l "You can go ahead, Louise. I'll stay here talking with Ian for a bit."
            lo "Oh, alright..."
            hide louise with short
            $ fian = "smile"
            i "She doesn't like leaving you alone, does she?"
            l "She can be a bit protective sometimes."
            i "I guess you're lucky to have such a friend."
    else:
        if lena_stan > 5:
            st "..."
            $ flouise = "serious"
            lo "Come on, Stan! Let's get inside, can't you see we're bothering them?"
            $ fstan = "sad"
            st "Uh... okay."
            hide louise
            hide stan
            with short
        else:
            $ flouise = "smile"
            lo "I'll go on ahead and give you lovebirds some intimacy. Good night!"
            hide louise with short
    $ fian = "smile"
    $ flena = "shy"
    l "Finally alone..."
    i "Yeah."
    $ flena = "n"
    l "What a crazy week this one has been. I'm happy it's over..."
    i "Do you have any plans for the weekend?"
    l "I'm going shopping with Ivy and Holly this Sunday, and I guess I'll use Saturday to get some rest. I've been needing it."
    i "Well, if you have the energy and feel like it, tomorrow we're meeting at our place for some beers."
    $ flena = "smile"
    l "Is that an official invitation to hang out with your friends?"
    i "Well, yeah. You already know most of them anyway. And Emma will be there..."
    $ fian = "n"
    i "Maybe you could talk to her about what happened tonight."
    $ flena = "n"
    l "Yeah..."
    $ fian = "smile"
    i "So, are you in?"
    $ flena = "smile"
    l "Sounds good!"
    i "Cool."
    if ian_lena_sex == False:
        $ flena = "shy"
        l "Good night, Ian. See you tomorrow."
        $ fian = "sad"
        i "Oh. Sure..."
        i "Good night."
        "Lena kissed me briefly before getting back home."
        hide lena with short
        $ fian = "worried"
        i "Damn... I was expecting her to invite me over..."
        i "I'm starting to get weird vibes about this. Why haven't we slept together yet?"
        i "I mean, I did eat hear out, but... I was hoping to go all the way..."
        $ fian = "smile"
        i "Well, tomorrow she's dropping by. Maybe then..."
        jump v8iansaturday
    $ flena = "shy"
    l "So... Now that my roommates are probably back in their rooms..."
    l "Wanna go inside?"
    i "I was starting to worry that you wouldn't offer."
    l "Let's go."

    label gallery_CH08_S06:
        if _in_replay:
            call setup_CH08_S06 from _call_setup_CH08_S06

# lola
    stop music fadeout 2.0
    play sound "sfx/door.mp3"
    scene lenaroomnight with long
    show ian at lef3
    show lena2 at rig3
    with short
    "When we entered Lena's bedroom we found someone already on her bed."
    show lola with short
    i "Oh, looks like someone was waiting for you."
    if ian_lena_sex_late:
        "I extended my hand to pet the cat, but stopped just before touching it to let it sniff me out first."
        $ flena = "sad"
        l "Wait...!"
        if v7_cindy_kiss:
            play sound "sfx/cat_angry.mp3"
            $ fian = "surprise"
            hide lola
            show lolamad
            with vpunch
            i "Woah!"
            "Lena's cat hissed at me before jumping off the bed and running away."
            call friend_xp('lola', -1) from _call_friend_xp_750
            $ fian = "worried"
            hide lolamad with short
            l "Sorry, I tried to warn you. Normally she doesn't let people pet her. Especially guys..."
            $ fian = "smile"
            i "I see. She doesn't trust us men... Maybe she's looking out for you in her own way."
            $ flena = "flirt"
            show lena2 at rig
            show ian at lef
            with move
            l "I'd say I'm in good hands right now..."
        else:
            "She looked at me for a second, sniffed my hand for a second time, doubtful, and finally jumped off the bed and ran away."
            hide lola with short
            $ fian = "n"
            i "Oh."
            l "Don't mind her. Normally she doesn't let people pet her."
            l "But at least she didn't hiss at you! She tends to do that to people she doesn't know... Especially guys."
            $ fian = "smile"
            i "I see. She doesn't trust us men... Maybe she's looking out for you in her own way."
            l "She seems to be on the fence about you. Which is a big deal, if you ask me!"
            show ian at lef with move
            i "And what about you? Are you on the fence too, or...?"
            $ flena = "flirt"
            show lena2 at rig with move
            l "No, I'm not."
    else:
        "I had met Lena's cat before. She seemed to be on the fence about me."
        "I tried again."
        i "Hey, Lola..."
        if v7_cindy_kiss:
            play sound "sfx/cat_angry.mp3"
            $ fian = "surprise"
            $ flena = "worried"
            hide lola
            show lolamad
            with vpunch
            i "Woah!"
            "Lena's cat hissed at me before jumping off the bed and running away."
            call friend_xp('lola', -1) from _call_friend_xp_751
            $ fian = "worried"
            hide lolamad with short
            l "Sorry about that. I told you she doesn't usually like guys..."
            $ fian = "n"
            i "Seems her opinion of me has worsened since last time..."
            $ flena = "flirt"
            show lena2 at rig with move
            l "Not mine, though."
            $ fian = "smile"
            show ian at lef with move
            i "That's the one that matters to me..."
        else:
            "She sniffed me out with curiosity once again."
            play sound "sfx/meow.mp3"
            "After a few seconds of doubt, she nudged me with her head, and then jumped off the bed, looked at us one last time, and left."
            call friend_xp('lola', 1) from _call_friend_xp_752
            hide lola with short
            l "Oh, that's rare."
            i "She still left..."
            $ flena = "flirt"
            hide lena2
            show lena at rig3
            with short
            show lena at rig with move
            l "Would you've rather had she stayed?"
            show ian at lef with move
            i "No... It's not her who I want to pet tonight."
    $ ian_lola_agenda = True
# sex starts
    $ lena_satisfaction = 0
    play music "music/sex_bright.mp3" loop
    if lena_look == 3 or lena_look == "sexy1":
        scene v8_lena1c
    elif lena_look == 4:
        scene v8_lena1a
    elif lena_look == "wits":
        scene v8_lena1b
    if v8_sy:
        show v8_lena1_sy
    if v8_choker:
        show v8_lena1_choker
    with long
    pause 1
    "I had been waiting for this moment for far too long."
    "I pulled Lena toward me and kissed her deeply, holding her tight."
    "She wrapped her arms around my neck and locked my head in place, responding with passionate kisses of her own."
    if ian_lena < 6:
        call friend_xp('lena') from _call_friend_xp_753
        $ ian_lena = 6
    "Every time I felt Lena's desire for me, her sweet lips and her ductile, soft tongue, a jolt of adrenaline rushed through my entire body."
    "There was no other place I'd rather be. No other body I wanted to feel and hold."
    if ian_lena_love:
        "She was just so damn lovely...!"
        "And, in this moment, I could feel how perfectly we fitted together, in more than just one sense."
    else:
        "She was just so damn hot!"
        "And, in this moment, we fitted together surprisingly well. In more than just one sense..."
    "Clothes began coming off as our kissing continued."
    if lena_bj > 3:
        scene v8_lena2
        if v8_sy:
            show v8_lena2_sy
        if v8_choker:
            show v8_lena2_choker
        with long
        pause 1
        "To my surprise, Lena got down to her knees, grabbing my cock and blessing it with her tongue right away."
        i "Mhhh...!"
        "I shivered when I felt her wet caresses."
        l "You're so hard..."
        i "I want you, Lena..."
        l "Wait... Let me enjoy myself a bit, first."
        "She continued kissing and licking my cock, taking her time."
        "She looked incredibly beautiful while doing it... and she seemed to be enjoying it even more than I was!"
        "Nobody had shown so much eagerness to suck me off... How lucky was I?"
        if ian_lust < 7:
            call xp_up('lust') from _call_xp_up_576
        "I let Lena continue for several minutes. She didn't seem to be getting tired of it..."
        l "I love sucking on your cock... Mhhh..."
        "I was on cloud nine. But I wanted more. I had been lusting over her too."
    i "Come here."
    scene v8_lena3a
    if lena_piercing1:
        show v8_lena3_p1
    elif lena_piercing2:
        show v8_lena3_p2
    if v8_sy:
        show v8_lena3_sy
    if v8_choker:
        show v8_lena3_choker
    with long
    pause 1
    "I picked her up by the armpits and moved the action to her bed."
    "I got behind her and cupped her perfect breasts. They were so firm and soft, and filled my hands completely..."
    "I felt the hard nipples against my palms as I fondled them while kissing Lena's neck."
    l "Nhaaa... Ian..."
    "I felt her shiver as she ground her ass against my hard cock. She was as horny as me."
    scene v8_lena3b
    if lena_piercing1:
        show v8_lena3_p1
    elif lena_piercing2:
        show v8_lena3_p2
    if v8_sy:
        show v8_lena3_sy
    if v8_choker:
        show v8_lena3_choker
    with long
    pause 1
    "I couldn't wait to feel Lena trembling beneath my touch. My fingers ventured between her legs, caressing her moist slit."
    l "Mhhh...!"
    "She reacted strongly, getting me even hornier."
    "I bit her ear and whispered to her."
    i "You drive me fucking crazy, Lena..."
    l "Oh, yes... I can feel how hard you are..."
    l "I can't wait to feel you inside me, please, Ian..."
    $ config.menu_include_disabled = False
    $ greyed_out_disabled = True
    menu:
        "{image=icon_charisma.webp}Suck my cock first" if lena_bj < 4 and ian_charisma > 4:
            $ renpy.block_rollback()
            $ lena_bj += 1
            "I wanted to fuck Lena, of course, but I wanted something else first, and I wasn't afraid to ask."
            i "I'd love to feel your mouth first. Can you do that for me?"
            l "Of course..."
            scene v8_lena2
            if v8_sy:
                show v8_lena2_sy
            if v8_choker:
                show v8_lena2_choker
            with long
            pause 1
            "Lena got down to her knees, grabbing my cock and blessing it with her tongue right away."
            i "Mhhh...!"
            "I shivered when I felt her wet caresses."
            l "You're so hard..."
            i "Guess who's the culprit."
            "She looked incredibly beautiful while sucking me off..."
            if ian_lust < 7:
                call xp_up('lust') from _call_xp_up_577
            "I enjoyed her mouth for a few minutes, but I didn't want to keep her waiting for too long."
            "And I was eager to be inside her, too!"

        "Let me eat you out first":
            $ renpy.block_rollback()
            if lena_bj > 3:
                i "Not so fast. I want to eat you out, too."
            else:
                i "Not so fast. I've been dying to eat you out again..."
            scene v8_lena4
            if lena_piercing1:
                show v8_lena4_p1
            elif lena_piercing2:
                show v8_lena4_p2
            if v8_sy:
                show v8_lena4_sy
            if v8_choker:
                show v8_lena4_choker
            with long
            pause 1
            "I took the lead, pushing Lena to the bed, and going down between her legs with a smile on my face."
            "She looked at me with desire, biting her lip, and moaned when my lips brushed against her sex."
            "I started licking her softly, building up the intensity slowly."
            "My tongue flicked her clit in alternative motions until I noticed her legs starting to tremble."
            l "Nhhh, Ian...!"
            l "How are you so good at this...?"
            i "It's because I love making you feel good..."
            i "And I love learning how to."
            l "Mhhh, please, don't stop learning then!"
            if ian_wits < 8:
                call xp_up('wits') from _call_xp_up_578
            "I continued with what I was doing. I had never enjoyed eating someone out as much as Lena...!"
            l "Stop, Ian...! You're gonna make me cum...!"
            i "That's the goal."
            l "No, not yet... I want you inside of me!"

        "Have sex with Lena":
            $ renpy.block_rollback()
            "I couldn't wait anymore myself. I was happy to oblige."

    if persistent.include_disabled:
        $ config.menu_include_disabled = True
    $ greyed_out_disabled = False
    scene v8_lena6a
    if v8_sy:
        show v8_lena6_sy
    if v8_choker:
        show v8_lena6_choker
    with long
    pause 1
    "I laid next to Lena and she offered me her back."
    "With her help, I slid my cock inside of her pussy. It was tight but slippery, yearning to receive me."
    l "Ohhh, yes... Slowly... I can feel you so much, you're so hard...!"
    "I drove my hips forwards, penetrating her completely, before starting to rock them back and forth slowly."
    l "I love it when you do it slowly..."
    i "I want to enjoy this... And I have no rush."
    l "Me neither... Enjoy me all you want..."
#anal
    if (v8_sexting_full and v8_lena_sexting == "ian" and v8_stalkfap_dm2 == 2) or (v8_sexting_full and v8_lena_sexting == "ian" and lena_anal > 0):
        $ v8_lena_anal = True
        i "I'm still thinking about that video you sent me yesterday..."
        l "Oh, yes..."
        if lena_anal_first == "ian":
            l "Do you want to do me in the ass again?"
        else:
            l "Do you want to give my ass a try?"
            "That was an offer I could not turn down!"
        i "Fuck yes..."
    elif lena_anal_first == "ian":
        $ v8_lena_anal = True
        l "In fact... I have been thinking we could try anal again..."
        i "You just have to ask."
    elif lena_anal == 2 and lena_lust > 5 and ian_lena > 7:
        $ v8_lena_anal = True
        l "In fact... Would you like to try my ass...?"
        "That was an offer I wasn't expecting...! And one I couldn't turn down, of course."
        i "Fuck yes..."
    if v8_lena_anal:
        l "Let's do it..."
        scene lenaroomnight with long
        "Lena grabbed a bottle of lube from one of her drawers and handed it to me."
        scene v8_lena5
        if v8_sy:
            show v8_lena5_sy
        if v8_choker:
            show v8_lena5_choker
        with long
        pause 1
        if lena_anal_first == "ian":
            "She laid down like the first time we did it, offering her ass to me."
        else:
            "She laid down on the bed, legs ups, offering her ass to me."
            "My heart was racing. I was really going to have anal sex with Lena..."
        "It was a heavenly view... And it was all mine."
        "And not only to look at."
        "I covered my cock with lube and smeared some on Lena's asshole too, before pushing the tip in..."
        scene v8_lena5b
        if v8_sy:
            show v8_lena5_sy
        if v8_choker:
            show v8_lena5_choker
        with long
        play sound "sfx/ah5.mp3"
        l "Ohhh...! Slowly..."
        if lena_anal_first == "ian":
            "It was as tight as the first time... But it swallowed my cock with surprising ease."
            "I pushed it in inch by inch, carefully. The lube was surely helping."
        else:
            if lena_anal == 2:
                "It was tight, but ductile and elastic. It swallowed my cock with surprising ease."
                l "Oh, yes... I love this..."
            else:
                "It was tight, but ductile and elastic. It swallowed my cock without too much difficulty."
                l "Careful... This is the first time I go all the way..."
            "I pushed it in inch by inch, carefully. The lube was surely helping."
        l "Oh, God... Ian, you're so big...!"
        i "Are you okay?"
        if lena_anal == 2:
            l "Yes, it feels good... So good..."
        else:
            l "Yes... So far it feels good..."
            i "It's not painful?"
            l "No... You can try moving slowly..."
        "I began penetrating her, retracting my cock bit by bit, then pushing it back in carefully."
        if lena_anal_first == "ian":
            "It was only the second time I was doing this, and I hoped I could repeat this many more times."
        else:
            "I was really doing it. I was fucking Lena's ass... I felt high as a kite."
        "What an awesome moment. What a great night."
        "How incredibly lucky I was."
        play sound "sfx/ah6.mp3"
        if lena_anal == 2:
            l "Oh, Ian... Your cock feels wonderful... How can you make me feel so good from my ass?"
            l "It's even better than the first time...!"
        else:
            l "I like it... I like how you're fucking me, Ian..."
            l "You're the first to do me like this... It feels even better than I thought...!"
        scene v8_lena6b
        if v8_sy:
            show v8_lena6_sy
        if v8_choker:
            show v8_lena6_choker
        with long
        pause 1
        "When I felt Lena's ass had loosened up enough, I turned her sideways and went back to our previous position without pulling out."
        "My breathing and my pulse were racing, my cock swelling with even more blood..."
        "I wanted to feel her body against me, to grab her, to kiss and bite her..."
        "I wanted to push my cock even deeper!"
        if ian_lust < 8:
            call xp_up('lust') from _call_xp_up_579
        i "You're driving me insane, Lena...! Fuck..."
        jump v8ianlenasexmenu
# no anal
    else:
        "Her words were luring out my most lustful instincts."
        "My breathing and my pulse were racing, my cock swelling with even more blood..."
        "I wanted to feel her body against me, to grab her, to kiss and bite her..."
        "I wanted to push my cock even deeper!"
        if ian_lust < 6:
            call xp_up('lust') from _call_xp_up_580
        i "You're driving me insane, Lena...! Fuck..."
    menu v8ianlenasexmenu:
        "{image=icon_love.webp}Fuck her softly" if ian_lena_love:
            $ renpy.block_rollback()
            if lena_satisfaction < 1:
                $ lena_satisfaction = 1
            stop music fadeout 2.0
            "Insane, yeah. The worst kind of insane."
            play music "music/sex_good.mp3" loop
            if v8_lena_anal:
                scene v8_lena9b
            else:
                scene v8_lena9
            if lena_piercing1:
                show v8_lena9_p1
            elif lena_piercing2:
                show v8_lena9_p2
            if v8_sy:
                show v8_lena9_sy
            if v8_choker:
                show v8_lena9_choker
            with long
            pause 1
            "And yet, the best kind, too..."
            l "Mhhh...!"
            "I made Lena turn her head so I could kiss her. Deeply."
            if v8_lena_anal:
                "As deep as my cock was going inside her ass. I pushed it as far as it would go, slowly, keeping it there."
            else:
                "As deep as my cock was going inside her pussy. I pushed it as far as it would go, slowly, keeping it there."
            "I felt her insides pulsating around me. Squeezing me warmly, welcoming."
            "I contracted my pelvic muscles, sending more blood to my penis, making it throb and swell while buried in Lena's most intimate spot. She shivered."
            "But my main focus was on our kiss. Our lips, breaths, and tongues fusing together in a lustful dance, drenched in desire..."
            "I began sliding my cock in and out again, delighting myself with the sensation, without a rush. It felt incredible..."
            "And I wasn't the only one feeling this way."
            play sound "sfx/ah3.mp3"
            l "Oh, Ian...! Don't stop, please...!"
            if v8_lena_anal:
                l "I don't know what you're doing, but I'm melting!"
            else:
                l "That's the spot, Ian! You're making me melt..."
            if ian_wits < 8:
                call xp_up('wits') from _call_xp_up_581
            l "I feel I could cum anytime...!"
            i "Me too... You feel so good, Lena!"
            if v8_lena_anal:
                "My cock slid in and out of Lena's lubed-up asshole without trouble now, receiving the pleasurable squeeze of its elastic tightness."
            else:
                "I felt Lena's hot pussy contracting, squeezing me delightfully."
            "We were both spiraling down the throes of passion, drunk on each other. Drunk on pleasure."
            l "If you cum, I'll follow you! I'll...!"
            i "I love it...! I love you...!"
            i "Ahhhh!!!" with vpunch
            pause 0.5
            play sound "sfx/orgasm1.mp3"
            if lena_ian_love:
                l "Yes, oh yes!!"
                l "I love you too!!" with vpunch
            else:
                l "Yes, yeeesss!!!" with vpunch
            pause 0.7
            with vpunch
            pause 0.7
            with vpunch
            pause 0.7
            if v8_lena_anal:
                "I shot my load inside Lena's ass, thrusting deep with every jet of cum."
            else:
                "I shot my load inside Lena's pussy, thrusting deep with every jet of cum."
            if lena_ian_love:
                l "Oh, yes...! Fill me up, please, Ian...!"
            else:
                l "Yes, yes...! I can feel you filling me up...!"

        "{image=icon_lust.webp}Fuck her hard" if ian_lust > 5 or ian_lena_dom > 0:
            $ renpy.block_rollback()
            $ ian_lena_dom += 1
            if lena_satisfaction < 2:
                $ lena_satisfaction = 2
            stop music fadeout 2.0
            play music "music/sex_deep.mp3" loop
            if ian_dirty_talk > 1:
                "If I had to judge from my previous encounters with Lena, she liked it rough."
                "And that was exactly how I felt like giving it to her."
            else:
                "I was so amped up...! I wanted to give it to her hard. I needed to."
            scene v8_lena7
            if v8_sy:
                show v8_lena7_sy
            if v8_choker:
                show v8_lena7_choker
            with long
            pause 1
            play sound "sfx/oh1.mp3"
            l "Oh! Ian...!"
            "I got on my knees and started fucking Lena from behind, increasing the rhythm of my hips."
            if v8_lena_anal:
                l "Ahhh...! You're tearing my ass apart...!"
                "Despite her words, her voice didn't convey complaints. In fact, her moans of pleasure became even more intense."
            else:
                "My cock was drenched with her juices, sliding in and out of her pussy fiercely."
                l "Oh yes... Oh yes, Ian!"
            "My fingers clamped down into Lena's soft flesh, grabbing her with all my strength. I wanted to feel in control."
            "To let her know I was in control."
            if ian_charisma < 8:
                call xp_up('charisma') from _call_xp_up_582
            if ian_dirty_talk == 2:
                "I knew Lena liked it this way. She was out of her mind when I fucked her like this last time."
            else:
                "I wasn't sure if this was the way Lena liked it, but my doubts were being dispelled..."
            l "Your cock's so deep inside me... I can feel it so perfectly...!"
            "I groaned into Lena's ear, letting her know I was feeling it too."
            if v8_lena_anal:
                i "I want it, Lena. I want your ass to be all mine."
                i "All your body, to be just mine."
            else:
                i "I want it, Lena. I want your body to be all mine."
            play sound "sfx/ah2.mp3"
            "She suddenly trembled and moaned. Seems I had struck a nerve."
            l "Ahhh...! Yes, all yours...!"
            i "Mine... To fuck just like so."
            play sound "sfx/orgasm1.mp3"
            l "Yes!! Ohhh, yessss!!" with vpunch
            pause 0.5
            with vpunch
            pause 0.5
            with vpunch
            pause 0.5
            if v8_lena_anal:
                "I felt her whole body tremble, struck by an orgasm, her ass tightening around my cock even more."
            else:
                "I felt her whole body tremble, struck by an orgasm, her pussy tightening around my cock even more."
            "But I wasn't done with her yet."
            scene v8_lena8
            if v8_sy:
                show v8_lena8_sy
            if v8_choker:
                show v8_lena8_choker
            with long
            pause 1
            "My thrusts became heavier, pushing Lena's body forward until she was laying on her belly."
            "My hands grabbed her even tighter, mercilessly, even. One of my fingers found its way into her whimpering mouth."
            play sound "sfx/ah4.mp3"
            l "Ohhhh!! Mhhh!!!" with vpunch
            "Lena convulsed again, almost violently."
            "We were both spiraling down the throes of lust, drunk on each other. Drunk on pleasure."
            "My finger was muffling her moans, but they were loud nonetheless."
            "Possessed by the orgasm, she dug her teeth into it so hard I was worried she would bite it off, but I didn't care."
            if v8_lena_anal:
                "I was going hard on her ass, and that seemed to be driving her crazy. My thrusts had dilated it enough for my cock to slide in and out without trouble."
            else:
                "I continued shoving my cock into her, stimulating that spot that was driving her crazy."
            l "Nhaaah!!" with vpunch
            if v8_lena_anal:
                "She continued to moan and tremble, her tight asshole clenching around my shaft, both her mind and body lost in a pelting stream of pleasure."
                "It was awesome... I had never been with anybody who came like that from anal sex. So damn hot...!"
                "Her pulsating anus and delirious moans were too much for me. They drove me right past the brink of desire and excitement."
            else:
                "She continued to moan and tremble, her pussy clenching around my shaft, both her mind and body lost in a pelting stream of pleasure."
                "Her pulsating pussy and delirious moans were too much for me. They drove me right past the brink of desire and excitement."
            i "I'm cumming too, Lena...!"
            with flash
            i "Ahhhh!!!" with vpunch
            pause 0.7
            with vpunch
            pause 0.7
            with vpunch
            pause 0.7
            if v8_lena_anal:
                "I shot my load inside Lena's ass, thrusting deep with every jet of cum."
            else:
                "I shot my load inside Lena's pussy, thrusting deep with every jet of cum."
            l "Yes, yes...! I can feel you filling me up...!"
            "This time it was her who moved her hips to the rhythm of my throbbing cock, squeezing out every last drop of my seed."
            l "So hot..."

        "Keep fucking her like this":
            $ renpy.block_rollback()
            $ lena_satisfaction = 0
            if v8_lena_anal:
                "My cock slid in and out of Lena's lubed-up asshole without trouble now, receiving the pleasurable squeeze of its elastic tightness."
            else:
                "I felt Lena's hot pussy contracting, squeezing me delightfully."
            "We were both spiraling down the throes of pleasure, drunk on each other."
            if v8_lena_anal:
                l "I don't know what you're doing, but it's so good!"
                l "I feel I could cum anytime...!"
            else:
                l "That's the spot, Ian! Don't stop, please...!"
            i "I'm almost there...!"
            l "Me too! If you cum, I'll follow you! I'll...!"
            i "Ahhhh!!!" with vpunch
            pause 0.5
            play sound "sfx/orgasm1.mp3"
            l "Yes, yeeesss!!!" with vpunch
            pause 0.7
            with vpunch
            pause 0.7
            with vpunch
            pause 0.7
            if v8_lena_anal:
                "I shot my load inside Lena's ass, thrusting deep with every jet of cum."
            else:
                "I shot my load inside Lena's pussy, thrusting deep with every jet of cum."
            l "Ohhh...! I can feel it filling me up..."
#end
    scene v8_lena10
    if v8_lena_anal:
        show v8_lena10_cum2
    else:
        show v8_lena10_cum1
    if v8_sy:
        show v8_lena10_sy
    if v8_choker:
        show v8_lena10_choker
    with long
    pause 1
    "I pulled out slowly and laid next to Lena, embracing her."
    "She was still panting and took several seconds to get her breath back, same as me."
    if lena_satisfaction > 0:
        l "That was incredible... I hadn't had an orgasm like that in ages...!"
    else:
        l "That was so good... {i}Ooof{/i}, what an orgasm...!"
    if ian_lena_love:
        "Hearing those words put a smile on my face. I kissed her cheek lovingly."
    else:
        "Hearing those words put a smile on my face. I kissed her cheek playfully."
    i "I'm glad to see I was not the only one who enjoyed this."
    if lena_satisfaction > 0:
        l "Are you kidding? Gosh..."
    else:
        l "Is there any doubt?"
    if v8_lena_anal:
        l "I never knew anal sex could feel this good... I might get addicted to this."
        i "I'll be happy to provide, in that case!"
    if lena_ian_love:
        l "The way you make love to me... I don't know what it is, but it feels so amazing..."
        i "I'll keep doing it until we find the answer, then."
    else:
        l "I don't know what it is that you do, but having sex with you feels amazing..."
        i "We should keep doing it until we find the answer, then."
    "Lena turned around and kissed me on the lips."
    l "Mhhh... That sounds great..."
    i "It really does."
    stop music fadeout 2.0
    if lena_ian_love:
        scene v7_lena7end
        if v8_sy:
            show v7_lena7end_sy
        if v8_choker:
            show v7_lena7end_choker
        with long
        if ian_lena_love:
            "It felt so good having her in my arms. Her sweet smell, her warm skin, her deep breathing..."
            "We fell asleep with smiles on our faces."
        else:
            "Lena turned over and snuggled against my chest."
            i "Oh, okay..."
            "We fell asleep embracing like that."
    else:
        l "I'm beat... Good night Ian."
        i "Good night."
        scene lenaroomnight with long
        "I was tired too... It didn't take long for us to fall asleep next to each other."
        if ian_lena_love:
            "I wouldn't have minded if she wanted to snuggle a bit before that, though..."
    $ renpy.end_replay()
    $ gallery_unlock_scene("CH08_S06")
## MORNING AFTER

    call calendar(_day="Saturday") from _call_calendar_88

    scene lenaroom with long
    $ lena_look = "ianshirt"
    $ ian_look = 3
    $ lena_necklace = 0
    i "..."
    show lola with short
    i "... ..."
    play sound "sfx/meow.mp3"
    $ fian = "worried"
    $ flena = "smile"
    show lola at rig with move
    show iannude2 at lef with short
    i "Uh..."
    play music "music/girls_day.mp3" loop
    "I woke up and found Lena's cat looking at me with unblinking eyes from the bedside table."
    "Lena was nowhere to be seen."
    play sound "sfx/meow.mp3"
    if v7_cindy_kiss:
        $ flena = "n"
        i "Good morning, Lola... I hope you're not gonna attack me again."
    else:
        $ fian = "smile"
        i "Good morning, Lola... Did you come to wake me up?"
    play sound "sfx/door.mp3"
    show iannude2 at lef3
    show lola at truecenter
    with move
    show lenaunder at rig3 with short
    l "She does that to me every morning."
    "Lena entered the room carrying two cups of coffee."
    hide lola
    hide iannude2
    show ianunder at lef3
    with short
    $ fian = "smile"
    "I put on my boxers and accepted the cup Lena was offering."
    show ianunder at lef
    show lenaunder at rig
    with move
    i "She's probably wondering what a random guy is doing on her bed."
    if lena_ian_love:
        $ flena = "shy"
        l "You're not a random guy..."
        i "I probably am to her."
        $ flena = "smile"
    if v7_cindy_kiss:
        l "She'll warm up to you... I hope."
    else:
        l "She'll warm up to you... Probably."
    i "Maybe I can bribe her with some cat snacks next time, ha ha."
    i "Good morning, by the way... And thank you for the coffee. I didn't notice when you got out of bed."
    l "You were sleeping so peacefully. It would've been a shame to wake you up."
    i "I slept like a log. I see you appropriated my shirt once again..."
    if lena_ian_love:
        $ flena = "shy"
        l "I like it. It smells like you..."
        i "You can keep it, if you like it so much."
    else:
        l "I don't know, I like it. I think it looks cute on me."
        i "It really does. You should keep it."
    $ ian_look = 2
    hide ianunder
    show ian at lef
    with short
    "I put on my clothes while drinking the coffee. It was hot and restorative."
    l "Really? Can I keep it?"
    i "That way I have an excuse to come back and pick it up some other time..."
    $ flena = "flirtshy"
    l "You don't need an excuse to come back. Not after last night..."
    if lena_satisfaction > 1:
        l "God, that was so intense..."
        $ fian = "shy"
        i "Yeah... I hope I didn't overdo it."
        if v8_lena_anal:
            $ flena = "shy"
            l "To be honest, my ass feels quite sore today..."
            $ fian = "blush"
            i "Oh, sorry...! I guess I got a bit carried away..."
            l "Don't worry. It was totally worth it."
            $ fian = "smile"
            "Last night had been crazy awesome. Totally worth it, indeed..."
        else:
            l "It was perfect."
            $ fian = "smile"
            "Last night had been really awesome. Perfect, indeed..."
        i "I'm glad, in that case."
    else:
        if lena_satisfaction == 1:
            l "It was so... perfect."
            $ fian = "shy"
            i "Yes... Yes, it was. It totally was."
        else:
            l "It was... just so good."
            $ fian = "shy"
            i "Yes, it was... It totally was."
        if v8_lena_anal:
            $ flena = "shy"
            l "My ass is a bit sore though if I'm being honest."
            $ fian = "blush"
            i "Oh, sorry...! Did I get too carried away?"
            l "Don't worry... It was totally worth it."
            $ fian = "smile"
            "Last night had been incredible. Totally worth it, indeed..."
    "Lena and I had such good chemistry... I wasn't sure if I had connected with someone on that level sexually before."
    "Emotions and sensations were so heightened when we had sex together. It was something really remarkable."
    i "So... What are you gonna do today?"
    l "Rest! This week has felt like an entire month."
    l "Now that the concert is done I feel like I can breathe again... And I want to enjoy that."
    l "What about you?"
    i "I need to work on my book... I've been pretty busy lately too."
    l "I won't keep you from your duties, then!"
    i "I'll see you tonight, right?"
    $ flena = "shy"
    l "Yeah."
    i "Great. I'm already looking forward to that."
    scene street with long
    show ian with short
    "I kissed Lena goodbye and left for home, knowing I would see her again in several hours."
    if ian_lena_love:
        $ fian = "happy"
        "I felt full of energy and positivity. To think I would get the chance to be with such a dreamy girl..."
    else:
        "I was excited about it. I was really enjoying our current situation..."
    i "You're a lucky guy, Ian."
    play sound "sfx/door_home.mp3"
    scene ianroom with long
    $ fian = "n"
    show ian with short
    "When I got home I prepared to make the best out of the day before tonight's meeting."
    jump v8iansaturday2

# cherry text
label v8iansaturday:
    if (ian_cherry_dating and ian_lena_dating == False) or (ian_cherry_dating and ian_lena_over):
        scene ianroomnight with long
        $ fian = "n"
        $ ian_look = 2
        "I got home and prepared to go to sleep, but before that, I checked my phone."
        show ianunder with short
        "Cherry hadn't given me her answer yet..."
        i "Maybe I should text her."
        nvl clear
        menu:
            "{image=icon_charisma.webp}Tease her with a selfie" if ian_charisma > 4:
                $ renpy.block_rollback()
                $ fian = "smile"
                i "And I can make it fun while I'm at it."
                "I didn't want to insist and look needy or pushy. She had sent me a selfie before, so repaying her was a good excuse to text her."
                show ianunder at left with move
                play sound "sfx/camera.mp3"
                show v8_sexting_ian with short
                $ ian_ian_pics.append("v8_sexting_ian.webp")
                "I got in front of the mirror and snapped a quick pic with a rather goofy attitude. I tried to make myself look good too, of course..."
                i_p "{i}I was trying to take a selfie but I'm not sure this is how it's done...{image=emoji_roll.webp}{/i}"
                i_p "{i}Care to share your professional opinion with me? {image=emoji_crazy.webp}{/i}"
                $ fian = "happy"
                i "I hope she doesn't think I'm a moron, ha ha."
                play sound "sfx/sms.mp3"
                "I got her answer faster than I expected."
                ch_p "{i}You nailed the pose, but you need to work on that facial expression! {image=emoji_laugh.webp}{/i}"
                i_p "{i}So that's it... I knew something was wrong!{/i}"
                ch_p "{i}Other than that, you're looking good {image=emoji_fire.webp} Thank you for the pic {image=emoji_flirt.webp}{/i}"
                call friend_xp('cherry', 1) from _call_friend_xp_754
                $ fian = "smile"
                i_p "{i}I owed you one, didn't I?{/i}"
                ch_p "{i}Is this your way of luring me to come tomorrow?{/i}"
                i_p "{i}You caught me... Smart girl.{/i}"
                ch_p "{i}I can't say no now, can I?{/i}"
                i_p "{i}I'll take that as a yes, then!{/i}"
                ch_p "{i}Yeah {image=emoji_smile.webp}{/i}"
                ch_p "{i}Sorry for not answering sooner, I was busy with some stuff. But I had already decided to come even before you sent me the sexy selfie!{/i}"
                i_p "{i}Damn, it was for naught then!{/i}"
                ch_p "{i}No, it wasn't.{/i}"
                play sound "sfx/sms.mp3"
                hide v8_sexting_ian
                show v8_selfie_cherry
                with short
                $ ian_cherry_pics.append("v8_selfie_cherry.webp")
                $ fian = "shy"
                ch_p "{i}Good night, Ian. See you tomorrow {image=emoji_kiss.webp}{/i}"
                i "Nice!"
                i "She has such a beautiful body..."
                $ fian = "confident"
                i "This Saturday is looking better by the minute..."

            "Text Cherry":
                $ renpy.block_rollback()
                i "I don't want to come across as too pushy or needy..."
                i_p "{i}Hey Cherry! I'll go buy some drinks for tomorrow. Is beer okay or you want something different?{/i}"
                i "This should do it."
                play sound "sfx/sms.mp3"
                "I got her answer faster than I expected."
                ch_p "{i}Hey Ian! Sorry for not answering earlier!{/i}"
                ch_p "{i}Beer is okay, if that's what you guys will be drinking.{/i}"
                $ fian = "smile"
                i_p "{i}I take it you're joining us tomorrow then {image=emoji_crazy.webp}{/i}"
                ch_p "{i}Yeah, I'll be there. Thank you for the invitation {image=emoji_smile.webp}{/i}"
                i_p "{i}Cool. Someone has been looking forward to seeing you again... {image=emoji_roll.webp}{/i}"
                ch_p "{i}Someone has been looking forward to seeing you again too! Good night {image=emoji_kiss.webp} {/i}"
                i "Nice, she said yes..."
                i "I have the feeling tomorrow will be a fun night."

    stop music fadeout 2.0

    call calendar(_day="Saturday") from _call_calendar_89

    scene ianroom with long
    $ ian_look = 2
    $ fian = "smile"
    play music "music/normal_day3.mp3" loop
    "For some reason, the next morning I woke up feeling energized."
    show ian with short
    "I wanted to make the best out of the day before tonight's meeting."
# saturday morning
label v8iansaturday2:
    if ian_job_magazine == 2:
        if v7_effort_weed:
            "I finally had the whole day to myself. I wanted to get some writing done, since the day of the contest was getting close."
            i "Maybe I could roll up a joint and give it a little puff. Might help with inspiration..."
            play sound "sfx/keyboard.mp3"
            scene ianroom
            show v2_ianwrite
            with short
            "I got a bit distracted, but in the end, I managed to concentrate on my writing."
        elif v7_effort_gym:
            "I finally had the whole day to myself. I wanted to get some writing done, but I also wanted to keep up with my training."
            if jiujitsu > 1:
                scene v8_jiujitsu with long
                "I made a trip to the gym, where I rolled with Wen once again."
                if ian_athletics < 10:
                    call xp_up('athletics') from _call_xp_up_583
                "He still kicked my ass, but I was getting some much-needed experience under my belt."
            else:
                scene gym
                show v2_box with long
                "I made a trip to the gym, where I worked the heavy bag until my arms felt heavy and I was soaked in sweat."
                if ian_athletics < 10:
                    call xp_up('athletics') from _call_xp_up_584
                "Bit by bit I was drilling the techniques into my muscle memory, as well as conditioning my body."
            scene ianroom with long
            "After taking a relaxing shower I got back home and sat at my desk, feeling refreshed and ready to work on my book."
            play sound "sfx/keyboard.mp3"
            show v2_ianwrite with short
        else:
            "I finally had the whole day to myself. I wanted to get some writing done, since the day of the contest was getting close."
            "I prepared a big cup of coffee and sat down in front of the computer, eager to work."
            play sound "sfx/keyboard.mp3"
            hide ian
            show v2_ianwrite
            with short
            "At first, I felt a bit of resistance, but I managed to push through. A professional writer doesn't sit idle and wait for inspiration, he just gets to work and finds it."
            if ian_wits < 8:
                call xp_up('wits') from _call_xp_up_585
    else:
        "I had been very busy during the week, with a lot of stuff on my mind. Now I finally had the whole day to myself."
        i "It's time to get some writing done. I have to get past that fucking block that has been making things hard for me these past few days!"
        "I prepared a big cup of coffee and sat down in front of the computer, eager to work."
        play sound "sfx/keyboard.mp3"
        hide ian
        show v2_ianwrite
        with short
        "Thankfully, the words finally flowed through my fingers and I could get into my writing once again."
    if book_scifi:
        i "Let's see... I have almost everything laid out for my {color=#3FB305}Science Fiction{/color} book."
    if book_fantasy:
        i "Let's see... I have almost everything laid out for my {color=#B30505}Fantasy{/color} book."
    if book_historical:
        i "Let's see... I have almost everything laid out for my {color=#D1B402}Historical{/color} book."
    i "I know how the story goes, the call to adventure, who the enemy is, the mentor, and the hardship the main character has to face..."
    i "But there's still a very important dimension I need to work on: the emotional one."
    i "Most stories have a love interest, even when it's not really necessary. That speaks to how important that part of the story is to us humans..."
    i "What kind of love story should I write for my book?"
    label v8_writebookchoice:
        call show_book_screen(6) from _call_show_book_screen_5
        
        if book_card5 == "romantic":
            i "We all love a romantic story. It's what most of us would like to live ourselves..."
        elif book_card5 == "crude":
            i "Love is not always nice. It can be crude and tragic... I know that well."
        elif book_card5 == "metaphysical":
            i "Love is a very abstract concept. We can't even properly define it..."
            i "I don't want to write about just love between two people. It can go deeper than that..."

        menu:
            "Choose this card":
                $ renpy.block_rollback()
                call book_card_choice_6 from _call_book_card_choice_6

            "Try something else":
                $ renpy.block_rollback()
                jump v8_writebookchoice

    play sound "sfx/keyboard.mp3"
    "I spent the day writing. Bit by bit I had been collecting pages, progressing the story, developing the characters..."
    "I could feel the end was not far away now. The book was nearly done..."
    if ian_job_victor:
        "That call from Victor White had really fanned my fire when I needed it the most."
        "Achieving my dreams seemed a bit more doable now, and that fueled me to keep writing with enthusiasm."
        if ian_lena_dating and ian_lena_over == False and ian_lena_sex:
            "The night I had just spent with Lena had a lot to do with that, too."
            "It had me so amped up and optimistic about life in general. Things were finally going well for me..."
    elif ian_lena_dating and ian_lena_over == False and ian_lena_sex:
        "The night I had just spent with Lena had really fanned my fire when I needed it the most."
        "It had me so amped up and optimistic about life in general, and that fueled me to keep writing with enthusiasm."
    stop music fadeout 2.0
    scene ianroomnight with long
    "The day went by just like that, and before I knew it it was time to greet tonight's guests."
## PARTY STARTS #########################################################################################################################################################################################################################################
    $ lena_necklace = 0
    $ ian_look = 2
    $ fian = "smile"
    $ femma = "n"
    $ fperry = "n"
    $ fcherry = "smile"
    play music "music/emmas_theme.mp3" loop
    scene ianhomenight with long
    show ian at lef3
    show emma
    show perry at rig3
    with short
    "Emma was the first to arrive. Perry didn't waste any time and cracked a beer open as soon as she set foot in our apartment."
    play sound "sfx/beer.mp3"
    p "Cheers!"
    e "I see you're in the mood to party tonight!"
    p "Yeah. These are exactly my kind of p--{w=0.5}parties."
    i "Can you call this a party? I'd say it's a simple hangout with a couple of friends."
    p "It's always a party as long as there's enough beer involved."
    e "Is Wade coming?"
    $ fperry = "meh"
    $ fian = "n"
# cindy
    if v7_cindy_kiss or wade_cindy == 2:
        p "Nah... He's s--{w=0.5}spending the weekend with Cindy."
        e "Oh, yeah, I remember seeing the picture she uploaded on Peoplegram today, now that you mention it."
        if v7_cindy_kiss:
            "I hadn't seen that picture. I took out my phone and entered Cindy's profile."
            show ian at left
            show perry at right
            show emma at rig3
            with move
            show v8_cindy with short
            $ fian = "worried"
            i "..."
            "I didn't know how to feel seeing that picture..."
            i "It looks like they're having a good time."
            e "That's good. They had been having some fights recently, right? Seems they're finally patching things up."
            "Were they? After what happened between Cindy and me?"
            if ian_cindy_sex:
                "It was clear Cindy hadn't told Wade about our... {i}slip{/i} the night of his birthday. And judging by what I was seeing it didn't look like she was going to."
                "But to think she was suddenly so invested in her relationship after what happened..."
                "What the hell? Was having sex with me what she needed to re-ignite her relationship with Wade?"
            else:
                "Granted, it was just a kiss, but still... It was still something very serious."
                "It was clear Cindy hadn't told Wade about it, and judging by what I was seeing it didn't look like she was going to."
                "Seems like that kiss had served to push her back to her boyfriend..."
            $ fian = "n"
            "It probably was better this way, but... I couldn't help but feel a stinging and sour disappointment."
            hide v8_cindy with short
            show ian at lef3
            show emma at truecenter
            show perry at rig3
            with move
            e "It sucks they won't come tonight, but I'm glad for them."
            hide perry
            show perry2 at rig3
            with short
            $ fperry = "n"
            p "I don't know. I don't think their relationship will work out."
            $ femma = "sad"
            e "What makes you think that way?"
            p "I can't put my finger on it, but I know Wade. And I've s--{w=0.5}seen how Cindy treats him..."
            p "I wouldn't be able to tolerate that."
            e "I know what you mean, but I guess Cindy is right to ask her boyfriend to put some effort into the relationship."
            e "If they end up breaking up, well... I think the one who stands to lose more from that will be Wade."
            hide perry2
            show perry at rig3
            with short
            p "I don't think so..."
        else:
            $ fian = "smile"
            i "Oh, I've seen it too..."
            show ian at left
            show perry at right
            show emma at rig3
            with move
            show v8_cindy with short
            i "Seems like they're patching things up."
            p "I wonder if that's possible for them."
            $ fian = "n"
            i "What makes you say that?"
            p "I don't know, but I know Wade. And Cindy has been giving him a h--{w=0.5}hard time lately."
            p "I wouldn't be able to tolerate that."
            i "Luckily you're not Wade. Cindy is right to ask her boyfriend to put some effort into the relationship."
            hide perry
            show perry2 at right behind emma
            with short
            p "So you're taking her s--{w=0.5}side?"
            i "I'm not taking sides. They're both my friends and I want things to work out for them."
            e "Me too, especially for Wade. I think he stands to lose the most if he and Cindy end up breaking up."
            i "Yeah."
            hide perry2
            show perry at right behind emma
            with short
            $ fperry = "n"
            p "I'm not sure I see things the same way..."
            hide v8_cindy with short
            show ian at lef3
            show emma at truecenter
            show perry at rig3
            with move
    else:
        p "Nah... He told me he wasn't f--{w=0.5}feeling like it."
        i "No surprises there."
        e "You guys told me he seemed a bit more energetic lately, right?"
        i "Yeah, but that doesn't really amount to much."
        hide perry
        show perry2 at rig3
        with short
        p "He would've come today, if it wasn't for Cindy."
        $ fian = "n"
        i "What makes you say that?"
        p "She's still giving him s--{w=0.5}shit. I think they fought again, and Wade's in no mood to party because of that."
        $ femma = "sad"
        e "That sucks... Seems their relationship is not working out..."
        if wade_cindy == 1:
            i "It's been a bit rough recently, but I thought they could turn things around..."
        else:
            i "It's been falling apart for some time. It's not looking good."
        hide perry2
        show perry at rig3
        with short
        $ fperry = "n"
        p "Honestly, I think breaking up might be the best for Wade. I wouldn't be able to t--{w=0.5}tolerate my girlfriend treating me like Cindy does."
        e "Really? I think the one who stands to lose more from that is probably Wade..."
        p "Why?"
        e "I don't know. Women's intuition, I guess."
        $ fian = "smile"
        i "I didn't know you had that..."
        $ femma = "smile"
        e "Hey!"
    $ fian = "n"
    i "So, Emma... About what happened yesterday..."
    $ femma = "sad"
    $ fperry = "n"
    i "Are you positive Seymour Ward is responsible for all those things you accused him of?"
    $ femma = "serious"
    e "Yeah. Some of his shady dealings have come to light recently. Everyone knows in my circles."
    i "I fail to understand how or why a book publisher would find himself involved in that kind of business..."
    e "He's a company owner first and foremost, and a filthy rich one. He might've started as a publisher, but now he deals in a lot more than just the book business."
    i "Is he doing something illegal, or...?"
    e "Not technically. That's the problem."
    e "He's a vulture, profiting from the current financial situation. This crisis is good for him, so he feeds it."
    e "Regular people lack savings of cash right now. Many small businesses are being forced to sell, but right now very few people can afford to buy, so the prices are down..."
    e "That's when powerful folks like Seymour Ward buy people out of their businesses paying the cheapest price they can get away with, practically extorting them!"
    menu:
        "That's how the market works":
            $ renpy.block_rollback()
            i "That's how the market works, though, ain't it? The law of supply and demand..."
            e "That's what those people want to make you believe. But they're actively manipulating the economy, and regular people are helpless before that."
            e "It affects whole neighborhoods, cities even. When these big funds buy all the businesses and jack up the prices, housing also becomes more expensive..."
            i "Yeah, that's gentrification."
            if ian_wits < 6:
                call xp_up('wits') from _call_xp_up_586
            e "Yeah. They start forcing people out of their homes when they can no longer afford to pay rent, then they buy the properties and make it impossible for working-class people to have a decent life..."
            e "All in pursuit of making even more money, which they don't even need."
            i "Politicians should do something about that, right?"

        "That's a problem":
            $ renpy.block_rollback()
            i "That really is a problem. These kinds of things will only hurt regular people, making the rich even richer."
            e "Exactly! They're actively manipulating the economy, and regular people are helpless before that."
            i "And they're doing this in pursuit of making even more money, which they don't even need."
            if ian_charisma < 6:
                call xp_up('charisma') from _call_xp_up_587
            e "Talk about values, huh? This affects whole neighborhoods, cities even. When these big funds buy all the businesses and jack up the prices, housing also becomes more expensive..."
            e "And then they start forcing people out of their homes when they can no longer afford to pay rent."
            i "Politicians should do something about that, right?"

        "That's none of my business":
            $ renpy.block_rollback()
            i "I don't mean to sound rude, but... I think that's none of my business."
            e "That's the problem... People think this doesn't go with them, but it's the main cause of the current situation!"
            if ian_emma > 3:
                call friend_xp('emma', -1) from _call_friend_xp_755
            i "That's why we have politicians and lawyers, so they can take care of these things."

    e "Most of them are in the pockets of these rich crooks, save a few honest ones, like Perry's father..."
    $ fperry = "serious"
    hide perry
    show perry2 at rig3
    with short
    p "That's enough p--{w=0.5}politics already. We're meeting to have f--{w=0.5}fun!"
    $ femma = "sad"
    e "Sorry, I always get carried away... I didn't mean to kill the mood."
    $ fperry = "meh"
    hide perry2
    show perry at rig3
    with short
    p "It's okay, it's just... I'd rather not discuss my f--{w=0.5}father's work on a Saturday night."
    i "It's me who asked. My bad."
    $ fian = "smile"
    i "Let's change the subject. Emma, you said you had something you wanted to show us?"
    $ femma = "smile"
    e "Oh, yeah! But you'll have to wait a bit more for that!"
## cherry
    play sound "sfx/doorbell.mp3"
    if ian_lena_dating and ian_lena_over == False:
        if ian_cherry_dating:
            i "This must be Lena..."
        else:
            i "I'll go get the door."
    else:
        i "I'll go get the door."
    hide perry
    hide emma
    with short
    show ian at lef with move
    play sound "sfx/door_home.mp3"
    $ fcherry = "n"
    show cherry at rig with long
    if ian_lena_dating and ian_lena_over == False:
        if ian_cherry_dating:
            $ fian = "surprise"
            i "Cherry? What are you doing here?"
            ch "Perry invited me. He didn't tell you?"
            $ fian = "serious"
            i "He failed to mention it..."
            $ fian = "worried"
            "Lena was coming too. This could lead to a very uncomfortable situation..."
            "She didn't know about Cherry and me, and the same went for Cherry. Tonight she would learn about it, though..."
            ch "Is everything alright?"
            i "Oh, yeah...! Come on in, please."
            $ fcherry = "smile"
        else:
            i "Oh, Cherry, here you are. Welcome to our humble place."
            $ fcherry = "smile"
            ch "Thanks for the invitation."
            i "Thank Perry for that."
            if v2_cherry_home:
                $ fian = "n"
                "I couldn't help but feel a bit awkward around Cherry, knowing Lena would show up too."
                "But as Perry said, it should be fine. Our fling was only a one-time thing."
                $ fian = "smile"
    elif ian_cherry_dating:
        i "Hey, Cherry. Glad you decided to come."
        $ fcherry = "smile"
        ch "It can be hard telling no to you..."
        ch "No, but really, I'm happy you invited me. Thanks."
    else:
        i "Oh, Cherry, here you are. Welcome to our humble place."
        $ fcherry = "smile"
        ch "Thanks for the invitation."
        i "Thank Perry for that."
    $ fperry = "smile"
    $ femma = "n"
    show ian at left
    show cherry at lef
    with move
    show perry at rig
    show emma at right
    with short
    p "Hey, Cherry! Long t--{w=0.5}time no see!"
    if ian_lena_dating and ian_lena_over == False and ian_cherry_dating:
        show cherry at left
        show ian at lef
        with move
        hide cherry
        hide emma
        with short
        $ fian = "serious"
        "I moved close to Perry, elbowed him discreetly, and whispered."
        $ fperry = "meh"
        i "What the hell, dude?"
        p "What?"
        i "You invited Cherry? You could've at least told me!"
        p "I thought I did. What's the problem? She's a cool girl, and you have a very good... {i}relationship{/i} with her."
        i "Yeah, that's exactly the problem. I invited Lena tonight."
        p "So what...?"
        $ fperry = "sad"
        p "Oh."
        i "Don't \"oh\" me! You knew she was coming..."
        show emma at right
        show cherry at left
        with short
        e "What are you guys talking about?"
        $ fian = "worried"
        i "It's nothing..."
        "I would need to talk to Cherry before Lena showed up to make sure things didn't blow up in my face."
    ch "I brought some beers and snacks. I didn't know if I had to bring something for dinner..."
    $ fperry = "smile"
    p "No, we ordered p--{w=0.5}pizzas."
    $ fian = "n"
    i "Meaning, he ordered them and I'll be paying for them."
    e "What kind of pizzas did you order?"
    $ fian = "smile"
    p "Chicken with BBQ sauce..."
    p "P--{w=0.5}pepperoni..."
    p "And bacon with goat cheese!"
    $ femma = "sad"
    e "Oh... There's no vegetarian option?"
    $ fperry = "sad"
    $ fcherry = "n"
    $ fian = "n"
    p "You're vegetarian?"
    $ femma = "n"
    e "Yeah."
    p "Since when?"
    e "About two years, I guess?"
    p "What? No way."
    $ fcherry = "smile"
    ch "Ha ha, what friends you are..."
    $ fperry = "meh"
    hide perry
    show perry2 at rig
    with short
    p "Hey, she never mentioned it!"
    ch "You just have to pay attention."
    p "I thought part of being v--{w=0.5}vegetarian is letting the world know on every possible occasion. And trying to convince people to turn vegetarian, too."
    e "I'm not interested in preaching! I just do it because I personally believe in it, but people are free to make their own choices."
    e "And I don't want to fight people every time I see someone eat a burger!"
    menu:
        "You're an activist, though":
            $ renpy.block_rollback()
            i "You're a political activist, though. Protesting against the wrongs of the world..."
            $ femma = "sad"
            e "You're right about that... Am I contradicting myself?"
            e "Maybe I'm a bad vegetarian..."
            ch "I feel your attitude is a great one to have, Emma. You've picked your cause, you don't have to fight all the battles."
            $ femma = "n"
            ch "It's plain to see you're a nice person, and a good vegetarian, if there's such a thing as a {i}bad{/i} one."
            e "Thanks, Cherry!"
            ch "You still need something to eat, though..."

        "That's a great attitude to have":
            $ renpy.block_rollback()
            $ fian = "smile"
            i "Well, I personally think that's a great attitude to have."
            e "Thanks!"
            if ian_emma < 10:
                call friend_xp('emma', 1) from _call_friend_xp_756
            p "Yeah... But now what do we do with all those p--{w=0.5}pizzas?"
            $ fcherry = "happy"
            ch "It might not look like it, but I eat quite a lot... I think we will be able to deal with them, ha ha!"
            $ fcherry = "smile"
            ch "Emma still needs something to eat, though..."

        "We all should turn vegetarian!":
            $ renpy.block_rollback()
            if ian_chad > 0:
                $ ian_chad -= 1
            i "The world would be a better place if we all turned vegetarian, don't you think so?"
            p "What are you yapping on about everybody turning vegetarian? You're gonna eat b--{w=0.5}bacon and chicken."
            $ fian = "worried"
            i "Well, yeah, but I'm trying to cut back on meat. Today's a special day..."
            $ femma = "smile"
            e "Ha ha ha, it's okay!"
            e "People make this choice when they feel it's right and they're ready. And it might not be for everybody."
            ch "You couldn't be more right. You still need something to eat, though..."

    $ fian = "n"
    $ fperry = "n"
    hide perry2
    show perry at rig
    with short
    p "Wait, I'll cook you some eggs. Do you eat eggs?"
    $ femma = "n"
    e "Yeah."
    p "Alright. I'll throw in some avocado and sriracha mayo... Do we have bagels?"
    i "There were a couple left I believe."
    $ femma = "smile"
    e "That sounds tasty! You have a knack for cooking, don't you?"
    if ian_cherry_dating == False:
        menu:
            "{image=icon_charisma.webp}{image=icon_friend.webp}Help Perry out with Cherry" if ian_charisma > 4 and ian_perry > 5 and perry_emma < 2:
                $ renpy.block_rollback()
                $ perry_cherry = True
                $ fian = "smile"
                "I saw the opportunity to help out Perry get closer to Cherry. I knew he was interested in her, despite what he said."
                i "Don't worry, I'll take care of it."
                p "You will?"
                i "Yeah. Why don't you show Cherry some of your drawings meanwhile?"
                i "Those last ones you showed me were really cool."
                p "I don't think she's interested..."
                ch "No, I am. Please, show me."
                $ fperry = "smile"
                p "Oh, okay. Come, I have them in my r--{w=0.5}room..."

            "Let Perry take care of it":
                $ renpy.block_rollback()
                $ perry_cherry = False
                if perry_emma < 2:
                    $ perry_emma += 1
                i "Good idea. It's the least you can do."
                p "Yeah, yeah."
    else:
        $ perry_cherry = False
        if perry_emma < 2:
            $ perry_emma += 1
        $ femma = "smile"
        i "See? We're not such bad friends after all."
        e "You two are the best!"
##EMMA
    if perry_cherry == True:
        hide perry
        hide cherry
        with short
        $ femma = "smile"
        show ian at lef
        show emma at rig
        with move
        e "Hey, I saw what you did there. Nice move!"
        if ian_emma < 12:
            call friend_xp('emma', 1) from _call_friend_xp_757
        $ fian = "happy"
        i "He needs a little push sometimes."
        $ femma = "smile"
        e "So that's the girl you guys told me about, Alison's friend... Wow, she's really stunning!"
        $ fian = "smile"
        i "She is, right? I was impressed the first time I saw her, too... No wonder Perry thinks she's out of his league."
        $ femma = "smile"
        e "The only impossible feat is the one you don't even attempt!"
        i "Now you're sounding like Jeremy..."
        play sound "sfx/beer.mp3"
        "We cracked a couple of beers open and moved to the kitchen to prepare some food Emma could eat."
        $ fian = "n"
        i "I'm still processing what you told me about Seymour Ward. It bummed me out."
        $ femma = "sad"
        e "I'm sorry. It seems my specialty is to bum people out with these things... Especially Perry."
        i "He really hates talking politics, huh?"
        e "I understand why. His relationship with his father is... complicated."
        i "Is it? Mr. Vermeer always seemed like a very polite and reasonable man to me, and I've never known him to fight with Perry."
        e "Perry never talks about it, but he told me about it a couple of times, when he was pretty drunk."
        e "Even if Mr. Vermeer tries to be civil about it, the truth is he's disappointed with his son. And Perry is well aware of it."
        $ fian = "sad"
        i "Well, I can see why... He's made it far in life, mayor of Baluart, no less. And Perry, well..."
        i "Saying he is an underachiever compared to his dad would be an understatement."
        e "He never wanted to follow in his father's footsteps, though, and it seems that was what was expected of him."
        i "I can relate to that, too. I know what it's like to be a disappointment to one's parents."
        $ femma = "n"
        e "I say they should let us be who we want to be and lead the lives we want for ourselves. We're not kids anymore."
        $ fian = "n"
        i "No, we're definitely not. But I never really felt like an {i}adult{/i} yet, if that makes any sense."
        e "I know what you mean... But honestly, I couldn't care less. I'm just me and that's the only way I want things to be."
        $ fian = "smile"
        "We finished cooking Emma's dinner and went back to the living room."
        e "Are those two still in Perry's room? Seems they're really hitting it off."
        i "Yeah. They've been there for a while now."
        if ian_lena_dating and ian_lena_over == False:
            $ femma = "flirt"
            e "Should we leave them be, or maybe take a peek...?"
        elif v7_emma_bj:
            $ fian = "evil"
            i "So, since those two seem to be busy... Maybe we could get busy ourselves, too."
            $ femma = "flirt"
            e "That sounds tempting, actually..."
            $ femma = "smile"
            e "But somebody needs to open the door for the pizzas!"
            $ fian = "happy"
            "I knew getting it on with Emma was way too risky to do at that moment, but it was fun just to talk about it."
            $ femma = "flirt"
            e "Should we leave them be, or maybe take a peek...?"
        else:
            $ femma = "flirt"
            e "Should we leave them be, or maybe take a peek...?"
        play sound "sfx/doorbell.mp3"
## CHERRY
    else:
        hide perry
        hide emma
        with short
        "Emma and Perry went to the kitchen and I was left with Cherry in the living room."
        show ian at lef
        show cherry at rig
        with move
        play sound "sfx/beer.mp3"
        "I handed her a beer and we cracked them open."
        ch "Cheers."
        if v6_ian_drinks == "cherry" and v6_rightaway != "alison":
            i "So, how's that painting exhibition coming along?"
            ch "Oh, you remembered about that?"
            i "Of course."
            ch "It seems it will end up happening, but nothing's on paper yet..."
            ch "But it's good motivation to keep me painting, at least."
        else:
            i "So, how's it been going? We didn't get a chance to talk too much last time..."
            i "Alison told me about you trying to make an art exhibit!"
            $ fcherry = "n"
            ch "I have an acquaintance who told me there's this art gallery that could be interested in displaying my art."
            ch "But I'm not sure it will even happen..."
            i "But if it does, will you invite us?"
            $ fcherry = "smile"
            ch "Sure. I'll let you know."
        $ fian = "smile"
        i "Model, painter, and accountant. That's a peculiar combination."
        ch "I know, right? I'm missing one of three."
        ch "I should be an actress or a musician to score the hat trick, but sadly I can't do any of those."
        i "I'd say you have more than enough talents. You have three professional careers available..."
        $ fcherry = "n"
        ch "Ah, if it were so easy. The only reliable one is accounting, and I really don't want that to be my high point!"
        ch "But making money from the painting is almost impossible, and modeling is not much better..."
        $ fian = "n"
        i "Really? I thought modeling was quite profitable."
        ch "Only if you're a high-profile model and work with the big agencies. Us amateurs are having trouble even booking commissions with the current situation."
        i "I see..."
        i "Have you thought about creating one of those Stalkfap accounts? Seems like lots of models do it these days."
        ch "Nah, that's not for me... I mean, not that there's something wrong with it, but it's just..."
        ch "The stuff people demand and what some girls post, well... I feel it's pretty degrading, for both parties."
        if ian_lust > 5:
            i "I suppose you don't have to do that kind of content if you don't want to. But you could always earn some extra cash."
            if ian_cherry_dating and ian_charisma > 5:
                $ fcherry = "flirt"
                ch "Why are you so interested in me creating a Stalkfap account? Do you want to see naked pictures of me so badly?"
                $ fian = "happy"
                i "Yeah, I do... But I was hoping to get them for free, since I'm a bit of a VIP after all..."
                ch "Well, Mr. VIP, you might get some sexy pics, but I'm not interested in posting them for some extra cash."
                
            else:
                ch "I guess I could, but that's not what I really want to do."     
        else:
            i "I get what you mean... It's not worth the extra cash."
            if ian_cherry_dating and ian_charisma > 5:
                $ fcherry = "flirt"
                ch "Why are you asking, though? Could be you want to see some sexy pictures of me?"
                $ fian = "happy"
                i "Depends. Do I get them for free?"
                ch "Not a chance! But you can pay with something other than money..."
                $ fian = "confident"
                i "Sounds like a deal."
        $ fcherry = "n"
        $ fian = "smile"
        ch "Truth be told, I got into artistic modeling so I could make some money out of it while I studied Fine Arts."
        ch "What I'd really love to make a living out of is my art. Modeling is okay to pursue, but it's not my passion."
        $ fian = "n"
        if lena_passion == "model":
            "She had a lot in common with Lena... But Lena seemed to be way more interested in the modeling stuff."
            "Just like Cindy..."
        elif lena_passion == "music":
            "She sounded exactly like Lena..."
            "I had never realized they were so alike... It was a bit spooky, even."
        else:
            "She had a lot of similarities with Lena... I had never realized they were so alike."
        if ian_lena_dating and ian_lena_over == False and ian_cherry_dating:
            $ fian = "sad"
            "And speaking about Lena, now was the moment to do it. She could show up any moment now..."
            "This would be uncomfortable as fuck, but I had to tell Cherry about the situation before it was too late."
            i "Um, Cherry... I need to talk to you about something."
            $ fcherry = "n"
            ch "What is it?"
            i "I didn't tell you about tonight's plan because, well... I had already invited a girl I've been seeing lately."
            $ fcherry = "sad"
            ch "Oh."
            i "I know this is awkward as fuck, but..."
            $ fcherry = "n"
            ch "Don't worry, I won't do anything that could compromise you."
            $ fian = "n"
            i "I hate asking you this."
            ch "It's okay... I should've told you I was coming. And it's not like you and I are dating or anything of that sort."
            ch "We were pretty clear about it, weren't we?"
            i "We were. Still, it's an uncomfortable situation."
            $ fcherry = "smile"
            ch "I'll deal with it. It's good you told me about it, in fact."
            $ fcherry = "n"
            ch "I just hope you haven't been cheating on her with me..."
            $ fian = "worried"
            i "No, it's not like that...! My relationship with her is similar to the one you and I have."
            ch "Okay, good to know. Though I'd say you two are closer than we are."
            $ fian = "n"
            i "Well, yeah... We have been seeing each other more often."
            $ fcherry = "smile"
            ch "I bet she's that model Alison always talks about."
            i "Yeah, that's the one."
            ch "Well, don't worry. It's under control."
            $ fian = "smile"
            i "Thanks, Cherry. You're being very cool about this."
        $ fcherry = "smile"
        ch "By the way, am I imagining things or does Perry stutter significantly less when he talks to Emma?"
        $ fian = "smile"
        i "Oh, so you noticed? You're very observant..."
        $ fcherry = "happy"
        ch "Oh, so I was indeed right! How curious... Why is that?"
        i "Why do you think it is?"
        ch "He's clearly into her."
        i "Yeah, everybody can see that, except maybe Emma. And he will never admit to it."
        ch "Emma seems like a smart girl. I'm sure she's aware of it..."
        if ian_cherry_dating and ian_lena_dating == False:
            i "By the way, at what time do you plan to leave tonight?"
            $ fcherry = "happy"
            ch "I just arrived and you already want me out?"
            $ fian = "confident"
            i "Quite the opposite. I was going to offer you to spend the night, if that's convenient for you."
            $ fcherry = "flirt"
            ch "I don't know how many beers I'll be drinking tonight, but something tells me it will be more than just a few, so..."
            ch "It does sound convenient, yeah."
            i "Awesome."
            play sound "sfx/doorbell.mp3"
        elif ian_alison_dating:
            $ fcherry = "smile"
            ch "Speaking of which, how's it going with you and Alison? She's always talking about you at the office."
            if ian_alison_like == 2:
                i "Oh, she is?"
                ch "I think having you is helping her. She's been having a rough patch lately."
                $ fian = "n"
                i "Yeah, she's been complaining quite a lot... But no wonder why. She doesn't deserve the bad luck she's been having, with her previous job, her ex and all..."
                ch "But I've seen her in a better mood lately, despite all the stress she's having at work."
                $ fian = "smile"
                i "I'm glad. She wanted to spend a weekend someplace nice with me soon. That could be nice."
                ch "You'd make for a good couple."
                $ fian = "blush"
                i "You think so...?"
                play sound "sfx/doorbell.mp3"
            else:
                $ fian = "worried"
                i "She is?"
                ch "Yeah. I think having you is keeping her mood up. She's been having a rough patch lately."
                i "Yeah, she's been complaining quite a lot... "
                ch "You can't really blame her. She doesn't deserve the bad luck she's been having, with her previous job, her ex and all that."
                ch "I think you'd make for a good couple..."
                play sound "sfx/doorbell.mp3"
                $ fian = "n"
                "The doorbell saved me from having to give an answer."
        else:
            play sound "sfx/doorbell.mp3"
    $ fian = "smile"
    $ femma = "n"
    if ian_lena_dating and ian_lena_over == False:
        i "Oh, that must be Lena!"
        if perry_cherry == False:
            $ fcherry = "sad"
            ch "... Lena?"
    else:
        $ fcherry = "smile"
        i "Oh, the pizzas must be here!"
## LENA
    hide cherry
    hide emma
    with short
    play sound "sfx/door_home.mp3"
    $ flena = "smile"
    $ lena_look = 4
    if ian_lena_dating and ian_lena_over == False:
        "I opened the door for her."
        show lena at rig with short
        l "Hey."
        i "We were waiting for you! Come in."
    else:
        "I opened the door expecting to find the pizza delivery guy, but I was surprised."
        $ fian = "surprise"
        show lena at rig with short
        l "Hey."
        if ian_lena_over and ian_cherry_dating:
            l "Lena...! What are you doing here?"
            $ flena = "worried"
            l "Emma invited me. Didn't she tell you?"
            $ fian = "worried"
            i "No, she didn't..."
            l "So... Can I come in?"
            i "Oh, yes! Of course."
            $ flena = "smile"
            $ fian = "n"
            "I wasn't planning on Cherry and Lena meeting... But now that Lena and I were done, it should be fine, right?"
            "Realistically, it was bound to be pretty uncomfortable all the same."
        else:
            i "Lena! This is a surprise."
            $ fian = "smile"
            i "I didn't know you were coming tonight."
            l "Emma invited me. Didn't she tell you?"
            i "Nope. Come in!"
    if perry_cherry == True:
        $ femma = "smile"
        show lena at rig3
        show ian at lef3
        with move
        show emma with short
        e "Lena, you made it!"
        l "Thanks for the invitation."
        $ femma = "sad"
        e "It's the least I could do after the scene I caused last time. Again, I'm really sorry."
        if ian_lena_dating and ian_lena_over == False:
            i "Seems like everyone wanted you to come tonight, huh?"
            $ femma = "n"
            l "Even Perry? Where is he, by the way?"
            $ femma = "smile"
            e "Oh, he's working on his courtship skills..."
        else:
            i "You could have told me that you had invited her, too."
            $ femma = "smile"
            e "Aren't surprises a nice thing?"
            "Not always, they weren't. And we were about to find out if this was one of those cases."
        stop music fadeout 2.0
        $ fcherry = "happy"
        $ fperry = "smile"
        show lena at truecenter
        show ian at left5
        show emma at rig4
        with move
        play sound "sfx/door.mp3"
        $ flena = "surprise"
        show perry at right5 behind emma
        show cherry at lef2
        with short
        ch "Those drawings were pretty good, indeed!"
        p "I'm glad you liked them! I guess I should draw more..."
        l "Wha--" with vpunch
        $ flena = "mad"
        $ fcherry = "blush"
        $ fian = "worried"
        $ femma = "sad"
        $ fperry = "sad"
        play music "music/danger.mp3" loop
        l "What are you doing here?!"
        ch "Lena...!"
        "Lena looked at me. Her expression had changed dramatically."
        l "Is this some kind of trap?"
        i "Trap? What are you talking about?"
        "I was so confused. What the hell was going on?"
        ch "I didn't know you were coming tonight, Lena. I didn't even know you were friends with these people."
        l "I don't believe a single word that comes out of your mouth. Not anymore."
    else:
        stop music fadeout 2.0
        show lena at rig3
        show ian at lef3
        with move
        $ fcherry = "blush"
        show cherry with short
        ch "Lena..."
        $ flena = "surprise"
        l "Wha--" with vpunch
        $ fian = "worried"
        $ flena = "mad"
        play music "music/danger.mp3" loop
        l "What are you doing here?!"
        "Lena looked at me. Her expression had changed dramatically."
        l "Is this some kind of trap?"
        i "Trap? What are you talking about?"
        ch "I didn't know you were coming tonight, Lena. I didn't even know you were friends with these people."
        l "I don't believe a single word that comes out of your mouth. Not anymore."
        "I was so confused. What the hell was going on?"
        $ femma = "sad"
        $ fperry = "sad"
        show lena at truecenter
        show ian at left5
        show cherry at lef2
        with move
        show perry at right5
        show emma at rig4
        with short
        e "Hey, is everything alright over here?"
    l "I'm leaving."
    hide lena with short
    "Lena turned tail decisively and stormed out."
    $ fian = "surprise"
    i "Lena, wait...!"
## GO AFTER LENA
    stop music fadeout 2.0
    $ fian = "worried"
    if ian_lena_dating:
        "I hesitated before following her, but I couldn't let her leave just like that..."
    else:
        "I looked at my friends. We all had surprised and conflicted looks on our faces."
        i "What the hell just happened?"
        ch "This is my fault..."
        i "You and Lena know each other?"
        ch "Yeah..."
        p "Ian, m--{w=0.5}maybe you should go after Lena and t--{w=0.5}talk to her?"
        e "I feel bad for letting her leave like that... And you're the one who's known her the longest."
        i "Yeah, you're right... I'll go."
    play music "music/melancholy.mp3" loop
    scene street2night with long
    "I rushed to the street and caught up to her."
    $ flena = "serious"
    show ian at lef
    show lena at rig
    with short
    i "Lena, please, wait."
    "She turned around. She looked very upset."
    menu:
        "Why did you storm off like that?":
            $ renpy.block_rollback()
            i "What just happened? Why did you storm off like that?"
            l "Are you sure you don't know?"
            $ fian = "n"
            i "If I did, I wouldn't be asking you."
            $ fian = "sad"
            i "Seems like you and Cherry have some history..."
            l "Yeah, we do. She hasn't told you anything about it?"

        "I don't understand what's happening":
            $ renpy.block_rollback()
            $ fian = "sad"
            i "Lena... I can see you're very upset right now, but I don't understand what just happened."
            i "So please, talk to me..."
            if ian_charisma < 8:
                call xp_up('charisma') from _call_xp_up_588
            $ flena = "sad"
            l "I... That girl is someone I don't even want to share a room with."
            l "I take it she hasn't told you anything about our history?"

        "What's your problem with Cherry?":
            $ renpy.block_rollback()
            i "What was all that about? What's your problem with Cherry?"
            $ flena = "mad"
            l "My problem? What's my problem?"
            call friend_xp('lena', -1) from _call_friend_xp_758
            l "I find it hard to believe you don't know about it."
            $ fian = "sad"
            i "I seriously don't... Seems like you and Cherry have some history..."
            $ flena = "serious"
            l "Yeah, we do. So she hasn't told you anything about it?"

    i "She hasn't... I had no idea you even knew each other."
    l "How do you know her?"
    i "Alison introduced us. They work at the same office."
    $ flena = "sad"
    l "..."
    l "Fuck... It really is a damn small world."
    l "I keep bumping into people I don't want to have anything to do with. And to think you guys are friends with her..."
    l "Have you been friends for long?"
    i "No... Perry and I met her about two months ago. This is the first time she joined us without Alison."
    l "So you met her more or less at the same time you met me."
    $ fian = "worried"
    i "I think so... But what's the issue? What happened between you and Cherry?"
    l "..."
    l "It has to do with the reason Axel and I broke up."
    i "So the girl he was cheating with was Cherry...?"
    $ flena = "serious"
    l "Yeah."
    "I had seen it coming, but it was still surprising."
    menu:
        "{image=icon_friend.webp}You can tell me about it" if ian_lena > 9 or v5_ian_confide:
            $ renpy.block_rollback()
            $ v8_lena_story = True
            $ fian = "n"
            i "You don't have to if you don't feel like it, but if you do... I'm here to listen."
            $ flena = "sad"
            l "..."
            l "I haven't talked about this in a long time. Only Ivy knows the full story."
            i "It feels like showing someone a deeply shameful part of yourself, right?"
            l "Exactly..."
            i "I know I'm not the one to say this, since I struggle a lot with it myself, but..."
            i "Sometimes sharing it with someone lessens the burden of those feelings."
            if ian_wits < 8:
                call xp_up('wits') from _call_xp_up_589
            l "Yeah, you're right..."
            l "Okay, so here's what happened:"
            l "Axel and I met through Ivy. She had just started doing some photo shoots and I used to tag along, just to make sure the photographers didn't try any funny stuff with her."
            l "These kinds of things happen more than you'd think, so girls need to look out for each other in the modeling business. Especially when you're starting and don't know the ropes..."
            if v5_cindy_shoot:
                i "I understand. I went with Cindy to her photo shoot, too, just to make her feel safe."
            l "Anyway, that's where I met Axel. He was the photographer Ivy was working with. We hit it off and started texting..."
            l "I did my first photo shoot with him. That's how I started modeling."
            l "I thought that I could try it out, since Ivy was also doing it, and hopefully make some extra money..."
            l "But if it wasn't for Axel I probably wouldn't have ever gotten into this business."
            hide lena
            show lena2 at rig
            with short
            l "We started dating shortly after. And when my father got cancer and my financial situation took a big hit, I moved in with him. But not for long."
            if v5_ian_confide:
                i "I know. You told me about how troublesome your relationship was, with all the jealousy and stuff..."
            else:
                l "Even before I knew he was cheating on me, our relationship was complicated. Jealousy, insecurities, unfair demands, and expectations..."
                l "In the beginning things were great, but as our relationship progressed they got more turbulent and, well..."
                l "Toxic."
            $ flena = "worried"
            l "You see, Axel is..."
            l "He likes to be liked. And a lot of people like him."
            l "A lot of girls."
            i "Yeah, I can see why..."
            $ flena = "sad"
            l "But he said he loved me. That he truly loved me, like he hadn't loved anyone else before."
            l "That he needed me."
            l "And I believed him. I gave him my complete trust..."
            l "I shared things with him that... Well, that I wouldn't have shared with anyone else. Things that rely on that complete trust I thought we had in each other."
            $ fian = "worried"
            $ flena = "serious"
            l "But it turns out that trust was shared in only one direction. You see..."
            $ flena = "blush"
            l "Cherry was another model Axel used to work with. I had met her a few times."
            l "And when he asked me to have a threesome with her... Well, I accepted."
            $ fian = "sad"
            i "Oh."
            $ flena = "serious"
            l "And then I learned he had been sleeping with her prior to that. While he was dating me."
            l "Those times I had gone out for drinks with them... They were already fucking behind my back."
            $ flena = "worried"
            l "The times we shared a bed together... The only one that wasn't in the know was me."
            $ flena = "cry"
            $ fian = "worried"
            l "The only one left out was me."
            "Lena's voice broke and two shiny tears rolled down her cheeks."
            i "That's... heavy. I had no idea."
            "Lena dried her tears and recomposed herself."
            $ flena = "sad"
            hide lena2
            show lena at rig
            with short
            l "So... That's the story."
            l "I'll spare you the bullshit excuses Axel gave me when I found out about this. And Cherry..."
            $ flena = "serious"
            l "Well, she was a liar and a coward, and stabbed me in the back. I guess you understand why I don't want to be around her."
            $ fian = "sad"
            i "Yeah... I wouldn't be able to forgive her, either."
            i "To think she did something like that..."
            l "She tricked me too with her nice attitude. Well, it's fake."
            if ian_cherry_dating:
                "Now a lot of stuff finally made sense. The guy Cherry had told me about..."
            $ flena = "sad"
            l "Anyway... Thanks for listening..."
            $ fian = "n"
            i "No, thank you for sharing this. I know it must've been hard."
            i "I wish I had a way to cheer you up..."

        "I had no idea":
            $ renpy.block_rollback()
            $ fian = "sad"
            if ian_cherry_dating:
                "Now a lot of stuff finally made sense. The guy Cherry had told me about..."
            i "I'm sorry... I had no idea."
            l "I know what you're gonna say: \"I never imagined Cherry could do such a thing, she looks so nice\"."
            $ flena = "serious"
            l "Well, I thought that, too. Until I found out what had been going on."
            l "I trusted her, and I trusted Axel. His betrayal is the one that really hurts, hers..."
            l "It just makes me angry. I was such a fool."
            l "And finding her here... Seeing her having fun with you guys, like nothing ever happened..."
            $ flena = "sad"
            l "It just makes me angrier. And sad."

        "You don't need to talk about it if you don't want to":
            $ renpy.block_rollback()
            $ fian = "n"
            i "You know you don't need to tell me about this if you don't feel like it."
            $ flena = "sad"
            if ian_lena < 8:
                call friend_xp('lena', 1) from _call_friend_xp_759
            if v5_ian_confide:
                l "Well, I already told you a bit about it..."
                i "Yeah, about the jealousy, unfair demands, and stuff..."
            else:
                l "Yeah. It's not something I relish talking about, as you can imagine, but..."
                l "Even before I knew he was cheating on me, our relationship was complicated. Jealousy, insecurities, unfair demands, and expectations..."
                l "In the beginning, things were great, but as our relationship progressed they got more turbulent and, well..."
                l "Toxic."
            $ flena = "serious"
            l "And then Cherry got involved..."
            $ fian = "sad"
            if ian_cherry_dating:
                "Now a lot of stuff finally made sense. The guy Cherry had told me about..."
            i "I had no idea."
            $ flena = "serious"
            l "I guess it's not something she goes around talking to people about, huh?"
            l "\"I kept my mouth shut while fucking the boyfriend of a girl I was friends with until I got found out\"."
            l "As you can imagine, finding her here with you guys, well..."
            l "It just makes me angry."
            $ flena = "sad"
            l "And sad."

    $ fian = "sad"
    i "I suppose asking you to come back to the party is out of the question."
    l "Yeah. Even if you kicked Cherry out, which I'm not gonna ask you to do, I couldn't be further from a partying mood right now."
    if ian_lena_dating and ian_lena_over == False:
        i "Do you want me to walk you to your house or...?"
        l "No, it's okay. Thank you for the offer, but you should go back to your friends."
        l "I've ruined the party enough as it is."
        $ fian = "n"
        i "Don't apologize. I'm sorry we've put you in this situation."
        l "You couldn't know."
        l "..."
        $ flena = "worried"
        l "There's something I need to ask, though. And I'll ask you to be honest with me about it, please."
        $ fian = "worried"
        l "What's your relationship with Cherry...?"
        if ian_cherry_dating:
            "I saw it coming. I knew I was going to be asked to lay my cards on the table."
            "Lying would be easier. But I couldn't do that."
            if ian_lena_love and v7_holly_kiss:
                "I was afraid to hurt Lena's feelings. To lose her. But she deserved my honesty... Especially after what had happened with Holly."
            elif ian_lena_love:
                "I was afraid to hurt Lena's feelings. To lose her. But she deserved my honesty."
            $ fian = "sad"
            i "Cherry and I have... hooked up a couple of times."
            $ flena = "sad"
            l "I knew it. She's the girl Perry mentioned, right? The one that had been in your room the night before me."
            i "Yeah..."
            $ flena = "drama"
            l "My life really feels like a joke sometimes. A bad one."
            $ flena = "sad"
            l "What were the odds...?"
            i "It's not a regular thing. It was Perry who invited her today, I had no intention of seeing her..."
            $ flena = "serious"
            l "I hope you weren't expecting to get yourself a threesome."
            $ fian = "worried"
            i "That's not it! It is just as I told you... Cherry and I met and we had a one-night stand."
            i "We talked about it, made our standing clear, and decided all we wanted this to be was a casual fling. What we both needed."
            i "It only happened twice, and we had no plans for it to happen again..."
            l "But you were not closed to the idea."
            $ fian = "sad"
            if ian_lena_love:
                i "I invited you to hang out with my friends tonight. The girl I really like..."
                $ flena = "blush"
                i "The one I want to keep dating."
            else:
                i "I... I don't know. Not tonight, that's for sure."
                i "I invited the girl I'm interested in to hang out with my friends tonight. You, not her."
                $ flena = "sad"
            l "This whole situation is just... surreal."
            l "I need to give all of this some thought."
            $ fian = "sad"
            i "I understand. I'm sorry things turned out this way... I had no idea."
            l "I know."
            l "I'll get going. Good night, Ian."
            $ fian = "n"
            i "Talk to you soon, okay?"
            l "Yeah."
        elif v2_cherry_home:
            "I saw it coming. I knew I was going to be asked to lay my cards on the table."
            i "We... We hooked up once."
            $ flena = "worried"
            if ian_cherry_sex:
                if ian_alison_dating:
                    l "I knew it. She's the girl Perry mentioned, right? The one that had been in your room the night before me."
                    i "No, she isn't... As I said, I only slept with her once, the night we first met."
                    $ flena = "worried"
                    l "I see."
                else:
                    l "I knew it... How I hate being right about these things."
                i "We talked about it, and it didn't mean anything to any of us. It was just a casual fling in a moment when we both needed it."
                i "It hasn't happened again since and we don't have plans for it to happen again."
                l "If you say so..."
            else:
                l "I knew it... How I hate being right about these things."
                $ fian = "blush"
                i "Well, I should say... We didn't end up... you know..."
                "It was so embarrassing telling Lena about me not being able to get it up that night."
                i "I didn't feel too comfortable with the situation, so in the end, we didn't have sex."
                $ flena = "worried"
                i "It hasn't happened again since and we don't have plans for it to happen again."
                i "Not that it happened in the first place, as I said..."
                l "I see..."
            i "I feel... a bit uncomfortable with the situation right now."
            $ flena = "worried"
            l "Imagine how I'm feeling, then."
            $ fian = "worried"
            i "Yeah..."
            $ flena = "sad"
            l "I'll get going. Good night, Ian."
            i "Talk to you soon, okay?"
            l "Sure."
        else:
            $ fian = "n"
            i "She's no more than a friend. An acquaintance, even."
            i "As I said, we're not so close. We met her through Alison pretty recently."
            l "I see. So I take it she won't be in all your meetings..."
            i "No. You don't need to worry about that. Especially now that we know your history."
            l "Okay... That's good to know."
            l "I'll get going. Good night, Ian."
            i "Talk to you soon, okay?"
            l "Sure."
    else:
        i "I understand..."
        $ flena = "sad"
        l "I'm sorry I caused a scene. Go back to your friends, they must be waiting for you."
        l "Tell them I'm sorry for ruining the party."
        $ fian = "n"
        i "It's okay. I'm sorry we've put you in this situation."
        l "You couldn't know."
        l "Good night, Ian."
        i "Talk to you soon, okay?"
        l "Sure."
    stop music fadeout 2.0
    hide lena with short
    pause 1
    if ian_lena_dating and ian_lena_over == False and v2_cherry_home:
        i "Fuck."
    hide ian with short
    pause 1
## BACK TO THE APARTMENT #########################################################################################################################
    play sound "sfx/door_home.mp3"
    $ fian = "n"
    $ fperry = "n"
    $ femma = "sad"
    scene ianhomenight with long
    show ian at lef3 with short
    i "I'm back."
    play music "music/normal_day2.mp3" loop
    show emma
    show perry at rig3
    with short
    p "Hey."
    e "How did it go?"
    i "It was... tense."
    i "Where's Cherry?"
    p "She left."
    e "She apologized and said she didn't feel comfortable after what happened, so she decided to go home."
    i "Not surprising... Did she tell you anything about her story with Lena?"
    e "She didn't want to get into the details, but yeah."
    p "Seems they had a f--{w=0.5}falling out due to a guy."
    i "Yeah. Due to Lena's ex, Axel."
    e "Damn... Those things are the worst. I wish they could find a way to bury the hatchet..."
    i "Yeah, that doesn't seem like it's going to happen."
    if ian_emma_sex:
        e "He's that tall, blonde guy we met at Blazer, right? The one who was talking to Ivy..."
        i "Yeah."
        e "He was really hot!"
        p "He was? I wanna see this Axel guy. Do you have a picture or something?"
    else:
        p "And w--{w=0.5}who's this Axel guy? Do you have a picture or something?"
    $ fian = "serious"
    i "Why the hell would I have his picture? You're such a gossip."
    $ fperry = "meh"
    p "I'm just curious..."
    $ femma = "smile"
    e "I'm sure we can find his Peoplegram!"
    $ fian = "worried"
    i "You too, Emma?"
    e "I'm curious too!"
    $ fian = "n"
    $ fperry = "n"
    $ femma = "n"
    show ian at left
    show emma at rig3
    show perry at right
    with move
    show v3_peoplegram_cherry1 with short
    pause 1
    "It wasn't hard to find. Cherry had tagged him in one of her pics."
    e "Let's see what we have here."
    e "He mostly posts pictures of the models he shoots..."
    hide v3_peoplegram_cherry1
    show v8_axel_pg1
    with short
    $ femma = "surprise"
    $ fperry = "surprise"
    if v3_cindy_date or (v7_holly_trip == False and wade_cindy != 2):
        $ fian = "sad"
        p "Hey, t--{w=0.5}that's Cindy!"
        i "Yeah... Axel is also the photographer she has been working with."
        e "Wow, this guy is involved with all the hot girls in this city, isn't he?"
        $ fperry = "sad"
        $ femma = "sad"
        if v6_confess_wade:
            p "What the hell? You never t--{w=0.5}told me who the photographer was..."
            i "It didn't seem relevant at the moment."
    else:
        $ fian = "surprise"
        p "T--{w=0.5}that's Cindy!"
        i "So the photographer she's been w--{w=0.5}working with is Axel?"
        e "Wow, this guy is involved with all the hot girls in this city, isn't  he?"
        $ fian = "sad"
        $ fperry = "sad"
        $ femma = "sad"
        "Emma was right... So he had taken interest in Cindy, now?"
        "That probably meant big trouble for Wade, if what Lena had told me about the guy was true."
    p "Keep scrolling. I want to see what this dude l--{w=0.5}looks like!"
    hide v8_axel_pg1
    show v9_axel_pg1
    with short
    i "This is him."
    $ fperry = "surprise"
    if ian_emma_sex == False:
        $ femma = "surprise"
    p "W--{w=0.5}what? This is Lena's e--{w=0.5}ex-boyfriend?"
    $ fian = "sad"
    i "Yeah."
    if ian_lena_dating:
        if ian_emma_sex:
            e "Told you... No wonder Cherry fell for him!"
        else:
            e "Wow, no wonder Cherry fell for him!"
        p "Oh, man. And you're s--{w=0.5}supposed to compete with him?"
        $ fian = "worried"
        i "What?"
        $ fperry = "sad"
        p "Dude, he looks like some nazis made him in a l--{w=0.5}lab..."
        p "I mean, are dudes this perfect even real? I think a guy like this could even l--{w=0.5}lure me to the gay side..."
        $ fian = "disgusted"
        $ femma = "smile"
        e "He's so handsome he turns guys gay, ha ha!"
        p "And not only handsome... Have you seen those f--{w=0.5}fucking muscles?"
        p "How the hell can a guy get so ripped? Shit, l--{w=0.5}look at us dude!"
        p "Our bodies are a greasy, d--{w=0.5}derelict ruin in comparison!"
        $ fian = "serious"
        i "Hey, speak for yourself! I don't drink anywhere near as much booze as you do!"
        $ femma = "smile"
        e "You guys look good too, don't worry!"
        p "Still, dude, have you s--{w=0.5}seen him?"
        $ fian = "worried"
        i "Yes, I have, that's the fucking problem!"
        $ fperry = "meh"
        p "Okay, okay, relax. All's not lost."
        p "Maybe he has a small d--{w=0.5}dick. Can we check?"
        $ fian = "worried"
        e "Let me see!"
        "Emma kept scrolling down Axel's profile."
        $ femma = "surprise"
        e "Wow, look!"
        $ fian = "disgusted"
        i "..."
        p "What? What is it?"
        p "Did you f--{w=0.5}find anything?"
        "I showed him the picture."
        hide perry2
        show perry at right
        hide v9_axel_pg1
        show v9_axel_pg2
        with vpunch
        $ fperry = "surprise"
        $ femma = "smile"
        p "Holy fuck! This dude is p--{w=0.5}p--{w=0.5}p--{w=0.5}packing like a horse!"
        p "F--{w=0.5}fuck...!"
        hide v9_axel_pg2 with short
        show ian at lef
        show perry at rig
        with move
        $ fperry = "sad"
        "Perry got closer and put his hand over my shoulder, looking at me."
        p "I'm so sorry, man."
        $ fian = "mad"
        show ian at lef3 with move
        i "What do you mean you're sorry!?"
        p "Everything's lost. T--{w=0.5}that's it, man."
        i "Fuck you, dude!"
        $ fperry = "meh"
        hide perry
        show perry2 at rig
        with short
        p "Hey, you've seen it too! He's literally p--{w=0.5}perfect, and has a monster cock to boot?"
        p "There's no comparison! Tell him, Emma!"
        show emma at truecenter
        show perry2 at rig3
        with move
        e "Why are guys so obsessed with the size of dicks? You care way more about it than we do!"
        $ femma  = "n"
        e "But wow, he's really hot, yeah."
        $ fperry = "n"
        $ fian = "disgusted"
        p "See? I mean, no disrespect, you know I think y--{w=0.5}you're quite a handsome guy."
        p "But that dude's in a league of his own. You and I can only d--{w=0.5}dream of reaching there."
        $ fian = "sad"
        i "Damn..."
        p "I know it's tough, man..."
        p "Come, I'll make you some h--{w=0.5}hot cocoa."
        $ fian = "serious"
        i "Get off my back."
        p "Wade's in a tough spot too... Now I understand why he's so un--{w=0.5}uncomfortable with Cindy wanting to be a model, if it means having this guy around!"
    else:
        p "No wonder Cherry fell for him! And he used to date Lena, too?"
        $ fian = "sad"
        $ femma = "sad"
        i "He did..."
        $ fperry = "sad"
        p "Dude, he looks like some nazis made him in a l--{w=0.5}lab..."
        p "I mean, are dudes this perfect even real? I think a guy like this could even l--{w=0.5}lure me to the gay side..."
        $ fian = "n"
        $ femma = "smile"
        i "What is this? Love at first sight or something?"
        e "Well, he's pretty hot! Look at those muscles!"
        p "How the hell can a guy get so ripped? Shit, l--{w=0.5}look at us dude!"
        p "Our bodies are a greasy, d--{w=0.5}derelict ruin in comparison!"
        hide v9_axel_pg1 with short
        show emma at truecenter
        show perry at rig3
        show ian at lef3
        with move
        $ fian = "serious"
        $ femma = "smile"
        i "Hey, speak for yourself! I don't drink anywhere near as much booze as you do!"
        $ femma = "smile"
        e "You guys look good too, don't worry!"
        p "Still, dude, look at him!"
        i "I'm not blind. He looks like an Adonis, so what?"
        p "So what? Now I understand why Wade's so un--{w=0.5}uncomfortable with Cindy wanting to be a model, if it means having this guy around!"
    $ fian = "sad"
    $ femma = "sad"
    i "That's right..."
    if ian_cindy_model or v5_cindy_shoot:
        "I was kinda worried about that too if I was honest with myself."
        "Somehow it was me who encouraged Cindy to pose for Axel, and I suspected Axel wanted more from Cindy than just taking some pics."
        if v7_cindy_kiss:
            "And I still had my own issues to solve with Cindy. What a mess."
    $ fperry = "serious"
    p "This guy is not to be t--{w=0.5}trusted, that's plain to see. He wields too much power..."
    p "I mean, I'm sure he'd get most g--{w=0.5}girls to drop their panties and b--{w=0.5}bend over for him by just asking!"
    $ fian = "serious"
    i "Stop it with the ass-licking, you sound like his number one fan."
    $ fperry = "meh"
    if v3_cindy_date:
        e "Well, it's you who met him. How's he like?"
        $ fian = "sad"
        i "He's a real charmer, that much was obvious by the way Cindy looked at him..."
        if v6_confess_wade:
            i "And during the photo shoot he kept things professional, I'd say. He was mostly polite and friendly..."
        else:
            i "I mean, you can see he's a bit arrogant, but he was mostly polite and friendly."
        if axel_knows_dating:
            i "That changed when I told him Lena and I were seeing each other, though. He got pretty tense."
            i "Ivy told me to be careful around him."
        else:
            i "But last time he gave off a bad vibe. Ivy told me to be careful around him, after all..."
    if ian_lena_dating:
        e "Seems like Wade should watch out with this guy... And you too, since you're stealing his ex away from him!"
        $ fian = "serious"
        i "I'm not stealing anything."
    $ fian = "sad"
    "I sighed and lay back on the couch."
    i "I need a beer."
    play sound "sfx/beer.mp3"
    show ian at truecenter
    show emma at lef3
    with move
    "Perry cracked one open and passed it to me. There already were quite a few empty cans on top of the table."
    i "I see you guys haven't been wasting time..."
    $ femma = "n"
    if v8_lena_story:
        p "Well, you took your sweet time to come back..."
        e "There's plenty more, don't worry!"
    else:
        e "They're mostly Perry's."
        p "You drank a few, too!"
    "I sighed again and took a long sip."
    i "Well, this night is not how I was expecting it to be."
    e "What, you don't like hanging out with your old friends?"
    i "I just feel bad about what happened. It ruined the mood for me too."
    if ian_lena_dating and ian_lena_over == False:
        "Knowing Axel was Lena's ex had been bumming me out, and now it turned out he was involved with Cherry, too..."
        "That guy's shadow was everywhere. And I was supposed to compete with him...?"
        "So much for thinking I was a lucky guy..."
        p "You look w--{w=0.5}worried."
        $ fian = "n"
        i "It's nothing. Let's talk about something else..."


    e "Well... I still have that thing I told you about before."
    $ fian = "n"
    i "What thing? That mysterious thing you wanted to show us?"
    $ femma = "smile"
    e "Yeah. Take a look."
    "Emma grabbed her fanny pack and took out a glass pipe and a small plastic bag with a brown, grainy dust."
    i "What's this?"
    e "Oh man, this is something really crazy."
    p "It is some kind of hash or...?"
    e "No, it has nothing to do with weed. It's called {i}ORS{/i}, which is short for {i}oxytocinribyl...{/i}"
    e "{i}Oxytocinribothyl...{/i}"
    e "Some weird chemical name. But guys, I tried it some time ago, and wow!"
    e "You guys need to give it a go. It's out of this world."
    menu:
        "I'm in":
            $ renpy.block_rollback()
            $ v8_trip = True
            $ reality = False
            $ fian = "smile"
            i "That's all I need to hear. I'm in."
            e "That's the spirit!"
            if ian_emma < 12:
                call friend_xp('emma', 1) from _call_friend_xp_760
            $ fperry = "sad"
            p "Are you sure?"
            e "Don't you wanna give it a go too, Perry?"
            $ fperry = "meh"
            hide perry
            show perry2 at rig3
            with short
            if ian_weed > 0:
                p "I don't think so... I smoked weed last time, and I'm not sure I should've."
                e "Why? You used to smoke all the time before."
                p "That's the exact reason why."
                e "Well, as I said, this has nothing to do with weed."
            else:
                p "No, and I don't think you should, Ian."
                p "You didn't even want to smoke weed last time!"
                i "Well, I know what weed does, and feel like trying this. If Emma says it's worth it..."
                e "It really is."
            p "I don't know... Let's see what happens to Ian and then maybe I'll give it a try."
            $ fian = "serious"
            i "You're gonna use me as a guinea pig?"
            p "Hey, you're the one who's willing to try this crazy thing."
            e "It's crazy, but really cool. It opened my eyes to things that had always been there but I was blind to, or something like that..."
            e "It's an experience I would recommend to anyone!"
            i "Enough chit-chat. Let's light this pipe up."

        "I'm not sure about this...":
            $ renpy.block_rollback()
            $ fian = "worried"
            i "I'm not sure about this, Emma... I mean, I have no idea of what this thing does."
            e "It's psychedelic, like weed..."
            i "You said it had nothing to do with weed."
            e "The effects are a bit more dramatic, yeah. But it's better if I don't spoil it for you!"
            if ian_weed > 0:
                i "I don't know... I can smoke weed from time to time, but this sounds like a whole other ball game."
            else:
                i "I don't know... I haven't even smoked weed recently."
            $ fian = "n"
            i "I don't want to get fucked up. Besides, I have a lot on my mind tonight."
            e "This could help you with that. It gave me a whole new perspective on things when I tried it, that's why I wanted you guys to give it a go, too."
            i "What do you mean, a new perspective?"
            e "It's hard to explain. It opened my eyes to things that had always been there but I was blind to, or something like that..."
            e "It's an experience I would recommend to anyone!"
            i "What about you, Perry? Are you up for trying it?"
            p "I don't know... You try it first, and depending on how that goes I'll do it too."
            $ fian = "serious"
            i "So you're gonna use me as a guinea pig?"
            p "I'm just being cautious."
            menu:
                "Okay, I'll try this":
                    $ renpy.block_rollback()
                    $ v8_trip = True
                    $ reality = False
                    $ fian = "n"
                    i "Alright, then... I'll give it a go."
                    e "That's the spirit!"
                    if ian_emma < 12:
                        call friend_xp('emma', 1) from _call_friend_xp_761
                    i "I hope you guys take care of me if anything happens!"
                    e "Don't worry! You'll be fine, just relax and enjoy the ride."

                "Sorry, I'll pass":
                    $ renpy.block_rollback()
                    $ fian = "n"
                    i "Sorry, but I'll pass. I don't feel like getting blasted out of my mind tonight."
                    $ femma = "n"
                    e "Alright, sorry for trying to push you into it. I found it really cool and I wanted to share it with you guys, but I understand your point of view."
                    $ femma = "smile"
                    e "No drugs tonight... Just a ton of alcohol, am I right?"

        "No drugs for me":
            $ renpy.block_rollback()
            i "No drugs for me, thanks."
            if ian_weed > 0:
                i "We can smoke some weed if you want, but I'm not touching the harder stuff."
            else:
                i "I don't even smoke weed lately, I'm not gonna touch the harder stuff."
            e "Aw, come on! A writer like you should try and experience what his mind's got to offer..."
            i "I already do that in my own way. Thanks for the offer, but I have to pass, seriously."
            $ femma = "n"
            e "Alright. I thought you might find it interesting..."
            e "When I tried it, it opened my eyes to things that had always been there but I was blind to, or something like that..."
            e "It's an experience I would recommend to anyone!"
            i "Sounds like something Perry would benefit from."
            $ fperry = "meh"
            p "Don't pass the buck to me, dude. I'm not gonna try it on your behalf."
            e "Alright, alright, sorry to have brought this up! I found it really cool and I wanted to share it with you guys, but I understand your point of view."
            $ femma = "smile"
            e "No drugs tonight... Just a ton of alcohol, am I right?"

    if v8_trip:
        jump v8trip1
    else:
        jump v8iansaturdayend

## TRIP #######################################################################################################################################################################################################################################
label v8trip1:
    $ ian_look = 2
    $ femma = "n"
    $ fian = "n"
    $ fperry = "n"
    "Emma prepared the pipe and handed it to me."
    e "Here. Light it up and give it a long, deep drag. Take in as much as you can and hold it for a few seconds."
    hide ian
    show ian_smoke
    show ian_pipe
    with short
    i "Okay, let's see..."
    play sound "sfx/lighter.mp3"
    "I questioned the reason why I was doing this one last time as I inhaled the smoke."
    "I had no idea. Maybe I was feeling adventurous."
    "Maybe I just wanted to escape reality for a while."
    p "Do you feel something?"
    i "Hmmm..."
    i "I'm starting to feel a bit dizzy..."
    stop music fadeout 5.0
    $ _game_menu_screen = None
    $ quick_menu = False
    p "Just dizzy?"
    i "Wait..."
    play sound "sfx/_the_.mp3"
    $ fian = "worried"
    hide ian_smoke
    hide ian_pipe
    show lsd1:
        alpha 0.0
        linear 8 alpha 1.0
    show ian
    with long
    i "More than just a bit..."
    $ fperry = "sad"
    p "You don't look so good..."
    i "Oh, shit."
    $ femma = "sad"
    e "Relax."
    $ fian = "surprise"
    i "Oh shit!" with vpunch
    i "Guys?! Where are you?!"
    hide emma
    hide perry
    hide ian
    with Dissolve (1)
    i "What the fuck is going on...?!"
    $ fake_error = "n"
    $ err_rollback = 0
    $ err_reload = 0
    $ err_bbc = 0
    $ err_quit = 0
    $ err_console = 0
    $ err_ignore = 0
    $ v8triptopless = False
    stop sound
label errorloop:
    $ renpy.block_rollback()
    scene error_base with hpunch
    pause 1
    if err_console == 0:
        play music "sfx/_lie_.mp3" loop
    call screen v8error
    $ renpy.block_rollback()
    if fake_error == "none":
        jump errorloop
    elif fake_error == "rollback":
        $ renpy.block_rollback()
        $ fian = "disgusted"
        $ err_rollback += 1
        if err_rollback == 1:
            if err_ignore == 0:
                $ fian = "n"
                scene ianhomenight
                show ian
                i "..."
                i "... ..."
                i "... ... ..."
                i "Something feels off."
                $ fian = "disgusted"
                scene lsd2
                show ian
                with Dissolve (2.0)
                i "What the hell...!?"
                i "What is this? What is happening to me...?"
            else:
                scene lsd2
                i "What the hell...?"
                show ian with short
                i "Where am I? What is happening to me...?"
            i "Emma? Perry? Are you guys there?"
            i "What the fuck is this place...?"
            i "Is this... even a place?"
            i "Oh, no."
            i "I feel it coming again. It's..."
            scene error_base with vpunch
            i "No! Not this again!"
            scene error_base
            jump errorloop
        elif err_rollback == 2:
            scene lsd3
            show ian with short
            i "Shit... Where am I now...?"
            i "Is this the same place? It feels familiar..."
            i "I need to find a way to get out of here!"
            i "It's like I'm trapped in a loop!"
            i "It's like my mind needs {i}reloading{/i}..."
            if err_reload > 0:
                i "I should try that again."
            scene error_base
            jump errorloop
        elif err_rollback == 3:
            scene lsd3
            show ian with short
            i "Damn! I can't get out of this!" with vpunch
            i "I need to do something...! Whatever it is...!"
            if err_reload < 2:
                i "{i}Reload{/i} your head, Ian!"
            scene error_base
            jump errorloop
        elif err_rollback == 4:
            scene lsd2
            show ian with short
            i "Fuck!" with vpunch
            i "I'm starting to lose my mind..."
            if err_reload < 2:
                i "I really need to {i}reload{/i}!"
            scene error_base
            jump errorloop
        else:
            scene lsd3
            show ian with short
            i "Have I gone crazy...?"
            scene error_base
            jump errorloop

    elif fake_error == "reload":
        $ renpy.block_rollback()
        if err_reload == 0:
            if err_rollback > 0 or err_quit > 0:
                $ err_reload = 1
            scene black
            call screen v8reload
            call screen v8reload2
            jump errorloop
        elif err_reload == 1:
            $ err_reload = 2
            scene black
            call screen v8reload
            play sound "sfx/willdown.mp3"
            call screen v8reload3
            jump errorloop
        else:
            scene black
            call screen v8reload
            call screen v8reload4
            jump errorloop

    elif fake_error == "ignore":
        $ renpy.block_rollback()
        if err_ignore == 0:
            $ err_ignore = 1
            $ fian = "disgusted"
            scene ianhomenight
            show ian
            i "..."
            i "What the hell was that?"
            $ fian = "sad"
            i "Emma? Perry?"
            i "Where are they...?"
            menu:
                "Go to your room":
                    $ renpy.block_rollback()
                    $ fian = "n"
                    i "Maybe they're in my room?"
                    play sound "sfx/door.mp3"
                    scene ianroomnight
                    show ian
                    with short
                    i "Nope, they're not here."
                    i "On second thought, why would they be here?"
                    "I went back to the living room."
                    play sound "sfx/door.mp3"
                    scene rockbar
                    show ian
                    with short
                    i "..."
                    $ fian = "disgusted"
                    i "Wait, this is not my living room."
                    i "What the hell am I doing here? Something's really off...!"

                "Go to Perry's room":
                    $ renpy.block_rollback()
                    $ fian = "n"
                    i "Maybe they're in Perry's room."
                    play sound "sfx/knock.mp3"
                    "I knocked on the door before entering, just in case."
                    i "I'm coming in!"
                    play sound "sfx/door.mp3"
                    scene rockbar
                    show ian
                    with short
                    i "They're not here."
                    i "..."
                    $ fian = "disgusted"
                    i "Wait, since when does this door lead to the Fortress?"
                    i "What is going on here!?"

            i "I need to get out of here!"
            "I turned around, looking for the exit, and rushed through the door."
            play sound "sfx/door.mp3"
            scene error_base
            $ fian = "surprise"
            show ian
            with vpunch
            i "What the hell...!?"
            i "What is this? How do I get out of here...?"
            hide ian with short
            jump errorloop
        elif err_ignore == 1:
            $ err_ignore = 2
            scene error_base
            $ fian = "worried"
            show ian
            i "This is definitely weird."
            i "I can't seem to... make sense of things."
            i "I can't stay here. I need to do something."
            i "Move forward, Ian."
            i "Or maybe... I should move {i}backward{/i}..."
            hide ian with short
            jump errorloop
        else:
            scene error_base
            i "I obviously can't ignore this situation."
            jump errorloop

    elif fake_error == "console":
        $ renpy.block_rollback()
        if err_reload < 2:
            play sound "sfx/phone_back.mp3"
            scene error_base with vpunch
            i "Fuck..."
            i "I should try something else."
            jump errorloop
        else:
            if err_console == 0:
                $ err_console = 1
                play music "sfx/_cake_.mp3"
                scene lsd with long
                $ fian = "surprise"
                i "Woooah... What in the world...?"
                show ian at lef3 with short
                i "Where am I?"
                "{i}You just opened the doors of perception.{/i}"
                $ fian = "worried"
                i "What? Who said that?"
                "{i}Go forth in the journey of the mind.{/i}"
                "{i}Open the final door.{/i}"
                i "{i}Open{/i}..."
                $ fian = "surprise"
                play sound "sfx/_a_.mp3"
                i "Whoa!{w=0.3}{nw}" with flash
                with vpunch
                with flash
                i "Wait! Help! {w=0.5}{nw}" with vpunch
                with flash
                scene error_base
                stop music
            else:
                scene error_base
                $ fian = "n"
                show ian
                with short
                i "They said I should {i}open{/i} the last door..."
            jump errorloop

    elif fake_error == "open":
        $ renpy.block_rollback()
        if err_console > 0:
            $ fian = "worried"
            play sound "sfx/door.mp3"
            scene ianroomnight
            show ian with short
            "I opened the door to my room, my heart beating like crazy in my chest."
            i "Fuck... Holy shit... I'm out?"
            i "I'm finally out?"
            "Those words came out of my mouth as my fingers wrote them on the screen."
            i "What the hell was that?"
            "I wasn't able to understand what was going on."
            $ fian = "disgusted"
            i "Wait, what...?"
            play sound "sfx/keyboard.mp3"
            hide ian
            show v2_ianwrite
            with short
            i "\"{i}How could I, when my awareness was limited by the mind that was giving life to me, to this particular moment?{/i}\""
            i "Mhhh..."
            i "I wonder if this will make sense to the readers? Maybe I'm going a bit too crazy with this narrative experiment."
            $ fian = "smile"
            scene ianroomnight
            show ian
            with short
            "I got up and decided to call it a day. Emma should be about to arrive."
            if ian_lena_dating and ian_lena_over == False:
                "And also Lena... I was hyped to spend the night together with her."
            elif ian_cherry_dating:
                "And also Cherry... I couldn't wait to see her again. I had been missing her."
            i "Let's check if Perry bought enough beers for tonight."
            "I opened the door and went to the living room."
            play sound "sfx/door.mp3"
            $ fian = "surprise"
            scene lsd4
            show ian
            play sound "sfx/_a_.mp3"
            play music "music/lsd.mp3" loop
            i "...?!" with vpunch
            i "What the fuck is going on?! I thought I made it out...!"
            jump v8trip2
        else:
            scene error_base
            play sound "sfx/door.mp3"
            i "..."
            i "Nothing's happening..."
            jump errorloop

    elif fake_error == "bbc":
        $ renpy.block_rollback()
        if err_bbc == 0:
            $ err_bbc = 1
            scene error_base
            $ jeremy_look = 3
            $ fjeremy = "happy"
            show jeremy at rig
            j "Somebody said BBC?"
            $ fian = "disgusted"
            show ian at lef with short
            i "Jeremy? How the hell did you get here?"
            j "Me? I'm just a bad joke."
            $ fjeremy = "smile"
            j "Don't mind me. I'm not even here..."
            hide jeremy with long
            i "Wait...!"
            hide ian with short
            jump errorloop
        else:
            i "Nope."
            jump errorloop

    elif fake_error == "copy":
        $ renpy.block_rollback()
        i "I have no idea what this does."
        jump errorloop
    elif fake_error == "quit":
        $ renpy.block_rollback()
        if err_quit == 0:
            $ err_quit = 1
            scene error_base
            pause 1
            play sound "sfx/door_slam.mp3"
            with vpunch
            jump errorloop
        elif err_quit == 1:
            $ err_quit = 2
            scene black
            call screen v8quit1
            jump errorloop
        elif err_quit == 2:
            $ err_quit = 3
            scene error_base
            $ fian = "surprise"
            $ fian = "worried"
            show ian with vpunch
            i "I want to quit! Get me the hell out of here!"
            hide ian with short
            jump errorloop
        elif err_quit == 3:
            $ err_quit = 4
            scene error_base
            $ fian = "sad"
            show ian with short
            i "There's a way out of here, I'm sure."
            if err_console > 0:
                i "I just need to {i}open{/i} the right door."
            else:
                i "I should try something else..."
            hide ian with short
            jump errorloop
        elif err_quit > 3:
            scene error_base
            call screen v8quit1
            jump errorloop
##TRIP CONTINUES ####################################################
label v8trip2:
    god "There's no place you can {i}make it out{/i} of, my child."
    i "Holy fucking shit, I'm tripping balls!!" with vpunch
    god" Leave your anxiety behind, it won't do you any good here."
    god "Let go. You have nothing to fear."
    god "Concentrate on what you're feeling right now."
    $ fian = "worried"
    "{i}I look around, trying to get my bearings. Something feels off.{/i}"
    i "I'm disoriented."
    god "It's normal. It's the first time you're here."
    god "What else do you feel?"
    "{i}I look at my body.{/i}"
    i "It feels... strange."
    "{i}I raise my hand in front of my face, staring at it like it is the first time I see it.{/i}"
    "{i}I move it.{/i}"
    $ fian = "n"
    i "Somehow it feels more real than reality itself..."
    i "It feels like things are happening {i}right now{/i}."
    i "Is this... what people call \"being present\"?"
    god "It's a way to describe it, yes."
    "{i}I try to find the source of the voice I'm hearing. I can't see any.{/i}"
    i "Where are you?"
    god "I'm not {i}somewhere{/i}. I'm not {i}something{/i} or {i}someone{/i}, either."
    god "But if you need to {i}see{/i} me..."
    show ian at lef3 with move
    $ fian = "disgusted"
    play sound "sfx/_is_.mp3"
    show god at rig3 with long
    god "Here I am."
    i "This is getting weirder and weirder...!"
    i "Who am I talking to?"
    i "{i}What{/i} are you?"
    god "Isn't that the question? Who do you think you're speaking with?"
    $ config.menu_include_disabled = False
    $ greyed_out_disabled = True
    menu:
        "{image=icon_wits.webp}The author's self-insertion" if ian_wits > 5:
            $ renpy.block_rollback()
            i "I'd say you're a self-insertion written by the author of this visual novel."
            $ fian = "n"
            i "A rather self-aggrandizing one, I must say..."
            ek "You're not wrong, ha ha."
            ek "That was a very self-conscious answer, don't you think?"
            ek "Who are you, Ian? Aren't you written by that same author?"
            ek "Isn't your character some kind of self-insertion, too?"
            $ fian = "sad"
            i "Well, technically... I guess I am."
            i "But I also feel there's more to it... Something beyond that..."
            god "There is."
            god "So, if you're not just a character, more than a self-insertion... {i}Who are you{/i}?"

        "God":
            $ renpy.block_rollback()
            $ fian = "n"
            i "Well, it says it in your name tag, doesn't it? You're God."
            pikachu "Oh, really? And what if I change my name?"
            i "Uh, that's..."
            pikachu "What if I change {i}your{/i} name?"
            $ fian = "sad"
            penisbreath "What, my--{w=0.5}{nw}"
            $ fian = "serious"
            penisbreath "Hey!"
            god "I was joking, ha ha."
            god "So, you have a better answer now?"
            $ fian = "n"
            i "You're some kind of... consciousness communicating with me?"
            god "You're not wrong. It could be called something like that."
            god "Which raises the question... {i}Who are you{/i}?"

        "A higher being":
            $ renpy.block_rollback()
            $ fian = "n"
            i "I guess you're one of those \"higher beings\" people talk about."
            god "Ha ha, well, I'm flattered that you think that way about me. I guess you could call me that."
            i "You're some kind of... consciousness communicating with me, right?"
            god "It could be called something like that. Which raises the question... {i}Who are you{/i}?"

        "A hallucination":
            $ renpy.block_rollback()
            $ fian = "n"
            i "This is clearly my brain tripping balls."
            $ fian = "serious"
            i "You're a hallucination caused by that crazy drug Emma gave me. I'm gonna kill her."
            god "A pragmatic answer. You're not wrong, of course, but there's more to it, don't you think?"
            $ fian = "n"
            i "Honestly, I have no idea what's going on. I'm just talking to myself, right?"
            god "Again, you're not wrong. But that raises the question... {i}Who are you{/i}?"

    $ fian = "n"
    i "Me? Well, I'm... me. Ian Watts."
    god "And what defines who Ian Watts is?"
    i "My actions, I guess."
    if ian_wits > 3:
        i "We are what we do, or so they say..."
    god "And who chooses those actions?"
    menu:
        "Me":
            $ renpy.block_rollback()
            i "Me. It's me who makes those choices."

        "Myself":
            $ renpy.block_rollback()
            i "I choose them myself, of course."

        "I do":
            $ renpy.block_rollback()
            i "I do. I'm the one who chooses."

    god "Are you sure about that?"
    i "I just did. Didn't you see?"
    god "Sure. You had three choices, and you picked one. But you only had three options, right?"
    $ fian = "worried"
    god "You chose, but did you create those options? Where did they come from?"
    menu:
        "You tell me":
            $ renpy.block_rollback()
            $ fian = "n"
            i "You seem pretty well informed. I'm sure you can tell me."
            god "You are free to choose from a very limited set of options. Not everything you wish for is possible."
            god "Beings are limited by themselves and the world around them. And can only choose from their available possibilities."
            god "So, in essence, those possibilities exist outside of your will to act."
            god "Whatever you decide to choose, all possible outcomes have been already determined."

        "Those are my possibilities":
            $ renpy.block_rollback()
            $ fian = "n"
            i "Well, those were my possibilities, right?"
            i "Sadly, not everything is possible in this world... I can't fly like Superman, same as I can't grow a dick the size of a horse..."
            god "Weird example, but okay."
            i "We're limited by ourselves and the world around us. And we choose from those possibilities."
            if ian_wits < 8:
                call xp_up('wits') from _call_xp_up_590
            god "So, in essence, those possibilities exist outside of your will to act, that's what you're saying."
            god "Whatever you decide to choose, all possible outcomes have been already determined."

        "{image=icon_mad.webp}Let's get this over with already":
            $ renpy.block_rollback()
            $ trip_kill = True
            $ fian = "serious"
            i "Look, I'm not getting it and I don't want to."
            i "I thought taking drugs would be fun, not a lesson about metaphysics."
            $ fian = "n"
            i "Can I... pass out already or something? I came here for the sexy girls and this is boring me to death."
            god "Haven't you heard about \"letting the trip take you where it wants to take you\"?"
            god "Ah, alright. Have it your way. You're letting a great experience go to waste..."
            i "Sure, whatever. I just want to wake up and get back to the sexy stuff."
            god "Kids these days..."
            jump trip2

    $ fian = "worried"
    i "So what are you trying to say? That my actions... That who I am is... already written?"
    god "Precisely."
    i "So everything's already set in stone? My free will is... just an illusion?"
    god "Yes... and no."
    god "Let me show you."
    hide ian
    hide god
    with long
    if jess_bad:
        show jessb
    else:
        show jessg
    with long
    $ fjess = "n"
    $ fian = "n"
    god "Look at this girl. You've already met her."
    if ian_jess_number:
        i "Yeah, that's Jessica. I got her number that night at the bar..."
        i "Maybe I should give her a call."
    else:
        i "Yeah, I saw her at the bar the other night, she's friends with Ivy."
    god "Did you know you could've met two very different versions of her?"
    if jess_bad:
        show jessg with short
        show jessb at rig
        show jessg at lef
        with move
    else:
        show jessb with short
        show jessg at lef
        show jessb at rig
        with move
    $ fian = "worried"
    i "What...? Uh..."
    menu:
        "Yes":
            $ renpy.block_rollback()
            i "Yes, I did in fact."
            god "Really? Unless you are lying... Ask yourself this question: how could you have known?"
            i "Uh, I... I just know... But I don't know why I know."
            god "Is it really {i}you{/i} who knows, Ian? Or maybe someone knows that {i}for{/i} you..."
            god "The one who is looking through your eyes right now. The one you really are."
            i "That's..."
            "{i}I shake my head.{/i}"
            i "Anyway, how is it even possible I could have met two different versions of the same person?"

        "No":
            $ renpy.block_rollback()
            i "No, I had no idea. Is that even possible?"

    if jess_bad:
        god "This version of Jessica came into being because Lena chose to sleep with a particular individual."
    else:
        god "This version of Jessica came into being because Lena chose not to sleep with two particular individuals."
    god "But both versions of Jessica exist at the same time, both equally real."
    menu:
        "How so?":
            $ renpy.block_rollback()
            hide jessg
            hide jessb
            show ian at lef3
            show god at rig3
            with short
            i "What? How does that even... make any sense?"
            i "I couldn't possibly meet them both at the same place and at the same time, right?"
            god "That's right. You'd have to travel to different places in time to meet each one of them."
            $ fian = "n"
            i "But you just said they exist at the same time..."
            god "You move through space all the time. And I suppose you're aware you're moving through time constantly, too."
            if ian_wits > 4:
                i "At the speed of one second per second, yeah."
            god "Always moving forward toward the future, leaving the past behind."
        
        "Can I meet the other one?":
            $ renpy.block_rollback()
            if jess_bad:
                i "The girl I met seems to have had it rough, but this other version of her looks pretty happy and... normal."
            else:
                i "The girl I met seems perfectly happy with her life and her relationship, but this other version of her... It looks like her life's pretty different."
            i "Is there a way for me to meet the other version of Jess?"
            god "Would you like to? That would mean jumping your consciousness to another, parallel timeline..."
            god "You could do that by starting over and navigating the right path with your choices. Or... you can cheat a bit."
            god "You're talking with the Creator, after all!"
            menu:
                "I want to switch":
                    $ renpy.block_rollback()
                    if jess_bad:
                        $ jess_bad = False
                    else:
                        $ jess_bad = True
                    i "Yes, I'd like you to switch them."
                    god "I already did. Or, better said, {i}you{/i} did, the moment you clicked on that option I just presented."
                    i "So I just travelled through dimensions? It didn't feel like it..."
                    
                "It's okay as it is":
                    $ renpy.block_rollback()
                    i "No... It's okay as it is. I don't think trying to travel through dimensions is a good idea."
            
            god "But you're travelling through dimensions constantly! Like you're moving from past to future at a constant rate right now."

    menu:
        "Only the present is real":
            $ renpy.block_rollback()
            i "But the past and the future don't exist. Just the present..."
            god "Well, you think places exist even if you're not there, right?"
            god "Mount Everest won't disappear just because nobody's sitting at the summit, and left won't disappear just because you move right."
            i "Yeah, that's obvious."
            god "Then why should time be different? Space and time, you know... are one and the same."
            $ fian = "worried"
            i "Look, I'm getting lost here..."
            god "Just look around you. All of time already exists."
            god "You're just moving through it."
            i "..."
            i "So then it's true... Everything that has happened... Everything that will happen..."
            i "It's already written, and we don't have a say in it."
            god "Well... You feel like you're not choosing?"
            if v7_cindy_kiss:
                god "Didn't you choose to pursue Cindy instead of staying away from her, for example?"
                $ fian = "blush"
            elif ian_defy_minerva:
                god "Didn't you choose to leave Minerva sexually frustrated instead of fucking her, for example?"
                $ fian = "confident"
            elif ian_emma_sex:
                god "Didn't you choose to have sex with Emma instead of helping your friend Perry get with her, for example?"
                $ fian = "blush"
            elif ian_switch_review or ian_honest_review:
                god "Didn't you choose to defy Minerva and write the review you wanted instead of the one she demanded, for example?"
                $ fian = "smile"
            else:
                god "Didn't you choose to write the review Minerva demanded instead of defying her, for example?"
                $ fian = "sad"
            i "Well, yeah... I've made many choices..."
            $ fian = "sad"
            i "But if those choices were already written... What's the point?"
            god "As I said, all of existence is already in place. Your freedom to choose lies in picking which place you want to visit."
            $ fian = "n"
            god "Every choice you make moves you in one direction."
            god "Going back to Jessica... The choices you made brought you to meet this version of her instead of the other."
            i "How's that even related to the choices I made? If anything, it has to do with what Lena chose..."
            god "Is there a difference?"
            god "You, too, exist in many different versions of yourself all at once."
            god "You're just embodying the one where your choices converge."
            $ fian = "worried"
            i "This is... just blowing my mind."
            i "I'm not even sure I'm getting any of this."
            god "It's not easy to get. You will try to explain it once you've woken up, but won't be able to."
            $ fian = "n"
            i "So I've heard. I guess it would be hard putting this experience into words..."
            god "You can always try."
            god "You're supposed to be a writer, aren't you...?"
            jump trip2

        "{image=icon_mad.webp}Okay, this is boring":
            $ renpy.block_rollback()
            $ trip_kill = True
            $ fian = "serious"
            i "Look, this is getting pretty tedious."
            i "I thought taking drugs would be fun, not a lesson about metaphysics."
            "Can I... pass out already or something? I came here for the sexy girls and this is boring me to death."
            god "Haven't you heard about \"letting the trip take you where it wants to take you\"?"
            god "Ah, alright. Have it your way. You're letting a great experience go to waste..."
            i "Sure, whatever. I just want to wake up and get back to the sexy stuff."
            god "I can get behind that."
            jump trip2

label trip2:
    stop music fadeout 2.0
    hide ian
    hide god
    with long
    scene blackbg with long
    $ lena_active = True
    $ ian_active = False
    $ save_name = "Lena: Chapter 8"
    show active_lena with long
    pause 1.0

    call calendar from _call_calendar_90

    $ _game_menu_screen = "phone"
    $ quick_menu = True
    $ flena = "n"
    $ lena_look = 1
    scene lenaroom with long
    play music "music/normal_day2.mp3" loop
    play sound "sfx/meow.mp3"
    show lola at lef with short
    "Like most mornings, Lola woke me up."
    show lenaunder at rig with short
    l "Can't you let me sleep in for a change? It's Saturday..."
    play sound "sfx/meow.mp3"
    l "Alright, alright. I'll feed you your breakfast."
    "I got up and stretched. It was a beautiful morning."
    l "Let me put on something first..."
    "I picked up a loose t-shirt and before putting it on I took a look in the mirror."
    $ flena = "shy"
    l "Damn, I'm looking good this morning..."
    l "Maybe instead of putting something on, I should take something off..."
    menu:
        "Take off your bra":
            $ renpy.block_rollback()
            $ v8triptopless = True
            $ flena = "flirtshy"
            hide lenaunder
            show lenatopless2 at rig
            with short
            l "Damn... Those look so nice..."
            "I groped my boobs, feeling their smoothness and perkiness. So awesome..."
            lola "You're having fun, huh?"

        "Put on the t-shirt":
            $ renpy.block_rollback()
            $ flena = "worried"
            l "Why would I do that? I wanted to get dressed..."
            hide lenaunder
            show lenabra at rig
            with short
            l "There. I woke up feeling a bit silly this morning."
            lola "It would seem that way."
    stop music
    $ flena = "surprise"
    l "Wha--?!" with vpunch
    l "Did I just hear...?"
    lola "A cat talking? Indeed. It's not that surprising."
    l "What the hell is going on...?!"
    $ flena = "worried"
    l "..."
    i "I'm still tripping balls, is that right?"
    $ _game_menu_screen = None
    $ quick_menu = False
    play sound "sfx/_is_.mp3"
    play music "sfx/_lie_.mp3" loop
    show lsd4:
        alpha 0.0
        linear 6 alpha 1.0
    hide lola
    hide lenabra
    hide lenatopless2
    if v8triptopless:
        show lenatopless at rig
    else:
        show lenabra at rig
    show lola at lef
    with short
    lola "It would seem that way."
    i "For how long is this madness gonna keep going?"
    i "What's this, now? I'm hallucinating being in Lena's body?"
    lola "Do you think it's the first time you've been in this situation?"
    i "Huh?"
    $ fian = "worried"
    hide lenatopless
    hide lenabra
    show ian at rig
    with Dissolve (1)
    pause 0.3
    show ian at lef3
    show lola at rig3
    with move
    lola "You've experienced the world through Lena's eyes before. Same as she's experienced the world through yours."
    lola "The mind is vast, and underlays everything. The way one experiences reality depends on the point of view one chooses to take..."
    play sound "sfx/_a_.mp3"
    hide lola
    show god at rig3
    with Dissolve (1)
    god "The set of eyes he chooses to look through."
    i "So what are you trying to say? That I can get into... other people's heads?"
    i "That I can {i}be{/i} anybody?"
    god "As I said before, is there really a difference in who's making the choices?"
    god "Oh, I think our time's up for today."
    god "Next time you visit we can delve even deeper into it. Though it will probably be a long time until we meet again..."
    god "Probably not until this whole ordeal is over."
    $ fian = "surprise"
    i "Wait a minute, I still have so many questions!"
    i "This is fucking with my mind. Who am I?"
    god "Ah, isn't that the age-old question?"
    scene black with Dissolve (2)
    if persistent.include_disabled:
        $ config.menu_include_disabled = True
    $ greyed_out_disabled = False
    $ _game_menu_screen = "phone"
    $ quick_menu = True
    $ lena_active = False
    $ ian_active = True
    $ save_name = "Ian: Chapter 8"
    stop music
    scene ianroomnight_dark with vpunch
    $ fian = "disgusted"
    "I woke up suddenly. I was on my bed, staring at the ceiling."
    if ian_emma_sex:
        jump v8emmadlc
    show ian with short
    "I sat up, my mind feeling foggy."
    i "Is it over...? Is this real?"
    "My senses confirmed it. I was still a bit out of it, but I could feel that sense of familiarity with my surroundings that had been absent during my trance."
    i "What a fucking trip..."
    if trip_kill:
        i "It was a bunch of anxiety-inducing nonsense... I thought it would be fun, but it was a drag."
    else:
        i "Nothing of what I saw made any sense at all..."
    i "If I tell anyone I had a conversation with God they'll lock me up in a mental asylum. It was nuts."
    i "I need some water..."
    play sound "sfx/door.mp3"
    $ fian = "worried"
    scene ianhomenight_dark with short
    if perry_emma > 1:
        label gallery_CH08_S08:
            if _in_replay:
                call setup_CH08_S08 from _call_setup_CH08_S08

        "The living room was completely dark. It seemed Emma and Perry had already called it a night."
        show ian with short
        "I was about to walk to the kitchen when I detected movement in the dark. Two silhouettes on the couch."
        show ian at left with move
        "I hid behind the corner of the hallway, worried I was gonna get another psychedelic surprise. But the surprise I got was even bigger."
        play sound "sfx/mh1.mp3"
        scene v8_perry with long
        "I had to make sure my eyes were not tricking me. But no, the image I was seeing in front of me was real."
        "Emma and Perry were making out in the dark, and they seemed to be really into it."
        "In fact... Emma had lost her pants and it looked like Perry was fingering her."
        play sound "sfx/ah3.mp3"
        "He definitely was, judging by Emma's moans!"
        "I had no idea what led to this, but it finally happened. Perry and Emma finally hooked up..."
        if ian_will < 2:
            call will_up() from _call_will_up_48
        $ fian = "smile"
        scene ianhomenight_dark
        show ian
        with long
        i "It was about time... It seems my nudges served their purpose."
        $ fian = "disgusted"
        i "This is not a scene I want to see unfold, though. I'll leave them to it..."
        play sound "sfx/door.mp3"
        scene ianroomnight_dark
        with short
        "I went back to bed and tried to sleep, even though my mind was still spiraling."
        i "What a fucking weird night this was..."
        $ renpy.end_replay()
        $ gallery_unlock_scene("CH08_S08")
    else:
        show ian with short
        i "Emma already left, and Perry must be sleeping. What time is it?"
        i "Man, what a night..."
        "I stumbled to the kitchen and got some water before going back to bed."
        "I hoped to wake up the next morning in the real world, because I still wasn't completely sure I was out of that crazy trip."
        i "I'll think twice before trusting Emma again. She really is an agent of Chaos."
    jump v8c

## SATURDAY END #######################################################################################################################################################################################################################################
label v8iansaturdayend:
    scene ianhomenight with long
    "We continued to drink until late at night, chilling like we used to do back in the day."
    "I just wished the mood hadn't turned kinda sour after what happened with Lena and Cherry."
    scene ianroomnight_dark with long
    "When I started to feel tired I decided to call it a night and went to my room, leaving Emma and Perry still at it."
    "I had a lot on my mind, and the booze was making it hard to sort out my thoughts."
    stop music fadeout 2.0
    "That was the point though, wasn't it? I would think about all of this tomorrow morning..."
    if ian_emma_sex:
        jump v8emmadlc
    if perry_emma > 1:
        $ ian_look = 3
        $ fian = "worried"
        i "..."
        show ianunder with short
        "I was trying to sleep, but I felt a bit dehydrated."
        i "I need some water, or else tomorrow I'll probably  wake up hungover."
        play sound "sfx/door.mp3"
        scene ianhomenight_dark
        show ianunder
        with short
        "I was about to walk to the kitchen when I detected movement in the dark. Two silhouettes on the couch."
        show ianunder at left with move
        "I hid behind the corner of the hallway, startled for a second. What I saw was a pretty big shock."
        play sound "sfx/mh1.mp3"
        scene v8_perry with long
        "Emma and Perry were making out in the dark, and they seemed to be really into it."
        "In fact... Emma had lost her pants and it looked like Perry was fingering her."
        play sound "sfx/ah3.mp3"
        "He definitely was, judging by Emma's moans!"
        "I had no idea what led to this, but it finally happened. Perry and Emma finally hooked up..."
        if ian_will < 2:
            call will_up() from _call_will_up_49
        $ fian = "smile"
        scene ianhomenight_dark
        show ianunder
        with long
        i "It was about time... It seems my nudges served their purpose."
        $ fian = "disgusted"
        i "This is not a scene I want to see unfold, though. I'll leave them to it..."
        play sound "sfx/door.mp3"
        scene ianroomnight_dark
        with short
        "I went back to bed and tried to sleep, even though I could've really used that glass of water."
        i "This night turned out to be even more unusual than I thought..."
        $ gallery_unlock_scene("CH08_S08")
    jump v8c

# SCREENS #########################################################################################################################################################################################################

screen book_screen_6():
    tag book
    imagebutton auto "card6_romantic_%s.webp" pos (83, 80) action SetVariable("book_card5", "romantic") , [ Play ("ch_one", "sfx/book_card.mp3") ] , Return()
    imagebutton auto "card6_crude_%s.webp" pos (677, 80) action SetVariable("book_card5", "crude")  , [ Play ("ch_one", "sfx/book_card.mp3") ] , Return()
    imagebutton auto "card6_metaphysical_%s.webp" pos (1270, 80) action SetVariable("book_card5", "metaphysical") , [ Play ("ch_one", "sfx/book_card.mp3") ] , Return()

screen v8error():
    imagebutton idle "error_base.webp"
    imagebutton idle "error_rollback.webp" hover "error_rollback_hover.webp" focus_mask True action SetVariable("fake_error", "rollback") , Return()
    imagebutton idle "error_ignore.webp" hover "error_ignore_hover.webp" focus_mask True action SetVariable("fake_error", "ignore") , Return()
    imagebutton idle "error_reload.webp" hover "error_reload_hover.webp" focus_mask True action SetVariable("fake_error", "reload") , Return()
    imagebutton idle "error_console.webp" hover "error_console_hover.webp" focus_mask True action SetVariable("fake_error", "console") , Return()
    imagebutton idle "error_open.webp" hover "error_open_hover.webp" focus_mask True action SetVariable("fake_error", "open") , Return()
    imagebutton idle "error_bbc.webp" hover "error_bbc_hover.webp" focus_mask True action SetVariable("fake_error", "bbc") , Return()
    imagebutton idle "error_copy.webp" hover "error_copy_hover.webp" focus_mask True action SetVariable("fake_error", "copy") , Return()
    imagebutton idle "error_quit.webp" hover "error_quit_hover.webp" focus_mask True action SetVariable("fake_error", "quit") , Return()
    imagebutton idle "error_l1.webp" hover "error_l1_hover.webp" focus_mask True action SetVariable("fake_error", "none") , Return()
    imagebutton idle "error_l2.webp" hover "error_l2_hover.webp" focus_mask True action SetVariable("fake_error", "none") , Return()
    imagebutton idle "error_l3.webp" hover "error_l3_hover.webp" focus_mask True action SetVariable("fake_error", "none") , Return()
    imagebutton idle "error_l4.webp" hover "error_l4_hover.webp" focus_mask True action SetVariable("fake_error", "none") , Return()
    imagebutton idle "error_l5.webp" hover "error_l5_hover.webp" focus_mask True action SetVariable("fake_error", "none") , Return()
    imagebutton idle "error_l6.webp" hover "error_l6_hover.webp" focus_mask True action SetVariable("fake_error", "none") , Return()
    imagebutton idle "error_l7.webp" hover "error_l7_hover.webp" focus_mask True action SetVariable("fake_error", "none") , Return()

screen v8reload():
    vbox:
        xcenter 0.5
        ycenter 0.5
        text "{font=DejaVuSans.ttf}{size=30}Reloading script..."
        timer 2 action Return()
screen v8reload2():
    vbox:
        xcenter 0.5
        ycenter 0.5
        text "{font=DejaVuSans.ttf}{size=30}File not found"
        timer 2 action Return()
screen v8reload3():
    vbox:
        xcenter 0.5
        ycenter 0.5
        text "{font=DejaVuSans.ttf}{size=30}Console access granted"
        timer 2 action Return()
screen v8reload4():
    vbox:
        xcenter 0.5
        ycenter 0.5
        text "{font=DejaVuSans.ttf}{size=30}The cake is a lie"
        timer 2 action Return()
screen v8quit1():
    vbox:
        xcenter 0.5
        ycenter 0.5
        text "{font=DejaVuSans.ttf}{size=30}Sorry, there is no quitting now"
        timer 2 action Return()

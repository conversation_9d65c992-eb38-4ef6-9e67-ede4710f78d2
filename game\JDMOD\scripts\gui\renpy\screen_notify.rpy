init offset = 1








screen notify(message):

    zorder 100
    style_prefix "notify"

    frame at notify_appear:
        text "[message!tq]"

    timer 3.25 action Hide('notify')


transform notify_appear:
    on show:
        alpha 0
        linear .25 alpha 1.0
    on hide:
        linear .5 alpha 0.0




init python:
    def JD_sceneunlocked(image):
        if (not JD_seen_image(scene_unlock(unlock_scene(image))) and not _in_replay) or persistent.JD_developer:
            renpy.show_screen("JD_sceneunlocked", "scene unlocked", unlock_scene(image))

    def unlock_scene(image_picked): 
        scene_picked = None
        
        for i in range(0,len(JD_gal_scenes)):
            if image_picked in JD_gal_scenes[i][5].split(", "):
                scene_picked = i
        if scene_picked == None:
            return "error"
        else:
            return JD_gal_scenes[scene_picked][0]

screen JD_sceneunlocked(message, scene_picked):

    zorder 100
    style_prefix "sceneunlocked"

    frame:
        at sceneunlocked_appear
        has vbox
        text message
        fixed:
            maximum int(config.screen_width * 0.10), int(config.screen_height * 0.10)
            for e in extension:
                if renpy.loadable("JDMOD/images/thumbnails/{scene}.{ext}".format(scene=scene_thumbnail(scene_picked), ext=e)):
                    add im.Scale("JDMOD/images/thumbnails/{scene}.{ext}".format(scene=scene_thumbnail(scene_picked), ext=e), int(config.screen_width * 0.10), int(config.screen_height * 0.10))
                elif renpy.loadable("{dir}{scene}.{ext}".format(dir=thumb_dir, scene=scene_thumbnail(scene_picked), ext=e)):
                    add im.Scale("{dir}{scene}.{ext}".format(dir=thumb_dir, scene=scene_thumbnail(scene_picked), ext=e), int(config.screen_width * 0.10), int(config.screen_height * 0.10))
            add im.Scale("JDMOD/images/gui/locked_idle.png", int(config.screen_width * 0.05), int(config.screen_width * 0.05)) align (0.5,0.5)
        if scene_picked == "error":
            text "Error":
                maximum int(config.screen_width * 0.10), int(config.screen_height * 0.10)
                color "#FFF"
                size 26
        else:
            text scene_desc(scene_picked):
                maximum int(config.screen_width * 0.10), int(config.screen_height * 0.10)
                color "#FFF"
                size 26
    add im.Scale("JDMOD/images/gui/JD.png", int(config.screen_height * 0.05), int(config.screen_height * 0.05)):
        xanchor 0.5
        xpos int(config.screen_width * 0.10 + 40)
        yanchor 0.5
        ypos 0.06
        at sceneunlocked_appear

    timer 2 action Hide('JD_sceneunlocked')


style sceneunlocked_frame is empty
style sceneunlocked_text is label_text

style sceneunlocked_frame:
    ypos 0.06
    background Frame("gui/frame.png", gui.notify_frame_borders, tile=gui.frame_tile)
    padding (20, 10, 20, 12)




transform sceneunlocked_appear:
    on show:
        xoffset int(-config.screen_width * 0.08) alpha 0.0
        easein 0.50 xoffset 0 alpha 1.0
    on hide:
        easeout 0.30 xoffset int(-config.screen_width * 0.08) alpha 0.0
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc

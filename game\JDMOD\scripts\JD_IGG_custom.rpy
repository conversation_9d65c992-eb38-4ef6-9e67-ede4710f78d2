init offset = 1






define JD_IGG_scenevar = "{u}Scene variation:{/u}"
define JD_IGG_tuto = "{u}Tutorial:{/u}"
define JD_IGG_IC = "{u}Important choice:{/u}"







define JD_IGG_cha = "{color=#C77822}{u}Chg:{/color}{/u}"


define JD_IGG_cha_lena_anal_p = "%s {color=#16A825}Increases{/color} <PERSON>'s anal skill." % JD_IGG_cha
define JD_IGG_cha_lena_bj_p = "%s {color=#16A825}Increases{/color} <PERSON>'s blowjob skill." % JD_IGG_cha

define JD_IGG_cha_lena_fty_3some = "%s lena_fty_3some" % JD_IGG_cha
define JD_IGG_cha_lena_fty_lesbo = "%s lena_fty_lesbo" % JD_IGG_cha
define JD_IGG_cha_lena_fty_bbc = "%s lena_fty_bbc" % JD_IGG_cha
define JD_IGG_cha_lena_fty_show = "%s lena_fty_show" % JD_IGG_cha
define JD_IGG_cha_lena_fty_slave = "%s lena_fty_slave" % JD_IGG_cha


define JD_IGG_cha_ian_dom_p = "%s {color=#16A825}Increases{/color} Ian's sexual dominance over partner." % JD_IGG_cha
define JD_IGG_cha_satisfaction_m = "%s {color=#C41D19}Decreases{/color} partner's sexual satisfaction." % JD_IGG_cha
define JD_IGG_cha_satisfaction_p = "%s {color=#16A825}Increases{/color} partner's sexual satisfaction." % JD_IGG_cha



define JD_IGG_cha_alison_sexy_p = "%s Alison will switch to a sexier style." % JD_IGG_cha
define JD_IGG_cha_alison_sexy_m = "%s Alison will switch to a less sexy style." % JD_IGG_cha


define JD_IGG_cha_axel_disposition_p = "%s {color=#16A825}Increases{/color} disposition to work with Axel." % JD_IGG_cha
define JD_IGG_cha_axel_disposition_m = "%s {color=#C41D19}Decreases{/color} disposition to work with Axel." % JD_IGG_cha


define JD_IGG_billy_trust = "Lena's trust in Billy"
define JD_IGG_cha_billy_trust_p = "%s {color=#16A825}Increases{/color} trust in Billy." % JD_IGG_cha
define JD_IGG_cha_billy_trust_m = "%s {color=#C41D19}Decreases{/color} trust in Billy." % JD_IGG_cha


define JD_IGG_cha_holly_change_m = "%s {color=#C41D19}Decreases{/color} how much Holly wants to become like Lena." % JD_IGG_cha
define JD_IGG_cha_holly_change_p = "%s {color=#16A825}Increases{/color} how much Holly wants to become like Lena." % JD_IGG_cha
define JD_IGG_cha_encourage_holly_m = "%s {color=#C41D19}Decreases{/color} Holly's selfconfidence." % JD_IGG_cha
define JD_IGG_cha_encourage_holly_p = "%s {color=#16A825}Increases{/color} Holly's selfconfidence." % JD_IGG_cha


define JD_IGG_cha_v12_perry_success_p = "%s Good for Perry's success." % JD_IGG_cha
define JD_IGG_cha_v12_perry_success_m = "%s Bad for Perry's success." % JD_IGG_cha


define JD_IGG_cha_seymour_disposition_p = "%s {color=#16A825}Increases{/color} disposition to work with Seymour." % JD_IGG_cha
define JD_IGG_cha_seymour_disposition_m = "%s {color=#C41D19}Decreases{/color} disposition to work with Seymour."  % JD_IGG_cha






define JD_IGG_req = "{color=#205681}{u}Req:{/color}{/u}"


define JD_IGG_req_NOT_v1_drunk = "%s Ian isn't drunk. Playing pool might have been a better idea." % JD_IGG_req
define JD_IGG_req_ian_lowkick = "%s Ian learned the technique during training." % JD_IGG_req
define JD_IGG_req_ian_grappling = "%s Ian learned the technique during training." % JD_IGG_req



define JD_IGG_req_v4_ian_kiss = "%s Ian kissed Lena." % JD_IGG_req



define JD_IGG_req_v5_cindy_shoot = "%s Ian went to Cindy's photoshoot." % JD_IGG_req
define JD_IGG_req_v5_cindy_nude = "%s Cindy agreed to pose nude." % JD_IGG_req



define JD_IGG_req_NOT_lena_mike_dating = "%s Lena isn't dating Mike." % JD_IGG_req


define JD_IGG_req_lena_fty_3some = "%s lena_fty_3some" % JD_IGG_req
define JD_IGG_req_lena_fty_lesbo = "%s lena_fty_lesbo" % JD_IGG_req
define JD_IGG_req_lena_fty_bbc = "%s lena_fty_bbc" % JD_IGG_req
define JD_IGG_req_lena_fty_show = "%s lena_fty_show" % JD_IGG_req
define JD_IGG_req_lena_fty_slave = "%s lena_fty_slave" % JD_IGG_req
define JD_IGG_req_v7_minerva_sex = "%s During Chapter 7, Ian had sex with Minerva." % JD_IGG_req



define JD_IGG_req_NOT_ian_lena_couple = "%s Ian and Lena aren't officially dating." % JD_IGG_req
define JD_IGG_req_NOT_lena_ian_love = "%s Lena isn't {image=JD_icon_ian_love} {i}in Love with Ian{/i}." % JD_IGG_req
define JD_IGG_req_v8_holly_strip = "%s Lena is in her underwear." % JD_IGG_req


define JD_IGG_req_NOT_ian_alison_love = "%s Ian isn't {image=JD_icon_alison_love} {i}in Love with Alison{/i}." % JD_IGG_req



define JD_IGG_req_louise_dominant = "%s Lena is Louise's Mistress." % JD_IGG_req
define JD_IGG_req_stalkfap_pro = "%s Lena decided to take stalkfap seriously." % JD_IGG_req
define JD_IGG_req_NOT_v10_lena_drug = "%s Lena isn't on drug." % JD_IGG_req
define JD_IGG_req_v3_gillian_stop = "%s In Ch. 3, Ian deleted Gillian's pics from his phone." % JD_IGG_req


define JD_IGG_req_alison_blonde = "%s Ian liked Alison's new style." % JD_IGG_req
define JD_IGG_req_NOT_lena_holly_dating = "%s Lena isn't dating Holly." % JD_IGG_req
define JD_IGG_req_v12_lena_topless = "%s Lena is topless." % JD_IGG_req
define JD_IGG_req_NOT_ian_holly_love = "%s Ian isn't {image=JD_icon_holly_love} {i}in Love with Holly{/i}." % JD_IGG_req
define JD_IGG_req_ian_fit = "%s Ian is fit." % JD_IGG_req



define JD_IGG_req_v13_seymour_shoot = "%s Lena participated in the photoshoot with Seymour." % JD_IGG_req
define JD_IGG_req_NOT_v13_seymour_shoot = "%s Lena didn't participate in the photoshoot with Seymour." % JD_IGG_req
define JD_IGG_req_lena_robert_sex = "%s Lena had sex with Robert." % JD_IGG_req
define JD_IGG_req_lena_mike_sex = "%s Lena had sex with Mike." % JD_IGG_req
define JD_IGG_req_NOT_v10_jeremy_3some_2 = "%s During Ch. 10, Lena didn't leave Louise out of the threesome with Jeremy." % JD_IGG_req
define JD_IGG_req_NOT_ian_cindy_love = "%s Ian isn't {image=JD_icon_cindy_love} {i}in Love with Cindy{/i}." % JD_IGG_req
define JD_IGG_req_ian_louise_flirt_2 = "%s Ian showed romantic interest for Louise." % JD_IGG_req


define JD_IGG_req_lena_fty_lesbo = "%s Lena has a lesbian fantasy." % JD_IGG_req
define JD_IGG_req_holly_change = "%s Holly became more like Lena." % JD_IGG_req
define JD_IGG_req_lena_anal = "%s Lena's anal skill is" % JD_IGG_req

define JD_IGG_req_gillian_stop = "%s Ian got over Gillian." % JD_IGG_req
define JD_IGG_req_v10_ivy_gym = "%s When they trained together, Ian choked Ivy and won." % JD_IGG_req
define JD_IGG_req_ian_cheating = "%s Ian cheated on Lena." % JD_IGG_req






define JD_IGG_routevar = "{u}Route variation:{/u}"


define JD_IGG_ian_cuck_route = "%sCuckold Route for Ian" % JD_IGG_routevar

# Define character lists for the mod
define CHAR = ["ian", "lena", "holly", "emma", "ivy", "alison", "cindy", "cherry", "axel", "jeremy", "perry", "wade", "billy", "mike", "robert", "seymour", "mark", "stan", "louise", "minerva", "gillian", "jessica", "agnes", "danny", "ed", "nat", "wen", "yuri", "jack", "marcel", "molly", "lola", "victor", "charles", "clark", "kent", "martin", "john", "rosa"]
define MAIN_CHAR = ["ian", "lena"]

init python:
    for route in CHAR:
        globals()["JD_IGG_{}_route".format(route)] = "{image=JD_icon_" + route + "_route}  {color=#A97C14}Route with "  + route.title() + "{/color}"


    TT_CHAR_ROUTE = [
        ("holly_mark", "Holly's route with Mark"),
        ("holly_robert", "Holly's route with Robert"),
        ("ivy_jeremy", "Ivy's route with Jeremy"),
        ("perry_cherry", "Perry's route with Cherry"),
        ("perry_emma", "Perry's route with Emma"),
        ("holly_mark", "Perry's route with Emma"),
        ("holly_trinity", "Holly's trinity route"),
    ]

    for route in TT_CHAR_ROUTE:
        globals()["JD_IGG_{}_route".format(route[0])] = "{image=JD_icon_" + route[0] + "_route}  {color=#A97C14}" + route[1] + "{/color}"





init python:
    for rel in CHAR:
        globals()["JD_IGG_cha_{}_rel_p".format(rel)] = JD_IGG_cha + " {image=JD_icon_" + rel + "_rel_+}  {color=#16A825}Increases{/color} Relationship with " + rel.title() + "."
        globals()["JD_IGG_cha_{}_rel_m".format(rel)] = JD_IGG_cha + " {image=JD_icon_" + rel + "_rel_-}  {color=#C41D19}Decreases{/color} Relationship with " + rel.title() + "."
        globals()["JD_IGG_cha_{}_rel_over".format(rel)] = JD_IGG_cha + " {image=JD_icon_" + rel + "_rel_over}  {color=#C41D19}Decreases{/color} Relationship with " + rel.title() + " {color=#C41D19}to the lowest possible{/color}."

    TT_CHAR_REL = [
        ("lena_cindy", "Lena's Relationship with Cindy"),
        ("lena_emma", "Lena's Relationship with Emma"),
        ("lena_perry", "Lena's Relationship with Perry"),
        ("perry_cherry", "Perry's Relationship with Cherry"),
    ]

    for rel in TT_CHAR_REL:
        globals()["JD_IGG_cha_{}_rel_p".format(rel[0])] = JD_IGG_cha + " {image=JD_icon_" + rel[0] + "_rel_+}  {color=#16A825}Increases{/color} " + rel[1] + "."
        globals()["JD_IGG_cha_{}_rel_m".format(rel[0])] = JD_IGG_cha + " {image=JD_icon_" + rel[0] + "_rel_-}  {color=#C41D19}Decreases{/color} " + rel[1] + "."
        globals()["JD_IGG_cha_{}_rel_over".format(rel[0])] = JD_IGG_cha + " {image=JD_icon_" + rel[0] + "_rel_over}  {color=#C41D19}Decreases{/color} " + rel[1] + " {color=#C41D19}to the lowest possible{/color}."
# Decompiled by unrpyc: https://github.com/CensoredUsername/unrpyc
